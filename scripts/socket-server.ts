#!/usr/bin/env node

/**
 * Socket Server for Music Quiz Multiplayer
 * Production-ready server with working game logic
 */

import { createServer } from 'http'
import { MultiplayerSocketServer } from '../lib/socket-server'

const PORT = process.env.SOCKET_PORT || 3001
const NODE_ENV = process.env.NODE_ENV || 'development'

// Server configuration
const httpServer = createServer()
let socketServer: MultiplayerSocketServer

// Initialize multiplayer server
async function initializeServer() {
  try {
    console.log('🎮 Starting Music Quiz Socket Server...')
    console.log(`📡 Environment: ${NODE_ENV}`)
    console.log(`🔌 Port: ${PORT}`)

    // Initialize multiplayer server with working implementation
    socketServer = new MultiplayerSocketServer(httpServer)

    console.log('✅ Multiplayer server initialized successfully')
    return true
  } catch (error) {
    console.error('💥 Failed to initialize server:', error)
    return false
  }
}

// Setup graceful shutdown and error handling
function setupProcessHandlers() {
  process.on('SIGTERM', async () => {
    console.log('🛑 Received SIGTERM, shutting down gracefully...')
    httpServer.close()
    process.exit(0)
  })

  process.on('SIGINT', async () => {
    console.log('🛑 Received SIGINT, shutting down gracefully...')
    httpServer.close()
    process.exit(0)
  })

  process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught Exception:', error)
    process.exit(1)
  })

  process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason)
    process.exit(1)
  })
}

// Start the server
async function startServer() {
  try {
    const initialized = await initializeServer()
    if (!initialized) {
      throw new Error('Failed to initialize server')
    }

    setupProcessHandlers()

    const HOST = '0.0.0.0' // Bind to all interfaces for network access

    httpServer.listen(Number(PORT), HOST, () => {
      console.log(`🚀 Socket server running on ${HOST}:${PORT}`)
      console.log(`🎯 Ready for multiplayer connections!`)

      // Log server statistics periodically in production
      if (NODE_ENV === 'production') {
        setInterval(() => {
          const stats = socketServer.getStats()
          console.log('📊 Server Stats:', {
            connectedClients: stats.connectedClients,
            activeGames: stats.activeGames,
            uptime: Math.round(process.uptime() / 60) + ' minutes'
          })
        }, 5 * 60 * 1000) // Every 5 minutes
      }
    })

  } catch (error) {
    console.error('💥 Failed to start socket server:', error)
    process.exit(1)
  }
}

// Start the server
startServer()