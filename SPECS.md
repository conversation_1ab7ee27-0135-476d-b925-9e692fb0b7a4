# Music Quiz Application - Technical Specifications

## Architecture Overview

The application follows a client-server architecture with real-time WebSocket communication for multiplayer functionality.

### Tech Stack
- **Frontend**: Next.js 15.2, React 19, TypeScript
- **Backend**: Node.js, Socket.IO, Express
- **Database**: PostgreSQL with Prisma ORM
- **Audio**: MPD (Music Player Daemon) with HTTP proxy
- **Process Management**: PM2
- **State Management**: React hooks, Socket.IO events
- **Styling**: Tailwind CSS, Shadcn UI components

## Server Architecture

### 1. Socket Server (`lib/socket-server.ts`)
The main multiplayer server implementation using Socket.IO.

**Key Classes:**
- `MultiplayerSocketServer`: Main server class handling all socket events
- Runs on port 3001 (configurable via `SOCKET_PORT`)
- Handles CORS for cross-origin requests

**Main Events Handled:**
- `create-game`: Initialize new game with settings
- `join-game`: Player joins existing game  
- `start-game`: Host begins the quiz
- `submit-answer`: Player submits answer (without revealing correctness)
- `next-action`: Move to next question or end game
- `leave-game`: Player exits
- `request-game-state`: Sync state on reconnection

**Events Emitted:**
- `game-created`: Confirms game creation with PIN
- `game-state`: Full game state updates
- `player-joined/left`: Player list changes
- `game-countdown`: 3-second countdown before start
- `game-started`: First question with timer
- `question`: New question data
- `question-results`: Reveals correct answer and scores
- `game-over`: Final leaderboard
- `error`: Error notifications

### 2. Game Manager (`lib/game-manager.ts`)
Core game logic and state management.

**Responsibilities:**
- Game creation with host player
- Question generation based on game mode
- Player management (add/remove)
- Answer validation and score calculation
- Game state transitions
- Timer management via socket server

**Scoring System:**
```typescript
// Points calculation based on answer speed
const timeRatio = Math.max(0, 1 - (timeTaken / questionTimeLimit))
const points = Math.round(1000 * timeRatio) // Max 1000 points
```

### 3. Multiplayer Quiz Generator (`lib/multiplayer-quiz-generator.ts`)
Generates questions for different game modes.

**Key Methods:**
- `generateUltimoteQuestions()`: Mixed category questions with rounds
- `generateGeneralKnowledgeQuestions()`: Trivia from database
- `generateDatabaseQuestions()`: Music questions from tracks

**UlTimote Configuration:**
```typescript
{
  questionsPerRound: number,
  categories: {
    generalKnowledge: {
      enabled: boolean,
      rounds: number,
      categories: string[], // ['general', 'science', 'history', etc.]
      difficulty: number
    },
    classic: { enabled: boolean, rounds: number },
    quickFire: { enabled: boolean, rounds: number },
    // ... other music categories
  }
}
```

## Client Architecture

### 1. Hooks

**`useMultiplayer` (`hooks/use-multiplayer.ts`)**
Main hook for multiplayer functionality.

State managed:
- `gameState`: Current game information
- `currentQuestion`: Active question with options
- `timeRemaining`: Client-side countdown
- `leaderboard`: Player rankings
- `questionEndData`: Correct answer info
- `isConnected`: Socket connection status

Key functions:
- `createGame(hostName, gameMode, settings)`
- `joinGame(gameCode, playerName)`
- `startGame()`
- `submitAnswer(answerIndex)` - Returns only acknowledgment
- `leaveGame()`

**`useMultiplayerSocket` (`hooks/use-multiplayer-socket.ts`)**
Singleton WebSocket connection management.

### 2. Components

**`MultiplayerQuiz` (`components/multiplayer-quiz.tsx`)**
Active quiz gameplay component.

Features:
- Question display with timer
- Answer selection (visual feedback without revealing correctness)
- "Answer submitted!" toast (no immediate right/wrong feedback)
- Correct answer revealed only after time expires
- Points display after question ends
- Live leaderboard updates
- Audio playback for music questions

**`MultiplayerLobby` (`components/multiplayer-lobby.tsx`)**
Pre-game configuration and waiting room.

Features:
- Game mode selection (defaults to UlTimote)
- UlTimote configuration UI
- Player list with connection status
- Game PIN display for sharing
- Start button (host only)
- Validation for category selection

## Game Flow

### 1. Game Creation
1. Host selects game mode and configures settings
2. Socket server creates game with unique 4-character PIN
3. GameManager generates all questions upfront
4. Host waits in lobby for players

### 2. Player Joining
1. Player enters name and game PIN
2. Socket server validates and adds to game
3. All players see updated player list
4. Minimum 2 players required to start

### 3. Game Start
1. Host clicks start button
2. 3-second countdown shown to all players
3. First question displayed simultaneously
4. Timer starts counting down

### 4. Question Flow
1. Question displayed with multiple choice options
2. Players select answer (visual feedback only)
3. "Answer submitted!" toast shown
4. Server tracks submissions without revealing results
5. When time expires or all answered:
   - `question-results` event reveals correct answer
   - Points calculated and scores updated
   - Shows feedback based on speed
6. 5-second delay showing results
7. Auto-advance to next question

### 5. Game End
1. After final question results
2. `game-over` event with final leaderboard
3. Players can return to menu or play again

## Data Models

### GameState
```typescript
interface GameState {
  gameId: string
  pin: string  // Same as gameId, used for display
  status: 'waiting' | 'playing' | 'question-results' | 'finished'
  gameMode: string
  players: Player[]
  questions: Question[]
  currentQuestionIndex: number
  currentQuestion: Question | null
  totalQuestions: number
  timePerQuestion: number
  hostId: string
  settings: GameSettings
  createdAt: number
  startedAt?: number
  finishedAt?: number
}
```

### Player
```typescript
interface Player {
  id: string
  name: string
  avatar: string
  score: number
  isHost: boolean
  hasAnswered: boolean
  lastAnswer?: number
  lastAnswerTime?: number
  lastScoreChange?: number
  joinedAt: number
}
```

### Question
```typescript
interface Question {
  id: string
  type: 'multiple-choice'
  question: string
  options: string[]
  correctAnswer: number  // Index in options array
  points: number
  timeLimit: number
  audioFile?: string     // For music questions
  track?: Track          // Full track metadata
  category?: string      // e.g., 'History', 'Science'
}
```

## PM2 Process Configuration

Three main processes managed by PM2:
1. **music-quiz-nextjs**: Next.js application (port 3000)
2. **music-quiz-socket**: Socket.IO server (port 3001)
3. **music-quiz-mpd-proxy**: MPD HTTP proxy (port 3002)

Configuration in `ecosystem.config.js`:
- Cluster mode for Next.js (multiple instances)
- Single instance for socket server
- Auto-restart on failure
- Environment variable management

## Security Considerations

1. **Input Validation**: All user inputs sanitized
2. **Rate Limiting**: Prevents spam and abuse
3. **CORS**: Configured for allowed origins only
4. **Authentication**: Session-based for registered users
5. **Game PIN**: Random 4-character codes prevent guessing

## Performance Optimizations

1. **Question Pre-generation**: All questions generated on game creation
2. **State Diffing**: Only changed data sent to clients
3. **Connection Pooling**: Reused database connections
4. **In-Memory Game Store**: Fast game state access
5. **Client-Side Timer**: Reduces server load

## Error Handling

1. **Graceful Disconnection**: Players can reconnect to active games
2. **Host Migration**: (Planned) Transfer host role if host disconnects
3. **Validation Errors**: Clear error messages to clients
4. **Server Crashes**: PM2 auto-restart with state recovery

## Testing

- Unit tests for game logic
- Integration tests for socket events
- E2E tests with Playwright
- Mock game simulations
- Load testing for concurrent games