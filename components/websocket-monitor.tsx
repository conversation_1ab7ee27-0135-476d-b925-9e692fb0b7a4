"use client"

import { useEffect, useState, useRef } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Activity, Wifi, WifiOff, Eye, EyeOff, RefreshCw, Download } from "lucide-react"
import { getMultiplayerSocket } from "@/lib/multiplayer-socket"

interface WebSocketMessage {
  id: number
  type: 'sent' | 'received'
  event: string
  data: any
  timestamp: Date
  size: number
}

export function WebSocketMonitor() {
  const [isConnected, setIsConnected] = useState(false)
  const [messages, setMessages] = useState<WebSocketMessage[]>([])
  const [isMonitoring, setIsMonitoring] = useState(true)
  const messageIdRef = useRef(0)
  const [socketInfo, setSocketInfo] = useState<any>(null)

  useEffect(() => {
    const socket = getMultiplayerSocket()
    
    // Update connection status
    const updateConnectionStatus = () => {
      setIsConnected(socket.connected)
      setSocketInfo({
        id: socket.id,
        url: socket.io.uri,
        transports: socket.io.opts.transports,
        connected: socket.connected,
        disconnected: socket.disconnected
      })
    }

    // Initial status
    updateConnectionStatus()

    // Connection events
    socket.on('connect', updateConnectionStatus)
    socket.on('disconnect', updateConnectionStatus)

    // Intercept emit
    const originalEmit = socket.emit
    socket.emit = function(...args: any[]) {
      if (isMonitoring) {
        const [event, ...data] = args
        const message: WebSocketMessage = {
          id: messageIdRef.current++,
          type: 'sent',
          event,
          data: data[0],
          timestamp: new Date(),
          size: JSON.stringify(data).length
        }
        setMessages(prev => [...prev, message])

        // Log to console for debugging
        console.log(`[WebSocket Monitor] 📤 SENT: ${event}`, data[0])
        
        // Special handling for create-game
        if (event === 'create-game' && data[0]?.settings?.ultimoteConfig) {
          console.log('[WebSocket Monitor] 🎮 ulTimote Config:', data[0].settings.ultimoteConfig)
        }
      }
      return originalEmit.apply(socket, args)
    }

    // Monitor all events
    const allEvents = [
      'connect',
      'disconnect',
      'error',
      'game-state',
      'player-joined',
      'player-left',
      'question',
      'answer-result',
      'game-ended'
    ]

    allEvents.forEach(event => {
      socket.on(event, (data: any) => {
        if (isMonitoring) {
          const message: WebSocketMessage = {
            id: messageIdRef.current++,
            type: 'received',
            event,
            data,
            timestamp: new Date(),
            size: JSON.stringify(data).length
          }
          setMessages(prev => [...prev, message])

          // Log to console
          console.log(`[WebSocket Monitor] 📥 RECEIVED: ${event}`, data)
        }
      })
    })

    // Cleanup
    return () => {
      socket.emit = originalEmit
      allEvents.forEach(event => socket.off(event))
      socket.off('connect', updateConnectionStatus)
      socket.off('disconnect', updateConnectionStatus)
    }
  }, [isMonitoring])

  const clearMessages = () => {
    setMessages([])
    messageIdRef.current = 0
  }

  const exportMessages = () => {
    const data = {
      exportTime: new Date().toISOString(),
      socketInfo,
      messages: messages.map(m => ({
        ...m,
        timestamp: m.timestamp.toISOString()
      }))
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `websocket-log-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  const getMessageColor = (type: string) => {
    return type === 'sent' ? 'text-blue-600' : 'text-green-600'
  }

  return (
    <Card className="fixed bottom-4 right-4 w-96 h-[500px] shadow-xl border-2 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur">
      <div className="flex flex-col h-full">
        <div className="p-4 border-b space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold flex items-center gap-2">
              <Activity className="h-4 w-4" />
              WebSocket Monitor
            </h3>
            <Badge variant={isConnected ? "success" : "secondary"}>
              {isConnected ? (
                <>
                  <Wifi className="h-3 w-3 mr-1" />
                  Connected
                </>
              ) : (
                <>
                  <WifiOff className="h-3 w-3 mr-1" />
                  Disconnected
                </>
              )}
            </Badge>
          </div>
          
          {socketInfo && (
            <div className="text-xs text-gray-600 space-y-1">
              <div>ID: {socketInfo.id || 'Not connected'}</div>
              <div>URL: {socketInfo.url}</div>
              <div>Transport: {socketInfo.transports?.join(', ')}</div>
            </div>
          )}
          
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsMonitoring(!isMonitoring)}
            >
              {isMonitoring ? (
                <>
                  <Eye className="h-3 w-3 mr-1" />
                  Monitoring
                </>
              ) : (
                <>
                  <EyeOff className="h-3 w-3 mr-1" />
                  Paused
                </>
              )}
            </Button>
            <Button size="sm" variant="outline" onClick={clearMessages}>
              <RefreshCw className="h-3 w-3 mr-1" />
              Clear
            </Button>
            <Button size="sm" variant="outline" onClick={exportMessages}>
              <Download className="h-3 w-3 mr-1" />
              Export
            </Button>
          </div>
        </div>

        <ScrollArea className="flex-1 p-4">
          <div className="space-y-2">
            {messages.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <p>No messages yet</p>
                <p className="text-xs mt-2">WebSocket traffic will appear here</p>
              </div>
            ) : (
              messages.map((message) => (
                <div
                  key={message.id}
                  className={`text-xs p-2 rounded border ${
                    message.type === 'sent' ? 'bg-blue-50 border-blue-200' : 'bg-green-50 border-green-200'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className={`font-semibold ${getMessageColor(message.type)}`}>
                      {message.type === 'sent' ? '📤' : '📥'} {message.event}
                    </span>
                    <span className="text-gray-500">
                      {message.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  
                  {message.data && (
                    <div className="mt-1 p-1 bg-gray-100 rounded text-[10px] font-mono overflow-x-auto">
                      <pre>{JSON.stringify(message.data, null, 2).slice(0, 200)}...</pre>
                    </div>
                  )}
                  
                  <div className="text-[10px] text-gray-500 mt-1">
                    Size: {message.size} bytes
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>

        <div className="p-2 border-t text-xs text-center text-gray-500">
          {messages.length} messages • {isMonitoring ? 'Recording' : 'Paused'}
        </div>
      </div>
    </Card>
  )
}