/**
 * ulTimote Game Engine - Ultimate customizable quiz experience
 * Handles complex multi-category gameplay with dynamic question selection
 */

"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import dynamic from "next/dynamic"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

// Dynamically import mobile version
const UlTimoteGameMobile = dynamic(
  () => import('./ultimote-game-mobile').then(mod => mod.UlTimoteGameMobile),
  { ssr: false }
)
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Crown, 
  Clock, 
  Star, 
  ArrowLeft, 
  Target,
  Flame,
  Award,
  Trophy,
  Zap,
  Music,
  Headphones,
  ImageIcon,
  Search,
  TrendingUp,
  Calendar,
  Radio,
  Brain,
  X
} from "lucide-react"
import type { UlTimoteGameConfig } from "./ultimote-game-config"
import type { QuizQuestion } from "@/lib/database/quiz-data"
import { QuickFireQuiz } from "./quick-fire-quiz"
import { AudioTricksQuiz } from "./audio-tricks-quiz"
import { AlbumArtQuiz } from "./album-art-quiz"
import { AudioFingerprintQuiz } from "./audio-fingerprint-quiz"
import { GeneralKnowledgeQuiz } from "./general-knowledge-quiz"

interface UlTimoteGameProps {
  config: UlTimoteGameConfig
  onComplete: (results: UlTimoteGameResults) => void
  onBackToMenu: () => void
}

interface UlTimoteGameResults {
  totalScore: number
  totalQuestions: number
  correctAnswers: number
  maxStreak: number
  roundResults: RoundResult[]
  categoryStats: CategoryStats[]
  achievements: string[]
  gameMode: 'ultimote'
  gameName: string
  duration: number
}

interface RoundResult {
  roundNumber: number
  category: string
  questions: number
  score: number
  correctAnswers: number
  timeSpent: number
  streak: number
}

interface CategoryStats {
  category: string
  questionsAsked: number
  correctAnswers: number
  totalScore: number
  averageTime: number
  bestStreak: number
}

interface UlTimoteRound {
  roundNumber: number
  category: string
  questions: QuizQuestion[]
  completed: boolean
  score: number
}

const CATEGORY_ICONS = {
  classic: Music,
  quickFire: Zap,
  audioTricks: Headphones,
  albumArt: ImageIcon,
  audioFingerprint: Search,
  chartPosition: TrendingUp,
  decadeChallenge: Calendar,
  genreSpecialist: Radio,
  generalKnowledge: Brain
}

const CATEGORY_COLORS = {
  classic: 'text-blue-400',
  quickFire: 'text-yellow-400',
  audioTricks: 'text-purple-400',
  albumArt: 'text-rose-400',
  audioFingerprint: 'text-green-400',
  chartPosition: 'text-emerald-400',
  decadeChallenge: 'text-orange-400',
  genreSpecialist: 'text-indigo-400',
  generalKnowledge: 'text-cyan-400'
}

export function UlTimoteGame({ config, onComplete, onBackToMenu }: UlTimoteGameProps) {
  // Check if mobile device
  const [isMobile, setIsMobile] = useState(false)
  
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])
  
  const [gameState, setGameState] = useState<'loading' | 'round-intro' | 'playing' | 'round-complete' | 'final-boss' | 'complete' | 'error'>('loading')
  const [currentRoundIndex, setCurrentRoundIndex] = useState(0)
  const [rounds, setRounds] = useState<UlTimoteRound[]>([])
  const [totalScore, setTotalScore] = useState(0)
  const [totalCorrectAnswers, setTotalCorrectAnswers] = useState(0)
  const [maxStreak, setMaxStreak] = useState(0)
  const [roundResults, setRoundResults] = useState<RoundResult[]>([])
  const [categoryStats, setCategoryStats] = useState<CategoryStats[]>([])
  const [achievements, setAchievements] = useState<string[]>([])
  const [gameStartTime] = useState(Date.now())
  const [isGeneratingQuestions, setIsGeneratingQuestions] = useState(false)

  const currentRound = rounds[currentRoundIndex]

  // Initialize game rounds
  useEffect(() => {
    const initializeGame = async () => {
      setIsGeneratingQuestions(true)
      
      try {
        const generatedRounds = await generateGameRounds(config)
        
        // Check if any rounds have questions
        const hasQuestions = generatedRounds.some(round => round.questions && round.questions.length > 0)
        if (!hasQuestions) {
          console.error('[ulTimote] No questions generated for any round')
          throw new Error('Failed to generate questions for the game')
        }
        
        setRounds(generatedRounds)
        setGameState('round-intro')
        console.log('[ulTimote] Game initialized with', generatedRounds.length, 'rounds')
      } catch (error) {
        console.error('[ulTimote] Failed to initialize game:', error)
        // Show error instead of using fallback
        setGameState('error')
      } finally {
        setIsGeneratingQuestions(false)
      }
    }

    initializeGame()
  }, [config])

  // Generate game rounds based on configuration
  const generateGameRounds = async (config: UlTimoteGameConfig): Promise<UlTimoteRound[]> => {
    const enabledCategories = Object.entries(config.categories)
      .filter(([_, category]) => category.enabled && category.rounds > 0)
      .map(([key, category]) => ({ key, ...category }))

    if (enabledCategories.length === 0) {
      throw new Error('No categories enabled with rounds > 0')
    }

    const rounds: UlTimoteRound[] = []
    
    // Create rounds based on individual category round settings
    for (const category of enabledCategories) {
      for (let roundNum = 0; roundNum < category.rounds; roundNum++) {
        // Generate questions for this round
        const questions = await generateQuestionsForCategory(
          category.key,
          config.questionsPerRound,
          category,
          config
        )

        rounds.push({
          roundNumber: rounds.length + 1,
          category: category.key,
          questions,
          completed: false,
          score: 0
        })
      }
    }
    
    // Shuffle rounds to mix categories
    const shuffledRounds = rounds.sort(() => Math.random() - 0.5)
    
    // Re-number rounds after shuffling
    shuffledRounds.forEach((round, index) => {
      round.roundNumber = index + 1
    })

    // Add final boss round if enabled
    if (config.gameFlow.finalBoss) {
      const finalBossQuestions = await generateFinalBossQuestions(config)
      shuffledRounds.push({
        roundNumber: shuffledRounds.length + 1,
        category: 'final-boss',
        questions: finalBossQuestions,
        completed: false,
        score: 0
      })
    }

    return shuffledRounds
  }

  // Generate questions for specific category
  const generateQuestionsForCategory = async (
    categoryKey: string,
    questionCount: number,
    categoryConfig: any,
    gameConfig: UlTimoteGameConfig
  ): Promise<QuizQuestion[]> => {
    try {
      // Map category to game mode
      const gameModeMap = {
        classic: 'classic',
        quickFire: 'quick-fire',
        audioTricks: 'audio-manipulation',
        albumArt: 'album-art',
        audioFingerprint: 'audio-fingerprint',
        chartPosition: 'chart-position',
        decadeChallenge: 'decade-challenge',
        genreSpecialist: 'genre-specialist',
        generalKnowledge: 'general-knowledge'
      }

      const gameMode = gameModeMap[categoryKey as keyof typeof gameModeMap] || 'classic'

      const settings: any = {
        totalQuestions: questionCount,
        difficultyLevel: categoryConfig.difficulty || categoryConfig.effectLevel || categoryConfig.visualLevel || categoryConfig.expertLevel || 3,
        timeLimit: 30,
        enableHints: false,
        autoPlay: true,
        volume: 70,
        previewDuration: 30
      }

      // Add music filters for music categories
      if (gameMode !== 'general-knowledge') {
        settings.genre = gameConfig.musicFilters.genres.length > 0 ? gameConfig.musicFilters.genres[0] : undefined
      }

      // Add general knowledge specific settings
      if (gameMode === 'general-knowledge' && categoryConfig.categories) {
        // If multiple categories selected, use 'mixed' to get questions from all
        if (categoryConfig.categories.length > 1) {
          settings.category = 'mixed'
          settings.categories = categoryConfig.categories // Pass all selected categories
        } else {
          settings.category = categoryConfig.categories.length > 0 ? categoryConfig.categories[0] : 'mixed'
        }
      }

      const response = await fetch('/api/quiz/questions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify({
          gameMode,
          settings
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: response.statusText }))
        console.error(`[ulTimote] API error for ${categoryKey}:`, response.status, errorData)
        throw new Error(`Failed to fetch questions: ${errorData.error || errorData.message || response.statusText}`)
      }

      const data = await response.json()
      if (!data.success) {
        console.error(`[ulTimote] Questions generation failed for ${categoryKey}:`, data)
        throw new Error(data.error || 'Failed to generate questions')
      }

      console.log(`[ulTimote] Generated ${data.questions.length} questions for ${categoryKey}`)
      return data.questions
    } catch (error) {
      console.error(`[ulTimote] Failed to generate questions for ${categoryKey}:`, error)
      // Try to generate fallback questions for the specific game mode
      try {
        console.log(`[ulTimote] Attempting fallback question generation for ${gameMode}`)
        const fallbackResponse = await fetch('/api/quiz/questions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            gameMode: gameMode === 'ultimote' ? 'classic' : gameMode, // Use classic as fallback for ultimote
            settings: { ...settings, totalQuestions: questionCount }
          })
        })
        
        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json()
          if (fallbackData.success && fallbackData.questions) {
            console.log(`[ulTimote] Fallback generated ${fallbackData.questions.length} questions`)
            return fallbackData.questions
          }
        }
      } catch (fallbackError) {
        console.error(`[ulTimote] Fallback also failed:`, fallbackError)
      }
      
      // Return empty array as last resort
      return []
    }
  }

  // Generate final boss questions (hardest mix)
  const generateFinalBossQuestions = async (config: UlTimoteGameConfig): Promise<QuizQuestion[]> => {
    const enabledCategories = Object.entries(config.categories)
      .filter(([_, category]) => category.enabled)
      .map(([key]) => key)

    const questions: QuizQuestion[] = []
    const questionsPerCategory = Math.max(1, Math.floor(5 / enabledCategories.length))

    for (const categoryKey of enabledCategories) {
      const categoryQuestions = await generateQuestionsForCategory(
        categoryKey,
        questionsPerCategory,
        { difficulty: 5, effectLevel: 5, visualLevel: 5, expertLevel: 5 },
        config
      )
      questions.push(...categoryQuestions)
    }

    // Shuffle final boss questions
    return questions.sort(() => Math.random() - 0.5).slice(0, 5)
  }



  // Start current round
  const startRound = useCallback(() => {
    if (currentRound) {
      setGameState('playing')
    }
  }, [currentRound])

  // Handle round completion
  const handleRoundComplete = useCallback((results: any) => {
    if (!currentRound) return

    const roundResult: RoundResult = {
      roundNumber: currentRound.roundNumber,
      category: currentRound.category,
      questions: results.totalQuestions || currentRound.questions.length,
      score: results.score || results.totalScore || 0,
      correctAnswers: results.correctAnswers || 0,
      timeSpent: results.averageResponseTime || 0,
      streak: results.maxStreak || 0
    }

    setRoundResults(prev => [...prev, roundResult])
    setTotalScore(prev => prev + roundResult.score)
    setTotalCorrectAnswers(prev => prev + roundResult.correctAnswers)
    setMaxStreak(prev => Math.max(prev, roundResult.streak))

    // Update category stats
    setCategoryStats(prev => {
      const existing = prev.find(stat => stat.category === currentRound.category)
      if (existing) {
        return prev.map(stat => 
          stat.category === currentRound.category
            ? {
                ...stat,
                questionsAsked: stat.questionsAsked + roundResult.questions,
                correctAnswers: stat.correctAnswers + roundResult.correctAnswers,
                totalScore: stat.totalScore + roundResult.score,
                averageTime: (stat.averageTime + roundResult.timeSpent) / 2,
                bestStreak: Math.max(stat.bestStreak, roundResult.streak)
              }
            : stat
        )
      } else {
        return [...prev, {
          category: currentRound.category,
          questionsAsked: roundResult.questions,
          correctAnswers: roundResult.correctAnswers,
          totalScore: roundResult.score,
          averageTime: roundResult.timeSpent,
          bestStreak: roundResult.streak
        }]
      }
    })

    // Check for achievements
    checkAchievements(roundResult, results)

    // Mark round as completed
    setRounds(prev => prev.map(round => 
      round.roundNumber === currentRound.roundNumber 
        ? { ...round, completed: true, score: roundResult.score }
        : round
    ))

    // Move to next round or complete game
    if (currentRoundIndex < rounds.length - 1) {
      setTimeout(() => {
        setCurrentRoundIndex(prev => prev + 1)
        setGameState('round-intro')
      }, 2000)
    } else {
      setTimeout(() => {
        completeGame()
      }, 2000)
    }
  }, [currentRound, currentRoundIndex, rounds.length])

  // Check for achievements
  const checkAchievements = (roundResult: RoundResult, results: any) => {
    const newAchievements: string[] = []

    if (roundResult.streak >= 10) {
      newAchievements.push('Streak Master')
    }
    if (roundResult.score >= 1000) {
      newAchievements.push('High Scorer')
    }
    if (roundResult.correctAnswers === roundResult.questions) {
      newAchievements.push('Perfect Round')
    }
    if (results.perfectFingerprints > 0) {
      newAchievements.push('Audio Expert')
    }
    if (results.perfectIdentifications > 0) {
      newAchievements.push('Visual Master')
    }

    setAchievements(prev => [...prev, ...newAchievements.filter(a => !prev.includes(a))])
  }

  // Complete game
  const completeGame = () => {
    const duration = Date.now() - gameStartTime
    const results: UlTimoteGameResults = {
      totalScore,
      totalQuestions: roundResults.reduce((sum, r) => sum + r.questions, 0),
      correctAnswers: totalCorrectAnswers,
      maxStreak,
      roundResults,
      categoryStats,
      achievements,
      gameMode: 'ultimote',
      gameName: config.gameName,
      duration
    }

    setGameState('complete')
    setTimeout(() => onComplete(results), 2000)
  }

  // Render specialized quiz component based on category
  const renderQuizComponent = () => {
    if (!currentRound) return null
    
    // Check if current round has questions
    if (!currentRound.questions || currentRound.questions.length === 0) {
      console.error('[ulTimote] Current round has no questions:', currentRound)
      return (
        <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4">
          <Card className="max-w-4xl mx-auto">
            <CardContent className="p-8 text-center">
              <X className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">No Questions Available</h2>
              <p className="text-lg text-gray-400 mb-6">
                Failed to load questions for {currentRound.category}. 
              </p>
              <Button onClick={onBackToMenu}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Menu
              </Button>
            </CardContent>
          </Card>
        </div>
      )
    }

    const baseProps = {
      questions: currentRound.questions,
      onComplete: handleRoundComplete,
      onBackToMenu: onBackToMenu
    }

    switch (currentRound.category) {
      case 'quickFire':
        return <QuickFireQuiz {...baseProps} />
      case 'audioTricks':
        return <AudioTricksQuiz {...baseProps} />
      case 'albumArt':
        return <AlbumArtQuiz {...baseProps} />
      case 'audioFingerprint':
        return <AudioFingerprintQuiz {...baseProps} />
      case 'generalKnowledge':
        return <GeneralKnowledgeQuiz {...baseProps} />
      default:
        // Return standard quiz interface for other categories
        return (
          <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4">
            <Card className="max-w-4xl mx-auto">
              <CardContent className="p-8 text-center">
                <h2 className="text-2xl font-bold mb-4">Standard Quiz Interface</h2>
                <p className="text-gray-400 mb-6">Category: {currentRound.category}</p>
                <Button onClick={() => handleRoundComplete({ totalScore: 500, correctAnswers: 3, maxStreak: 3 })}>
                  Complete Round (Mock)
                </Button>
              </CardContent>
            </Card>
          </div>
        )
    }
  }

  // Use mobile version on small screens
  if (isMobile) {
    return <UlTimoteGameMobile config={config} onComplete={onComplete} onBackToMenu={onBackToMenu} />
  }

  if (gameState === 'loading' || isGeneratingQuestions) {
    return (
      <motion.div
        className="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", duration: 0.6 }}
            >
              <Crown className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Preparing ulTimote Game</h2>
              <p className="text-lg text-muted-foreground mb-4">{config.gameName}</p>
              <div className="animate-spin rounded-full h-8 w-8 border-4 border-yellow-500/30 border-t-yellow-500 mx-auto"></div>
              <p className="text-sm text-gray-400 mt-4">Generating {config.totalRounds} rounds...</p>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  if (gameState === 'playing') {
    return renderQuizComponent()
  }

  if (gameState === 'complete') {
    return (
      <motion.div
        className="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", duration: 0.6 }}
            >
              <Trophy className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">ulTimote Complete!</h2>
              <p className="text-lg text-muted-foreground">Processing final results...</p>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  if (gameState === 'error') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4">
        <div className="max-w-4xl mx-auto">
          <Button 
            variant="ghost" 
            onClick={onBackToMenu}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>

          <Card className="bg-black/20 backdrop-blur-md border-white/20">
            <CardContent className="p-8 text-center">
              <X className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Unable to Start Game</h2>
              <p className="text-lg text-gray-400 mb-6">
                We couldn&apos;t generate questions for your selected categories. 
                Please try adjusting your configuration.
              </p>
              <Button
                onClick={onBackToMenu}
                className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white"
              >
                Back to Configuration
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Round intro screen
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <Crown className="w-8 h-8 text-yellow-400" />
            <h1 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-purple-400 bg-clip-text text-transparent">
              {config.gameName}
            </h1>
          </div>
        </motion.div>

        {/* Game Progress */}
        <Card className="bg-black/20 backdrop-blur-md border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <span className="text-lg font-semibold">Game Progress</span>
              <Badge variant="outline" className="bg-yellow-500/20 text-yellow-300">
                Round {currentRoundIndex + 1} of {rounds.length}
              </Badge>
            </div>
            <Progress 
              value={((currentRoundIndex) / rounds.length) * 100} 
              className="h-3 mb-4"
            />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-purple-400">{totalScore.toLocaleString()}</div>
                <div className="text-sm text-gray-400">Total Score</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-400">{totalCorrectAnswers}</div>
                <div className="text-sm text-gray-400">Correct Answers</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-400">{maxStreak}</div>
                <div className="text-sm text-gray-400">Best Streak</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Round Info */}
        {currentRound && (
          <Card className="bg-black/20 backdrop-blur-md border-white/20">
            <CardContent className="p-8 text-center">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                {currentRound.category === 'final-boss' ? (
                  <>
                    <Trophy className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
                    <h2 className="text-3xl font-bold mb-2 text-yellow-400">FINAL BOSS</h2>
                    <p className="text-lg text-gray-300 mb-6">Ultimate challenge awaits!</p>
                  </>
                ) : (
                  <>
                    {(() => {
                      const Icon = CATEGORY_ICONS[currentRound.category as keyof typeof CATEGORY_ICONS] || Music
                      const colorClass = CATEGORY_COLORS[currentRound.category as keyof typeof CATEGORY_COLORS] || 'text-blue-400'
                      return <Icon className={`w-16 h-16 ${colorClass} mx-auto mb-4`} />
                    })()}
                    <h2 className="text-3xl font-bold mb-2">Round {currentRound.roundNumber}</h2>
                    <p className="text-lg text-gray-300 mb-2 capitalize">
                      {currentRound.category.replace(/([A-Z])/g, ' $1')} Challenge
                    </p>
                  </>
                )}
                
                <div className="flex items-center justify-center gap-4 mb-8">
                  <Badge variant="outline" className="bg-blue-500/20 text-blue-300">
                    {currentRound.questions.length} Questions
                  </Badge>
                  {currentRound.category !== 'final-boss' && (
                    <Badge variant="outline" className="bg-purple-500/20 text-purple-300">
                      {currentRound.category.charAt(0).toUpperCase() + currentRound.category.slice(1)} Mode
                    </Badge>
                  )}
                </div>

                <Button
                  onClick={startRound}
                  size="lg"
                  className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white px-8 py-3"
                >
                  <Flame className="w-5 h-5 mr-2" />
                  Start Round
                </Button>
              </motion.div>
            </CardContent>
          </Card>
        )}

        {/* Completed Rounds */}
        {roundResults.length > 0 && (
          <Card className="bg-black/20 backdrop-blur-md border-white/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5 text-green-400" />
                Completed Rounds
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {roundResults.map((result) => {
                  const Icon = CATEGORY_ICONS[result.category as keyof typeof CATEGORY_ICONS] || Music
                  const colorClass = CATEGORY_COLORS[result.category as keyof typeof CATEGORY_COLORS] || 'text-blue-400'
                  
                  return (
                    <div key={result.roundNumber} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Icon className={`w-5 h-5 ${colorClass}`} />
                        <span className="font-medium">Round {result.roundNumber}</span>
                        <Badge variant="outline" className="text-xs capitalize">
                          {result.category.replace(/([A-Z])/g, ' $1')}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm">
                        <span className="text-green-400">{result.correctAnswers}/{result.questions}</span>
                        <span className="text-yellow-400">{result.score.toLocaleString()}</span>
                        {result.streak > 0 && (
                          <Badge variant="outline" className="text-xs bg-orange-500/20 text-orange-300">
                            {result.streak}x streak
                          </Badge>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Back Button */}
        <div className="flex justify-center">
          <Button variant="outline" onClick={onBackToMenu}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Menu
          </Button>
        </div>
      </div>
    </div>
  )
}