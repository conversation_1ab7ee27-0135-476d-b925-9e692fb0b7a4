'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Volume2, Headphones, AlertCircle } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

type SpeakerOutput = 'onboard' | 'alsa'

export function SpeakerOutputSwitcher() {
  const [output, setOutput] = useState<SpeakerOutput>('alsa')
  const [switching, setSwitching] = useState(false)
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    fetchCurrentOutput()
  }, [])

  const fetchCurrentOutput = async () => {
    try {
      const response = await fetch('/api/audio/speaker-output')
      if (response.ok) {
        const data = await response.json()
        setOutput(data.output === 'onboard' ? 'onboard' : 'alsa')
      }
    } catch (error) {
      console.error('Failed to fetch speaker output status:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSwitch = async (newOutput: SpeakerOutput) => {
    if (output === newOutput) return
    
    setSwitching(true)
    
    try {
      const response = await fetch('/api/audio/speaker-output', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ output: newOutput })
      })

      if (!response.ok) {
        throw new Error('Failed to switch audio output')
      }

      const result = await response.json()
      setOutput(newOutput)
      
      toast({
        title: 'Audio Output Changed',
        description: result.message || `Switched to ${newOutput === 'alsa' ? 'ALSA (Headphones)' : 'Onboard Speaker'}`
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to switch audio output',
        variant: 'destructive'
      })
    } finally {
      setSwitching(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Speaker Output</CardTitle>
        <CardDescription>
          Control whether audio plays through the onboard speaker or ALSA (headphone jack)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="flex items-center gap-3">
            <Volume2 className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="font-medium">Onboard Speaker</p>
              <p className="text-sm text-muted-foreground">Built-in laptop/PC speaker</p>
            </div>
          </div>
          <Button
            variant={output === 'onboard' ? 'default' : 'outline'}
            onClick={() => handleSwitch('onboard')}
            disabled={switching || loading}
          >
            {output === 'onboard' ? 'Active' : 'Switch'}
          </Button>
        </div>

        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="flex items-center gap-3">
            <Headphones className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="font-medium">ALSA (Headphones)</p>
              <p className="text-sm text-muted-foreground">Headphone jack / External speakers</p>
            </div>
          </div>
          <Button
            variant={output === 'alsa' ? 'default' : 'outline'}
            onClick={() => handleSwitch('alsa')}
            disabled={switching || loading}
          >
            {output === 'alsa' ? 'Active' : 'Switch'}
          </Button>
        </div>

        <div className="flex items-start gap-2 p-3 bg-muted/50 rounded-lg">
          <AlertCircle className="h-4 w-4 text-muted-foreground mt-0.5" />
          <p className="text-sm text-muted-foreground">
            ALSA output disables the onboard speaker and routes audio through the headphone jack only.
            This setting persists across reboots.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}