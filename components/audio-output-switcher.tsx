'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { Headphones, Bluetooth, Loader2, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react'

interface AudioStatus {
  currentMode: 'alsa' | 'bluetooth'
  bluetoothConnected: boolean
  bluetoothDevices: string[]
}

export function AudioOutputSwitcher() {
  const [status, setStatus] = useState<AudioStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [switching, setSwitching] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  
  useEffect(() => {
    loadStatus()
    // Auto-refresh status every 10 seconds
    const interval = setInterval(loadStatus, 10000)
    return () => clearInterval(interval)
  }, [])
  
  const loadStatus = async () => {
    try {
      const response = await fetch('/api/mpd/audio-output')
      
      if (!response.ok) {
        throw new Error(`Failed to get status: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      if (data.success) {
        setStatus({
          currentMode: data.currentMode,
          bluetoothConnected: data.bluetoothConnected,
          bluetoothDevices: data.bluetoothDevices || []
        })
      } else {
        throw new Error(data.message || 'Failed to load audio status')
      }
    } catch (error) {
      console.error('Failed to load audio output status:', error)
      toast.error('Failed to load audio output status', {
        description: error instanceof Error ? error.message : 'Unknown error',
        duration: 5000
      })
    } finally {
      setLoading(false)
    }
  }
  
  const switchOutput = async (mode: 'alsa' | 'bluetooth') => {
    if (!status || status.currentMode === mode) return
    
    // Check if Bluetooth is available when switching to it
    if (mode === 'bluetooth' && !status.bluetoothConnected) {
      toast.warning('No Bluetooth device connected', {
        description: 'Please connect a Bluetooth audio device first',
        duration: 4000
      })
      return
    }
    
    setSwitching(true)
    
    try {
      const response = await fetch('/api/mpd/audio-output', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ mode })
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `Failed to switch: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      if (data.success) {
        toast.success('Audio output switched', {
          description: `Now playing through ${mode === 'alsa' ? 'headphone jack' : 'Bluetooth'}`,
          duration: 3000,
          icon: <CheckCircle className="h-4 w-4" />
        })
        
        // Reload status after a short delay
        setTimeout(() => {
          loadStatus()
        }, 2000)
        
        // Update status immediately to reflect the change
        setStatus(prev => prev ? { ...prev, currentMode: mode } : null)
      } else {
        throw new Error(data.message || 'Failed to switch audio output')
      }
    } catch (error) {
      console.error('Failed to switch audio output:', error)
      toast.error('Failed to switch audio output', {
        description: error instanceof Error ? error.message : 'Unknown error',
        duration: 5000
      })
    } finally {
      setSwitching(false)
    }
  }
  
  const handleRefresh = async () => {
    setRefreshing(true)
    await loadStatus()
    setRefreshing(false)
    toast.success('Status refreshed', {
      duration: 2000
    })
  }
  
  if (loading) {
    return (
      <Card className="bg-white/5 dark:bg-white/5 border border-gray-200/20 dark:border-gray-700/20">
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className="bg-white/5 dark:bg-white/5 border border-gray-200/20 dark:border-gray-700/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <Headphones className="h-5 w-5" />
              Audio Output
            </CardTitle>
            <CardDescription className="text-xs sm:text-sm">
              Switch between headphone jack and Bluetooth speakers
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing || switching}
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Status */}
        <div className="flex items-center justify-between p-3 rounded-lg bg-white/5 dark:bg-white/5">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Current Output:</span>
            {status?.currentMode === 'bluetooth' ? (
              <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                <Bluetooth className="h-3 w-3 mr-1" />
                Bluetooth
              </Badge>
            ) : (
              <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                <Headphones className="h-3 w-3 mr-1" />
                Headphone Jack
              </Badge>
            )}
          </div>
        </div>
        
        {/* Bluetooth Status */}
        {status?.bluetoothConnected && status.bluetoothDevices.length > 0 && (
          <div className="text-xs text-muted-foreground space-y-1">
            <p className="font-medium">Connected Bluetooth devices:</p>
            {status.bluetoothDevices.map((device, index) => (
              <p key={index} className="pl-2">• {device}</p>
            ))}
          </div>
        )}
        
        {/* Switch Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant={status?.currentMode === 'alsa' ? 'default' : 'outline'}
            onClick={() => switchOutput('alsa')}
            disabled={switching || status?.currentMode === 'alsa'}
            className="w-full"
          >
            {switching && status?.currentMode !== 'alsa' ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Headphones className="h-4 w-4 mr-2" />
            )}
            Headphone Jack
            {status?.currentMode === 'alsa' && (
              <CheckCircle className="h-3 w-3 ml-1" />
            )}
          </Button>
          
          <Button
            variant={status?.currentMode === 'bluetooth' ? 'default' : 'outline'}
            onClick={() => switchOutput('bluetooth')}
            disabled={switching || status?.currentMode === 'bluetooth' || !status?.bluetoothConnected}
            className="w-full"
          >
            {switching && status?.currentMode !== 'bluetooth' ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Bluetooth className="h-4 w-4 mr-2" />
            )}
            Bluetooth
            {status?.currentMode === 'bluetooth' && (
              <CheckCircle className="h-3 w-3 ml-1" />
            )}
          </Button>
        </div>
        
        {/* Warning if no Bluetooth */}
        {!status?.bluetoothConnected && (
          <div className="flex items-start gap-2 p-3 rounded-lg bg-yellow-500/10 border border-yellow-500/20">
            <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5" />
            <div className="text-xs text-yellow-600 dark:text-yellow-400">
              <p className="font-medium">No Bluetooth device connected</p>
              <p>Connect a Bluetooth speaker to enable Bluetooth output</p>
            </div>
          </div>
        )}
        
        {/* Status message */}
        {status?.currentMode === 'bluetooth' && status?.bluetoothConnected && (
          <div className="text-xs text-muted-foreground">
            <p className="text-green-600 dark:text-green-400">✓ Currently playing through Bluetooth</p>
          </div>
        )}
        
        {status?.currentMode === 'alsa' && (
          <div className="text-xs text-muted-foreground">
            <p className="text-green-600 dark:text-green-400">✓ Currently playing through headphone jack</p>
          </div>
        )}
        
        {/* Info */}
        <div className="text-xs text-muted-foreground pt-2 border-t">
          <p>Switching audio output will restart MPD to apply the new configuration.</p>
        </div>
      </CardContent>
    </Card>
  )
}