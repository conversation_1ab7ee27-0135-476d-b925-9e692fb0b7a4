'use client'

import React, { useState, useEffect, memo, useRef } from 'react'
import Image from 'next/image'
import { ImageIcon } from 'lucide-react'
import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'
import { logAlbumArtChange } from '@/lib/album-art-logger'

// LRU Cache implementation for album art URLs
class LRUCache<K, V> {
  private cache = new Map<K, V>()
  private maxSize: number

  constructor(maxSize: number) {
    this.maxSize = maxSize
  }

  get(key: K): V | undefined {
    const value = this.cache.get(key)
    if (value !== undefined) {
      // Move to end (most recently used)
      this.cache.delete(key)
      this.cache.set(key, value)
    }
    return value
  }

  set(key: K, value: V): void {
    // Remove oldest if at capacity
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    // Delete and re-add to move to end
    this.cache.delete(key)
    this.cache.set(key, value)
  }

  has(key: K): boolean {
    return this.cache.has(key)
  }

  clear(): void {
    this.cache.clear()
  }

  get size(): number {
    return this.cache.size
  }
}

// Use LRU caches with reasonable limits
const albumArtCache = new LRUCache<string, AlbumArtUrls>(500)
// Cache for failed requests to avoid repeated 404s
const failedRequestCache = new LRUCache<string, boolean>(200)
// Cache to track if we've already processed a trackId+fallbackUrl combination
const processedCombinations = new LRUCache<string, boolean>(300)

// Clear the failed cache since we've fixed the ID mapping issue
if (typeof window !== 'undefined') {
  failedRequestCache.clear()
}

// No need for periodic cleanup with LRU caches - they self-manage their size

interface AlbumArtProps {
  trackId?: string
  artist: string
  album: string
  title?: string
  size?: 'sm' | 'thumbnail' | 'cover' | 'large' | 'xl'
  className?: string
  fallbackUrl?: string
  showPlaceholder?: boolean
  priority?: boolean
  eager?: boolean
}

interface AlbumArtUrls {
  thumbnail: string
  cover: string
  original: string
}

function AlbumArtComponent({ 
  trackId,
  artist, 
  album,
  title,
  size = 'thumbnail',
  className,
  fallbackUrl,
  showPlaceholder = true,
  priority = false,
  eager = false
}: AlbumArtProps) {
  // Create a comprehensive identifier that includes all track-specific data
  // This ensures the component updates when ANY track detail changes
  const trackIdentifier = `${trackId || 'no-id'}-${fallbackUrl || 'no-url'}-${artist}-${album}-${title || 'no-title'}`
  
  // Initialize state from cache if available
  const [albumArt, setAlbumArt] = useState<AlbumArtUrls | null>(() => {
    if (trackId && albumArtCache.has(trackId)) {
      return albumArtCache.get(trackId)!
    }
    if (fallbackUrl && !fallbackUrl.startsWith('http')) {
      return {
        thumbnail: fallbackUrl,
        cover: fallbackUrl,
        original: fallbackUrl
      }
    }
    return null
  })
  const [isLoading, setIsLoading] = useState(() => {
    // Not loading if we already have data
    return !albumArt
  })
  const [error, setError] = useState(false)

  // Size configurations
  const sizeConfig = {
    sm: { width: 150, height: 150, className: 'w-12 h-12' },
    thumbnail: { width: 150, height: 150, className: 'w-16 h-16' },
    cover: { width: 500, height: 500, className: 'w-32 h-32' },
    large: { width: 500, height: 500, className: 'w-64 h-64' },
    xl: { width: 500, height: 500, className: 'w-80 h-80' }
  }

  const config = sizeConfig[size] || sizeConfig.thumbnail // Fallback to thumbnail if size is invalid

  // Load album art when track changes
  useEffect(() => {
    // Use centralized logger to reduce spam
    logAlbumArtChange(`ID: ${trackId}, Artist: ${artist}, Title: ${title}`)
    
    // Reset state when track changes
    setAlbumArt(null)
    setError(false)
    setIsLoading(true)
    
    // Skip if no trackId and no fallbackUrl
    if (!trackId && !fallbackUrl) {
      // console.log('[AlbumArt] No trackId or fallbackUrl - showing placeholder')
      setIsLoading(false)
      setError(true) // Show placeholder for tracks without any art
      return
    }
    
    const loadArt = async () => {
      // Try cache first
      if (trackId && albumArtCache.has(trackId)) {
        const cached = albumArtCache.get(trackId)!
        // console.log(`[AlbumArt] Using cached art for trackId: ${trackId}`)
        setAlbumArt(cached)
        setIsLoading(false)
        setError(false)
        return
      }
      
      // Use fallback if available and no trackId (but only if it's a local path)
      if (!trackId && fallbackUrl && !fallbackUrl.startsWith('http')) {
        const urls = {
          thumbnail: fallbackUrl,
          cover: fallbackUrl,
          original: fallbackUrl
        }
        setAlbumArt(urls)
        setIsLoading(false)
        setError(false)
        return
      }
      
      // Load from API if we have trackId
      if (trackId) {
        await loadAlbumArt()
      }
    }

    loadArt()
  }, [trackIdentifier]) // Re-run when track identifier changes

  const loadAlbumArt = async () => {
    if (!trackId) return

    // Skip numeric IDs - these are MPD internal IDs, not database IDs
    if (!isNaN(Number(trackId))) {
      // Only log once per numeric trackId
      if (!failedRequestCache.has(trackId)) {
        // console.log(`[AlbumArt] Skipping numeric trackId: ${trackId} - using fallback: ${fallbackUrl}`)
        failedRequestCache.set(trackId, true)
      }
      
      if (fallbackUrl && !fallbackUrl.startsWith('http')) {
        const urls = {
          thumbnail: fallbackUrl,
          cover: fallbackUrl,
          original: fallbackUrl
        }
        // Cache the fallback URL for this trackId
        albumArtCache.set(trackId, urls)
        setAlbumArt(urls)
        // console.log(`[AlbumArt] Set fallback URLs for numeric ID ${trackId}:`, urls)
      } else {
        // console.log(`[AlbumArt] No fallback URL for numeric trackId: ${trackId}`)
        setError(true)
      }
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(false)
      
      // Check cache first
      if (albumArtCache.has(trackId)) {
        const cachedArt = albumArtCache.get(trackId)!
        setAlbumArt(cachedArt)
        setIsLoading(false)
        return
      }
      
      // Check if this request has failed before
      if (failedRequestCache.get(trackId)) {
        // console.log(`[AlbumArt] Skipping known failed trackId: ${trackId}`)
        if (fallbackUrl && !fallbackUrl.startsWith('http')) {
          setAlbumArt({
            thumbnail: fallbackUrl,
            cover: fallbackUrl,
            original: fallbackUrl
          })
        } else {
          setError(true)
        }
        setIsLoading(false)
        return
      }
      
      // Use the new intelligent availability checking
      const availabilityResponse = await fetch(`/api/album-art/status/${trackId}`)
      
      if (availabilityResponse.ok) {
        const availability = await availabilityResponse.json()
        
        if (availability.success && availability.hasLocal) {
          // We have local files, construct URLs directly
          const albumArtPaths: AlbumArtUrls = {
            thumbnail: availability.localPaths.thumbnail 
              ? `/api/images/${availability.localPaths.thumbnail}`
              : availability.localPaths.cover 
                ? `/api/images/${availability.localPaths.cover}` 
                : availability.localPaths.original 
                  ? `/api/images/${availability.localPaths.original}` 
                  : '',
            cover: availability.localPaths.cover 
              ? `/api/images/${availability.localPaths.cover}`
              : availability.localPaths.original 
                ? `/api/images/${availability.localPaths.original}` 
                : availability.localPaths.thumbnail 
                  ? `/api/images/${availability.localPaths.thumbnail}` 
                  : '',
            original: availability.localPaths.original 
              ? `/api/images/${availability.localPaths.original}`
              : availability.localPaths.cover 
                ? `/api/images/${availability.localPaths.cover}` 
                : availability.localPaths.thumbnail 
                  ? `/api/images/${availability.localPaths.thumbnail}` 
                  : ''
          }
          
          // Only cache and use if we have at least one valid path
          if (albumArtPaths.thumbnail || albumArtPaths.cover || albumArtPaths.original) {
            albumArtCache.set(trackId, albumArtPaths)
            setAlbumArt(albumArtPaths)
            setIsLoading(false)
            return
          }
        }
        
        // If no local files but availability check worked, 
        // we know not to make unnecessary API calls to missing images
        // Only log once per trackId to avoid spam
        if (!failedRequestCache.has(trackId)) {
          // console.log(`[AlbumArt] No local files available for trackId: ${trackId}`)
        }
        
        // Mark as failed to prevent repeated checks
        failedRequestCache.set(trackId, true)
      }
      
      // Fallback to the original database lookup method
      const trackResponse = await fetch(`/api/songs/${trackId}`)
      
      if (!trackResponse.ok) {
        console.warn(`Song API failed: ${trackResponse.status} for trackId: ${trackId}`)
        
        // Mark this trackId as failed to avoid repeated requests
        if (trackResponse.status === 404) {
          failedRequestCache.set(trackId, true)
        }
        
        // If API fails, fall back to fallbackUrl
        if (fallbackUrl && !fallbackUrl.startsWith('http')) {
          setAlbumArt({
            thumbnail: fallbackUrl,
            cover: fallbackUrl,
            original: fallbackUrl
          })
          setIsLoading(false)
          return
        }
        setError(true)
        setIsLoading(false)
        return
      }
      
      const trackData = await trackResponse.json()
      
      if (trackData.success && trackData.song) {
        const track = trackData.song
        
        // Skip external URLs - we should only use local files
        // External URLs from the database should have been downloaded already
        if (track.albumArtUrl && track.albumArtUrl.startsWith('http')) {
          // console.log(`[AlbumArt] Skipping external URL for ${trackId}, should use local files`)
          // Don't use external URLs, fall through to error handling
        }
      }
      
      // If API call succeeded but no track found, try fallback (only if local)
      if (fallbackUrl && !fallbackUrl.startsWith('http')) {
        setAlbumArt({
          thumbnail: fallbackUrl,
          cover: fallbackUrl,
          original: fallbackUrl
        })
        setIsLoading(false)
        return
      }
      
      // If nothing found, set error to show placeholder
      setError(true)
    } catch (error) {
      console.error('Failed to load album art for trackId:', trackId, error)
      
      // Try fallback before showing error
      if (fallbackUrl) {
        setAlbumArt({
          thumbnail: fallbackUrl,
          cover: fallbackUrl,
          original: fallbackUrl
        })
        setIsLoading(false)
        return
      }
      
      setError(true)
    } finally {
      setIsLoading(false)
    }
  }

  const processAlbumArt = async () => {
    if (!trackId) return

    try {
      const response = await fetch('/api/album-art/process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          trackId,
          artist,
          album,
          sourceUrl: fallbackUrl
        })
      })

      const data = await response.json()
      if (data.success && data.paths) {
        setAlbumArt(data.paths)
      }
    } catch (error) {
      console.error('Failed to process album art:', error)
      setError(true)
    }
  }

  // Get the appropriate image source
  const getImageSrc = (): string | null => {
    if (albumArt) {
      switch (size) {
        case 'sm':
        case 'thumbnail':
          return albumArt.thumbnail
        case 'cover':
        case 'large':
        case 'xl':
          // For large/xl sizes, prefer cover but fallback to thumbnail if cover is empty
          const coverSrc = albumArt.cover?.trim()
          const thumbnailSrc = albumArt.thumbnail?.trim()
          
          if (coverSrc && coverSrc !== '') {
            return coverSrc
          }
          if (thumbnailSrc && thumbnailSrc !== '') {
            return thumbnailSrc
          }
          return albumArt.original?.trim() || ''
        default:
          return albumArt.thumbnail
      }
    }
    return fallbackUrl || null
  }

  const imageSrc = getImageSrc()
  
  // Debug logging removed - album art is now working

  // Loading state
  if (isLoading) {
    return (
      <div 
        data-testid="album-art-loading"
        className={cn(
          'bg-muted animate-pulse rounded-md flex items-center justify-center',
          config.className,
          className
        )}
      >
        <ImageIcon className="h-6 w-6 text-muted-foreground" />
      </div>
    )
  }

  // Error state or no image available
  if (error || !imageSrc) {
    if (!showPlaceholder) {
      return null
    }

    return (
      <motion.div 
        initial={{ opacity: 0, scale: 1.05 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className={cn(
          'bg-gradient-to-br from-purple-500/20 to-blue-500/20 dark:from-purple-600/30 dark:to-blue-600/30 rounded-md flex flex-col items-center justify-center text-center p-2 overflow-hidden backdrop-blur-sm border border-white/10',
          config.className,
          className
        )}
      >
        <div className="bg-white/10 dark:bg-white/5 rounded-full p-3 mb-2">
          <ImageIcon className={cn(
            'text-white/60 dark:text-white/70',
            size === 'sm' || size === 'thumbnail' ? 'h-4 w-4' : 
            size === 'cover' ? 'h-8 w-8' :
            size === 'large' ? 'h-12 w-12' :
            'h-16 w-16'
          )} />
        </div>
        {(size === 'large' || size === 'xl') && (
          <>
            <div className="text-sm font-medium text-white/80 dark:text-white/90 leading-tight truncate w-full px-2 mb-1">
              {artist || 'Unknown Artist'}
            </div>
            {album && album !== artist && album !== 'Unknown Album' && (
              <div className="text-xs text-white/60 dark:text-white/70 leading-tight truncate w-full px-2">
                {album}
              </div>
            )}
          </>
        )}
      </motion.div>
    )
  }

  return (
    <div className={cn('relative overflow-hidden rounded-md', config.className, className)}>
      <div className="relative w-full h-full">
        <Image
          src={imageSrc}
          alt={`${artist} - ${album}`}
          width={config.width}
          height={config.height}
          className="object-cover w-full h-full transition-opacity duration-300"
          loading={priority || eager ? "eager" : "lazy"}
          priority={priority}
          onError={() => setError(true)}
          fetchPriority={priority ? "high" : eager ? "low" : "auto"}
        />
      </div>
    </div>
  )
}

// Memoized AlbumArt component - only re-renders when track changes
export const AlbumArt = memo(AlbumArtComponent, (prevProps, nextProps) => {
  // Create comprehensive identifiers for comparison that include ALL track details
  const prevId = `${prevProps.trackId || 'no-id'}-${prevProps.fallbackUrl || 'no-url'}-${prevProps.artist}-${prevProps.album}-${prevProps.title || 'no-title'}`
  const nextId = `${nextProps.trackId || 'no-id'}-${nextProps.fallbackUrl || 'no-url'}-${nextProps.artist}-${nextProps.album}-${nextProps.title || 'no-title'}`
  
  // Re-render if ANY track detail changes
  const shouldNotRerender = prevId === nextId && prevProps.size === nextProps.size
  
  if (!shouldNotRerender) {
    // console.log(`[AlbumArt] Memo: Re-rendering due to change - prev: ${prevId}, next: ${nextId}`)
  }
  
  return shouldNotRerender
})

// Utility component for track lists
export function TrackAlbumArt({ track, size = 'thumbnail', className }: {
  track: {
    id?: string
    artist: string
    album: string
    title?: string
    albumArtUrl?: string
  }
  size?: 'sm' | 'thumbnail' | 'cover' | 'large' | 'xl'
  className?: string
}) {
  return (
    <AlbumArt
      trackId={track.id}
      artist={track.artist}
      album={track.album}
      title={track.title}
      size={size}
      className={className}
      fallbackUrl={track.albumArtUrl}
    />
  )
}
