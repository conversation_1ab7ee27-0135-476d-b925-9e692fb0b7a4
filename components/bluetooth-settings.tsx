'use client'

import { useState } from 'react'
import { useBluetooth } from '@/hooks/use-bluetooth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Bluetooth, BluetoothConnected, BluetoothOff, Headphones, Speaker, Smartphone } from 'lucide-react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

export function BluetoothSettings() {
  const {
    status,
    scanning,
    connecting,
    scanDevices,
    connectDevice,
    disconnectDevice,
    removeDevice,
    togglePower,
    autoConnect
  } = useBluetooth()

  const [deviceToRemove, setDeviceToRemove] = useState<string | null>(null)

  if (!status) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  if (!status.available) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert>
            <BluetoothOff className="h-4 w-4" />
            <AlertDescription>
              Bluetooth is not available on this system. Please ensure you have a Bluetooth adapter installed.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  const getDeviceIcon = (device: any) => {
    if (device.name.toLowerCase().includes('headphone') || device.name.toLowerCase().includes('airpod')) {
      return <Headphones className="h-4 w-4" />
    }
    if (device.name.toLowerCase().includes('speaker')) {
      return <Speaker className="h-4 w-4" />
    }
    if (device.audioDevice) {
      return <Headphones className="h-4 w-4" />
    }
    return <Smartphone className="h-4 w-4" />
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {status.enabled ? (
                  <BluetoothConnected className="h-5 w-5 text-blue-600" />
                ) : (
                  <BluetoothOff className="h-5 w-5 text-muted-foreground" />
                )}
                Bluetooth Audio Output
              </CardTitle>
              <CardDescription>
                Stream MPD audio to Bluetooth speakers and headphones
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Label htmlFor="bluetooth-power">Enable</Label>
              <Switch
                id="bluetooth-power"
                checked={status.enabled}
                onCheckedChange={togglePower}
              />
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {status.enabled && (
            <>
              {/* Connected Device */}
              {status.connectedDevice && (
                <div className="rounded-lg border p-4 bg-muted/50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getDeviceIcon(status.connectedDevice)}
                      <div>
                        <p className="font-medium">{status.connectedDevice.name}</p>
                        <p className="text-sm text-muted-foreground">{status.connectedDevice.mac}</p>
                      </div>
                      <Badge variant="default">Connected</Badge>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => disconnectDevice()}
                    >
                      Disconnect
                    </Button>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={scanDevices}
                  disabled={scanning || connecting}
                  className="flex-1"
                >
                  {scanning ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Scanning...
                    </>
                  ) : (
                    <>
                      <Bluetooth className="mr-2 h-4 w-4" />
                      Scan for Devices
                    </>
                  )}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={autoConnect}
                  disabled={scanning || connecting || !!status.connectedDevice}
                >
                  Auto-Connect
                </Button>
              </div>

              {/* Device List */}
              {status.devices.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Available Devices</h4>
                  <div className="space-y-2">
                    {status.devices.map((device) => (
                      <div
                        key={device.mac}
                        className="flex items-center justify-between rounded-lg border p-3"
                      >
                        <div className="flex items-center gap-3">
                          {getDeviceIcon(device)}
                          <div>
                            <p className="font-medium">{device.name}</p>
                            <p className="text-sm text-muted-foreground">{device.mac}</p>
                            <div className="flex gap-2 mt-1">
                              {device.paired && (
                                <Badge variant="secondary" className="text-xs">Paired</Badge>
                              )}
                              {device.trusted && (
                                <Badge variant="secondary" className="text-xs">Trusted</Badge>
                              )}
                              {device.audioDevice && (
                                <Badge variant="secondary" className="text-xs">Audio</Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex gap-2">
                          {!device.connected && (
                            <Button
                              size="sm"
                              onClick={() => connectDevice(device.mac)}
                              disabled={connecting}
                            >
                              {connecting ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                'Connect'
                              )}
                            </Button>
                          )}
                          
                          {device.paired && !device.connected && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => setDeviceToRemove(device.mac)}
                            >
                              Remove
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Scanning message */}
              {scanning && (
                <Alert>
                  <Bluetooth className="h-4 w-4 animate-pulse" />
                  <AlertDescription>
                    Make sure your Bluetooth device is in pairing mode and within range.
                  </AlertDescription>
                </Alert>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Remove Device Confirmation */}
      <AlertDialog open={!!deviceToRemove} onOpenChange={() => setDeviceToRemove(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Bluetooth Device</AlertDialogTitle>
            <AlertDialogDescription>
              This will remove the pairing with this device. You&apos;ll need to pair it again to use it.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (deviceToRemove) {
                  removeDevice(deviceToRemove)
                  setDeviceToRemove(null)
                }
              }}
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}