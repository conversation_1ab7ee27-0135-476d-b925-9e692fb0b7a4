"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, AlertCircle } from "lucide-react"

interface UltimoteConfigDebuggerProps {
  config: any
  className?: string
}

export function UltimoteConfigDebugger({ config, className }: UltimoteConfigDebuggerProps) {
  const calculateTotalQuestions = () => {
    if (!config?.categories) return 0
    
    let totalRounds = 0
    Object.entries(config.categories).forEach(([key, cat]: [string, any]) => {
      if (cat.enabled && cat.rounds > 0) {
        totalRounds += cat.rounds
      }
    })
    
    return totalRounds * (config.questionsPerRound || 0)
  }
  
  const getMusicCategories = () => {
    if (!config?.categories) return []
    
    return Object.entries(config.categories)
      .filter(([key, cat]: [string, any]) => 
        key !== 'generalKnowledge' && cat.enabled && cat.rounds > 0
      )
      .map(([key, cat]: [string, any]) => ({ name: key, ...cat }))
  }
  
  const hasGeneralKnowledge = () => {
    return config?.categories?.generalKnowledge?.enabled && 
           config?.categories?.generalKnowledge?.rounds > 0
  }
  
  const hasOnlyGeneralKnowledge = () => {
    return hasGeneralKnowledge() && getMusicCategories().length === 0
  }
  
  const hasIssues = () => {
    // Check for common configuration issues
    const issues = []
    
    Object.entries(config?.categories || {}).forEach(([key, cat]: [string, any]) => {
      if (cat.enabled && cat.rounds === 0) {
        issues.push(`${key} is enabled but has 0 rounds`)
      }
      if (!cat.enabled && cat.rounds > 0) {
        issues.push(`${key} is disabled but has ${cat.rounds} rounds`)
      }
    })
    
    return issues
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-sm flex items-center gap-2">
          <AlertCircle className="h-4 w-4" />
          Configuration Debugger
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 text-sm">
        {/* Summary */}
        <div>
          <p className="font-medium mb-2">Summary:</p>
          <div className="space-y-1">
            <p>Questions per round: {config?.questionsPerRound || 0}</p>
            <p>Total questions: {calculateTotalQuestions()}</p>
            <p>Music categories enabled: {getMusicCategories().length}</p>
            <p>General knowledge: {hasGeneralKnowledge() ? 'Enabled' : 'Disabled'}</p>
          </div>
        </div>
        
        {/* Configuration Type */}
        <div>
          <p className="font-medium mb-2">Configuration Type:</p>
          {hasOnlyGeneralKnowledge() && (
            <Badge className="bg-green-100 text-green-800">
              General Knowledge Only
            </Badge>
          )}
          {getMusicCategories().length > 0 && !hasGeneralKnowledge() && (
            <Badge className="bg-blue-100 text-blue-800">
              Music Only
            </Badge>
          )}
          {getMusicCategories().length > 0 && hasGeneralKnowledge() && (
            <Badge className="bg-purple-100 text-purple-800">
              Mixed (Music + GK)
            </Badge>
          )}
          {getMusicCategories().length === 0 && !hasGeneralKnowledge() && (
            <Badge variant="destructive">
              No Categories Enabled
            </Badge>
          )}
        </div>
        
        {/* Detailed Categories */}
        <div>
          <p className="font-medium mb-2">Categories:</p>
          <div className="space-y-1">
            {Object.entries(config?.categories || {}).map(([key, cat]: [string, any]) => (
              <div key={key} className="flex items-center gap-2">
                {cat.enabled && cat.rounds > 0 ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <XCircle className="h-3 w-3 text-gray-400" />
                )}
                <span className={cat.enabled ? 'font-medium' : 'text-gray-500'}>
                  {key}: {cat.enabled ? 'enabled' : 'disabled'}, {cat.rounds} rounds
                </span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Issues */}
        {hasIssues().length > 0 && (
          <div className="border-t pt-4">
            <p className="font-medium mb-2 text-red-600">⚠️ Configuration Issues:</p>
            <ul className="space-y-1 text-red-600">
              {hasIssues().map((issue, i) => (
                <li key={i}>• {issue}</li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Raw Config */}
        <details className="border-t pt-4">
          <summary className="cursor-pointer font-medium">Raw Configuration</summary>
          <pre className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto">
            {JSON.stringify(config, null, 2)}
          </pre>
        </details>
      </CardContent>
    </Card>
  )
}