"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { 
  Trophy, Crown, Users, BarChart3, CheckCircle2, 
  Music, Calendar, Gamepad2, Palette, PartyPopper,
  TrendingUp, Award, Star, AlertCircle
} from "lucide-react"
import { VotingResult, VotingOption, PlayerVote } from "@/lib/types"
import confetti from 'canvas-confetti'

interface VotingResultsProps {
  result: VotingResult
  votingType: string
  title: string
  onContinue: () => void
  showContinueButton?: boolean
  autoAdvanceDelay?: number
  className?: string
}

const getVotingTypeIcon = (type: string) => {
  switch (type) {
    case 'category':
      return <Music className="h-5 w-5" />
    case 'decade':
      return <Calendar className="h-5 w-5" />
    case 'game-mode':
      return <Gamepad2 className="h-5 w-5" />
    case 'theme':
      return <Palette className="h-5 w-5" />
    default:
      return <Trophy className="h-5 w-5" />
  }
}

const getOptionEmoji = (option: VotingOption) => {
  const emojiMap: { [key: string]: string } = {
    'rock': '🎸',
    'pop': '🎵',
    'jazz': '🎷',
    'electronic': '🎧',
    'country': '🤠',
    'hip-hop': '🎤',
    'classical': '🎼',
    'hiphop': '🎤',
    '80s': '📻',
    '90s': '💿',
    '2000s': '💽',
    '2010s': '📱',
    'classic': '🎯',
    'challenge': '⚡',
    'collaborative': '🤝',
    'speed': '🏃‍♂️',
    'year': '📅'
  }
  
  const key = option.value.toLowerCase().replace(/[^a-z0-9]/g, '')
  return emojiMap[key] || option.emoji || '🎵'
}

export function VotingResults({
  result,
  votingType,
  title,
  onContinue,
  showContinueButton = true,
  autoAdvanceDelay,
  className = ""
}: VotingResultsProps) {
  const [showConfetti, setShowConfetti] = useState(false)
  const [animationPhase, setAnimationPhase] = useState<'winner' | 'breakdown' | 'complete'>('winner')

  useEffect(() => {
    // Trigger confetti animation for winner announcement
    setShowConfetti(true)
    const confettiTimer = setTimeout(() => {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      })
    }, 500)

    // Phase transitions
    const phaseTimer = setTimeout(() => {
      setAnimationPhase('breakdown')
    }, 2000)

    const completeTimer = setTimeout(() => {
      setAnimationPhase('complete')
    }, 4000)

    // Auto advance if specified
    let autoAdvanceTimer: NodeJS.Timeout
    if (autoAdvanceDelay) {
      autoAdvanceTimer = setTimeout(() => {
        onContinue()
      }, autoAdvanceDelay)
    }

    return () => {
      clearTimeout(confettiTimer)
      clearTimeout(phaseTimer)
      clearTimeout(completeTimer)
      if (autoAdvanceTimer) clearTimeout(autoAdvanceTimer)
    }
  }, [autoAdvanceDelay, onContinue])

  const winnerOption = result.winnerOption
  const winnerVotes = result.voteCounts[result.winnerIndex]
  const winnerPercentage = result.totalVotes > 0 ? Math.round((winnerVotes / result.totalVotes) * 100) : 0

  const sortedOptions = result.voteCounts
    .map((count, index) => ({ index, count, option: winnerOption, percentage: result.totalVotes > 0 ? Math.round((count / result.totalVotes) * 100) : 0 }))
    .sort((a, b) => b.count - a.count)

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`space-y-6 ${className}`}
    >
      {/* Winner Announcement */}
      <AnimatePresence>
        {animationPhase === 'winner' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="text-center space-y-4"
          >
            <motion.div
              animate={{ rotate: showConfetti ? [0, 10, -10, 0] : 0 }}
              transition={{ duration: 0.5, repeat: 2 }}
              className="text-6xl"
            >
              {getOptionEmoji(winnerOption)}
            </motion.div>
            
            <motion.h1
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3 }}
              className="text-3xl font-bold text-green-700"
            >
              {winnerOption.label} Wins!
            </motion.h1>
            
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="flex items-center justify-center gap-2 text-green-600"
            >
              <Trophy className="h-5 w-5" />
              <span className="text-lg font-medium">
                {winnerVotes} vote{winnerVotes !== 1 ? 's' : ''} ({winnerPercentage}%)
              </span>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Detailed Results */}
      <AnimatePresence>
        {(animationPhase === 'breakdown' || animationPhase === 'complete') && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <Card className="border-green-200 bg-green-50">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-green-700">
                  {getVotingTypeIcon(votingType)}
                  {title} - Results
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Winner Highlight */}
                <motion.div
                  initial={{ scale: 0.95 }}
                  animate={{ scale: 1 }}
                  className="p-4 bg-green-100 border-2 border-green-300 rounded-lg"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Crown className="h-6 w-6 text-yellow-500" />
                      <span className="text-2xl">{getOptionEmoji(winnerOption)}</span>
                      <div>
                        <div className="font-bold text-green-800 text-lg">
                          {winnerOption.label}
                        </div>
                        {winnerOption.description && (
                          <div className="text-sm text-green-600">
                            {winnerOption.description}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-700">
                        {winnerVotes}
                      </div>
                      <div className="text-sm text-green-600">
                        {winnerPercentage}% of votes
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* All Results Breakdown */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                    <BarChart3 className="h-4 w-4" />
                    <span>Complete Results</span>
                  </div>
                  
                  {result.voteCounts.map((count, index) => {
                    const percentage = result.totalVotes > 0 ? Math.round((count / result.totalVotes) * 100) : 0
                    const isWinner = index === result.winnerIndex
                    const option = winnerOption // Note: In real implementation, you'd get the actual option for this index
                    
                    return (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 * index }}
                        className={`p-3 rounded-lg border ${
                          isWinner 
                            ? 'border-green-400 bg-green-50' 
                            : 'border-gray-200 bg-white'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {isWinner && <Star className="h-4 w-4 text-yellow-500" />}
                            <span className="text-lg">{getOptionEmoji(option)}</span>
                            <span className={`font-medium ${isWinner ? 'text-green-700' : 'text-gray-700'}`}>
                              Option {index + 1}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{count} votes</span>
                            <Badge variant={isWinner ? "default" : "outline"} className="text-xs">
                              {percentage}%
                            </Badge>
                          </div>
                        </div>
                        
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: "100%" }}
                          transition={{ delay: 0.5 + (0.1 * index), duration: 0.8 }}
                        >
                          <Progress 
                            value={percentage} 
                            className={`h-2 ${isWinner ? 'bg-green-200' : ''}`}
                          />
                        </motion.div>
                      </motion.div>
                    )
                  })}
                </div>

                {/* Vote Summary */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-blue-500" />
                      <span className="font-medium text-blue-700">Vote Summary</span>
                    </div>
                    <div className="text-blue-600">
                      {result.totalVotes} total votes cast
                    </div>
                  </div>
                  
                  {result.isTie && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 1 }}
                      className="mt-2 flex items-center gap-2 text-orange-600"
                    >
                      <AlertCircle className="h-4 w-4" />
                      <span className="text-xs">
                        There was a tie! Winner selected by first option rule.
                      </span>
                    </motion.div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Action Button */}
      <AnimatePresence>
        {animationPhase === 'complete' && showContinueButton && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <Button
              onClick={onContinue}
              size="lg"
              className="bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:scale-105 transition-transform"
            >
              <CheckCircle2 className="h-5 w-5 mr-2" />
              Continue Game
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Celebration Elements */}
      <AnimatePresence>
        {showConfetti && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 pointer-events-none z-50"
          >
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ y: -100, opacity: 0, rotate: 0 }}
                animate={{ 
                  y: window.innerHeight + 100, 
                  opacity: [0, 1, 1, 0],
                  rotate: 360
                }}
                transition={{ 
                  duration: 3,
                  delay: i * 0.2,
                  ease: "easeOut"
                }}
                className="absolute text-4xl"
                style={{
                  left: `${10 + (i * 15)}%`,
                  animationDelay: `${i * 200}ms`
                }}
              >
                {['🎉', '🎊', '⭐', '🏆', '🎵', '🎁'][i]}
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Auto-advance indicator */}
      {autoAdvanceDelay && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center text-xs text-gray-500"
        >
          Continuing automatically in {Math.ceil(autoAdvanceDelay / 1000)} seconds...
        </motion.div>
      )}
    </motion.div>
  )
}