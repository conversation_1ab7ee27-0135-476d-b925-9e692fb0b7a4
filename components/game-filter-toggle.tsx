'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Filter, Settings } from 'lucide-react'
import { ContentFilterService } from '@/lib/services/content-filter-service'
import { ContentFilterSettings } from './content-filter-settings'
import type { GameFilterSettings } from '@/lib/types/filters'

interface GameFilterToggleProps {
  gameMode?: string
  onFilterChange?: (enabled: boolean) => void
  className?: string
}

export function GameFilterToggle({ 
  gameMode, 
  onFilterChange,
  className 
}: GameFilterToggleProps) {
  const filterService = ContentFilterService.getInstance()
  const [settings, setSettings] = useState<GameFilterSettings>(filterService.loadSettings())
  const [filterStats, setFilterStats] = useState<{ filtered: number; total: number } | null>(null)
  const [popoverOpen, setPopoverOpen] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  
  useEffect(() => {
    loadFilterStats()
  }, [settings.enabled, settings.activePresetId, settings.customFilters, refreshTrigger])
  
  // Refresh settings when popover opens
  useEffect(() => {
    if (popoverOpen) {
      const currentSettings = filterService.loadSettings()
      setSettings(currentSettings)
      // Also refresh stats when opening popover
      if (currentSettings.enabled) {
        setRefreshTrigger(prev => prev + 1)
      }
    }
  }, [popoverOpen, filterService])
  
  const loadFilterStats = async () => {
    if (!settings.enabled) {
      setFilterStats(null)
      return
    }
    
    try {
      // Get the current filters from the service
      const currentFilters = filterService.getCurrentFilters()
      
      const response = await fetch('/api/quiz/tracks/filtered', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          gameMode,
          limit: 1,
          page: 1,
          filters: currentFilters // Send the actual filters!
        })
      })
      
      const data = await response.json()
      if (data.success && data.filterStats) {
        setFilterStats({
          filtered: data.filterStats.filteredTracks,
          total: data.filterStats.totalTracks
        })
      }
    } catch (error) {
      console.error('Failed to load filter stats:', error)
    }
  }
  
  const handleToggle = (enabled: boolean) => {
    filterService.setFilteringEnabled(enabled)
    setSettings({ ...settings, enabled })
    onFilterChange?.(enabled)
    
    // Reload stats when toggled
    if (enabled) {
      loadFilterStats()
    } else {
      setFilterStats(null)
    }
  }
  
  const getActivePresetName = () => {
    if (settings.customFilters) return 'Custom'
    if (settings.activePresetId) {
      const preset = filterService.getPreset(settings.activePresetId)
      return preset?.name || 'Unknown'
    }
    return 'All Content'
  }
  
  // Get a summary of active filters for display
  const getFilterSummary = () => {
    const filters = filterService.getCurrentFilters()
    const summary = []
    
    if (filters.genres.values.length > 0) {
      summary.push(`${filters.genres.values.length} genres`)
    }
    if (filters.playlists.values.length > 0) {
      summary.push(`${filters.playlists.values.length} categories`)
    }
    if (filters.yearRange.enabled) {
      summary.push(`${filters.yearRange.min || '?'}-${filters.yearRange.max || '?'}`)
    }
    if (filters.folders?.values.length > 0) {
      summary.push(`${filters.folders.values.length} folders`)
    }
    if (!filters.sources.includeMyItunes) {
      summary.push('No MyItunes')
    }
    
    return summary.length > 0 ? summary.join(', ') : null
  }
  
  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div className="flex items-center gap-2">
        <Filter className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm font-medium">Content Filter</span>
      </div>
      
      <Switch
        checked={settings.enabled}
        onCheckedChange={handleToggle}
      />
      
      {settings.enabled && (
        <>
          <Badge variant="secondary" className="text-xs">
            {getActivePresetName()}
          </Badge>
          
          {getFilterSummary() && (
            <Badge variant="outline" className="text-xs">
              {getFilterSummary()}
            </Badge>
          )}
          
          {filterStats && (
            <Badge variant="outline" className="text-xs">
              {filterStats.filtered}/{filterStats.total} songs
            </Badge>
          )}
          
          <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <Settings className="h-3 w-3" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <ContentFilterSettings 
                key={popoverOpen ? 'open' : 'closed'}
                compact={true}
                gameMode={gameMode}
                onFiltersChange={() => {
                  // Refresh settings to get updated filter summary
                  const currentSettings = filterService.loadSettings()
                  setSettings(currentSettings)
                  // Trigger stats refresh
                  setRefreshTrigger(prev => prev + 1)
                }}
              />
            </PopoverContent>
          </Popover>
        </>
      )}
    </div>
  )
}