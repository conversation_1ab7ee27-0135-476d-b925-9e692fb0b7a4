'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from '@/hooks/use-toast'
import { Trash2, Edit2, Play, Plus, Music, Users, GripVertical, Search, X } from 'lucide-react'

interface Track {
  position: number
  file: string
  title: string
  artist: string
  album: string
  duration: number
  genre?: string
  year?: number | string
  id?: string
}

interface Playlist {
  name: string
  trackCount: number
  tracks: { title: string; artist: string }[]
}

interface PlaylistEditorProps {
  userRole?: 'admin' | 'dj' | 'user'
  userId?: string
}

export function PlaylistEditor({ userRole = 'user', userId }: PlaylistEditorProps) {
  const [playlists, setPlaylists] = useState<Playlist[]>([])
  const [selectedPlaylist, setSelectedPlaylist] = useState<string | null>(null)
  const [playlistTracks, setPlaylistTracks] = useState<Track[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchResults, setSearchResults] = useState<Track[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [newPlaylistName, setNewPlaylistName] = useState('')
  const [editingPlaylistName, setEditingPlaylistName] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showRenameDialog, setShowRenameDialog] = useState(false)
  const [showAddTracksDialog, setShowAddTracksDialog] = useState(false)
  const [draggedTrack, setDraggedTrack] = useState<number | null>(null)

  // Check if user has playlist management permissions
  const canManagePlaylists = userRole === 'admin' || userRole === 'dj'

  useEffect(() => {
    loadPlaylists()
  }, [])

  const loadPlaylists = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/manager/playlists')
      const data = await response.json()
      
      if (data.success) {
        setPlaylists(data.playlists)
      } else {
        toast({
          title: 'Error loading playlists',
          description: data.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Failed to load playlists:', error)
      toast({
        title: 'Error',
        description: 'Failed to load playlists',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const loadPlaylistTracks = async (playlistName: string) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/manager/playlists/${encodeURIComponent(playlistName)}`)
      const data = await response.json()
      
      if (data.success) {
        setPlaylistTracks(data.playlist.tracks)
        setSelectedPlaylist(playlistName)
      } else {
        toast({
          title: 'Error loading playlist',
          description: data.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Failed to load playlist tracks:', error)
      toast({
        title: 'Error',
        description: 'Failed to load playlist tracks',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const createPlaylist = async () => {
    if (!newPlaylistName.trim()) return

    try {
      const response = await fetch('/api/manager/playlists', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          playlistName: newPlaylistName.trim()
        })
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: 'Playlist created',
          description: `"${newPlaylistName}" has been created`
        })
        setNewPlaylistName('')
        setShowCreateDialog(false)
        loadPlaylists()
      } else {
        toast({
          title: 'Error creating playlist',
          description: data.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Failed to create playlist:', error)
      toast({
        title: 'Error',
        description: 'Failed to create playlist',
        variant: 'destructive'
      })
    }
  }

  const deletePlaylist = async (playlistName: string) => {
    if (!confirm(`Are you sure you want to delete "${playlistName}"?`)) return

    try {
      const response = await fetch('/api/manager/playlists', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'delete',
          playlistName
        })
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: 'Playlist deleted',
          description: `"${playlistName}" has been deleted`
        })
        if (selectedPlaylist === playlistName) {
          setSelectedPlaylist(null)
          setPlaylistTracks([])
        }
        loadPlaylists()
      } else {
        toast({
          title: 'Error deleting playlist',
          description: data.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Failed to delete playlist:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete playlist',
        variant: 'destructive'
      })
    }
  }

  const renamePlaylist = async () => {
    if (!editingPlaylistName.trim() || !selectedPlaylist) return

    try {
      const response = await fetch('/api/manager/playlists', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'rename',
          playlistName: selectedPlaylist,
          newName: editingPlaylistName.trim()
        })
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: 'Playlist renamed',
          description: `Renamed to "${editingPlaylistName}"`
        })
        setEditingPlaylistName('')
        setShowRenameDialog(false)
        setSelectedPlaylist(editingPlaylistName.trim())
        loadPlaylists()
      } else {
        toast({
          title: 'Error renaming playlist',
          description: data.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Failed to rename playlist:', error)
      toast({
        title: 'Error',
        description: 'Failed to rename playlist',
        variant: 'destructive'
      })
    }
  }

  const loadToQueue = async (playlistName: string, append = false) => {
    try {
      const response = await fetch('/api/manager/playlists', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: append ? 'append-to-queue' : 'load-to-queue',
          playlistName
        })
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: append ? 'Playlist appended' : 'Playlist loaded',
          description: `"${playlistName}" ${append ? 'added to' : 'loaded to'} queue`
        })
      } else {
        toast({
          title: 'Error loading playlist',
          description: data.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Failed to load playlist to queue:', error)
      toast({
        title: 'Error',
        description: 'Failed to load playlist to queue',
        variant: 'destructive'
      })
    }
  }

  const searchTracks = async () => {
    if (!searchQuery.trim()) return

    try {
      // Get auth token
      const { ClientTokenManager } = await import('@/lib/client-auth')
      const token = ClientTokenManager.getToken()
      
      const headers: HeadersInit = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch(`/api/mpd/search?q=${encodeURIComponent(searchQuery)}&limit=50`, { headers })
      const data = await response.json()
      
      if (data.success) {
        setSearchResults(data.tracks || [])
        toast({
          title: 'Search completed',
          description: `Found ${data.count} tracks in your music library`
        })
      } else {
        toast({
          title: 'Search failed',
          description: data.message || 'Failed to search music library',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Failed to search tracks:', error)
      toast({
        title: 'Search error',
        description: 'Failed to search music library',
        variant: 'destructive'
      })
    }
  }

  const addTrackToPlaylist = async (track: Track) => {
    if (!selectedPlaylist) return

    try {
      const response = await fetch('/api/manager/playlists', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'add-track',
          playlistName: selectedPlaylist,
          filePath: track.file
        })
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: 'Track added',
          description: `"${track.title}" added to "${selectedPlaylist}"`
        })
        loadPlaylistTracks(selectedPlaylist)
      } else {
        toast({
          title: 'Error adding track',
          description: data.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Failed to add track:', error)
      toast({
        title: 'Error',
        description: 'Failed to add track',
        variant: 'destructive'
      })
    }
  }

  const removeTrackFromPlaylist = async (position: number) => {
    if (!selectedPlaylist) return

    try {
      const response = await fetch('/api/manager/playlists', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'remove-track',
          playlistName: selectedPlaylist,
          position
        })
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: 'Track removed',
          description: 'Track removed from playlist'
        })
        loadPlaylistTracks(selectedPlaylist)
      } else {
        toast({
          title: 'Error removing track',
          description: data.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Failed to remove track:', error)
      toast({
        title: 'Error',
        description: 'Failed to remove track',
        variant: 'destructive'
      })
    }
  }

  const reorderTracks = async (newOrder: Track[]) => {
    if (!selectedPlaylist) return

    try {
      const response = await fetch('/api/manager/playlists', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'reorder',
          playlistName: selectedPlaylist,
          tracks: newOrder
        })
      })

      const data = await response.json()
      if (data.success) {
        setPlaylistTracks(newOrder)
        toast({
          title: 'Playlist reordered',
          description: 'Track order updated'
        })
      } else {
        toast({
          title: 'Error reordering tracks',
          description: data.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Failed to reorder tracks:', error)
      toast({
        title: 'Error',
        description: 'Failed to reorder tracks',
        variant: 'destructive'
      })
    }
  }

  const handleDragStart = (index: number) => {
    setDraggedTrack(index)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    if (draggedTrack === null) return

    const newTracks = [...playlistTracks]
    const draggedItem = newTracks[draggedTrack]
    newTracks.splice(draggedTrack, 1)
    newTracks.splice(dropIndex, 0, draggedItem)

    // Update positions
    const reorderedTracks = newTracks.map((track, index) => ({
      ...track,
      position: index
    }))

    reorderTracks(reorderedTracks)
    setDraggedTrack(null)
  }

  if (!canManagePlaylists) {
    return (
      <Card className="m-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Music className="h-5 w-5" />
            Playlist Viewer
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            You can view playlists but need admin or DJ privileges to edit them.
          </p>
          
          <div className="grid gap-3">
            {playlists.map((playlist) => (
              <Card key={playlist.name}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">{playlist.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {playlist.trackCount} tracks
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        onClick={() => loadToQueue(playlist.name)}
                        className="gap-1"
                      >
                        <Play className="h-3 w-3" />
                        Load
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => loadToQueue(playlist.name, true)}
                        className="gap-1"
                      >
                        <Plus className="h-3 w-3" />
                        Add
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="p-4 max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Music className="h-6 w-6" />
            Playlist Manager
          </h1>
          <p className="text-muted-foreground">
            Create and manage playlists for your music collection
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="gap-1">
            <Users className="h-3 w-3" />
            {userRole?.toUpperCase()}
          </Badge>
          
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="gap-1">
                <Plus className="h-4 w-4" />
                New Playlist
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Playlist</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <Input
                  placeholder="Playlist name"
                  value={newPlaylistName}
                  onChange={(e) => setNewPlaylistName(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && createPlaylist()}
                />
                <div className="flex gap-2">
                  <Button onClick={createPlaylist} disabled={!newPlaylistName.trim()}>
                    Create
                  </Button>
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Playlist List */}
        <Card>
          <CardHeader>
            <CardTitle>Playlists ({playlists.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[600px]">
              <div className="space-y-2">
                {playlists.map((playlist) => (
                  <Card 
                    key={playlist.name}
                    className={`cursor-pointer transition-colors ${
                      selectedPlaylist === playlist.name ? 'ring-2 ring-primary' : ''
                    }`}
                    onClick={() => loadPlaylistTracks(playlist.name)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium truncate">{playlist.name}</h3>
                          <p className="text-xs text-muted-foreground">
                            {playlist.trackCount} tracks
                          </p>
                        </div>
                        
                        <div className="flex gap-1 ml-2">
                          <Button 
                            size="sm" 
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation()
                              loadToQueue(playlist.name)
                            }}
                            className="h-7 w-7 p-0"
                          >
                            <Play className="h-3 w-3" />
                          </Button>
                          <Button 
                            size="sm" 
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation()
                              deletePlaylist(playlist.name)
                            }}
                            className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Playlist Editor */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>
                {selectedPlaylist ? `Editing: ${selectedPlaylist}` : 'Select a playlist to edit'}
              </CardTitle>
              
              {selectedPlaylist && (
                <div className="flex gap-2">
                  <Dialog open={showRenameDialog} onOpenChange={setShowRenameDialog}>
                    <DialogTrigger asChild>
                      <Button size="sm" variant="outline" className="gap-1">
                        <Edit2 className="h-3 w-3" />
                        Rename
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Rename Playlist</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <Input
                          placeholder="New playlist name"
                          value={editingPlaylistName}
                          onChange={(e) => setEditingPlaylistName(e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && renamePlaylist()}
                        />
                        <div className="flex gap-2">
                          <Button onClick={renamePlaylist} disabled={!editingPlaylistName.trim()}>
                            Rename
                          </Button>
                          <Button variant="outline" onClick={() => setShowRenameDialog(false)}>
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                  
                  <Dialog open={showAddTracksDialog} onOpenChange={setShowAddTracksDialog}>
                    <DialogTrigger asChild>
                      <Button size="sm" className="gap-1">
                        <Plus className="h-3 w-3" />
                        Add Tracks
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Add Tracks to {selectedPlaylist}</DialogTitle>
                      </DialogHeader>
                      
                      <div className="space-y-4">
                        <div className="flex gap-2">
                          <Input
                            placeholder="Search for tracks..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            onKeyDown={(e) => e.key === 'Enter' && searchTracks()}
                          />
                          <Button onClick={searchTracks}>
                            <Search className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <ScrollArea className="h-[400px]">
                          <div className="space-y-2">
                            {searchResults.map((track) => (
                              <div key={track.id} className="flex items-center justify-between p-3 border rounded hover:bg-muted/50">
                                <div className="flex-1 min-w-0">
                                  <p className="font-medium truncate">{track.title}</p>
                                  <p className="text-sm text-muted-foreground truncate">{track.artist}</p>
                                  <div className="flex items-center gap-2 mt-1">
                                    <p className="text-xs text-muted-foreground truncate">{track.album}</p>
                                    {track.genre && track.genre !== 'Unknown Genre' && (
                                      <Badge variant="secondary" className="text-xs px-1 py-0 h-4">
                                        {track.genre}
                                      </Badge>
                                    )}
                                    {track.year && (
                                      <span className="text-xs text-muted-foreground">
                                        {track.year}
                                      </span>
                                    )}
                                    {track.duration > 0 && (
                                      <span className="text-xs text-muted-foreground">
                                        {Math.floor(track.duration / 60)}:{(track.duration % 60).toString().padStart(2, '0')}
                                      </span>
                                    )}
                                  </div>
                                </div>
                                <Button 
                                  size="sm" 
                                  onClick={() => addTrackToPlaylist(track)}
                                  className="gap-1"
                                >
                                  <Plus className="h-3 w-3" />
                                  Add
                                </Button>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              )}
            </div>
          </CardHeader>
          
          <CardContent>
            {selectedPlaylist ? (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <p className="text-sm text-muted-foreground">
                    {playlistTracks.length} tracks
                  </p>
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      onClick={() => loadToQueue(selectedPlaylist)}
                      className="gap-1"
                    >
                      <Play className="h-3 w-3" />
                      Load to Queue
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => loadToQueue(selectedPlaylist, true)}
                      className="gap-1"
                    >
                      <Plus className="h-3 w-3" />
                      Add to Queue
                    </Button>
                  </div>
                </div>
                
                <ScrollArea className="h-[500px]">
                  <div className="space-y-2">
                    {playlistTracks.map((track, index) => (
                      <div 
                        key={track.id || index}
                        className="flex items-center gap-2 p-2 border rounded cursor-move"
                        draggable
                        onDragStart={() => handleDragStart(index)}
                        onDragOver={handleDragOver}
                        onDrop={(e) => handleDrop(e, index)}
                      >
                        <GripVertical className="h-4 w-4 text-muted-foreground" />
                        <div className="flex-1 min-w-0">
                          <p className="font-medium truncate">{track.title}</p>
                          <p className="text-sm text-muted-foreground truncate">
                            {track.artist} • {track.album}
                          </p>
                        </div>
                        <Button 
                          size="sm" 
                          variant="ghost"
                          onClick={() => removeTrackFromPlaylist(track.position)}
                          className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            ) : (
              <div className="text-center py-12">
                <Music className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Playlist Selected</h3>
                <p className="text-muted-foreground">
                  Select a playlist from the left to start editing
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 