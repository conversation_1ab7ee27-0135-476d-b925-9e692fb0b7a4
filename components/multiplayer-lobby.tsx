"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react"
import { useMultiplayer } from "@/hooks/use-multiplayer"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Users, Copy, Check, Play, Loader2, Filter, Settings, Music, Brain } from "lucide-react"
import { toast } from 'sonner'
import { motion } from "framer-motion"
import { GameFilterToggle } from "./game-filter-toggle"
import { ContentFilterService } from "@/lib/services/content-filter-service"
import type { ContentFilters } from "@/lib/types/filters"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import type { UlTimoteGameConfig } from "./ultimote-game-config"
// Debug components - only loaded in development
const UltimoteConfigDebugger = process.env.NODE_ENV === 'development' 
  ? require("./ultimote-config-debugger").UltimoteConfigDebugger 
  : null
const GameModeDebugger = process.env.NODE_ENV === 'development' 
  ? require("./game-mode-debugger").GameModeDebugger 
  : null
const WebSocketDebugger = process.env.NODE_ENV === 'development' 
  ? require("./websocket-debugger").WebSocketDebugger 
  : null
import { WebSocketMonitor } from "./websocket-monitor"
import { useUser } from "@/lib/user-context"

// Conditional debug logging for production cleanup
const DEBUG = process.env.NODE_ENV === 'development'
const debugLog = DEBUG ? console.log : () => {}
const warnLog = console.warn // Keep warnings in production

export type GameMode = "classic" | "chart_position" | "guess_the_year" | "genre_specialist" | "decade_challenge" | "audio_manipulation" | "ultimote"

interface MultiplayerLobbyProps {
  role: "host" | "player"
  onStartGame: (mode: GameMode) => void
  onBackToMenu: () => void
}

// Icon mapping for general knowledge categories
const iconMap: Record<string, string> = {
  // Working icons
  'brain': '🧠',        // allgemeinwissen
  'target': '🎯',       // sport
  
  // Common Lucide icon names
  'help-circle': '❓',
  'globe': '🌍',
  'film': '🎬',
  'tv': '📺',
  'music': '🎵',
  'book': '📚',
  'gamepad': '🎮',
  'gamepad-2': '🎮',
  'football': '⚽',
  'cpu': '💻',
  'monitor': '💻',
  'smartphone': '📱',
  'science': '🔬',
  'flask': '🔬',
  'heart': '❤️',
  'star': '⭐',
  'award': '🏆',
  'trophy': '🏆',
  'camera': '📷',
  'palette': '🎨',
  'zap': '⚡',
  'lightbulb': '💡',
  'users': '👥',
  'clock': '⏰',
  'calendar': '📅',
  'map': '🗺️',
  'plane': '✈️',
  'car': '🚗',
  'home': '🏠',
  'building': '🏢',
  'tree': '🌳',
  'flower': '🌸',
  'sun': '☀️',
  'moon': '🌙',
  'cloud': '☁️',
  'umbrella': '☂️',
  'default': '📌'
}

export function MultiplayerLobby({ role, onStartGame, onBackToMenu }: MultiplayerLobbyProps) {
  const { user } = useUser()
  const [copied, setCopied] = useState(false)
  const [playerName, setPlayerName] = useState("")
  const [joinGameCode, setJoinGameCode] = useState("")
  const [selectedGameMode, setSelectedGameMode] = useState<GameMode>("ultimote")
  const [isCreating, setIsCreating] = useState(false)
  const [isJoining, setIsJoining] = useState(false)
  const [hasJoined, setHasJoined] = useState(false)
  const [isStarting, setIsStarting] = useState(false)
  const [contentFilters, setContentFilters] = useState<ContentFilters>({
    genres: { mode: 'include', values: [] },
    playlists: { mode: 'include', values: [] },
    yearRange: { enabled: false },
    charts: { includeChartMusic: true, includeNonChartMusic: true },
    quality: { minPopularity: 0 },
    sources: { includeMyItunes: true, includeSharedLibrary: true, includePlaylists: [] },
    metadata: {},
    folders: { mode: 'include', values: [] }
  })
  const [questionsPerGame, setQuestionsPerGame] = useState(10)
  const [timePerQuestion, setTimePerQuestion] = useState(30)
  const [availableGeneralCategories, setAvailableGeneralCategories] = useState<Array<{
    slug: string
    name: string
    icon: string
    questionCount: number
  }>>([])
  const [ultimoteConfig, setUltimoteConfig] = useState<Partial<UlTimoteGameConfig>>({
    questionsPerRound: 5,
    categories: {
      classic: { enabled: false, weight: 20, difficulty: 3, rounds: 0 },
      quickFire: { enabled: false, weight: 10, rounds: 0 },
      audioTricks: { enabled: false, weight: 5, effectLevel: 3, rounds: 0 },
      chartPosition: { enabled: false, weight: 5, eraFocus: 'all', rounds: 0 },
      decadeChallenge: { enabled: false, weight: 0, decades: ['1980s', '1990s'], rounds: 0 },
      genreSpecialist: { enabled: false, weight: 0, genres: ['Rock', 'Pop'], rounds: 0 },
      generalKnowledge: { enabled: true, weight: 45, categories: ['general', 'science', 'history', 'geography'], difficulty: 3, rounds: 1 }
    }
  })
  
  // Use ref to always have access to the latest config
  const ultimoteConfigRef = useRef(ultimoteConfig)
  ultimoteConfigRef.current = ultimoteConfig
  
  const filterService = ContentFilterService.getInstance()
  
  const { 
    isConnected, 
    gameState, 
    isHost,
    createGame, 
    joinGame, 
    startGame,
    leaveGame 
  } = useMultiplayer()

  // Load saved player name and check for joining game code
  useEffect(() => {
    // Clear any stale game data when starting as host
    if (role === "host") {
      localStorage.removeItem('currentGameId')
      localStorage.removeItem('playerId')
      debugLog('[MultiplayerLobby] Cleared stored game data for fresh host session')
    }
    
    // Use logged in user's name if available
    if (user) {
      const userName = user.displayName || user.username || user.name
      if (userName) {
        setPlayerName(userName)
      } else {
        // User exists but has no name - shouldn't happen but handle it
        console.warn('[MultiplayerLobby] User exists but has no name:', user)
        const savedName = localStorage.getItem('playerName') || ''
        if (savedName) setPlayerName(savedName)
      }
    } else {
      const savedName = localStorage.getItem('playerName') || ''
      if (savedName) setPlayerName(savedName)
    }
    
    // Check if we're joining from game browser
    const joiningCode = localStorage.getItem('joiningGameCode')
    if (joiningCode && role === "player") {
      setJoinGameCode(joiningCode)
      localStorage.removeItem('joiningGameCode') // Clean up
    }
    
    // Load filter settings
    const settings = filterService.loadSettings()
    if (settings.enabled) {
      setContentFilters(filterService.getCurrentFilters())
    }
  }, [role, user])

  // Fetch available general knowledge categories
  useEffect(() => {
    const fetchGeneralCategories = async () => {
      try {
        const response = await fetch('/api/general-quiz/categories')
        if (response.ok) {
          const categories = await response.json()
          debugLog('[MultiplayerLobby] General categories loaded:', categories)
          setAvailableGeneralCategories(categories)
          
          // Don't auto-clear categories - let user control them
          const availableSlugs = categories.map((c: any) => c.slug)
          debugLog('[MultiplayerLobby] Available categories loaded, user can now select from:', availableSlugs)
        }
      } catch (error) {
        console.error('Failed to fetch general quiz categories:', error)
      }
    }
    fetchGeneralCategories()
  }, [])

  // Auto-create game for host - remove the hasJoined check to allow retry
  useEffect(() => {
    console.log('[MultiplayerLobby] Auto-create check:', {
      role,
      isConnected,
      gameState: !!gameState,
      playerName: !!playerName,
      isCreating,
      shouldCreate: role === "host" && isConnected && !gameState && playerName && !isCreating
    })
    if (role === "host" && isConnected && !gameState && playerName && !isCreating) {
      debugLog('[MultiplayerLobby] Auto-creating game...')
      handleCreateGame()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [role, isConnected, gameState, playerName, isCreating])

  // Auto-join if we have both name and code
  useEffect(() => {
    if (role === "player" && isConnected && !gameState && playerName && joinGameCode && !isJoining && !hasJoined) {
      handleJoinGame()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [role, isConnected, gameState, playerName, joinGameCode, isJoining, hasJoined])

  // Handle game started
  useEffect(() => {
    debugLog('[MultiplayerLobby] Game status:', gameState?.status)
    if (gameState?.status === 'playing') {
      debugLog('[MultiplayerLobby] Calling onStartGame with mode:', selectedGameMode)
      onStartGame(selectedGameMode)
    }
  }, [gameState?.status, selectedGameMode, onStartGame])

  const handleCreateGame = useCallback(async () => {
    if (!playerName.trim()) {
      toast.error('Please enter your name')
      return
    }

    // Validate ultimote configuration using ref for latest state
    const currentConfig = ultimoteConfigRef.current
    if (selectedGameMode === 'ultimote' && currentConfig) {
      const hasEnabledCategories = Object.entries(currentConfig.categories || {})
        .some(([key, cat]) => cat.enabled && cat.rounds > 0)
      
      if (!hasEnabledCategories) {
        toast.error('Please enable at least one category for ulTimote mode')
        return
      }
      
      // Check if general knowledge is enabled but has no categories
      const gk = currentConfig.categories?.generalKnowledge
      if (gk?.enabled && gk.rounds > 0 && (!gk.categories || gk.categories.length === 0)) {
        toast.error('Please select at least one general knowledge category')
        return
      }
    }

    setIsCreating(true)
    localStorage.setItem('playerName', playerName)
    
    // Debug log current state values
    console.log('[MultiplayerLobby] Current state before creating game:', {
      selectedGameMode,
      timePerQuestion,
      questionsPerGame,
      ultimoteConfig: JSON.stringify(ultimoteConfig, null, 2)
    })
    
    // Calculate total questions for UlTimote based on rounds
    let ultimoteTotalQuestions = 5 // Default should match questionsPerRound
    if (selectedGameMode === 'ultimote' && ultimoteConfig) {
      const enabledCategories = Object.entries(ultimoteConfig.categories || {})
        .filter(([key, cat]) => cat.enabled && cat.rounds > 0)
        .map(([key, cat]) => ({ name: key, rounds: cat.rounds, enabled: cat.enabled }))
      
      debugLog('[MultiplayerLobby] All categories:', ultimoteConfig.categories)
      debugLog('[MultiplayerLobby] Enabled categories with rounds > 0:', enabledCategories)
      
      const totalRounds = enabledCategories
        .reduce((sum, cat) => sum + cat.rounds, 0)
      ultimoteTotalQuestions = totalRounds * (ultimoteConfig.questionsPerRound || 5)
      
      console.log('[MultiplayerLobby] Calculation:', {
        totalRounds,
        questionsPerRound: ultimoteConfig.questionsPerRound || 5,
        ultimoteTotalQuestions,
        generalKnowledgeEnabled: ultimoteConfig.categories?.generalKnowledge?.enabled,
        generalKnowledgeRounds: ultimoteConfig.categories?.generalKnowledge?.rounds
      })
    }

    // Debug: log current ultimote config state
    debugLog('[MultiplayerLobby] Current ultimoteConfig state:', JSON.stringify(currentConfig, null, 2))
    debugLog('[MultiplayerLobby] General Knowledge categories in state:', currentConfig.categories?.generalKnowledge?.categories)
    
    debugLog('[MultiplayerLobby] Before gameSettings - ultimoteTotalQuestions:', ultimoteTotalQuestions)
    
    const gameSettings = selectedGameMode === 'ultimote' 
      ? {
          totalQuestions: ultimoteTotalQuestions,
          timePerQuestion: timePerQuestion,
          filters: contentFilters,
          ultimoteConfig: currentConfig
        }
      : {
          totalQuestions: questionsPerGame,
          timePerQuestion: timePerQuestion,
          filters: contentFilters
        }
    
    console.log('[MultiplayerLobby] Creating game with settings:', {
      gameMode: selectedGameMode,
      totalQuestions: gameSettings.totalQuestions,
      timePerQuestion: gameSettings.timePerQuestion,
      timePerQuestionValue: timePerQuestion,
      ultimoteConfig: gameSettings.ultimoteConfig,
      isUltimote: selectedGameMode === 'ultimote',
      hasGeneralKnowledge: gameSettings.ultimoteConfig?.categories?.generalKnowledge?.enabled
    })
    
    // Log the exact category configuration
    if (selectedGameMode === 'ultimote' && gameSettings.ultimoteConfig) {
      debugLog('[MultiplayerLobby] Full UlTimote config:', JSON.stringify(gameSettings.ultimoteConfig, null, 2))
      debugLog('[MultiplayerLobby] General Knowledge categories being sent:', gameSettings.ultimoteConfig?.categories?.generalKnowledge?.categories)
    }
    
    try {
      debugLog('[MultiplayerLobby] Creating game with settings:', JSON.stringify(gameSettings, null, 2))
    const result = await createGame(playerName, selectedGameMode, gameSettings)
      
      if (result.success) {
        toast.success(`Game created! Code: ${result.gamePin}`)
        // Game state will update automatically via socket events
      } else {
        toast.error(result.error || 'Failed to create game')
        // Add retry suggestion
        if (result.error?.includes('timeout')) {
          toast.error('Tip: Check if the socket server is running on port 3001', {
            duration: 5000
          })
        }
      }
    } catch (error) {
      console.error('[MultiplayerLobby] Create game error:', error)
      toast.error('Failed to create game. Please check your connection.')
    } finally {
      setIsCreating(false)
    }
  }, [playerName, selectedGameMode, timePerQuestion, contentFilters, questionsPerGame, createGame])

  const handleJoinGame = useCallback(async () => {
    if (!playerName.trim()) {
      toast.error('Please enter your name')
      return
    }
    
    if (!joinGameCode.trim()) {
      toast.error('Please enter a game code')
      return
    }

    setIsJoining(true)
    localStorage.setItem('playerName', playerName)
    
    const result = await joinGame(joinGameCode.toUpperCase(), playerName)
    
    if (result.success) {
      toast.success('Joined game successfully!')
      setHasJoined(true)
    } else {
      toast.error(result.error || 'Failed to join game')
      // If game not found, clear the join code and prevent further attempts
      if (result.error === 'Game not found') {
        setJoinGameCode('')
        setHasJoined(true) // Prevent infinite retry loop
      }
    }
    
    setIsJoining(false)
  }, [playerName, joinGameCode, joinGame])

  const handleStartGame = async () => {
    try {
      setIsStarting(true)
      const result = await startGame()
      
      if (!result?.success) {
        toast.error(result?.error || 'Failed to start game. Please try again.')
        setIsStarting(false)
      }
    } catch (error) {
      console.error('[MultiplayerLobby] Start game error:', error)
      toast.error('An error occurred while starting the game. Please try again.')
      setIsStarting(false)
    }
  }

  const copyGameCode = () => {
    if (gameState?.pin) {
      navigator.clipboard.writeText(gameState.pin)
      setCopied(true)
      toast.success('Game code copied!')
      setTimeout(() => setCopied(false), 2000)
    }
  }

  const handleLeaveGame = () => {
    leaveGame()
    onBackToMenu()
  }

  // Show connection status
  if (!isConnected) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardContent className="py-12">
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
            <p className="text-lg">Connecting to game server...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Host view - waiting for players
  if (role === "host" && gameState) {
    return (
      <Card className="max-w-4xl mx-auto backdrop-blur-lg bg-white/80 dark:bg-gray-900/80 border-white/20 shadow-2xl">
        <CardHeader>
          <CardTitle className="text-2xl sm:text-3xl text-center bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent font-bold">
            Waiting for Players
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Game Code Display */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-center text-white">
            <p className="text-sm mb-2">Game Code</p>
            <div className="flex items-center justify-center gap-3">
              <span className="text-4xl font-bold tracking-wider">{gameState.pin}</span>
              <Button
                variant="secondary"
                size="sm"
                onClick={copyGameCode}
                className="bg-white/20 hover:bg-white/30 text-white"
              >
                {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {/* Players List */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Users className="h-5 w-5" />
              Players ({gameState.players.length})
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {gameState.players.map((player) => (
                <motion.div
                  key={player.id}
                  initial={{ opacity: 0, scale: 0.9, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg p-4 flex items-center gap-3 shadow-md hover:shadow-lg transition-shadow"
                >
                  <div className="flex-1">
                    <p className="font-medium">{player.name}</p>
                    {player.isHost && (
                      <Badge variant="secondary" className="text-xs">Host</Badge>
                    )}
                  </div>
                  {player.connected ? (
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                  ) : (
                    <div className="w-2 h-2 bg-gray-400 rounded-full" />
                  )}
                </motion.div>
              ))}
            </div>
          </div>

          {/* Game Settings */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="game-mode" className="mb-2 block">Game Mode</Label>
              <select
                id="game-mode"
                value={selectedGameMode}
                onChange={(e) => setSelectedGameMode(e.target.value as GameMode)}
                className="w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-800"
              >
                <option value="ultimote">ulTimote - Ultimate customizable quiz experience</option>
                <option value="classic">Classic - Who is the artist?</option>
                <option value="chart_position">Chart Position - Guess how high it charted</option>
                <option value="guess_the_year">Guess the Year - When was it released?</option>
                <option value="genre_specialist">Genre Specialist - Name the genre</option>
                <option value="decade_challenge">Decade Challenge - Which decade?</option>
                <option value="audio_manipulation">Audio Effects - With rewind/speed changes</option>
              </select>
            </div>
            
            {/* Standard Game Configuration */}
            {selectedGameMode !== "ultimote" && isHost && (
              <div className="border-t pt-4 space-y-4">
                <h4 className="font-semibold flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Game Configuration
                </h4>
                
                {/* Questions per game */}
                <div>
                  <Label className="text-sm">Questions per Game: {questionsPerGame}</Label>
                  <Slider
                    value={[questionsPerGame]}
                    onValueChange={([value]) => setQuestionsPerGame(value)}
                    min={5}
                    max={50}
                    step={5}
                    className="mt-2"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>5</span>
                    <span>25</span>
                    <span>50</span>
                  </div>
                </div>
                
                {/* Time per question */}
                <div>
                  <Label className="text-sm">Time per Question: {timePerQuestion}s</Label>
                  <Slider
                    value={[timePerQuestion]}
                    onValueChange={([value]) => setTimePerQuestion(value)}
                    min={10}
                    max={60}
                    step={5}
                    className="mt-2"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>10s</span>
                    <span>30s</span>
                    <span>60s</span>
                  </div>
                </div>
                
                {/* Game Mode Debugger */}
                {process.env.NODE_ENV === 'development' && (
                  <GameModeDebugger 
                    gameMode={selectedGameMode}
                    questionsPerGame={questionsPerGame}
                    timePerQuestion={timePerQuestion}
                    className="mt-4"
                  />
                )}
              </div>
            )}
            
            {/* ulTimote Configuration */}
            {selectedGameMode === "ultimote" && isHost && (
              <div className="border-t pt-4 space-y-4">
                <h4 className="font-semibold flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  ulTimote Configuration
                </h4>
                
                {/* Questions per Round */}
                <div>
                  <Label className="text-sm">Questions per Round: {ultimoteConfig.questionsPerRound}</Label>
                  <Slider
                    value={[ultimoteConfig.questionsPerRound || 5]}
                    onValueChange={([value]) => setUltimoteConfig(prev => ({ ...prev, questionsPerRound: value }))}
                    min={3}
                    max={10}
                    step={1}
                    className="mt-2"
                  />
                </div>
                
                {/* Time per Question */}
                <div>
                  <Label className="text-sm">Time per Question: {timePerQuestion}s</Label>
                  <Slider
                    value={[timePerQuestion]}
                    onValueChange={([value]) => setTimePerQuestion(value)}
                    min={10}
                    max={60}
                    step={5}
                    className="mt-2"
                  />
                </div>
                
                {/* Music Quiz Categories */}
                <div className="space-y-3">
                  <h5 className="text-sm font-medium flex items-center gap-2">
                    <Music className="h-4 w-4 text-purple-400" />
                    Music Quiz Categories
                  </h5>
                  
                  {Object.entries(ultimoteConfig.categories || {})
                    .filter(([key]) => key !== 'generalKnowledge')
                    .map(([key, category]) => (
                      <div key={key} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Switch
                              checked={category.enabled}
                              onCheckedChange={(enabled) => {
                                debugLog(`[MultiplayerLobby] Toggling ${key} to ${enabled}`)
                                setUltimoteConfig(prev => ({
                                  ...prev,
                                  categories: {
                                    ...prev.categories,
                                    [key]: { ...category, enabled, rounds: enabled ? (category.rounds || 1) : 0 }
                                  }
                                }))
                              }}
                            />
                            <span className="text-sm font-medium capitalize">
                              {key.replace(/([A-Z])/g, ' $1').trim()}
                            </span>
                          </div>
                          {category.enabled && (
                            <Badge variant="outline" className="text-xs">
                              {category.rounds} rounds
                            </Badge>
                          )}
                        </div>
                        
                        {category.enabled && (
                          <div className="pl-8 space-y-2">
                            <div>
                              <Label className="text-xs">Rounds: {category.rounds}</Label>
                              <Slider
                                value={[category.rounds]}
                                onValueChange={([value]) => {
                                  debugLog(`[MultiplayerLobby] Setting ${key} rounds to ${value}`)
                                  setUltimoteConfig(prev => ({
                                    ...prev,
                                    categories: {
                                      ...prev.categories,
                                      [key]: { ...category, rounds: value, enabled: value > 0 }
                                    }
                                  }))
                                }}
                                min={0}
                                max={5}
                                step={1}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label className="text-xs">Difficulty: {category.difficulty || 3}</Label>
                              <Slider
                                value={[category.difficulty || 3]}
                                onValueChange={([value]) => {
                                  debugLog(`[MultiplayerLobby] Setting ${key} difficulty to ${value}`)
                                  setUltimoteConfig(prev => ({
                                    ...prev,
                                    categories: {
                                      ...prev.categories,
                                      [key]: { ...category, difficulty: value }
                                    }
                                  }))
                                }}
                                min={1}
                                max={5}
                                step={1}
                                className="mt-1"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                </div>
                
                {/* General Knowledge */}
                <div className="space-y-3">
                  <h5 className="text-sm font-medium flex items-center gap-2">
                    <Brain className="h-4 w-4 text-cyan-400" />
                    General Knowledge
                  </h5>
                  
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={ultimoteConfig.categories?.generalKnowledge?.enabled || false}
                          onCheckedChange={(enabled) => 
                            setUltimoteConfig(prev => ({
                              ...prev,
                              categories: {
                                ...prev.categories,
                                generalKnowledge: { 
                                  ...prev.categories?.generalKnowledge,
                                  enabled,
                                  rounds: enabled ? (prev.categories?.generalKnowledge?.rounds || 3) : 0
                                }
                              }
                            }))
                          }
                        />
                        <span className="text-sm font-medium">General Knowledge</span>
                      </div>
                      {ultimoteConfig.categories?.generalKnowledge?.enabled && (
                        <Badge variant="outline" className="text-xs">
                          {ultimoteConfig.categories.generalKnowledge.rounds} rounds
                        </Badge>
                      )}
                    </div>
                    
                    {ultimoteConfig.categories?.generalKnowledge?.enabled && (
                      <div className="pl-8 space-y-2">
                        <div>
                          <Label className="text-xs">Rounds: {ultimoteConfig.categories.generalKnowledge.rounds}</Label>
                          <Slider
                            value={[ultimoteConfig.categories.generalKnowledge.rounds]}
                            onValueChange={([value]) => {
                              debugLog(`[MultiplayerLobby] Setting generalKnowledge rounds to ${value}`)
                              setUltimoteConfig(prev => ({
                                ...prev,
                                categories: {
                                  ...prev.categories,
                                  generalKnowledge: { 
                                    ...prev.categories?.generalKnowledge,
                                    rounds: value,
                                    enabled: value > 0
                                  }
                                }
                              }))
                            }}
                            min={0}
                            max={10}
                            step={1}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label className="text-xs">Difficulty: {ultimoteConfig.categories.generalKnowledge.difficulty || 3}</Label>
                          <Slider
                            value={[ultimoteConfig.categories.generalKnowledge.difficulty || 3]}
                            onValueChange={([value]) => {
                              debugLog(`[MultiplayerLobby] Setting generalKnowledge difficulty to ${value}`)
                              setUltimoteConfig(prev => ({
                                ...prev,
                                categories: {
                                  ...prev.categories,
                                  generalKnowledge: { 
                                    ...prev.categories?.generalKnowledge,
                                    difficulty: value
                                  }
                                }
                              }))
                            }}
                            min={1}
                            max={5}
                            step={1}
                            className="mt-1"
                          />
                        </div>
                        
                        {/* Category Selection */}
                        <div>
                          <Label className="text-xs mb-2">Categories:</Label>
                          <div className="grid grid-cols-2 gap-2 mt-1">
                            {availableGeneralCategories.map((category) => (
                              <Button
                                key={category.slug}
                                variant={ultimoteConfig.categories?.generalKnowledge?.categories?.includes(category.slug) ? "default" : "outline"}
                                size="sm"
                                onClick={() => {
                                  const currentCategories = ultimoteConfig.categories?.generalKnowledge?.categories || []
                                  const newCategories = currentCategories.includes(category.slug)
                                    ? currentCategories.filter(c => c !== category.slug)
                                    : [...currentCategories, category.slug]
                                  
                                  debugLog('[MultiplayerLobby] Updated general knowledge categories:', newCategories)
                                  setUltimoteConfig(prev => {
                                    const updated = {
                                      ...prev,
                                      categories: {
                                        ...prev.categories,
                                        generalKnowledge: { 
                                          ...prev.categories?.generalKnowledge,
                                          categories: newCategories
                                        }
                                      }
                                    }
                                    debugLog('[MultiplayerLobby] New ultimoteConfig after update:', JSON.stringify(updated.categories?.generalKnowledge, null, 2))
                                    return updated
                                  })
                                }}
                                className="text-xs justify-start h-auto py-1"
                              >
                                <span className="flex items-center gap-1">
                                  <span>{iconMap[category.icon] || category.icon || '📌'}</span>
                                  <span>{category.name}</span>
                                  {category.questionCount > 0 && (
                                    <span className="text-xs opacity-60">({category.questionCount})</span>
                                  )}
                                </span>
                              </Button>
                            ))}
                          </div>
                          {(!ultimoteConfig.categories?.generalKnowledge?.categories || 
                            ultimoteConfig.categories.generalKnowledge.categories.length === 0) && (
                            <p className="text-xs text-red-500 mt-2">
                              Select at least one category
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Total Rounds Summary */}
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                  <p className="text-sm">
                    <span className="font-medium">Total Rounds: </span>
                    {Object.values(ultimoteConfig.categories || {})
                      .filter(cat => cat.enabled)
                      .reduce((sum, cat) => sum + cat.rounds, 0)}
                  </p>
                </div>
                
                {/* Configuration Debugger */}
                {process.env.NODE_ENV === 'development' && (
                  <UltimoteConfigDebugger config={ultimoteConfig} className="mt-4" />
                )}
              </div>
            )}
            
            {/* Content Filters */}
            <div className="border-t pt-4">
              <GameFilterToggle 
                gameMode={selectedGameMode}
                onFilterChange={(enabled) => {
                  if (enabled) {
                    setContentFilters(filterService.getCurrentFilters())
                  } else {
                    setContentFilters(null)
                  }
                }}
                className="justify-between"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={handleStartGame}
              className="flex-1 touch-manipulation"
              size="lg"
              disabled={
                gameState.players.length < 2 || 
                isStarting ||
                (selectedGameMode === 'ultimote' && 
                 ultimoteConfig?.categories?.generalKnowledge?.enabled && 
                 ultimoteConfig?.categories?.generalKnowledge?.rounds > 0 &&
                 (!ultimoteConfig?.categories?.generalKnowledge?.categories || 
                  ultimoteConfig?.categories?.generalKnowledge?.categories.length === 0))
              }
            >
              {isStarting ? (
                <>
                  <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                  Starting...
                </>
              ) : (
                <>
                  <Play className="h-5 w-5 mr-2" />
                  Start Game
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={handleLeaveGame}
              className="touch-manipulation"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Leave
            </Button>
          </div>

          {gameState.players.length < 2 && (
            <p className="text-center text-sm text-gray-500">
              Need at least 2 players to start
            </p>
          )}
          
          {selectedGameMode === 'ultimote' && 
           ultimoteConfig?.categories?.generalKnowledge?.enabled && 
           ultimoteConfig?.categories?.generalKnowledge?.rounds > 0 &&
           (!ultimoteConfig?.categories?.generalKnowledge?.categories || 
            ultimoteConfig?.categories?.generalKnowledge?.categories.length === 0) && (
            <p className="text-center text-sm text-red-500">
              Please select at least one general knowledge category
            </p>
          )}
        </CardContent>
      </Card>
    )
  }

  // Player view - join game
  if (role === "player" && !hasJoined) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="max-w-md mx-auto backdrop-blur-lg bg-white/80 dark:bg-gray-900/80 border-white/20 shadow-2xl">
          <CardHeader>
            <CardTitle className="text-2xl sm:text-3xl text-center bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent font-bold">
              Join Game
            </CardTitle>
          </CardHeader>
        <CardContent className="space-y-4">
          {user && user.name ? (
            <div className="text-center p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg mb-4">
              <p className="text-lg">Playing as <span className="font-semibold">{user.name}</span></p>
            </div>
          ) : (
            <div>
              <Label htmlFor="player-name">Your Name</Label>
              <Input
                id="player-name"
                value={playerName}
                onChange={(e) => setPlayerName(e.target.value)}
                placeholder="Enter your name"
                maxLength={20}
              />
            </div>
          )}

          <div>
            <Label htmlFor="game-code">Game Code</Label>
            <Input
              id="game-code"
              value={joinGameCode}
              onChange={(e) => setJoinGameCode(e.target.value.toUpperCase())}
              placeholder="Enter 4-digit code"
              maxLength={4}
              className="text-center text-2xl font-bold tracking-wider"
            />
          </div>

          <Button
            onClick={handleJoinGame}
            className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 touch-manipulation"
            size="lg"
            disabled={isJoining || !playerName.trim() || !joinGameCode.trim()}
          >
            {isJoining ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                Joining...
              </>
            ) : (
              <>
                <Users className="h-5 w-5 mr-2" />
                Join Game
              </>
            )}
          </Button>

          <Button
            variant="outline"
            onClick={onBackToMenu}
            className="w-full"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back
          </Button>
        </CardContent>
      </Card>
      </motion.div>
    )
  }

  // Player view - waiting in lobby
  if (role === "player" && hasJoined && gameState) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl text-center">Waiting for Host to Start</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Game Info */}
          <div className="text-center">
            <p className="text-sm text-gray-500 mb-1">Game Code</p>
            <p className="text-3xl font-bold">{gameState.pin}</p>
          </div>

          {/* Players List */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Users className="h-5 w-5" />
              Players ({gameState.players.length})
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {gameState.players.map((player) => (
                <motion.div
                  key={player.id}
                  initial={{ opacity: 0, scale: 0.9, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg p-4 flex items-center gap-3 shadow-md hover:shadow-lg transition-shadow"
                >
                  <div className="flex-1">
                    <p className="font-medium">{player.name}</p>
                    {player.isHost && (
                      <Badge variant="secondary" className="text-xs">Host</Badge>
                    )}
                  </div>
                  {player.connected ? (
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                  ) : (
                    <div className="w-2 h-2 bg-gray-400 rounded-full" />
                  )}
                </motion.div>
              ))}
            </div>
          </div>

          <Button
            variant="outline"
            onClick={handleLeaveGame}
            className="w-full"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Leave Game
          </Button>
        </CardContent>
      </Card>
    )
  }

  // Debug logging
  console.log('[MultiplayerLobby] Render state:', {
    role,
    gameState: !!gameState,
    playerName: !!playerName,
    hasJoined,
    isCreating,
    isJoining,
    isConnected
  })

  // Initial state - enter name for host or waiting for game creation
  if (role === "host" && !gameState) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl text-center">Setting up Game</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {user && playerName ? (
            <div className="text-center p-4 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg">
              <p className="text-lg">Welcome back, <span className="font-semibold">{playerName}</span>!</p>
            </div>
          ) : (
            <div>
              <Label htmlFor="host-name">Your Name</Label>
              <Input
                id="host-name"
                value={playerName}
                onChange={(e) => setPlayerName(e.target.value)}
                placeholder="Enter your name"
                maxLength={20}
                autoFocus
              />
            </div>
          )}

          {/* Connection Status */}
          <div className="text-center">
            <Badge variant={isConnected ? "success" : "destructive"} className="mb-2">
              {isConnected ? "Connected" : "Not Connected"}
            </Badge>
          </div>

          {isCreating && (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Creating game lobby...</span>
            </div>
          )}

          {!isCreating && !isConnected && (
            <div className="text-center text-sm text-destructive">
              Socket connection failed. Please check if the server is running on port 3001.
            </div>
          )}

          <Button
            onClick={() => playerName && handleCreateGame()}
            className="w-full"
            size="lg"
            disabled={!playerName.trim() || isCreating || !isConnected}
          >
            {isCreating ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              'Create Game'
            )}
          </Button>

          <Button
            variant="outline"
            onClick={onBackToMenu}
            className="w-full"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      {/* WebSocket Debugger in development */}
      {process.env.NODE_ENV === 'development' && <WebSocketDebugger />}
      {/* WebSocket Monitor for better visibility */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-50">
          <WebSocketMonitor />
        </div>
      )}
    </>
  )
}