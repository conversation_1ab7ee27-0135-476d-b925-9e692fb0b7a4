"use client"

import { useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import type { GameMode } from "@/lib/types"
import type { CustomQuizSettings } from "./custom-quiz-config"
import { useQuizGameState } from "@/hooks/useQuizGameState"
import { useAudioManager } from "@/hooks/use-audio-manager"
import { useGameTransitions } from "@/hooks/useGameTransitions"

// Extracted components
import { ProgressHeader } from "./quiz/ProgressHeader"
import { AudioPlayerControls } from "./quiz/AudioPlayerControls"
import { QuestionDisplayCard } from "./quiz/QuestionDisplayCard"

// Specialized quiz components
import { QuickFireQuiz } from "./quick-fire-quiz"
import { AudioTricksQuiz } from "./audio-tricks-quiz"
import { AlbumArtQuiz } from "./album-art-quiz"
import { AudioFingerprintQuiz } from "./audio-fingerprint-quiz"
import { UlTimoteGameHandler } from "./ultimote-game-handler"
import { HitsterGameScreen } from "./hitster-game-screen"
import { MultiplayerQuiz } from "./multiplayer-quiz"
import { TeamQuizInterface } from "./team-quiz-interface"
import { TeamFormation } from "./team-formation"
import { AudioMixerPanel } from "./audio-mixer-panel"
import { GameTransition } from "./GameTransition"

// Loading and utility components
import { Card, CardContent } from "@/components/ui/card"
import { LoadingScreen } from "./LoadingScreen"
import { toast } from "sonner"

interface QuizInterfaceProps {
  gameMode: GameMode
  onGameComplete: (results: any) => void
  onBackToMenu: () => void
  isMultiplayer: boolean
  multiplayerRole?: "host" | "player"
  genreFilter?: string
  customSettings?: CustomQuizSettings | null
  showBackButton?: boolean
}

export function QuizInterfaceRefactored({
  gameMode,
  genreFilter,
  customSettings,
  onGameComplete,
  onBackToMenu,
  isMultiplayer,
  multiplayerRole,
  showBackButton = true,
}: QuizInterfaceProps) {
  
  // Main game state hook
  const gameState = useQuizGameState({
    gameMode,
    isMultiplayer,
    multiplayerRole,
    genreFilter,
    customSettings
  })

  // Audio management
  const {
    audioManager,
    audioStatus,
    volume,
    audioError,
    isPlaying,
    showMixer,
    setVolume,
    setShowMixer,
    togglePlayPause
  } = useAudioManager()

  // Game transitions
  const {
    isTransitioning,
    transitionConfig,
    hideTransition,
    showGameStart,
    showRoundStart,
    showGameEnd
  } = useGameTransitions()

  // Initialize game
  useEffect(() => {
    if (!isMultiplayer && gameState.questions.length === 0 && !gameState.isLoadingQuestions) {
      gameState.setIsLoadingQuestions(true)
      // Load questions based on game mode
      loadQuestions()
    }
  }, [gameMode, isMultiplayer, gameState.questions.length])

  const loadQuestions = async () => {
    try {
      // This would be replaced with actual question loading logic
      // For now, mock some questions
      const mockQuestions = Array.from({ length: 10 }, (_, i) => ({
        id: i + 1,
        question: `Question ${i + 1}`,
        options: ['Option A', 'Option B', 'Option C', 'Option D'],
        correctAnswerIndex: Math.floor(Math.random() * 4),
        type: 'multiple-choice'
      }))
      
      gameState.setQuestions(mockQuestions)
      gameState.setIsLoadingQuestions(false)
      
      // Show game start transition
      showGameStart(`${gameMode} Quiz Started!`, false, [])
    } catch (error) {
      console.error('Failed to load questions:', error)
      toast.error('Failed to load quiz questions')
      gameState.setIsLoadingQuestions(false)
    }
  }

  // Handle answer submission
  const handleAnswerSubmit = () => {
    const currentQuestion = gameState.getCurrentQuestion()
    
    if (gameState.isSliderMode()) {
      if (!gameState.hasInteractedWithSlider) {
        toast.error('Please set your answer using the slider')
        return
      }
      gameState.submitAnswer(gameState.sliderValue)
    } else {
      if (gameState.selectedAnswer === null) {
        toast.error('Please select an answer')
        return
      }
      gameState.submitAnswer(gameState.selectedAnswer)
    }
  }

  // Handle volume changes
  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
    if (audioManager) {
      audioManager.setVolume(newVolume)
    }
  }

  // Handle game completion
  useEffect(() => {
    if (gameState.gameEnded) {
      const results = {
        score: gameState.score,
        totalQuestions: gameState.questions.length,
        correctAnswers: gameState.answers.filter(a => a.isCorrect).length,
        gameMode,
        isMultiplayer,
        answers: gameState.answers,
        timePlayed: Date.now() - gameState.startTime
      }
      onGameComplete(results)
    }
  }, [gameState.gameEnded])

  // Show loading screen
  if (gameState.isLoadingQuestions || (!isMultiplayer && gameState.questions.length === 0)) {
    return (
      <LoadingScreen
        title="ulTimote"
        message={gameState.isLoadingQuestions ? 'Loading quiz from database...' : 'Preparing quiz...'}
        error={audioError}
      />
    )
  }

  // Handle specialized game modes
  if (isMultiplayer) {
    return <MultiplayerQuiz onGameComplete={onGameComplete} onBackToMenu={onBackToMenu} />
  }

  if (gameMode === 'hitster-timeline') {
    const urlParams = new URLSearchParams(window.location.search)
    const gameCode = urlParams.get('gameCode') || localStorage.getItem('currentGameCode') || ''
    const playerId = gameState.playerId || 'player-' + Math.random().toString(36).substring(7)
    const playerName = gameState.playerName || 'Player'
    
    return (
      <HitsterGameScreen
        gameCode={gameCode}
        playerId={playerId}
        playerName={playerName}
        isHost={multiplayerRole === 'host'}
        onBackToLobby={onBackToMenu}
      />
    )
  }

  if (gameMode === 'quick-fire' && gameState.questions.length > 0) {
    return (
      <QuickFireQuiz
        questions={gameState.questions}
        onComplete={onGameComplete}
        onBackToMenu={onBackToMenu}
      />
    )
  }

  if (gameMode === 'audio-manipulation' && gameState.questions.length > 0) {
    return (
      <AudioTricksQuiz
        questions={gameState.questions}
        onComplete={onGameComplete}
        onBackToMenu={onBackToMenu}
      />
    )
  }

  if (gameMode === 'album-art' && gameState.questions.length > 0) {
    return (
      <AlbumArtQuiz
        questions={gameState.questions}
        onComplete={onGameComplete}
        onBackToMenu={onBackToMenu}
      />
    )
  }

  if (gameMode === 'audio-fingerprint' && gameState.questions.length > 0) {
    return (
      <AudioFingerprintQuiz
        questions={gameState.questions}
        onComplete={onGameComplete}
        onBackToMenu={onBackToMenu}
      />
    )
  }

  // Safety check
  if (!gameState.questions || gameState.questions.length === 0 || gameState.currentQuestion >= gameState.questions.length) {
    return (
      <LoadingScreen
        title="ulTimote"
        message="Loading Questions..."
        error={null}
      />
    )
  }

  const currentQuestion = gameState.getCurrentQuestion()

  // Main quiz interface
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68] p-2 sm:p-4">
      <div className="max-w-4xl mx-auto space-y-3 sm:space-y-4 md:space-y-6 relative">
        
        {/* ulTimote Branding Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-6"
        >
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-extrabold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent mb-1 sm:mb-2">
            ulTimote
          </h1>
          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 font-medium">
            Music Quiz Challenge
          </p>
        </motion.div>

        {/* Progress Header */}
        <ProgressHeader
          gameMode={gameMode}
          currentQuestion={gameState.currentQuestion}
          totalQuestions={gameState.questions.length}
          score={gameState.score}
          streak={gameState.streak}
          timeLeft={gameState.timeLeft}
          showBackButton={showBackButton}
          isMultiplayer={isMultiplayer}
          multiplayerGameId={gameState.multiplayerGameId}
          playerName={gameState.playerName}
          leaderboard={gameState.leaderboard}
          waitingForPlayers={gameState.waitingForPlayers}
          onBackToMenu={onBackToMenu}
        />

        {/* Main Quiz Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          
          {/* Question Display - Takes up 2 columns on large screens */}
          <div className="lg:col-span-2">
            <QuestionDisplayCard
              question={currentQuestion}
              gameMode={gameMode}
              selectedAnswer={gameState.selectedAnswer}
              sliderValue={gameState.sliderValue}
              hasInteractedWithSlider={gameState.hasInteractedWithSlider}
              showSliderPrompt={gameState.showSliderPrompt}
              isMultiplayer={isMultiplayer}
              isAnswered={gameState.showFeedback}
              showFeedback={gameState.showFeedback}
              correctAnswer={currentQuestion?.correctAnswerIndex}
              onSelectAnswer={gameState.setSelectedAnswer}
              onSliderChange={([value]) => gameState.setSliderValue(value)}
              onSliderInteraction={() => {}} // Handled in setSliderValue
              onSubmitAnswer={handleAnswerSubmit}
            />
          </div>

          {/* Audio Controls - Takes up 1 column on large screens */}
          <div className="space-y-4">
            <AudioPlayerControls
              audioStatus={audioStatus}
              volume={volume}
              audioError={audioError}
              isPlaying={isPlaying}
              showMixer={showMixer}
              onPlayPause={togglePlayPause}
              onVolumeChange={handleVolumeChange}
              onToggleMixer={() => setShowMixer(!showMixer)}
            />

            {/* Waiting for players indicator */}
            {gameState.waitingForPlayers && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4 text-center"
              >
                <div className="animate-spin rounded-full h-8 w-8 border-4 border-yellow-500/30 border-t-yellow-500 mx-auto mb-2"></div>
                <p className="text-yellow-400 font-medium">
                  Waiting for other players...
                </p>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Audio Mixer Panel */}
      <AnimatePresence>
        {showMixer && audioManager && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="fixed top-0 right-0 h-full w-full sm:w-96 bg-white/95 dark:bg-gray-900/95 backdrop-blur-lg border-l border-white/20 dark:border-white/10 shadow-2xl z-50 overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">Audio Mixer</h2>
                <button
                  onClick={() => setShowMixer(false)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  ×
                </button>
              </div>
              <AudioMixerPanel
                mixer={audioManager as any}
                onSourceAdded={(source) => toast.success(`Added audio source: ${source.id}`)}
                onSourceRemoved={(sourceId) => toast.success(`Removed audio source: ${sourceId}`)}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Game Transitions */}
      {isTransitioning && transitionConfig && (
        <GameTransition
          {...transitionConfig}
          onComplete={hideTransition}
        />
      )}
    </div>
  )
} 