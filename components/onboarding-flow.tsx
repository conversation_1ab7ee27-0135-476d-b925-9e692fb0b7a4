"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { motion, AnimatePresence } from "framer-motion"
import { 
  User, 
  Music, 
  Headphones, 
  Star, 
  ArrowRight, 
  ArrowLeft, 
  Check, 
  Heart,
  Volume2,
  Palette,
  Globe,
  ChevronRight,
  Sparkles,
  Loader2,
} from "lucide-react"
import { ScrollArea } from '@/components/ui/scroll-area'
import { useIsMobile } from '@/hooks/use-mobile'

interface OnboardingFlowProps {
  onComplete: (userProfile: UserProfile) => void
  onSkip: () => Promise<void>
}

export interface UserProfile {
  username: string
  displayName: string
  email: string
  bio: string
  avatar: string
  favoriteGenres: string[]
  preferences: {
    theme: 'light' | 'dark' | 'system'
    difficulty: number
    volume: number
    autoPlay: boolean
    showHints: boolean
    soundEffects: boolean
  }
  location?: string
  isFirstTime: boolean
}

const ONBOARDING_STEPS = [
  { id: 'welcome', title: 'Welcome!', description: 'Let\'s get you started' },
  { id: 'profile', title: 'Create Profile', description: 'Tell us about yourself' },
  { id: 'avatar', title: 'Choose Avatar', description: 'Pick your music persona' },
  { id: 'preferences', title: 'Game Settings', description: 'Customize your experience' },
  { id: 'genres', title: 'Music Taste', description: 'What do you love?' },
  { id: 'complete', title: 'All Set!', description: 'Ready to play' }
]

// Simplified steps for local network mode
const SIMPLIFIED_ONBOARDING_STEPS = [
  { id: 'welcome', title: 'Welcome!', description: 'Quick setup for local play' },
  { id: 'profile', title: 'Your Name', description: 'What should we call you?' },
  { id: 'complete', title: 'All Set!', description: 'Ready to play' }
]

const MUSIC_GENRES = [
  { id: 'rock', name: 'Rock', icon: '🎸', color: 'bg-red-500' },
  { id: 'pop', name: 'Pop', icon: '🎤', color: 'bg-pink-500' },
  { id: 'hip-hop', name: 'Hip-Hop', icon: '🎧', color: 'bg-purple-500' },
  { id: 'jazz', name: 'Jazz', icon: '🎷', color: 'bg-yellow-500' },
  { id: 'classical', name: 'Classical', icon: '🎼', color: 'bg-blue-500' },
  { id: 'electronic', name: 'Electronic', icon: '🔊', color: 'bg-green-500' },
  { id: 'country', name: 'Country', icon: '🤠', color: 'bg-orange-500' },
  { id: 'r&b', name: 'R&B', icon: '💫', color: 'bg-indigo-500' },
  { id: 'reggae', name: 'Reggae', icon: '🌴', color: 'bg-emerald-500' },
  { id: 'blues', name: 'Blues', icon: '🎺', color: 'bg-slate-500' },
  { id: 'folk', name: 'Folk', icon: '🪕', color: 'bg-amber-500' },
  { id: 'metal', name: 'Metal', icon: '⚡', color: 'bg-gray-800' }
]

const AVATAR_OPTIONS = [
  { id: 'rockstar', name: 'Rock Legend', icon: '🎸', description: 'Classic rock vibes' },
  { id: 'popstar', name: 'Pop Icon', icon: '⭐', description: 'Chart-topping energy' },
  { id: 'dj', name: 'DJ Master', icon: '🎧', description: 'Electronic beats' },
  { id: 'jazz', name: 'Jazz Cat', icon: '🎷', description: 'Smooth sophistication' },
  { id: 'classical', name: 'Maestro', icon: '🎼', description: 'Timeless elegance' },
  { id: 'rapper', name: 'Hip-Hop Head', icon: '🎤', description: 'Urban rhythm' },
  { id: 'country', name: 'Country Star', icon: '🤠', description: 'Down-home charm' },
  { id: 'indie', name: 'Indie Artist', icon: '🎨', description: 'Creative spirit' },
  { id: 'metal', name: 'Metal Head', icon: '⚡', description: 'Heavy power' },
  { id: 'soul', name: 'Soul Singer', icon: '💫', description: 'Deep emotion' },
  { id: 'folk', name: 'Folk Hero', icon: '🪕', description: 'Storytelling tradition' },
  { id: 'disco', name: 'Disco Fever', icon: '🕺', description: 'Dance floor magic' }
]

export function OnboardingFlow({ onComplete, onSkip }: OnboardingFlowProps) {
  // Check for simplified mode from environment variable
  const isSimplifiedMode = process.env.NEXT_PUBLIC_SIMPLIFIED_ONBOARDING === 'true'
  const steps = isSimplifiedMode ? SIMPLIFIED_ONBOARDING_STEPS : ONBOARDING_STEPS
  const isMobile = useIsMobile()
  
  // Prevent body scroll on mobile when onboarding is active
  useEffect(() => {
    if (isMobile) {
      document.body.style.overflow = 'hidden'
      document.body.style.position = 'fixed'
      document.body.style.width = '100%'
      
      return () => {
        document.body.style.overflow = ''
        document.body.style.position = ''
        document.body.style.width = ''
      }
    }
  }, [isMobile])
  
  const [currentStep, setCurrentStep] = useState(0)
  const [profile, setProfile] = useState<UserProfile>({
    username: '',
    displayName: '',
    email: '',
    bio: '',
    avatar: 'rockstar',
    favoriteGenres: isSimplifiedMode ? ['pop', 'rock'] : [], // Default genres for simplified mode
    preferences: {
      theme: 'system',
      difficulty: 3,
      volume: 70,
      autoPlay: true,
      showHints: true,
      soundEffects: true
    },
    location: '',
    isFirstTime: true
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isAnimating, setIsAnimating] = useState(false)
  const [isSkipping, setIsSkipping] = useState(false)

  // Handle mobile viewport adjustments
  useEffect(() => {
    if (isMobile) {
      // Prevent zoom on input focus on iOS
      const viewport = document.querySelector('meta[name=viewport]')
      if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no')
      }

      // Prevent body scroll when onboarding is active
      document.body.style.overflow = 'hidden'
      document.body.style.position = 'fixed'
      document.body.style.width = '100%'

      return () => {
        document.body.style.overflow = ''
        document.body.style.position = ''
        document.body.style.width = ''

        // Restore normal viewport
        if (viewport) {
          viewport.setAttribute('content', 'width=device-width, initial-scale=1')
        }
      }
    }
  }, [isMobile])

  const currentStepData = steps[currentStep]
  const progress = ((currentStep + 1) / steps.length) * 100

  const validateStep = () => {
    const newErrors: Record<string, string> = {}

    if (currentStepData.id === 'profile') {
      if (!profile.username.trim()) {
        newErrors.username = 'Username is required'
      } else if (profile.username.length < 3) {
        newErrors.username = 'Username must be at least 3 characters'
      } else if (!/^[a-zA-Z0-9_-]+$/.test(profile.username)) {
        newErrors.username = 'Username can only contain letters, numbers, underscore, and dash'
      }

      // Display name will be same as username, no need to validate separately

      // Only require email in full mode
      if (!isSimplifiedMode) {
        if (!profile.email.trim()) {
          newErrors.email = 'Email is required'
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profile.email)) {
          newErrors.email = 'Please enter a valid email address'
        }
      }
    }

    if (currentStepData.id === 'genres' && profile.favoriteGenres.length === 0) {
      newErrors.genres = 'Please select at least one genre you enjoy'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const nextStep = () => {
    if (!validateStep()) return

    if (currentStep < steps.length - 1) {
      setIsAnimating(true)
      setTimeout(() => {
        setCurrentStep(currentStep + 1)
        setIsAnimating(false)
      }, 200)
    } else {
      onComplete(profile)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setIsAnimating(true)
      setTimeout(() => {
        setCurrentStep(currentStep - 1)
        setIsAnimating(false)
      }, 200)
    }
  }

  const toggleGenre = (genreId: string) => {
    setProfile(prev => ({
      ...prev,
      favoriteGenres: prev.favoriteGenres.includes(genreId)
        ? prev.favoriteGenres.filter(g => g !== genreId)
        : [...prev.favoriteGenres, genreId]
    }))
  }

  const renderStepContent = () => {
    switch (currentStepData.id) {
      case 'welcome':
        return (
          <motion.div 
            className="text-center space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="w-16 h-16 sm:w-24 sm:h-24 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <Music className="w-8 h-8 sm:w-12 sm:h-12 text-white" />
            </div>
            <div>
              <h2 className="text-2xl sm:text-3xl font-bold mb-2 sm:mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Welcome to Music Quiz!
              </h2>
              <p className="text-base sm:text-lg text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                Test your music knowledge across decades, genres, and chart positions. 
                Let&apos;s create your perfect quiz experience!
              </p>
            </div>
            <div className="grid grid-cols-3 gap-2 sm:gap-4 max-w-xs sm:max-w-sm mx-auto">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center mb-2">
                  <Headphones className="w-6 h-6 text-blue-600" />
                </div>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Audio Quizzes</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto bg-purple-100 dark:bg-purple-900/50 rounded-lg flex items-center justify-center mb-2">
                  <Star className="w-6 h-6 text-purple-600" />
                </div>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Achievements</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto bg-green-100 dark:bg-green-900/50 rounded-lg flex items-center justify-center mb-2">
                  <Heart className="w-6 h-6 text-green-600" />
                </div>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Playlists</p>
              </div>
            </div>
          </motion.div>
        )

      case 'profile':
        return (
          <motion.div 
            className="space-y-6"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="text-center mb-6">
              <User className="w-12 h-12 sm:w-16 sm:h-16 mx-auto text-blue-600 mb-4" />
              <h3 className="text-xl sm:text-2xl font-bold">
                {isSimplifiedMode ? 'Quick Setup' : 'Create Your Profile'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {isSimplifiedMode ? 'Just the basics to get started' : 'Tell us a bit about yourself'}
              </p>
            </div>

            <div>
              <Label htmlFor="username">Your Name *</Label>
              <Input
                id="username"
                value={profile.username}
                onChange={(e) => {
                  const value = e.target.value;
                  setProfile(prev => ({ 
                    ...prev, 
                    username: value,
                    displayName: value // Set displayName same as username
                  }))
                }}
                placeholder="Enter your name"
                className={errors.username ? 'border-red-500' : ''}
              />
              {errors.username && <p className="text-red-500 text-sm mt-1">{errors.username}</p>}
              <p className="text-xs text-gray-500 mt-1">This will be your username and display name</p>
            </div>

            {!isSimplifiedMode && (
              <>
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profile.email}
                    onChange={(e) => setProfile(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                </div>

                <div>
                  <Label htmlFor="location">Location (Optional)</Label>
                  <Input
                    id="location"
                    value={profile.location}
                    onChange={(e) => setProfile(prev => ({ ...prev, location: e.target.value }))}
                    placeholder="New York, NY"
                  />
                </div>

                <div>
                  <Label htmlFor="bio">Bio (Optional)</Label>
                  <Textarea
                    id="bio"
                    value={profile.bio}
                    onChange={(e) => setProfile(prev => ({ ...prev, bio: e.target.value }))}
                    placeholder="Tell us about your music taste and what you love about music!"
                    rows={3}
                  />
                </div>
              </>
            )}

            {isSimplifiedMode && (
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  🚀 <strong>Quick Mode:</strong> Perfect for local network setups! We&apos;ve streamlined the setup to get you playing faster.
                </p>
              </div>
            )}
          </motion.div>
        )

      case 'avatar':
        return (
          <motion.div 
            className="space-y-6"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="text-center mb-4 sm:mb-6">
              <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-3 sm:mb-4">
                <Palette className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
              </div>
              <h3 className="text-xl sm:text-2xl font-bold">Choose Your Avatar</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm sm:text-base">Pick a music persona that represents you</p>
            </div>

            {/* Current Selection */}
            <div className="text-center mb-6">
              <div className="inline-block relative">
                <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-3xl sm:text-4xl">
                  {AVATAR_OPTIONS.find(a => a.id === profile.avatar)?.icon}
                </div>
                <Badge className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">Selected</Badge>
              </div>
              <h4 className="text-lg sm:text-xl font-semibold mt-3">
                {AVATAR_OPTIONS.find(a => a.id === profile.avatar)?.name}
              </h4>
              <p className="text-gray-600 dark:text-gray-400">
                {AVATAR_OPTIONS.find(a => a.id === profile.avatar)?.description}
              </p>
            </div>

            {/* Avatar Grid */}
            <div className="grid grid-cols-3 sm:grid-cols-4 gap-2 sm:gap-4">
              {AVATAR_OPTIONS.map((avatar) => (
                <motion.div
                  key={avatar.id}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Card
                    className={`cursor-pointer transition-all hover:shadow-lg ${
                      profile.avatar === avatar.id 
                        ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950' 
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                    }`}
                    onClick={() => setProfile(prev => ({ ...prev, avatar: avatar.id }))}
                  >
                    <CardContent className="p-2 text-center">
                      <div className="text-2xl sm:text-3xl mb-1">{avatar.icon}</div>
                      <h5 className="font-semibold text-xs sm:text-sm truncate">{avatar.name}</h5>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 hidden sm:block truncate">
                        {avatar.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )

      case 'preferences':
        return (
          <motion.div 
            className="space-y-6"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="text-center mb-4 sm:mb-6">
              <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mb-3 sm:mb-4">
                <Headphones className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
              </div>
              <h3 className="text-xl sm:text-2xl font-bold">Game Preferences</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm sm:text-base">Customize your quiz experience</p>
            </div>

            <div className="space-y-6">
              {/* Difficulty */}
              <div>
                <Label className="text-base font-semibold">Difficulty Level</Label>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  How challenging should your quizzes be?
                </p>
                <Slider
                  min={1}
                  max={5}
                  step={1}
                  value={[profile.preferences.difficulty]}
                  onValueChange={([val]) => setProfile(p => ({ ...p, preferences: { ...p.preferences, difficulty: val } }))}
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Easy</span>
                  <span>Normal</span>
                  <span>Hard</span>
                  <span>Expert</span>
                  <span>Master</span>
                </div>
              </div>

              {/* Volume */}
              <div>
                <Label className="text-base font-semibold">Master Volume</Label>
                <div className="flex items-center gap-4 mt-2">
                  <Volume2 className="w-5 h-5 text-gray-500" />
                  <Slider
                    min={0}
                    max={100}
                    step={1}
                    value={[profile.preferences.volume]}
                    onValueChange={([val]) => setProfile(p => ({ ...p, preferences: { ...p.preferences, volume: val } }))}
                  />
                </div>
              </div>

              {/* Toggles */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="autoPlay" 
                    checked={profile.preferences.autoPlay} 
                    onCheckedChange={checked => setProfile(p => ({ ...p, preferences: { ...p.preferences, autoPlay: !!checked } }))}
                  />
                  <Label htmlFor="autoPlay">Auto-play next track</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="showHints" 
                    checked={profile.preferences.showHints} 
                    onCheckedChange={checked => setProfile(p => ({ ...p, preferences: { ...p.preferences, showHints: !!checked } }))}
                  />
                  <Label htmlFor="showHints">Show hints for tough questions</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="soundEffects" 
                    checked={profile.preferences.soundEffects} 
                    onCheckedChange={checked => setProfile(p => ({ ...p, preferences: { ...p.preferences, soundEffects: !!checked } }))}
                  />
                  <Label htmlFor="soundEffects">Enable sound effects</Label>
                </div>
              </div>
            </div>
          </motion.div>
        )

      case 'genres':
        return (
          <motion.div 
            className="space-y-6"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="text-center mb-4 sm:mb-6">
              <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gradient-to-br from-pink-500 to-red-500 rounded-full flex items-center justify-center mb-3 sm:mb-4">
                <Heart className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
              </div>
              <h3 className="text-xl sm:text-2xl font-bold">Your Music Taste</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm sm:text-base">
                Select your favorite genres to personalize your quizzes
              </p>
            </div>

            {errors.genres && <p className="text-red-500 text-sm text-center -mt-2 mb-4">{errors.genres}</p>}

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 sm:gap-4">
              {MUSIC_GENRES.map(genre => (
                <Card
                  key={genre.id}
                  className={`cursor-pointer transition-all ${
                    profile.favoriteGenres.includes(genre.id)
                      ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950 scale-105'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                  onClick={() => toggleGenre(genre.id)}
                >
                  <CardContent className="p-3 sm:p-4 text-center">
                    <div className="text-2xl sm:text-3xl mb-1 sm:mb-2">{genre.icon}</div>
                    <h4 className="text-xs sm:text-sm font-semibold truncate">{genre.name}</h4>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        )

      case 'complete':
        return (
          <motion.div 
            className="text-center space-y-6"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', stiffness: 260, damping: 20 }}
          >
            <div className="w-24 h-24 mx-auto bg-gradient-to-br from-green-400 to-teal-500 rounded-full flex items-center justify-center">
              <Sparkles className="w-12 h-12 text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-4">You&apos;re All Set, {profile.displayName}!</h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                Your profile is complete. Get ready to put your music knowledge to the test.
              </p>
            </div>
            <Card className="bg-gray-50 dark:bg-gray-800 p-4 text-left max-w-sm mx-auto">
              <CardHeader className="p-2">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Avatar>
                    <AvatarFallback>{profile.avatar.slice(0, 2)}</AvatarFallback>
                  </Avatar>
                  {profile.displayName}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2 text-sm space-y-2">
                <p>
                  <strong className="font-semibold">Username:</strong> {profile.username}
                </p>
                <div className="flex flex-wrap gap-1">
                  <strong className="font-semibold">Genres:</strong> 
                  {profile.favoriteGenres.map(g => (
                    <Badge key={g} variant="secondary">{MUSIC_GENRES.find(mg => mg.id === g)?.name}</Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )

      default:
        return null
    }
  }

  return (
    <div className={`w-full bg-gray-100 dark:bg-gray-900 flex flex-col items-center justify-center p-2 sm:p-4 transition-colors duration-300 ${
      isMobile ? 'min-h-[100dvh]' : 'min-h-screen'
    }`}>
      <div className={`w-full max-w-2xl mx-auto flex flex-col ${
        isMobile ? 'h-[100dvh] max-h-[100dvh]' : 'flex-1'
      }`}>
        <Card className={`shadow-lg bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 w-full flex flex-col ${
          isMobile ? 'h-full my-2' : 'flex-1 my-4'
        }`}>
          <CardHeader className="text-center p-4 sm:p-6 border-b dark:border-gray-700">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">{currentStepData.title}</h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">{currentStepData.description}</p>
            <Progress value={progress} className="w-full mt-4" />
          </CardHeader>

          <CardContent className={`p-4 sm:p-6 md:p-8 flex-1 ${
            isMobile ? 'overflow-hidden' : 'overflow-y-auto'
          }`}>
            {isMobile ? (
              <ScrollArea className="h-full max-h-[60vh]">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentStep}
                    initial={{ opacity: 0, x: isAnimating ? 50 : 0 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.2 }}
                    className="pb-4"
                  >
                    {renderStepContent()}
                  </motion.div>
                </AnimatePresence>
              </ScrollArea>
            ) : (
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: isAnimating ? 50 : 0 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.2 }}
                  className="h-full"
                >
                  {renderStepContent()}
                </motion.div>
              </AnimatePresence>
            )}
          </CardContent>

          <CardFooter className="flex justify-between items-center bg-gray-50 dark:bg-gray-800/50 p-4 border-t dark:border-gray-700 gap-2">
            <div className="flex-shrink-0">
              {currentStep > 0 && (
                <Button variant="ghost" onClick={prevStep} className="flex items-center gap-1 sm:gap-2 px-2 sm:px-4">
                  <ArrowLeft className="w-4 h-4" />
                  <span className="hidden sm:inline">Back</span>
                </Button>
              )}
            </div>
            <div className="flex items-center gap-2 sm:gap-4 flex-1 justify-end">
              <Button 
                variant="link" 
                onClick={async () => {
                  setIsSkipping(true)
                  try {
                    await onSkip()
                  } finally {
                    setIsSkipping(false)
                  }
                }} 
                disabled={isSkipping}
                className="text-xs sm:text-sm px-2 sm:px-4"
              >
                {isSkipping ? (
                  <div className="flex items-center gap-1 sm:gap-2">
                    <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin" />
                    <span className="hidden sm:inline">Creating...</span>
                  </div>
                ) : (
                  <span className="whitespace-nowrap">Skip</span>
                )}
              </Button>
              <Button 
                onClick={nextStep} 
                disabled={isAnimating} 
                className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 min-w-[80px] sm:min-w-[100px]"
              >
                <span>{currentStep === steps.length - 1 ? 'Finish' : 'Next'}</span>
                {isAnimating ? <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin" /> : <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4" />}
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
} 