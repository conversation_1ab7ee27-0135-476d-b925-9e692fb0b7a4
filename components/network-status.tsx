'use client'

import { useEffect, useState } from 'react'
import { WifiOff, Wifi, AlertCircle } from 'lucide-react'
import { getNetworkStatus, initNetworkMonitoring } from '@/lib/utils/network-fallback'
import { cn } from '@/lib/utils'

export function NetworkStatus() {
  const [status, setStatus] = useState({
    isOnline: true,
    canReachServer: true,
    canReachMpd: true,
    fallbackMode: false
  })

  useEffect(() => {
    // Initialize network monitoring
    initNetworkMonitoring()

    // Update status periodically
    const updateStatus = () => {
      const networkStatus = getNetworkStatus()
      setStatus({
        isOnline: networkStatus.isOnline,
        canReachServer: networkStatus.canReachServer,
        canReachMpd: networkStatus.canReachMpd,
        fallbackMode: networkStatus.fallbackMode
      })
    }

    updateStatus()
    const interval = setInterval(updateStatus, 5000)

    // Listen for online/offline events
    window.addEventListener('online', updateStatus)
    window.addEventListener('offline', updateStatus)

    return () => {
      clearInterval(interval)
      window.removeEventListener('online', updateStatus)
      window.removeEventListener('offline', updateStatus)
    }
  }, [])

  // Don't show anything if everything is working fine
  if (status.isOnline && status.canReachServer && status.canReachMpd && !status.fallbackMode) {
    return null
  }

  return (
    <div className={cn(
      "fixed bottom-4 right-4 p-3 rounded-lg shadow-lg flex items-center gap-2 text-sm z-50",
      !status.isOnline ? "bg-red-500 text-white" :
      status.fallbackMode ? "bg-yellow-500 text-white" :
      "bg-gray-700 text-white"
    )}>
      {!status.isOnline ? (
        <>
          <WifiOff className="h-4 w-4" />
          <span>No internet connection</span>
        </>
      ) : status.fallbackMode ? (
        <>
          <AlertCircle className="h-4 w-4" />
          <span>
            Limited connectivity - Using fallback mode
            {!status.canReachMpd && ' (MPD unavailable)'}
          </span>
        </>
      ) : (
        <>
          <Wifi className="h-4 w-4" />
          <span>Reconnecting...</span>
        </>
      )}
    </div>
  )
}