"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { CheckCircle, XCircle, AlertCircle, Play, Loader2 } from "lucide-react"
import { getMultiplayerSocket } from "@/lib/multiplayer-socket"
import { toast } from "sonner"

interface TestConfiguration {
  name: string
  description: string
  config: any
  expectedQuestions: number
  expectedCategories: string[]
  shouldHaveAudio: boolean | 'mixed'
  shouldFail?: boolean
}

interface TestResult {
  name: string
  passed: boolean
  errors: string[]
  actualQuestions: number
  actualCategories: string[]
  duration: number
}

const testConfigurations: TestConfiguration[] = [
  {
    name: "General Knowledge Only",
    description: "Only general knowledge questions, no music",
    config: {
      questionsPerRound: 3,
      categories: {
        classic: { enabled: false, weight: 0, difficulty: 3, rounds: 0 },
        quickFire: { enabled: false, weight: 0, rounds: 0 },
        audioTricks: { enabled: false, weight: 0, effectLevel: 3, rounds: 0 },
        albumArt: { enabled: false, weight: 0, visualLevel: 3, rounds: 0 },
        audioFingerprint: { enabled: false, weight: 0, expertLevel: 3, rounds: 0 },
        chartPosition: { enabled: false, weight: 0, eraFocus: 'all', rounds: 0 },
        decadeChallenge: { enabled: false, weight: 0, decades: ['1980s', '1990s'], rounds: 0 },
        genreSpecialist: { enabled: false, weight: 0, genres: ['Rock', 'Pop'], rounds: 0 },
        generalKnowledge: { enabled: true, weight: 100, categories: ['allgemein'], difficulty: 3, rounds: 2 }
      }
    },
    expectedQuestions: 6,
    expectedCategories: ['General Knowledge', 'Allgemeinwissen'],
    shouldHaveAudio: false
  },
  {
    name: "Music Only - Classic",
    description: "Only classic music questions",
    config: {
      questionsPerRound: 2,
      categories: {
        classic: { enabled: true, weight: 100, difficulty: 3, rounds: 3 },
        quickFire: { enabled: false, weight: 0, rounds: 0 },
        audioTricks: { enabled: false, weight: 0, effectLevel: 3, rounds: 0 },
        albumArt: { enabled: false, weight: 0, visualLevel: 3, rounds: 0 },
        audioFingerprint: { enabled: false, weight: 0, expertLevel: 3, rounds: 0 },
        chartPosition: { enabled: false, weight: 0, eraFocus: 'all', rounds: 0 },
        decadeChallenge: { enabled: false, weight: 0, decades: ['1980s', '1990s'], rounds: 0 },
        genreSpecialist: { enabled: false, weight: 0, genres: ['Rock', 'Pop'], rounds: 0 },
        generalKnowledge: { enabled: false, weight: 0, categories: [], difficulty: 3, rounds: 0 }
      }
    },
    expectedQuestions: 6,
    expectedCategories: ['Classic Quiz', 'Classic'],
    shouldHaveAudio: true
  },
  {
    name: "Mixed Configuration",
    description: "Mix of music and general knowledge",
    config: {
      questionsPerRound: 2,
      categories: {
        classic: { enabled: true, weight: 50, difficulty: 3, rounds: 1 },
        quickFire: { enabled: false, weight: 0, rounds: 0 },
        audioTricks: { enabled: false, weight: 0, effectLevel: 3, rounds: 0 },
        albumArt: { enabled: false, weight: 0, visualLevel: 3, rounds: 0 },
        audioFingerprint: { enabled: false, weight: 0, expertLevel: 3, rounds: 0 },
        chartPosition: { enabled: true, weight: 50, eraFocus: 'all', rounds: 1 },
        decadeChallenge: { enabled: false, weight: 0, decades: ['1980s', '1990s'], rounds: 0 },
        genreSpecialist: { enabled: false, weight: 0, genres: ['Rock', 'Pop'], rounds: 0 },
        generalKnowledge: { enabled: true, weight: 50, categories: ['allgemein'], difficulty: 3, rounds: 1 }
      }
    },
    expectedQuestions: 6,
    expectedCategories: ['Classic Quiz', 'Chart Position', 'General Knowledge', 'Allgemeinwissen'],
    shouldHaveAudio: 'mixed'
  },
  {
    name: "Empty Configuration",
    description: "No categories enabled - should fail",
    config: {
      questionsPerRound: 3,
      categories: {
        classic: { enabled: false, weight: 0, difficulty: 3, rounds: 0 },
        quickFire: { enabled: false, weight: 0, rounds: 0 },
        audioTricks: { enabled: false, weight: 0, effectLevel: 3, rounds: 0 },
        albumArt: { enabled: false, weight: 0, visualLevel: 3, rounds: 0 },
        audioFingerprint: { enabled: false, weight: 0, expertLevel: 3, rounds: 0 },
        chartPosition: { enabled: false, weight: 0, eraFocus: 'all', rounds: 0 },
        decadeChallenge: { enabled: false, weight: 0, decades: ['1980s', '1990s'], rounds: 0 },
        genreSpecialist: { enabled: false, weight: 0, genres: ['Rock', 'Pop'], rounds: 0 },
        generalKnowledge: { enabled: false, weight: 0, categories: [], difficulty: 3, rounds: 0 }
      }
    },
    expectedQuestions: 0,
    expectedCategories: [],
    shouldHaveAudio: false,
    shouldFail: true
  }
]

export function UltimoteConfigTester() {
  const [isRunning, setIsRunning] = useState(false)
  const [currentTest, setCurrentTest] = useState<string | null>(null)
  const [results, setResults] = useState<TestResult[]>([])

  const runTest = async (testConfig: TestConfiguration) => {
    return new Promise<TestResult>((resolve) => {
      const startTime = Date.now()
      const socket = getMultiplayerSocket()
      const result: TestResult = {
        name: testConfig.name,
        passed: true,
        errors: [],
        actualQuestions: 0,
        actualCategories: [],
        duration: 0
      }

      let gameId: string | null = null
      let questionsReceived: any[] = []
      let timeout: NodeJS.Timeout

      const cleanup = () => {
        clearTimeout(timeout)
        result.duration = Date.now() - startTime
        socket.off('game-state')
        socket.off('game-started')
        socket.off('question')
        socket.off('game-ended')
        resolve(result)
      }

      // Set timeout
      timeout = setTimeout(() => {
        if (!testConfig.shouldFail) {
          result.passed = false
          result.errors.push('Test timed out after 20 seconds')
        }
        cleanup()
      }, 20000)

      // Create game
      socket.emit('create-game', {
        hostName: `Tester_${Date.now()}`,
        gameMode: 'ultimote',
        settings: {
          totalQuestions: testConfig.expectedQuestions,
          timePerQuestion: 30,
          ultimoteConfig: testConfig.config
        }
      }, (response: any) => {
        if (response.success) {
          gameId = response.gameId
          
          // Add second player
          const socket2 = getMultiplayerSocket()
          socket2.emit('join-game', {
            gamePin: response.gamePin,
            playerName: 'TestPlayer2'
          }, (joinResponse: any) => {
            if (joinResponse.success) {
              // Start game
              setTimeout(() => {
                socket.emit('start-game', { gameId }, (startResponse: any) => {
                  if (!startResponse.success) {
                    if (testConfig.shouldFail) {
                      result.passed = true
                    } else {
                      result.passed = false
                      result.errors.push(`Failed to start: ${startResponse.error}`)
                    }
                    socket2.disconnect()
                    cleanup()
                  }
                })
              }, 500)
            } else {
              result.errors.push('Failed to join game')
              socket2.disconnect()
              cleanup()
            }
          })
        } else {
          result.passed = false
          result.errors.push('Failed to create game')
          cleanup()
        }
      })

      socket.on('game-started', (data: any) => {
        if (data.totalQuestions !== testConfig.expectedQuestions) {
          result.errors.push(`Expected ${testConfig.expectedQuestions} questions, got ${data.totalQuestions}`)
          result.passed = false
        }
      })

      socket.on('question', (data: any) => {
        questionsReceived.push(data.question)
        
        if (!result.actualCategories.includes(data.question.category)) {
          result.actualCategories.push(data.question.category)
        }
        
        // Validate category
        const validCategory = testConfig.expectedCategories.some(cat => 
          data.question.category === cat || 
          data.question.category.includes(cat) ||
          cat.includes(data.question.category)
        )
        
        if (!validCategory) {
          result.errors.push(`Unexpected category: ${data.question.category}`)
          result.passed = false
        }
        
        // Validate audio
        if (testConfig.shouldHaveAudio === true && !data.question.track) {
          result.errors.push(`Question ${data.questionNumber} should have audio`)
          result.passed = false
        } else if (testConfig.shouldHaveAudio === false && data.question.track) {
          result.errors.push(`Question ${data.questionNumber} shouldn't have audio`)
          result.passed = false
        }
      })

      socket.on('game-ended', () => {
        result.actualQuestions = questionsReceived.length
        
        if (result.actualQuestions !== testConfig.expectedQuestions) {
          result.errors.push(`Question count mismatch`)
          result.passed = false
        }
        
        cleanup()
      })
    })
  }

  const runAllTests = async () => {
    setIsRunning(true)
    setResults([])
    
    for (const testConfig of testConfigurations) {
      setCurrentTest(testConfig.name)
      try {
        const result = await runTest(testConfig)
        setResults(prev => [...prev, result])
      } catch (error) {
        setResults(prev => [...prev, {
          name: testConfig.name,
          passed: false,
          errors: [`Test error: ${error}`],
          actualQuestions: 0,
          actualCategories: [],
          duration: 0
        }])
      }
    }
    
    setCurrentTest(null)
    setIsRunning(false)
    
    const passed = results.filter(r => r.passed).length
    if (passed === testConfigurations.length) {
      toast.success('All tests passed!')
    } else {
      toast.error(`${testConfigurations.length - passed} tests failed`)
    }
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>ulTimote Configuration Tester</span>
          <Button
            onClick={runAllTests}
            disabled={isRunning}
            size="sm"
          >
            {isRunning ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Running Tests...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Run All Tests
              </>
            )}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Test configurations */}
          <div>
            <h3 className="font-semibold mb-2">Test Configurations</h3>
            <div className="grid gap-2">
              {testConfigurations.map((config, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    currentTest === config.name
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{config.name}</p>
                      <p className="text-sm text-gray-500">{config.description}</p>
                    </div>
                    {currentTest === config.name && (
                      <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                    )}
                    {results.find(r => r.name === config.name) && (
                      results.find(r => r.name === config.name)?.passed ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Results */}
          {results.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">Test Results</h3>
              <ScrollArea className="h-[400px] border rounded-lg p-4">
                <div className="space-y-4">
                  {results.map((result, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg ${
                        result.passed
                          ? 'bg-green-50 dark:bg-green-900/20 border-green-200'
                          : 'bg-red-50 dark:bg-red-900/20 border-red-200'
                      } border`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium flex items-center gap-2">
                          {result.passed ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                          {result.name}
                        </h4>
                        <Badge variant="outline" className="text-xs">
                          {result.duration}ms
                        </Badge>
                      </div>
                      
                      <div className="text-sm space-y-1">
                        <p>Questions: {result.actualQuestions}</p>
                        <p>Categories: {result.actualCategories.join(', ')}</p>
                        
                        {result.errors.length > 0 && (
                          <div className="mt-2">
                            <p className="font-medium text-red-600">Errors:</p>
                            <ul className="list-disc list-inside text-red-600">
                              {result.errors.map((error, i) => (
                                <li key={i}>{error}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
              
              {/* Summary */}
              <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center justify-between">
                  <p className="font-medium">Summary</p>
                  <div className="flex gap-4">
                    <span className="text-green-600">
                      Passed: {results.filter(r => r.passed).length}
                    </span>
                    <span className="text-red-600">
                      Failed: {results.filter(r => !r.passed).length}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}