'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Volume2, VolumeX, Radio, Copy } from 'lucide-react';

export const JukeboxStreamPlayer = () => {
  const [showPlayer, setShowPlayer] = useState(false);
  const streamUrl = `http://${typeof window !== 'undefined' ? window.location.hostname : '************'}:8000`;
  
  const copyUrl = () => {
    navigator.clipboard.writeText(streamUrl);
    alert('Stream URL copied to clipboard!');
  };
  
  return (
    <div className="bg-gray-900 text-white p-6 rounded-lg shadow-xl">
      <div className="flex items-center gap-2 mb-4">
        <Radio className="w-5 h-5" />
        <h3 className="text-lg font-semibold">Jukebox Live Stream</h3>
      </div>
      
      <div className="space-y-4">
        <Button
          onClick={() => setShowPlayer(!showPlayer)}
          variant="default"
          className="w-full"
        >
          {showPlayer ? (
            <>
              <VolumeX className="w-4 h-4 mr-2" />
              Hide Player
            </>
          ) : (
            <>
              <Volume2 className="w-4 h-4 mr-2" />
              Show Player
            </>
          )}
        </Button>
        
        {showPlayer && (
          <>
            <audio 
              controls 
              className="w-full"
              src={streamUrl}
              autoPlay
            >
              Your browser does not support audio streaming.
            </audio>
            
            <div className="text-xs text-gray-400 space-y-1">
              <p>• Use the browser controls to play/pause</p>
              <p>• If no sound, make sure jukebox is playing</p>
              <p>• Stream URL: {streamUrl}</p>
            </div>
          </>
        )}
        
        <Button
          onClick={copyUrl}
          variant="outline"
          size="sm"
          className="w-full"
        >
          <Copy className="w-4 h-4 mr-2" />
          Copy Stream URL for VLC
        </Button>
        
        <div className="text-xs text-gray-500 border-t border-gray-800 pt-3">
          <p className="font-semibold mb-1">Alternative Players:</p>
          <p>• VLC: Media → Open Network Stream</p>
          <p>• Browser: <a href={streamUrl} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">Direct Link</a></p>
          <p>• MPV: mpv {streamUrl}</p>
        </div>
      </div>
    </div>
  );
};