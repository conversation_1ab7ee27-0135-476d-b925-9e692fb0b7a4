"use client"

import { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { EnhancedAutoQueueService } from '@/lib/services/auto-queue-enhanced-service'
import type { AutoQueueNotification } from '@/lib/services/auto-queue-enhanced-service'
import { Music, Zap, Heart, TrendingUp, Shuffle } from 'lucide-react'

interface AutoQueueNotificationsProps {
  enabled?: boolean
  showToasts?: boolean
  onNotification?: (notification: AutoQueueNotification) => void
}

export function AutoQueueNotifications({ 
  enabled = true, 
  showToasts = true,
  onNotification 
}: AutoQueueNotificationsProps) {
  const [lastNotification, setLastNotification] = useState<AutoQueueNotification | null>(null)

  useEffect(() => {
    if (!enabled) return

    const handleNotification = (notification: AutoQueueNotification) => {
      setLastNotification(notification)
      
      // Call callback if provided
      if (onNotification) {
        onNotification(notification)
      }

      // Show toast notifications
      if (showToasts) {
        switch (notification.type) {
          case 'songs-added':
            if (notification.details?.songsAdded && notification.details.songsAdded > 0) {
              // Create custom toast with song details
              const icon = getSourceIcon(notification.details.source)
              const message = (
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {icon}
                  </div>
                  <div className="flex-1 space-y-1">
                    <p className="font-semibold">
                      {notification.details.songsAdded} tracks added from {notification.details.source}
                    </p>
                    {notification.details.reason && (
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {notification.details.reason}
                      </p>
                    )}
                    {notification.details.songs && notification.details.songs.length > 0 && (
                      <div className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                        <p className="font-medium mb-1">Including:</p>
                        <ul className="space-y-0.5">
                          {notification.details.songs.slice(0, 3).map((song, idx) => (
                            <li key={idx} className="truncate">
                              • {song.title} - {song.artist}
                            </li>
                          ))}
                          {notification.details.songs.length > 3 && (
                            <li className="text-gray-500">
                              • and {notification.details.songs.length - 3} more...
                            </li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )
              
              toast.success(message, {
                duration: 6000,
                className: 'auto-queue-notification'
              })
            } else if (notification.title === 'Auto Queue Started') {
              toast.info(notification.message, {
                icon: <Zap className="h-5 w-5 text-blue-500" />,
                duration: 4000
              })
            } else if (notification.title === 'Auto Queue Stopped') {
              toast.info(notification.message, {
                duration: 3000
              })
            }
            break

          case 'warning':
            toast.warning(notification.message, {
              description: notification.details?.reason,
              duration: 5000
            })
            break

          case 'error':
            toast.error(notification.message, {
              description: notification.details?.reason,
              duration: 5000
            })
            break
        }
      }
    }

    // Register listener
    EnhancedAutoQueueService.addNotificationListener(handleNotification)

    // Cleanup
    return () => {
      EnhancedAutoQueueService.removeNotificationListener(handleNotification)
    }
  }, [enabled, showToasts, onNotification])

  return null // This component doesn't render anything visible
}

function getSourceIcon(source?: string) {
  if (!source) return <Music className="h-5 w-5 text-gray-500" />
  
  const lowerSource = source.toLowerCase()
  
  if (lowerSource.includes('favorite')) {
    return <Heart className="h-5 w-5 text-red-500" />
  } else if (lowerSource.includes('ai') || lowerSource.includes('suggestion')) {
    return <Zap className="h-5 w-5 text-blue-500" />
  } else if (lowerSource.includes('popular')) {
    return <TrendingUp className="h-5 w-5 text-green-500" />
  } else if (lowerSource.includes('mixed')) {
    return <Shuffle className="h-5 w-5 text-purple-500" />
  }
  
  return <Music className="h-5 w-5 text-gray-500" />
}

// Export a hook for components that want to track notifications
export function useAutoQueueNotifications() {
  const [notifications, setNotifications] = useState<AutoQueueNotification[]>([])
  const [lastNotification, setLastNotification] = useState<AutoQueueNotification | null>(null)

  useEffect(() => {
    const handleNotification = (notification: AutoQueueNotification) => {
      setLastNotification(notification)
      setNotifications(prev => [...prev, notification].slice(-50)) // Keep last 50
    }

    EnhancedAutoQueueService.addNotificationListener(handleNotification)

    return () => {
      EnhancedAutoQueueService.removeNotificationListener(handleNotification)
    }
  }, [])

  return {
    notifications,
    lastNotification,
    clearNotifications: () => setNotifications([])
  }
}