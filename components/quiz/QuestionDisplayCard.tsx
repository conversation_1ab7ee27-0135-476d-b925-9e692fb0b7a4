"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>lider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { motion, AnimatePresence } from "framer-motion"
import { CheckCircle, XCircle, Clock, Target, Music, Brain } from "lucide-react"
import type { GameMode } from "@/lib/types"

interface QuestionDisplayCardProps {
  question: any
  gameMode: GameMode
  selectedAnswer: string | null
  sliderValue: number | null
  hasInteractedWithSlider: boolean
  showSliderPrompt: boolean
  isMultiplayer?: boolean
  isAnswered?: boolean
  showFeedback?: boolean
  correctAnswer?: string | number
  onSelectAnswer: (answer: string) => void
  onSliderChange: (value: number[]) => void
  onSliderInteraction: () => void
  onSubmitAnswer: () => void
  className?: string
}

export function QuestionDisplayCard({
  question,
  gameMode,
  selectedAnswer,
  sliderValue,
  hasInteractedWithSlider,
  showSliderPrompt,
  isMultiplayer = false,
  isAnswered = false,
  showFeedback = false,
  correctAnswer,
  onSelectAnswer,
  onSliderChange,
  onSliderInteraction,
  onSubmitAnswer,
  className = ""
}: QuestionDisplayCardProps) {
  const [animatePrompt, setAnimatePrompt] = useState(false)

  // Animate the slider prompt
  useEffect(() => {
    if (showSliderPrompt) {
      setAnimatePrompt(true)
      const timer = setTimeout(() => setAnimatePrompt(false), 1000)
      return () => clearTimeout(timer)
    }
  }, [showSliderPrompt])

  const getQuestionIcon = () => {
    if (question?.type === 'general-knowledge') return Brain
    return Music
  }

  const getQuestionTypeColor = () => {
    if (question?.type === 'general-knowledge') return 'from-blue-500 to-indigo-600'
    return 'from-purple-500 to-pink-600'
  }

  const isSliderMode = gameMode === "chart-position" || 
                      gameMode === "decade-challenge" || 
                      gameMode === "guess-the-year"

  const getSliderConfig = () => {
    switch (gameMode) {
      case "chart-position":
        return {
          min: 1,
          max: 100,
          default: 50,
          label: "Guess Peak Chart Position",
          unit: "#"
        }
      case "decade-challenge":
        return {
          min: 1950,
          max: 2020,
          default: 1980,
          label: "Guess Decade",
          unit: ""
        }
      case "guess-the-year":
        return {
          min: 1950,
          max: 2024,
          default: 1990,
          label: "Guess Release Year",
          unit: ""
        }
      default:
        return {
          min: 1,
          max: 100,
          default: 50,
          label: "Your Guess",
          unit: ""
        }
    }
  }

  const sliderConfig = getSliderConfig()
  const QuestionIcon = getQuestionIcon()

  const handleAnswerClick = (option: string, index: number) => {
    if (!isAnswered) {
      onSelectAnswer(index.toString())
    }
  }

  const getAnswerButtonStyle = (option: string, index: number) => {
    const isSelected = selectedAnswer === index.toString()
    const isCorrect = showFeedback && correctAnswer === index.toString()
    const isWrong = showFeedback && selectedAnswer === index.toString() && correctAnswer !== index.toString()

    if (showFeedback) {
      if (isCorrect) {
        return "bg-green-500/20 border-green-500/50 text-green-400 hover:bg-green-500/30"
      }
      if (isWrong) {
        return "bg-red-500/20 border-red-500/50 text-red-400 hover:bg-red-500/30"
      }
      return "bg-gray-500/10 border-gray-500/30 text-gray-400"
    }

    if (isSelected) {
      return "bg-blue-500/30 border-blue-500/50 text-blue-300 hover:bg-blue-500/40"
    }

    return "bg-white/10 dark:bg-white/5 border-white/20 dark:border-white/10 text-gray-900 dark:text-white hover:bg-white/20 dark:hover:bg-white/10"
  }

  return (
    <Card className={`backdrop-blur-md border border-white/20 dark:border-white/10 shadow-xl bg-white/10 dark:bg-white/5 ${className}`}>
      <CardContent className="p-6 sm:p-8">
        {/* Question Type Badge */}
        <div className="flex items-center justify-center mb-6">
          <Badge className={`bg-gradient-to-r ${getQuestionTypeColor()} text-white border-0 px-4 py-2`}>
            <QuestionIcon className="w-4 h-4 mr-2" />
            {question?.type === 'general-knowledge' ? 'General Knowledge' : 'Music Question'}
          </Badge>
        </div>

        {/* Question Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-8"
        >
          <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4 leading-tight">
            {question?.question || "What do you hear?"}
          </h2>
          
          {question?.category && (
            <Badge variant="outline" className="mb-4">
              {question.category}
            </Badge>
          )}
        </motion.div>

        {/* Answer Section */}
        <AnimatePresence mode="wait">
          {isSliderMode ? (
            /* Slider Interface */
            <motion.div
              key="slider"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-6"
            >
              {/* Interactive Prompt */}
              <AnimatePresence>
                {showSliderPrompt && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ 
                      opacity: 1, 
                      scale: animatePrompt ? [1, 1.05, 1] : 1 
                    }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.3 }}
                    className="text-center p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg"
                  >
                    <p className="text-yellow-400 font-medium">
                      💡 Use the slider to make your guess!
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Question instruction */}
              <div className="text-center">
                <Label htmlFor="value-slider" className="text-lg font-semibold text-gray-900 dark:text-white">
                  {sliderConfig.label}
                </Label>
              </div>

              {/* Enhanced Slider */}
              <div className="space-y-4">
                <Slider
                  id="value-slider"
                  value={sliderValue ? [sliderValue] : [sliderConfig.default]}
                  onValueChange={(value) => {
                    onSliderChange(value)
                    onSliderInteraction()
                  }}
                  max={sliderConfig.max}
                  min={sliderConfig.min}
                  step={1}
                  className="my-4"
                  disabled={isAnswered}
                />

                {/* Range indicators */}
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                  <span>{sliderConfig.min}{sliderConfig.unit}</span>
                  <span>{sliderConfig.max}{sliderConfig.unit}</span>
                </div>
              </div>

              {/* Dynamic Value Display */}
              <motion.div
                key={sliderValue}
                initial={{ scale: 1.1 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.2 }}
                className="text-center"
              >
                <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500/20 to-purple-600/20 border border-blue-500/30 rounded-full">
                  <Target className="w-5 h-5 text-blue-400" />
                  <span className="text-2xl font-bold text-gray-900 dark:text-white">
                    {sliderConfig.unit}{sliderValue || sliderConfig.default}
                  </span>
                </div>
              </motion.div>

              {/* Submit Button with Enhanced UX */}
              <div className="text-center">
                <Button
                  onClick={onSubmitAnswer}
                  disabled={!hasInteractedWithSlider || isAnswered}
                  className="w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed font-semibold text-lg"
                >
                  {isAnswered ? "Answer Submitted" : "Submit Guess"}
                </Button>
              </div>

              {/* Interaction Hint */}
              {!hasInteractedWithSlider && !isAnswered && (
                <p className="text-center text-sm text-gray-600 dark:text-gray-400">
                  Move the slider to set your answer
                </p>
              )}
            </motion.div>
          ) : (
            /* Multiple Choice Interface */
            <motion.div
              key="multiple-choice"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4"
            >
              {question?.options?.map((option: string, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={!isAnswered ? { scale: 1.02 } : {}}
                  whileTap={!isAnswered ? { scale: 0.98 } : {}}
                >
                  <Button
                    onClick={() => handleAnswerClick(option, index)}
                    disabled={isAnswered}
                    className={`w-full p-4 text-left justify-start transition-all duration-200 ${getAnswerButtonStyle(option, index)}`}
                  >
                    <div className="flex items-center gap-3">
                      {showFeedback && (
                        <span className="flex-shrink-0">
                          {correctAnswer === index.toString() ? (
                            <CheckCircle className="w-5 h-5 text-green-400" />
                          ) : selectedAnswer === index.toString() ? (
                            <XCircle className="w-5 h-5 text-red-400" />
                          ) : null}
                        </span>
                      )}
                      <span className="font-medium text-sm sm:text-base">
                        {option}
                      </span>
                    </div>
                  </Button>
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  )
} 