"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>lider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"
import { Play, Pause, Volume2, Settings, Wifi, WifiOff } from "lucide-react"
import type { AudioStatus } from "@/lib/audio-manager"

interface AudioPlayerControlsProps {
  audioStatus: AudioStatus
  volume: number
  audioError: string | null
  isPlaying: boolean
  showMixer: boolean
  onPlayPause: () => void
  onVolumeChange: (volume: number) => void
  onToggleMixer: () => void
  className?: string
}

export function AudioPlayerControls({
  audioStatus,
  volume,
  audioError,
  isPlaying,
  showMixer,
  onPlayPause,
  onVolumeChange,
  onToggleMixer,
  className = ""
}: AudioPlayerControlsProps) {
  const [localVolume, setLocalVolume] = useState(volume)

  // Sync local volume with parent when external changes occur
  useEffect(() => {
    setLocalVolume(volume)
  }, [volume])

  const handleVolumeChange = (newVolume: number[]) => {
    const vol = newVolume[0]
    setLocalVolume(vol)
    onVolumeChange(vol)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className={`bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl p-4 shadow-xl ${className}`}
    >
      {/* Audio Connection Status */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          {audioStatus.isConnected ? (
            <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
              <Wifi className="w-3 h-3 mr-1" />
              Connected
            </Badge>
          ) : (
            <Badge variant="destructive" className="bg-red-500/20 text-red-400 border-red-500/30">
              <WifiOff className="w-3 h-3 mr-1" />
              Disconnected
            </Badge>
          )}
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={onToggleMixer}
          className={`p-2 transition-colors ${
            showMixer 
              ? 'bg-blue-500/20 text-blue-400 border-blue-500/30' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <Settings className="w-4 h-4" />
        </Button>
      </div>

      {/* Audio Error Display */}
      {audioError && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg"
        >
          <p className="text-red-400 text-sm font-medium">
            Audio Error: {audioError}
          </p>
        </motion.div>
      )}

      {/* Enhanced Audio Player */}
      <div className="space-y-4">
        {/* Play/Pause Controls */}
        <div className="flex items-center justify-center">
          <Button
            size="lg"
            onClick={onPlayPause}
            disabled={!audioStatus.isConnected}
            className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isPlaying ? (
              <Pause className="w-6 h-6" />
            ) : (
              <Play className="w-6 h-6 ml-1" />
            )}
          </Button>
        </div>

        {/* Track Information */}
        {audioStatus.currentTrack && (
          <div className="text-center space-y-1">
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {audioStatus.currentTrack.title || 'Unknown Track'}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {audioStatus.currentTrack.artist || 'Unknown Artist'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-500">
              {audioStatus.position}
            </p>
          </div>
        )}

        {/* Volume Control */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Volume2 className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Volume
              </span>
            </div>
            <span className="text-sm font-bold text-gray-900 dark:text-white">
              {Math.round(localVolume)}%
            </span>
          </div>
          
          <Slider
            value={[localVolume]}
            onValueChange={handleVolumeChange}
            max={100}
            min={0}
            step={1}
            className="w-full"
            disabled={!audioStatus.isConnected}
          />
        </div>

        {/* Audio Progress Bar */}
        {audioStatus.duration > 0 && (
          <div className="space-y-1">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-1 rounded-full transition-all duration-1000"
                style={{
                  width: `${(audioStatus.elapsed / audioStatus.duration) * 100}%`,
                }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>{Math.floor(audioStatus.elapsed / 60)}:{(audioStatus.elapsed % 60).toString().padStart(2, '0')}</span>
              <span>{Math.floor(audioStatus.duration / 60)}:{(audioStatus.duration % 60).toString().padStart(2, '0')}</span>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  )
} 