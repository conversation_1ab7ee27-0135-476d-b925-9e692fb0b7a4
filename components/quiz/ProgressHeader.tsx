"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { motion } from "framer-motion"
import { ArrowLeft, Clock, Trophy, Users, Flame, Target } from "lucide-react"

interface ProgressHeaderProps {
  // Game info
  gameMode: string
  currentQuestion: number
  totalQuestions: number
  score: number
  streak: number
  timeLeft: number
  
  // UI state
  showBackButton?: boolean
  isMultiplayer?: boolean
  
  // Multiplayer info
  multiplayerGameId?: string | null
  playerName?: string | null
  leaderboard?: any[]
  waitingForPlayers?: boolean
  
  // Callbacks
  onBackToMenu: () => void
  
  className?: string
}

export function ProgressHeader({
  gameMode,
  currentQuestion,
  totalQuestions,
  score,
  streak,
  timeLeft,
  showBackButton = true,
  isMultiplayer = false,
  multiplayerGameId,
  playerName,
  leaderboard = [],
  waitingForPlayers = false,
  onBackToMenu,
  className = ""
}: ProgressHeaderProps) {
  const progress = ((currentQuestion + 1) / totalQuestions) * 100
  
  const getGameModeLabel = () => {
    const modes: Record<string, string> = {
      'classic': 'Classic Quiz',
      'chart-position': 'Chart Position',
      'guess-the-year': 'Guess the Year',
      'decade-challenge': 'Decade Challenge',
      'genre-specialist': 'Genre Expert',
      'ultimote': 'ulTimote',
      'quick-fire': 'Quick Fire',
      'audio-manipulation': 'Audio Tricks',
      'album-art': 'Album Art Quiz',
      'audio-fingerprint': 'Audio Fingerprint'
    }
    return modes[gameMode] || gameMode
  }

  const getTimerColor = () => {
    if (timeLeft <= 5) return 'text-red-500'
    if (timeLeft <= 10) return 'text-yellow-500'
    return 'text-green-500'
  }

  const getStreakBadge = () => {
    if (streak === 0) return null
    
    const getStreakColor = () => {
      if (streak >= 10) return 'bg-purple-500/20 text-purple-400 border-purple-500/30'
      if (streak >= 5) return 'bg-orange-500/20 text-orange-400 border-orange-500/30'
      return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
    }

    return (
      <Badge className={`${getStreakColor()} font-bold`}>
        <Flame className="w-3 h-3 mr-1" />
        {streak} streak
      </Badge>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`space-y-4 ${className}`}
    >
      {/* Header Row */}
      <div className="flex items-center justify-between">
        {/* Back Button */}
        {showBackButton && (
          <Button
            variant="ghost"
            onClick={onBackToMenu}
            className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            <ArrowLeft className="w-4 h-4" />
            <span className="hidden sm:inline">Back</span>
          </Button>
        )}

        {/* Game Mode Badge */}
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-white/10 border-white/20">
            {getGameModeLabel()}
          </Badge>
          
          {isMultiplayer && (
            <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
              <Users className="w-3 h-3 mr-1" />
              Multiplayer
            </Badge>
          )}
        </div>

        {/* Score & Streak */}
        <div className="flex items-center gap-2">
          {getStreakBadge()}
          <Badge className="bg-gradient-to-r from-blue-500/20 to-purple-600/20 text-white border-blue-500/30 font-bold">
            <Trophy className="w-3 h-3 mr-1" />
            {score.toLocaleString()}
          </Badge>
        </div>
      </div>

      {/* Progress Section */}
      <div className="space-y-3">
        {/* Question Progress */}
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">
            Question {currentQuestion + 1} of {totalQuestions}
          </span>
          
          {/* Timer */}
          <div className="flex items-center gap-2">
            <Clock className={`w-4 h-4 ${getTimerColor()}`} />
            <span className={`font-bold ${getTimerColor()}`}>
              {timeLeft}s
            </span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="relative">
          <Progress 
            value={progress} 
            className="h-2 bg-white/20"
          />
          {/* Timer overlay on progress bar */}
          <div 
            className="absolute top-0 left-0 h-2 bg-gradient-to-r from-yellow-400 to-red-500 rounded-full transition-all duration-1000"
            style={{
              width: `${Math.max(0, (timeLeft / 30) * 100)}%`
            }}
          />
        </div>
      </div>

      {/* Multiplayer Info */}
      {isMultiplayer && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-sm text-gray-300">
                {playerName || 'Player'} • Game #{multiplayerGameId?.slice(-6)}
              </span>
            </div>
            
            {waitingForPlayers ? (
              <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                Waiting for players...
              </Badge>
            ) : (
              <span className="text-xs text-gray-400">
                {leaderboard.length} players
              </span>
            )}
          </div>

          {/* Mini Leaderboard */}
          {leaderboard.length > 0 && !waitingForPlayers && (
            <div className="mt-2 space-y-1">
              {leaderboard.slice(0, 3).map((player, index) => (
                <div 
                  key={player.id || index}
                  className="flex items-center justify-between text-xs"
                >
                  <div className="flex items-center gap-2">
                    <span className={`
                      w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold
                      ${index === 0 ? 'bg-yellow-500 text-black' : 
                        index === 1 ? 'bg-gray-400 text-black' : 
                        'bg-amber-600 text-white'}
                    `}>
                      {index + 1}
                    </span>
                    <span className={`${player.name === playerName ? 'text-blue-400 font-bold' : 'text-gray-300'}`}>
                      {player.name}
                    </span>
                  </div>
                  <span className="text-gray-400">
                    {player.score?.toLocaleString() || 0}
                  </span>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      )}
    </motion.div>
  )
} 