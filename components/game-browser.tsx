"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Users, RefreshCw, Gamepad2, Clock } from "lucide-react"
import { toast } from 'sonner'
import { motion } from 'framer-motion'
import { getMultiplayerSocket } from '@/lib/multiplayer-socket'

interface AvailableGame {
  gameId: string
  pin: string
  hostName: string
  gameMode: string
  playerCount: number
  maxPlayers: number
}

interface GameBrowserProps {
  onJoinGame: (gamePin: string) => void
  onCreateGame: () => void
}

export function GameBrowser({ onJoinGame, onCreateGame }: GameBrowserProps) {
  const [availableGames, setAvailableGames] = useState<AvailableGame[]>([])
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [playerName, setPlayerName] = useState(localStorage.getItem('playerName') || '')
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())
  
  // Use the shared multiplayer socket
  const socket = getMultiplayerSocket()
  
  // Setup event listeners
  useEffect(() => {
    const handleConnect = () => {
      setIsConnected(true)
      console.log('[GameBrowser] Socket connected')
      // Fetch games on connect
      socket.emit('get-available-games', {})
    }
    
    const handleDisconnect = () => {
      setIsConnected(false)
      console.log('[GameBrowser] Socket disconnected')
    }
    
    const handleAvailableGames = (data: { games: AvailableGame[] }) => {
      setAvailableGames(data.games)
      console.log('[GameBrowser] Received games:', data.games)
    }
    
    const handleGameListUpdated = (newGame: AvailableGame) => {
      console.log('[GameBrowser] New game created:', newGame)
      // Refresh the games list when a new game is created
      socket.emit('get-available-games', {})
    }
    
    // Check initial connection state
    setIsConnected(socket.connected)
    
    socket.on('connect', handleConnect)
    socket.on('disconnect', handleDisconnect)
    socket.on('available-games', handleAvailableGames)
    socket.on('game-list-updated', handleGameListUpdated)
    
    return () => {
      socket.off('connect', handleConnect)
      socket.off('disconnect', handleDisconnect)
      socket.off('available-games', handleAvailableGames)
      socket.off('game-list-updated', handleGameListUpdated)
    }
  }, [])

  // Auto-refresh games
  useEffect(() => {
    if (!isConnected) return

    const fetchGames = () => {
      socket.emit('get-available-games', {})
      setLastRefresh(new Date())
    }

    fetchGames() // Initial fetch
    const interval = setInterval(fetchGames, 5000) // Refresh every 5 seconds

    return () => clearInterval(interval)
  }, [isConnected, socket])
  
  const refreshGames = () => {
    if (!isConnected) return
    setIsRefreshing(true)
    socket.emit('get-available-games', {})
    setTimeout(() => setIsRefreshing(false), 500)
  }
  
  const formatGameMode = (mode: string) => {
    return mode.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const handleJoinGame = (gamePin: string) => {
    if (!playerName.trim()) {
      toast.error('Please enter your name first')
      return
    }
    localStorage.setItem('playerName', playerName)
    onJoinGame(gamePin)
  }

  return (
    <div className="space-y-4">
      {/* Player Name Input */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="backdrop-blur-lg bg-white/80 dark:bg-gray-900/80 border-white/20 shadow-xl">
          <CardContent className="pt-6">
            <div className="space-y-2">
              <Label htmlFor="player-name" className="text-sm font-medium">Your Name</Label>
              <Input
              id="player-name"
              value={playerName}
              onChange={(e) => {
                setPlayerName(e.target.value)
                localStorage.setItem('playerName', e.target.value)
              }}
              placeholder="Enter your name"
            />
          </div>
        </CardContent>
      </Card>
      </motion.div>

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Available Games</h2>
          <p className="text-sm text-gray-500 mt-1">
            <Clock className="inline h-3 w-3 mr-1" />
            Last updated: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={refreshGames}
            variant="outline"
            size="sm"
            disabled={!isConnected || isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button 
            onClick={onCreateGame} 
            size="sm"
            className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-200"
          >
            <Gamepad2 className="h-4 w-4 mr-2" />
            Create Game
          </Button>
        </div>
      </div>

      {/* Games List */}
      {!isConnected ? (
        <Card>
          <CardContent className="py-8 text-center text-gray-500">
            Connecting to game server...
          </CardContent>
        </Card>
      ) : availableGames.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center text-gray-500">
            No games available. Create one to get started!
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-3">
          {availableGames.map((game, index) => (
            <motion.div
              key={game.gameId}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="backdrop-blur-lg bg-white/80 dark:bg-gray-900/80 border-white/20 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-[1.02]">
                <CardContent className="flex items-center justify-between p-4">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <p className="font-semibold">{game.hostName}'s Game</p>
                    <Badge variant="secondary" className="text-xs">
                      {formatGameMode(game.gameMode)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span className="font-mono">PIN: {game.pin}</span>
                    <span className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {game.playerCount}/{game.maxPlayers}
                    </span>
                  </div>
                </div>
                <Button
                  onClick={() => handleJoinGame(game.pin)}
                  disabled={game.playerCount >= game.maxPlayers || !playerName.trim()}
                >
                  {!playerName.trim() ? 'Enter Name' : 'Join'}
                </Button>
              </CardContent>
            </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  )
}