"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Clock, Music, Users, Heart, AlertCircle, Trophy, ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import type { TimelineEntry, TimelineSong, Team, HitsterGameState } from "@/lib/types"

interface HitsterTimelineProps {
  gameState: HitsterGameState
  currentTeam: Team | null
  teams: Team[]
  onPlaceSong: (position: number) => void
  isMyTurn: boolean
}

export function HitsterTimeline({ 
  gameState, 
  currentTeam,
  teams,
  onPlaceSong,
  isMyTurn
}: HitsterTimelineProps) {
  const [selectedPosition, setSelectedPosition] = useState<number | null>(null)
  const [hoveredPosition, setHoveredPosition] = useState<number | null>(null)

  // Get team color based on team index
  const getTeamColor = (teamId: string) => {
    const teamIndex = teams.findIndex(t => t.id === teamId)
    const colors = ["text-red-400", "text-blue-400", "text-green-400", "text-yellow-400"]
    return colors[teamIndex % colors.length]
  }

  // Get team emoji based on team index
  const getTeamEmoji = (teamId: string) => {
    const teamIndex = teams.findIndex(t => t.id === teamId)
    const emojis = ["🔴", "🔵", "🟢", "🟡"]
    return emojis[teamIndex % emojis.length]
  }

  const renderTimelineEntry = (entry: TimelineEntry, index: number) => {
    const teamColor = getTeamColor(entry.placedByTeamId)
    const teamEmoji = getTeamEmoji(entry.placedByTeamId)
    
    return (
      <motion.div
        key={entry.id}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        transition={{ duration: 0.3 }}
        className={cn(
          "relative bg-white/10 dark:bg-white/5 backdrop-blur-sm rounded-lg p-4",
          "border-2 transition-colors",
          entry.isCorrect === false ? "border-red-500/50" : "border-gray-300/30 dark:border-gray-700/30"
        )}
      >
        {/* Year indicator */}
        <div className="absolute -left-16 top-1/2 -translate-y-1/2 text-2xl font-bold text-gray-400">
          {gameState.revealPhase || !gameState.placementPhase ? entry.song.year : "????"}
        </div>

        {/* Song info */}
        <div className="space-y-2">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className="font-medium text-gray-900 dark:text-white">
                {entry.song.title}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {entry.song.artist}
              </p>
              {entry.song.genre && (
                <Badge variant="outline" className="mt-1 text-xs">
                  {entry.song.genre}
                </Badge>
              )}
            </div>
            <div className="text-right">
              <span className={cn("text-lg", teamColor)}>{teamEmoji}</span>
              <p className="text-xs text-gray-500 mt-1">
                {entry.placedByPlayerName}
              </p>
            </div>
          </div>

          {/* Placement result */}
          {entry.isCorrect !== undefined && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className={cn(
                "text-sm font-medium",
                entry.isCorrect ? "text-green-400" : "text-red-400"
              )}
            >
              {entry.isCorrect ? "✓ Correct placement!" : "✗ Wrong position"}
            </motion.div>
          )}
        </div>
      </motion.div>
    )
  }

  const renderPlacementButton = (position: number) => {
    const isHovered = hoveredPosition === position
    const isSelected = selectedPosition === position
    
    return (
      <motion.div
        key={`placement-${position}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="relative h-16 flex items-center justify-center"
      >
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "w-full h-8 border-2 border-dashed transition-all",
            isHovered && "border-indigo-400 bg-indigo-400/10",
            isSelected && "border-indigo-500 bg-indigo-500/20",
            !isHovered && !isSelected && "border-gray-400/30"
          )}
          onMouseEnter={() => setHoveredPosition(position)}
          onMouseLeave={() => setHoveredPosition(null)}
          onClick={() => {
            setSelectedPosition(position)
            onPlaceSong(position)
          }}
          disabled={!isMyTurn || !gameState.placementPhase}
        >
          {(isHovered || isSelected) && (
            <span className="text-xs text-indigo-400">
              Place here
            </span>
          )}
        </Button>
      </motion.div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Current song to place */}
      {gameState.currentSong && gameState.placementPhase && (
        <Card className="bg-indigo-500/10 border-indigo-500/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-white flex items-center">
              <Music className="h-5 w-5 mr-2" />
              Now Playing
            </h3>
            {isMyTurn && (
              <Badge className="bg-indigo-500/20 text-indigo-400 border-indigo-500/30">
                Your Turn
              </Badge>
            )}
          </div>
          
          <div className="space-y-2">
            <h4 className="text-xl font-bold text-white">
              {gameState.currentSong.title}
            </h4>
            <p className="text-gray-300">
              {gameState.currentSong.artist}
            </p>
            {gameState.currentSong.decade && (
              <Badge variant="outline" className="text-gray-400">
                {gameState.currentSong.decade}
              </Badge>
            )}
          </div>

          {isMyTurn && (
            <div className="mt-4 p-3 bg-white/5 rounded-lg">
              <p className="text-sm text-gray-300">
                🎯 Place this song in the correct chronological position on the timeline below
              </p>
            </div>
          )}
        </Card>
      )}

      {/* Timeline */}
      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-400/30" />
        
        {/* Timeline entries */}
        <div className="space-y-4 pl-24">
          {/* Start placement button */}
          {gameState.placementPhase && isMyTurn && renderPlacementButton(0)}
          
          {/* Existing timeline entries */}
          <AnimatePresence mode="popLayout">
            {gameState.timeline.map((entry, index) => (
              <div key={entry.id}>
                {renderTimelineEntry(entry, index)}
                {/* Placement button between entries */}
                {gameState.placementPhase && isMyTurn && renderPlacementButton(index + 1)}
              </div>
            ))}
          </AnimatePresence>
        </div>
      </div>

      {/* Team tokens */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {teams.map((team, index) => {
          const tokens = gameState.tokensPerTeam.get(team.id) || 0
          const teamColor = getTeamColor(team.id)
          const teamEmoji = getTeamEmoji(team.id)
          const isCurrentTeam = gameState.teamTurns[gameState.currentTeamIndex] === team.id
          
          return (
            <Card
              key={team.id}
              className={cn(
                "p-4 transition-all",
                isCurrentTeam && "ring-2 ring-indigo-500 bg-indigo-500/5"
              )}
            >
              <div className="flex items-center justify-between mb-2">
                <span className={cn("text-lg", teamColor)}>{teamEmoji}</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {team.name}
                </span>
              </div>
              <div className="flex items-center gap-1">
                {Array.from({ length: 3 }).map((_, i) => (
                  <Heart
                    key={i}
                    className={cn(
                      "h-4 w-4 transition-colors",
                      i < tokens ? "fill-red-500 text-red-500" : "text-gray-400"
                    )}
                  />
                ))}
              </div>
              {isCurrentTeam && (
                <Badge className="mt-2 w-full justify-center bg-indigo-500/20 text-indigo-400">
                  Playing
                </Badge>
              )}
            </Card>
          )
        })}
      </div>

      {/* Game stats */}
      <div className="flex items-center justify-between text-sm text-gray-500">
        <div className="flex items-center gap-4">
          <span>Round {gameState.roundNumber}</span>
          <span>•</span>
          <span>{gameState.timeline.length} / {15} songs</span>
        </div>
        <div className="flex items-center gap-4">
          <span>{gameState.correctPlacements} correct</span>
          <span>•</span>
          <span>{Math.round((gameState.correctPlacements / Math.max(1, gameState.totalPlacements)) * 100)}% accuracy</span>
        </div>
      </div>
    </div>
  )
}