"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  ThumbsUp,
  CheckCircle,
  X,
  Heart,
  Loader2,
  RefreshCw,
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { AlbumArt } from './album-art'
import type { SongSuggestion } from "@/lib/types"
import { useState, useCallback } from "react"

interface SuggestionsTabProps {
  suggestions: SongSuggestion[]
  isAdmin: () => boolean
  handleSuggestionVote: (suggestionId: string) => Promise<void>
  handleSuggestionApprove: (suggestionId: string) => Promise<void>
  handleSuggestionReject: (suggestionId: string) => Promise<void>
  onRefresh?: () => Promise<void>
  isRefreshing?: boolean
}

// Animation variants for staggered card entry
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
}

const cardVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 120,
      damping: 14,
      duration: 0.3,
    },
  },
}

export function SuggestionsTab({
  suggestions,
  isAdmin,
  handleSuggestionVote,
  handleSuggestionApprove,
  handleSuggestionReject,
  onRefresh,
  isRefreshing = false
}: SuggestionsTabProps) {
  // console.log('[SuggestionsTab] Received suggestions:', suggestions)
  // Track loading states for each suggestion action
  const [loadingStates, setLoadingStates] = useState<{
    [suggestionId: string]: {
      voting?: boolean
      approving?: boolean
      rejecting?: boolean
    }
  }>({})

  // Helper function to set loading state
  const setLoading = useCallback((suggestionId: string, action: 'voting' | 'approving' | 'rejecting', loading: boolean) => {
    setLoadingStates(prev => ({
      ...prev,
      [suggestionId]: {
        ...prev[suggestionId],
        [action]: loading
      }
    }))
  }, [])

  // Wrapper functions with loading states
  const handleVoteWithLoading = useCallback(async (suggestionId: string) => {
    setLoading(suggestionId, 'voting', true)
    try {
      await handleSuggestionVote(suggestionId)
    } finally {
      setLoading(suggestionId, 'voting', false)
    }
  }, [handleSuggestionVote, setLoading])

  const handleApproveWithLoading = useCallback(async (suggestionId: string) => {
    setLoading(suggestionId, 'approving', true)
    try {
      await handleSuggestionApprove(suggestionId)
    } finally {
      setLoading(suggestionId, 'approving', false)
    }
  }, [handleSuggestionApprove, setLoading])

  const handleRejectWithLoading = useCallback(async (suggestionId: string) => {
    setLoading(suggestionId, 'rejecting', true)
    try {
      await handleSuggestionReject(suggestionId)
    } finally {
      setLoading(suggestionId, 'rejecting', false)
    }
  }, [handleSuggestionReject, setLoading])

  return (
    <Card className="bg-white/10 backdrop-blur-md border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between gap-2">
          <div className="flex-1 min-w-0">
            <CardTitle>Song Suggestions</CardTitle>
            <CardDescription>Songs suggested by users. Admins can approve or reject them.</CardDescription>
          </div>
          {onRefresh && (
            <Button 
              onClick={onRefresh} 
              variant="ghost" 
              size="icon" 
              disabled={isRefreshing}
              className="h-8 w-8 sm:h-9 sm:w-9 flex-shrink-0"
            >
              <RefreshCw className={`w-3 h-3 sm:w-4 sm:h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[50vh] sm:h-[60vh] max-h-[400px] sm:max-h-[500px]">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {suggestions.length > 0 ? suggestions.map(song => {
              const isVoting = loadingStates[song.id]?.voting || false
              const isApproving = loadingStates[song.id]?.approving || false
              const isRejecting = loadingStates[song.id]?.rejecting || false
              
              return (
                <motion.div
                  key={song.id}
                  variants={cardVariants}
                  whileHover={{ y: -2, scale: 1.01 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20, duration: 0.2 }}
                >
                  <Card className="p-4 sm:p-3 bg-black/10 mb-3 sm:mb-2">
                    {/* Mobile-responsive layout */}
                    <div className="space-y-4 sm:space-y-3">
                      {/* Top section: Album art and song info */}
                      <div className="flex items-start gap-4 sm:gap-3">
                        {/* Album Art */}
                        <AlbumArt
                          trackId={song.id}
                          fallbackUrl={song.albumArtUrl}
                          title={song.title}
                          artist={song.artist || 'Unknown Artist'}
                          album={song.album || 'Unknown Album'}
                          size="sm"
                          className="flex-shrink-0"
                        />
                        
                        {/* Song Info */}
                        <div className="min-w-0 flex-1">
                          <h4 className="font-semibold text-sm sm:text-base truncate">{song.title}</h4>
                          <p className="text-xs sm:text-sm text-gray-300 truncate">{song.artist}</p>
                          <p className="text-xs text-gray-400">Suggested by: {song.suggestedBy}</p>
                          {song.album && <p className="text-xs text-gray-500 truncate">{song.album}</p>}
                        </div>
                        
                        {/* Vote Count - positioned top right */}
                        <div className="text-xs text-gray-400 bg-black/20 px-2 py-1 rounded flex-shrink-0">
                          {song.votes || 0} votes
                        </div>
                      </div>
                      
                      {/* Bottom section: Action buttons - responsive layout */}
                      <div className="flex flex-col sm:flex-row gap-2 sm:gap-1 sm:justify-end">
                        {/* Vote Button (for all users including admins) */}
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleVoteWithLoading(song.id)}
                          disabled={isVoting}
                          className="text-blue-400 hover:text-blue-300 w-full sm:w-auto min-h-[40px] sm:min-h-[36px]"
                        >
                          {isVoting ? (
                            <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                          ) : (
                            <ThumbsUp className="w-4 h-4 mr-1" />
                          )}
                          {isVoting ? 'Voting...' : 'Vote'}
                        </Button>
                        
                        {/* Admin Controls */}
                        {isAdmin() && (
                          <div className="flex gap-2 sm:gap-1">
                            <Button 
                              onClick={() => handleApproveWithLoading(song.id)} 
                              variant="ghost" 
                              size="sm" 
                              disabled={isApproving}
                              className="text-green-400 hover:text-green-300 flex-1 sm:flex-none min-h-[40px] sm:min-h-[36px]"
                            >
                              {isApproving ? (
                                <Loader2 className="w-4 h-4 sm:mr-1 animate-spin" />
                              ) : (
                                <CheckCircle className="w-4 h-4 sm:mr-1" />
                              )}
                              <span className="hidden sm:inline">
                                {isApproving ? 'Approving...' : 'Approve'}
                              </span>
                            </Button>
                            <Button 
                              onClick={() => handleRejectWithLoading(song.id)} 
                              variant="ghost" 
                              size="sm" 
                              disabled={isRejecting}
                              className="text-red-400 hover:text-red-300 flex-1 sm:flex-none min-h-[40px] sm:min-h-[36px]"
                            >
                              {isRejecting ? (
                                <Loader2 className="w-4 h-4 sm:mr-1 animate-spin" />
                              ) : (
                                <X className="w-4 h-4 sm:mr-1" />
                              )}
                              <span className="hidden sm:inline">
                                {isRejecting ? 'Rejecting...' : 'Reject'}
                              </span>
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                </motion.div>
              )
            }) : (
              <motion.div 
                variants={cardVariants}
                className="text-center py-10 text-gray-500"
              >
                <Heart className="mx-auto w-12 h-12 mb-2" />
                No suggestions yet.
              </motion.div>
            )}
          </motion.div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}