"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, User, Lock, LogIn } from "lucide-react"
import { useUser } from "@/lib/user-context"

interface LoginFormProps {
  onSuccess?: () => void
  onCancel?: () => void
  onSwitchToRegister?: () => void
}

export function LoginForm({ onSuccess, onCancel, onSwitchToRegister }: LoginFormProps) {
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  
  const { login } = useUser()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    try {
      // The API now handles username/email/displayName lookup
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
      })

      const data = await response.json()
      
      if (data.success) {
        console.log('[LoginForm] Login successful')
        await login(data.user.email, password) // Update user context
        setTimeout(() => {
          onSuccess?.()
        }, 100)
      } else {
        setError(data.message || "Invalid username or password")
      }
    } catch (error) {
      setError("Login failed. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }


  return (
    <Card className="w-full max-w-md mx-auto shadow-lg bg-card/95 backdrop-blur">
      <CardHeader className="text-center pb-4 sm:pb-6">
        <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-3 sm:mb-4">
          <LogIn className="w-7 h-7 sm:w-8 sm:h-8 text-white" />
        </div>
        <CardTitle className="text-xl sm:text-2xl">Welcome Back!</CardTitle>
        <p className="text-sm sm:text-base text-muted-foreground mt-2">
          Sign in with your username or display name
        </p>
      </CardHeader>
      <CardContent className="space-y-4 sm:space-y-6 px-4 sm:px-6">
        <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-4">
          <div className="space-y-1.5 sm:space-y-2">
            <Label htmlFor="username" className="text-sm sm:text-base">Username</Label>
            <div className="relative">
              <User className="absolute left-3 top-2.5 sm:top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="username"
                type="text"
                placeholder="Enter your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="pl-10 h-10 sm:h-11 text-sm sm:text-base"
                required
                autoComplete="username"
                autoCapitalize="none"
              />
            </div>
          </div>

          <div className="space-y-1.5 sm:space-y-2">
            <Label htmlFor="password" className="text-sm sm:text-base">Password</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-2.5 sm:top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="password"
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="pl-10 h-10 sm:h-11 text-sm sm:text-base"
                required
                autoComplete="current-password"
              />
            </div>
          </div>

          {error && (
            <Alert variant="destructive" className="text-sm">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col sm:flex-row gap-2">
            <Button 
              type="submit" 
              disabled={isLoading || !username || !password}
              className="flex-1 h-10 sm:h-11 text-sm sm:text-base"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : (
                <>
                  <LogIn className="mr-2 h-4 w-4" />
                  Sign In
                </>
              )}
            </Button>
            {onCancel && (
              <Button 
                type="button" 
                variant="outline" 
                onClick={onCancel}
                className="h-10 sm:h-11 text-sm sm:text-base"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>

        <div className="text-center space-y-2 pt-4 border-t">
          <Button
            type="button"
            variant="ghost"
            onClick={onSwitchToRegister}
            className="w-full h-9 sm:h-10 text-sm"
          >
            Don&apos;t have an account? Register here
          </Button>
          <p className="text-xs sm:text-sm text-muted-foreground">
            Or use &quot;Continue as Guest&quot; to explore without an account
          </p>
        </div>
      </CardContent>
    </Card>
  )
}