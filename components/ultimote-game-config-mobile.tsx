/**
 * Mobile-optimized ulTimote Game Configuration
 * Redesigned for touch-friendly interaction and small screens
 */

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from 'sonner'
import { 
  Star, 
  Settings, 
  Zap, 
  Music, 
  Headphones,
  ImageIcon,
  Search,
  Trophy,
  Crown,
  Brain,
  ChevronRight,
  ArrowLeft,
  Check,
  Plus,
  Minus,
  Sparkles,
  Play,
  Calendar,
  Radio
} from "lucide-react"
import type { UlTimoteGameConfig } from "./ultimote-game-config"

interface UlTimoteGameConfigMobileProps {
  onStartGame: (config: UlTimoteGameConfig) => void
  onBackToMenu: () => void
}

// Available options for category-specific selections
const AVAILABLE_DECADES = [
  '1950s', '1960s', '1970s', '1980s', '1990s', '2000s', '2010s', '2020s'
]

const AVAILABLE_GENRES = [
  'Rock', 'Pop', 'Hip-Hop', 'Electronic', 'Jazz', 'Classical', 'Country', 
  'R&B', 'Reggae', 'Blues', 'Folk', 'Punk', 'Metal', 'Indie', 'Alternative'
]

// Mobile-friendly preset configurations
const MOBILE_PRESETS = {
  'Quick Play': {
    name: 'Quick Play',
    description: 'Fast 15-minute game',
    icon: Zap,
    color: 'text-yellow-400',
    config: {
      totalRounds: 4,
      questionsPerRound: 5,
      categories: {
        classic: { enabled: true, weight: 40, difficulty: 3 },
        quickFire: { enabled: true, weight: 30, rounds: 2 },
        albumArt: { enabled: true, weight: 30, visualLevel: 2 },
        audioTricks: { enabled: false, weight: 0, effectLevel: 1 },
        audioFingerprint: { enabled: false, weight: 0, expertLevel: 1 },
        chartPosition: { enabled: false, weight: 0, eraFocus: 'all' },
        decadeChallenge: { enabled: false, weight: 0, decades: [] },
        genreSpecialist: { enabled: false, weight: 0, genres: [] },
        generalKnowledge: { enabled: false, weight: 0, categories: [], difficulty: 1 }
      }
    }
  },
  'Music Expert': {
    name: 'Music Expert',
    description: 'Challenge your music knowledge',
    icon: Headphones,
    color: 'text-purple-400',
    config: {
      totalRounds: 5,
      questionsPerRound: 5,
      categories: {
        classic: { enabled: true, weight: 20, difficulty: 4 },
        quickFire: { enabled: false, weight: 0, rounds: 0 },
        albumArt: { enabled: true, weight: 25, visualLevel: 4 },
        audioTricks: { enabled: true, weight: 25, effectLevel: 4 },
        audioFingerprint: { enabled: true, weight: 30, expertLevel: 4 },
        chartPosition: { enabled: false, weight: 0, eraFocus: 'all' },
        decadeChallenge: { enabled: false, weight: 0, decades: [] },
        genreSpecialist: { enabled: false, weight: 0, genres: [] },
        generalKnowledge: { enabled: false, weight: 0, categories: [], difficulty: 1 }
      }
    }
  },
  'Mixed Quiz': {
    name: 'Mixed Quiz',
    description: 'Music & general knowledge',
    icon: Brain,
    color: 'text-cyan-400',
    config: {
      totalRounds: 6,
      questionsPerRound: 5,
      categories: {
        classic: { enabled: true, weight: 25, difficulty: 3 },
        quickFire: { enabled: true, weight: 15, rounds: 2 },
        albumArt: { enabled: true, weight: 15, visualLevel: 3 },
        audioTricks: { enabled: false, weight: 0, effectLevel: 1 },
        audioFingerprint: { enabled: false, weight: 0, expertLevel: 1 },
        chartPosition: { enabled: true, weight: 10, eraFocus: 'all' },
        decadeChallenge: { enabled: false, weight: 0, decades: [] },
        genreSpecialist: { enabled: false, weight: 0, genres: [] },
        generalKnowledge: { enabled: true, weight: 35, categories: ['funny', 'science', 'film'], difficulty: 3 }
      }
    }
  }
}

// Mobile-friendly category metadata
const MOBILE_CATEGORIES = [
  { key: 'classic', name: 'Classic Quiz', icon: Music, color: 'text-blue-400' },
  { key: 'quickFire', name: 'Quick Fire', icon: Zap, color: 'text-yellow-400' },
  { key: 'audioTricks', name: 'Audio Tricks', icon: Headphones, color: 'text-purple-400' },
  { key: 'albumArt', name: 'Album Art', icon: ImageIcon, color: 'text-rose-400' },
  { key: 'audioFingerprint', name: 'Audio Expert', icon: Search, color: 'text-green-400' },
  { key: 'chartPosition', name: 'Chart Position', icon: Trophy, color: 'text-amber-400' },
  { key: 'decadeChallenge', name: 'Guess the Year', icon: Calendar, color: 'text-indigo-400' },
  { key: 'genreSpecialist', name: 'Genre Specialist', icon: Radio, color: 'text-pink-400' },
  { key: 'generalKnowledge', name: 'General Knowledge', icon: Brain, color: 'text-cyan-400' }
]

export function UlTimoteGameConfigMobile({ onStartGame, onBackToMenu }: UlTimoteGameConfigMobileProps) {
  const [step, setStep] = useState(0) // 0: Presets, 1: Customize, 2: Review
  const [selectedPreset, setSelectedPreset] = useState<string>('')
  const [config, setConfig] = useState<UlTimoteGameConfig>(MOBILE_PRESETS['Quick Play'].config as any)
  
  // Initialize with default config
  useEffect(() => {
    setConfig({
      ...MOBILE_PRESETS['Quick Play'].config,
      gameFlow: {
        adaptiveDifficulty: true,
        streakBonuses: true,
        timeAttack: false,
        eliminationRounds: false,
        finalBoss: false
      },
      scoring: {
        basePoints: 100,
        perfectMultiplier: 2.0,
        speedBonuses: true,
        categoryBonuses: true,
        streakSystem: 'linear'
      },
      specialModes: {
        blindMode: false,
        masterClass: false,
        tournamentMode: false,
        endlessMode: false
      },
      musicFilters: {
        genres: [],
        decades: [],
        artists: [],
        minPopularity: 20,
        excludeCommon: false
      },
      contentCategories: {
        quizCategories: [],
        thematicTags: [],
        specialLists: []
      },
      gameName: 'Quick Play',
      description: 'Mobile-optimized quiz',
      estimatedDuration: 15,
      difficultyRating: 3
    } as UlTimoteGameConfig)
  }, [])

  const selectPreset = (presetKey: string) => {
    const preset = MOBILE_PRESETS[presetKey as keyof typeof MOBILE_PRESETS]
    if (preset) {
      setSelectedPreset(presetKey)
      setConfig({
        ...preset.config,
        gameFlow: {
          adaptiveDifficulty: true,
          streakBonuses: true,
          timeAttack: presetKey === 'Quick Play',
          eliminationRounds: false,
          finalBoss: false
        },
        scoring: {
          basePoints: 100,
          perfectMultiplier: 2.0,
          speedBonuses: true,
          categoryBonuses: true,
          streakSystem: 'linear'
        },
        specialModes: {
          blindMode: false,
          masterClass: presetKey === 'Music Expert',
          tournamentMode: false,
          endlessMode: false
        },
        musicFilters: {
          genres: [],
          decades: [],
          artists: [],
          minPopularity: 20,
          excludeCommon: false
        },
        contentCategories: {
          quizCategories: [],
          thematicTags: [],
          specialLists: []
        },
        gameName: preset.name,
        description: preset.description,
        estimatedDuration: preset.config.totalRounds * 5,
        difficultyRating: 3
      } as UlTimoteGameConfig)
    }
  }

  const toggleCategory = (categoryKey: string) => {
    setConfig(prev => ({
      ...prev,
      categories: {
        ...prev.categories,
        [categoryKey]: {
          ...prev.categories[categoryKey as keyof typeof prev.categories],
          enabled: !prev.categories[categoryKey as keyof typeof prev.categories].enabled
        }
      }
    }))
  }

  const updateCategory = (categoryKey: string, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      categories: {
        ...prev.categories,
        [categoryKey]: {
          ...prev.categories[categoryKey as keyof typeof prev.categories],
          [field]: value
        }
      }
    }))
  }

  const adjustRounds = (delta: number) => {
    setConfig(prev => ({
      ...prev,
      totalRounds: Math.max(3, Math.min(10, prev.totalRounds + delta))
    }))
  }

  const adjustQuestions = (delta: number) => {
    setConfig(prev => ({
      ...prev,
      questionsPerRound: Math.max(3, Math.min(8, prev.questionsPerRound + delta))
    }))
  }

  const startGame = () => {
    toast.success('Starting game...')
    onStartGame(config)
  }

  // Step 0: Select Preset
  if (step === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4">
        <div className="max-w-lg mx-auto">
          <Button 
            variant="ghost" 
            onClick={onBackToMenu}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>

          <Card className="bg-black/40 backdrop-blur-md border-white/20">
            <CardHeader className="text-center">
              <Crown className="w-12 h-12 text-yellow-400 mx-auto mb-2" />
              <CardTitle className="text-2xl">ulTimote Game</CardTitle>
              <CardDescription>Choose a game mode to start</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {Object.entries(MOBILE_PRESETS).map(([key, preset]) => {
                const Icon = preset.icon
                return (
                  <motion.button
                    key={key}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => {
                      selectPreset(key)
                      setStep(1)
                    }}
                    className="w-full p-4 rounded-lg bg-white/10 hover:bg-white/20 
                             border border-white/20 hover:border-white/30 
                             transition-all text-left"
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-3 rounded-lg bg-black/30 ${preset.color}`}>
                        <Icon className="w-6 h-6" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg">{preset.name}</h3>
                        <p className="text-sm text-gray-400">{preset.description}</p>
                      </div>
                      <ChevronRight className="w-5 h-5 text-gray-400" />
                    </div>
                  </motion.button>
                )
              })}

              <Separator className="my-4" />

              <Button
                variant="outline"
                className="w-full"
                onClick={() => setStep(1)}
              >
                <Settings className="w-4 h-4 mr-2" />
                Custom Game
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Step 1: Customize
  if (step === 1) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4">
        <div className="max-w-lg mx-auto">
          <div className="flex items-center justify-between mb-4">
            <Button 
              variant="ghost" 
              onClick={() => setStep(0)}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <Button 
              variant="default"
              onClick={() => setStep(2)}
            >
              Review
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>

          <Card className="bg-black/40 backdrop-blur-md border-white/20 mb-4">
            <CardHeader>
              <CardTitle>Game Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Rounds */}
              <div>
                <Label className="text-base mb-2 block">Rounds: {config.totalRounds}</Label>
                <div className="flex items-center gap-3">
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => adjustRounds(-1)}
                    className="h-12 w-12"
                  >
                    <Minus className="w-5 h-5" />
                  </Button>
                  <div className="flex-1">
                    <Slider
                      value={[config.totalRounds]}
                      onValueChange={([value]) => setConfig(prev => ({ ...prev, totalRounds: value }))}
                      min={3}
                      max={10}
                      step={1}
                      className="touch-none"
                    />
                  </div>
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => adjustRounds(1)}
                    className="h-12 w-12"
                  >
                    <Plus className="w-5 h-5" />
                  </Button>
                </div>
              </div>

              {/* Questions per round */}
              <div>
                <Label className="text-base mb-2 block">Questions per Round: {config.questionsPerRound}</Label>
                <div className="flex items-center gap-3">
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => adjustQuestions(-1)}
                    className="h-12 w-12"
                  >
                    <Minus className="w-5 h-5" />
                  </Button>
                  <div className="flex-1">
                    <Slider
                      value={[config.questionsPerRound]}
                      onValueChange={([value]) => setConfig(prev => ({ ...prev, questionsPerRound: value }))}
                      min={3}
                      max={8}
                      step={1}
                      className="touch-none"
                    />
                  </div>
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => adjustQuestions(1)}
                    className="h-12 w-12"
                  >
                    <Plus className="w-5 h-5" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/40 backdrop-blur-md border-white/20">
            <CardHeader>
              <CardTitle>Quiz Categories</CardTitle>
              <CardDescription>Tap to enable/disable categories</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {MOBILE_CATEGORIES.map(({ key, name, icon: Icon, color }) => {
                const category = config.categories[key as keyof typeof config.categories]
                if (!category) return null
                
                return (
                  <div key={key} className="space-y-2">
                    <motion.button
                      whileTap={{ scale: 0.98 }}
                      onClick={() => toggleCategory(key)}
                      className={`w-full p-4 rounded-lg border transition-all ${
                        category.enabled 
                          ? 'bg-white/15 border-white/30' 
                          : 'bg-gray-800/30 border-gray-700/30 opacity-60'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg bg-black/30 ${color}`}>
                            <Icon className="w-5 h-5" />
                          </div>
                          <span className="font-medium">{name}</span>
                        </div>
                        <Switch
                          checked={category.enabled}
                          onCheckedChange={() => toggleCategory(key)}
                          onClick={(e) => e.stopPropagation()}
                          className="data-[state=checked]:bg-purple-500"
                        />
                      </div>
                    </motion.button>

                    {/* Additional UI controls for specific categories */}
                    {key === 'decadeChallenge' && category.enabled && (
                      <div className="px-4 py-3 bg-black/20 rounded-lg border border-white/10">
                        <Label className="text-xs font-semibold mb-2 block">Include songs from these decades:</Label>
                        <div className="grid grid-cols-3 gap-2">
                          {AVAILABLE_DECADES.map((decade) => (
                            <Button
                              key={decade}
                              variant={category.decades?.includes(decade) ? "default" : "outline"}
                              size="sm"
                              onClick={() => {
                                const currentDecades = category.decades || []
                                const newDecades = currentDecades.includes(decade)
                                  ? currentDecades.filter(d => d !== decade)
                                  : [...currentDecades, decade]
                                updateCategory(key, 'decades', newDecades)
                              }}
                              className="text-xs h-8"
                            >
                              {decade}
                            </Button>
                          ))}
                        </div>
                        {(!category.decades || category.decades.length === 0) && (
                          <p className="text-xs text-yellow-400 mt-2">Select at least one decade</p>
                        )}
                      </div>
                    )}

                    {key === 'genreSpecialist' && category.enabled && (
                      <div className="px-4 py-3 bg-black/20 rounded-lg border border-white/10">
                        <Label className="text-xs font-semibold mb-2 block">Include these genres:</Label>
                        <div className="grid grid-cols-3 gap-2">
                          {AVAILABLE_GENRES.slice(0, 9).map((genre) => (
                            <Button
                              key={genre}
                              variant={category.genres?.includes(genre) ? "default" : "outline"}
                              size="sm"
                              onClick={() => {
                                const currentGenres = category.genres || []
                                const newGenres = currentGenres.includes(genre)
                                  ? currentGenres.filter(g => g !== genre)
                                  : [...currentGenres, genre]
                                updateCategory(key, 'genres', newGenres)
                              }}
                              className="text-xs h-8"
                            >
                              {genre}
                            </Button>
                          ))}
                        </div>
                        {(!category.genres || category.genres.length === 0) && (
                          <p className="text-xs text-yellow-400 mt-2">Select at least one genre</p>
                        )}
                      </div>
                    )}

                    {key === 'chartPosition' && category.enabled && (
                      <div className="px-4 py-3 bg-black/20 rounded-lg border border-white/10">
                        <Label className="text-xs font-semibold mb-2 block">Chart Era Focus:</Label>
                        <div className="grid grid-cols-3 gap-2">
                          {(['all', 'classic', 'modern'] as const).map((era) => (
                            <Button
                              key={era}
                              variant={category.eraFocus === era ? "default" : "outline"}
                              size="sm"
                              onClick={() => updateCategory(key, 'eraFocus', era)}
                              className="text-xs h-8"
                            >
                              {era === 'all' ? 'All Eras' : era === 'classic' ? 'Classic' : 'Modern'}
                            </Button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Step 2: Review & Start
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4">
      <div className="max-w-lg mx-auto">
        <Button 
          variant="ghost" 
          onClick={() => setStep(1)}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>

        <Card className="bg-black/40 backdrop-blur-md border-white/20 mb-6">
          <CardHeader className="text-center">
            <Sparkles className="w-12 h-12 text-yellow-400 mx-auto mb-2" />
            <CardTitle className="text-2xl">Ready to Play!</CardTitle>
            <CardDescription>{config.gameName}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="text-center p-4 bg-white/10 rounded-lg">
                <div className="text-2xl font-bold text-purple-400">
                  {config.totalRounds * config.questionsPerRound}
                </div>
                <div className="text-sm text-gray-400">Total Questions</div>
              </div>
              <div className="text-center p-4 bg-white/10 rounded-lg">
                <div className="text-2xl font-bold text-blue-400">
                  {config.totalRounds * 5}
                </div>
                <div className="text-sm text-gray-400">Est. Minutes</div>
              </div>
            </div>

            <div className="space-y-3 mb-6">
              <h3 className="font-semibold text-sm text-gray-400">Active Categories:</h3>
              {MOBILE_CATEGORIES
                .filter(({ key }) => config.categories[key as keyof typeof config.categories]?.enabled)
                .map(({ key, name, icon: Icon, color }) => (
                  <div key={key} className="flex items-center gap-2">
                    <Icon className={`w-4 h-4 ${color}`} />
                    <span className="text-sm">{name}</span>
                    <Check className="w-4 h-4 text-green-400 ml-auto" />
                  </div>
                ))
              }
            </div>

            <Button
              onClick={startGame}
              size="lg"
              className="w-full bg-gradient-to-r from-purple-500 to-blue-500 
                       hover:from-purple-600 hover:to-blue-600 text-white"
            >
              <Play className="w-5 h-5 mr-2" />
              Start Game
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}