"use client"

import { useEffect } from 'react'
import { useTour } from '@/hooks/use-tour'
import { toast } from 'sonner'

const landingTourSteps = [
  {
    id: 'welcome',
    title: 'Welcome back to ulTimote! 🎵',
    text: 'Now that you\'ve set up your profile, let me show you all the amazing features you can use!',
    attachTo: {
      element: 'h1',
      on: 'bottom' as const
    },
    buttons: [
      {
        text: 'Skip Tour',
        action: 'complete' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Show me around!',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'jukebox',
    title: 'Remote Jukebox 📻',
    text: 'Control music playback from any device. Queue songs, vote on tracks, and manage playlists remotely!',
    attachTo: {
      element: '[href="/jukebox"]',
      on: 'bottom' as const
    },
    canClickTarget: true,
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Next',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'multiplayer',
    title: 'Multiplayer Quiz 🎮',
    text: 'Join quiz games with friends! Test your music knowledge and compete in real-time.',
    attachTo: {
      element: '[href="/multiplayer"]',
      on: 'bottom' as const
    },
    canClickTarget: true,
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Next',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'library',
    title: 'Music Library 🎵',
    text: 'Browse and search your entire music collection. Find songs, create playlists, and discover new favorites.',
    attachTo: {
      element: '[href="/library"]',
      on: 'top' as const
    },
    canClickTarget: true,
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Next',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'ready',
    title: 'You\'re All Set! 🎉',
    text: 'That\'s all the main features! Remember, you can always click the help button (?) to restart this tour. Have fun with ulTimote!',
    attachTo: {
      element: 'h1',
      on: 'bottom' as const
    },
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Start exploring!',
        action: 'complete' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  }
]

interface LandingTourProps {
  autoStart?: boolean
  onComplete?: () => void
}

export function LandingTour({ autoStart = false, onComplete }: LandingTourProps) {
  const { start, reset } = useTour({
    steps: landingTourSteps,
    tourName: 'landing-tour',
    defaultStyles: true,
    onComplete: () => {
      toast.success('Tour completed! Enjoy exploring ulTimote! 🎉')
      onComplete?.()
    },
    onCancel: () => {
      toast.info('Tour skipped. You can restart it anytime from the help button (?).')
    }
  })

  useEffect(() => {
    if (autoStart) {
      // Ensure DOM is ready and all elements are rendered
      const timer = setTimeout(() => {
        // Check if key elements exist before starting
        const hasJukebox = document.querySelector('[href="/jukebox"]')
        const hasMultiplayer = document.querySelector('[href="/multiplayer"]')
        
        if (hasJukebox && hasMultiplayer) {
          start()
        } else {
          // Try again after a short delay
          setTimeout(() => start(), 1000)
        }
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [autoStart, start])

  return null
}

// Export function to reset the tour
export function resetLandingTour() {
  localStorage.removeItem('tour-completed-landing-tour')
}