"use client"

import { useEffect } from 'react'
import { useTour } from '@/hooks/use-tour'
import { toast } from 'sonner'

const jukeboxTourSteps = [
  {
    id: 'now-playing',
    title: 'Now Playing 🎵',
    text: 'See what\'s currently playing, control playback, and adjust volume. The heart of your jukebox experience!',
    attachTo: {
      element: '[data-tour="now-playing"]',
      on: 'bottom' as const
    },
    buttons: [
      {
        text: 'Start Tour',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'queue',
    title: 'Music Queue 📋',
    text: 'View upcoming songs in the queue. Songs can be added from suggestions or voted up/down by users.',
    attachTo: {
      element: '[data-tour="queue-section"]',
      on: 'left' as const
    },
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Next',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'suggestions',
    title: 'Song Suggestions 💡',
    text: 'Community members can suggest songs to add to the queue. Ad<PERSON> can approve or reject suggestions.',
    attachTo: {
      element: '[role="tab"][data-value="suggestions"]',
      on: 'top' as const
    },
    canClickTarget: true,
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Next',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'favorites',
    title: 'Your Favorites ❤️',
    text: 'Quick access to your favorite tracks. Click the heart icon on any song to add it to your favorites!',
    attachTo: {
      element: '[role="tab"][data-value="favorites"]',
      on: 'top' as const
    },
    canClickTarget: true,
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Next',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'library-browser',
    title: 'Browse Library 📚',
    text: 'Search and browse the entire music library. Filter by genre, artist, or album to find exactly what you want.',
    attachTo: {
      element: '[data-tour="library-browser"]',
      on: 'top' as const
    },
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Next',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'voting',
    title: 'Vote on Songs 👍👎',
    text: 'Use the voting buttons to influence the queue order. Popular songs move up, unpopular ones move down!',
    attachTo: {
      element: '[data-tour="vote-buttons"]:first-of-type',
      on: 'left' as const
    },
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Finish Tour',
        action: 'complete' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  }
]

interface JukeboxTourProps {
  autoStart?: boolean
  onComplete?: () => void
}

export function JukeboxTour({ autoStart = false, onComplete }: JukeboxTourProps) {
  const { start, reset } = useTour({
    steps: jukeboxTourSteps,
    tourName: 'jukebox-tour',
    defaultStyles: true,
    onComplete: () => {
      toast.success('Jukebox tour completed! 🎉 Enjoy controlling the music!')
      onComplete?.()
    },
    onCancel: () => {
      toast.info('Tour skipped. You can restart it anytime from the help menu.')
    }
  })

  useEffect(() => {
    if (autoStart) {
      // Delay to ensure DOM is ready
      const timer = setTimeout(() => {
        start()
      }, 1500)
      return () => clearTimeout(timer)
    }
  }, [autoStart, start])

  return null
}

// Export function to reset the tour
export function resetJukeboxTour() {
  localStorage.removeItem('tour-completed-jukebox-tour')
}