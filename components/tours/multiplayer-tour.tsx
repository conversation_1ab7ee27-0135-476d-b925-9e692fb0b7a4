"use client"

import { useEffect } from 'react'
import { useTour } from '@/hooks/use-tour'
import { toast } from 'sonner'

const multiplayerTourSteps = [
  {
    id: 'welcome',
    title: 'Multiplayer Music Quiz! 🎮',
    text: 'Welcome to the multiplayer quiz arena! Let me show you how to join or host games.',
    attachTo: {
      element: 'h1',
      on: 'bottom' as const
    },
    buttons: [
      {
        text: 'Start Tour',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'host-game',
    title: 'Host a Game 🎯',
    text: 'Click here to create a new quiz game. You\'ll get a game PIN that others can use to join.',
    attachTo: {
      element: 'button:has-text("Host Game")',
      on: 'bottom' as const
    },
    canClickTarget: false,
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Next',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'join-game',
    title: 'Join a Game 🎲',
    text: 'Have a game PIN? Click here to join an existing game and compete with friends!',
    attachTo: {
      element: 'button:has-text("Join Game")',
      on: 'bottom' as const
    },
    canClickTarget: false,
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Next',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'active-games',
    title: 'Active Games 📊',
    text: 'See all currently active games. You can spectate or join games that haven\'t started yet.',
    attachTo: {
      element: '[data-tour="active-games"]',
      on: 'top' as const
    },
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Next',
        action: 'next' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  },
  {
    id: 'game-modes',
    title: 'Game Modes 🎵',
    text: 'Different quiz modes test your music knowledge in various ways - from classic trivia to audio recognition!',
    attachTo: {
      element: '[data-tour="game-modes"]',
      on: 'left' as const
    },
    buttons: [
      {
        text: 'Back',
        action: 'back' as const,
        classes: 'shepherd-button-secondary'
      },
      {
        text: 'Got it!',
        action: 'complete' as const,
        classes: 'shepherd-button-primary'
      }
    ]
  }
]

interface MultiplayerTourProps {
  autoStart?: boolean
  onComplete?: () => void
}

export function MultiplayerTour({ autoStart = false, onComplete }: MultiplayerTourProps) {
  const { start, reset } = useTour({
    steps: multiplayerTourSteps,
    tourName: 'multiplayer-tour',
    defaultStyles: true,
    onComplete: () => {
      toast.success('Ready to compete! May the best music lover win! 🏆')
      onComplete?.()
    },
    onCancel: () => {
      toast.info('Tour skipped. You can restart it anytime from the help menu.')
    }
  })

  useEffect(() => {
    if (autoStart) {
      // Delay to ensure DOM is ready
      const timer = setTimeout(() => {
        start()
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [autoStart, start])

  return null
}

// Export function to reset the tour
export function resetMultiplayerTour() {
  localStorage.removeItem('tour-completed-multiplayer-tour')
}