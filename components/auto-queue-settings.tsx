"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Music, 
  Settings, 
  Zap, 
  Shield, 
  BarChart3, 
  AlertTriangle,
  CheckCircle,
  Info,
  RefreshCw,
  Save,
  Loader2
} from "lucide-react"
import { toast } from "sonner"

interface AutoQueueConfig {
  id?: string
  name: string
  description?: string
  isActive: boolean
  isDefault: boolean
  
  // Trigger Settings
  queueThreshold: number
  checkInterval: number
  minConnectedUsers: number
  adaptiveMonitoring: boolean
  predictiveQueueing: boolean
  
  // Algorithm Settings
  algorithm: string
  algorithmWeights: Record<string, number>
  fallbackEnabled: boolean
  fallbackAlgorithm: string
  
  // Content Filters
  genreFilter: string[]
  yearRangeMin?: number
  yearRangeMax?: number
  minDuration?: number
  maxDuration?: number
  excludeExplicit: boolean
  
  // Advanced Settings
  diversityLevel: string
  energyAdaptation: boolean
  timeOfDayAdaptation: boolean
  userPreferenceWeight: number
  maxSongsPerTrigger: number
  preventRepeats: number
  
  // Buffer Settings
  maintainBuffer: boolean
  bufferSize: number
  emergencyQueueSize: number
  
  // Analytics
  totalSongsAdded?: number
  totalTriggers?: number
  lastTriggered?: string
  averageQueueTime?: number
}

const defaultConfig: AutoQueueConfig = {
  name: "Default Configuration",
  description: "Standard auto queue configuration",
  isActive: true,
  isDefault: true,
  queueThreshold: 5,
  checkInterval: 30,
  minConnectedUsers: 1,
  adaptiveMonitoring: false,
  predictiveQueueing: false,
  algorithm: "hybrid",
  algorithmWeights: {
    favorites: 0.4,
    intelligent: 0.3,
    popularity: 0.3
  },
  fallbackEnabled: true,
  fallbackAlgorithm: "popularity",
  genreFilter: [],
  excludeExplicit: false,
  diversityLevel: "medium",
  energyAdaptation: true,
  timeOfDayAdaptation: true,
  userPreferenceWeight: 0.7,
  maxSongsPerTrigger: 10,
  preventRepeats: 50,
  maintainBuffer: true,
  bufferSize: 20,
  emergencyQueueSize: 5
}

export function AutoQueueSettings() {
  const [configs, setConfigs] = useState<AutoQueueConfig[]>([])
  const [selectedConfig, setSelectedConfig] = useState<AutoQueueConfig>(defaultConfig)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState("triggers")

  useEffect(() => {
    loadConfigs()
  }, [])

  const loadConfigs = async () => {
    try {
      const response = await fetch('/api/jukebox/auto-queue-config')
      
      if (!response.ok) {
        let errorData = { error: 'Failed to load configurations' }
        try {
          errorData = await response.json()
        } catch (e) {
          console.error('Failed to parse error response:', e)
        }
        console.error('API Error:', errorData)
        
        if (response.status === 401) {
          toast.error('Please log in to access settings')
        } else if (response.status === 403) {
          toast.error('You need DJ or Superuser role to access these settings')
        } else {
          toast.error(errorData.error || 'Failed to load configurations')
        }
        return
      }
      
      const text = await response.text()
      if (!text) {
        console.error('Empty response from server')
        setConfigs([])
        setSelectedConfig(defaultConfig)
        toast.info('No configurations found. Using default settings.')
        return
      }
      
      let data
      try {
        data = JSON.parse(text)
      } catch (e) {
        console.error('Failed to parse response:', e, 'Response text:', text)
        toast.error('Invalid response from server')
        return
      }
      setConfigs(data.configs || [])
      
      // Find and set the active config
      const activeConfig = data.configs?.find((c: AutoQueueConfig) => c.isActive) || 
                          data.configs?.[0] || 
                          defaultConfig
      setSelectedConfig(activeConfig)
      
      if (data.configs?.length === 0) {
        toast.info('No configurations found. Using default settings.')
      }
    } catch (error) {
      console.error('Failed to load auto queue configs:', error)
      toast.error('Network error: Could not connect to server')
    } finally {
      setLoading(false)
    }
  }

  const saveConfig = async () => {
    setSaving(true)
    try {
      const response = await fetch('/api/jukebox/auto-queue-config', {
        method: selectedConfig.id ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(selectedConfig)
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Save API Error:', errorData)
        
        if (response.status === 401) {
          toast.error('Please log in to save settings')
        } else if (response.status === 403) {
          toast.error('You need DJ or Superuser role to save settings')
        } else {
          toast.error(errorData.error || 'Failed to save configuration')
        }
        return
      }

      const data = await response.json()
      toast.success('Configuration saved successfully')
      
      // Update the selectedConfig with the saved version (includes generated ID)
      if (data.config) {
        setSelectedConfig(data.config)
      }
      
      await loadConfigs()
    } catch (error) {
      console.error('Failed to save config:', error)
      toast.error('Network error: Could not save configuration')
    } finally {
      setSaving(false)
    }
  }

  const updateConfig = (updates: Partial<AutoQueueConfig>) => {
    setSelectedConfig(prev => ({ ...prev, ...updates }))
  }

  const updateAlgorithmWeight = (algorithm: string, weight: number) => {
    setSelectedConfig(prev => ({
      ...prev,
      algorithmWeights: {
        ...prev.algorithmWeights,
        [algorithm]: weight
      }
    }))
  }

  if (loading) {
    return (
      <Card className="bg-white/5 dark:bg-white/5 border border-gray-200/20 dark:border-gray-700/20">
        <CardContent className="py-12">
          <div className="flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-white/5 dark:bg-white/5 border border-gray-200/20 dark:border-gray-700/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base sm:text-lg flex items-center gap-2">
              <Music className="h-5 w-5" />
              Auto Queue Configuration
            </CardTitle>
            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1">
              Manage automatic queue filling behavior
            </p>
          </div>
          <div className="flex items-center gap-2">
            {selectedConfig.isActive && (
              <Badge variant="default" className="bg-green-600">
                Active
              </Badge>
            )}
            <Button
              size="sm"
              onClick={saveConfig}
              disabled={saving}
            >
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="triggers">Triggers</TabsTrigger>
            <TabsTrigger value="algorithms">Algorithms</TabsTrigger>
            <TabsTrigger value="filters">Filters</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="triggers" className="space-y-4 mt-4">
            <div className="grid gap-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="queueThreshold">Queue Threshold</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="queueThreshold"
                      type="number"
                      min={1}
                      max={50}
                      value={selectedConfig.queueThreshold}
                      onChange={(e) => updateConfig({ queueThreshold: parseInt(e.target.value) || 5 })}
                      className="flex-1"
                    />
                    <span className="text-sm text-gray-500">tracks</span>
                  </div>
                  <p className="text-xs text-gray-500">Trigger when queue has fewer tracks</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="checkInterval">Check Interval</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="checkInterval"
                      type="number"
                      min={10}
                      max={300}
                      value={selectedConfig.checkInterval}
                      onChange={(e) => updateConfig({ checkInterval: parseInt(e.target.value) || 30 })}
                      className="flex-1"
                    />
                    <span className="text-sm text-gray-500">seconds</span>
                  </div>
                  <p className="text-xs text-gray-500">How often to check queue status</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="adaptiveMonitoring">Adaptive Monitoring</Label>
                    <p className="text-xs text-gray-500">Adjust check frequency based on consumption rate</p>
                  </div>
                  <Switch
                    id="adaptiveMonitoring"
                    checked={selectedConfig.adaptiveMonitoring}
                    onCheckedChange={(checked) => updateConfig({ adaptiveMonitoring: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="predictiveQueueing">Predictive Queueing</Label>
                    <p className="text-xs text-gray-500">Add tracks before reaching threshold</p>
                  </div>
                  <Switch
                    id="predictiveQueueing"
                    checked={selectedConfig.predictiveQueueing}
                    onCheckedChange={(checked) => updateConfig({ predictiveQueueing: checked })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="minConnectedUsers">Minimum Connected Users</Label>
                <Input
                  id="minConnectedUsers"
                  type="number"
                  min={0}
                  max={100}
                  value={selectedConfig.minConnectedUsers}
                  onChange={(e) => updateConfig({ minConnectedUsers: parseInt(e.target.value) || 1 })}
                />
                <p className="text-xs text-gray-500">Only activate when this many users are connected</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="algorithms" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="algorithm">Primary Algorithm</Label>
                <Select
                  value={selectedConfig.algorithm}
                  onValueChange={(value) => updateConfig({ algorithm: value })}
                >
                  <SelectTrigger id="algorithm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="favorites-only">Favorites Only</SelectItem>
                    <SelectItem value="intelligent">Intelligent (AI-Powered)</SelectItem>
                    <SelectItem value="hybrid">Hybrid (Mixed)</SelectItem>
                    <SelectItem value="popularity">Popularity Based</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {selectedConfig.algorithm === 'hybrid' && (
                <div className="space-y-4 p-4 bg-gray-100/10 rounded-lg">
                  <Label>Algorithm Weights</Label>
                  {Object.entries(selectedConfig.algorithmWeights).map(([algo, weight]) => (
                    <div key={algo} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm capitalize">{algo}</Label>
                        <span className="text-sm text-gray-500">{Math.round(weight * 100)}%</span>
                      </div>
                      <Slider
                        value={[weight]}
                        onValueChange={([value]) => updateAlgorithmWeight(algo, value)}
                        min={0}
                        max={1}
                        step={0.1}
                        className="w-full"
                      />
                    </div>
                  ))}
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="fallbackEnabled">Enable Fallback</Label>
                  <p className="text-xs text-gray-500">Use simpler algorithm if primary fails</p>
                </div>
                <Switch
                  id="fallbackEnabled"
                  checked={selectedConfig.fallbackEnabled}
                  onCheckedChange={(checked) => updateConfig({ fallbackEnabled: checked })}
                />
              </div>

              {selectedConfig.fallbackEnabled && (
                <div className="space-y-2">
                  <Label htmlFor="fallbackAlgorithm">Fallback Algorithm</Label>
                  <Select
                    value={selectedConfig.fallbackAlgorithm}
                    onValueChange={(value) => updateConfig({ fallbackAlgorithm: value })}
                  >
                    <SelectTrigger id="fallbackAlgorithm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="popularity">Popularity Based</SelectItem>
                      <SelectItem value="random">Random Selection</SelectItem>
                      <SelectItem value="recent">Recently Added</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="filters" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Year Range</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      min={1900}
                      max={new Date().getFullYear()}
                      value={selectedConfig.yearRangeMin || ''}
                      onChange={(e) => updateConfig({ yearRangeMin: parseInt(e.target.value) || undefined })}
                    />
                    <span className="text-sm">to</span>
                    <Input
                      type="number"
                      placeholder="Max"
                      min={1900}
                      max={new Date().getFullYear()}
                      value={selectedConfig.yearRangeMax || ''}
                      onChange={(e) => updateConfig({ yearRangeMax: parseInt(e.target.value) || undefined })}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Duration Range</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      min={0}
                      value={selectedConfig.minDuration || ''}
                      onChange={(e) => updateConfig({ minDuration: parseInt(e.target.value) || undefined })}
                    />
                    <span className="text-sm">to</span>
                    <Input
                      type="number"
                      placeholder="Max"
                      min={0}
                      value={selectedConfig.maxDuration || ''}
                      onChange={(e) => updateConfig({ maxDuration: parseInt(e.target.value) || undefined })}
                    />
                    <span className="text-sm text-gray-500">sec</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="excludeExplicit">Exclude Explicit Content</Label>
                  <p className="text-xs text-gray-500">Filter out songs marked as explicit</p>
                </div>
                <Switch
                  id="excludeExplicit"
                  checked={selectedConfig.excludeExplicit}
                  onCheckedChange={(checked) => updateConfig({ excludeExplicit: checked })}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="diversityLevel">Diversity Level</Label>
                <Select
                  value={selectedConfig.diversityLevel}
                  onValueChange={(value) => updateConfig({ diversityLevel: value })}
                >
                  <SelectTrigger id="diversityLevel">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low (More repetition)</SelectItem>
                    <SelectItem value="medium">Medium (Balanced)</SelectItem>
                    <SelectItem value="high">High (Maximum variety)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxSongsPerTrigger">Max Songs Per Trigger</Label>
                <Input
                  id="maxSongsPerTrigger"
                  type="number"
                  min={1}
                  max={50}
                  value={selectedConfig.maxSongsPerTrigger}
                  onChange={(e) => updateConfig({ maxSongsPerTrigger: parseInt(e.target.value) || 10 })}
                />
                <p className="text-xs text-gray-500">Maximum tracks to add in one operation</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="preventRepeats">Prevent Repeats</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="preventRepeats"
                    type="number"
                    min={0}
                    max={500}
                    value={selectedConfig.preventRepeats}
                    onChange={(e) => updateConfig({ preventRepeats: parseInt(e.target.value) || 50 })}
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-500">tracks</span>
                </div>
                <p className="text-xs text-gray-500">Don't repeat tracks within this many plays</p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="maintainBuffer">Maintain Buffer</Label>
                  <p className="text-xs text-gray-500">Keep emergency tracks ready</p>
                </div>
                <Switch
                  id="maintainBuffer"
                  checked={selectedConfig.maintainBuffer}
                  onCheckedChange={(checked) => updateConfig({ maintainBuffer: checked })}
                />
              </div>

              {selectedConfig.maintainBuffer && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bufferSize">Buffer Size</Label>
                    <Input
                      id="bufferSize"
                      type="number"
                      min={5}
                      max={100}
                      value={selectedConfig.bufferSize}
                      onChange={(e) => updateConfig({ bufferSize: parseInt(e.target.value) || 20 })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="emergencyQueueSize">Emergency Queue</Label>
                    <Input
                      id="emergencyQueueSize"
                      type="number"
                      min={1}
                      max={20}
                      value={selectedConfig.emergencyQueueSize}
                      onChange={(e) => updateConfig({ emergencyQueueSize: parseInt(e.target.value) || 5 })}
                    />
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        {/* Analytics Section */}
        {selectedConfig.totalSongsAdded !== undefined && (
          <div className="mt-6 pt-6 border-t border-gray-200/20 dark:border-gray-700/20">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold">{selectedConfig.totalSongsAdded || 0}</p>
                <p className="text-xs text-gray-500">Songs Added</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">{selectedConfig.totalTriggers || 0}</p>
                <p className="text-xs text-gray-500">Triggers</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">
                  {selectedConfig.averageQueueTime ? `${selectedConfig.averageQueueTime.toFixed(1)}s` : '0s'}
                </p>
                <p className="text-xs text-gray-500">Avg Queue Time</p>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium">
                  {selectedConfig.lastTriggered 
                    ? new Date(selectedConfig.lastTriggered).toLocaleString()
                    : 'Never'
                  }
                </p>
                <p className="text-xs text-gray-500">Last Triggered</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}