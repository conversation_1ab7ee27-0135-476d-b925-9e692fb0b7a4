"use client"

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Plus, Shuffle, Heart, Music, Loader2 } from "lucide-react"
import { toast } from "sonner"
import type { Song } from "@/lib/types"
import { api } from '@/lib/api-client'
import { getClientUserInfo } from '@/lib/auth-cookies-client'

interface FillPlaylistButtonProps {
  selectedCategory: string
  onSongsAdded?: (songs: Song[]) => void
  refreshQueue?: () => Promise<void>
  className?: string
}

export function FillPlaylistButton({ 
  selectedCategory, 
  onSongsAdded,
  refreshQueue,
  className = "" 
}: FillPlaylistButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [songCount, setSongCount] = useState(10)
  const [isLoading, setIsLoading] = useState(false)

  const isAllTimeFavorites = selectedCategory === 'all-time-favorites'

  const handleFillPlaylist = async () => {
    if (!isAllTimeFavorites && selectedCategory === 'all') {
      toast.error('Please select a category first')
      return
    }

    setIsLoading(true)
    try {
      // Fetch songs from the selected category
      const params = new URLSearchParams({
        category: selectedCategory,
        limit: songCount.toString(),
        shuffle: 'true'
      })

      const data = await api.get(`/api/library/category-songs?${params}`)

      if (!data.success || !data.songs || data.songs.length === 0) {
        toast.error(data.error || 'No songs found in this category')
        return
      }

      // Get user info for rate limiting
      const userInfo = getClientUserInfo()
      
      let successCount = 0
      let failedCount = 0
      let duplicateCount = 0
      
      // Add songs sequentially to respect rate limits and handle errors
      for (const song of data.songs) {
        try {
          try {
            await api.post('/api/mpd/queue', { 
              action: 'add',
              filePath: song.filePath || song.file,
              addedBy: userInfo?.username || 'Fill Playlist',
              userId: userInfo?.id,
              userRole: userInfo?.role
            })
            successCount++
          } catch (error: any) {
            if (error.data?.duplicate) {
              duplicateCount++
            } else if (error.data?.rateLimited) {
              toast.error(error.data?.message || 'Rate limit reached')
              break // Stop trying to add more songs
            } else {
              failedCount++
              console.error(`Failed to add song: ${song.title}`, error)
            }
          }
        } catch (error) {
          failedCount++
          console.error(`Error adding song: ${song.title}`, error)
        }
      }
      
      if (onSongsAdded) {
        onSongsAdded(data.songs)
      }
      
      if (refreshQueue) {
        await refreshQueue()
      }

      // Show appropriate message based on results
      if (successCount > 0 && failedCount === 0 && duplicateCount === 0) {
        toast.success(`Added ${successCount} ${isAllTimeFavorites ? 'all-time favorites' : 'songs from ' + selectedCategory} to queue`)
      } else if (successCount > 0) {
        let message = `Added ${successCount} songs to queue`
        if (duplicateCount > 0) {
          message += ` (${duplicateCount} already in queue)`
        }
        if (failedCount > 0) {
          message += ` (${failedCount} failed)`
        }
        toast.success(message)
      } else if (duplicateCount > 0) {
        toast.warning(`All ${duplicateCount} songs are already in queue`)
      } else {
        toast.error('Failed to add songs to queue')
      }
      setIsOpen(false)
    } catch (error) {
      console.error('Failed to fill playlist:', error)
      toast.error('Failed to fill playlist')
    } finally {
      setIsLoading(false)
    }
  }

  const getCategoryDisplay = () => {
    if (isAllTimeFavorites) {
      return (
        <div className="flex items-center gap-1">
          <Heart className="w-3 h-3" />
          <span>All-Time Favorites</span>
        </div>
      )
    }
    if (selectedCategory === 'all') {
      return 'Select a category'
    }
    return selectedCategory.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          className={`bg-white/10 border-white/20 text-white hover:bg-white/20 ${className}`}
          disabled={!isAllTimeFavorites && selectedCategory === 'all'}
        >
          <Plus className="w-4 h-4 mr-2" />
          Fill Playlist
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">Fill Playlist with Songs</h4>
            <p className="text-sm text-muted-foreground">
              Add random songs from the selected category to your queue
            </p>
          </div>
          
          <div className="space-y-3">
            <div>
              <Label className="text-xs">Selected Category</Label>
              <Badge variant="secondary" className="mt-1 w-full justify-center py-1">
                {getCategoryDisplay()}
              </Badge>
            </div>

            <div>
              <Label htmlFor="song-count">Number of Songs</Label>
              <div className="flex items-center gap-2 mt-1">
                <Input
                  id="song-count"
                  type="number"
                  value={songCount}
                  onChange={(e) => setSongCount(Math.max(1, Math.min(50, parseInt(e.target.value) || 1)))}
                  min="1"
                  max="50"
                  className="flex-1"
                />
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSongCount(5)}
                  >
                    5
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSongCount(10)}
                  >
                    10
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSongCount(20)}
                  >
                    20
                  </Button>
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Songs will be randomly selected and shuffled
              </p>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleFillPlaylist}
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <Shuffle className="w-4 h-4 mr-2" />
                  Add {songCount} Songs
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
            >
              Cancel
            </Button>
          </div>

          {isAllTimeFavorites && (
            <div className="text-xs text-center text-muted-foreground bg-orange-50 dark:bg-orange-900/20 p-2 rounded">
              <Music className="w-4 h-4 inline mr-1" />
              Adding from your all-time favorite tracks
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}