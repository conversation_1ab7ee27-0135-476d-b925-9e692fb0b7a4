"use client"

import { useState, use<PERSON><PERSON><PERSON> } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Search, 
  Download, 
  Play, 
  Clock, 
  Eye, 
  Calendar, 
  Music, 
  User, 
  Album, 
  <PERSON><PERSON>,
  <PERSON><PERSON>2,
  CheckCircle,
  XCircle,
  ExternalLink,
  Volume2,
  <PERSON><PERSON><PERSON>riangle,
  Info,
  Lightbulb
} from "lucide-react"
import Image from "next/image"
import { useToast } from "@/hooks/use-toast"

interface YouTubeResult {
  id: string
  title: string
  channel: string
  duration: string
  thumbnail: string
  url: string
  views?: string
  uploadDate?: string
}

interface DownloadMetadata {
  artist: string
  title: string
  album?: string
  year?: number
  genre?: string
}

interface DownloadStatus {
  status: 'idle' | 'searching' | 'checking' | 'downloading' | 'processing' | 'success' | 'error'
  message?: string
  progress?: number
}

interface DuplicateTrack {
  id: string
  artist: string
  title: string
  album?: string
  year?: number
  genre?: string
  mpdFilePath: string
}

interface MetadataPreview {
  suggestedGenre?: string
  suggestedYear?: number
  similarTracks: Array<{
    artist: string
    title: string
    album?: string
    year?: number
    genre?: string
  }>
}

interface DuplicateCheckResult {
  exists: boolean
  tracks: DuplicateTrack[]
}

const GENRES = [
  'Rock', 'Pop', 'Hip Hop', 'Electronic', 'Jazz', 'Classical', 'Country', 
  'R&B', 'Folk', 'Blues', 'Reggae', 'Punk', 'Metal', 'Indie', 'Alternative',
  'Dance', 'House', 'Techno', 'Ambient', 'World', 'Soundtrack', 'Other'
]

export function MusicSearch() {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<YouTubeResult[]>([])
  const [selectedTrack, setSelectedTrack] = useState<YouTubeResult | null>(null)
  const [downloadMetadata, setDownloadMetadata] = useState<DownloadMetadata>({
    artist: '',
    title: '',
    album: '',
    year: new Date().getFullYear(),
    genre: ''
  })
  const [downloadStatus, setDownloadStatus] = useState<DownloadStatus>({ status: 'idle' })
  const [showMetadataDialog, setShowMetadataDialog] = useState(false)
  const [duplicateCheck, setDuplicateCheck] = useState<DuplicateCheckResult | null>(null)
  const [metadataPreview, setMetadataPreview] = useState<MetadataPreview | null>(null)
  const [showDuplicateWarning, setShowDuplicateWarning] = useState(false)

  const handleSearch = useCallback(async () => {
    if (!searchQuery.trim()) return

    setDownloadStatus({ status: 'searching', message: 'Searching YouTube...' })
    
    try {
      const response = await fetch(`/api/music/search?q=${encodeURIComponent(searchQuery)}&limit=15`)
      
      if (!response.ok) {
        throw new Error('Search failed')
      }

      const data = await response.json()
      setSearchResults(data.results || [])
      setDownloadStatus({ status: 'idle' })
      
      toast({
        title: "Search completed",
        description: `Found ${data.results?.length || 0} results`,
      })
    } catch (error) {
      console.error('Search failed:', error)
      setDownloadStatus({ 
        status: 'error', 
        message: 'Search failed. Please try again.' 
      })
      
      toast({
        title: "Search failed",
        description: "Please check your connection and try again.",
        variant: "destructive",
      })
    }
  }, [searchQuery])

  const checkDuplicatesAndMetadata = async (artist: string, title: string) => {
    setDownloadStatus({ status: 'checking', message: 'Checking for duplicates and loading suggestions...' })
    
    try {
      const response = await fetch('/api/music/search', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ artist, title })
      })

      if (!response.ok) {
        throw new Error('Failed to check duplicates')
      }

      const data = await response.json()
      return {
        duplicateCheck: data.duplicateCheck,
        metadataPreview: data.metadataPreview
      }
    } catch (error) {
      console.error('Duplicate check failed:', error)
      toast({
        title: "Warning",
        description: "Could not check for duplicates. Proceeding anyway.",
        variant: "destructive",
      })
      return null
    }
  }

  const handleSelectTrack = async (track: YouTubeResult) => {
    setSelectedTrack(track)
    
    // Try to parse artist and title from video title
    const titleParts = track.title.split(' - ')
    let artist = track.channel
    let title = track.title
    
    if (titleParts.length >= 2) {
      artist = titleParts[0].trim()
      title = titleParts.slice(1).join(' - ').trim()
    }

    // Check for duplicates and get metadata suggestions
    const checkResult = await checkDuplicatesAndMetadata(artist, title)
    
    if (checkResult) {
      setDuplicateCheck(checkResult.duplicateCheck)
      setMetadataPreview(checkResult.metadataPreview)
      
      if (checkResult.duplicateCheck.exists) {
        setShowDuplicateWarning(true)
        setDownloadStatus({ status: 'idle' })
        return
      }
      
      // Pre-populate metadata with suggestions
      setDownloadMetadata({
        artist,
        title,
        album: '',
        year: checkResult.metadataPreview.suggestedYear || new Date().getFullYear(),
        genre: checkResult.metadataPreview.suggestedGenre || ''
      })
      
      // Show suggestions to user
      if (checkResult.metadataPreview.suggestedGenre || checkResult.metadataPreview.suggestedYear) {
        toast({
          title: "Metadata suggestions loaded",
          description: `Pre-populated genre and year based on similar tracks by ${artist}`,
        })
      }
    } else {
      // Fallback if duplicate check fails
      setDownloadMetadata({
        artist,
        title,
        album: '',
        year: new Date().getFullYear(),
        genre: ''
      })
    }
    
    setDownloadStatus({ status: 'idle' })
    setShowMetadataDialog(true)
  }

  const handleDownload = async () => {
    if (!selectedTrack || !downloadMetadata.artist || !downloadMetadata.title) {
      return
    }

    setDownloadStatus({ status: 'downloading', message: 'Starting download...' })
    setShowMetadataDialog(false)

    try {
      const response = await fetch('/api/music/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          videoId: selectedTrack.id,
          artist: downloadMetadata.artist,
          title: downloadMetadata.title,
          album: downloadMetadata.album || undefined,
          year: downloadMetadata.year || undefined,
          genre: downloadMetadata.genre || undefined
        })
      })

      const result = await response.json()

      if (!response.ok) {
        // Handle duplicate error specifically
        if (response.status === 409 && result.duplicates) {
          setDownloadStatus({ status: 'error', message: 'Track already exists in database' })
          toast({
            title: "Duplicate track",
            description: `This track already exists: ${result.duplicates[0].artist} - ${result.duplicates[0].title}`,
            variant: "destructive",
          })
          return
        }
        throw new Error(result.error || 'Download failed')
      }

      setDownloadStatus({ 
        status: 'success', 
        message: `Successfully downloaded: ${downloadMetadata.artist} - ${downloadMetadata.title}` 
      })

      toast({
        title: "Download completed",
        description: `${downloadMetadata.artist} - ${downloadMetadata.title} has been added to your library`,
      })

      // Clear selection after successful download
      setTimeout(() => {
        setSelectedTrack(null)
        setDownloadStatus({ status: 'idle' })
        setDuplicateCheck(null)
        setMetadataPreview(null)
      }, 3000)

    } catch (error) {
      console.error('Download failed:', error)
      setDownloadStatus({ 
        status: 'error', 
        message: 'Download failed. Please try again.' 
      })
      
      toast({
        title: "Download failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      })
    }
  }

  const handleForceDownload = () => {
    setShowDuplicateWarning(false)
    setShowMetadataDialog(true)
  }

  const getStatusIcon = () => {
    switch (downloadStatus.status) {
      case 'searching':
      case 'checking':
      case 'downloading':
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const getStatusColor = () => {
    switch (downloadStatus.status) {
      case 'success':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'error':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'searching':
      case 'checking':
      case 'downloading':
      case 'processing':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Music className="h-5 w-5" />
            Music Search & Download
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-2">
            <Input
              placeholder="Search for artist - title"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="flex-1"
            />
            <Button 
              onClick={handleSearch}
              disabled={!searchQuery.trim() || ['searching', 'checking'].includes(downloadStatus.status)}
              className="w-full sm:w-auto"
            >
              {downloadStatus.status === 'searching' ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Search className="h-4 w-4 mr-2" />
              )}
              Search
            </Button>
          </div>

          {/* Status Display */}
          {downloadStatus.status !== 'idle' && (
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <Badge className={getStatusColor()}>
                {downloadStatus.message}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
          <CardHeader>
            <CardTitle>Search Results ({searchResults.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              {searchResults.map((result) => (
                <div 
                  key={result.id} 
                  className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 p-4 rounded-lg bg-white/5 dark:bg-white/5 hover:bg-white/10 dark:hover:bg-white/10 transition-colors"
                >
                  <div className="flex items-start gap-3 w-full sm:w-auto">
                    {/* Thumbnail */}
                    <div className="relative w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={result.thumbnail}
                        alt={result.title}
                        fill
                        className="object-cover"
                        sizes="80px"
                      />
                      <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                        <Play className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                      </div>
                    </div>

                    {/* Track Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-sm mb-1 line-clamp-2 sm:truncate" title={result.title}>
                        {result.title}
                      </h3>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span className="truncate">{result.channel}</span>
                      </p>
                      <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs text-gray-500 dark:text-gray-500">
                        {result.duration && (
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {result.duration}
                          </span>
                        )}
                        {result.views && (
                          <span className="flex items-center gap-1 hidden sm:flex">
                            <Eye className="h-3 w-3" />
                            {result.views}
                          </span>
                        )}
                        {result.uploadDate && (
                          <span className="flex items-center gap-1 hidden sm:flex">
                            <Calendar className="h-3 w-3" />
                            {result.uploadDate}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 w-full sm:w-auto sm:flex-shrink-0">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(result.url, '_blank')}
                      className="flex-1 sm:flex-initial"
                    >
                      <ExternalLink className="h-4 w-4" />
                      <span className="ml-1 sm:hidden">View</span>
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleSelectTrack(result)}
                      disabled={['checking', 'downloading', 'processing'].includes(downloadStatus.status)}
                      className="flex-1 sm:flex-initial"
                    >
                      {downloadStatus.status === 'checking' ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      ) : (
                        <Download className="h-4 w-4 mr-1" />
                      )}
                      Download
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Duplicate Warning Dialog */}
      <AlertDialog open={showDuplicateWarning} onOpenChange={setShowDuplicateWarning}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              Duplicate Track Found
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-3">
              <p>This track already exists in your database:</p>
              {duplicateCheck?.tracks.map((track, index) => (
                <div key={index} className="p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <p className="font-medium">{track.artist} - {track.title}</p>
                  {track.album && <p className="text-sm text-gray-600 dark:text-gray-400">Album: {track.album}</p>}
                  {track.year && <p className="text-sm text-gray-600 dark:text-gray-400">Year: {track.year}</p>}
                  <p className="text-xs text-gray-500 dark:text-gray-500">File: {track.mpdFilePath}</p>
                </div>
              ))}
              <p>Do you still want to download this track? This will create a duplicate entry.</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowDuplicateWarning(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleForceDownload}>
              Download Anyway
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Metadata Dialog */}
      <Dialog open={showMetadataDialog} onOpenChange={setShowMetadataDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Download Track</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedTrack && (
              <div className="flex items-center gap-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <Image
                  src={selectedTrack.thumbnail}
                  alt={selectedTrack.title}
                  width={60}
                  height={60}
                  className="rounded object-cover"
                />
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-sm truncate">{selectedTrack.title}</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">{selectedTrack.channel}</p>
                </div>
              </div>
            )}

            {/* Metadata Suggestions */}
            {metadataPreview && (metadataPreview.suggestedGenre || metadataPreview.suggestedYear) && (
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Lightbulb className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-300">Suggestions from similar tracks</span>
                </div>
                <div className="text-sm text-blue-600 dark:text-blue-400">
                  {metadataPreview.suggestedGenre && <p>Genre: {metadataPreview.suggestedGenre}</p>}
                  {metadataPreview.suggestedYear && <p>Year: {metadataPreview.suggestedYear}</p>}
                  {metadataPreview.similarTracks.length > 0 && (
                    <p>Based on {metadataPreview.similarTracks.length} other tracks by this artist</p>
                  )}
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="artist">Artist *</Label>
                <Input
                  id="artist"
                  value={downloadMetadata.artist}
                  onChange={(e) => setDownloadMetadata(prev => ({ ...prev, artist: e.target.value }))}
                  placeholder="Artist name"
                />
              </div>
              <div>
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={downloadMetadata.title}
                  onChange={(e) => setDownloadMetadata(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Song title"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="album">Album</Label>
                <Input
                  id="album"
                  value={downloadMetadata.album}
                  onChange={(e) => setDownloadMetadata(prev => ({ ...prev, album: e.target.value }))}
                  placeholder="Album name"
                />
              </div>
              <div>
                <Label htmlFor="year">Year</Label>
                <Input
                  id="year"
                  type="number"
                  value={downloadMetadata.year}
                  onChange={(e) => setDownloadMetadata(prev => ({ ...prev, year: parseInt(e.target.value) || undefined }))}
                  placeholder="Release year"
                  min="1900"
                  max={new Date().getFullYear() + 1}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="genre">Genre</Label>
              <Select value={downloadMetadata.genre} onValueChange={(value) => setDownloadMetadata(prev => ({ ...prev, genre: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select genre" />
                </SelectTrigger>
                <SelectContent>
                  {GENRES.map((genre) => (
                    <SelectItem key={genre} value={genre}>
                      {genre}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setShowMetadataDialog(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleDownload}
                disabled={!downloadMetadata.artist || !downloadMetadata.title}
              >
                <Download className="h-4 w-4 mr-2" />
                Download & Add to Library
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
} 