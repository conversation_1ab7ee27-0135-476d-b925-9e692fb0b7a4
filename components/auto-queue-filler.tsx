"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Play, 
  Pause, 
  Shuffle, 
  Heart, 
  Music, 
  Settings, 
  Plus,
  RefreshCw,
  Filter,
  Clock,
  Users,
  Lightbulb
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "sonner"
import { useUser } from "@/lib/user-context"
import type { Song } from "@/lib/types"
import { enhancedDeduplicateSongs } from "@/lib/enhanced-duplicate-detection"

interface AutoQueueFillerProps {
  currentQueueLength: number
  onSongsAdded: (songs: Song[]) => void
  isAdmin: boolean
}

interface QueueFillerSettings {
  enabled: boolean
  minQueueSize: number
  maxSongsToAdd: number
  source: 'favorites' | 'library' | 'mixed' | 'smart-mix'
  genreFilter: string
  yearRange: { min: number; max: number }
  shuffleOrder: boolean
  autoRefill: boolean
  refillInterval: number // minutes
}

const defaultSettings: QueueFillerSettings = {
  enabled: false,
  minQueueSize: 5,
  maxSongsToAdd: 10,
  source: 'mixed',
  genreFilter: '',
  yearRange: { min: 1950, max: new Date().getFullYear() },
  shuffleOrder: true,
  autoRefill: true,
  refillInterval: 5
}

export function AutoQueueFiller({ currentQueueLength, onSongsAdded, isAdmin }: AutoQueueFillerProps) {
  const { user } = useUser()
  const [settings, setSettings] = useState<QueueFillerSettings>(defaultSettings)
  const [isLoading, setIsLoading] = useState(false)
  const [lastFillTime, setLastFillTime] = useState<Date | null>(null)
  const [availableGenres, setAvailableGenres] = useState<string[]>([])
  const [libraryStats, setLibraryStats] = useState({ total: 0, favorites: 0 })
  const [isRunning, setIsRunning] = useState(false)

  // Load available genres and library stats
  useEffect(() => {
    const loadLibraryInfo = async () => {
      try {
        // Get auth token
        const { ClientTokenManager } = await import('@/lib/client-auth')
        const token = ClientTokenManager.getToken()
        
        const headers: HeadersInit = {}
        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }
        
        const [genresResponse, statsResponse] = await Promise.all([
          fetch('/api/library/genres', { headers }),
          fetch('/api/library/stats', { headers })
        ])

        if (genresResponse.ok) {
          const genresData = await genresResponse.json()
          setAvailableGenres(genresData.genres || [])
        }

        if (statsResponse.ok) {
          const statsData = await statsResponse.json()
          setLibraryStats({
            total: statsData.stats?.totalTracks || 0,
            favorites: statsData.stats?.totalFavorites || 0
          })
        }
      } catch (error) {
        console.error('Failed to load library info:', error)
      }
    }

    loadLibraryInfo()
  }, [])

  // Auto-refill logic
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (settings.enabled && settings.autoRefill && isRunning) {
      interval = setInterval(() => {
        if (currentQueueLength < settings.minQueueSize) {
          handleFillQueue()
        }
      }, settings.refillInterval * 60 * 1000) // Convert minutes to milliseconds
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [settings, currentQueueLength, isRunning])

  const handleFillQueue = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        source: settings.source,
        limit: settings.maxSongsToAdd.toString(),
        shuffle: settings.shuffleOrder.toString(),
        ...(settings.genreFilter && { genre: settings.genreFilter }),
        minYear: settings.yearRange.min.toString(),
        maxYear: settings.yearRange.max.toString()
      })

      const response = await fetch(`/api/library/queue-filler?${params}`)
      const data = await response.json()

      if (data.success && data.songs) {
        // Deduplicate songs before adding
        const deduplicatedSongs = enhancedDeduplicateSongs(data.songs)
        
        // Add songs to MPD queue
        // Get auth token for queue add requests
        const { ClientTokenManager } = await import('@/lib/client-auth')
        const token = ClientTokenManager.getToken()
        
        const headers: HeadersInit = { 'Content-Type': 'application/json' }
        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }
        
        const addPromises = deduplicatedSongs.map(song => 
          fetch('/api/mpd/queue/add', {
            method: 'POST',
            headers,
            body: JSON.stringify({ 
              filePath: song.filePath || song.file,
              addedBy: user?.displayName || 'Auto Queue Filler'
            })
          })
        )

        await Promise.all(addPromises)
        
        onSongsAdded(deduplicatedSongs)
        setLastFillTime(new Date())
        
        toast.success(`Added ${deduplicatedSongs.length} songs to queue`)
      } else {
        toast.error(data.error || 'Failed to get songs for queue')
      }
    } catch (error) {
      console.error('Failed to fill queue:', error)
      toast.error('Failed to fill queue')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddAllFavorites = async () => {
    if (!isAdmin) {
      toast.error('Only admins can add all favorites')
      return
    }

    setIsLoading(true)
    try {
      // Get auth token
      const { TokenManager } = await import('@/lib/auth-service')
      const token = TokenManager.getToken()
      
      const authHeaders: HeadersInit = {}
      if (token) {
        authHeaders['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch('/api/library/favorites', { headers: authHeaders })
      const data = await response.json()

      if (data.success && data.favorites) {
        const headers: HeadersInit = { 'Content-Type': 'application/json' }
        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }
        
        const addPromises = data.favorites.map((song: Song) => 
          fetch('/api/mpd/queue/add', {
            method: 'POST',
            headers,
            body: JSON.stringify({ 
              filePath: song.filePath || song.file,
              addedBy: user?.displayName || 'Admin'
            })
          })
        )

        await Promise.all(addPromises)
        onSongsAdded(data.favorites)
        
        toast.success(`Added ${data.favorites.length} favorite songs to queue`)
      } else {
        toast.error('Failed to get favorite songs')
      }
    } catch (error) {
      console.error('Failed to add favorites:', error)
      toast.error('Failed to add favorites to queue')
    } finally {
      setIsLoading(false)
    }
  }

  const toggleAutoFiller = () => {
    setIsRunning(!isRunning)
    setSettings(prev => ({ ...prev, enabled: !isRunning }))
    
    if (!isRunning) {
      toast.success('Auto queue filler started')
    } else {
      toast.info('Auto queue filler stopped')
    }
  }

  const needsRefill = currentQueueLength < settings.minQueueSize
  const fillProgress = Math.min((currentQueueLength / settings.minQueueSize) * 100, 100)

  return (
    <Card className="bg-white/10 backdrop-blur-md border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Shuffle className="w-5 h-5" />
              Auto Queue Filler
            </CardTitle>
            <p className="text-sm text-gray-400 mt-1">
              Automatically maintain queue with your music preferences
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={needsRefill ? "destructive" : "secondary"}>
              Queue: {currentQueueLength}
            </Badge>
            <Switch
              checked={isRunning}
              onCheckedChange={toggleAutoFiller}
              disabled={isLoading}
            />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Queue Status */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Queue Level</span>
            <span>{currentQueueLength} / {settings.minQueueSize} minimum</span>
          </div>
          <Progress value={fillProgress} className="h-2" />
          {needsRefill && (
            <p className="text-sm text-amber-400 flex items-center gap-1">
              <Clock className="w-4 h-4" />
              Queue needs refilling
            </p>
          )}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <Button
            onClick={handleFillQueue}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Plus className="w-4 h-4 mr-2" />
            )}
            Fill Queue Now
          </Button>

          {isAdmin && (
            <Button
              onClick={handleAddAllFavorites}
              disabled={isLoading}
              variant="outline"
              className="border-pink-500 text-pink-400 hover:bg-pink-500/10"
            >
              <Heart className="w-4 h-4 mr-2" />
              Add All Favorites ({libraryStats.favorites})
            </Button>
          )}
        </div>

        {/* Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-white/10">
          <div className="space-y-3">
            <Label>Source</Label>
            <Select
              value={settings.source}
              onValueChange={(value: any) => setSettings(prev => ({ ...prev, source: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="favorites">
                  <div className="flex items-center gap-2">
                    <Heart className="w-4 h-4" />
                    Favorites Only
                  </div>
                </SelectItem>
                <SelectItem value="library">
                  <div className="flex items-center gap-2">
                    <Music className="w-4 h-4" />
                    Full Library
                  </div>
                </SelectItem>
                <SelectItem value="mixed">
                  <div className="flex items-center gap-2">
                    <Shuffle className="w-4 h-4" />
                    Mixed (70% Favorites)
                  </div>
                </SelectItem>
                <SelectItem value="smart-mix">
                  <div className="flex items-center gap-2">
                    <Lightbulb className="w-4 h-4" />
                    Smart Mix (AI-Powered)
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <Label>Genre Filter</Label>
            <Select
              value={settings.genreFilter}
              onValueChange={(value) => setSettings(prev => ({ ...prev, genreFilter: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="All genres" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Genres</SelectItem>
                {availableGenres.map(genre => (
                  <SelectItem key={genre} value={genre}>{genre}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <Label>Min Queue Size</Label>
            <Input
              type="number"
              value={settings.minQueueSize}
              onChange={(e) => setSettings(prev => ({ 
                ...prev, 
                minQueueSize: parseInt(e.target.value) || 5 
              }))}
              min="1"
              max="50"
            />
          </div>

          <div className="space-y-3">
            <Label>Songs to Add</Label>
            <Input
              type="number"
              value={settings.maxSongsToAdd}
              onChange={(e) => setSettings(prev => ({ 
                ...prev, 
                maxSongsToAdd: parseInt(e.target.value) || 10 
              }))}
              min="1"
              max="100"
            />
          </div>
        </div>

        {/* Advanced Settings */}
        <div className="space-y-3 pt-4 border-t border-white/10">
          <div className="flex items-center justify-between">
            <Label>Shuffle Order</Label>
            <Switch
              checked={settings.shuffleOrder}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, shuffleOrder: checked }))}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label>Auto Refill</Label>
            <Switch
              checked={settings.autoRefill}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, autoRefill: checked }))}
            />
          </div>

          {settings.autoRefill && (
            <div className="space-y-3">
              <Label>Check Interval (minutes)</Label>
              <Input
                type="number"
                value={settings.refillInterval}
                onChange={(e) => setSettings(prev => ({ 
                  ...prev, 
                  refillInterval: parseInt(e.target.value) || 5 
                }))}
                min="1"
                max="60"
              />
            </div>
          )}
        </div>

        {/* Status Info */}
        {lastFillTime && (
          <div className="text-sm text-gray-400 pt-4 border-t border-white/10">
            Last filled: {lastFillTime.toLocaleTimeString()}
          </div>
        )}

        {/* Library Stats */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-white/10">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">{libraryStats.total.toLocaleString()}</div>
            <div className="text-sm text-gray-400">Total Songs</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-pink-400">{libraryStats.favorites.toLocaleString()}</div>
            <div className="text-sm text-gray-400">Favorites</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}