"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { toast } from "sonner"
import { 
  List, 
  Plus, 
  Music, 
  Clock, 
  Loader2, 
  RefreshCw,
  PlayCircle,
  Trash2,
  Eye
} from "lucide-react"
import type { MPDPlaylist, MPDTrack } from "@/lib/types"

interface PlaylistQueueManagerProps {
  userRole: string
  userId?: string
  onPlaylistAdded?: () => void
}

export default function PlaylistQueueManager({ userRole, userId, onPlaylistAdded }: PlaylistQueueManagerProps) {
  const [playlists, setPlaylists] = useState<MPDPlaylist[]>([])
  const [selectedPlaylist, setSelectedPlaylist] = useState<string>("")
  const [playlistTracks, setPlaylistTracks] = useState<MPDTrack[]>([])
  const [clearQueue, setClearQueue] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingTracks, setIsLoadingTracks] = useState(false)
  const [isAddingToQueue, setIsAddingToQueue] = useState(false)
  const [showTrackPreview, setShowTrackPreview] = useState(false)

  // Check if user has admin/DJ privileges
  const hasPlaylistAccess = userRole === 'superuser' || userRole === 'dj'

  // Load playlists on component mount
  useEffect(() => {
    if (hasPlaylistAccess) {
      loadPlaylists()
    }
  }, [hasPlaylistAccess])

  // Load playlist tracks when selection changes
  useEffect(() => {
    if (selectedPlaylist) {
      loadPlaylistTracks(selectedPlaylist)
    } else {
      setPlaylistTracks([])
    }
  }, [selectedPlaylist])

  const loadPlaylists = async () => {
    setIsLoading(true)
    try {
      // Get auth token
      const { ClientTokenManager } = await import('@/lib/client-auth')
      const token = ClientTokenManager.getToken()
      
      const headers: HeadersInit = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch('/api/mpd/playlists', { headers })
      const data = await response.json()
      
      if (data.success) {
        setPlaylists(data.playlists || [])
        console.log(`[PlaylistManager] Loaded ${data.playlists?.length || 0} playlists`)
      } else {
        toast.error('Failed to load playlists')
        console.error('[PlaylistManager] Failed to load playlists:', data.message)
      }
    } catch (error) {
      toast.error('Failed to connect to music server')
      console.error('[PlaylistManager] Error loading playlists:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadPlaylistTracks = async (playlistName: string) => {
    setIsLoadingTracks(true)
    try {
      // Get auth token
      const { ClientTokenManager } = await import('@/lib/client-auth')
      const token = ClientTokenManager.getToken()
      
      const headers: HeadersInit = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch(`/api/mpd/playlists?playlist=${encodeURIComponent(playlistName)}`, { headers })
      const data = await response.json()
      
      if (data.success) {
        setPlaylistTracks(data.tracks || [])
        console.log(`[PlaylistManager] Loaded ${data.tracks?.length || 0} tracks from playlist "${playlistName}"`)
      } else {
        toast.error(`Failed to load tracks from playlist "${playlistName}"`)
        console.error('[PlaylistManager] Failed to load playlist tracks:', data.message)
      }
    } catch (error) {
      toast.error('Failed to load playlist tracks')
      console.error('[PlaylistManager] Error loading playlist tracks:', error)
    } finally {
      setIsLoadingTracks(false)
    }
  }

  const addPlaylistToQueue = async () => {
    if (!selectedPlaylist) {
      toast.error('Please select a playlist first')
      return
    }

    setIsAddingToQueue(true)
    try {
      // Get auth token
      const { ClientTokenManager } = await import('@/lib/client-auth')
      const token = ClientTokenManager.getToken()
      
      const headers: HeadersInit = { 'Content-Type': 'application/json' }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch('/api/mpd/playlists', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          playlistName: selectedPlaylist,
          clearQueue,
          userId,
          userRole
        }),
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success(data.message)
        console.log(`[PlaylistManager] Successfully added playlist to queue:`, data)
        
        // Reset selection and notify parent
        setSelectedPlaylist("")
        setPlaylistTracks([])
        onPlaylistAdded?.()
      } else {
        toast.error(data.message || 'Failed to add playlist to queue')
        console.error('[PlaylistManager] Failed to add playlist to queue:', data.message)
      }
    } catch (error) {
      toast.error('Failed to add playlist to queue')
      console.error('[PlaylistManager] Error adding playlist to queue:', error)
    } finally {
      setIsAddingToQueue(false)
    }
  }

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getTotalDuration = (tracks: MPDTrack[]): string => {
    const totalSeconds = tracks.reduce((sum, track) => sum + (track.time || 0), 0)
    const hours = Math.floor(totalSeconds / 3600)
    const mins = Math.floor((totalSeconds % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  if (!hasPlaylistAccess) {
    return null
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-1 sm:gap-2">
          <List className="w-4 h-4" />
          <span className="hidden sm:inline">Quick Add Playlist</span>
          <span className="sm:hidden">Playlist</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <List className="w-5 h-5" />
            Quick Add Playlist to Queue
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Playlist Selection */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="playlist-select">Select Playlist</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={loadPlaylists}
                disabled={isLoading}
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
            <Select value={selectedPlaylist} onValueChange={setSelectedPlaylist} disabled={isLoading}>
              <SelectTrigger>
                <SelectValue placeholder={isLoading ? "Loading playlists..." : "Choose a playlist"} />
              </SelectTrigger>
              <SelectContent>
                {playlists.map((playlist) => (
                  <SelectItem key={playlist.name} value={playlist.name}>
                    <div className="flex items-center justify-between w-full">
                      <span>{playlist.name}</span>
                      <Badge variant="secondary" className="ml-2">
                        {new Date(playlist.last_modified).toLocaleDateString()}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Options */}
          <div className="flex items-center space-x-2">
            <Switch
              id="clear-queue"
              checked={clearQueue}
              onCheckedChange={setClearQueue}
            />
            <Label htmlFor="clear-queue">Clear current queue first</Label>
          </div>

          {/* Playlist Preview */}
          {selectedPlaylist && (
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Music className="w-5 h-5" />
                    {selectedPlaylist}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowTrackPreview(!showTrackPreview)}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    {showTrackPreview ? 'Hide' : 'Show'} Tracks
                  </Button>
                </div>
                {playlistTracks.length > 0 && (
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <Music className="w-4 h-4" />
                      {playlistTracks.length} tracks
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {getTotalDuration(playlistTracks)}
                    </span>
                  </div>
                )}
              </CardHeader>
              
              {showTrackPreview && (
                <CardContent className="pt-0">
                  <ScrollArea className="h-48">
                    {isLoadingTracks ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="w-6 h-6 animate-spin" />
                        <span className="ml-2">Loading tracks...</span>
                      </div>
                    ) : playlistTracks.length > 0 ? (
                      <div className="space-y-2">
                        {playlistTracks.map((track, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                            <div className="min-w-0 flex-1">
                              <p className="font-medium truncate">
                                {track.title || 'Unknown Title'}
                              </p>
                              <p className="text-sm text-gray-500 truncate">
                                {track.artist || 'Unknown Artist'}
                              </p>
                            </div>
                            <div className="text-sm text-gray-500 ml-2">
                              {formatDuration(track.time || 0)}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        No tracks found in this playlist
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              )}
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              onClick={addPlaylistToQueue}
              disabled={!selectedPlaylist || isAddingToQueue || playlistTracks.length === 0}
              className="gap-2"
            >
              {isAddingToQueue ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : clearQueue ? (
                <Trash2 className="w-4 h-4" />
              ) : (
                <Plus className="w-4 h-4" />
              )}
              {isAddingToQueue 
                ? 'Adding...' 
                : clearQueue 
                  ? `Replace Queue (${playlistTracks.length} tracks)`
                  : `Add to Queue (${playlistTracks.length} tracks)`
              }
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 