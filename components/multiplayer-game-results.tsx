"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Trophy, 
  Medal, 
  Award, 
  Crown,
  Zap,
  Target,
  Clock,
  Music,
  Users,
  TrendingUp,
  Star,
  Home,
  RotateCcw
} from "lucide-react"
import { motion } from "framer-motion"
import confetti from "canvas-confetti"

interface Player {
  id: string
  name: string
  score: number
  position?: number
  isYou?: boolean
  correctAnswers?: number
  avgResponseTime?: number
  longestStreak?: number
}

interface GameStats {
  totalQuestions: number
  gameMode: string
  duration?: number
  quickestAnswer?: {
    player: string
    time: number
    question: string
  }
  mostAccurate?: {
    player: string
    accuracy: number
  }
  longestStreak?: {
    player: string
    streak: number
  }
  perfectRounds?: string[]
}

interface MultiplayerGameResultsProps {
  leaderboard: Player[]
  playerPosition: number
  playerScore: number
  gameStats?: GameStats
  onPlayAgain: () => void
  onBackToMenu: () => void
}

export function MultiplayerGameResults({
  leaderboard,
  playerPosition,
  playerScore,
  gameStats,
  onPlayAgain,
  onBackToMenu
}: MultiplayerGameResultsProps) {
  const [showConfetti, setShowConfetti] = useState(false)

  // Trigger confetti for top 3 finish
  useEffect(() => {
    if (playerPosition <= 3 && playerPosition > 0) {
      setShowConfetti(true)
      const duration = playerPosition === 1 ? 3000 : 2000
      const particleCount = playerPosition === 1 ? 200 : 100

      const fire = () => {
        confetti({
          particleCount,
          spread: 70,
          origin: { y: 0.6 },
          colors: ['#FFD700', '#FFA500', '#FF6347', '#4169E1', '#32CD32']
        })
      }

      fire()
      if (playerPosition === 1) {
        // Extra confetti for winner
        setTimeout(fire, 250)
        setTimeout(fire, 500)
      }

      setTimeout(() => setShowConfetti(false), duration)
    }
  }, [playerPosition])

  const getPositionIcon = (position: number) => {
    switch (position) {
      case 1:
        return <Crown className="h-6 w-6 text-yellow-500" />
      case 2:
        return <Medal className="h-6 w-6 text-gray-400" />
      case 3:
        return <Award className="h-6 w-6 text-orange-600" />
      default:
        return <span className="text-2xl font-bold">{position}</span>
    }
  }

  const getPositionColor = (position: number) => {
    switch (position) {
      case 1:
        return "from-yellow-500 to-amber-500"
      case 2:
        return "from-gray-400 to-gray-500"
      case 3:
        return "from-orange-500 to-orange-600"
      default:
        return "from-blue-500 to-purple-500"
    }
  }

  const getPositionMessage = (position: number) => {
    switch (position) {
      case 1:
        return "🎉 Victory! You are the champion!"
      case 2:
        return "🥈 Excellent! So close to victory!"
      case 3:
        return "🥉 Great job! You made the podium!"
      default:
        return `Good effort! You finished in ${position}${getOrdinalSuffix(position)} place`
    }
  }

  const getOrdinalSuffix = (num: number) => {
    const j = num % 10
    const k = num % 100
    if (j === 1 && k !== 11) return "st"
    if (j === 2 && k !== 12) return "nd"
    if (j === 3 && k !== 13) return "rd"
    return "th"
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="max-w-4xl mx-auto space-y-6 p-4"
    >
      {/* Player Result Card */}
      <motion.div
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className={`relative overflow-hidden ${playerPosition <= 3 ? 'border-2 border-yellow-400' : ''}`}>
          <div className={`absolute inset-0 bg-gradient-to-br ${getPositionColor(playerPosition)} opacity-10`} />
          <CardHeader className="relative">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {getPositionIcon(playerPosition)}
                <div>
                  <CardTitle className="text-3xl">Game Complete!</CardTitle>
                  <p className="text-lg text-muted-foreground mt-1">
                    {getPositionMessage(playerPosition)}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-4xl font-bold">{playerScore}</p>
                <p className="text-sm text-muted-foreground">points</p>
              </div>
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Game Statistics */}
      {gameStats && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Game Highlights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {gameStats.quickestAnswer && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Zap className="h-4 w-4" />
                      Fastest Answer
                    </div>
                    <p className="font-semibold">{gameStats.quickestAnswer.player}</p>
                    <p className="text-sm text-muted-foreground">
                      {gameStats.quickestAnswer.time}s
                    </p>
                  </div>
                )}
                
                {gameStats.mostAccurate && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Target className="h-4 w-4" />
                      Most Accurate
                    </div>
                    <p className="font-semibold">{gameStats.mostAccurate.player}</p>
                    <p className="text-sm text-muted-foreground">
                      {Math.round(gameStats.mostAccurate.accuracy)}% correct
                    </p>
                  </div>
                )}
                
                {gameStats.longestStreak && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Star className="h-4 w-4" />
                      Longest Streak
                    </div>
                    <p className="font-semibold">{gameStats.longestStreak.player}</p>
                    <p className="text-sm text-muted-foreground">
                      {gameStats.longestStreak.streak} in a row
                    </p>
                  </div>
                )}
              </div>

              {gameStats.perfectRounds && gameStats.perfectRounds.length > 0 && (
                <div className="mt-4 pt-4 border-t">
                  <p className="text-sm text-muted-foreground mb-2">
                    Perfect Rounds by: {gameStats.perfectRounds.join(", ")}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Final Leaderboard */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Final Leaderboard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {leaderboard.map((player, index) => (
                <motion.div
                  key={player.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 + index * 0.1 }}
                  className={`
                    flex items-center justify-between p-4 rounded-lg border
                    ${player.isYou ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-500' : 'bg-gray-50 dark:bg-gray-800/50'}
                    ${player.position === 1 ? 'border-yellow-400' : ''}
                    ${player.position === 2 ? 'border-gray-400' : ''}
                    ${player.position === 3 ? 'border-orange-400' : ''}
                  `}
                >
                  <div className="flex items-center gap-4">
                    <div className="flex items-center justify-center w-12 h-12">
                      {getPositionIcon(player.position || index + 1)}
                    </div>
                    <div>
                      <p className="font-semibold flex items-center gap-2">
                        {player.name}
                        {player.isYou && (
                          <Badge variant="secondary" className="text-xs">You</Badge>
                        )}
                      </p>
                      <div className="flex gap-4 text-sm text-muted-foreground">
                        {player.correctAnswers !== undefined && (
                          <span>{player.correctAnswers} correct</span>
                        )}
                        {player.avgResponseTime !== undefined && (
                          <span>{player.avgResponseTime.toFixed(1)}s avg</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold">{player.score}</p>
                    <p className="text-sm text-muted-foreground">points</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.9 }}
        className="flex gap-4 justify-center"
      >
        <Button
          onClick={onBackToMenu}
          variant="outline"
          size="lg"
          className="gap-2"
        >
          <Home className="h-5 w-5" />
          Back to Menu
        </Button>
        <Button
          onClick={onPlayAgain}
          size="lg"
          className="gap-2"
        >
          <RotateCcw className="h-5 w-5" />
          Play Again
        </Button>
      </motion.div>
    </motion.div>
  )
}