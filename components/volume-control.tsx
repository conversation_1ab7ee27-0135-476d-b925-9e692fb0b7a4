"use client"

import { useState } from "react"
import { Volume2, VolumeX } from "lucide-react"
import { Slider } from "@/components/ui/slider"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export function VolumeControl() {
  const [volume, setVolume] = useState(50)
  const [muted, setMuted] = useState(false)

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0])
    setMuted(value[0] === 0)
    // TODO: Actually control audio volume
  }

  const toggleMute = () => {
    setMuted(!muted)
    // TODO: Actually mute/unmute audio
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
          {muted || volume === 0 ? (
            <VolumeX className="h-5 w-5" />
          ) : (
            <Volume2 className="h-5 w-5" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-48">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Volume</span>
            <span className="text-sm text-gray-500">{volume}%</span>
          </div>
          <Slider
            value={[muted ? 0 : volume]}
            onValueChange={handleVolumeChange}
            max={100}
            step={1}
            className="w-full"
          />
        </div>
      </PopoverContent>
    </Popover>
  )
}