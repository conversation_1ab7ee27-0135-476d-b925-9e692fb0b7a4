"use client"

import { useState, useEffect } from "react"
import { Heart } from "lucide-react"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

interface FavoriteButtonProps {
  track: {
    id?: number | string
    title: string
    artist: string
    album?: string
    file?: string
    mpdFilePath?: string
    year?: number
    genre?: string
    duration?: number
    albumArtUrl?: string
  }
  userId?: string
  className?: string
  size?: "sm" | "default" | "lg" | "icon"
  variant?: "ghost" | "outline" | "default"
}

export function FavoriteButton({ 
  track, 
  userId = "default-user", 
  className,
  size = "icon",
  variant = "ghost"
}: FavoriteButtonProps) {
  // Check if this is a multiplayer session (player IDs start with "player_")
  const isMultiplayerSession = userId.startsWith("player_")
  const [isFavorite, setIsFavorite] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const filePath = track.file || track.mpdFilePath

  // Check if track is already favorited (skip for multiplayer sessions)
  useEffect(() => {
    if (!filePath || isMultiplayerSession) return

    const checkFavorite = async () => {
      try {
        const response = await fetch(`/api/jukebox/favorites?userId=${userId}`)
        if (response.ok) {
          const data = await response.json()
          const favorites = data.favorites || []
          const isInFavorites = favorites.some((fav: any) => fav.filePath === filePath)
          setIsFavorite(isInFavorites)
        }
      } catch (error) {
        console.error("Failed to check favorite status:", error)
      }
    }

    checkFavorite()
  }, [filePath, userId, isMultiplayerSession])

  const toggleFavorite = async () => {
    if (!filePath || isLoading || isMultiplayerSession) {
      if (isMultiplayerSession) {
        toast.info("Favorites are not available in multiplayer mode")
      }
      return
    }

    setIsLoading(true)
    try {
      if (isFavorite) {
        // Remove from favorites
        const response = await fetch(`/api/jukebox/favorites?userId=${userId}&filePath=${encodeURIComponent(filePath)}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          setIsFavorite(false)
          toast.success("Removed from favorites")
        } else {
          const data = await response.json()
          toast.error(data.message || "Failed to remove from favorites")
        }
      } else {
        // Add to favorites
        const response = await fetch('/api/jukebox/favorites', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId,
            song: {
              title: track.title,
              artist: track.artist,
              album: track.album,
              filePath: filePath,
              year: track.year,
              genre: track.genre,
              duration: track.duration,
              albumArtUrl: track.albumArtUrl
            }
          })
        })

        if (response.ok) {
          setIsFavorite(true)
          toast.success("Added to favorites")
        } else {
          const data = await response.json()
          if (data.alreadyExists) {
            setIsFavorite(true)
            toast.info("Already in favorites")
          } else {
            toast.error(data.message || "Failed to add to favorites")
          }
        }
      }
    } catch (error) {
      console.error("Failed to toggle favorite:", error)
      toast.error("Failed to update favorites")
    } finally {
      setIsLoading(false)
    }
  }

  if (!filePath) {
    return null // Don't show button if there's no file path
  }
  
  // Show disabled button for multiplayer sessions
  if (isMultiplayerSession) {
    return (
      <Button
        onClick={toggleFavorite}
        disabled={true}
        size={size}
        variant={variant}
        className={cn(
          "transition-colors opacity-50 cursor-not-allowed",
          className
        )}
        title="Favorites are not available in multiplayer mode"
      >
        <Heart className={cn("h-4 w-4", size === "lg" && "h-5 w-5")} />
        {size !== "icon" && (
          <span className="ml-2">Favorite</span>
        )}
      </Button>
    )
  }

  return (
    <Button
      onClick={toggleFavorite}
      disabled={isLoading}
      size={size}
      variant={variant}
      className={cn(
        "transition-colors",
        isFavorite && "text-red-500 hover:text-red-600",
        className
      )}
      title={isFavorite ? "Remove from favorites" : "Add to favorites"}
    >
      <Heart 
        className={cn(
          "h-4 w-4",
          size === "lg" && "h-5 w-5",
          isFavorite && "fill-current"
        )} 
      />
      {size !== "icon" && (
        <span className="ml-2">
          {isFavorite ? "Favorited" : "Favorite"}
        </span>
      )}
    </Button>
  )
}