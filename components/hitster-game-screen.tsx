"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON>ton } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Users, Trophy, Clock, Music } from "lucide-react"
import { motion } from "framer-motion"
import { HitsterTimeline } from "./hitster-timeline"
import { HitsterTeamFormation } from "./hitster-team-formation"
// import { useMultiplayerSocket } from "@/hooks/use-multiplayer-socket" // Removed - using unified multiplayer
import type { Team, HitsterGameState, TimelinePlacement } from "@/lib/types"
import { VolumeControl } from "./volume-control"
import { useRouter } from "next/navigation"

interface HitsterGameScreenProps {
  gameCode: string
  playerId: string
  playerName: string
  isHost: boolean
  onBackToLobby: () => void
}

export function HitsterGameScreen({
  gameCode,
  playerId,
  playerName,
  isHost,
  onBackToLobby
}: HitsterGameScreenProps) {
  const router = useRouter()
  const [gameState, setGameState] = useState<HitsterGameState | null>(null)
  const [teams, setTeams] = useState<Team[]>([])
  const [myTeam, setMyTeam] = useState<Team | null>(null)
  const [isInTeamFormation, setIsInTeamFormation] = useState(true)
  const [gameEnded, setGameEnded] = useState(false)
  const [finalScores, setFinalScores] = useState<Map<string, number>>(new Map())

  // const { socket } = useMultiplayerSocket() // Removed - using unified multiplayer
  const socket = null // Placeholder

  useEffect(() => {
    if (!socket) return

    // Listen for Hitster-specific events
    socket.on('hitster_round_started', (data) => {
      setGameState(prev => ({
        ...prev!,
        currentSong: data.song,
        timeline: data.timeline,
        placementPhase: true,
        revealPhase: false,
        roundNumber: data.roundNumber
      }))
    })

    socket.on('hitster_song_placed', (data) => {
      setGameState(prev => ({
        ...prev!,
        timeline: data.timeline,
        placementPhase: false,
        revealPhase: true,
        correctPlacements: prev!.correctPlacements + (data.isCorrect ? 1 : 0),
        totalPlacements: prev!.totalPlacements + 1
      }))

      // Update team tokens
      if (data.teamTokens !== undefined) {
        setGameState(prev => {
          const newTokens = new Map(prev!.tokensPerTeam)
          newTokens.set(data.entry.placedByTeamId, data.teamTokens)
          return { ...prev!, tokensPerTeam: newTokens }
        })
      }
    })

    socket.on('hitster_turn_changed', (data) => {
      setGameState(prev => ({
        ...prev!,
        currentTeamIndex: prev!.teamTurns.indexOf(data.currentTeamId),
        tokensPerTeam: data.teamTokens
      }))
    })

    socket.on('hitster_timeout', (data) => {
      // Handle timeout
      console.log('Team timeout:', data)
    })

    socket.on('hitster_game_ended', (data) => {
      setGameEnded(true)
      setFinalScores(data.scores)
      setGameState(prev => ({
        ...prev!,
        timeline: data.timeline
      }))
    })

    socket.on('team_created', (team: Team) => {
      setTeams(prev => [...prev, team])
    })

    socket.on('player_joined_team', (data) => {
      if (data.playerId === playerId) {
        const team = teams.find(t => t.id === data.teamId)
        if (team) setMyTeam(team)
      }
    })

    socket.on('game_started', () => {
      setIsInTeamFormation(false)
      // Initialize game state
      setGameState({
        timeline: [],
        currentTeamIndex: 0,
        teamTurns: teams.map(t => t.id),
        currentSong: null,
        placementPhase: false,
        revealPhase: false,
        correctPlacements: 0,
        totalPlacements: 0,
        tokensPerTeam: new Map(teams.map(t => [t.id, 3])),
        roundNumber: 0
      })
    })

    return () => {
      socket.off('hitster_round_started')
      socket.off('hitster_song_placed')
      socket.off('hitster_turn_changed')
      socket.off('hitster_timeout')
      socket.off('hitster_game_ended')
      socket.off('team_created')
      socket.off('player_joined_team')
      socket.off('game_started')
    }
  }, [socket, playerId, teams])

  const handlePlaceSong = (position: number) => {
    if (!socket || !gameState || !myTeam) return

    const placement: TimelinePlacement = {
      songId: gameState.currentSong?.id || '',
      position,
      teamId: myTeam.id,
      playerId,
      confidence: 80 // Could add a confidence slider
    }

    socket.emit('place_timeline_song', {
      gameId: gameCode,
      placement
    })
  }

  const handleStartGame = () => {
    if (!socket || !isHost) return
    socket.emit('start_game', { gameId: gameCode })
  }

  const isMyTurn = gameState && myTeam && 
    gameState.teamTurns[gameState.currentTeamIndex] === myTeam.id

  // Team formation phase
  if (isInTeamFormation) {
    return (
      <HitsterTeamFormation
        gameCode={gameCode}
        playerId={playerId}
        playerName={playerName}
        isHost={isHost}
        teams={teams}
        onStartGame={handleStartGame}
        onBackToLobby={onBackToLobby}
      />
    )
  }

  // Game ended
  if (gameEnded) {
    const sortedScores = Array.from(finalScores.entries())
      .sort(([, a], [, b]) => b - a)

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4"
      >
        <div className="max-w-4xl mx-auto space-y-6">
          <Card className="bg-white/10 backdrop-blur-lg border-gray-700/30 p-8">
            <div className="text-center space-y-6">
              <Trophy className="h-16 w-16 mx-auto text-yellow-400" />
              <h1 className="text-4xl font-bold text-white">Game Over!</h1>
              
              <div className="space-y-4">
                {sortedScores.map(([teamId, score], index) => {
                  const team = teams.find(t => t.id === teamId)
                  if (!team) return null
                  
                  return (
                    <motion.div
                      key={teamId}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={cn(
                        "p-4 rounded-lg",
                        index === 0 ? "bg-yellow-500/20 border-2 border-yellow-500/50" :
                        index === 1 ? "bg-gray-400/20 border-2 border-gray-400/50" :
                        index === 2 ? "bg-orange-500/20 border-2 border-orange-500/50" :
                        "bg-white/10 border border-gray-700/30"
                      )}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <span className="text-2xl">
                            {index === 0 ? "🥇" : index === 1 ? "🥈" : index === 2 ? "🥉" : `#${index + 1}`}
                          </span>
                          <span className="text-lg font-medium text-white">{team.name}</span>
                        </div>
                        <span className="text-2xl font-bold text-white">{score}</span>
                      </div>
                    </motion.div>
                  )
                })}
              </div>

              <div className="pt-6 space-y-4">
                <Button
                  onClick={() => router.push('/music')}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600"
                >
                  Back to Menu
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </motion.div>
    )
  }

  // Main game
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4"
    >
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={onBackToLobby}
            className="text-gray-400 hover:text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Leave Game
          </Button>
          
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="text-gray-400">
              {gameCode}
            </Badge>
            <VolumeControl />
          </div>
        </div>

        {/* Game title */}
        <Card className="bg-white/10 backdrop-blur-lg border-gray-700/30 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Clock className="h-8 w-8 text-indigo-400" />
              <div>
                <h1 className="text-2xl font-bold text-white">Hitster Timeline</h1>
                <p className="text-gray-400">Place songs in chronological order</p>
              </div>
            </div>
            
            {myTeam && (
              <div className="text-right">
                <p className="text-sm text-gray-400">Your Team</p>
                <p className="text-lg font-medium text-white">{myTeam.name}</p>
              </div>
            )}
          </div>
        </Card>

        {/* Timeline game */}
        {gameState && (
          <Card className="bg-white/10 backdrop-blur-lg border-gray-700/30 p-6">
            <HitsterTimeline
              gameState={gameState}
              currentTeam={myTeam}
              teams={teams}
              onPlaceSong={handlePlaceSong}
              isMyTurn={isMyTurn || false}
            />
          </Card>
        )}
      </div>
    </motion.div>
  )
}

function cn(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(' ')
}