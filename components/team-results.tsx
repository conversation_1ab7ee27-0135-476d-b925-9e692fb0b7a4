"use client"

import React, { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Trophy, Crown, Medal, Star, TrendingUp, 
  Users, Target, MessageCircle, Zap, Award,
  CheckCircle2, ArrowUp, ArrowDown, Minus,
  Clock, Timer, Brain, Heart, Share2
} from "lucide-react"
import { Team, Player, TeamGameSettings, TeamGameMode } from "@/lib/types"
import { ScoreResult } from "@/lib/team-scoring"

interface TeamResultsProps {
  teams: Team[]
  finalScores: ScoreResult[]
  gameSettings: TeamGameSettings
  gameDuration: number
  totalQuestions: number
  individualContributions: Map<string, { contribution: number; percentage: number }>
  teamAchievements: Map<string, string[]>
  onPlayAgain: () => void
  onBackToMenu: () => void
  onShareResults: () => void
  className?: string
}

interface TeamAchievement {
  id: string
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

const TEAM_ACHIEVEMENTS: TeamAchievement[] = [
  {
    id: 'perfect-team',
    name: 'Perfect Harmony',
    description: 'All team members answered correctly',
    icon: CheckCircle2,
    rarity: 'legendary'
  },
  {
    id: 'speed-demons',
    name: 'Speed Demons',
    description: 'Answered 5 questions under 10 seconds',
    icon: Timer,
    rarity: 'rare'
  },
  {
    id: 'comeback-kings',
    name: 'Comeback Kings',
    description: 'Came back from last place to win',
    icon: TrendingUp,
    rarity: 'epic'
  },
  {
    id: 'team-spirit',
    name: 'Team Spirit',
    description: 'Sent 50+ team chat messages',
    icon: MessageCircle,
    rarity: 'common'
  },
  {
    id: 'specialist-mastery',
    name: 'Specialist Mastery',
    description: 'Perfect score in specialist mode',
    icon: Target,
    rarity: 'epic'
  },
  {
    id: 'relay-masters',
    name: 'Relay Masters',
    description: 'Flawless relay coordination',
    icon: Zap,
    rarity: 'rare'
  },
  {
    id: 'collaborative-genius',
    name: 'Collaborative Genius',
    description: 'Perfect teamwork in collaborative mode',
    icon: Brain,
    rarity: 'rare'
  },
  {
    id: 'underdog-victory',
    name: 'Underdog Victory',
    description: 'Won with smallest team',
    icon: Heart,
    rarity: 'epic'
  }
]

export function TeamResults({
  teams,
  finalScores,
  gameSettings,
  gameDuration,
  totalQuestions,
  individualContributions,
  teamAchievements,
  onPlayAgain,
  onBackToMenu,
  onShareResults,
  className = ""
}: TeamResultsProps) {
  const [selectedTeam, setSelectedTeam] = useState<string | null>(null)
  const [showIndividualStats, setShowIndividualStats] = useState(false)
  
  // Sort teams by final score
  const sortedTeams = [...teams].sort((a, b) => b.score - a.score)
  
  // Get team with most improvement
  const getTeamImprovement = (team: Team) => {
    // This would be calculated based on score progression
    // For now, return a mock improvement
    return Math.floor(Math.random() * 200) - 100
  }
  
  // Get game mode icon
  const getGameModeIcon = (mode: TeamGameMode) => {
    switch (mode) {
      case 'collaborative': return MessageCircle
      case 'relay': return Zap
      case 'specialist': return Target
      default: return Users
    }
  }
  
  // Get rarity color
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-500'
      case 'rare': return 'bg-blue-500'
      case 'epic': return 'bg-purple-500'
      case 'legendary': return 'bg-yellow-500'
      default: return 'bg-gray-500'
    }
  }
  
  // Calculate team stats
  const calculateTeamStats = (team: Team) => {
    const teamScore = finalScores.find(s => s.teamId === team.id)
    const accuracy = totalQuestions > 0 ? (team.score / (totalQuestions * 100)) * 100 : 0
    const avgResponseTime = 15 // Mock data
    const improvement = getTeamImprovement(team)
    
    return {
      accuracy: Math.min(100, Math.max(0, accuracy)),
      avgResponseTime,
      improvement,
      totalBonus: teamScore?.bonusPoints || 0
    }
  }

  return (
    <div className={`max-w-6xl mx-auto p-6 space-y-6 ${className}`}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Trophy className="h-8 w-8 text-yellow-500" />
          Team Results
        </h1>
        <p className="text-muted-foreground">
          {gameSettings.teamGameMode} mode • {Math.floor(gameDuration / 60)}m {gameDuration % 60}s
        </p>
      </motion.div>

      {/* Winners Podium */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="border-2 border-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Crown className="h-6 w-6 text-yellow-500" />
              Victory Podium
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center items-end gap-4">
              {sortedTeams.slice(0, 3).map((team, index) => (
                <motion.div
                  key={team.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 + index * 0.1 }}
                  className={`text-center ${
                    index === 0 ? 'order-2' : index === 1 ? 'order-1' : 'order-3'
                  }`}
                >
                  <div
                    className={`w-20 h-20 rounded-full flex items-center justify-center text-2xl mb-2 ${
                      index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : 'bg-amber-600'
                    }`}
                  >
                    {team.emoji}
                  </div>
                  <div className="space-y-1">
                    <div className="font-semibold">{team.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {team.score} points
                    </div>
                    <Badge
                      variant={index === 0 ? "default" : "secondary"}
                      className={`text-xs ${
                        index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : 'bg-amber-600'
                      }`}
                    >
                      {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'} {
                        ['1st', '2nd', '3rd'][index]
                      }
                    </Badge>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Team Leaderboard */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left: Team Rankings */}
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Final Standings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {sortedTeams.map((team, index) => {
                const stats = calculateTeamStats(team)
                const teamScore = finalScores.find(s => s.teamId === team.id)
                
                return (
                  <motion.div
                    key={team.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 * index }}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      selectedTeam === team.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedTeam(selectedTeam === team.id ? null : team.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Badge 
                          variant={index === 0 ? "default" : "secondary"}
                          className="w-8 h-8 p-0 text-sm"
                        >
                          {index + 1}
                        </Badge>
                        <div className="text-2xl">{team.emoji}</div>
                        <div>
                          <div className="font-semibold">{team.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {team.players.length} members
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="font-bold text-lg">{team.score}</div>
                        <div className="text-sm text-muted-foreground">
                          {stats.improvement > 0 && (
                            <span className="text-green-600">
                              <ArrowUp className="h-3 w-3 inline mr-1" />
                              +{stats.improvement}
                            </span>
                          )}
                          {stats.improvement < 0 && (
                            <span className="text-red-600">
                              <ArrowDown className="h-3 w-3 inline mr-1" />
                              {stats.improvement}
                            </span>
                          )}
                          {stats.improvement === 0 && (
                            <span className="text-gray-600">
                              <Minus className="h-3 w-3 inline mr-1" />
                              0
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {/* Expanded team details */}
                    <AnimatePresence>
                      {selectedTeam === team.id && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          className="mt-4 pt-4 border-t"
                        >
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div>
                              <div className="text-sm font-medium">Accuracy</div>
                              <div className="flex items-center gap-2">
                                <Progress value={stats.accuracy} className="flex-1" />
                                <span className="text-sm">{stats.accuracy.toFixed(1)}%</span>
                              </div>
                            </div>
                            <div>
                              <div className="text-sm font-medium">Avg Response Time</div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span className="text-sm">{stats.avgResponseTime}s</span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            <div className="text-sm font-medium">Team Members</div>
                            <div className="grid grid-cols-2 gap-2">
                              {team.players.map((player) => {
                                const contribution = individualContributions.get(player.id)
                                return (
                                  <div key={player.id} className="flex items-center gap-2 text-sm">
                                    <Avatar className="h-6 w-6">
                                      <AvatarImage src={player.avatar} />
                                      <AvatarFallback className="text-xs">
                                        {player.name.slice(0, 2).toUpperCase()}
                                      </AvatarFallback>
                                    </Avatar>
                                    <span>{player.name}</span>
                                    {player.isTeamCaptain && (
                                      <Crown className="h-3 w-3 text-yellow-500" />
                                    )}
                                    {contribution && (
                                      <Badge variant="outline" className="text-xs">
                                        {contribution.contribution} pts
                                      </Badge>
                                    )}
                                  </div>
                                )
                              })}
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                )
              })}
            </CardContent>
          </Card>
        </div>

        {/* Right: Game Stats & Achievements */}
        <div className="space-y-4">
          {/* Game Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {React.createElement(getGameModeIcon(gameSettings.teamGameMode), { className: "h-5 w-5" })}
                Game Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Game Mode</span>
                  <span className="font-medium capitalize">{gameSettings.teamGameMode}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Scoring Mode</span>
                  <span className="font-medium capitalize">{gameSettings.teamScoringMode}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Duration</span>
                  <span className="font-medium">
                    {Math.floor(gameDuration / 60)}m {gameDuration % 60}s
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Questions</span>
                  <span className="font-medium">{totalQuestions}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Teams</span>
                  <span className="font-medium">{teams.length}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Total Players</span>
                  <span className="font-medium">
                    {teams.reduce((sum, team) => sum + team.players.length, 0)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Team Achievements */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Team Achievements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Array.from(teamAchievements.entries()).map(([teamId, achievements]) => {
                  const team = teams.find(t => t.id === teamId)
                  if (!team || achievements.length === 0) return null
                  
                  return (
                    <div key={teamId} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{team.emoji}</span>
                        <span className="font-medium">{team.name}</span>
                      </div>
                      <div className="space-y-1">
                        {achievements.map((achievementId) => {
                          const achievement = TEAM_ACHIEVEMENTS.find(a => a.id === achievementId)
                          if (!achievement) return null
                          
                          return (
                            <div key={achievementId} className="flex items-center gap-2 text-sm">
                              <div className={`w-2 h-2 rounded-full ${getRarityColor(achievement.rarity)}`} />
                              {React.createElement(achievement.icon, { className: "h-3 w-3" })}
                              <span>{achievement.name}</span>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )
                })}
                
                {teamAchievements.size === 0 && (
                  <div className="text-center text-muted-foreground py-4">
                    <Award className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No team achievements unlocked</p>
                    <p className="text-xs">Play more games to unlock team achievements!</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="flex justify-center gap-4"
      >
        <Button onClick={onShareResults} variant="outline" className="flex items-center gap-2">
          <Share2 className="h-4 w-4" />
          Share Results
        </Button>
        <Button onClick={onPlayAgain} className="flex items-center gap-2">
          <Trophy className="h-4 w-4" />
          Play Again
        </Button>
        <Button onClick={onBackToMenu} variant="outline">
          Back to Menu
        </Button>
      </motion.div>
    </div>
  )
}