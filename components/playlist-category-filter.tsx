"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"

// Common music quiz categories
const COMMON_CATEGORIES = [
  { value: "all-time-favorites", label: "All Time Favorites", emoji: "⭐" },
  { value: "chart-hits", label: "Chart Hits", emoji: "📈" },
  { value: "popular-hits", label: "Popular Hits", emoji: "🎵" },
  { value: "german-charts", label: "German Charts", emoji: "🇩🇪" },
  { value: "soccer-songs", label: "Soccer Songs", emoji: "⚽" },
  { value: "movie-soundtracks", label: "Movie Soundtracks", emoji: "🎬" },
  { value: "dance", label: "Dance", emoji: "💃" },
  { value: "rock", label: "Rock", emoji: "🎸" },
  { value: "pop", label: "Pop", emoji: "🎤" },
  { value: "rap/hip hop", label: "Rap/Hip Hop", emoji: "🎤" },
  { value: "alternative", label: "Alternative", emoji: "🎶" },
  { value: "r&b", label: "R&B", emoji: "🎶" }
]

interface PlaylistCategoryFilterProps {
  selectedCategories: string[]
  onCategoriesChange: (categories: string[]) => void
  includeMyItunes?: boolean
  onMyItunesChange?: (include: boolean) => void
}

export function PlaylistCategoryFilter({ 
  selectedCategories, 
  onCategoriesChange,
  includeMyItunes = true,
  onMyItunesChange
}: PlaylistCategoryFilterProps) {
  const handleCategoryToggle = (category: string) => {
    if (selectedCategories.includes(category)) {
      onCategoriesChange(selectedCategories.filter(c => c !== category))
    } else {
      onCategoriesChange([...selectedCategories, category])
    }
  }

  return (
    <div className="space-y-4">
      <div>
        <Label className="text-base font-semibold">Categories & Playlists</Label>
        <p className="text-sm text-muted-foreground mt-1">
          Select categories to include in your quiz
        </p>
      </div>

      <ScrollArea className="h-64 rounded-md border p-4">
        <div className="space-y-2">
          {COMMON_CATEGORIES.map(category => (
            <div key={category.value} className="flex items-center space-x-2">
              <Checkbox
                id={category.value}
                checked={selectedCategories.includes(category.value)}
                onCheckedChange={() => handleCategoryToggle(category.value)}
              />
              <Label
                htmlFor={category.value}
                className="flex items-center gap-2 cursor-pointer"
              >
                <span>{category.emoji}</span>
                <span>{category.label}</span>
              </Label>
            </div>
          ))}
        </div>
      </ScrollArea>

      {onMyItunesChange && (
        <div className="border-t pt-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="myitunes"
              checked={includeMyItunes}
              onCheckedChange={(checked) => onMyItunesChange(checked as boolean)}
            />
            <Label htmlFor="myitunes" className="cursor-pointer">
              Include MyItunes Library
            </Label>
          </div>
          <p className="text-xs text-muted-foreground mt-1 ml-6">
            Include songs from your personal iTunes library
          </p>
        </div>
      )}

      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {selectedCategories.map(cat => {
            const category = COMMON_CATEGORIES.find(c => c.value === cat)
            return (
              <Badge key={cat} variant="secondary" className="text-xs">
                {category?.emoji} {category?.label || cat}
              </Badge>
            )
          })}
        </div>
      )}
    </div>
  )
}