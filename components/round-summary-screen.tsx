"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Lightbulb, CheckCircle, XCircle, ArrowRight, Trophy, Music, Heart } from "lucide-react"
import type { Track } from "@/lib/music-database"

interface PlayerScore {
  name: string
  score: number
  isCurrentUser?: boolean
  avatar?: string // HINT FOR AI/Dev: This would ideally come from a user profile system.
}

interface RoundSummaryScreenProps {
  track: Track
  isCorrect: boolean
  playerScore: number
  questionPoints: number
  multiplayerScores?: PlayerScore[]
  feedbackDetail?: string
  onContinue: () => void
  onFavorite?: () => Promise<boolean>
  showFavoriteButton?: boolean
  isMultiplayer?: boolean
  waitingForServer?: boolean
}

const containerVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94] as const,
      staggerChildren: 0.07,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 15 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.25, ease: [0.25, 0.46, 0.45, 0.94] as const } },
}

// Simple sanitizer: escape tags except allow <a href="..."> ... </a>

function sanitizeHtml(raw: string): string {
  // Escape all < >
  let escaped = raw.replace(/</g, '&lt;').replace(/>/g, '&gt;')
  // Re-enable anchor tags
  escaped = escaped.replace(/&lt;a (.*?)&gt;/g, '<a $1>').replace(/&lt;\/a&gt;/g, '</a>')
  return escaped
}

export function RoundSummaryScreen({
  track,
  isCorrect,
  playerScore,
  questionPoints,
  multiplayerScores,
  feedbackDetail,
  onContinue,
  onFavorite,
  showFavoriteButton = false,
  isMultiplayer = false,
  waitingForServer = false,
}: RoundSummaryScreenProps) {
  const [countdown, setCountdown] = useState(10)
  const [currentTriviaIndex, setCurrentTriviaIndex] = useState(0)
  const [isFavorited, setIsFavorited] = useState(false)
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const triviaTimerRef = useRef<NodeJS.Timeout | null>(null)
  const hasContinuedRef = useRef(false)

  useEffect(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
    }
    if (triviaTimerRef.current) {
      clearInterval(triviaTimerRef.current)
    }
    hasContinuedRef.current = false
    setCountdown(10)
    setCurrentTriviaIndex(0)

    // Don't auto-continue in multiplayer mode - wait for server
    if (!waitingForServer) {
      timerRef.current = setInterval(() => {
        setCountdown((prevCountdown) => {
          if (prevCountdown <= 1) {
            clearInterval(timerRef.current!)
            if (!hasContinuedRef.current) {
              hasContinuedRef.current = true
              onContinue()
            }
            return 0
          }
          return prevCountdown - 1
        })
      }, 1000)
    }

    // Cycle through trivia facts every 3.5 seconds
    const triviaFacts = (track as any).triviaFacts || []
    if (triviaFacts.length > 1) {
      triviaTimerRef.current = setInterval(() => {
        setCurrentTriviaIndex((prevIndex) => (prevIndex + 1) % triviaFacts.length)
      }, 3500)
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
      if (triviaTimerRef.current) {
        clearInterval(triviaTimerRef.current)
      }
    }
  }, [onContinue, track, waitingForServer])

  const handleManualContinue = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
    }
    if (triviaTimerRef.current) {
      clearInterval(triviaTimerRef.current)
    }
    if (!hasContinuedRef.current) {
      hasContinuedRef.current = true
      onContinue()
    }
  }

  const handleFavorite = async () => {
    if (!onFavorite || isFavorited) return
    
    try {
      const success = await onFavorite()
      if (success) {
        setIsFavorited(true)
      }
    } catch (error) {
      console.error('Failed to favorite track in summary screen:', error)
    }
  }

  // Get trivia facts from the actual track data instead of mock database
  const triviaFacts = (track as any).triviaFacts || []
  const hasTrivia = triviaFacts.length > 0
  const rawTrivia = hasTrivia
    ? triviaFacts[currentTriviaIndex] || triviaFacts[0]
    : ""

  const currentTrivia = hasTrivia ? sanitizeHtml(rawTrivia) : ""

  const imageToShow = (track as any).artistImageUrl || (track as any).albumArtUrl || track.artistImageUrl || track.albumArtUrl

  // HINT FOR AI/Dev: `multiplayerScores` are passed in. In a real multiplayer game,
  // these scores would be received via a real-time backend (e.g., WebSockets)
  // and updated in the parent component's state.
  const sortedScores = multiplayerScores
    ? [...multiplayerScores].sort((a, b) => b.score - a.score)
    : [{ name: "You", score: playerScore, isCurrentUser: true, avatar: "/placeholder.svg?height=40&width=40" }]

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="hidden"
      className="fixed inset-0 bg-gradient-to-br from-slate-50/95 via-blue-100/95 to-indigo-200/95 dark:from-[#18122B]/95 dark:via-[#393053]/95 dark:to-[#443C68]/95 backdrop-blur-md flex items-center justify-center p-4 z-[60]"
      onClick={(e) => e.stopPropagation()}
    >
      <Card className="w-full max-w-2xl shadow-2xl bg-white/10 dark:bg-white/5 backdrop-blur-md border-white/20 dark:border-white/10 max-h-[90vh] overflow-y-auto sm:max-h-none sm:overflow-visible rounded-lg">
        <CardHeader className="text-center pb-4 pt-6">
          <motion.div variants={itemVariants} className="mb-3">
            {isCorrect ? (
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto drop-shadow-lg" />
            ) : questionPoints > 0 ? (
              <CheckCircle className="h-16 w-16 text-yellow-400 mx-auto drop-shadow-lg" />
            ) : (
              <XCircle className="h-16 w-16 text-red-500 mx-auto drop-shadow-lg" />
            )}
          </motion.div>
          <motion.h2 variants={itemVariants} className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            {isCorrect ? `Correct! +${questionPoints} points` : questionPoints>0?`Close! +${questionPoints} pts`:'Incorrect'}
          </motion.h2>
          <motion.p variants={itemVariants} className="text-base sm:text-lg text-gray-700 dark:text-gray-300">
            {feedbackDetail ? feedbackDetail : `The answer was: "${track.title}" by ${track.artist}`}
          </motion.p>
        </CardHeader>
        <CardContent className="space-y-6 px-6 pb-6">
          <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 gap-6 items-start">
            {/* Image and Trivia Section */}
            <div className="space-y-4">
              <Card className="overflow-hidden bg-white/20 dark:bg-white/10 backdrop-blur-sm border-white/30 dark:border-white/20">
                <div className="w-full h-40 sm:h-48 bg-white/10 dark:bg-white/5 flex items-center justify-center">
                  {imageToShow ? (
                    <img 
                      src={imageToShow} 
                      alt={`${track.artist} - ${track.album}`}
                      className="max-w-full max-h-full object-contain rounded"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none'
                        const fallback = (e.target as HTMLImageElement).nextElementSibling
                        if (fallback) (fallback as HTMLElement).style.display = 'flex'
                      }}
                    />
                  ) : null}
                  <div className="flex items-center justify-center w-full h-full" style={{ display: imageToShow ? 'none' : 'flex' }}>
                    <Music className="h-12 w-12 text-gray-500 dark:text-gray-400" />
                  </div>
                </div>
              </Card>
              {hasTrivia && (
                <Card className="bg-white/20 dark:bg-white/10 backdrop-blur-sm border-white/30 dark:border-white/20">
                  <CardHeader className="flex flex-row items-center space-x-2 pb-2 pt-3 px-4">
                    <Lightbulb className="h-5 w-5 text-yellow-500" />
                    <CardTitle className="text-md font-semibold text-gray-900 dark:text-white">Music Trivia</CardTitle>
                  </CardHeader>
                  <CardContent className="text-sm pt-0 pb-3 px-4 min-h-[60px] flex items-center">
                    <p
                      className="leading-relaxed text-gray-800 dark:text-gray-200 [&_a]:text-blue-600 [&_a]:underline"
                      dangerouslySetInnerHTML={{ __html: currentTrivia }}
                    />
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Track Metadata */}
            <Card className="bg-white/20 dark:bg-white/10 backdrop-blur-sm border-white/30 dark:border-white/20">
              <CardHeader className="flex flex-row items-center space-x-2 pb-2 pt-3 px-4">
                <Music className="h-5 w-5 text-purple-500" />
                <CardTitle className="text-md font-semibold text-gray-900 dark:text-white">Track Info</CardTitle>
              </CardHeader>
              <CardContent className="text-sm pt-0 pb-3 px-4 space-y-1">
                {(track as any).date && <p><strong>Year:</strong> {(track as any).date}</p>}
                {(track as any).chartPosition && <p><strong>Peak Chart:</strong> #{(track as any).chartPosition} {(track as any).chartCountry ? `(${(track as any).chartCountry})` : ''}</p>}
                {(track as any).genre && <p><strong>Genre:</strong> {(track as any).genre}</p>}
                {track.album && track.album !== 'Unknown Album' && <p><strong>Album:</strong> {track.album}</p>}
              </CardContent>
            </Card>

            {/* Scoreboard Section */}
            <div className="space-y-4">
              <Card className="bg-white/20 dark:bg-white/10 backdrop-blur-sm border-white/30 dark:border-white/20">
                <CardHeader className="flex flex-row items-center space-x-2 pb-2 pt-3 px-4">
                  <Trophy className="h-5 w-5 text-yellow-500" />
                  <CardTitle className="text-md font-semibold text-gray-900 dark:text-white">Scoreboard</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 pt-0 pb-3 px-4 max-h-48 overflow-y-auto">
                  {sortedScores.slice(0, 5).map((player, index) => (
                    <div
                      key={index}
                      className={`flex items-center justify-between p-3 rounded-lg ${
                        player.isCurrentUser
                          ? "bg-gradient-to-r from-blue-500/20 to-purple-600/20 border border-blue-400/30"
                          : "bg-white/10 dark:bg-white/5"
                      } backdrop-blur-sm`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                          index === 0 ? "bg-yellow-500 text-white" :
                          index === 1 ? "bg-gray-400 text-white" :
                          index === 2 ? "bg-amber-600 text-white" :
                          "bg-white/20 dark:bg-white/10 text-gray-700 dark:text-gray-300"
                        }`}>
                          {index + 1}
                        </div>
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={player.avatar} />
                          <AvatarFallback className="text-xs bg-white/20 dark:bg-white/10 text-gray-700 dark:text-gray-300">
                            {player.name.slice(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {player.name}
                        </span>
                      </div>
                      <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0">
                        {player.score}
                      </Badge>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div variants={itemVariants} className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-4 pt-4">
            {showFavoriteButton && (
              <Button
                onClick={handleFavorite}
                disabled={isFavorited}
                size="lg"
                variant="outline"
                className={`bg-white/10 dark:bg-white/5 backdrop-blur-md border-white/20 dark:border-white/10 hover:bg-white/20 dark:hover:bg-white/10 p-3 sm:px-6 sm:py-3 shadow-xl ${
                  isFavorited 
                    ? 'text-red-500 border-red-500/30' 
                    : 'text-gray-900 dark:text-white'
                }`}
                title={isFavorited ? "Added to favorites" : "Add to favorites"}
              >
                <Heart className={`h-5 w-5 ${isFavorited ? 'fill-current' : ''}`} />
                <span className="ml-2 sm:inline hidden">
                  {isFavorited ? 'Favorited!' : 'Add to Favorites'}
                </span>
              </Button>
            )}
            {waitingForServer ? (
              <div className="flex items-center gap-3 px-8 py-3">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0s' }}></div>
                  <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                </div>
                <span className="text-gray-700 dark:text-gray-300 font-medium">
                  Waiting for next question...
                </span>
              </div>
            ) : (
              <Button
                onClick={handleManualContinue}
                size="lg"
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 px-8 py-3 text-lg font-semibold shadow-xl w-full sm:w-auto"
              >
                Continue <ArrowRight className="ml-2 h-5 w-5" />
                <span className="ml-2 text-sm opacity-80">({countdown}s)</span>
              </Button>
            )}
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
