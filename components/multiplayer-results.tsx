"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Trophy, Medal, Award, Users, Home, RotateCcw } from "lucide-react"
import { motion } from "framer-motion"

interface MultiplayerResultsProps {
  leaderboard: Array<{
    position: number
    name: string
    score: number
    isYou?: boolean
  }>
  onPlayAgain: () => void
  onBackToMenu: () => void
}

export function MultiplayerResults({ 
  leaderboard, 
  onPlayAgain, 
  onBackToMenu 
}: MultiplayerResultsProps) {
  const playerResult = leaderboard.find(p => p.isYou)
  const position = playerResult?.position || 0
  const score = playerResult?.score || 0

  const getMedal = (pos: number) => {
    switch (pos) {
      case 1:
        return <Trophy className="h-6 w-6 text-yellow-500" />
      case 2:
        return <Medal className="h-6 w-6 text-gray-400" />
      case 3:
        return <Award className="h-6 w-6 text-orange-600" />
      default:
        return <span className="text-xl font-bold">{pos}</span>
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-3xl">Game Over!</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Player's Result */}
          {playerResult && (
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="text-center mb-8 p-6 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg"
            >
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-2">You finished</p>
              <div className="flex items-center justify-center gap-3 mb-3">
                {getMedal(position)}
                <span className="text-4xl font-bold">
                  {position === 1 && '1st'}
                  {position === 2 && '2nd'}
                  {position === 3 && '3rd'}
                  {position > 3 && `${position}th`}
                </span>
              </div>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {score} points
              </p>
            </motion.div>
          )}

          {/* Full Leaderboard */}
          <div className="space-y-2">
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <Users className="h-5 w-5" />
              Final Leaderboard
            </h3>
            {leaderboard.map((player, index) => (
              <motion.div
                key={`${player.name}-${index}`}
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: index * 0.1 }}
                className={`
                  flex items-center justify-between p-3 rounded-lg
                  ${player.isYou 
                    ? 'bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-500' 
                    : 'bg-gray-100 dark:bg-gray-800'
                  }
                `}
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 flex justify-center">
                    {getMedal(player.position)}
                  </div>
                  <span className="font-medium">
                    {player.name}
                    {player.isYou && ' (You)'}
                  </span>
                </div>
                <span className="font-bold text-lg">{player.score}</span>
              </motion.div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex gap-3">
            <Button
              onClick={onPlayAgain}
              className="flex-1"
              size="lg"
            >
              <RotateCcw className="h-5 w-5 mr-2" />
              Play Again
            </Button>
            <Button
              variant="outline"
              onClick={onBackToMenu}
              className="flex-1"
              size="lg"
            >
              <Home className="h-5 w-5 mr-2" />
              Main Menu
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}