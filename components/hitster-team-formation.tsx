"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  Users, Plus, ArrowLeft, Play, Shuffle
} from "lucide-react"
import type { Team } from "@/lib/types"
// import { useMultiplayerSocket } from "@/hooks/use-multiplayer-socket" // Removed - using unified multiplayer

interface HitsterTeamFormationProps {
  gameCode: string
  playerId: string
  playerName: string
  isHost: boolean
  teams: Team[]
  onStartGame: () => void
  onBackToLobby: () => void
}

const teamColors = [
  { name: 'Blue', value: '#3B82F6', bg: 'bg-blue-500' },
  { name: 'Red', value: '#EF4444', bg: 'bg-red-500' },
  { name: 'Green', value: '#10B981', bg: 'bg-green-500' },
  { name: 'Yellow', value: '#F59E0B', bg: 'bg-yellow-500' },
]

export function HitsterTeamFormation({
  gameCode,
  playerId,
  playerName,
  isHost,
  teams,
  onStartGame,
  onBackToLobby
}: HitsterTeamFormationProps) {
  // const { socket } = useMultiplayerSocket() // Removed - using unified multiplayer
  const socket = null // Placeholder
  const [newTeamName, setNewTeamName] = useState("")
  const [selectedColorIndex, setSelectedColorIndex] = useState(0)
  const [showCreateForm, setShowCreateForm] = useState(false)
  
  // Get the actual database game ID from localStorage
  const gameId = localStorage.getItem('currentGameId') || gameCode
  
  // Find which team the current player is in
  const myTeam = teams.find(team => 
    team.players.some(p => p.id === playerId)
  )

  const handleCreateTeam = () => {
    if (!socket || !newTeamName.trim()) return
    
    const color = teamColors[selectedColorIndex]
    socket.emit('create-team', {
      gameId: gameId,
      teamName: newTeamName.trim(),
      color: color.value
    })
    
    setNewTeamName("")
    setShowCreateForm(false)
    // Move to next color
    setSelectedColorIndex((selectedColorIndex + 1) % teamColors.length)
  }

  const handleJoinTeam = (teamId: string) => {
    if (!socket) return
    socket.emit('join-team', {
      gameId: gameId,
      teamId
    })
  }

  const handleLeaveTeam = () => {
    if (!socket) return
    socket.emit('leave-team', {
      gameId: gameId
    })
  }

  const handleAutoBalance = () => {
    if (!socket || !isHost) return
    socket.emit('auto-balance-teams', {
      gameId: gameId
    })
  }

  // Check if all players are in teams
  const allPlayersAssigned = teams.length > 0 && 
    teams.reduce((count, team) => count + team.players.length, 0) >= 2

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4"
    >
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={onBackToLobby}
            className="text-gray-400 hover:text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Lobby
          </Button>
          
          <Badge variant="outline" className="text-gray-400">
            {gameCode}
          </Badge>
        </div>

        {/* Title */}
        <Card className="bg-white/10 backdrop-blur-lg border-gray-700/30 p-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white mb-2">Hitster Timeline</h1>
            <p className="text-gray-400">Form teams to compete in chronological music challenges!</p>
          </div>
        </Card>

        {/* Team Formation */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white flex items-center gap-2">
              <Users className="h-5 w-5" />
              Teams
            </h2>
            
            {!myTeam && (
              <Button
                onClick={() => setShowCreateForm(!showCreateForm)}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create Team
              </Button>
            )}
          </div>

          {/* Create Team Form */}
          {showCreateForm && !myTeam && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
            >
              <Card className="bg-white/10 backdrop-blur-lg border-gray-700/30">
                <CardContent className="pt-6 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="team-name" className="text-white">Team Name</Label>
                    <Input
                      id="team-name"
                      value={newTeamName}
                      onChange={(e) => setNewTeamName(e.target.value)}
                      placeholder="Enter team name..."
                      maxLength={20}
                      className="bg-white/10 border-gray-600 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-white">Team Color</Label>
                    <div className="flex gap-2">
                      {teamColors.map((color, index) => (
                        <button
                          key={color.value}
                          onClick={() => setSelectedColorIndex(index)}
                          className={`w-10 h-10 rounded-full border-2 ${color.bg} ${
                            selectedColorIndex === index ? 'border-white scale-110' : 'border-gray-600'
                          } transition-all`}
                        />
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button onClick={handleCreateTeam} disabled={!newTeamName.trim()}>
                      Create Team
                    </Button>
                    <Button onClick={() => setShowCreateForm(false)} variant="outline">
                      Cancel
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Teams List */}
          <div className="grid gap-4">
            {teams.map((team) => (
              <Card 
                key={team.id} 
                className="bg-white/10 backdrop-blur-lg border-2"
                style={{ borderColor: team.color || '#6B7280' }}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-white flex items-center gap-2">
                      {team.name}
                      <Badge variant="secondary" className="text-xs">
                        {team.players.length} players
                      </Badge>
                    </CardTitle>
                    
                    {myTeam?.id === team.id ? (
                      <Button onClick={handleLeaveTeam} variant="outline" size="sm">
                        Leave Team
                      </Button>
                    ) : !myTeam && team.players.length < 4 ? (
                      <Button onClick={() => handleJoinTeam(team.id)} size="sm">
                        Join Team
                      </Button>
                    ) : team.players.length >= 4 ? (
                      <Badge variant="secondary">Full</Badge>
                    ) : null}
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {team.players.map((player) => (
                      <div
                        key={player.id}
                        className="flex items-center gap-2 bg-gray-800/50 rounded-lg px-3 py-2"
                      >
                        <Avatar className="h-6 w-6">
                          <AvatarFallback className="text-xs bg-gray-700">
                            {player.name.slice(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm text-white">{player.name}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* No teams message */}
          {teams.length === 0 && (
            <Card className="bg-white/10 backdrop-blur-lg border-gray-700/30">
              <CardContent className="py-8 text-center">
                <p className="text-gray-400">No teams created yet. Create a team to get started!</p>
              </CardContent>
            </Card>
          )}

          {/* Host Controls */}
          {isHost && (
            <Card className="bg-white/10 backdrop-blur-lg border-gray-700/30">
              <CardContent className="pt-6 space-y-4">
                <div className="flex flex-wrap gap-2">
                  {teams.length > 0 && (
                    <Button
                      onClick={handleAutoBalance}
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <Shuffle className="h-4 w-4" />
                      Auto Balance Teams
                    </Button>
                  )}
                  
                  <Button
                    onClick={onStartGame}
                    disabled={!allPlayersAssigned || teams.length < 2}
                    className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600"
                  >
                    <Play className="h-4 w-4" />
                    Start Game
                  </Button>
                </div>
                
                {!allPlayersAssigned && (
                  <p className="text-sm text-yellow-400">
                    All players must join a team before starting. Need at least 2 teams.
                  </p>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </motion.div>
  )
}