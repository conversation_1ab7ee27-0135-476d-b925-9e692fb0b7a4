"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { motion } from "framer-motion"
import { toast } from 'sonner'
import { 
  Star, 
  Settings, 
  Play,
  ArrowLeft,
  Crown,
  Zap,
  Trophy
} from "lucide-react"

// Extracted components
import { MusicQuizSection } from "./ultimote/MusicQuizSection"
import { GeneralKnowledgeSection } from "./ultimote/GeneralKnowledgeSection"
import { GameplaySettingsTab } from "./ultimote/GameplaySettingsTab"

// Types
export interface UlTimoteGameConfig {
  // Music categories
  categories: {
    classic: { enabled: boolean; weight: number; rounds: number }
    chartPosition: { enabled: boolean; weight: number; rounds: number; eraFocus?: string; chartType?: string }
    guessTheYear: { enabled: boolean; weight: number; rounds: number; decadeSelection?: string[] }
    genreSpecialist: { enabled: boolean; weight: number; rounds: number; selectedGenres?: string[] }
    quickFire: { enabled: boolean; weight: number; rounds: number }
    audioTricks: { enabled: boolean; weight: number; rounds: number }
    albumArt: { enabled: boolean; weight: number; rounds: number }
    audioFingerprint: { enabled: boolean; weight: number; rounds: number }
    
    // General Knowledge
    generalKnowledge: {
      enabled: boolean
      weight: number
      rounds: number
      categories: string[]
      difficulty: number
      questionTypes: string[]
      timePerQuestion: number
    }
  }
  
  // Gameplay settings
  questionsPerRound: number
  timePerQuestion: number
  totalRounds: number
  basePoints: number
  timeBonus: boolean
  streakMultiplier: boolean
  difficultyBonus: boolean
  suddenDeath: boolean
  powerUps: boolean
  hints: boolean
  shuffleQuestions: boolean
  showResults: boolean
  allowSkip: boolean
}

interface UlTimoteGameConfigProps {
  onStartGame: (config: UlTimoteGameConfig) => void
  onBackToMenu: () => void
  initialConfig?: Partial<UlTimoteGameConfig>
  className?: string
}

export function UlTimoteGameConfigRefactored({
  onStartGame,
  onBackToMenu,
  initialConfig,
  className = ""
}: UlTimoteGameConfigProps) {
  
  // Default configuration
  const [config, setConfig] = useState<UlTimoteGameConfig>({
    categories: {
      classic: { enabled: true, weight: 30, rounds: 2 },
      chartPosition: { enabled: false, weight: 20, rounds: 1, eraFocus: 'All Time', chartType: 'Billboard Hot 100' },
      guessTheYear: { enabled: false, weight: 20, rounds: 1, decadeSelection: [] },
      genreSpecialist: { enabled: false, weight: 20, rounds: 1, selectedGenres: [] },
      quickFire: { enabled: false, weight: 15, rounds: 1 },
      audioTricks: { enabled: false, weight: 15, rounds: 1 },
      albumArt: { enabled: false, weight: 15, rounds: 1 },
      audioFingerprint: { enabled: false, weight: 15, rounds: 1 },
      generalKnowledge: {
        enabled: true,
        weight: 70,
        rounds: 3,
        categories: ['science', 'history', 'geography'],
        difficulty: 3,
        questionTypes: ['multiple-choice'],
        timePerQuestion: 30
      }
    },
    questionsPerRound: 5,
    timePerQuestion: 30,
    totalRounds: 5,
    basePoints: 100,
    timeBonus: true,
    streakMultiplier: true,
    difficultyBonus: false,
    suddenDeath: false,
    powerUps: false,
    hints: true,
    shuffleQuestions: true,
    showResults: true,
    allowSkip: false,
    ...initialConfig
  })

  // Calculate totals
  const enabledCategories = Object.entries(config.categories).filter(([_, cat]) => cat.enabled)
  const totalRounds = enabledCategories.reduce((sum, [_, cat]) => sum + cat.rounds, 0)
  const totalQuestions = totalRounds * config.questionsPerRound
  const estimatedTime = Math.ceil(totalQuestions * config.timePerQuestion / 60)

  // Update handlers
  const updateMusicCategories = (musicConfig: any) => {
    setConfig(prev => ({
      ...prev,
      categories: {
        ...prev.categories,
        ...musicConfig
      }
    }))
  }

  const updateGeneralKnowledge = (gkConfig: any) => {
    setConfig(prev => ({
      ...prev,
      categories: {
        ...prev.categories,
        generalKnowledge: gkConfig
      }
    }))
  }

  const updateGameplaySettings = (gameplaySettings: any) => {
    setConfig(prev => ({
      ...prev,
      ...gameplaySettings
    }))
  }

  // Validation
  const validateConfig = () => {
    if (enabledCategories.length === 0) {
      toast.error('Please enable at least one quiz category')
      return false
    }

    if (totalQuestions === 0) {
      toast.error('Total questions cannot be zero')
      return false
    }

    if (config.categories.generalKnowledge.enabled && config.categories.generalKnowledge.categories.length === 0) {
      toast.error('Please select at least one general knowledge category')
      return false
    }

    return true
  }

  const handleStartGame = () => {
    if (validateConfig()) {
      onStartGame(config)
    }
  }

  // Auto-save to localStorage
  useEffect(() => {
    localStorage.setItem('ultimote-config', JSON.stringify(config))
  }, [config])

  return (
    <div className={`min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4 ${className}`}>
      <div className="max-w-6xl mx-auto space-y-6">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              ulTimote Configuration
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Create your ultimate quiz experience
            </p>
          </div>
          
          <Button
            variant="ghost"
            onClick={onBackToMenu}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Menu
          </Button>
        </motion.div>

        {/* Game Summary Card */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border-purple-500/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="w-5 h-5 text-purple-400" />
                Game Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{enabledCategories.length}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Categories</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{totalRounds}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Total Rounds</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{totalQuestions}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Questions</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">~{estimatedTime}m</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Est. Time</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Configuration Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Tabs defaultValue="categories" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="categories">Quiz Categories</TabsTrigger>
              <TabsTrigger value="gameplay">Gameplay</TabsTrigger>
              <TabsTrigger value="summary">Summary</TabsTrigger>
            </TabsList>

            {/* Categories Tab */}
            <TabsContent value="categories" className="space-y-6">
              <MusicQuizSection
                config={config.categories}
                onConfigChange={updateMusicCategories}
                maxRounds={5}
              />
              
              <GeneralKnowledgeSection
                config={config.categories.generalKnowledge}
                onConfigChange={updateGeneralKnowledge}
                maxRounds={10}
              />
            </TabsContent>

            {/* Gameplay Tab */}
            <TabsContent value="gameplay">
              <GameplaySettingsTab
                settings={{
                  questionsPerRound: config.questionsPerRound,
                  timePerQuestion: config.timePerQuestion,
                  totalRounds: config.totalRounds,
                  basePoints: config.basePoints,
                  timeBonus: config.timeBonus,
                  streakMultiplier: config.streakMultiplier,
                  difficultyBonus: config.difficultyBonus,
                  suddenDeath: config.suddenDeath,
                  powerUps: config.powerUps,
                  hints: config.hints,
                  shuffleQuestions: config.shuffleQuestions,
                  showResults: config.showResults,
                  allowSkip: config.allowSkip
                }}
                onSettingsChange={updateGameplaySettings}
              />
            </TabsContent>

            {/* Summary Tab */}
            <TabsContent value="summary">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Trophy className="w-5 h-5" />
                    Configuration Summary
                  </CardTitle>
                  <CardDescription>
                    Review your ultimate quiz configuration
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-3">Enabled Categories</h4>
                      <div className="space-y-2">
                        {enabledCategories.map(([key, cat]) => (
                          <div key={key} className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                            <span className="text-sm capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                            <Badge variant="outline">{cat.rounds} rounds</Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-3">Game Settings</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Questions per Round:</span>
                          <span>{config.questionsPerRound}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Time per Question:</span>
                          <span>{config.timePerQuestion}s</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Base Points:</span>
                          <span>{config.basePoints}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Special Features:</span>
                          <span>
                            {[
                              config.timeBonus && 'Time Bonus',
                              config.streakMultiplier && 'Streak',
                              config.hints && 'Hints'
                            ].filter(Boolean).join(', ') || 'None'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center gap-4"
        >
          <Button
            size="lg"
            onClick={handleStartGame}
            disabled={enabledCategories.length === 0}
            className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white px-8 py-3"
          >
            <Play className="w-4 h-4 mr-2" />
            Start ulTimote Game
          </Button>
        </motion.div>
      </div>
    </div>
  )
} 