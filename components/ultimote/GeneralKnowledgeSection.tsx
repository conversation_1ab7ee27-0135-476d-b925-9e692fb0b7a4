"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { 
  Brain,
  BookOpen,
  Globe,
  Target,
  Star,
  Clock,
  Award
} from "lucide-react"

interface GeneralKnowledgeConfig {
  enabled: boolean
  weight: number
  rounds: number
  categories: string[]
  difficulty: number
  questionTypes: string[]
  timePerQuestion: number
}

interface GeneralKnowledgeSectionProps {
  config: GeneralKnowledgeConfig
  onConfigChange: (config: GeneralKnowledgeConfig) => void
  maxRounds?: number
  className?: string
}

export function GeneralKnowledgeSection({
  config,
  onConfigChange,
  maxRounds = 10,
  className = ""
}: GeneralKnowledgeSectionProps) {

  const updateConfig = (updates: Partial<GeneralKnowledgeConfig>) => {
    onConfigChange({
      ...config,
      ...updates
    })
  }

  const availableCategories = [
    { key: 'science', label: 'Science & Technology', icon: Target, description: 'Physics, Chemistry, Biology, Technology' },
    { key: 'history', label: 'History', icon: BookOpen, description: 'World History, Events, Figures' },
    { key: 'geography', label: 'Geography', icon: Globe, description: 'Countries, Capitals, Landmarks' },
    { key: 'sports', label: 'Sports', icon: Award, description: 'Sports, Athletes, Records' },
    { key: 'entertainment', label: 'Entertainment', icon: Star, description: 'Movies, TV, Celebrities' },
    { key: 'literature', label: 'Literature', icon: BookOpen, description: 'Books, Authors, Poetry' },
    { key: 'politics', label: 'Politics', icon: Target, description: 'Government, Leaders, Policy' },
    { key: 'nature', label: 'Nature & Animals', icon: Globe, description: 'Wildlife, Environment, Biology' }
  ]

  const questionTypes = [
    { key: 'multiple-choice', label: 'Multiple Choice', description: '4 options to choose from' },
    { key: 'true-false', label: 'True/False', description: 'Binary choice questions' },
    { key: 'estimation', label: 'Estimation', description: 'Numeric estimation with range' }
  ]

  const difficultyLevels = [
    { value: 1, label: 'Beginner', description: 'Easy questions for everyone', color: 'bg-green-500' },
    { value: 2, label: 'Easy', description: 'Slightly more challenging', color: 'bg-blue-500' },
    { value: 3, label: 'Medium', description: 'Balanced difficulty', color: 'bg-yellow-500' },
    { value: 4, label: 'Hard', description: 'Challenging questions', color: 'bg-orange-500' },
    { value: 5, label: 'Expert', description: 'Very difficult questions', color: 'bg-red-500' }
  ]

  const toggleCategory = (categoryKey: string) => {
    const currentCategories = config.categories || []
    const isSelected = currentCategories.includes(categoryKey)
    
    const updatedCategories = isSelected
      ? currentCategories.filter(c => c !== categoryKey)
      : [...currentCategories, categoryKey]
    
    updateConfig({ categories: updatedCategories })
  }

  const toggleQuestionType = (typeKey: string) => {
    const currentTypes = config.questionTypes || ['multiple-choice']
    const isSelected = currentTypes.includes(typeKey)
    
    const updatedTypes = isSelected
      ? currentTypes.filter(t => t !== typeKey)
      : [...currentTypes, typeKey]
    
    // Ensure at least one type is selected
    if (updatedTypes.length > 0) {
      updateConfig({ questionTypes: updatedTypes })
    }
  }

  const selectAllCategories = () => {
    updateConfig({ categories: availableCategories.map(c => c.key) })
  }

  const clearAllCategories = () => {
    updateConfig({ categories: [] })
  }

  const currentDifficulty = difficultyLevels.find(d => d.value === config.difficulty) || difficultyLevels[2]

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <Brain className="w-5 h-5 text-blue-500" />
        <h3 className="text-lg font-semibold">General Knowledge</h3>
        <Badge variant="outline" className="ml-auto">
          {config.enabled ? 'Enabled' : 'Disabled'}
        </Badge>
      </div>

      <Card className={`transition-all duration-200 ${
        config.enabled 
          ? 'border-blue-500/50 bg-blue-500/5' 
          : 'border-gray-200 dark:border-gray-700'
      }`}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-600">
                <Brain className="w-4 h-4 text-white" />
              </div>
              <div>
                <CardTitle className="text-sm font-medium">General Knowledge Questions</CardTitle>
                <CardDescription className="text-xs">
                  Trivia questions from various knowledge domains
                </CardDescription>
              </div>
            </div>
            <Switch
              checked={config.enabled}
              onCheckedChange={(enabled) => updateConfig({ enabled })}
            />
          </div>
        </CardHeader>

        {config.enabled && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <CardContent className="space-y-6">
              {/* Basic Settings */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label className="text-xs">Weight ({config.weight}%)</Label>
                  <Slider
                    value={[config.weight]}
                    onValueChange={([weight]) => updateConfig({ weight })}
                    max={100}
                    min={10}
                    step={5}
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-xs">Rounds ({config.rounds})</Label>
                  <Slider
                    value={[config.rounds]}
                    onValueChange={([rounds]) => updateConfig({ rounds })}
                    max={maxRounds}
                    min={0}
                    step={1}
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-xs">Time per Question ({config.timePerQuestion}s)</Label>
                  <Slider
                    value={[config.timePerQuestion]}
                    onValueChange={([timePerQuestion]) => updateConfig({ timePerQuestion })}
                    max={60}
                    min={10}
                    step={5}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Difficulty Selection */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Difficulty Level</Label>
                <div className="flex flex-wrap gap-2">
                  {difficultyLevels.map((level) => (
                    <Badge
                      key={level.value}
                      variant={config.difficulty === level.value ? "default" : "outline"}
                      className={`cursor-pointer transition-all ${
                        config.difficulty === level.value
                          ? `${level.color} text-white`
                          : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                      }`}
                      onClick={() => updateConfig({ difficulty: level.value })}
                    >
                      {level.label}
                    </Badge>
                  ))}
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {currentDifficulty.description}
                </p>
              </div>

              {/* Question Types */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Question Types</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {questionTypes.map((type) => {
                    const isSelected = config.questionTypes?.includes(type.key)
                    return (
                      <Card
                        key={type.key}
                        className={`cursor-pointer transition-all ${
                          isSelected
                            ? 'border-blue-500 bg-blue-500/5'
                            : 'border-gray-200 dark:border-gray-700 hover:border-blue-300'
                        }`}
                        onClick={() => toggleQuestionType(type.key)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center gap-2 mb-1">
                            <div className={`w-2 h-2 rounded-full ${
                              isSelected ? 'bg-blue-500' : 'bg-gray-300'
                            }`} />
                            <span className="text-sm font-medium">{type.label}</span>
                          </div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {type.description}
                          </p>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </div>

              {/* Category Selection */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Knowledge Categories</Label>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={selectAllCategories}
                      className="h-6 px-2 text-xs"
                    >
                      Select All
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={clearAllCategories}
                      className="h-6 px-2 text-xs"
                    >
                      Clear All
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {availableCategories.map((category) => {
                    const isSelected = config.categories?.includes(category.key)
                    const IconComponent = category.icon
                    
                    return (
                      <Card
                        key={category.key}
                        className={`cursor-pointer transition-all ${
                          isSelected
                            ? 'border-blue-500 bg-blue-500/5'
                            : 'border-gray-200 dark:border-gray-700 hover:border-blue-300'
                        }`}
                        onClick={() => toggleCategory(category.key)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center gap-3">
                            <div className={`p-1.5 rounded-md ${
                              isSelected ? 'bg-blue-500' : 'bg-gray-300'
                            }`}>
                              <IconComponent className={`w-3 h-3 ${
                                isSelected ? 'text-white' : 'text-gray-600'
                              }`} />
                            </div>
                            <div>
                              <p className="text-sm font-medium">{category.label}</p>
                              <p className="text-xs text-gray-600 dark:text-gray-400">
                                {category.description}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
                
                <div className="text-xs text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                  <strong>Selected:</strong> {config.categories?.length || 0} categories
                  {config.categories?.length > 0 && (
                    <span className="ml-2">
                      ({config.categories.join(', ')})
                    </span>
                  )}
                </div>
              </div>
            </CardContent>
          </motion.div>
        )}
      </Card>
    </div>
  )
} 