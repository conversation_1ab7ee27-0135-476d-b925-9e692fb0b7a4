"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"
import { 
  Music, 
  Headphones,
  ImageIcon,
  Search,
  TrendingUp,
  Calendar,
  Radio,
  Award,
  Target
} from "lucide-react"

interface MusicQuizConfig {
  classic: {
    enabled: boolean
    weight: number
    rounds: number
    timePerQuestion?: number
    difficulty?: number
  }
  chartPosition: {
    enabled: boolean
    weight: number
    rounds: number
    eraFocus?: string
    chartType?: string
  }
  guessTheYear: {
    enabled: boolean
    weight: number
    rounds: number
    decadeSelection?: string[]
    yearRange?: { min: number; max: number }
  }
  genreSpecialist: {
    enabled: boolean
    weight: number
    rounds: number
    selectedGenres?: string[]
    difficulty?: number
  }
  quickFire: {
    enabled: boolean
    weight: number
    rounds: number
    speed?: string
  }
  audioTricks: {
    enabled: boolean
    weight: number
    rounds: number
    effects?: string[]
  }
  albumArt: {
    enabled: boolean
    weight: number
    rounds: number
    artStyle?: string
  }
  audioFingerprint: {
    enabled: boolean
    weight: number
    rounds: number
    difficulty?: string
  }
}

interface MusicQuizSectionProps {
  config: MusicQuizConfig
  onConfigChange: (config: MusicQuizConfig) => void
  maxRounds?: number
  className?: string
}

export function MusicQuizSection({
  config,
  onConfigChange,
  maxRounds = 5,
  className = ""
}: MusicQuizSectionProps) {
  
  const updateCategory = (category: keyof MusicQuizConfig, updates: Partial<MusicQuizConfig[keyof MusicQuizConfig]>) => {
    onConfigChange({
      ...config,
      [category]: {
        ...config[category],
        ...updates
      }
    })
  }

  const musicCategories = [
    {
      key: 'classic' as const,
      title: 'Classic Quiz',
      description: 'Traditional artist/title identification',
      icon: Music,
      color: 'from-blue-500 to-purple-600',
      hasAdvanced: true
    },
    {
      key: 'chartPosition' as const,
      title: 'Chart Challenge',
      description: 'Guess peak chart positions',
      icon: TrendingUp,
      color: 'from-green-500 to-blue-500',
      hasAdvanced: true
    },
    {
      key: 'guessTheYear' as const,
      title: 'Guess the Year',
      description: 'Identify release years',
      icon: Calendar,
      color: 'from-yellow-500 to-orange-500',
      hasAdvanced: true
    },
    {
      key: 'genreSpecialist' as const,
      title: 'Genre Expert',
      description: 'Music genre identification',
      icon: Radio,
      color: 'from-purple-500 to-pink-500',
      hasAdvanced: true
    },
    {
      key: 'quickFire' as const,
      title: 'Quick Fire',
      description: 'Fast-paced rapid questions',
      icon: Award,
      color: 'from-red-500 to-orange-500',
      hasAdvanced: false
    },
    {
      key: 'audioTricks' as const,
      title: 'Audio Tricks',
      description: 'Modified audio challenges',
      icon: Headphones,
      color: 'from-cyan-500 to-blue-500',
      hasAdvanced: false
    },
    {
      key: 'albumArt' as const,
      title: 'Album Art Quiz',
      description: 'Visual identification challenges',
      icon: ImageIcon,
      color: 'from-pink-500 to-purple-500',
      hasAdvanced: false
    },
    {
      key: 'audioFingerprint' as const,
      title: 'Audio Fingerprint',
      description: 'Advanced audio recognition',
      icon: Search,
      color: 'from-gray-500 to-blue-500',
      hasAdvanced: false
    }
  ]

  const decades = ['1950s', '1960s', '1970s', '1980s', '1990s', '2000s', '2010s', '2020s']
  const genres = [
    'Rock', 'Pop', 'Hip Hop', 'Electronic', 'Jazz', 'Blues', 'Country', 'Classical',
    'R&B', 'Reggae', 'Folk', 'Metal', 'Punk', 'Alternative', 'Indie', 'Dance'
  ]
  const chartTypes = ['Billboard Hot 100', 'UK Singles Chart', 'Album Charts', 'Global Charts']
  const eraOptions = ['All Time', '1960s-1980s', '1990s-2000s', '2010s-Present', 'Custom Range']

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <Music className="w-5 h-5 text-blue-500" />
        <h3 className="text-lg font-semibold">Music Quiz Categories</h3>
        <Badge variant="outline" className="ml-auto">
          {Object.values(config).filter(c => c.enabled).length} enabled
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {musicCategories.map((category) => {
          const categoryConfig = config[category.key]
          const IconComponent = category.icon

          return (
            <motion.div
              key={category.key}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card className={`transition-all duration-200 ${
                categoryConfig.enabled 
                  ? 'border-blue-500/50 bg-blue-500/5' 
                  : 'border-gray-200 dark:border-gray-700'
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color}`}>
                        <IconComponent className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-sm font-medium">
                          {category.title}
                        </CardTitle>
                        <CardDescription className="text-xs">
                          {category.description}
                        </CardDescription>
                      </div>
                    </div>
                    <Switch
                      checked={categoryConfig.enabled}
                      onCheckedChange={(enabled) => 
                        updateCategory(category.key, { enabled })
                      }
                    />
                  </div>
                </CardHeader>

                {categoryConfig.enabled && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                  >
                    <CardContent className="pt-0 space-y-4">
                      {/* Weight and Rounds */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="text-xs">Weight ({categoryConfig.weight}%)</Label>
                          <Slider
                            value={[categoryConfig.weight]}
                            onValueChange={([weight]) => 
                              updateCategory(category.key, { weight })
                            }
                            max={100}
                            min={10}
                            step={5}
                            className="w-full"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-xs">Rounds ({categoryConfig.rounds})</Label>
                          <Slider
                            value={[categoryConfig.rounds]}
                            onValueChange={([rounds]) => 
                              updateCategory(category.key, { rounds })
                            }
                            max={maxRounds}
                            min={0}
                            step={1}
                            className="w-full"
                          />
                        </div>
                      </div>

                      {/* Category-specific settings */}
                      {category.key === 'chartPosition' && (
                        <div className="space-y-3">
                          <div className="space-y-2">
                            <Label className="text-xs">Era Focus</Label>
                            <Select
                              value={config.chartPosition.eraFocus || 'All Time'}
                              onValueChange={(eraFocus) => 
                                updateCategory('chartPosition', { eraFocus })
                              }
                            >
                              <SelectTrigger className="h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {eraOptions.map(era => (
                                  <SelectItem key={era} value={era}>{era}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-xs">Chart Type</Label>
                            <Select
                              value={config.chartPosition.chartType || 'Billboard Hot 100'}
                              onValueChange={(chartType) => 
                                updateCategory('chartPosition', { chartType })
                              }
                            >
                              <SelectTrigger className="h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {chartTypes.map(type => (
                                  <SelectItem key={type} value={type}>{type}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      )}

                      {category.key === 'guessTheYear' && (
                        <div className="space-y-2">
                          <Label className="text-xs">Decade Selection</Label>
                          <div className="flex flex-wrap gap-1">
                            {decades.map(decade => {
                              const isSelected = config.guessTheYear.decadeSelection?.includes(decade)
                              return (
                                <Badge
                                  key={decade}
                                  variant={isSelected ? "default" : "outline"}
                                  className={`cursor-pointer text-xs ${
                                    isSelected 
                                      ? 'bg-blue-500 text-white' 
                                      : 'hover:bg-blue-500/10'
                                  }`}
                                  onClick={() => {
                                    const current = config.guessTheYear.decadeSelection || []
                                    const updated = isSelected
                                      ? current.filter(d => d !== decade)
                                      : [...current, decade]
                                    updateCategory('guessTheYear', { decadeSelection: updated })
                                  }}
                                >
                                  {decade}
                                </Badge>
                              )
                            })}
                          </div>
                        </div>
                      )}

                      {category.key === 'genreSpecialist' && (
                        <div className="space-y-2">
                          <Label className="text-xs">Genre Selection</Label>
                          <div className="flex flex-wrap gap-1">
                            {genres.slice(0, 8).map(genre => {
                              const isSelected = config.genreSpecialist.selectedGenres?.includes(genre)
                              return (
                                <Badge
                                  key={genre}
                                  variant={isSelected ? "default" : "outline"}
                                  className={`cursor-pointer text-xs ${
                                    isSelected 
                                      ? 'bg-purple-500 text-white' 
                                      : 'hover:bg-purple-500/10'
                                  }`}
                                  onClick={() => {
                                    const current = config.genreSpecialist.selectedGenres || []
                                    const updated = isSelected
                                      ? current.filter(g => g !== genre)
                                      : [...current, genre]
                                    updateCategory('genreSpecialist', { selectedGenres: updated })
                                  }}
                                >
                                  {genre}
                                </Badge>
                              )
                            })}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </motion.div>
                )}
              </Card>
            </motion.div>
          )
        })}
      </div>
    </div>
  )
} 