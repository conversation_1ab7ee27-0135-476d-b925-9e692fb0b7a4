"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"
import { 
  Clock, 
  Trophy, 
  Target, 
  Zap,
  Award,
  Crown,
  Settings
} from "lucide-react"

interface GameplaySettings {
  questionsPerRound: number
  timePerQuestion: number
  totalRounds: number
  
  // Scoring
  basePoints: number
  timeBonus: boolean
  streakMultiplier: boolean
  difficultyBonus: boolean
  
  // Special modes
  suddenDeath: boolean
  powerUps: boolean
  hints: boolean
  
  // Flow
  shuffleQuestions: boolean
  showResults: boolean
  allowSkip: boolean
}

interface GameplaySettingsTabProps {
  settings: GameplaySettings
  onSettingsChange: (settings: GameplaySettings) => void
  className?: string
}

export function GameplaySettingsTab({
  settings,
  onSettingsChange,
  className = ""
}: GameplaySettingsTabProps) {
  
  const updateSetting = (key: keyof GameplaySettings, value: any) => {
    onSettingsChange({
      ...settings,
      [key]: value
    })
  }

  const scoringFeatures = [
    {
      key: 'timeBonus' as const,
      title: 'Time Bonus',
      description: 'Extra points for quick answers',
      icon: Clock,
      color: 'text-blue-500'
    },
    {
      key: 'streakMultiplier' as const,
      title: 'Streak Multiplier',
      description: 'Multiplier for consecutive correct answers',
      icon: Zap,
      color: 'text-yellow-500'
    },
    {
      key: 'difficultyBonus' as const,
      title: 'Difficulty Bonus',
      description: 'Extra points for harder questions',
      icon: Trophy,
      color: 'text-purple-500'
    }
  ]

  const specialModes = [
    {
      key: 'suddenDeath' as const,
      title: 'Sudden Death',
      description: 'Game ends on first wrong answer',
      icon: Target,
      color: 'text-red-500'
    },
    {
      key: 'powerUps' as const,
      title: 'Power-ups',
      description: 'Special abilities during gameplay',
      icon: Award,
      color: 'text-green-500'
    },
    {
      key: 'hints' as const,
      title: 'Hints Available',
      description: 'Players can request hints',
      icon: Crown,
      color: 'text-orange-500'
    }
  ]

  const flowOptions = [
    {
      key: 'shuffleQuestions' as const,
      title: 'Shuffle Questions',
      description: 'Randomize question order'
    },
    {
      key: 'showResults' as const,
      title: 'Show Results',
      description: 'Display results after each question'
    },
    {
      key: 'allowSkip' as const,
      title: 'Allow Skip',
      description: 'Players can skip questions'
    }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <Settings className="w-5 h-5 text-blue-500" />
        <h3 className="text-lg font-semibold">Gameplay Settings</h3>
      </div>

      {/* Game Flow Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Game Flow
          </CardTitle>
          <CardDescription>Control the basic game timing and structure</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label className="text-sm">Questions per Round</Label>
              <div className="flex items-center gap-2">
                <Slider
                  value={[settings.questionsPerRound]}
                  onValueChange={([value]) => updateSetting('questionsPerRound', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="flex-1"
                />
                <Badge variant="outline" className="min-w-[40px] text-center">
                  {settings.questionsPerRound}
                </Badge>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm">Time per Question (seconds)</Label>
              <div className="flex items-center gap-2">
                <Slider
                  value={[settings.timePerQuestion]}
                  onValueChange={([value]) => updateSetting('timePerQuestion', value)}
                  max={60}
                  min={10}
                  step={5}
                  className="flex-1"
                />
                <Badge variant="outline" className="min-w-[40px] text-center">
                  {settings.timePerQuestion}s
                </Badge>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm">Total Rounds</Label>
              <div className="flex items-center gap-2">
                <Slider
                  value={[settings.totalRounds]}
                  onValueChange={([value]) => updateSetting('totalRounds', value)}
                  max={20}
                  min={1}
                  step={1}
                  className="flex-1"
                />
                <Badge variant="outline" className="min-w-[40px] text-center">
                  {settings.totalRounds}
                </Badge>
              </div>
            </div>
          </div>

          {/* Flow Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {flowOptions.map((option) => (
              <div key={option.key} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="text-sm font-medium">{option.title}</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">{option.description}</p>
                </div>
                <Switch
                  checked={settings[option.key] as boolean}
                  onCheckedChange={(checked) => updateSetting(option.key, checked)}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Scoring System */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Trophy className="w-4 h-4" />
            Scoring System
          </CardTitle>
          <CardDescription>Configure how points are awarded</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm">Base Points per Correct Answer</Label>
            <div className="flex items-center gap-2">
              <Slider
                value={[settings.basePoints]}
                onValueChange={([value]) => updateSetting('basePoints', value)}
                max={1000}
                min={50}
                step={50}
                className="flex-1"
              />
              <Badge variant="outline" className="min-w-[60px] text-center">
                {settings.basePoints}
              </Badge>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {scoringFeatures.map((feature) => {
              const IconComponent = feature.icon
              const isEnabled = settings[feature.key] as boolean
              
              return (
                <motion.div
                  key={feature.key}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Card className={`cursor-pointer transition-all ${
                    isEnabled 
                      ? 'border-blue-500 bg-blue-500/5' 
                      : 'border-gray-200 dark:border-gray-700 hover:border-blue-300'
                  }`}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <IconComponent className={`w-5 h-5 ${feature.color}`} />
                        <Switch
                          checked={isEnabled}
                          onCheckedChange={(checked) => updateSetting(feature.key, checked)}
                        />
                      </div>
                      <h4 className="text-sm font-medium mb-1">{feature.title}</h4>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {feature.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Special Modes */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Zap className="w-4 h-4" />
            Special Modes
          </CardTitle>
          <CardDescription>Advanced gameplay features</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {specialModes.map((mode) => {
              const IconComponent = mode.icon
              const isEnabled = settings[mode.key] as boolean
              
              return (
                <motion.div
                  key={mode.key}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Card className={`cursor-pointer transition-all ${
                    isEnabled 
                      ? 'border-orange-500 bg-orange-500/5' 
                      : 'border-gray-200 dark:border-gray-700 hover:border-orange-300'
                  }`}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <IconComponent className={`w-5 h-5 ${mode.color}`} />
                        <Switch
                          checked={isEnabled}
                          onCheckedChange={(checked) => updateSetting(mode.key, checked)}
                        />
                      </div>
                      <h4 className="text-sm font-medium mb-1">{mode.title}</h4>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {mode.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 