'use client'

import React, { useState, useEffect, use<PERSON>allback, useMemo } from 'react'
import { 
  Search, 
  Music, 
  Calendar,
  Tag,
  User,
  Filter,
  X,
  Plus,
  Heart,
  PlayCircle,
  ChevronDown,
  ChevronUp,
  Loader2,
  Clock,
  Disc3,
  Mic2,
  ListMusic,
  Send
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AlbumArt } from '@/components/album-art'
import { toast } from 'sonner'
import { useJukeboxStore } from '@/stores/jukeboxStore'
import type { Song } from '@/lib/types'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cn } from '@/lib/utils'
import { useInView } from 'react-intersection-observer'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'

interface MobileLibraryBrowserProps {
  userRole?: string
  isVisible?: boolean
  onClose?: () => void
  currentUser?: any
}

interface FilterState {
  search: string
  genre: string
  year: string
  artist: string
  category: string
}

interface TrackWithMetadata extends Song {
  genre?: string
  year?: number | null
  quizCategories?: string
  mpdFilePath?: string
}

export function MobileLibraryBrowser({ 
  userRole = 'user', 
  isVisible = true, 
  onClose,
  currentUser: userProp
}: MobileLibraryBrowserProps) {
  const { userFavorites, currentUser, setCurrentUser } = useJukeboxStore()
  
  const [tracks, setTracks] = useState<TrackWithMetadata[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  const [totalTracks, setTotalTracks] = useState(0)
  
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    genre: 'all',
    year: 'all',
    artist: 'all',
    category: 'all'
  })
  
  const [showFilters, setShowFilters] = useState(false)
  const [genres, setGenres] = useState<string[]>([])
  const [years, setYears] = useState<string[]>([])
  const [artists, setArtists] = useState<string[]>([])
  const [categories, setCategories] = useState<string[]>([])
  
  const [operationLoading, setOperationLoading] = useState<Set<string>>(new Set())
  
  // Intersection observer for infinite scroll
  const { ref: loadMoreRef, inView } = useInView({
    threshold: 0,
    rootMargin: '100px',
  })

  // Set current user if provided
  useEffect(() => {
    if (userProp && (!currentUser || currentUser.id !== userProp.id)) {
      console.log('[MobileLibraryBrowser] Setting current user:', userProp.email)
      setCurrentUser(userProp)
      loadUserFavorites(userProp.id)
    }
  }, [userProp, currentUser, setCurrentUser])

  // Load filter options and user favorites
  useEffect(() => {
    loadFilterOptions()
    // Load user favorites if we have a user
    if (currentUser?.id) {
      loadUserFavorites()
    }
  }, [])
  
  const loadUserFavorites = async (userId?: string) => {
    const userIdToUse = userId || currentUser?.id || userProp?.id
    if (!userIdToUse) return
    
    try {
      const response = await fetch(`/api/jukebox/favorites?userId=${userIdToUse}`, {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        console.log('[MobileLibraryBrowser] Loaded user favorites:', data.favorites?.length || 0)
        if (data.favorites) {
          useJukeboxStore.setState({ userFavorites: data.favorites })
        }
      }
    } catch (error) {
      console.error('[MobileLibraryBrowser] Failed to load favorites:', error)
    }
  }

  // Load tracks when filters change
  useEffect(() => {
    setTracks([])
    setPage(1)
    setHasMore(true)
    loadTracks(1, true)
  }, [filters])

  // Load more tracks when scrolling
  useEffect(() => {
    if (inView && hasMore && !isLoading) {
      loadTracks(page + 1)
    }
  }, [inView, hasMore, isLoading, page])

  const loadFilterOptions = async () => {
    try {
      const response = await fetch('/api/library/filters', {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        setGenres(data.genres || [])
        setYears(data.years || [])
        setArtists(data.artists || [])
        setCategories(data.categories || [])
      } else {
        // If filters API requires auth, we can fallback to loading from tracks API
        console.warn('Filters API requires authentication, using fallback')
        // For now, we'll leave the filters empty
      }
    } catch (error) {
      console.error('Failed to load filter options:', error)
    }
  }

  const loadTracks = async (pageNum: number, reset: boolean = false) => {
    if (isLoading) return
    
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        page: pageNum.toString(),
        limit: '50',
        ...(filters.search && { search: filters.search }),
        ...(filters.genre !== 'all' && { genre: filters.genre }),
        ...(filters.year !== 'all' && { year: filters.year }),
        ...(filters.artist !== 'all' && { artist: filters.artist }),
        ...(filters.category !== 'all' && { category: filters.category }),
      })

      const response = await fetch(`/api/quiz/tracks?${params}`)
      if (response.ok) {
        const data = await response.json()
        
        if (reset) {
          setTracks(data.tracks || [])
        } else {
          setTracks(prev => [...prev, ...(data.tracks || [])])
        }
        
        setPage(pageNum)
        setHasMore(data.pagination?.hasNext || false)
        setTotalTracks(data.pagination?.total || 0)
      }
    } catch (error) {
      console.error('Failed to load tracks:', error)
      toast.error('Failed to load tracks')
    } finally {
      setIsLoading(false)
    }
  }

  const handleQueueTrack = async (track: TrackWithMetadata) => {
    const loadingKey = `queue-${track.id}`
    setOperationLoading(prev => new Set(prev).add(loadingKey))
    
    try {
      const response = await fetch('/api/mpd/queue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'add',
          filePath: track.mpdFilePath || track.filePath
        })
      })

      if (response.ok) {
        toast.success('Added to queue')
      } else {
        throw new Error('Failed to add to queue')
      }
    } catch (error) {
      console.error('Failed to queue track:', error)
      toast.error('Failed to add to queue')
    } finally {
      setOperationLoading(prev => {
        const next = new Set(prev)
        next.delete(loadingKey)
        return next
      })
    }
  }

  const handleSuggestTrack = async (track: TrackWithMetadata) => {
    const loadingKey = `suggest-${track.id}`
    setOperationLoading(prev => new Set(prev).add(loadingKey))
    
    console.log('[MobileLibraryBrowser] Suggesting track:', {
      title: track.title,
      artist: track.artist,
      filePath: track.mpdFilePath || track.filePath
    })
    
    try {
      const response = await fetch('/api/jukebox/suggestions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include', // Important for cookie-based auth
        body: JSON.stringify({
          title: track.title,
          artist: track.artist,
          album: track.album,
          duration: track.duration,
          genre: track.genre,
          year: track.year,
          filePath: track.mpdFilePath || track.filePath,
          albumArtUrl: track.albumArtUrl
        })
      })

      const data = await response.json()
      console.log('[MobileLibraryBrowser] Suggest response:', response.status, data)
      
      if (response.ok && data.success) {
        toast.success('Track suggested successfully!')
        // Optionally refresh or update UI
      } else {
        const errorMessage = data.message || 'Failed to suggest track'
        console.error('[MobileLibraryBrowser] Suggest failed:', errorMessage)
        toast.error(errorMessage)
      }
    } catch (error) {
      console.error('[MobileLibraryBrowser] Failed to suggest track:', error)
      toast.error('Network error: Failed to suggest track')
    } finally {
      setOperationLoading(prev => {
        const next = new Set(prev)
        next.delete(loadingKey)
        return next
      })
    }
  }

  const toggleFavorite = async (track: TrackWithMetadata) => {
    const trackFilePath = track.mpdFilePath || track.filePath
    const isFavorite = userFavorites.some(f => 
      f.filePath === trackFilePath || 
      (track.id && (f.trackId === track.id.toString() || f.songId === track.id))
    )
    const loadingKey = `favorite-${track.id}`
    setOperationLoading(prev => new Set(prev).add(loadingKey))
    
    console.log('[MobileLibraryBrowser] Toggle favorite:', {
      track: track.title,
      filePath: trackFilePath,
      isFavorite,
      currentUser: currentUser?.id,
      userFavoritesCount: userFavorites.length,
      matchingFavorite: userFavorites.find(f => 
        f.filePath === trackFilePath || f.trackId === track.id?.toString()
      )
    })
    
    try {
      // Make sure we have a current user
      if (!currentUser) {
        toast.error('Please log in to add favorites')
        return
      }
      
      // Handle DELETE and POST differently
      if (isFavorite) {
        // For DELETE, use query params
        const deleteUrl = `/api/jukebox/favorites?userId=${currentUser.id}&filePath=${encodeURIComponent(trackFilePath)}`
        const deleteResponse = await fetch(deleteUrl, {
          method: 'DELETE',
          credentials: 'include'
        })
        
        if (deleteResponse.ok) {
          // Update local state directly
          useJukeboxStore.setState((state) => ({
            userFavorites: state.userFavorites.filter(f => 
              // Keep favorites that DON'T match the track we're removing
              !(f.filePath === trackFilePath || 
                (track.id && (f.trackId === track.id.toString() || f.songId === track.id)))
            )
          }))
          toast.success('Removed from favorites')
        } else {
          const error = await deleteResponse.json()
          throw new Error(error.message || 'Failed to remove favorite')
        }
      } else {
        // For POST, send body with data
        const response = await fetch('/api/jukebox/favorites', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            userId: currentUser.id,
            song: {
              filePath: trackFilePath,
              title: track.title,
              artist: track.artist,
              album: track.album,
              duration: track.duration,
              genre: track.genre,
              year: track.year,
              albumArtUrl: track.albumArtUrl
            }
          })
        })
        
        const data = await response.json()
        console.log('[MobileLibraryBrowser] Add favorite response:', data)
        
        if (response.ok && data.success) {
          // Update local state directly instead of using store method
          // which expects the track to be in library/queue
          useJukeboxStore.setState((state) => ({
            userFavorites: [...state.userFavorites, {
              ...data.favorite,
              // Ensure we have the fields we need for checking
              filePath: data.favorite.filePath || trackFilePath,
              trackId: data.favorite.songId || track.id?.toString() || '',
              addedAt: new Date(data.favorite.addedAt || Date.now())
            }]
          }))
          toast.success('Added to favorites')
        } else {
          // Check if it's already in favorites
          if (data.alreadyExists) {
            toast.info('Already in your favorites')
            // Still update local state in case it's out of sync
            useJukeboxStore.setState((state) => {
              const exists = state.userFavorites.some(f => 
                f.filePath === trackFilePath || 
                (track.id && f.trackId === track.id.toString())
              )
              if (!exists) {
                return {
                  userFavorites: [...state.userFavorites, {
                    id: `temp-${Date.now()}`,
                    userId: currentUser.id,
                    trackId: track.id?.toString() || '',
                    filePath: trackFilePath,
                    addedAt: new Date()
                  }]
                }
              }
              return state
            })
          } else {
            throw new Error(data.message || 'Failed to add favorite')
          }
        }
      }
    } catch (error) {
      console.error('[MobileLibraryBrowser] Failed to toggle favorite:', error)
      toast.error(`Failed to ${isFavorite ? 'remove from' : 'add to'} favorites`)
    } finally {
      setOperationLoading(prev => {
        const next = new Set(prev)
        next.delete(loadingKey)
        return next
      })
    }
  }

  const isDJ = userRole === 'dj' || userRole === 'admin' || userRole === 'superuser'

  const activeFiltersCount = [
    filters.genre !== 'all',
    filters.year !== 'all',
    filters.artist !== 'all',
    filters.category !== 'all',
  ].filter(Boolean).length

  const formatDuration = (seconds?: number) => {
    if (!seconds) return ''
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className={cn(
      "flex flex-col h-full bg-gradient-to-b from-gray-900 via-gray-900 to-black text-white",
      !isVisible && "hidden"
    )}>
      {/* Header */}
      <div className="sticky top-0 z-20 bg-gray-900/95 backdrop-blur-xl border-b border-white/10">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center shadow-lg shadow-purple-500/25">
                <Music className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  Music Library
                </h1>
                <p className="text-sm text-gray-400">
                  {totalTracks > 0 ? `${totalTracks.toLocaleString()} tracks` : 'Loading...'}
                </p>
              </div>
            </div>
            {onClose && (
              <Button 
                variant="ghost" 
                size="icon"
                onClick={onClose}
                className="text-gray-400 hover:text-white hover:bg-white/10"
              >
                <X className="h-5 w-5" />
              </Button>
            )}
          </div>
          
          {/* Search Bar */}
          <div className="relative mb-3">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search songs, artists, albums..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10 bg-white/5 border-white/10 text-white placeholder:text-gray-500 focus:border-purple-500 focus:ring-purple-500/20"
            />
          </div>

          {/* Filter Toggle */}
          <Button
            variant={showFilters ? "default" : "outline"}
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className={cn(
              "w-full gap-2",
              showFilters 
                ? "bg-purple-600 hover:bg-purple-700 text-white border-purple-600" 
                : "bg-white/5 border-white/10 text-white hover:bg-white/10"
            )}
          >
            <Filter className="h-4 w-4" />
            {showFilters ? 'Hide' : 'Show'} Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-auto bg-purple-500/20 text-purple-300 border-0">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="p-4 pt-0 grid grid-cols-2 gap-3 border-t border-white/10">
            <Select value={filters.genre} onValueChange={(value) => setFilters(prev => ({ ...prev, genre: value }))}>
              <SelectTrigger className="bg-white/5 border-white/10 text-white">
                <Tag className="h-4 w-4 mr-2 text-gray-400" />
                <SelectValue placeholder="Genre" />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-white/10">
                <SelectItem value="all" className="text-white hover:bg-white/10">All Genres</SelectItem>
                {genres.map(genre => (
                  <SelectItem key={genre} value={genre} className="text-white hover:bg-white/10">
                    {genre}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.year} onValueChange={(value) => setFilters(prev => ({ ...prev, year: value }))}>
              <SelectTrigger className="bg-white/5 border-white/10 text-white">
                <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                <SelectValue placeholder="Year" />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-white/10">
                <SelectItem value="all" className="text-white hover:bg-white/10">All Years</SelectItem>
                {years.map(year => (
                  <SelectItem key={year} value={year} className="text-white hover:bg-white/10">
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.artist} onValueChange={(value) => setFilters(prev => ({ ...prev, artist: value }))}>
              <SelectTrigger className="bg-white/5 border-white/10 text-white">
                <Mic2 className="h-4 w-4 mr-2 text-gray-400" />
                <SelectValue placeholder="Artist" />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-white/10">
                <SelectItem value="all" className="text-white hover:bg-white/10">All Artists</SelectItem>
                {artists.map(artist => (
                  <SelectItem key={artist} value={artist} className="text-white hover:bg-white/10">
                    {artist}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.category} onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}>
              <SelectTrigger className="bg-white/5 border-white/10 text-white">
                <ListMusic className="h-4 w-4 mr-2 text-gray-400" />
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-white/10">
                <SelectItem value="all" className="text-white hover:bg-white/10">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category} className="text-white hover:bg-white/10">
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      {/* Track List */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-3">
          {tracks.map((track, index) => {
            const trackFilePath = track.mpdFilePath || track.filePath
            const isFavorite = userFavorites.some(f => 
              f.filePath === trackFilePath || 
              (track.id && (f.trackId === track.id.toString() || f.songId === track.id))
            )
            const isLoadingQueue = operationLoading.has(`queue-${track.id}`)
            const isLoadingSuggest = operationLoading.has(`suggest-${track.id}`)
            const isLoadingFavorite = operationLoading.has(`favorite-${track.id}`)
            
            return (
              <Card 
                key={`${track.id}-${index}`} 
                className="overflow-hidden bg-white/5 border-white/10 hover:bg-white/10 transition-colors"
              >
                <CardContent className="p-4">
                  <div className="flex gap-3">
                    {/* Album Art */}
                    <div className="flex-shrink-0">
                      <AlbumArt 
                        trackId={track.id?.toString()}
                        fallbackUrl={track.albumArtUrl}
                        artist={track.artist}
                        album={track.album}
                        size="md"
                        className="rounded-lg shadow-lg"
                      />
                    </div>
                    
                    {/* Track Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold line-clamp-1 text-white">{track.title}</h3>
                      <p className="text-sm text-gray-400 line-clamp-1">
                        {track.artist}
                      </p>
                      {track.album && (
                        <p className="text-xs text-gray-500 line-clamp-1">
                          {track.album}
                        </p>
                      )}
                      
                      {/* Metadata badges */}
                      <div className="flex flex-wrap gap-1 mt-2">
                        {track.genre && track.genre !== 'Unknown' && (
                          <Badge variant="secondary" className="text-xs bg-purple-500/20 text-purple-300 border-0">
                            <Tag className="h-3 w-3 mr-1" />
                            {track.genre}
                          </Badge>
                        )}
                        {track.year && (
                          <Badge variant="secondary" className="text-xs bg-blue-500/20 text-blue-300 border-0">
                            <Calendar className="h-3 w-3 mr-1" />
                            {track.year}
                          </Badge>
                        )}
                        {track.duration && (
                          <Badge variant="secondary" className="text-xs bg-green-500/20 text-green-300 border-0">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatDuration(track.duration)}
                          </Badge>
                        )}
                      </div>
                      
                      {/* Action buttons */}
                      <div className="flex flex-wrap gap-2 mt-3">
                        {/* Suggest button - shown for all users */}
                        <Button
                          size="sm"
                          onClick={() => handleSuggestTrack(track)}
                          disabled={isLoadingSuggest}
                          className="bg-blue-600 hover:bg-blue-700 text-white flex-shrink-0"
                        >
                          {isLoadingSuggest ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <>
                              <Send className="h-4 w-4 sm:mr-1" />
                              <span className="hidden sm:inline">Suggest</span>
                            </>
                          )}
                        </Button>
                        
                        {/* Queue button - only for DJ/Admin users */}
                        {isDJ && (
                          <Button
                            size="sm"
                            onClick={() => handleQueueTrack(track)}
                            disabled={isLoadingQueue}
                            className="bg-purple-600 hover:bg-purple-700 text-white flex-shrink-0"
                          >
                            {isLoadingQueue ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <>
                                <Plus className="h-4 w-4 sm:mr-1" />
                                <span className="hidden sm:inline">Queue</span>
                              </>
                            )}
                          </Button>
                        )}
                        
                        <Button
                          size="sm"
                          variant={isFavorite ? "default" : "outline"}
                          onClick={() => toggleFavorite(track)}
                          disabled={isLoadingFavorite}
                          className={cn(
                            "flex-shrink-0 min-w-[2.25rem]",
                            isFavorite 
                              ? "bg-pink-600 hover:bg-pink-700 text-white" 
                              : "bg-white/5 border-white/20 text-white hover:bg-white/10"
                          )}
                          aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
                        >
                          {isLoadingFavorite ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <>
                              <Heart className={cn("h-4 w-4", isFavorite && "fill-current")} />
                              <span className="hidden sm:inline sm:ml-1">{isFavorite ? "Liked" : "Like"}</span>
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
          
          {/* Loading more indicator */}
          {isLoading && (
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <Card key={`skeleton-${i}`} className="overflow-hidden bg-white/5 border-white/10">
                  <CardContent className="p-4">
                    <div className="flex gap-3">
                      <Skeleton className="h-16 w-16 rounded-lg bg-white/10" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4 bg-white/10" />
                        <Skeleton className="h-3 w-1/2 bg-white/10" />
                        <Skeleton className="h-3 w-1/3 bg-white/10" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
          
          {/* Load more trigger */}
          {hasMore && !isLoading && (
            <div ref={loadMoreRef} className="h-10 flex items-center justify-center">
              <Loader2 className="h-5 w-5 animate-spin text-gray-500" />
            </div>
          )}
          
          {/* No results */}
          {!isLoading && tracks.length === 0 && (
            <div className="text-center py-12 text-gray-400">
              <Music className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No tracks found</p>
              <p className="text-sm mt-1">Try adjusting your filters</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}