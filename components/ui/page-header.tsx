"use client"

import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface PageHeaderProps {
  title: string
  backHref?: string
  backLabel?: string
  showBack?: boolean
  titleClassName?: string
  className?: string
  children?: React.ReactNode
}

export function PageHeader({
  title,
  backHref = "/",
  backLabel = "Back",
  showBack = true,
  titleClassName,
  className,
  children
}: PageHeaderProps) {
  return (
    <div className={cn("flex items-center justify-between mb-6 md:mb-8", className)}>
      <div className="flex items-center gap-2 sm:gap-4">
        {showBack && (
          <Link href={backHref}>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-8 sm:h-9 px-2 sm:px-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only sm:not-sr-only sm:ml-1">{backLabel}</span>
            </Button>
          </Link>
        )}
        <h1 className={cn(
          "text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent",
          titleClassName
        )}>
          {title}
        </h1>
      </div>
      {children && (
        <div className="flex items-center gap-2">
          {children}
        </div>
      )}
    </div>
  )
}