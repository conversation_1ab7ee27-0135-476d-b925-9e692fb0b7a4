'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { toast } from 'sonner'
import { Music2, Loader2, CheckCircle } from 'lucide-react'

interface AudioSettings {
  crossfade: number
  replayGain: {
    mode: string
    status: string
  }
}

export function AudioSettings() {
  const [settings, setSettings] = useState<AudioSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  
  // Local state for editing
  const [crossfadeEnabled, setCrossfadeEnabled] = useState(false)
  const [crossfadeDuration, setCrossfadeDuration] = useState(3)
  const [replayGainMode, setReplayGainMode] = useState<'off' | 'track' | 'album' | 'auto'>('off')
  
  useEffect(() => {
    loadSettings()
  }, [])
  
  const loadSettings = async () => {
    try {
      const response = await fetch('/api/mpd/audio-enhancements-debug')
      
      if (!response.ok) {
        // Handle non-200 responses
        let errorMessage = `HTTP ${response.status}`
        try {
          const errorData = await response.json()
          errorMessage = errorData.message || errorMessage
        } catch {
          // If response isn't JSON, use status text
          errorMessage = response.statusText || errorMessage
        }
        throw new Error(errorMessage)
      }
      
      const data = await response.json()
      
      if (data.success && data.settings) {
        setSettings(data.settings)
        setCrossfadeEnabled(data.settings.crossfade > 0)
        setCrossfadeDuration(data.settings.crossfade || 3)
        setReplayGainMode(data.settings.replayGain.mode as any || 'off')
        
        // Show message if settings were restored
        if (data.restored) {
          toast.info('Audio settings restored from saved preferences', {
            description: 'Your previous settings have been reapplied',
            duration: 4000
          })
        }
      } else {
        throw new Error(data.message || 'Failed to load settings')
      }
    } catch (error) {
      console.error('Failed to load audio settings:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      toast.error('Failed to load audio settings', {
        description: errorMessage,
        duration: 5000
      })
    } finally {
      setLoading(false)
    }
  }
  
  const saveSettings = async () => {
    setSaving(true)
    
    try {
      const response = await fetch('/api/mpd/audio-enhancements-debug', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          crossfade: crossfadeEnabled ? crossfadeDuration : 0,
          replayGain: {
            mode: replayGainMode,
            preventClipping: true,
            missingPreamp: -3
          }
        })
      })
      
      if (!response.ok) {
        // Handle non-200 responses
        let errorMessage = `HTTP ${response.status}`
        try {
          const errorData = await response.json()
          errorMessage = errorData.message || errorMessage
          
          // Special handling for role permission errors
          if (response.status === 403) {
            errorMessage = 'You need DJ permissions to change audio settings. Please ask an administrator to upgrade your account.'
          }
        } catch {
          // If response isn't JSON, use status text
          if (response.status === 403) {
            errorMessage = 'Permission denied - DJ role required'
          } else {
            errorMessage = response.statusText || errorMessage
          }
        }
        throw new Error(errorMessage)
      }
      
      const data = await response.json()
      
      if (data.success) {
        const description = data.persisted 
          ? 'Settings saved and will persist across sessions'
          : 'Your audio enhancements are now active'
        
        toast.success('Audio settings saved successfully', {
          description,
          duration: 3000,
          icon: <CheckCircle className="h-4 w-4" />
        })
        setSettings(data.settings)
      } else {
        throw new Error(data.message || 'Failed to save settings')
      }
    } catch (error) {
      console.error('Failed to save audio settings:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      toast.error('Failed to save audio settings', {
        description: errorMessage,
        duration: 5000
      })
    } finally {
      setSaving(false)
    }
  }
  
  const applyRecommended = async () => {
    setSaving(true)
    
    try {
      const response = await fetch('/api/mpd/audio-enhancements-debug', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ applyRecommended: true })
      })
      
      if (!response.ok) {
        // Handle non-200 responses
        let errorMessage = `HTTP ${response.status}`
        try {
          const errorData = await response.json()
          errorMessage = errorData.message || errorMessage
          
          // Special handling for role permission errors
          if (response.status === 403) {
            errorMessage = 'You need DJ permissions to change audio settings. Please ask an administrator to upgrade your account.'
          }
        } catch {
          // If response isn't JSON, use status text
          if (response.status === 403) {
            errorMessage = 'Permission denied - DJ role required'
          } else {
            errorMessage = response.statusText || errorMessage
          }
        }
        throw new Error(errorMessage)
      }
      
      const data = await response.json()
      
      if (data.success) {
        toast.success('Recommended settings applied', {
          description: '3s crossfade and automatic volume normalization enabled',
          duration: 4000,
          icon: <Music2 className="h-4 w-4" />
        })
        await loadSettings() // Reload to show the applied settings
      } else {
        throw new Error(data.message || 'Failed to apply recommended settings')
      }
    } catch (error) {
      console.error('Failed to apply recommended settings:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      toast.error('Failed to apply recommended settings', {
        description: errorMessage,
        duration: 5000
      })
    } finally {
      setSaving(false)
    }
  }
  
  if (loading) {
    return (
      <Card className="bg-white/5 dark:bg-white/5 border border-gray-200/20 dark:border-gray-700/20">
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className="bg-white/5 dark:bg-white/5 border border-gray-200/20 dark:border-gray-700/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
          <Music2 className="h-5 w-5" />
          Audio Enhancements
        </CardTitle>
        <CardDescription className="text-xs sm:text-sm">
          Configure crossfading and volume normalization for a better listening experience
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Quick Actions */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={applyRecommended}
            disabled={saving}
          >
            {saving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            Apply Recommended Settings
          </Button>
        </div>
        
        {/* Crossfade Settings */}
        <div className="space-y-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1">
              <Label htmlFor="crossfade-enabled" className="text-sm font-medium">Crossfade</Label>
              <p className="text-xs text-muted-foreground">
                Smoothly blend between tracks
              </p>
            </div>
            <Switch
              id="crossfade-enabled"
              checked={crossfadeEnabled}
              onCheckedChange={setCrossfadeEnabled}
            />
          </div>
          
          {crossfadeEnabled && (
            <div className="space-y-2 pl-2 sm:pl-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="crossfade-duration" className="text-sm">Duration</Label>
                <span className="text-xs sm:text-sm text-muted-foreground">{crossfadeDuration}s</span>
              </div>
              <Slider
                id="crossfade-duration"
                value={[crossfadeDuration]}
                onValueChange={([value]) => setCrossfadeDuration(value)}
                min={1}
                max={10}
                step={1}
                className="w-full"
              />
            </div>
          )}
        </div>
        
        {/* ReplayGain Settings */}
        <div className="space-y-4">
          <div>
            <Label htmlFor="replaygain-mode" className="text-sm font-medium">Volume Normalization (ReplayGain)</Label>
            <p className="text-xs text-muted-foreground mb-2">
              Automatically adjust volume levels between tracks
            </p>
            <Select
              value={replayGainMode}
              onValueChange={(value: any) => setReplayGainMode(value)}
            >
              <SelectTrigger id="replaygain-mode" className="text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="off">Off</SelectItem>
                <SelectItem value="track">Track Mode</SelectItem>
                <SelectItem value="album">Album Mode</SelectItem>
                <SelectItem value="auto">Auto (Smart)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {replayGainMode !== 'off' && (
            <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-md">
              <p className="font-medium mb-1">How it works:</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>Analyzes each track&apos;s loudness</li>
                <li>Adjusts playback volume to match target level</li>
                <li>Prevents sudden volume jumps between songs</li>
                <li>Preserves dynamic range within songs</li>
              </ul>
            </div>
          )}
        </div>
        
        {/* Save Button */}
        <div className="flex justify-end pt-4 border-t">
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Settings'
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}