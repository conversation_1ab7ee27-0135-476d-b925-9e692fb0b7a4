"use client"

import { useEffect, useRef } from 'react'
import { toast } from 'sonner'
import type { SongSuggestion } from '@/lib/types'

interface AutoSuggestionAdderProps {
  currentQueueLength: number
  suggestions: SongSuggestion[]
  isEnabled?: boolean
  onAutoAdd?: (suggestion: SongSuggestion) => void
}

export function AutoSuggestionAdder({
  currentQueueLength,
  suggestions,
  isEnabled = true,
  onAutoAdd
}: AutoSuggestionAdderProps) {
  const lastAddedRef = useRef<string | null>(null)
  const checkIntervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (!isEnabled) return

    const checkAndAddTopSuggestion = async () => {
      // Only add if queue has 1 or fewer songs (to prevent empty queue)
      if (currentQueueLength > 1) {
        lastAddedRef.current = null // Reset when queue is healthy
        return
      }

      // Get pending suggestions sorted by votes
      const pendingSuggestions = suggestions
        .filter(s => s.status === 'pending')
        .sort((a, b) => b.votes - a.votes)

      if (pendingSuggestions.length === 0) return

      const topSuggestion = pendingSuggestions[0]
      
      // Don't add the same suggestion twice in a row
      if (topSuggestion.id === lastAddedRef.current) return

      // No need to check for votes since all suggestions have at least 1 vote from suggester

      console.log(`[AutoSuggestionAdder] Queue low (${currentQueueLength} songs), adding top suggestion:`, topSuggestion.title)
      
      try {
        // Approve the suggestion (which adds it to queue)
        const response = await fetch(`/api/jukebox/suggestions/${topSuggestion.id}/approve`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            autoAdded: true,
            reason: 'Auto-added: Queue running low' 
          })
        })

        if (response.ok) {
          lastAddedRef.current = topSuggestion.id
          
          toast.success(
            `Auto-added: "${topSuggestion.title}" by ${topSuggestion.artist}`,
            {
              description: `Top suggestion with ${topSuggestion.votes} votes`,
              duration: 5000
            }
          )

          if (onAutoAdd) {
            onAutoAdd(topSuggestion)
          }
        } else {
          console.error('[AutoSuggestionAdder] Failed to approve suggestion')
        }
      } catch (error) {
        console.error('[AutoSuggestionAdder] Error approving suggestion:', error)
      }
    }

    // Check immediately
    checkAndAddTopSuggestion()

    // Then check every 10 seconds
    checkIntervalRef.current = setInterval(checkAndAddTopSuggestion, 10000)

    return () => {
      if (checkIntervalRef.current) {
        clearInterval(checkIntervalRef.current)
      }
    }
  }, [currentQueueLength, suggestions, isEnabled, onAutoAdd])

  // No UI component, just logic
  return null
}