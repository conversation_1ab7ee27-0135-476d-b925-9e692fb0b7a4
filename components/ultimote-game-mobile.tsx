/**
 * Mobile-optimized ulTimote Game Engine
 * Touch-friendly interface for the ultimate quiz experience
 */

"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Crown, 
  Star, 
  ArrowLeft, 
  Trophy,
  Flame,
  Music,
  Headphones,
  ImageIcon,
  Search,
  Brain,
  ChevronRight
} from "lucide-react"
import type { UlTimoteGameConfig } from "./ultimote-game-config"
import type { QuizQuestion } from "@/lib/database/quiz-data"

interface UlTimoteGameMobileProps {
  config: UlTimoteGameConfig
  onComplete: (results: any) => void
  onBackToMenu: () => void
}

interface MobileRoundInfo {
  number: number
  category: string
  icon: any
  color: string
  questions: number
}

const MOBILE_CATEGORY_INFO = {
  classic: { icon: Music, color: 'text-blue-400' },
  quickFire: { icon: Star, color: 'text-yellow-400' },
  audioTricks: { icon: Headphones, color: 'text-purple-400' },
  albumArt: { icon: ImageIcon, color: 'text-rose-400' },
  audioFingerprint: { icon: Search, color: 'text-green-400' },
  generalKnowledge: { icon: Brain, color: 'text-cyan-400' },
  'final-boss': { icon: Trophy, color: 'text-yellow-400' }
}

export function UlTimoteGameMobile({ config, onComplete, onBackToMenu }: UlTimoteGameMobileProps) {
  const [gameState, setGameState] = useState<'loading' | 'round-intro' | 'playing' | 'complete'>('loading')
  const [currentRoundIndex, setCurrentRoundIndex] = useState(0)
  const [totalScore, setTotalScore] = useState(0)
  const [totalCorrectAnswers, setTotalCorrectAnswers] = useState(0)
  const [rounds, setRounds] = useState<MobileRoundInfo[]>([])
  
  // Initialize rounds info
  useEffect(() => {
    // Simulate round generation
    const mockRounds: MobileRoundInfo[] = []
    const categories = Object.entries(config.categories)
      .filter(([_, cat]) => cat.enabled)
      .map(([key]) => key)
    
    for (let i = 0; i < config.totalRounds; i++) {
      const category = categories[i % categories.length]
      const info = MOBILE_CATEGORY_INFO[category as keyof typeof MOBILE_CATEGORY_INFO]
      
      mockRounds.push({
        number: i + 1,
        category,
        icon: info?.icon || Music,
        color: info?.color || 'text-blue-400',
        questions: config.questionsPerRound
      })
    }
    
    setRounds(mockRounds)
    setTimeout(() => setGameState('round-intro'), 1000)
  }, [config])
  
  const startRound = () => {
    setGameState('playing')
    // Simulate round completion after delay
    setTimeout(() => {
      handleRoundComplete()
    }, 3000)
  }
  
  const handleRoundComplete = () => {
    // Update scores
    const roundScore = Math.floor(Math.random() * 500) + 300
    const correctAnswers = Math.floor(Math.random() * config.questionsPerRound) + 1
    
    setTotalScore(prev => prev + roundScore)
    setTotalCorrectAnswers(prev => prev + correctAnswers)
    
    // Move to next round or complete
    if (currentRoundIndex < rounds.length - 1) {
      setCurrentRoundIndex(prev => prev + 1)
      setGameState('round-intro')
    } else {
      setGameState('complete')
      setTimeout(() => {
        onComplete({
          totalScore: totalScore + roundScore,
          totalQuestions: config.totalRounds * config.questionsPerRound,
          correctAnswers: totalCorrectAnswers + correctAnswers,
          gameMode: 'ultimote',
          gameName: config.gameName
        })
      }, 2000)
    }
  }
  
  if (gameState === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4 
                      flex items-center justify-center">
        <Card className="w-full max-w-sm bg-black/40 backdrop-blur-md border-white/20">
          <CardContent className="pt-8 text-center">
            <Crown className="w-16 h-16 text-yellow-400 mx-auto mb-4 animate-pulse" />
            <h2 className="text-xl font-bold mb-2">Loading Game...</h2>
            <div className="animate-spin rounded-full h-8 w-8 border-4 
                          border-yellow-500/30 border-t-yellow-500 mx-auto"></div>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  if (gameState === 'complete') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4 
                      flex items-center justify-center">
        <Card className="w-full max-w-sm bg-black/40 backdrop-blur-md border-white/20">
          <CardContent className="pt-8 text-center">
            <Trophy className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-4">Game Complete!</h2>
            <div className="space-y-3 mb-6">
              <div className="p-3 bg-white/10 rounded-lg">
                <div className="text-2xl font-bold text-purple-400">{totalScore.toLocaleString()}</div>
                <div className="text-sm text-gray-400">Total Score</div>
              </div>
              <div className="p-3 bg-white/10 rounded-lg">
                <div className="text-2xl font-bold text-blue-400">{totalCorrectAnswers}</div>
                <div className="text-sm text-gray-400">Correct Answers</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  if (gameState === 'playing') {
    const currentRound = rounds[currentRoundIndex]
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4">
        <div className="max-w-md mx-auto">
          {/* Game Header */}
          <div className="flex items-center justify-between mb-4">
            <Button variant="ghost" size="sm" onClick={onBackToMenu}>
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <Badge variant="outline" className="bg-yellow-500/20 text-yellow-300">
              Round {currentRound.number}/{rounds.length}
            </Badge>
            <div className="text-sm font-medium text-purple-400">
              {totalScore.toLocaleString()}
            </div>
          </div>
          
          {/* Progress */}
          <Progress 
            value={(currentRoundIndex / rounds.length) * 100} 
            className="h-2 mb-6"
          />
          
          {/* Placeholder for quiz content */}
          <Card className="bg-black/40 backdrop-blur-md border-white/20">
            <CardContent className="pt-6 text-center">
              <currentRound.icon className={`w-16 h-16 ${currentRound.color} mx-auto mb-4`} />
              <h2 className="text-xl font-bold mb-4">Playing {currentRound.category}</h2>
              <p className="text-gray-400 mb-6">Quiz interface would appear here</p>
              <Button onClick={handleRoundComplete} className="w-full">
                Complete Round (Demo)
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }
  
  // Round intro screen
  const currentRound = rounds[currentRoundIndex]
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" size="sm" onClick={onBackToMenu}>
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <Badge variant="outline" className="bg-purple-500/20 text-purple-300">
            {config.gameName}
          </Badge>
        </div>
        
        {/* Progress */}
        <Progress 
          value={(currentRoundIndex / rounds.length) * 100} 
          className="h-3 mb-6"
        />
        
        {/* Score Summary */}
        <div className="grid grid-cols-2 gap-3 mb-6">
          <Card className="bg-black/30 backdrop-blur-md border-white/20">
            <CardContent className="p-4 text-center">
              <div className="text-xl font-bold text-purple-400">{totalScore.toLocaleString()}</div>
              <div className="text-xs text-gray-400">Total Score</div>
            </CardContent>
          </Card>
          <Card className="bg-black/30 backdrop-blur-md border-white/20">
            <CardContent className="p-4 text-center">
              <div className="text-xl font-bold text-blue-400">{totalCorrectAnswers}</div>
              <div className="text-xs text-gray-400">Correct Answers</div>
            </CardContent>
          </Card>
        </div>
        
        {/* Round Info */}
        <Card className="bg-black/40 backdrop-blur-md border-white/20">
          <CardContent className="pt-8 text-center">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: "spring", duration: 0.6 }}
            >
              <currentRound.icon className={`w-20 h-20 ${currentRound.color} mx-auto mb-4`} />
              <h1 className="text-3xl font-bold mb-2">Round {currentRound.number}</h1>
              <p className="text-lg text-gray-300 mb-6 capitalize">
                {currentRound.category.replace(/([A-Z])/g, ' $1')} Challenge
              </p>
              
              <Badge variant="outline" className="bg-blue-500/20 text-blue-300 mb-8">
                {currentRound.questions} Questions
              </Badge>
              
              <Button
                onClick={startRound}
                size="lg"
                className="w-full bg-gradient-to-r from-purple-500 to-blue-500 
                         hover:from-purple-600 hover:to-blue-600 text-white"
              >
                <Flame className="w-5 h-5 mr-2" />
                Start Round
              </Button>
            </motion.div>
          </CardContent>
        </Card>
        
        {/* Round History */}
        {currentRoundIndex > 0 && (
          <div className="mt-6 space-y-2">
            <h3 className="text-sm font-medium text-gray-400 mb-2">Completed Rounds</h3>
            {rounds.slice(0, currentRoundIndex).map((round, idx) => (
              <div key={idx} className="flex items-center gap-2 p-2 bg-white/5 rounded-lg">
                <round.icon className={`w-4 h-4 ${round.color}`} />
                <span className="text-sm flex-1">Round {round.number}</span>
                <ChevronRight className="w-4 h-4 text-gray-500" />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}