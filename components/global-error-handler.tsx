'use client'

import { useEffect } from 'react'
import { toast } from 'sonner'

/**
 * Global Error Handler Component
 * Intercepts and converts ugly browser alerts/errors into nice toast notifications
 */
export function GlobalErrorHandler() {
  useEffect(() => {
    // Override window.alert to use toast notifications instead
    const originalAlert = window.alert
    window.alert = (message: any) => {
      console.warn('<PERSON><PERSON> intercepted and converted to toast:', message)
      toast.error('Alert', {
        description: String(message),
        duration: 5000
      })
    }

    // Override window.confirm to use toast (though it's less ideal for confirmations)
    const originalConfirm = window.confirm
    window.confirm = (message: any) => {
      console.warn('Confirm intercepted and converted to toast:', message)
      toast.warning('Confirmation Required', {
        description: String(message),
        duration: 7000,
        action: {
          label: 'OK',
          onClick: () => console.log('Confirm clicked')
        }
      })
      return true // Default to true since we can't get user input
    }

    // Enhanced error handling for unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason)
      
      // Only show toast for user-facing errors, not development errors
      if (process.env.NODE_ENV === 'production' || !String(event.reason).includes('TypeError')) {
        toast.error('Operation Failed', {
          description: 'An unexpected error occurred. Please try again.',
          duration: 5000
        })
      }
      
      // Prevent default browser error display
      event.preventDefault()
    }

    // Enhanced error handling for general errors
    const handleError = (event: ErrorEvent) => {
      // Handle "Script error" specifically (common on mobile/cross-origin)
      if (event.message === 'Script error.' && event.lineno === 0 && event.colno === 0) {
        console.warn('Cross-origin script error detected (common on mobile)', {
          filename: event.filename,
          userAgent: navigator.userAgent
        })
        
        // Don't show toast for these generic script errors
        event.preventDefault()
        return
      }
      
      console.error('Global error:', event.error instanceof Error ? event.error.message : event.message)
      
      // Only show toast for user-facing errors
      if (process.env.NODE_ENV === 'production') {
        toast.error('Application Error', {
          description: 'Something went wrong. Please refresh the page if the issue persists.',
          duration: 5000,
          action: {
            label: 'Refresh',
            onClick: () => window.location.reload()
          }
        })
      }
      
      // Prevent default browser error display
      event.preventDefault()
    }

    // Add event listeners
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    window.addEventListener('error', handleError)

    // Cleanup function
    return () => {
      // Restore original functions
      window.alert = originalAlert
      window.confirm = originalConfirm
      
      // Remove event listeners
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
      window.removeEventListener('error', handleError)
    }
  }, [])

  // This component doesn't render anything
  return null
}

/**
 * Hook for consistent error handling across the app
 */
export function useGlobalErrorHandler() {
  const handleError = (error: unknown, context?: string) => {
    console.error(`Error${context ? ` in ${context}` : ''}:`, error)
    
    let message = 'An unexpected error occurred'
    let description = 'Please try again or refresh the page'
    
    if (error instanceof Error) {
      message = error.name || 'Error'
      description = error.message || description
    } else if (typeof error === 'string') {
      description = error
    }
    
    toast.error(message, {
      description,
      duration: 5000,
      action: context ? {
        label: 'Retry',
        onClick: () => {
          console.log(`Retry requested for: ${context}`)
        }
      } : undefined
    })
  }
  
  const handleSuccess = (message: string, description?: string) => {
    toast.success(message, {
      description,
      duration: 3000
    })
  }
  
  const handleWarning = (message: string, description?: string) => {
    toast.warning(message, {
      description,
      duration: 4000
    })
  }
  
  const handleInfo = (message: string, description?: string) => {
    toast.info(message, {
      description,
      duration: 3000
    })
  }
  
  return {
    handleError,
    handleSuccess,
    handleWarning,
    handleInfo
  }
}