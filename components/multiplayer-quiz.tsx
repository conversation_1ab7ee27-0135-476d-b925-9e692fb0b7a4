"use client"

import { useState, useEffect, useCallback } from "react"
import { useMultiplayer } from "@/hooks/use-multiplayer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Clock, Users, Trophy, CheckCircle, XCircle, Loader2 } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "sonner"
import { useMultiplayerSocketDirect } from "@/hooks/use-multiplayer-socket-direct"
import { useAudioManager } from "@/hooks/use-audio-manager"
import { QuizTriviaScreen } from "./quiz-trivia-screen"
import { FavoriteButton } from "./favorite-button"

// Conditional debug logging for production cleanup
const DEBUG = process.env.NODE_ENV === 'development'
const debugLog = DEBUG ? console.log : () => {}

interface MultiplayerQuizProps {
  onGameComplete: (results: any) => void
  onBackToMenu: () => void
}

export function MultiplayerQuiz({ onGameComplete, onBackToMenu }: MultiplayerQuizProps) {
  debugLog('[MultiplayerQuiz] Component rendered')
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)
  const [hasAnswered, setHasAnswered] = useState(false)
  const [showCorrectAnswer, setShowCorrectAnswer] = useState(false)
  const [isCorrect, setIsCorrect] = useState(false)
  const [points, setPoints] = useState(0)
  const [correctAnswerText, setCorrectAnswerText] = useState<string>('')
  const [questionEnded, setQuestionEnded] = useState(false)
  const [showCountdown, setShowCountdown] = useState(false)
  const [countdown, setCountdown] = useState(3)
  const [showTrivia, setShowTrivia] = useState(false)
  const [triviaAutoAdvanceTime] = useState(10) // 10 seconds for trivia screen
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [lastPlayedQuestionId, setLastPlayedQuestionId] = useState<string | null>(null)
  
  const audioManager = useAudioManager()
  
  // Debug audioManager instance
  console.log('[MultiplayerQuiz] AudioManager instance:', {
    audioManager: !!audioManager,
    audioManagerType: audioManager ? audioManager.constructor.name : 'null',
    isReady: audioManager?.isReady ? audioManager.isReady() : 'no isReady method',
    methods: audioManager ? Object.getOwnPropertyNames(Object.getPrototypeOf(audioManager)) : []
  })
  
  const { 
    gameState, 
    currentQuestion, 
    timeRemaining, 
    leaderboard,
    gameStats,
    questionEndData,
    playerId,
    submitAnswer,
    leaveGame,
    isConnected 
  } = useMultiplayer()
  
  console.log('[MultiplayerQuiz] Hook data:', { 
    gameState, 
    currentQuestion, 
    timeRemaining, 
    isConnected,
    storedGameId: typeof window !== 'undefined' ? localStorage.getItem('currentGameId') : null,
    storedPlayerId: typeof window !== 'undefined' ? localStorage.getItem('playerId') : null
  })

  // Request game state on mount if we don't have it
  useEffect(() => {
    debugLog('[MultiplayerQuiz] Component mounted, checking game state')
    if (!gameState && isConnected) {
      const storedGameId = typeof window !== 'undefined' ? localStorage.getItem('currentGameId') : null
      const storedPlayerId = typeof window !== 'undefined' ? localStorage.getItem('playerId') : null
      if (storedGameId && storedPlayerId) {
        debugLog('[MultiplayerQuiz] No game state, requesting it')
        // The hook should handle this, but let's add a manual trigger
        // TODO: Replace with direct socket hook - temporarily disabled
        // const socket = useMultiplayerSocketDirect(...)
        // if (socket && socket.connected) {
        //   socket.emit('request-game-state', { gameId: storedGameId, playerId: storedPlayerId })
        // }
      }
    }
  }, [gameState, isConnected])
  
  // Handle game ended
  useEffect(() => {
    console.log('[MultiplayerQuiz] Game end check:', {
      status: gameState?.status,
      leaderboardLength: leaderboard.length,
      hasGameStats: !!gameStats
    })
    
    if (gameState?.status === 'finished' && leaderboard.length > 0) {
      // Mark current player in leaderboard
      const playerName = localStorage.getItem('playerName')
      const markedLeaderboard = leaderboard.map(player => ({
        ...player,
        isYou: player.name === playerName
      }))
      
      console.log('[MultiplayerQuiz] Calling onGameComplete with:', {
        leaderboard: markedLeaderboard,
        playerPosition: markedLeaderboard.find(p => p.isYou)?.position || 0,
        playerScore: markedLeaderboard.find(p => p.isYou)?.score || 0,
        gameStats: gameStats
      })
      
      onGameComplete({
        leaderboard: markedLeaderboard,
        playerPosition: markedLeaderboard.find(p => p.isYou)?.position || 0,
        playerScore: markedLeaderboard.find(p => p.isYou)?.score || 0,
        gameStats: gameStats // Use gameStats from the hook
      })
    }
  }, [gameState?.status, leaderboard, gameStats, onGameComplete])

  // Reset answer state when new question arrives
  useEffect(() => {
    if (currentQuestion) {
      debugLog('[MultiplayerQuiz] New question received:', currentQuestion)
      setSelectedAnswer(null)
      setHasAnswered(false)
      setShowCorrectAnswer(false)
      setIsCorrect(false)
      setQuestionEnded(false)
      setCorrectAnswerText('')
      setShowTrivia(false)
    }
  }, [currentQuestion?.id])

  // Handle new question
  useEffect(() => {
    if (currentQuestion && !questionEndData && !showTrivia) {
      // Check if this is truly a new question by comparing IDs
      const isNewQuestion = currentQuestion.id !== lastPlayedQuestionId
      
      if (isNewQuestion) {
        // Reset state for new question only if we're not showing trivia
        setSelectedAnswer(null)
        setHasAnswered(false)
        setShowCorrectAnswer(false)
        setIsCorrect(false)
        setPoints(0)
        setCorrectAnswerText('')
        setQuestionEnded(false)
        setIsSubmitting(false)
        console.log('[MultiplayerQuiz] New question received:', {
          category: currentQuestion.category,
          type: currentQuestion.type,
          timeLimit: currentQuestion.timeLimit,
          hasTrack: !!currentQuestion.track,
          question: currentQuestion.question
        })
        
        // Play audio if this is a music question (but not during trivia)
        if (audioManager && audioManager.isReady() && currentQuestion.track && !showTrivia) {
          console.log('[MultiplayerQuiz] Playing track for question:', {
            questionId: currentQuestion.id,
            questionNumber: gameState?.currentQuestion,
            track: currentQuestion.track,
            title: currentQuestion.track.title,
            artist: currentQuestion.track.artist
          })
          const filePath = currentQuestion.track.file || (currentQuestion.track as any).mpdFilePath
          debugLog('[MultiplayerQuiz] File path:', filePath)
          debugLog('[MultiplayerQuiz] AudioManager ready:', audioManager?.isReady?.() ?? false)
          if (filePath) {
            setLastPlayedQuestionId(currentQuestion.id)
            audioManager.playQuizTrack(
              filePath,
              currentQuestion.timeLimit,
              currentQuestion.track.previewStart
            ).catch(err => {
              console.error('[MultiplayerQuiz] Error playing track:', {
                error: err,
                message: err?.message,
                stack: err?.stack,
                filePath,
                audioManager: !!audioManager,
                isReady: audioManager?.isReady?.() ?? false
              })
            })
          } else {
            console.error('[MultiplayerQuiz] No file path found in track:', currentQuestion.track)
          }
        } else if (!audioManager?.isReady() && currentQuestion.track && !showTrivia) {
          console.warn('[MultiplayerQuiz] AudioManager not ready yet, will retry...')
          // Try again in a moment
          const retryTimeout = setTimeout(() => {
            if (audioManager?.isReady() && currentQuestion.track && !showTrivia && currentQuestion.id !== lastPlayedQuestionId) {
              const filePath = currentQuestion.track.file || (currentQuestion.track as any).mpdFilePath
              if (filePath) {
                setLastPlayedQuestionId(currentQuestion.id)
                audioManager.playQuizTrack(
                  filePath,
                  currentQuestion.timeLimit,
                  currentQuestion.track.previewStart
                ).catch(err => {
                  console.error('[MultiplayerQuiz] Error playing track on retry:', err)
                })
              }
            }
          }, 500)
          return () => clearTimeout(retryTimeout)
        }
      } else {
        console.log('[MultiplayerQuiz] Same question ID, not replaying audio')
      }
    } else if (gameState?.status === 'finished') {
      debugLog('[MultiplayerQuiz] Game ended, stopping audio')
      audioManager?.stop()
    }
  }, [currentQuestion, audioManager, gameState?.status, showTrivia, lastPlayedQuestionId])

  // Cleanup audio on unmount
  useEffect(() => {
    return () => {
      if (audioManager && audioManager.isReady()) {
        debugLog('[MultiplayerQuiz] Cleaning up audio on unmount')
        audioManager.stop()
      }
    }
  }, [audioManager])
  
  // Handle question end data
  useEffect(() => {
    if (questionEndData) {
      setShowCorrectAnswer(true)
      setQuestionEnded(true)
      setCorrectAnswerText(questionEndData.correctAnswer)
      
      // Stop audio when question ends
      if (audioManager && audioManager.isReady()) {
        console.log('[MultiplayerQuiz] Stopping audio - question ended')
        audioManager.stop()
      }
      
      // Check if player was correct
      if (selectedAnswer !== null && currentQuestion?.options) {
        const selectedOption = currentQuestion.options[selectedAnswer]
        setIsCorrect(selectedOption === questionEndData.correctAnswer)
      }
      
      // Show trivia screen after a short delay
      setTimeout(() => {
        setShowTrivia(true)
      }, 3000) // 3 seconds to show correct answer, then trivia
    }
  }, [questionEndData, selectedAnswer, currentQuestion, audioManager])

  const handleAnswerSelect = async (answerIndex: number) => {
    if (hasAnswered || !currentQuestion || isSubmitting) return
    
    console.log(`[MultiplayerQuiz] Answer selected: ${answerIndex} - ${currentQuestion.options[answerIndex]}`)
    
    setSelectedAnswer(answerIndex)
    setHasAnswered(true)
    setIsSubmitting(true)
    
    try {
      const result = await submitAnswer(answerIndex)
      
      if (result.success) {
        // Don't reveal if answer is correct yet
        console.log(`[MultiplayerQuiz] Answer submitted successfully`)
        
        // Just show that answer was recorded
        toast.success('Answer submitted!', {
          duration: 2000,
          style: {
            background: '#3b82f6',
            color: 'white'
          }
        })
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleLeaveGame = () => {
    if (window.confirm('Are you sure you want to leave the game?')) {
      leaveGame()
      onBackToMenu()
    }
  }
  
  // Handle trivia screen continue
  const handleTriviaComplete = () => {
    setShowTrivia(false)
    // If we have a current question waiting, play its audio now
    if (currentQuestion?.track && audioManager && audioManager.isReady()) {
      const filePath = currentQuestion.track.file || (currentQuestion.track as any).mpdFilePath
      if (filePath) {
        debugLog('[MultiplayerQuiz] Playing track after trivia complete')
        audioManager.playQuizTrack(
          filePath,
          currentQuestion.timeLimit,
          currentQuestion.track.previewStart
        ).catch(err => {
          console.error('[MultiplayerQuiz] Error playing track after trivia:', err)
        })
      }
    }
  }

  // Get current player from game state
  const currentPlayer = gameState?.players.find(p => p.id === playerId)

  // Show game starting countdown
  useEffect(() => {
    if (gameState?.status === 'playing' && !currentQuestion && !showCountdown && !questionEndData) {
      debugLog('[MultiplayerQuiz] Showing pre-game countdown')
      setShowCountdown(true)
      setCountdown(3)
      
      const countdownInterval = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(countdownInterval)
            setShowCountdown(false)
            return 0
          }
          return prev - 1
        })
      }, 1000)
      
      return () => clearInterval(countdownInterval)
    }
  }, [gameState?.status, gameState?.currentQuestion])

  if (!gameState) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardContent className="py-12">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-amber-500 to-orange-500 animate-pulse" />
            <p className="text-lg">Loading game...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show countdown before first question (but don't block if question is already available)
  if (showCountdown && !currentQuestion) {
    return (
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50">
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="bg-gradient-to-br from-white to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-3xl p-16 text-center shadow-2xl"
        >
          <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Get Ready!
          </h2>
          <motion.div
            key={countdown}
            initial={{ scale: 0.5, opacity: 0, rotate: -180 }}
            animate={{ scale: 1, opacity: 1, rotate: 0 }}
            exit={{ scale: 1.5, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
            className="text-8xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent"
          >
            {countdown || 'GO!'}
          </motion.div>
        </motion.div>
      </div>
    )
  }

  // Show waiting between questions
  if (!currentQuestion && gameState.status === 'playing') {
    debugLog('[MultiplayerQuiz] Waiting for question, gameState:', gameState)
    console.log('[MultiplayerQuiz] Game settings detail:', {
      gameMode: gameState.settings?.gameMode,
      timePerQuestion: gameState.settings?.timePerQuestion,
      ultimoteConfig: gameState.settings?.ultimoteConfig,
      totalQuestions: gameState.totalQuestions,
      rawSettings: JSON.stringify(gameState.settings)
    })
    return (
      <Card className="max-w-4xl mx-auto">
        <CardContent className="py-12">
          <div className="text-center space-y-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-blue-500 to-purple-500"
            />
            <p className="text-lg">Next question coming up...</p>
            <p className="text-sm text-gray-500">
              Question {gameState.currentQuestionIndex + 1} of {gameState.totalQuestions}
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  // Show trivia screen after question ends (only for music questions)
  if (showTrivia && currentQuestion?.track && questionEndData) {
    // Get current scores with player info
    const playersWithScores = gameState.players.map(player => ({
      id: player.id,
      name: player.name,
      score: player.score,
      isYou: player.id === playerId,
      scoreChange: player.lastScoreChange || 0
    }))
    
    return (
      <QuizTriviaScreen
        track={currentQuestion.track}
        players={playersWithScores}
        currentPlayerId={playerId}
        questionNumber={gameState.currentQuestionIndex + 1}
        totalQuestions={gameState.totalQuestions}
        onContinue={handleTriviaComplete}
        autoAdvanceTime={triviaAutoAdvanceTime}
      />
    )
  }

  // Show loading/waiting state when game is finished (parent should handle the end screen)
  if (gameState.status === 'finished') {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardContent className="py-12">
          <div className="text-center space-y-4">
            <Trophy className="w-16 h-16 mx-auto text-yellow-500" />
            <p className="text-xl font-semibold">Game Complete!</p>
            <p className="text-gray-500">Calculating final scores...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Game Header */}
      <Card className="backdrop-blur-lg bg-white/80 dark:bg-gray-900/80 border-white/20 shadow-xl">
        <CardContent className="py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Badge variant="secondary" className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {gameState.players.length} Players
              </Badge>
              <Badge variant="outline">
                Question {gameState.currentQuestionIndex + 1} of {gameState.totalQuestions}
              </Badge>
              {gameState?.settings?.timePerQuestion && (
                <Badge variant="secondary">
                  {gameState.settings.timePerQuestion}s per question
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm text-gray-500">Your Score</p>
                <p className="text-xl font-bold">{currentPlayer?.score || 0}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLeaveGame}
              >
                Leave Game
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Question Card */}
      {currentQuestion ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="backdrop-blur-lg bg-white/80 dark:bg-gray-900/80 border-white/20 shadow-2xl">
          <CardHeader>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              {gameState?.settings?.gameMode === 'ultimote' && (
                <Badge variant="default" className="bg-gradient-to-r from-purple-600 to-pink-600">
                  UlTimote
                </Badge>
              )}
              <Badge variant="secondary">{currentQuestion?.category || 'Quiz'}</Badge>
              {currentQuestion?.track && (
                <FavoriteButton 
                  track={currentQuestion.track} 
                  userId={playerId}
                  size="icon"
                  variant="ghost"
                />
              )}
            </div>
            <div className="flex flex-col items-end">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-gray-500" />
                <span className="text-xl font-bold">{timeRemaining}s</span>
              </div>
              <span className="text-xs text-gray-500">
                {Math.round(1000 - ((currentQuestion.timeLimit - timeRemaining) / currentQuestion.timeLimit) * 900)} pts
              </span>
            </div>
          </div>
          <Progress 
            value={(timeRemaining / currentQuestion.timeLimit) * 100} 
            className="h-2 mb-4"
          />
          <CardTitle className="text-xl text-center">
            {currentQuestion.question}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {currentQuestion.options?.map((option, index) => {
              const isSelected = selectedAnswer === index
              const isCorrectAnswer = showCorrectAnswer && option === correctAnswerText
              const isWrongAnswer = showCorrectAnswer && isSelected && option !== correctAnswerText
              
              return (
                <motion.button
                  key={`${currentQuestion.id}-option-${index}`}
                  whileHover={!hasAnswered && !isSubmitting ? { scale: 1.02 } : {}}
                  whileTap={!hasAnswered && !isSubmitting ? { scale: 0.98 } : {}}
                  onClick={() => handleAnswerSelect(index)}
                  disabled={hasAnswered || isSubmitting}
                  className={`
                    relative p-4 rounded-xl border-2 text-left transition-all duration-200 font-medium
                    ${!hasAnswered && !isSubmitting && 'hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:shadow-lg hover:scale-[1.02]'}
                    ${isSelected && !showCorrectAnswer && 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg'}
                    ${isCorrectAnswer && 'border-green-500 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 shadow-xl'}
                    ${isWrongAnswer && 'border-red-500 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20'}
                    ${!isSelected && !isCorrectAnswer && hasAnswered && 'opacity-50'}
                    ${isSubmitting && 'cursor-not-allowed'}
                  `}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{option}</span>
                    {isCorrectAnswer && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                    {isWrongAnswer && (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                  </div>
                </motion.button>
              )
            })}
          </div>

          {hasAnswered && !showCorrectAnswer && (
            <div className="mt-6 text-center">
              <p className="text-gray-500">Waiting for other players...</p>
            </div>
          )}

          {showCorrectAnswer && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg"
            >
              <p className="font-semibold mb-2">
                {isCorrect ? '🎉 Correct!' : '❌ Wrong Answer'}
              </p>
              {isCorrect && currentPlayer?.lastScoreChange && currentPlayer.lastScoreChange > 0 && (
                <div className="space-y-1">
                  <p className="text-green-600 dark:text-green-400 font-bold text-xl">
                    +{currentPlayer.lastScoreChange} points
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {currentPlayer.lastScoreChange >= 900 ? '⚡ Lightning fast response!' : 
                     currentPlayer.lastScoreChange >= 700 ? '🎯 Quick thinking!' :
                     currentPlayer.lastScoreChange >= 500 ? '✓ Good timing!' :
                     'Next time, answer faster for more points!'}
                  </p>
                </div>
              )}
              {questionEndData?.explanation && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                  {questionEndData.explanation}
                </p>
              )}
            </motion.div>
          )}
        </CardContent>
      </Card>
      </motion.div>
      ) : (
        <Card className="backdrop-blur-lg bg-white/80 dark:bg-gray-900/80 border-white/20 shadow-xl">
          <CardContent className="py-12">
            <div className="text-center space-y-4">
              <Loader2 className="w-12 h-12 mx-auto animate-spin text-blue-500" />
              <p className="text-lg">Waiting for next question...</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Live Leaderboard */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Card className="backdrop-blur-lg bg-white/80 dark:bg-gray-900/80 border-white/20 shadow-xl">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500 animate-pulse" />
              Live Scores
            </CardTitle>
          </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {gameState.players
              .sort((a, b) => b.score - a.score)
              .slice(0, 5)
              .map((player, index) => (
                <div
                  key={player.id}
                  className={`
                    flex items-center justify-between p-3 rounded-lg transition-all duration-200
                    ${player.id === playerId ? 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 shadow-md' : 'hover:bg-gray-50 dark:hover:bg-gray-800'}
                  `}
                >
                  <div className="flex items-center gap-3">
                    <span className="font-bold text-lg w-6">{index + 1}</span>
                    <div className="text-center space-y-2">
                      <div className="text-2xl font-bold">
                        {player.name}
                        {player.id === playerId && ' (You)'}
                      </div>
                      <div className="text-xl">{player.score} points</div>
                      {(player as any).lastScoreChange && (player as any).lastScoreChange > 0 && (
                        <div className="text-green-500 font-medium">
                          +{(player as any).lastScoreChange}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
      </motion.div>
    </div>
  )
}