"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { AlertCircle, CheckCircle, XCircle, RefreshCw } from "lucide-react"
import { getMultiplayerSocket } from "@/lib/multiplayer-socket"

export function WebSocketDebugger() {
  const [isConnected, setIsConnected] = useState(false)
  const [socketId, setSocketId] = useState<string | null>(null)
  const [messages, setMessages] = useState<Array<{ type: 'sent' | 'received', event: string, data: any, timestamp: string }>>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const socket = getMultiplayerSocket()
    
    // Connection status
    const updateConnectionStatus = () => {
      setIsConnected(socket.connected)
      setSocketId(socket.id || null)
      if (socket.connected) {
        setError(null)
      }
    }
    
    socket.on('connect', () => {
      console.log('[WebSocketDebugger] Connected:', socket.id)
      updateConnectionStatus()
      addMessage('received', 'connect', { id: socket.id })
    })
    
    socket.on('disconnect', () => {
      console.log('[WebSocketDebugger] Disconnected')
      updateConnectionStatus()
      addMessage('received', 'disconnect', {})
    })
    
    socket.on('connect_error', (err) => {
      console.error('[WebSocketDebugger] Connection error:', err)
      setError(err.message)
      addMessage('received', 'connect_error', { message: err.message })
    })
    
    // Intercept all events
    const originalEmit = socket.emit
    socket.emit = function(...args: any[]) {
      const [event, ...data] = args
      console.log('[WebSocketDebugger] Emitting:', event, data)
      addMessage('sent', event, data[0])
      return originalEmit.apply(socket, args)
    }
    
    // Listen to common events
    const events = ['game-state', 'question', 'game-started', 'game-ended', 'error']
    events.forEach(event => {
      socket.on(event, (data) => {
        console.log('[WebSocketDebugger] Received:', event, data)
        addMessage('received', event, data)
      })
    })
    
    // Initial status
    updateConnectionStatus()
    
    return () => {
      // Restore original emit
      socket.emit = originalEmit
      
      // Remove listeners
      socket.off('connect')
      socket.off('disconnect')
      socket.off('connect_error')
      events.forEach(event => socket.off(event))
    }
  }, [])
  
  const addMessage = (type: 'sent' | 'received', event: string, data: any) => {
    setMessages(prev => [...prev.slice(-19), {
      type,
      event,
      data,
      timestamp: new Date().toLocaleTimeString()
    }])
  }
  
  const testConnection = () => {
    const socket = getMultiplayerSocket()
    if (!socket.connected) {
      socket.connect()
    } else {
      // Test with a simple emit
      socket.emit('test-connection', { timestamp: Date.now() })
    }
  }
  
  const clearMessages = () => {
    setMessages([])
  }

  return (
    <Card className="fixed bottom-4 right-4 w-96 max-h-[600px] z-50 shadow-xl">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center justify-between">
          <span className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            WebSocket Debugger
          </span>
          <div className="flex items-center gap-2">
            <Badge variant={isConnected ? "default" : "destructive"} className="text-xs">
              {isConnected ? "Connected" : "Disconnected"}
            </Badge>
            <Button
              size="icon"
              variant="ghost"
              className="h-6 w-6"
              onClick={testConnection}
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Connection Info */}
        <div className="text-xs space-y-1 p-2 bg-gray-100 dark:bg-gray-800 rounded">
          <div className="flex justify-between">
            <span>Status:</span>
            <span className="font-mono flex items-center gap-1">
              {isConnected ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <XCircle className="h-3 w-3 text-red-500" />
              )}
              {isConnected ? "Connected" : "Disconnected"}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Socket ID:</span>
            <span className="font-mono text-[10px]">{socketId || "N/A"}</span>
          </div>
          <div className="flex justify-between">
            <span>URL:</span>
            <span className="font-mono text-[10px]">ws://localhost:3001</span>
          </div>
          {error && (
            <div className="text-red-500 text-[10px] mt-1">
              Error: {error}
            </div>
          )}
        </div>
        
        {/* Messages */}
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-xs font-medium">Messages ({messages.length})</span>
            <Button
              size="sm"
              variant="ghost"
              className="h-6 text-xs"
              onClick={clearMessages}
            >
              Clear
            </Button>
          </div>
          <div className="max-h-[350px] overflow-y-auto space-y-1 text-xs">
            {messages.length === 0 ? (
              <div className="text-gray-500 text-center py-4">
                No messages yet
              </div>
            ) : (
              messages.map((msg, i) => (
                <div
                  key={i}
                  className={`p-2 rounded text-[10px] ${
                    msg.type === 'sent' 
                      ? 'bg-blue-50 dark:bg-blue-900/20 ml-4' 
                      : 'bg-green-50 dark:bg-green-900/20 mr-4'
                  }`}
                >
                  <div className="flex justify-between items-start mb-1">
                    <span className="font-mono font-semibold">
                      {msg.type === 'sent' ? '→' : '←'} {msg.event}
                    </span>
                    <span className="text-gray-500">{msg.timestamp}</span>
                  </div>
                  {msg.data && (
                    <details className="mt-1">
                      <summary className="cursor-pointer text-gray-600">Data</summary>
                      <pre className="mt-1 p-1 bg-gray-100 dark:bg-gray-800 rounded overflow-x-auto">
                        {JSON.stringify(msg.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
        
        {/* Debug Info */}
        <details className="text-xs">
          <summary className="cursor-pointer font-medium">Debug Info</summary>
          <div className="mt-2 space-y-1 text-[10px]">
            <p>• Check browser console for detailed logs</p>
            <p>• Network tab → WS filter shows WebSocket frames</p>
            <p>• If no WS tab, connection might be failing</p>
            <p>• Try: localStorage.clear() and refresh</p>
          </div>
        </details>
      </CardContent>
    </Card>
  )
}