"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Heart,
  Plus,
  Music,
  Send,
  Loader2,
  RefreshCw,
  Trash2
} from "lucide-react"
import { AlbumArt } from './album-art'
import { TextOverflowTooltip } from './ui/text-overflow-tooltip'
import { toast } from 'sonner'
import type { Song, UserRole } from "@/lib/types"

interface FavoritesTabProps {
  // User state
  userRole: UserRole
  userProfile: { id?: string } | null
  
  // Actions
  handleSuggestSong: (song: Song) => Promise<void>
  handleAddToQueue?: (song: Song) => Promise<void>
  handleToggleFavorite: (song: Song) => Promise<void>
  
  // Loading state functions
  isSongLoading: (songId: string) => boolean
  isFavoriteLoading: (songId: string) => boolean
  
  // Permission functions
  hasDirectQueueAccess: () => boolean
  
  // Optional favorites data from parent
  userFavorites?: Set<string>
  onRefresh?: () => Promise<void>
  isLoading?: boolean
}

interface FavoriteSong extends Song {
  addedAt?: string
}

export function FavoritesTab({
  userRole,
  userProfile,
  handleSuggestSong,
  handleAddToQueue,
  handleToggleFavorite,
  isSongLoading,
  isFavoriteLoading,
  hasDirectQueueAccess,
  userFavorites,
  onRefresh,
  isLoading: propIsLoading
}: FavoritesTabProps) {
  const [favorites, setFavorites] = useState<FavoriteSong[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Load user favorites
  const loadFavorites = async () => {
    if (!userProfile?.id) return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/jukebox/favorites?userId=${userProfile.id}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        const text = await response.text()
        console.error('API returned non-JSON response:', text.substring(0, 200))
        throw new Error('API returned HTML instead of JSON')
      }
      
      const data = await response.json()

      if (data.success) {
        // Convert database favorites to Song objects
        const favoriteSongs: FavoriteSong[] = data.favorites.map((fav: any) => ({
          id: fav.songId,
          filePath: fav.filePath,
          title: fav.title,
          artist: fav.artist,
          album: fav.album,
          duration: fav.duration,
          genre: fav.genre,
          year: fav.year,
          albumArtUrl: fav.albumArtUrl,
          addedAt: fav.addedAt
        }))
        setFavorites(favoriteSongs)
      }
    } catch (error) {
      console.error('Failed to load favorites:', error)
      toast.error('Failed to load favorites')
    } finally {
      setIsLoading(false)
    }
  }

  // Load favorites when component mounts or user profile changes
  useEffect(() => {
    // If userFavorites is provided from parent, don't load separately
    if (userFavorites !== undefined) {
      return
    }
    if (userProfile?.id) {
      loadFavorites()
    }
  }, [userProfile?.id])
  

  // Handle removing from favorites
  const handleRemoveFavorite = async (song: Song) => {
    // Simply call the toggle function - the parent component handles state updates
    // via the useUserFavorites hook which already implements optimistic updates
    await handleToggleFavorite(song)
  }

  return (
    <Card className="bg-white/10 backdrop-blur-md border-white/20">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Heart className="w-5 h-5 text-red-400" />
              My Favorites
            </CardTitle>
            <CardDescription className="hidden sm:block">
              Your saved favorite songs - {favorites.length} song{favorites.length !== 1 ? 's' : ''}
            </CardDescription>
          </div>
          <Button 
            onClick={onRefresh || loadFavorites} 
            variant="ghost" 
            size="icon" 
            disabled={propIsLoading ?? isLoading}
            className="h-8 w-8 sm:h-9 sm:w-9"
          >
            <RefreshCw className={`w-3 h-3 sm:w-4 sm:h-4 ${(propIsLoading ?? isLoading) ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="px-2 sm:px-6">
        <ScrollArea className="h-[65vh] sm:h-[60vh]">
          {!userProfile?.id ? (
            <div className="flex justify-center items-center h-full">
              <div className="text-center space-y-4">
                <Heart className="w-16 h-16 text-gray-400 mx-auto" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-300">Sign in to view favorites</h3>
                  <p className="text-sm text-gray-400">
                    Create a profile to save and access your favorite songs
                  </p>
                </div>
              </div>
            </div>
          ) : (propIsLoading ?? isLoading) ? (
            <div className="flex justify-center items-center h-full">
              <div className="text-center space-y-2">
                <Loader2 className="w-8 h-8 animate-spin text-gray-400 mx-auto" />
                <p className="text-sm text-gray-400">Loading your favorites...</p>
              </div>
            </div>
          ) : favorites.length === 0 ? (
            <div className="flex justify-center items-center h-full">
              <div className="text-center space-y-4">
                <Heart className="w-16 h-16 text-gray-400 mx-auto" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-300">No favorites yet</h3>
                  <p className="text-sm text-gray-400">
                    Heart songs in the Library tab to add them to your favorites
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-2 mr-2">
              {favorites.map((song) => (
                <Card key={`${song.filePath}-${song.addedAt}`} className="p-3 bg-black/10">
                  {/* Mobile Layout - Stacked */}
                  <div className="sm:hidden">
                    {/* Song Info */}
                    <div className="flex items-center gap-2 mb-2">
                      <AlbumArt
                        trackId={song.id?.toString()}
                        fallbackUrl={song.albumArtUrl}
                        title={song.title}
                        artist={song.artist || 'Unknown Artist'}
                        album={song.album || 'Unknown Album'}
                        size="sm"
                        className="flex-shrink-0"
                      />
                      <div className="min-w-0 flex-1">
                        <TextOverflowTooltip text={song.title}>
                          <h4 className="font-semibold text-sm leading-tight line-clamp-1 break-words">
                            {song.title}
                          </h4>
                        </TextOverflowTooltip>
                        <TextOverflowTooltip text={song.artist || 'Unknown Artist'}>
                          <p className="text-xs text-gray-400 line-clamp-1 break-words">{song.artist}</p>
                        </TextOverflowTooltip>
                      </div>
                    </div>
                    
                    {/* Action buttons */}
                    <div className="flex items-center justify-between">
                      {/* Suggest/Queue buttons based on role */}
                      {hasDirectQueueAccess() ? (
                        <>
                          <Button 
                            size="sm" 
                            className="h-8 px-3" 
                            onClick={() => handleSuggestSong(song)}
                            disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                            title="Suggest this song"
                          >
                            {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                              <Loader2 className="w-4 h-4 animate-spin"/> 
                            ) : (
                              <Send className="w-4 h-4"/> 
                            )}
                          </Button>
                          <Button 
                            size="sm" 
                            variant="secondary"
                            className="h-8 px-3" 
                            onClick={() => handleAddToQueue?.(song)}
                            disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                            title="Add directly to queue"
                          >
                            {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                              <Loader2 className="w-4 h-4 animate-spin"/> 
                            ) : (
                              <Plus className="w-4 h-4"/> 
                            )}
                          </Button>
                        </>
                      ) : (
                        <Button 
                          size="sm" 
                          className="h-8 px-3" 
                          onClick={() => handleSuggestSong(song)}
                          disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                          title="Suggest this song"
                        >
                          {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                            <Loader2 className="w-4 h-4 animate-spin"/> 
                          ) : (
                            <Send className="w-4 h-4"/> 
                          )}
                        </Button>
                      )}
                      
                      {/* Spacer */}
                      <div className="flex-1"></div>
                      
                      {/* Remove from favorites button */}
                      <Button 
                        size="sm" 
                        variant="destructive"
                        className="h-8 px-2" 
                        onClick={() => handleRemoveFavorite(song)}
                        disabled={isFavoriteLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                        title="Remove from favorites"
                      >
                        {isFavoriteLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                          <Loader2 className="w-4 h-4 animate-spin"/> 
                        ) : (
                          <Trash2 className="w-4 h-4"/>
                        )}
                      </Button>
                    </div>
                  </div>
                  
                  {/* Desktop Layout - Side by side */}
                  <div className="hidden sm:flex sm:items-center sm:justify-between">
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                      <AlbumArt
                        trackId={song.id?.toString()}
                        fallbackUrl={song.albumArtUrl}
                        title={song.title}
                        artist={song.artist || 'Unknown Artist'}
                        album={song.album || 'Unknown Album'}
                        size="sm"
                        className="flex-shrink-0"
                      />
                      <div className="min-w-0 flex-1">
                        <TextOverflowTooltip text={song.title}>
                          <h4 className="font-semibold line-clamp-1 break-words">
                            {song.title}
                          </h4>
                        </TextOverflowTooltip>
                        <TextOverflowTooltip text={song.artist || 'Unknown Artist'}>
                          <p className="text-sm text-gray-400 line-clamp-1 break-words">{song.artist}</p>
                        </TextOverflowTooltip>
                        {song.addedAt && (
                          <p className="text-xs text-gray-500">
                            Added {new Date(song.addedAt).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      {/* Suggest/Queue buttons based on role */}
                      {hasDirectQueueAccess() ? (
                        <>
                          <Button 
                            size="sm" 
                            onClick={() => handleSuggestSong(song)}
                            disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                          >
                            {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                              <Loader2 className="w-4 h-4 mr-1 animate-spin"/> 
                            ) : (
                              <Send className="w-4 h-4 mr-1"/> 
                            )}
                            Suggest
                          </Button>
                          <Button 
                            size="sm" 
                            variant="secondary"
                            onClick={() => handleAddToQueue?.(song)}
                            disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                          >
                            {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                              <Loader2 className="w-4 h-4 mr-1 animate-spin"/> 
                            ) : (
                              <Plus className="w-4 h-4 mr-1"/> 
                            )}
                            Queue
                          </Button>
                        </>
                      ) : (
                        <Button 
                          size="sm" 
                          onClick={() => handleSuggestSong(song)}
                          disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                        >
                          {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                            <Loader2 className="w-4 h-4 mr-1 animate-spin"/> 
                          ) : (
                            <Send className="w-4 h-4 mr-1"/> 
                          )}
                          Suggest
                        </Button>
                      )}
                      
                      {/* Remove from favorites button */}
                      <Button 
                        size="sm" 
                        variant="destructive"
                        onClick={() => handleRemoveFavorite(song)}
                        disabled={isFavoriteLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                      >
                        {isFavoriteLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                          <Loader2 className="w-4 h-4 animate-spin"/> 
                        ) : (
                          <Trash2 className="w-4 h-4"/>
                        )}
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}