/**
 * ulTimote Game Configuration - Ultimate customizable quiz experience
 * Allows complex multi-category gameplay with mixed quiz types
 */

"use client"

import { useState, useEffect } from "react"
import dynamic from "next/dynamic"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

// Dynamically import mobile version
const UlTimoteGameConfigMobile = dynamic(
  () => import('./ultimote-game-config-mobile').then(mod => mod.UlTimoteGameConfigMobile),
  { ssr: false }
)
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from 'sonner'
import { 
  Star, 
  Settings, 
  Zap, 
  Clock, 
  Target, 
  Music, 
  Headphones,
  ImageIcon,
  Search,
  TrendingUp,
  Calendar,
  Radio,
  Shuffle,
  Award,
  Trophy,
  Crown,
  Flame,
  Brain,
  BookOpen,
  Globe
} from "lucide-react"

export interface UlTimoteGameConfig {
  // Game Structure
  totalRounds: number
  questionsPerRound: number
  
  // Quiz Categories Mix
  categories: {
    classic: { enabled: boolean; weight: number; difficulty: number; rounds: number }
    quickFire: { enabled: boolean; weight: number; rounds: number }
    audioTricks: { enabled: boolean; weight: number; effectLevel: number; rounds: number }
    albumArt: { enabled: boolean; weight: number; visualLevel: number; rounds: number }
    audioFingerprint: { enabled: boolean; weight: number; expertLevel: number; rounds: number }
    chartPosition: { enabled: boolean; weight: number; eraFocus: string; rounds: number }
    decadeChallenge: { enabled: boolean; weight: number; decades: string[]; rounds: number }
    genreSpecialist: { enabled: boolean; weight: number; genres: string[]; rounds: number }
    generalKnowledge: { enabled: boolean; weight: number; categories: string[]; difficulty: number; rounds: number }
  }
  
  // Advanced Settings
  gameFlow: {
    adaptiveDifficulty: boolean
    streakBonuses: boolean
    timeAttack: boolean
    eliminationRounds: boolean
    finalBoss: boolean
  }
  
  // Scoring System
  scoring: {
    basePoints: number
    perfectMultiplier: number
    speedBonuses: boolean
    categoryBonuses: boolean
    streakSystem: 'linear' | 'exponential' | 'fibonacci'
  }
  
  // Special Modes
  specialModes: {
    blindMode: boolean // No visual hints
    masterClass: boolean // Expert-only questions
    tournamentMode: boolean // Elimination style
    endlessMode: boolean // No round limit
  }
  
  // Music Filters
  musicFilters: {
    genres: string[]
    decades: string[]
    artists: string[]
    minPopularity: number
    excludeCommon: boolean
  }
  
  // Content Categories (from database)
  contentCategories: {
    quizCategories: string[]     // ["top-400", "german-charts", "soccer-songs"]
    thematicTags: string[]       // ["sports", "movies", "christmas", "love-songs"]
    specialLists: string[]       // ["billboard-hot-100", "rolling-stone-500"]
  }
  
  // Game Metadata
  gameName: string
  description: string
  estimatedDuration: number
  difficultyRating: number
}

interface UlTimoteGameConfigProps {
  onStartGame: (config: UlTimoteGameConfig) => void
  onBackToMenu: () => void
}

const AVAILABLE_GENRES = [
  'Rock', 'Pop', 'Hip-Hop', 'Electronic', 'Jazz', 'Classical', 'Country', 
  'R&B', 'Reggae', 'Blues', 'Folk', 'Punk', 'Metal', 'Indie', 'Alternative'
]

const AVAILABLE_DECADES = [
  '1950s', '1960s', '1970s', '1980s', '1990s', '2000s', '2010s', '2020s'
]

const AVAILABLE_GENERAL_CATEGORIES = [
  'allgemein', 'art', 'economy', 'film', 'food', 'funny', 'geography', 
  'guess', 'history', 'kinderserien', 'nature', 'science', 'soccer', 
  'sport', 'technology'
]

// Quiz category display names
const CATEGORY_DISPLAY_NAMES = {
  classic: 'Classic Quiz',
  quickFire: 'Quick Fire',
  audioTricks: 'Audio Tricks',
  albumArt: 'Album Art',
  audioFingerprint: 'Audio Fingerprint',
  chartPosition: 'Chart Position',
  decadeChallenge: 'Guess the Year',
  genreSpecialist: 'Genre Specialist',
  generalKnowledge: 'General Knowledge'
}

const CATEGORY_DESCRIPTIONS = {
  classic: 'Identify the artist of the song',
  quickFire: 'Rapid-fire questions with time pressure',
  audioTricks: 'Songs with audio effects (speed/pitch changes)',
  albumArt: 'Identify songs from album covers',
  audioFingerprint: 'Expert-level audio recognition from short clips',
  chartPosition: 'Guess the highest chart position',
  decadeChallenge: 'Guess the exact release year using a slider',
  genreSpecialist: 'Identify the genre of the song',
  generalKnowledge: 'Non-music trivia questions'
}

// Category display names and metadata
const CATEGORY_METADATA = {
  'allgemein': { name: 'Allgemeinwissen', icon: '🧠', count: 200 },
  'art': { name: 'Art', icon: '🎨', count: 150 },
  'economy': { name: 'Economy', icon: '💰', count: 100 },
  'film': { name: 'Film & Movies', icon: '🎬', count: 249 },
  'food': { name: 'Food & Drinks', icon: '🍔', count: 150 },
  'funny': { name: 'Funny & Trivia', icon: '😄', count: 350 },
  'geography': { name: 'Geography', icon: '🌍', count: 150 },
  'guess': { name: 'Schätzfragen', icon: '🔢', count: 162 },
  'history': { name: 'History', icon: '📚', count: 250 },
  'kinderserien': { name: "Children's Shows", icon: '📺', count: 150 },
  'nature': { name: 'Nature', icon: '🌿', count: 150 },
  'science': { name: 'Science', icon: '🔬', count: 250 },
  'soccer': { name: 'Soccer/Football', icon: '⚽', count: 250 },
  'sport': { name: 'Sports', icon: '🏆', count: 90 },
  'technology': { name: 'Technology', icon: '💻', count: 249 }
}

const PRESET_CONFIGURATIONS = {
  'Ultimate Challenge': {
    description: 'The most comprehensive quiz experience - 50% music, 50% general knowledge',
    totalRounds: 15,
    questionsPerRound: 5,
    categories: {
      classic: { enabled: true, weight: 15, difficulty: 4, rounds: 3 },
      quickFire: { enabled: true, weight: 10, rounds: 2 },
      audioTricks: { enabled: true, weight: 8, effectLevel: 3, rounds: 2 },
      albumArt: { enabled: true, weight: 8, visualLevel: 3, rounds: 2 },
      audioFingerprint: { enabled: true, weight: 5, expertLevel: 4, rounds: 1 },
      chartPosition: { enabled: true, weight: 5, eraFocus: 'all', rounds: 1 },
      decadeChallenge: { enabled: false, weight: 0, decades: [], rounds: 0 },
      genreSpecialist: { enabled: false, weight: 0, genres: [], rounds: 0 },
      generalKnowledge: { enabled: true, weight: 50, categories: ['funny', 'history', 'science', 'film', 'sport', 'technology', 'geography'], difficulty: 3, rounds: 5 }
    }
  },
  'Speed Master': {
    description: 'Fast-paced quiz with emphasis on quick thinking',
    totalRounds: 8,
    questionsPerRound: 8,
    categories: {
      classic: { enabled: true, weight: 25, difficulty: 3, rounds: 2 },
      quickFire: { enabled: true, weight: 35, rounds: 3 },
      audioTricks: { enabled: false, weight: 0, effectLevel: 1, rounds: 0 },
      albumArt: { enabled: true, weight: 15, visualLevel: 2, rounds: 1 },
      audioFingerprint: { enabled: false, weight: 0, expertLevel: 1, rounds: 0 },
      chartPosition: { enabled: true, weight: 10, eraFocus: 'modern', rounds: 1 },
      decadeChallenge: { enabled: false, weight: 0, decades: [], rounds: 0 },
      genreSpecialist: { enabled: false, weight: 0, genres: [], rounds: 0 },
      generalKnowledge: { enabled: true, weight: 15, categories: ['funny'], difficulty: 2, rounds: 1 }
    }
  },
  'Expert Analysis': {
    description: 'Deep audio analysis for music experts',
    totalRounds: 5,
    questionsPerRound: 4,
    categories: {
      classic: { enabled: false, weight: 0, difficulty: 1, rounds: 0 },
      quickFire: { enabled: false, weight: 0, rounds: 0 },
      audioTricks: { enabled: true, weight: 35, effectLevel: 5, rounds: 2 },
      albumArt: { enabled: true, weight: 25, visualLevel: 4, rounds: 1 },
      audioFingerprint: { enabled: true, weight: 40, expertLevel: 5, rounds: 2 },
      chartPosition: { enabled: false, weight: 0, eraFocus: 'all', rounds: 0 },
      decadeChallenge: { enabled: false, weight: 0, decades: [], rounds: 0 },
      genreSpecialist: { enabled: false, weight: 0, genres: [], rounds: 0 },
      generalKnowledge: { enabled: false, weight: 0, categories: [], difficulty: 1, rounds: 0 }
    }
  },
  'Knowledge Master': {
    description: 'Focus on general knowledge with 2,955 questions across 16 categories',
    totalRounds: 12,
    questionsPerRound: 5,
    categories: {
      classic: { enabled: true, weight: 15, difficulty: 3, rounds: 2 },
      quickFire: { enabled: false, weight: 0, rounds: 0 },
      audioTricks: { enabled: false, weight: 0, effectLevel: 1, rounds: 0 },
      albumArt: { enabled: true, weight: 10, visualLevel: 2, rounds: 1 },
      audioFingerprint: { enabled: false, weight: 0, expertLevel: 1, rounds: 0 },
      chartPosition: { enabled: false, weight: 0, eraFocus: 'all', rounds: 0 },
      decadeChallenge: { enabled: false, weight: 0, decades: [], rounds: 0 },
      genreSpecialist: { enabled: false, weight: 0, genres: [], rounds: 0 },
      generalKnowledge: { enabled: true, weight: 75, categories: ['funny', 'science', 'history', 'film', 'soccer', 'technology', 'food', 'nature'], difficulty: 3, rounds: 9 }
    }
  },
  'Balanced Mix': {
    description: 'Equal mix of music and general knowledge for diverse gameplay',
    totalRounds: 10,
    questionsPerRound: 5,
    categories: {
      classic: { enabled: true, weight: 25, difficulty: 3, rounds: 2 },
      quickFire: { enabled: false, weight: 0, rounds: 0 },
      audioTricks: { enabled: true, weight: 10, effectLevel: 2, rounds: 1 },
      albumArt: { enabled: true, weight: 10, visualLevel: 2, rounds: 1 },
      audioFingerprint: { enabled: false, weight: 0, expertLevel: 1, rounds: 0 },
      chartPosition: { enabled: true, weight: 5, eraFocus: 'modern', rounds: 1 },
      decadeChallenge: { enabled: false, weight: 0, decades: [], rounds: 0 },
      genreSpecialist: { enabled: false, weight: 0, genres: [], rounds: 0 },
      generalKnowledge: { enabled: true, weight: 50, categories: ['sport', 'film', 'technology', 'geography'], difficulty: 3, rounds: 5 }
    }
  }
}

export function UlTimoteGameConfig({ onStartGame, onBackToMenu }: UlTimoteGameConfigProps) {
  // Check if mobile device
  const [isMobile, setIsMobile] = useState(false)
  
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])
  
  const [availableCategories, setAvailableCategories] = useState<{
    quizCategories: string[]
    thematicTags: string[]
    specialLists: string[]
  }>({
    quizCategories: [],
    thematicTags: [],
    specialLists: []
  })

  const [availableGeneralCategories, setAvailableGeneralCategories] = useState<Array<{
    id: string
    slug: string
    name: string
    description?: string
    icon: string
    color: string
    questionCount: number
  }>>([])

  // Fetch available categories from the database
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/quiz/categories')
        if (response.ok) {
          const data = await response.json()
          console.log('Fetched music categories:', data)
          setAvailableCategories({
            quizCategories: data.categories?.quiz_categories || [],
            thematicTags: data.categories?.thematic_tags || [],
            specialLists: data.categories?.special_lists || []
          })
        }
      } catch (error) {
        console.error('Failed to fetch quiz categories:', error)
      }
    }
    fetchCategories()
  }, [])

  // Fetch available general knowledge categories
  useEffect(() => {
    const fetchGeneralCategories = async () => {
      try {
        const response = await fetch('/api/general-quiz/categories')
        if (response.ok) {
          const categories = await response.json()
          console.log('[UlTimote] Fetched general categories:', categories)
          console.log('[UlTimote] Economy category:', categories.find((c: any) => c.slug === 'economy'))
          setAvailableGeneralCategories(categories)
        }
      } catch (error) {
        console.error('Failed to fetch general quiz categories:', error)
      }
    }
    fetchGeneralCategories()
  }, [])
  const [config, setConfig] = useState<UlTimoteGameConfig>({
    totalRounds: 6,
    questionsPerRound: 5,
    categories: {
      classic: { enabled: true, weight: 20, difficulty: 3, rounds: 2 },
      quickFire: { enabled: true, weight: 10, rounds: 2 },
      audioTricks: { enabled: true, weight: 5, effectLevel: 3, rounds: 1 },
      albumArt: { enabled: true, weight: 5, visualLevel: 3, rounds: 1 },
      audioFingerprint: { enabled: false, weight: 0, expertLevel: 3, rounds: 1 },
      chartPosition: { enabled: true, weight: 5, eraFocus: 'all', rounds: 1 },
      decadeChallenge: { enabled: false, weight: 0, decades: ['1980s', '1990s'], rounds: 1 },
      genreSpecialist: { enabled: false, weight: 0, genres: ['Rock', 'Pop'], rounds: 1 },
      generalKnowledge: { enabled: true, weight: 45, categories: ['science', 'history', 'film'], difficulty: 3, rounds: 3 }
    },
    gameFlow: {
      adaptiveDifficulty: true,
      streakBonuses: true,
      timeAttack: false,
      eliminationRounds: false,
      finalBoss: true
    },
    scoring: {
      basePoints: 100,
      perfectMultiplier: 2.5,
      speedBonuses: true,
      categoryBonuses: true,
      streakSystem: 'exponential'
    },
    specialModes: {
      blindMode: false,
      masterClass: false,
      tournamentMode: false,
      endlessMode: false
    },
    musicFilters: {
      genres: [],
      decades: [],
      artists: [],
      minPopularity: 20,
      excludeCommon: false
    },
    contentCategories: {
      quizCategories: [],
      thematicTags: [],
      specialLists: []
    },
    gameName: 'My ulTimote Game',
    description: 'Custom configured ultimate quiz experience',
    estimatedDuration: 25,
    difficultyRating: 7
  })

  const [activeTab, setActiveTab] = useState('structure')
  const [selectedPreset, setSelectedPreset] = useState<string>('')

  // Calculate estimated duration and difficulty
  useEffect(() => {
    const enabledCategories = Object.values(config.categories).filter(cat => cat.enabled)
    const avgTimePerQuestion = enabledCategories.reduce((acc, cat) => {
      if (cat === config.categories.quickFire) return acc + 8 // Quick fire is faster
      if (cat === config.categories.audioFingerprint) return acc + 30 // Fingerprint takes longer
      return acc + 20 // Average time
    }, 0) / Math.max(enabledCategories.length, 1)
    
    const totalQuestions = getTotalRounds() * config.questionsPerRound
    const estimatedMinutes = Math.round((totalQuestions * avgTimePerQuestion) / 60)
    
    const avgDifficulty = enabledCategories.reduce((acc, cat) => {
      return acc + (cat.difficulty || cat.effectLevel || cat.visualLevel || cat.expertLevel || 3)
    }, 0) / Math.max(enabledCategories.length, 1)
    
    setConfig(prev => ({
      ...prev,
      estimatedDuration: estimatedMinutes,
      difficultyRating: Math.round(avgDifficulty)
    }))
  }, [config.categories, config.totalRounds, config.questionsPerRound])

  const applyPreset = (presetName: string) => {
    const preset = PRESET_CONFIGURATIONS[presetName as keyof typeof PRESET_CONFIGURATIONS]
    if (preset) {
      setConfig(prev => ({
        ...prev,
        ...preset,
        gameName: presetName,
        gameFlow: {
          adaptiveDifficulty: true,
          streakBonuses: true,
          timeAttack: presetName === 'Speed Master',
          eliminationRounds: presetName === 'Expert Analysis',
          finalBoss: true
        },
        specialModes: {
          blindMode: false,
          masterClass: presetName === 'Expert Analysis',
          tournamentMode: false,
          endlessMode: false
        }
      }))
      setSelectedPreset(presetName)
    }
  }

  const updateCategory = (categoryKey: string, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      categories: {
        ...prev.categories,
        [categoryKey]: {
          ...prev.categories[categoryKey as keyof typeof prev.categories],
          [field]: value
        }
      }
    }))
  }

  const getTotalWeight = () => {
    return Object.values(config.categories)
      .filter(cat => cat.enabled)
      .reduce((sum, cat) => sum + cat.weight, 0)
  }

  const getTotalRounds = () => {
    return Object.values(config.categories)
      .filter(cat => cat.enabled)
      .reduce((sum, cat) => sum + cat.rounds, 0) + (config.gameFlow.finalBoss ? 1 : 0)
  }

  const getCategoryIcon = (categoryKey: string) => {
    const icons = {
      classic: Music,
      quickFire: Zap,
      audioTricks: Headphones,
      albumArt: ImageIcon,
      audioFingerprint: Search,
      chartPosition: TrendingUp,
      decadeChallenge: Calendar,
      genreSpecialist: Radio,
      generalKnowledge: Brain
    }
    return icons[categoryKey as keyof typeof icons] || Music
  }

  // Use mobile version on small screens
  if (isMobile) {
    return <UlTimoteGameConfigMobile onStartGame={onStartGame} onBackToMenu={onBackToMenu} />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <Crown className="w-8 h-8 text-yellow-400" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-yellow-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
              ulTimote Game
            </h1>
            <Crown className="w-8 h-8 text-yellow-400" />
          </div>
          <p className="text-lg text-gray-300">Configure the ultimate music quiz experience</p>
        </motion.div>

        {/* Game Summary Card */}
        <Card className="bg-black/20 backdrop-blur-md border-white/20">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">{getTotalRounds()}</div>
                <div className="text-sm text-gray-400">Rounds</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">{getTotalRounds() * config.questionsPerRound}</div>
                <div className="text-sm text-gray-400">Total Questions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">{config.estimatedDuration}min</div>
                <div className="text-sm text-gray-400">Estimated Time</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star 
                      key={i} 
                      className={`w-4 h-4 ${i < config.difficultyRating ? 'text-yellow-400 fill-current' : 'text-gray-600'}`} 
                    />
                  ))}
                </div>
                <div className="text-sm text-gray-400">Difficulty</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Configuration Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6 bg-black/20 backdrop-blur-md">
            <TabsTrigger value="structure">Structure</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="gameplay">Gameplay</TabsTrigger>
            <TabsTrigger value="filters">Filters</TabsTrigger>
            <TabsTrigger value="presets">Presets</TabsTrigger>
          </TabsList>

          {/* Structure Tab */}
          <TabsContent value="structure" className="space-y-6">
            <Card className="bg-black/20 backdrop-blur-md border-white/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5 text-purple-400" />
                  Game Structure
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label>Game Name</Label>
                    <Input
                      value={config.gameName}
                      onChange={(e) => setConfig(prev => ({ ...prev, gameName: e.target.value }))}
                      placeholder="Enter game name"
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label>Description</Label>
                    <Input
                      value={config.description}
                      onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Game description"
                      className="mt-2"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label>Questions per Round: {config.questionsPerRound}</Label>
                    <Slider
                      value={[config.questionsPerRound]}
                      onValueChange={([value]) => setConfig(prev => ({ ...prev, questionsPerRound: value }))}
                      min={3}
                      max={10}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label className="text-sm text-gray-400">
                      Total Rounds: {getTotalRounds()} (calculated from individual category rounds)
                    </Label>
                    <div className="mt-4 p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                      <p className="text-xs text-purple-300">
                        Each category has its own round setting. The total is the sum of all enabled category rounds
                        {config.gameFlow.finalBoss && ' plus 1 final boss round'}.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories" className="space-y-6">
            <Card className="bg-black/20 backdrop-blur-md border-white/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-blue-400" />
                  Quiz Game Modes
                </CardTitle>
                <p className="text-sm text-gray-400">
                  Select which game modes to include. Total weight: {getTotalWeight()}%
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Music Quiz Section */}
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <Music className="w-5 h-5 text-purple-400" />
                    <h3 className="text-lg font-semibold text-purple-300">Music Quiz Modes</h3>
                    <Badge variant="outline" className="ml-auto">
                      {Object.entries(config.categories)
                        .filter(([key, cat]) => key !== 'generalKnowledge' && cat.enabled)
                        .length} active
                    </Badge>
                  </div>
                  <div className="space-y-4">
                    {Object.entries(config.categories)
                      .filter(([key]) => key !== 'generalKnowledge')
                      .map(([key, category]) => {
                      const Icon = getCategoryIcon(key)
                      return (
                        <motion.div
                      key={key}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={`p-4 rounded-lg border transition-all ${
                        category.enabled 
                          ? 'bg-white/10 border-white/20' 
                          : 'bg-gray-800/20 border-gray-700/30 opacity-60'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-1">
                            <Icon className="w-5 h-5 text-purple-400" />
                            <span className="font-semibold">{CATEGORY_DISPLAY_NAMES[key as keyof typeof CATEGORY_DISPLAY_NAMES] || key}</span>
                            <Switch
                              checked={category.enabled}
                              onCheckedChange={(enabled) => updateCategory(key, 'enabled', enabled)}
                            />
                          </div>
                          <p className="text-xs text-gray-400 ml-8">
                            {CATEGORY_DESCRIPTIONS[key as keyof typeof CATEGORY_DESCRIPTIONS]}
                          </p>
                        </div>
                        {category.enabled && (
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="bg-purple-500/20 text-purple-300">
                              {category.weight}%
                            </Badge>
                            {key === 'generalKnowledge' && category.categories && category.categories.length > 0 && (
                              <Badge variant="outline" className="bg-cyan-500/20 text-cyan-300">
                                {category.categories.reduce((total, catSlug) => {
                                  const generalCat = availableGeneralCategories.find(gc => gc.slug === catSlug)
                                  return total + (generalCat?.questionCount || 0)
                                }, 0)} questions
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                      
                      {category.enabled && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          <div>
                            <Label className="text-xs">Weight: {category.weight}%</Label>
                            <Slider
                              value={[category.weight]}
                              onValueChange={([value]) => updateCategory(key, 'weight', value)}
                              min={5}
                              max={50}
                              step={5}
                              className="mt-1"
                            />
                          </div>
                          
                          {category.difficulty !== undefined && (
                            <div>
                              <Label className="text-xs">Difficulty: {category.difficulty}</Label>
                              <Slider
                                value={[category.difficulty]}
                                onValueChange={([value]) => updateCategory(key, 'difficulty', value)}
                                min={1}
                                max={5}
                                step={1}
                                className="mt-1"
                              />
                            </div>
                          )}
                          
                          {category.rounds !== undefined && (
                            <div>
                              <Label className="text-xs">Rounds: {category.rounds}</Label>
                              <Slider
                                value={[category.rounds]}
                                onValueChange={([value]) => updateCategory(key, 'rounds', value)}
                                min={0}
                                max={5}
                                step={1}
                                className="mt-1"
                              />
                            </div>
                          )}
                          
                          {category.effectLevel !== undefined && (
                            <div>
                              <Label className="text-xs">Effect Level: {category.effectLevel}</Label>
                              <Slider
                                value={[category.effectLevel]}
                                onValueChange={([value]) => updateCategory(key, 'effectLevel', value)}
                                min={1}
                                max={5}
                                step={1}
                                className="mt-1"
                              />
                            </div>
                          )}
                          
                          {category.visualLevel !== undefined && (
                            <div>
                              <Label className="text-xs">Visual Level: {category.visualLevel}</Label>
                              <Slider
                                value={[category.visualLevel]}
                                onValueChange={([value]) => updateCategory(key, 'visualLevel', value)}
                                min={1}
                                max={5}
                                step={1}
                                className="mt-1"
                              />
                            </div>
                          )}
                          
                          {category.expertLevel !== undefined && (
                            <div>
                              <Label className="text-xs">Expert Level: {category.expertLevel}</Label>
                              <Slider
                                value={[category.expertLevel]}
                                onValueChange={([value]) => updateCategory(key, 'expertLevel', value)}
                                min={1}
                                max={5}
                                step={1}
                                className="mt-1"
                              />
                            </div>
                          )}
                        </div>
                      )}
                      
                      {/* Era focus for Chart Position */}
                      {key === 'chartPosition' && category.enabled && (
                        <div className="mt-4">
                          <Label className="text-xs font-semibold mb-2 block">Chart Era Focus:</Label>
                          <div className="grid grid-cols-3 gap-2">
                            {(['all', 'classic', 'modern'] as const).map((era) => (
                              <Button
                                key={era}
                                variant={category.eraFocus === era ? "default" : "outline"}
                                size="sm"
                                onClick={() => updateCategory(key, 'eraFocus', era)}
                                className="text-xs capitalize"
                              >
                                {era === 'all' ? 'All Eras' : era === 'classic' ? 'Classic (Pre-2000)' : 'Modern (2000+)'}
                              </Button>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {/* Decade selection for Guess the Year */}
                      {key === 'decadeChallenge' && category.enabled && (
                        <div className="mt-4">
                          <Label className="text-xs font-semibold mb-2 block">Include songs from these decades:</Label>
                          <div className="grid grid-cols-4 gap-2">
                            {AVAILABLE_DECADES.map((decade) => (
                              <Button
                                key={decade}
                                variant={category.decades?.includes(decade) ? "default" : "outline"}
                                size="sm"
                                onClick={() => {
                                  const currentDecades = category.decades || []
                                  const newDecades = currentDecades.includes(decade)
                                    ? currentDecades.filter(d => d !== decade)
                                    : [...currentDecades, decade]
                                  updateCategory(key, 'decades', newDecades)
                                }}
                                className="text-xs"
                              >
                                {decade}
                              </Button>
                            ))}
                          </div>
                          {(!category.decades || category.decades.length === 0) && (
                            <p className="text-xs text-red-400 mt-2">
                              ⚠️ Select at least one decade to include songs from
                            </p>
                          )}
                          {category.decades && category.decades.length > 0 && (
                            <p className="text-xs text-gray-400 mt-2">
                              Players will guess exact years for songs from: {category.decades.join(', ')}
                            </p>
                          )}
                        </div>
                      )}
                      
                      {/* Genre selection for Genre Specialist */}
                      {key === 'genreSpecialist' && category.enabled && (
                        <div className="mt-4">
                          <Label className="text-xs font-semibold mb-2 block">Include these genres:</Label>
                          <div className="grid grid-cols-3 gap-2">
                            {AVAILABLE_GENRES.slice(0, 9).map((genre) => (
                              <Button
                                key={genre}
                                variant={category.genres?.includes(genre) ? "default" : "outline"}
                                size="sm"
                                onClick={() => {
                                  const currentGenres = category.genres || []
                                  const newGenres = currentGenres.includes(genre)
                                    ? currentGenres.filter(g => g !== genre)
                                    : [...currentGenres, genre]
                                  updateCategory(key, 'genres', newGenres)
                                }}
                                className="text-xs"
                              >
                                {genre}
                              </Button>
                            ))}
                          </div>
                          <details className="mt-2">
                            <summary className="text-xs text-gray-400 cursor-pointer">Show more genres...</summary>
                            <div className="grid grid-cols-3 gap-2 mt-2">
                              {AVAILABLE_GENRES.slice(9).map((genre) => (
                                <Button
                                  key={genre}
                                  variant={category.genres?.includes(genre) ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => {
                                    const currentGenres = category.genres || []
                                    const newGenres = currentGenres.includes(genre)
                                      ? currentGenres.filter(g => g !== genre)
                                      : [...currentGenres, genre]
                                    updateCategory(key, 'genres', newGenres)
                                  }}
                                  className="text-xs"
                                >
                                  {genre}
                                </Button>
                              ))}
                            </div>
                          </details>
                          {(!category.genres || category.genres.length === 0) && (
                            <p className="text-xs text-red-400 mt-2">
                              ⚠️ Select at least one genre
                            </p>
                          )}
                        </div>
                      )}
                    </motion.div>
                      )
                    })}
                  </div>
                </div>

                {/* General Knowledge Section */}
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <Brain className="w-5 h-5 text-cyan-400" />
                    <h3 className="text-lg font-semibold text-cyan-300">General Knowledge Quiz</h3>
                    <Badge variant="outline" className="ml-auto">
                      {config.categories.generalKnowledge.enabled ? 
                        `${config.categories.generalKnowledge.categories.length} categories` : 
                        'Disabled'}
                    </Badge>
                  </div>
                  
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className={`p-4 rounded-lg border transition-all ${
                      config.categories.generalKnowledge.enabled 
                        ? 'bg-white/10 border-white/20' 
                        : 'bg-gray-800/20 border-gray-700/30 opacity-60'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-1">
                          <Brain className="w-5 h-5 text-cyan-400" />
                          <span className="font-semibold">General Knowledge</span>
                          <Switch
                            checked={config.categories.generalKnowledge.enabled}
                            onCheckedChange={(enabled) => updateCategory('generalKnowledge', 'enabled', enabled)}
                          />
                        </div>
                        <p className="text-xs text-gray-400 ml-8">
                          Non-music trivia questions from categories like Politik, Technic, Geography, History, etc.
                        </p>
                      </div>
                      {config.categories.generalKnowledge.enabled && (
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="bg-cyan-500/20 text-cyan-300">
                            {config.categories.generalKnowledge.weight}%
                          </Badge>
                          {config.categories.generalKnowledge.categories && config.categories.generalKnowledge.categories.length > 0 && (
                            <Badge variant="outline" className="bg-green-500/20 text-green-300">
                              {config.categories.generalKnowledge.categories.reduce((total, catSlug) => {
                                const generalCat = availableGeneralCategories.find(gc => gc.slug === catSlug)
                                return total + (generalCat?.questionCount || 0)
                              }, 0)} questions
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {config.categories.generalKnowledge.enabled && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <Label className="text-xs">Weight: {config.categories.generalKnowledge.weight}%</Label>
                            <Slider
                              value={[config.categories.generalKnowledge.weight]}
                              onValueChange={([value]) => updateCategory('generalKnowledge', 'weight', value)}
                              min={5}
                              max={50}
                              step={5}
                              className="mt-1"
                            />
                          </div>
                          
                          <div>
                            <Label className="text-xs">Difficulty: {config.categories.generalKnowledge.difficulty}</Label>
                            <Slider
                              value={[config.categories.generalKnowledge.difficulty]}
                              onValueChange={([value]) => updateCategory('generalKnowledge', 'difficulty', value)}
                              min={1}
                              max={5}
                              step={1}
                              className="mt-1"
                            />
                          </div>
                          
                          <div>
                            <Label className="text-xs">Rounds: {config.categories.generalKnowledge.rounds}</Label>
                            <Slider
                              value={[config.categories.generalKnowledge.rounds]}
                              onValueChange={([value]) => updateCategory('generalKnowledge', 'rounds', value)}
                              min={0}
                              max={10}
                              step={1}
                              className="mt-1"
                            />
                          </div>
                        </div>
                        
                        <div className="mt-4">
                          <Label className="text-xs font-semibold mb-2 block">Select Knowledge Categories:</Label>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                            {availableGeneralCategories.map((category) => (
                              <Button
                                key={category.slug}
                                variant={config.categories.generalKnowledge.categories.includes(category.slug) ? "default" : "outline"}
                                size="sm"
                                onClick={() => {
                                  const categories = config.categories.generalKnowledge.categories.includes(category.slug)
                                    ? config.categories.generalKnowledge.categories.filter(c => c !== category.slug)
                                    : [...config.categories.generalKnowledge.categories, category.slug]
                                  console.log('[UlTimote] Updated general knowledge categories:', categories)
                                  updateCategory('generalKnowledge', 'categories', categories)
                                }}
                                className="text-xs justify-start"
                              >
                                <span className="flex items-center gap-1">
                                  <span>{category.icon}</span>
                                  <span>{category.name}</span>
                                  <span className="text-xs opacity-60">({category.questionCount})</span>
                                </span>
                              </Button>
                            ))}
                          </div>
                          {(!config.categories.generalKnowledge.categories || config.categories.generalKnowledge.categories.length === 0) && (
                            <p className="text-xs text-red-400 mt-2">
                              ⚠️ Select at least one knowledge category
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </motion.div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Content Categories Tab */}
          <TabsContent value="content" className="space-y-6">
            <Card className="bg-black/20 backdrop-blur-md border-white/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5 text-yellow-400" />
                  Quiz Content Categories
                </CardTitle>
                <CardDescription>
                  Select specific content categories from your music library for more targeted quizzes
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Quiz Categories */}
                <div>
                  <Label className="text-base font-semibold">Quiz Categories</Label>
                  <p className="text-sm text-gray-400 mb-3">Curated music collections and chart-based categories</p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {availableCategories.quizCategories.map((category) => (
                      <Button
                        key={category}
                        variant={config.contentCategories.quizCategories.includes(category) ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          const categories = config.contentCategories.quizCategories.includes(category)
                            ? config.contentCategories.quizCategories.filter(c => c !== category)
                            : [...config.contentCategories.quizCategories, category]
                          setConfig(prev => ({
                            ...prev,
                            contentCategories: { ...prev.contentCategories, quizCategories: categories }
                          }))
                        }}
                        className="text-xs justify-start"
                      >
                        {CATEGORY_METADATA[category] ? (
                          <span className="flex items-center gap-1">
                            <span>{CATEGORY_METADATA[category].icon}</span>
                            <span>{CATEGORY_METADATA[category].name}</span>
                            <span className="text-xs opacity-60">({CATEGORY_METADATA[category].count})</span>
                          </span>
                        ) : (
                          category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
                        )}
                      </Button>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Thematic Tags */}
                <div>
                  <Label className="text-base font-semibold">Thematic Tags</Label>
                  <p className="text-sm text-gray-400 mb-3">Songs tagged by themes, moods, and occasions</p>
                  <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
                    {availableCategories.thematicTags.map((tag) => (
                      <Button
                        key={tag}
                        variant={config.contentCategories.thematicTags.includes(tag) ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          const tags = config.contentCategories.thematicTags.includes(tag)
                            ? config.contentCategories.thematicTags.filter(t => t !== tag)
                            : [...config.contentCategories.thematicTags, tag]
                          setConfig(prev => ({
                            ...prev,
                            contentCategories: { ...prev.contentCategories, thematicTags: tags }
                          }))
                        }}
                        className="text-xs justify-start"
                      >
                        {tag.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Button>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Special Lists */}
                <div>
                  <Label className="text-base font-semibold">Special Lists</Label>
                  <p className="text-sm text-gray-400 mb-3">Famous charts and curated collections</p>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {availableCategories.specialLists.map((list) => (
                      <Button
                        key={list}
                        variant={config.contentCategories.specialLists.includes(list) ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          const lists = config.contentCategories.specialLists.includes(list)
                            ? config.contentCategories.specialLists.filter(l => l !== list)
                            : [...config.contentCategories.specialLists, list]
                          setConfig(prev => ({
                            ...prev,
                            contentCategories: { ...prev.contentCategories, specialLists: lists }
                          }))
                        }}
                        className="text-xs justify-start"
                      >
                        {list.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Button>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* General Knowledge Categories */}
                <div>
                  <Label className="text-base font-semibold">General Knowledge Categories</Label>
                  <p className="text-sm text-gray-400 mb-3">Select specific categories for general knowledge questions</p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {availableGeneralCategories.map((category) => (
                      <Button
                        key={category.slug}
                        variant={config.categories.generalKnowledge.categories.includes(category.slug) ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          const categories = config.categories.generalKnowledge.categories.includes(category.slug)
                            ? config.categories.generalKnowledge.categories.filter(c => c !== category.slug)
                            : [...config.categories.generalKnowledge.categories, category.slug]
                          setConfig(prev => ({
                            ...prev,
                            categories: {
                              ...prev.categories,
                              generalKnowledge: {
                                ...prev.categories.generalKnowledge,
                                categories
                              }
                            }
                          }))
                        }}
                        className="text-xs justify-start"
                      >
                        <span className="flex items-center gap-1">
                          <span>{category.icon === 'brain' ? '🧠' : category.icon === 'target' ? '🎯' : '❓'}</span>
                          <span>{category.name}</span>
                          <span className="text-xs opacity-60">({category.questionCount})</span>
                        </span>
                      </Button>
                    ))}
                  </div>
                  {config.categories.generalKnowledge.categories.length > 0 && (
                    <div className="mt-3 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                      <p className="text-sm text-green-300">
                        <span className="font-semibold">
                          {config.categories.generalKnowledge.categories.length} general knowledge categories selected
                        </span>
                        <span className="text-xs opacity-80 block mt-1">
                          Total: {config.categories.generalKnowledge.categories.reduce((total, catSlug) => {
                            const generalCat = availableGeneralCategories.find(gc => gc.slug === catSlug)
                            return total + (generalCat?.questionCount || 0)
                          }, 0)} questions
                        </span>
                      </p>
                    </div>
                  )}
                </div>

                {/* Summary */}
                {(config.contentCategories.quizCategories.length > 0 || 
                  config.contentCategories.thematicTags.length > 0 || 
                  config.contentCategories.specialLists.length > 0 ||
                  config.categories.generalKnowledge.categories.length > 0) && (
                  <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-300 mb-2">Selected Content Categories</h4>
                    <div className="space-y-2 text-sm">
                      {config.contentCategories.quizCategories.length > 0 && (
                        <p><span className="text-gray-400">Quiz Categories:</span> {config.contentCategories.quizCategories.length} selected</p>
                      )}
                      {config.contentCategories.thematicTags.length > 0 && (
                        <p><span className="text-gray-400">Thematic Tags:</span> {config.contentCategories.thematicTags.length} selected</p>
                      )}
                      {config.contentCategories.specialLists.length > 0 && (
                        <p><span className="text-gray-400">Special Lists:</span> {config.contentCategories.specialLists.length} selected</p>
                      )}
                      {config.categories.generalKnowledge.categories.length > 0 && (
                        <p><span className="text-gray-400">General Knowledge:</span> {config.categories.generalKnowledge.categories.length} categories selected</p>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Gameplay Tab */}
          <TabsContent value="gameplay" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Game Flow */}
              <Card className="bg-black/20 backdrop-blur-md border-white/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Flame className="w-5 h-5 text-orange-400" />
                    Game Flow
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Object.entries(config.gameFlow).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <Label>{CATEGORY_DISPLAY_NAMES[key as keyof typeof CATEGORY_DISPLAY_NAMES] || key}</Label>
                      <Switch
                        checked={value}
                        onCheckedChange={(checked) => 
                          setConfig(prev => ({
                            ...prev,
                            gameFlow: { ...prev.gameFlow, [key]: checked }
                          }))
                        }
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Scoring System */}
              <Card className="bg-black/20 backdrop-blur-md border-white/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Trophy className="w-5 h-5 text-yellow-400" />
                    Scoring System
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Base Points: {config.scoring.basePoints}</Label>
                    <Slider
                      value={[config.scoring.basePoints]}
                      onValueChange={([value]) => 
                        setConfig(prev => ({
                          ...prev,
                          scoring: { ...prev.scoring, basePoints: value }
                        }))
                      }
                      min={50}
                      max={500}
                      step={25}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label>Perfect Multiplier: {config.scoring.perfectMultiplier}x</Label>
                    <Slider
                      value={[config.scoring.perfectMultiplier]}
                      onValueChange={([value]) => 
                        setConfig(prev => ({
                          ...prev,
                          scoring: { ...prev.scoring, perfectMultiplier: value }
                        }))
                      }
                      min={1.5}
                      max={5}
                      step={0.5}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label>Streak System</Label>
                    <Select
                      value={config.scoring.streakSystem}
                      onValueChange={(value) => 
                        setConfig(prev => ({
                          ...prev,
                          scoring: { ...prev.scoring, streakSystem: value as any }
                        }))
                      }
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="linear">Linear (1x, 2x, 3x...)</SelectItem>
                        <SelectItem value="exponential">Exponential (1x, 2x, 4x...)</SelectItem>
                        <SelectItem value="fibonacci">Fibonacci (1x, 2x, 3x, 5x...)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {['speedBonuses', 'categoryBonuses'].map((key) => (
                    <div key={key} className="flex items-center justify-between">
                      <Label>{CATEGORY_DISPLAY_NAMES[key as keyof typeof CATEGORY_DISPLAY_NAMES] || key}</Label>
                      <Switch
                        checked={config.scoring[key as keyof typeof config.scoring] as boolean}
                        onCheckedChange={(checked) => 
                          setConfig(prev => ({
                            ...prev,
                            scoring: { ...prev.scoring, [key]: checked }
                          }))
                        }
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Special Modes */}
            <Card className="bg-black/20 backdrop-blur-md border-white/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5 text-purple-400" />
                  Special Modes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {Object.entries(config.specialModes).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                      <Label className="text-sm">{CATEGORY_DISPLAY_NAMES[key as keyof typeof CATEGORY_DISPLAY_NAMES] || key}</Label>
                      <Switch
                        checked={value}
                        onCheckedChange={(checked) => 
                          setConfig(prev => ({
                            ...prev,
                            specialModes: { ...prev.specialModes, [key]: checked }
                          }))
                        }
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Filters Tab */}
          <TabsContent value="filters" className="space-y-6">
            <Card className="bg-black/20 backdrop-blur-md border-white/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shuffle className="w-5 h-5 text-green-400" />
                  Music Filters
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label>Minimum Popularity: {config.musicFilters.minPopularity}</Label>
                  <Slider
                    value={[config.musicFilters.minPopularity]}
                    onValueChange={([value]) => 
                      setConfig(prev => ({
                        ...prev,
                        musicFilters: { ...prev.musicFilters, minPopularity: value }
                      }))
                    }
                    min={0}
                    max={100}
                    step={10}
                    className="mt-2"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label>Exclude Common Songs</Label>
                  <Switch
                    checked={config.musicFilters.excludeCommon}
                    onCheckedChange={(checked) => 
                      setConfig(prev => ({
                        ...prev,
                        musicFilters: { ...prev.musicFilters, excludeCommon: checked }
                      }))
                    }
                  />
                </div>
                
                <Separator />
                
                <div>
                  <Label>Preferred Genres</Label>
                  <div className="grid grid-cols-3 md:grid-cols-5 gap-2 mt-2">
                    {AVAILABLE_GENRES.map((genre) => (
                      <Button
                        key={genre}
                        variant={config.musicFilters.genres.includes(genre) ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          const genres = config.musicFilters.genres.includes(genre)
                            ? config.musicFilters.genres.filter(g => g !== genre)
                            : [...config.musicFilters.genres, genre]
                          setConfig(prev => ({
                            ...prev,
                            musicFilters: { ...prev.musicFilters, genres }
                          }))
                        }}
                        className="text-xs"
                      >
                        {genre}
                      </Button>
                    ))}
                  </div>
                </div>
                
                <div>
                  <Label>Preferred Decades</Label>
                  <div className="grid grid-cols-4 md:grid-cols-8 gap-2 mt-2">
                    {AVAILABLE_DECADES.map((decade) => (
                      <Button
                        key={decade}
                        variant={config.musicFilters.decades.includes(decade) ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          const decades = config.musicFilters.decades.includes(decade)
                            ? config.musicFilters.decades.filter(d => d !== decade)
                            : [...config.musicFilters.decades, decade]
                          setConfig(prev => ({
                            ...prev,
                            musicFilters: { ...prev.musicFilters, decades }
                          }))
                        }}
                        className="text-xs"
                      >
                        {decade}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Presets Tab */}
          <TabsContent value="presets" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Object.entries(PRESET_CONFIGURATIONS).map(([name, preset]) => (
                <Card 
                  key={name}
                  className={`bg-black/20 backdrop-blur-md border-white/20 cursor-pointer transition-all hover:border-purple-400/50 ${
                    selectedPreset === name ? 'border-purple-400 bg-purple-500/10' : ''
                  }`}
                  onClick={() => applyPreset(name)}
                >
                  <CardHeader>
                    <CardTitle className="text-lg">{name}</CardTitle>
                    <p className="text-sm text-gray-400">{preset.description}</p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Rounds:</span>
                        <span>{preset.totalRounds}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Questions:</span>
                        <span>{preset.totalRounds * preset.questionsPerRound}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Categories:</span>
                        <span>{Object.values(preset.categories).filter(c => c.enabled).length}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={onBackToMenu}>
            Back to Menu
          </Button>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => {
                const enabledCategories = Object.values(config.categories).filter(c => c.enabled)
                if (enabledCategories.length === 0) {
                  toast.error('Please enable at least one quiz category', {
                    description: 'Select at least one category to start the game',
                    duration: 4000
                  })
                  return
                }
                if (getTotalWeight() === 0) {
                  toast.error('Please set weights for enabled categories', {
                    description: 'Categories need weights to determine question distribution',
                    duration: 4000
                  })
                  return
                }
                console.log('Game Configuration:', config)
              }}
            >
              <Settings className="w-4 h-4 mr-2" />
              Preview Config
            </Button>
            
            <Button
              onClick={() => {
                const enabledCategories = Object.values(config.categories).filter(c => c.enabled)
                if (enabledCategories.length === 0) {
                  toast.error('Please enable at least one quiz category', {
                    description: 'Select at least one category to start the game',
                    duration: 4000
                  })
                  return
                }
                if (getTotalWeight() === 0) {
                  toast.error('Please set weights for enabled categories', {
                    description: 'Categories need weights to determine question distribution',
                    duration: 4000
                  })
                  return
                }
                onStartGame(config)
              }}
              className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white"
            >
              <Crown className="w-4 h-4 mr-2" />
              Start ulTimote Game
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}