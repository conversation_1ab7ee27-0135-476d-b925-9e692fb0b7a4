'use client'

import React, { useState, useEffect, memo, useCallback, useMemo } from 'react'
import { Search, Plus, Heart, Music, Send } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { useJukeboxStore } from '@/stores/jukeboxStore'
import { useUserFavorites } from '@/hooks/useUserFavorites'
import { AlbumArt } from '@/components/album-art'
import { toast } from 'sonner'
import type { Song } from '@/lib/types'

interface LibraryBrowserProps {
  userRole?: string
  isVisible?: boolean
  onClose?: () => void
}

export const LibraryBrowser = memo(function LibraryBrowser({ userRole = 'user', isVisible = true, onClose }: LibraryBrowserProps) {
  const {
    library,
    isLoading,
    setLibrary,
    setIsLoading
  } = useJukeboxStore()
  
  const { userFavorites, toggleFavorite } = useUserFavorites()
  
  const [searchTerm, setSearchTerm] = useState('')
  const [operationLoading, setOperationLoading] = useState<Set<string>>(new Set())
  
  // Load library - memoized
  const loadLibrary = useCallback(async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/quiz/tracks', {
        credentials: 'include'
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setLibrary(data.tracks || [])
        }
      }
    } catch (error) {
      console.error('Library load error:', error)
      toast.error('Failed to load music library')
    } finally {
      setIsLoading(false)
    }
  }, [setLibrary, setIsLoading])
  
  useEffect(() => {
    loadLibrary()
  }, [loadLibrary])
  
  // Filter library - memoized for performance
  const filteredLibrary = useMemo(() => {
    if (!searchTerm) return library
    const search = searchTerm.toLowerCase()
    return library.filter(song => 
      song.title?.toLowerCase().includes(search) ||
      song.artist?.toLowerCase().includes(search) ||
      song.album?.toLowerCase().includes(search)
    )
  }, [library, searchTerm])
  
  // Add to queue - memoized
  const handleAddToQueue = useCallback(async (song: Song) => {
    const songId = String(song.id || song.filePath)
    setOperationLoading(prev => new Set([...prev, songId]))
    
    try {
      // Get auth token
      const { ClientTokenManager } = await import('@/lib/client-auth')
      const token = ClientTokenManager.getToken()
      
      const headers: HeadersInit = { 'Content-Type': 'application/json' }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch('/api/mpd/queue', {
        method: 'POST',
        headers,
        credentials: 'include',
        body: JSON.stringify({
          action: 'add',
          filePath: song.filePath
        })
      })
      
      if (response.ok) {
        toast.success(`Added "${song.title}" to queue`)
      } else {
        throw new Error('Failed to add to queue')
      }
    } catch (error) {
      console.error('Add to queue error:', error)
      toast.error('Failed to add to queue')
    } finally {
      setOperationLoading(prev => {
        const newSet = new Set(prev)
        newSet.delete(songId)
        return newSet
      })
    }
  }, [setOperationLoading])
  
  // Toggle favorite - memoized
  const handleToggleFavorite = useCallback(async (song: Song) => {
    if (!song.filePath) {
      toast.error('Cannot favorite this song')
      return
    }
    
    const songId = String(song.id || song.filePath)
    setOperationLoading(prev => new Set([...prev, `fav-${songId}`]))
    
    try {
      await toggleFavorite(song)
    } catch (error) {
      console.error('Toggle favorite error:', error)
      toast.error('Failed to update favorites')
    } finally {
      setOperationLoading(prev => {
        const newSet = new Set(prev)
        newSet.delete(`fav-${songId}`)
        return newSet
      })
    }
  }, [toggleFavorite, setOperationLoading])
  
  // Suggest track - memoized
  const handleSuggestTrack = useCallback(async (song: Song) => {
    const songId = String(song.id || song.filePath)
    setOperationLoading(prev => new Set([...prev, `suggest-${songId}`]))
    
    try {
      const response = await fetch('/api/jukebox/suggestions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          title: song.title,
          artist: song.artist,
          album: song.album,
          duration: song.duration,
          genre: (song as any).genre,
          year: (song as any).year,
          filePath: song.filePath,
          albumArtUrl: song.albumArtUrl
        })
      })
      
      if (response.ok) {
        toast.success('Track suggested!')
      } else {
        throw new Error('Failed to suggest track')
      }
    } catch (error) {
      console.error('Suggest track error:', error)
      toast.error('Failed to suggest track')
    } finally {
      setOperationLoading(prev => {
        const newSet = new Set(prev)
        newSet.delete(`suggest-${songId}`)
        return newSet
      })
    }
  }, [setOperationLoading])
  
  if (!isVisible) return null
  
  return (
    <div className="flex flex-col h-full bg-background border-l">
      {/* Header */}
      <div className="p-4 border-b bg-muted/50">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Music className="h-5 w-5" />
            Music Library
          </h2>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              ×
            </Button>
          )}
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search songs, artists, albums..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>
      
      {/* Library Content */}
      <div className="flex-1 overflow-auto p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-muted-foreground">Loading library...</div>
          </div>
        ) : filteredLibrary.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-center">
            <Music className="h-12 w-12 text-muted-foreground mb-2" />
            <div className="text-muted-foreground">No songs found</div>
          </div>
        ) : (
          <div className="space-y-2">
            {filteredLibrary.map((song, index) => {
              const songId = String(song.id || song.filePath)
              const isFavorite = song.filePath && userFavorites.has(song.filePath)
              const isQueueLoading = operationLoading.has(songId)
              const isFavLoading = operationLoading.has(`fav-${songId}`)
              const isSuggestLoading = operationLoading.has(`suggest-${songId}`)
              
              return (
                <Card key={`${song.id}-${index}`} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-3 sm:p-4">
                    {/* Mobile-first: Vertical layout */}
                    <div className="sm:hidden space-y-3">
                      {/* Top row: album art and actions */}
                      <div className="flex items-center justify-between">
                        <AlbumArt 
                          trackId={song.id?.toString()}
                          fallbackUrl={song.albumArtUrl}
                          artist={song.artist}
                          album={song.album}
                          size="sm"
                          className="rounded"
                        />
                        
                        {/* Larger mobile action buttons */}
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            className="h-10 w-10 p-0"
                            onClick={() => handleAddToQueue(song)}
                            disabled={isQueueLoading}
                            title="Add to queue"
                          >
                            {isQueueLoading ? '...' : <Plus className="h-4 w-4" />}
                          </Button>
                          
                          <Button
                            size="sm"
                            className="h-10 w-10 p-0"
                            variant="outline"
                            onClick={() => handleSuggestTrack(song)}
                            disabled={isSuggestLoading}
                            title="Suggest track"
                          >
                            {isSuggestLoading ? '...' : <Send className="h-4 w-4" />}
                          </Button>
                          
                          <Button
                            size="sm"
                            className="h-10 w-10 p-0"
                            variant={isFavorite ? "default" : "outline"}
                            onClick={() => handleToggleFavorite(song)}
                            disabled={isFavLoading}
                            title={isFavorite ? "Remove from favorites" : "Add to favorites"}
                          >
                            {isFavLoading ? '...' : <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />}
                          </Button>
                        </div>
                      </div>
                      
                      {/* Song info - allow wrapping */}
                      <div className="space-y-1">
                        <div className="font-medium leading-tight">{song.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {song.artist}
                        </div>
                        {song.album && (
                          <div className="text-xs text-muted-foreground">
                            {song.album}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Desktop: Horizontal layout */}
                    <div className="hidden sm:flex items-center gap-3">
                      <div className="flex-shrink-0">
                        <AlbumArt 
                          trackId={song.id?.toString()}
                          fallbackUrl={song.albumArtUrl}
                          artist={song.artist}
                          album={song.album}
                          size="sm"
                          className="rounded"
                        />
                      </div>
                      
                      <div className="flex-1 min-w-0 mr-2">
                        <div className="font-medium truncate">{song.title}</div>
                        <div className="text-sm text-muted-foreground truncate">
                          {song.artist} • {song.album}
                        </div>
                      </div>
                      
                      <div className="flex gap-1 flex-shrink-0">
                        <Button
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => handleAddToQueue(song)}
                          disabled={isQueueLoading}
                          title="Add to queue"
                        >
                          {isQueueLoading ? '...' : <Plus className="h-4 w-4" />}
                        </Button>
                        
                        <Button
                          size="sm"
                          className="h-8 w-8 p-0"
                          variant="outline"
                          onClick={() => handleSuggestTrack(song)}
                          disabled={isSuggestLoading}
                          title="Suggest track"
                        >
                          {isSuggestLoading ? '...' : <Lightbulb className="h-4 w-4" />}
                        </Button>
                        
                        <Button
                          size="sm"
                          className="h-8 w-8 p-0"
                          variant={isFavorite ? "default" : "outline"}
                          onClick={() => handleToggleFavorite(song)}
                          disabled={isFavLoading}
                          title={isFavorite ? "Remove from favorites" : "Add to favorites"}
                        >
                          {isFavLoading ? '...' : <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
})

LibraryBrowser.displayName = 'LibraryBrowser'
