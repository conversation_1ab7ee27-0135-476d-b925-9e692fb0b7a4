"use client"

import React, { memo, useMemo } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Slider } from "@/components/ui/slider"
import {
  Play,
  Pause,
  SkipForward,
  SkipBack,
  Volume2,
  VolumeX,
  Shuffle,
  Repeat,
} from "lucide-react"

import { AlbumArt } from '@/components/album-art'
import { useJukeboxStore } from '@/stores/jukeboxStore'
import { useOptimizedMPDControls } from '@/hooks/useOptimizedMPDControls'

interface OptimizedPlayerControlsProps {
  onNext?: () => void
  onPrevious?: () => void
  userRole?: string
  className?: string
}

const OptimizedPlayerControls: React.FC<OptimizedPlayerControlsProps> = memo(({
  onNext,
  onPrevious,
  userRole,
  className = ""
}) => {
  // Get state from Zustand store
  const {
    currentTrack,
    progress,
    duration,
  } = useJukeboxStore()
  
  // Use optimized controls hook
  const {
    handlePlayPause,
    handleVolumeChange,
    handleSkip,
    isPlaying,
    volume
  } = useOptimizedMPDControls()
  
  // Memoized calculations
  const canControl = useMemo(() => 
    userRole === 'admin' || userRole === 'moderator' || userRole === 'dj' || userRole === 'superuser', 
    [userRole]
  )

  const progressPercentage = useMemo(() => 
    duration > 0 ? (progress / duration) * 100 : 0, 
    [progress, duration]
  )

  const formattedProgress = useMemo(() => 
    formatDuration(progress), 
    [progress]
  )

  const formattedDurationText = useMemo(() => 
    formatDuration(duration), 
    [duration]
  )
  
  const isMuted = volume === 0

  // Format duration helper
  function formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }
  
  // Handlers that use the optimized functions
  const handleNext = () => {
    if (onNext) {
      onNext()
    } else {
      handleSkip('forward')
    }
  }
  
  const handlePrevious = () => {
    if (onPrevious) {
      onPrevious()
    } else {
      handleSkip('back')
    }
  }
  
  const handleMuteToggle = () => {
    handleVolumeChange([isMuted ? 70 : 0])
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <span>Now Playing</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current track info */}
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <AlbumArt 
              key={`${currentTrack?.id || currentTrack?.fingerprint || 'no-track'}-${currentTrack?.title || 'no-title'}`}
              trackId={currentTrack?.fingerprint || currentTrack?.fileFingerprint || currentTrack?.id}
              artist={currentTrack?.artist || "Unknown"}
              album={currentTrack?.album || "Unknown"}
              title={currentTrack?.title}
              size="cover"
              className="rounded-md"
              fallbackUrl={currentTrack?.albumArtUrl || "/placeholder.svg"}
              priority={true}
            />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg truncate">
              {currentTrack?.title || "No track selected"}
            </h3>
            <p className="text-sm text-muted-foreground truncate">
              {currentTrack?.artist || "Unknown artist"}
              {typeof currentTrack?.year === 'number' && currentTrack.year > 0 ? `  -  ${currentTrack.year}` : ""}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              {currentTrack?.album || "Unknown album"}
            </p>
          </div>
        </div>

        {/* Progress bar - Read only for now */}
        <div className="space-y-2">
          <div className="w-full bg-secondary rounded-full h-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{formattedProgress}</span>
            <span>{formattedDurationText}</span>
          </div>
        </div>

        {/* Player controls */}
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrevious}
            disabled={!currentTrack || !canControl}
            className="h-10 w-10 p-0 transition-all duration-150"
          >
            <SkipBack className="h-4 w-4" />
          </Button>

          <Button
            onClick={() => {
              console.log('[PlayerControls] Play/pause button clicked');
              console.log('[PlayerControls] Current track:', currentTrack);
              console.log('[PlayerControls] User role:', userRole);
              console.log('[PlayerControls] Is playing:', isPlaying);
              handlePlayPause();
            }}
            disabled={!currentTrack}
            className="h-12 w-12 p-0 transition-all duration-150 hover:scale-105"
          >
            {isPlaying ? (
              <Pause className="h-6 w-6" />
            ) : (
              <Play className="h-6 w-6 ml-0.5" />
            )}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleNext}
            disabled={!currentTrack || !canControl}
            className="h-10 w-10 p-0 transition-all duration-150"
          >
            <SkipForward className="h-4 w-4" />
          </Button>
        </div>

        {/* Volume control */}
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleMuteToggle}
            className="h-8 w-8 p-0 flex-shrink-0"
          >
            {isMuted ? (
              <VolumeX className="h-4 w-4" />
            ) : (
              <Volume2 className="h-4 w-4" />
            )}
          </Button>
          <div className="flex-1">
            <Slider
              value={[volume]}
              onValueChange={handleVolumeChange}
              max={100}
              step={1}
              className="w-full cursor-pointer"
            />
          </div>
          <span className="text-xs text-muted-foreground w-8 text-right">
            {Math.round(volume)}%
          </span>
        </div>

        {/* Additional controls */}
        <div className="flex items-center justify-center space-x-2 pt-2">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Shuffle className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Repeat className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
})

OptimizedPlayerControls.displayName = 'OptimizedPlayerControls'

export default OptimizedPlayerControls