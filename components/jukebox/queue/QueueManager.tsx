"use client"

import React, { useState, useEffect, memo, useCallback, useMemo } from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import {
  Trash2,
  GripVertical,
  ArrowUp,
  ArrowDown,
  Music,
  Clock,
  User,
  Play,
} from "lucide-react"

import type { QueuedSong, UserRole } from "@/lib/types"
import { AlbumArt } from '@/components/album-art'
import { toast } from 'sonner'
import { useJukeboxStore } from '@/stores/jukeboxStore'

interface QueueManagerProps {
  userRole: UserRole
  currentUser?: any
  onRemoveFromQueue: (songId: string) => void
  onPlayTrack: (trackId: string) => void
  onToggleFavorite: (songId: string, isFavorite: boolean) => void
  onMoveInQueue: (songId: string, direction: 'up' | 'down') => void
  className?: string
}

interface QueueItemProps {
  song: QueuedSong
  index: number
  isCurrentTrack: boolean
  canManage: boolean
  onRemove: () => void
  onMoveUp: () => void
  onMoveDown: () => void
  onPlay?: () => void
  isFirst: boolean
  isLast: boolean
}

function QueueItem({
  song,
  index,
  isCurrentTrack,
  canManage,
  onRemove,
  onMoveUp,
  onMoveDown,
  onPlay,
  isFirst,
  isLast
}: QueueItemProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleAction = async (action: () => Promise<void> | void, actionName: string) => {
    setIsLoading(true)
    try {
      await Promise.resolve(action())
      toast.success(`${actionName} successful`)
    } catch (error) {
      toast.error(`Failed to ${actionName.toLowerCase()}`)
      console.error(`Queue ${actionName} error:`, error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatDuration = (seconds: number | undefined): string => {
    if (!seconds) return "0:00"
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <div 
      className={`
        rounded-lg border transition-colors
        ${isCurrentTrack 
          ? 'bg-primary/5 border-primary/20' 
          : 'bg-card hover:bg-accent/50'
        }
      `}
    >
      {/* Mobile-first: Vertical layout */}
      <div className="sm:hidden p-3 space-y-3">
        {/* Top row: position, album art, and primary actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className={`
              text-sm font-medium
              ${isCurrentTrack ? 'text-primary' : 'text-muted-foreground'}
            `}>
              {index + 1}
            </span>
            <AlbumArt 
              artist={song.artist}
              album={song.album || "Unknown"}
              title={song.title}
              size="thumbnail"
              className="rounded"
              fallbackUrl={song.albumArtUrl}
            />
          </div>
          
          {/* Primary actions - larger touch targets */}
          <div className="flex items-center space-x-2">
            {onPlay && !isCurrentTrack && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onPlay}
                className="h-10 w-10 p-0"
                title="Play now"
              >
                <Play className="h-4 w-4" />
              </Button>
            )}
            {canManage && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleAction(onRemove, "Remove")}
                disabled={isLoading}
                className="h-10 w-10 p-0 text-destructive hover:text-destructive"
                title="Remove from queue"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        
        {/* Song info - no truncation, allow wrapping */}
        <div className="space-y-1">
          <h4 className={`
            font-medium leading-tight
            ${isCurrentTrack ? 'text-primary' : 'text-foreground'}
          `}>
            {song.title}
          </h4>
          <p className="text-sm text-muted-foreground">
            {song.artist}
          </p>
          {song.album && (
            <p className="text-xs text-muted-foreground">
              {song.album}
            </p>
          )}
        </div>
        
        {/* Bottom row: metadata and management actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 text-xs text-muted-foreground">
            {song.duration && (
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {formatDuration(song.duration)}
              </div>
            )}
            {song.addedBy && (
              <div className="flex items-center">
                <User className="h-3 w-3 mr-1" />
                {song.addedBy}
              </div>
            )}
          </div>
          
          {/* Queue management actions */}
          {canManage && (
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleAction(onMoveUp, "Move up")}
                disabled={isFirst || isLoading}
                className="h-8 w-8 p-0"
                title="Move up"
              >
                <ArrowUp className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleAction(onMoveDown, "Move down")}
                disabled={isLast || isLoading}
                className="h-8 w-8 p-0"
                title="Move down"
              >
                <ArrowDown className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 cursor-move"
                title="Drag to reorder"
              >
                <GripVertical className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
      </div>
      
      {/* Desktop: Horizontal layout */}
      <div className="hidden sm:flex items-center space-x-3 p-3">
        {/* Queue position */}
        <div className="flex-shrink-0 w-8 text-center">
          <span className={`
            text-sm font-medium
            ${isCurrentTrack ? 'text-primary' : 'text-muted-foreground'}
          `}>
            {index + 1}
          </span>
        </div>

        {/* Album art */}
        <div className="flex-shrink-0">
          <AlbumArt 
            artist={song.artist}
            album={song.album || "Unknown"}
            title={song.title}
            size="thumbnail"
            className="rounded"
            fallbackUrl={song.albumArtUrl}
          />
        </div>

        {/* Song info - increased min width for better readability */}
        <div className="flex-1 min-w-0 mr-4">
          <div className="flex items-start justify-between">
            <div className="min-w-0 flex-1">
              <h4 className={`
                font-medium line-clamp-2
                ${isCurrentTrack ? 'text-primary' : 'text-foreground'}
              `}
                title={song.title}
              >
                {song.title}
              </h4>
              <p className="text-sm text-muted-foreground line-clamp-1"
                title={song.artist}
              >
                {song.artist}
              </p>
              {song.album && (
                <p className="text-xs text-muted-foreground line-clamp-1"
                  title={song.album}
                >
                  {song.album}
                </p>
              )}
            </div>
            
            <div className="flex items-center space-x-2 ml-2">
              {song.duration && (
                <div className="flex items-center text-xs text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDuration(song.duration)}
                </div>
              )}
              {song.addedBy && (
                <div className="flex items-center text-xs text-muted-foreground">
                  <User className="h-3 w-3 mr-1" />
                  {song.addedBy}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1">
          {onPlay && !isCurrentTrack && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onPlay}
              className="h-8 w-8 p-0"
              title="Play now"
            >
              <Play className="h-3 w-3" />
            </Button>
          )}

          {canManage && (
            <>
              {/* Move up */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleAction(onMoveUp, "Move up")}
                disabled={isFirst || isLoading}
                className="h-8 w-8 p-0"
                title="Move up"
              >
                <ArrowUp className="h-3 w-3" />
              </Button>

              {/* Move down */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleAction(onMoveDown, "Move down")}
                disabled={isLast || isLoading}
                className="h-8 w-8 p-0"
                title="Move down"
              >
                <ArrowDown className="h-3 w-3" />
              </Button>

              {/* Remove */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleAction(onRemove, "Remove")}
                disabled={isLoading}
                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                title="Remove from queue"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </>
          )}

          {canManage && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 cursor-move"
              title="Drag to reorder"
            >
              <GripVertical className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

const QueueManager: React.FC<QueueManagerProps> = memo(function QueueManager({
  userRole,
  currentUser,
  onRemoveFromQueue,
  onPlayTrack,
  onToggleFavorite,
  onMoveInQueue,
  className = ""
}) {
  // Get state from Zustand store
  const { queue, currentTrack, isLoading } = useJukeboxStore()
  // Memoized calculations
  const canManageQueue = useMemo(() => 
    userRole === 'superuser' || userRole === 'dj', 
    [userRole]
  )

  const queueDuration = useMemo(() => {
    if (!queue?.length) return 0
    return queue.reduce((total, song) => total + (song.duration || 0), 0)
  }, [queue])

  const formattedQueueDuration = useMemo(() => {
    const minutes = Math.floor(queueDuration / 60)
    const hours = Math.floor(minutes / 60)
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }, [queueDuration])

  const queueStats = useMemo(() => ({
    totalTracks: queue?.length || 0,
    duration: formattedQueueDuration,
    contributors: [...new Set(queue?.map(song => song.addedBy?.name || 'Unknown') || [])].length
  }), [queue, formattedQueueDuration])

  // Memoized callbacks
  const handleRemoveFromQueue = useCallback(async (songId: string, songTitle?: string) => {
    try {
      await onRemoveFromQueue(songId)
      toast({
        title: "Removed from queue",
        description: songTitle ? `"${songTitle}" removed from queue` : "Song removed from queue",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove song from queue",
        variant: "destructive",
      })
    }
  }, [onRemoveFromQueue])

  const handlePlayTrack = useCallback(async (trackId: string, trackTitle?: string) => {
    try {
      await onPlayTrack(trackId)
      toast({
        title: "Now playing",
        description: trackTitle ? `Playing "${trackTitle}"` : "Track started",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to play track",
        variant: "destructive",
      })
    }
  }, [onPlayTrack])

  const handleToggleFavorite = useCallback(async (songId: string, currentlyFavorite: boolean, songTitle?: string) => {
    try {
      await onToggleFavorite(songId, !currentlyFavorite)
      toast({
        title: currentlyFavorite ? "Removed from favorites" : "Added to favorites",
        description: songTitle || "Song updated",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update favorites",
        variant: "destructive",
      })
    }
  }, [onToggleFavorite])

  const handleMoveInQueue = useCallback(async (songId: string, direction: 'up' | 'down', songTitle?: string) => {
    try {
      await onMoveInQueue(songId, direction)
      toast({
        title: "Queue updated",
        description: `"${songTitle || 'Song'}" moved ${direction}`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to move song in queue",
        variant: "destructive",
      })
    }
  }, [onMoveInQueue])

  return (
    <Card className={`w-full ${className}`} data-tour="queue-section">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Music className="h-5 w-5" />
            Queue ({queue.length})
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              {formattedQueueDuration}
            </Badge>
            {canManageQueue && (
              <Badge variant="outline" className="text-xs">
                Manager
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {queue.length === 0 ? (
          <div className="text-center py-8">
            <Music className="h-12 w-12 mx-auto text-muted-foreground/50 mb-3" />
            <p className="text-muted-foreground">
              No songs in queue
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              Add some songs to get started!
            </p>
          </div>
        ) : (
          <ScrollArea className="h-[400px] sm:h-[450px] w-full">
            <div className="space-y-2">
              {queue.map((song, index) => (
                <QueueItem
                  key={`${song.id || song.title}-${index}`}
                  song={song}
                  index={index}
                  isCurrentTrack={currentTrack?.id === song.id || 
                    (currentTrack?.title === song.title && currentTrack?.artist === song.artist)}
                  canManage={canManageQueue}
                  onRemove={() => handleRemoveFromQueue(song.id || '', song.title)}
                  onMoveUp={() => handleMoveInQueue(song.id || '', 'up', song.title)}
                  onMoveDown={() => handleMoveInQueue(song.id || '', 'down', song.title)}
                  onPlay={() => handlePlayTrack(song.id || '', song.title)}
                  isFirst={index === 0}
                  isLast={index === queue.length - 1}
                />
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
})

export default QueueManager 