"use client"

import { useState, useEffect } from 'react'
import { useAutoQueueIntegration } from '@/hooks/useAutoQueueIntegration'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Slider } from "@/components/ui/slider"
import { 
  Brain, 
  Settings, 
  Users, 
  Heart, 
  TrendingUp,
  Zap,
  Clock,
  RefreshCw,
  Save,
  Play,
  Pause,
  BarChart3,
  Lightbulb,
  Shield,
  Target,
  Music,
  Shuffle,
  Filter
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "sonner"
import type { AutoQueueConfig, AutoQueueConfigSummary } from "@/lib/services/auto-queue-config-service"

interface IntelligentAutoQueueAdminProps {
  currentQueueLength: number
  onSongsAdded: (songs: any[]) => void
  isAdmin: boolean
  userId: string
}

interface QuickStats {
  connectedUsers: number
  totalFavorites: number
  uniqueTracks: number
  topGenres: Array<{ genre: string; count: number }>
  topArtists: Array<{ artist: string; count: number }>
}

interface FavoritesAnalysis {
  connectedUsers: string[]
  totalConnectedUsers: number
  topSharedFavorites: any[]
  personalizedRecommendations: any[]
  popularGenres: any[]
  popularArtists: any[]
  diversityScore: number
}

export function IntelligentAutoQueueAdmin({ 
  currentQueueLength, 
  onSongsAdded, 
  isAdmin, 
  userId 
}: IntelligentAutoQueueAdminProps) {
  // Auto-queue integration hook
  const autoQueue = useAutoQueueIntegration({
    userId,
    sessionType: 'jukebox',
    autoStart: true,
    enableStatusPolling: true,
    statusPollingInterval: 30
  })

  // State management
  const [isLoading, setIsLoading] = useState(false)
  const [activeConfig, setActiveConfig] = useState<AutoQueueConfig | null>(null)
  const [availableConfigs, setAvailableConfigs] = useState<AutoQueueConfigSummary[]>([])
  const [quickStats, setQuickStats] = useState<QuickStats | null>(null)
  const [analysis, setAnalysis] = useState<FavoritesAnalysis | null>(null)
  const [lastAnalysisTime, setLastAnalysisTime] = useState<Date | null>(null)
  const [editingConfig, setEditingConfig] = useState<AutoQueueConfig | null>(null)

  // Load initial data
  useEffect(() => {
    loadConfigurations()
    loadQuickStats()
    loadActiveConfiguration()
  }, [userId])

  const loadConfigurations = async () => {
    try {
      const response = await fetch('/api/jukebox/auto-queue-config')
      const data = await response.json()
      
      if (data.success) {
        setAvailableConfigs(data.data.configurations)
      }
    } catch (error) {
      console.error('Failed to load configurations:', error)
    }
  }

  const loadActiveConfiguration = async () => {
    try {
      const response = await fetch(`/api/jukebox/auto-queue-config?activeOnly=true&userId=${userId}`)
      const data = await response.json()
      
      if (data.success) {
        setActiveConfig(data.data)
      }
    } catch (error) {
      console.error('Failed to load active configuration:', error)
    }
  }

  const loadQuickStats = async () => {
    try {
      const response = await fetch('/api/jukebox/favorites-analysis?quickStats=true&sessionType=jukebox')
      const data = await response.json()
      
      if (data.success) {
        setQuickStats(data.data)
      }
    } catch (error) {
      console.error('Failed to load quick stats:', error)
    }
  }

  const loadFullAnalysis = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/jukebox/favorites-analysis?sessionType=jukebox')
      const data = await response.json()
      
      if (data.success) {
        setAnalysis(data.data)
        setLastAnalysisTime(new Date())
      }
    } catch (error) {
      console.error('Failed to load analysis:', error)
      toast.error('Failed to load favorites analysis')
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfigurationChange = async (configId: string) => {
    try {
      const response = await fetch('/api/jukebox/auto-queue-config', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ configId, userId })
      })
      
      const data = await response.json()
      
      if (data.success) {
        await loadActiveConfiguration()
        toast.success(`Switched to ${data.data.activeConfig.name}`)
      } else {
        toast.error('Failed to change configuration')
      }
    } catch (error) {
      console.error('Failed to change configuration:', error)
      toast.error('Failed to change configuration')
    }
  }

  const generateIntelligentRecommendations = async () => {
    setIsLoading(true)
    try {
      const excludePaths = [] // Could get from current queue
      const currentQueue = [] // Could get current queue data
      const recentlyPlayed = [] // Could get recently played tracks
      
      const params = new URLSearchParams({
        sessionType: 'jukebox',
        limit: (activeConfig?.recommendationSettings.maxSongsToAdd || 5).toString(),
        excludePaths: excludePaths.join(','),
        currentQueue: encodeURIComponent(JSON.stringify(currentQueue)),
        recentlyPlayed: encodeURIComponent(JSON.stringify(recentlyPlayed))
      })
      
      const response = await fetch(`/api/jukebox/intelligent-recommendations?${params}`)
      const data = await response.json()
      
      if (data.success) {
        const recommendations = data.data.recommendations
        
        if (recommendations.length > 0) {
          // Get auth token
          const { ClientTokenManager } = await import('@/lib/client-auth')
          const token = ClientTokenManager.getToken()
          
          const headers: HeadersInit = { 'Content-Type': 'application/json' }
          if (token) {
            headers['Authorization'] = `Bearer ${token}`
          }
          
          // Add recommendations to queue
          const addPromises = recommendations.map((rec: any) => 
            fetch('/api/mpd/queue/add', {
              method: 'POST',
              headers,
              body: JSON.stringify({ 
                filePath: rec.song.filePath,
                addedBy: 'Intelligent Auto-Queue'
              })
            })
          )
          
          await Promise.all(addPromises)
          onSongsAdded(recommendations.map((rec: any) => rec.song))
          
          toast.success(`Added ${recommendations.length} intelligent recommendations`)
          
          // Show recommendation details
          const algorithmCounts = data.data.metadata.algorithmBreakdown
          const algorithmInfo = Object.entries(algorithmCounts)
            .map(([alg, count]) => `${alg}: ${count}`)
            .join(', ')
          
          toast.info(`Algorithms used: ${algorithmInfo}`, { duration: 5000 })
        } else {
          toast.warning('No recommendations available at this time')
        }
      } else {
        toast.error('Failed to generate recommendations')
      }
    } catch (error) {
      console.error('Failed to generate recommendations:', error)
      toast.error('Failed to generate recommendations')
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleAutoQueue = async () => {
    await autoQueue.toggle()
  }

  const algorithmColors = {
    'collaborative-filtering': 'bg-blue-500',
    'content-based': 'bg-green-500',
    'popularity-based': 'bg-purple-500',
    'context-aware': 'bg-orange-500',
    'hybrid': 'bg-pink-500',
    'favorites-only': 'bg-red-500'
  }

  return (
    <div className="space-y-6">
      {/* Header Card */}
      <Card className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 backdrop-blur-md border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 text-xl">
                <Brain className="w-6 h-6 text-purple-400" />
                Intelligent Auto-Queue System
              </CardTitle>
              <p className="text-sm text-gray-400 mt-1">
                AI-powered music recommendations based on connected users&apos; preferences
              </p>
            </div>
            <div className="flex items-center gap-3">
              {quickStats && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Users className="w-3 w-3" />
                  {quickStats.connectedUsers} users online
                </Badge>
              )}
              <Switch
                checked={autoQueue.isRunning}
                onCheckedChange={handleToggleAutoQueue}
                disabled={!isAdmin || autoQueue.isLoading}
              />
            </div>
          </div>
        </CardHeader>
      </Card>

      <Tabs defaultValue="control" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="control" className="flex items-center gap-2">
            <Play className="w-4 h-4" />
            Control
          </TabsTrigger>
          <TabsTrigger value="analysis" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Analysis
          </TabsTrigger>
          <TabsTrigger value="config" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Configuration
          </TabsTrigger>
          <TabsTrigger value="monitoring" className="flex items-center gap-2">
            <Shield className="w-4 h-4" />
            Monitoring
          </TabsTrigger>
        </TabsList>

        {/* Control Tab */}
        <TabsContent value="control" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Active Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Active Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {activeConfig && (
                  <>
                    <div className="space-y-2">
                      <Label>Current Configuration</Label>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{activeConfig.name}</Badge>
                        <Badge 
                          className={`${algorithmColors[activeConfig.recommendationSettings.algorithm as keyof typeof algorithmColors] || 'bg-gray-500'} text-white`}
                        >
                          {activeConfig.recommendationSettings.algorithm}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-400">{activeConfig.description}</p>
                    </div>
                    
                    <Select
                      value={activeConfig.id}
                      onValueChange={handleConfigurationChange}
                      disabled={!isAdmin}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {availableConfigs.map(config => (
                          <SelectItem key={config.id} value={config.id}>
                            <div className="flex items-center gap-2">
                              <span>{config.name}</span>
                              {config.isDefault && <Badge variant="outline" className="text-xs">Default</Badge>}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  onClick={() => autoQueue.trigger('manual-button')}
                  disabled={autoQueue.isLoading || !isAdmin}
                  className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                >
                  {autoQueue.isLoading ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Brain className="w-4 h-4 mr-2" />
                  )}
                  Generate Smart Recommendations
                </Button>
                
                <Button
                  onClick={loadQuickStats}
                  variant="outline"
                  className="w-full"
                  disabled={isLoading}
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh Stats
                </Button>
                
                <Button
                  onClick={loadFullAnalysis}
                  variant="outline"
                  className="w-full"
                  disabled={isLoading}
                >
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Run Full Analysis
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Quick Stats */}
          {quickStats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Current Session Stats
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-400">{quickStats.connectedUsers}</div>
                    <div className="text-sm text-gray-400">Connected Users</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">{autoQueue.totalSongsAdded}</div>
                    <div className="text-sm text-gray-400">Auto-Added Songs</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400">{quickStats.uniqueTracks}</div>
                    <div className="text-sm text-gray-400">Unique Tracks</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-400">{quickStats.topGenres.length}</div>
                    <div className="text-sm text-gray-400">Genres</div>
                  </div>
                </div>
                
                {quickStats.topGenres.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-white/10">
                    <h4 className="text-sm font-medium mb-2">Popular Genres</h4>
                    <div className="flex flex-wrap gap-2">
                      {quickStats.topGenres.slice(0, 5).map(genre => (
                        <Badge key={genre.genre} variant="outline" className="text-xs">
                          {genre.genre} ({genre.count})
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Auto-Queue Status */}
                {autoQueue.status && (
                  <div className="mt-4 pt-4 border-t border-white/10">
                    <div className="flex items-center justify-between text-sm">
                      <span>Auto-Queue Status:</span>
                      <Badge variant={autoQueue.isRunning ? "default" : "secondary"}>
                        {autoQueue.isRunning ? "Running" : "Stopped"}
                      </Badge>
                    </div>
                    
                    {autoQueue.activeConfiguration && (
                      <div className="flex items-center justify-between text-sm mt-2">
                        <span>Configuration:</span>
                        <span className="text-gray-400">{autoQueue.activeConfiguration}</span>
                      </div>
                    )}
                    
                    {autoQueue.lastActivity && (
                      <div className="flex items-center justify-between text-sm mt-2">
                        <span>Last Activity:</span>
                        <span className="text-gray-400">{autoQueue.lastActivity.toLocaleTimeString()}</span>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Analysis Tab */}
        <TabsContent value="analysis" className="space-y-4">
          {analysis ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* Shared Favorites */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Heart className="w-5 h-5 text-red-400" />
                    Shared Favorites
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {analysis.topSharedFavorites.length > 0 ? (
                    <div className="space-y-2">
                      {analysis.topSharedFavorites.slice(0, 5).map((fav, index) => (
                        <div key={index} className="flex justify-between items-center p-2 rounded bg-white/5">
                          <div>
                            <div className="font-medium">{fav.track.title}</div>
                            <div className="text-sm text-gray-400">{fav.track.artist}</div>
                          </div>
                          <Badge variant="secondary">{fav.score}/100</Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-400 text-center py-4">No shared favorites found</p>
                  )}
                </CardContent>
              </Card>

              {/* Popular Genres */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Music className="w-5 h-5 text-green-400" />
                    Genre Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analysis.popularGenres.slice(0, 5).map((genre, index) => (
                      <div key={index} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>{genre.genre}</span>
                          <span>{Math.round(genre.percentage)}%</span>
                        </div>
                        <Progress value={genre.percentage} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Diversity Score */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shuffle className="w-5 h-5 text-purple-400" />
                    Music Diversity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-purple-400 mb-2">
                      {Math.round(analysis.diversityScore * 100)}%
                    </div>
                    <p className="text-sm text-gray-400">
                      Variety across genres, artists, and eras
                    </p>
                    <Progress value={analysis.diversityScore * 100} className="mt-4" />
                  </div>
                </CardContent>
              </Card>

              {/* Popular Artists */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5 text-blue-400" />
                    Popular Artists
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {analysis.popularArtists.slice(0, 5).map((artist, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="font-medium">{artist.artist}</span>
                        <Badge variant="outline">{artist.count} tracks</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <BarChart3 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-400 mb-4">No analysis data available</p>
                <Button onClick={loadFullAnalysis} disabled={isLoading}>
                  {isLoading ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <BarChart3 className="w-4 h-4 mr-2" />
                  )}
                  Run Analysis
                </Button>
              </CardContent>
            </Card>
          )}
          
          {lastAnalysisTime && (
            <div className="text-sm text-gray-400 text-center">
              Last analysis: {lastAnalysisTime.toLocaleString()}
            </div>
          )}
        </TabsContent>

        {/* Configuration Tab */}
        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Configuration Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {availableConfigs.map(config => (
                    <Card key={config.id} className={`cursor-pointer transition-colors ${
                      activeConfig?.id === config.id ? 'ring-2 ring-blue-500' : ''
                    }`}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{config.name}</h4>
                          {config.isDefault && (
                            <Badge variant="outline" className="text-xs">Default</Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-400 mb-3">{config.description}</p>
                        <div className="flex items-center justify-between">
                          <Badge 
                            className={`${algorithmColors[config.algorithm as keyof typeof algorithmColors] || 'bg-gray-500'} text-white text-xs`}
                          >
                            {config.algorithm}
                          </Badge>
                          <span className="text-xs text-gray-400">
                            Max: {config.maxSongsToAdd}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                
                {!isAdmin && (
                  <p className="text-sm text-gray-400 text-center">
                    Admin privileges required to modify configurations
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Monitoring Tab */}
        <TabsContent value="monitoring" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                System Monitoring
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Clock className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-400">
                  Advanced monitoring features coming soon
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}