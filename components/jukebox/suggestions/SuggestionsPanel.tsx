"use client"

import React, { useState, useEffect, memo, useCallback, useMemo } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  <PERSON><PERSON>les,
  TrendingUp,
  Heart,
  Clock,
  Music,
  Plus,
  RefreshCw,
  Users,
  Star,
  PlayCircle,
  Shuffle,
  Volume2,
} from "lucide-react"

import type { Song, QueuedSong, UserRole } from "@/lib/types"
import { AlbumArt } from '@/components/album-art'
import { toast } from 'sonner'
import { useJukeboxStore } from '@/stores/jukeboxStore'

interface SuggestionsPanelProps {
  userRole: UserRole
  currentUser?: any
  onAddToQueue: (song: Song) => void
  onPlayNow: (song: Song) => void
  className?: string
}

interface SuggestionItemProps {
  song: Song
  index: number
  canAddToQueue: boolean
  onAddToQueue: () => void
  onPlayNow?: () => void
  suggestionReason?: string
  confidence?: number
}

function SuggestionItem({
  song,
  index,
  canAddToQueue,
  onAddToQueue,
  onPlayNow,
  suggestionReason,
  confidence = 0
}: SuggestionItemProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleAction = async (action: () => Promise<void> | void, actionName: string) => {
    setIsLoading(true)
    try {
      await Promise.resolve(action())
      toast.success(`${actionName} successful`)
    } catch (error) {
      toast.error(`Failed to ${actionName.toLowerCase()}`)
      console.error(`Suggestion ${actionName} error:`, error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatDuration = (seconds: number | undefined): string => {
    if (!seconds) return "0:00"
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return "text-green-600"
    if (confidence >= 0.6) return "text-yellow-600"
    return "text-gray-600"
  }

  return (
    <div className="rounded-lg border bg-card hover:bg-accent/50 transition-colors">
      {/* Mobile-first: Vertical layout */}
      <div className="sm:hidden p-3 space-y-3">
        {/* Top row: rank, album art, and actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="text-sm font-medium text-muted-foreground">
              #{index + 1}
            </span>
            <AlbumArt 
              artist={song.artist}
              album={song.album || "Unknown"}
              title={song.title}
              size="thumbnail"
              className="rounded"
              fallbackUrl={song.albumArtUrl}
            />
          </div>
          
          {/* Large mobile action buttons */}
          <div className="flex items-center space-x-2">
            {onPlayNow && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleAction(onPlayNow, "Play now")}
                disabled={isLoading}
                className="h-10 w-10 p-0"
                title="Play now"
              >
                <PlayCircle className="h-4 w-4" />
              </Button>
            )}
            {canAddToQueue && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleAction(onAddToQueue, "Add to queue")}
                disabled={isLoading}
                className="h-10 w-10 p-0"
                title="Add to queue"
              >
                <Plus className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        
        {/* Song info - no truncation, let it wrap */}
        <div className="space-y-1">
          <h4 className="font-medium text-foreground leading-tight">
            {song.title}
          </h4>
          <p className="text-sm text-muted-foreground">
            {song.artist}
          </p>
          {song.album && (
            <p className="text-xs text-muted-foreground">
              {song.album}
            </p>
          )}
          {suggestionReason && (
            <p className="text-xs text-blue-600">
              {suggestionReason}
            </p>
          )}
        </div>
        
        {/* Bottom row: duration and confidence */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          {song.duration && (
            <div className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {formatDuration(song.duration)}
            </div>
          )}
          {confidence > 0 && (
            <div className={`flex items-center ${getConfidenceColor(confidence)}`}>
              <Star className="h-3 w-3 mr-1" />
              {Math.round(confidence * 100)}%
            </div>
          )}
        </div>
      </div>
      
      {/* Desktop: Horizontal layout */}
      <div className="hidden sm:flex items-center space-x-3 p-3">
        {/* Suggestion rank */}
        <div className="flex-shrink-0 w-8 text-center">
          <span className="text-sm font-medium text-muted-foreground">
            #{index + 1}
          </span>
        </div>

        {/* Album art */}
        <div className="flex-shrink-0">
          <AlbumArt 
            artist={song.artist}
            album={song.album || "Unknown"}
            title={song.title}
            size="thumbnail"
            className="rounded"
            fallbackUrl={song.albumArtUrl}
          />
        </div>

        {/* Song info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="min-w-0 flex-1">
              <h4 className="font-medium text-foreground truncate">
                {song.title}
              </h4>
              <p className="text-sm text-muted-foreground truncate">
                {song.artist}
              </p>
              {song.album && (
                <p className="text-xs text-muted-foreground truncate">
                  {song.album}
                </p>
              )}
              {suggestionReason && (
                <p className="text-xs text-blue-600 mt-1 truncate">
                  {suggestionReason}
                </p>
              )}
            </div>
            
            <div className="flex items-center space-x-2 ml-2">
              {song.duration && (
                <div className="flex items-center text-xs text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDuration(song.duration)}
                </div>
              )}
              {confidence > 0 && (
                <div className={`flex items-center text-xs ${getConfidenceColor(confidence)}`}>
                  <Star className="h-3 w-3 mr-1" />
                  {Math.round(confidence * 100)}%
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1">
          {onPlayNow && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAction(onPlayNow, "Play now")}
              disabled={isLoading}
              className="h-8 w-8 p-0"
              title="Play now"
            >
              <PlayCircle className="h-3 w-3" />
            </Button>
          )}

          {canAddToQueue && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAction(onAddToQueue, "Add to queue")}
              disabled={isLoading}
              className="h-8 w-8 p-0"
              title="Add to queue"
            >
              <Plus className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

const SuggestionsPanel: React.FC<SuggestionsPanelProps> = memo(function SuggestionsPanel({
  userRole,
  currentUser,
  onAddToQueue,
  onPlayNow,
  className = ""
}) {
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("intelligent")
  const [suggestions, setSuggestions] = useState<{
    intelligent: Song[]
    popular: Song[]
    favorites: Song[]
    random: Song[]
  }>({
    intelligent: [],
    popular: [],
    favorites: [],
    random: []
  })

  // Get state from Zustand store
  const { userFavorites, queue, currentTrack, library } = useJukeboxStore()

  // Permission checks
  const canAddToQueue = useMemo(() => 
    userRole === 'superuser' || userRole === 'dj' || userRole === 'user', 
    [userRole]
  )

  const canPlayNow = useMemo(() => 
    userRole === 'superuser' || userRole === 'dj', 
    [userRole]
  )

  // Fetch suggestions from API
  const fetchSuggestions = useCallback(async (type: string) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/jukebox/suggestions?type=${type}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch suggestions')
      }
      
      const data = await response.json()
      
      setSuggestions(prev => ({
        ...prev,
        [type]: data.suggestions || []
      }))
    } catch (error) {
      console.error('Error fetching suggestions:', error)
      toast.error('Failed to load suggestions')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Generate intelligent suggestions based on current context
  const generateIntelligentSuggestions = useCallback(async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/jukebox/intelligent-recommendations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentTrack: currentTrack,
          recentQueue: queue.slice(-5),
          userFavorites: userFavorites,
          contextHints: {
            timeOfDay: new Date().getHours(),
            sessionDuration: Date.now() - (currentUser?.sessionStartTime || Date.now()),
            queueGenres: [...new Set(queue.map(song => song.genre).filter(Boolean))]
          }
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to generate intelligent suggestions')
      }
      
      const data = await response.json()
      
      setSuggestions(prev => ({
        ...prev,
        intelligent: data.recommendations || []
      }))
    } catch (error) {
      console.error('Error generating intelligent suggestions:', error)
      toast.error('Failed to generate intelligent suggestions')
    } finally {
      setIsLoading(false)
    }
  }, [currentTrack, queue, userFavorites, currentUser])

  // Generate random suggestions from library
  const generateRandomSuggestions = useCallback(() => {
    if (!library.length) return

    const shuffled = [...library]
      .filter(song => !queue.some(q => q.id === song.id))
      .sort(() => Math.random() - 0.5)
      .slice(0, 10)

    setSuggestions(prev => ({
      ...prev,
      random: shuffled
    }))
  }, [library, queue])

  // Generate popular suggestions based on recent activity
  const generatePopularSuggestions = useCallback(async () => {
    try {
      const response = await fetch('/api/jukebox/favorites-analysis', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch popular suggestions')
      }
      
      const data = await response.json()
      
      setSuggestions(prev => ({
        ...prev,
        popular: data.popular || []
      }))
    } catch (error) {
      console.error('Error fetching popular suggestions:', error)
      toast.error('Failed to load popular suggestions')
    }
  }, [])

  // Generate favorites-based suggestions
  const generateFavoritesSuggestions = useCallback(() => {
    if (!userFavorites.length) return

    const favoriteArtists = [...new Set(userFavorites.map(song => song.artist))]
    const favoriteGenres = [...new Set(userFavorites.map(song => song.genre).filter(Boolean))]

    const suggestions = library
      .filter(song => 
        !userFavorites.some(fav => fav.id === song.id) && // Not already in favorites
        !queue.some(q => q.id === song.id) && // Not already in queue
        (favoriteArtists.includes(song.artist) || favoriteGenres.includes(song.genre || ''))
      )
      .sort(() => Math.random() - 0.5)
      .slice(0, 10)

    setSuggestions(prev => ({
      ...prev,
      favorites: suggestions
    }))
  }, [userFavorites, library, queue])

  // Load suggestions when tab changes
  useEffect(() => {
    switch (activeTab) {
      case 'intelligent':
        generateIntelligentSuggestions()
        break
      case 'popular':
        generatePopularSuggestions()
        break
      case 'favorites':
        generateFavoritesSuggestions()
        break
      case 'random':
        generateRandomSuggestions()
        break
    }
  }, [activeTab, generateIntelligentSuggestions, generatePopularSuggestions, generateFavoritesSuggestions, generateRandomSuggestions])

  // Handlers
  const handleAddToQueue = useCallback(async (song: Song) => {
    try {
      await onAddToQueue(song)
      toast.success(`"${song.title}" added to queue`)
    } catch (error) {
      toast.error('Failed to add song to queue')
      console.error('Add to queue error:', error)
    }
  }, [onAddToQueue])

  const handlePlayNow = useCallback(async (song: Song) => {
    try {
      await onPlayNow(song)
      toast.success(`Now playing "${song.title}"`)
    } catch (error) {
      toast.error('Failed to play song')
      console.error('Play now error:', error)
    }
  }, [onPlayNow])

  const handleRefresh = useCallback(() => {
    switch (activeTab) {
      case 'intelligent':
        generateIntelligentSuggestions()
        break
      case 'popular':
        generatePopularSuggestions()
        break
      case 'favorites':
        generateFavoritesSuggestions()
        break
      case 'random':
        generateRandomSuggestions()
        break
    }
  }, [activeTab, generateIntelligentSuggestions, generatePopularSuggestions, generateFavoritesSuggestions, generateRandomSuggestions])

  const currentSuggestions = suggestions[activeTab as keyof typeof suggestions] || []

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Suggestions
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
              title="Refresh suggestions"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            {canAddToQueue && (
              <Badge variant="outline" className="text-xs">
                Can Add
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
            <TabsTrigger value="intelligent" className="flex items-center gap-1">
              <Sparkles className="h-3 w-3" />
              Smart
            </TabsTrigger>
            <TabsTrigger value="popular" className="flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              Popular
            </TabsTrigger>
            <TabsTrigger value="favorites" className="flex items-center gap-1">
              <Heart className="h-3 w-3" />
              Similar
            </TabsTrigger>
            <TabsTrigger value="random" className="flex items-center gap-1">
              <Shuffle className="h-3 w-3" />
              Random
            </TabsTrigger>
          </TabsList>

          <TabsContent value="intelligent" className="mt-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
                <Sparkles className="h-4 w-4" />
                AI-powered recommendations based on your current session
              </div>
              {isLoading ? (
                <div className="text-center py-8">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">Generating intelligent suggestions...</p>
                </div>
              ) : currentSuggestions.length === 0 ? (
                <div className="text-center py-8">
                  <Sparkles className="h-12 w-12 mx-auto text-muted-foreground/50 mb-3" />
                  <p className="text-muted-foreground">No intelligent suggestions available</p>
                  <p className="text-sm text-muted-foreground mt-1">Play some songs to get better recommendations!</p>
                </div>
              ) : (
                <ScrollArea className="h-[300px] sm:h-[350px] w-full">
                  <div className="space-y-2">
                    {currentSuggestions.map((song, index) => (
                      <SuggestionItem
                        key={`${song.id}-${index}`}
                        song={song}
                        index={index}
                        canAddToQueue={canAddToQueue}
                        onAddToQueue={() => handleAddToQueue(song)}
                        onPlayNow={canPlayNow ? () => handlePlayNow(song) : undefined}
                        suggestionReason="AI recommendation"
                        confidence={0.85}
                      />
                    ))}
                  </div>
                </ScrollArea>
              )}
            </div>
          </TabsContent>

          <TabsContent value="popular" className="mt-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
                <TrendingUp className="h-4 w-4" />
                Most popular tracks among all users
              </div>
              {currentSuggestions.length === 0 ? (
                <div className="text-center py-8">
                  <TrendingUp className="h-12 w-12 mx-auto text-muted-foreground/50 mb-3" />
                  <p className="text-muted-foreground">No popular suggestions available</p>
                </div>
              ) : (
                <ScrollArea className="h-[300px] sm:h-[350px] w-full">
                  <div className="space-y-2">
                    {currentSuggestions.map((song, index) => (
                      <SuggestionItem
                        key={`${song.id}-${index}`}
                        song={song}
                        index={index}
                        canAddToQueue={canAddToQueue}
                        onAddToQueue={() => handleAddToQueue(song)}
                        onPlayNow={canPlayNow ? () => handlePlayNow(song) : undefined}
                        suggestionReason="Popular choice"
                      />
                    ))}
                  </div>
                </ScrollArea>
              )}
            </div>
          </TabsContent>

          <TabsContent value="favorites" className="mt-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
                <Heart className="h-4 w-4" />
                Similar to your favorites
              </div>
              {currentSuggestions.length === 0 ? (
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 mx-auto text-muted-foreground/50 mb-3" />
                  <p className="text-muted-foreground">No similar suggestions available</p>
                  <p className="text-sm text-muted-foreground mt-1">Add some favorites to get personalized recommendations!</p>
                </div>
              ) : (
                <ScrollArea className="h-[300px] sm:h-[350px] w-full">
                  <div className="space-y-2">
                    {currentSuggestions.map((song, index) => (
                      <SuggestionItem
                        key={`${song.id}-${index}`}
                        song={song}
                        index={index}
                        canAddToQueue={canAddToQueue}
                        onAddToQueue={() => handleAddToQueue(song)}
                        onPlayNow={canPlayNow ? () => handlePlayNow(song) : undefined}
                        suggestionReason="Similar to your favorites"
                      />
                    ))}
                  </div>
                </ScrollArea>
              )}
            </div>
          </TabsContent>

          <TabsContent value="random" className="mt-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
                <Shuffle className="h-4 w-4" />
                Random discoveries from the library
              </div>
              {currentSuggestions.length === 0 ? (
                <div className="text-center py-8">
                  <Shuffle className="h-12 w-12 mx-auto text-muted-foreground/50 mb-3" />
                  <p className="text-muted-foreground">No random suggestions available</p>
                </div>
              ) : (
                <ScrollArea className="h-[300px] sm:h-[350px] w-full">
                  <div className="space-y-2">
                    {currentSuggestions.map((song, index) => (
                      <SuggestionItem
                        key={`${song.id}-${index}`}
                        song={song}
                        index={index}
                        canAddToQueue={canAddToQueue}
                        onAddToQueue={() => handleAddToQueue(song)}
                        onPlayNow={canPlayNow ? () => handlePlayNow(song) : undefined}
                        suggestionReason="Random discovery"
                      />
                    ))}
                  </div>
                </ScrollArea>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
})

export default SuggestionsPanel