"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo, memo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  Home, 
  ArrowLeft, 
  Settings,
  Crown,
  RefreshCw,
  Lightbulb,
  Users,
  Music,
  Sparkles,
  Search,
  Smartphone
} from "lucide-react"
import Link from "next/link"

// Import our new components
import PlayerControls from './player/OptimizedPlayerControls'
import QueueManager from './queue/QueueManager'
import { LibraryBrowser } from './library/LibraryBrowser'
import { MobileLibraryBrowser } from '../mobile-library-browser/MobileLibraryBrowser'
import SuggestionsPanel from './suggestions/SuggestionsPanel'
import AdminControls from './admin/AdminControls'
import { useJukeboxAuth } from './hooks/useJukeboxAuth'
import { ErrorBoundary } from '../error-boundary'

// Import existing components that we'll keep for now
import { LoginForm } from '../login-form'
import { ProfileScreen } from '../profile-screen'
import { AutoQueueFiller } from '../auto-queue-filler'
import { IntelligentAutoQueueAdmin } from './admin/IntelligentAutoQueueAdmin'
import { AutoQueueNotifications } from '../auto-queue-notifications'
import { MusicSearch } from '../music-search'

// Import Zustand store
import { useJukeboxStore } from '@/stores/jukeboxStore'

// Import session tracking
import { useSessionTracking } from '@/hooks/useSessionTracking'

// Import types and utilities
import type { Song, QueuedSong, SongSuggestion } from "@/lib/types"
import { AudioManager } from "@/lib/audio-manager"
import { MPDClient } from "@/lib/mpd-client"
import { toast } from 'sonner'

interface JukeboxContainerProps {
  debug?: boolean
}

export const JukeboxContainer = memo(function JukeboxContainer({ debug = false }: JukeboxContainerProps) {
  // Authentication hook
  const auth = useJukeboxAuth()
  
  // Session tracking for intelligent auto-queue
  const sessionTracking = useSessionTracking(
    auth.user?.id || null,
    {
      sessionType: 'jukebox',
      autoStart: true,
      enableActiveUsersPolling: true,
      activeUsersPollingInterval: 30 // 30 seconds
    }
  )
  
  // Get state from Zustand store
  const {
    queue,
    currentTrack,
    isPlaying,
    volume,
    progress,
    duration,
    setQueue,
    setCurrentTrack,
    setIsPlaying,
    setVolume,
    setProgress,
    setDuration,
    setCurrentUser,
    setUserFavorites
  } = useJukeboxStore()
  
  // Library and suggestions state
  const [library, setLibrary] = useState<Song[]>([])
  const [suggestions, setSuggestions] = useState<SongSuggestion[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  // UI state
  const [showProfileScreen, setShowProfileScreen] = useState(false)
  const [isLibraryBrowserOpen, setIsLibraryBrowserOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("library")
  
  // Audio manager
  const [audioManager, setAudioManager] = useState<AudioManager | null>(null)
  const [mpdClient, setMpdClient] = useState<MPDClient | null>(null)
  const [mpdStatus, setMpdStatus] = useState<'connecting' | 'connected' | 'error' | 'disconnected'>('disconnected')
  
  // Initialize audio when authenticated
  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      initializeAudio()
      loadQueue()
      loadSuggestions()
      // Set current user in store
      if (auth.user) {
        setCurrentUser(auth.user)
        loadUserFavorites()
      }
    }
  }, [auth.isAuthenticated, auth.isLoading, auth.user, setCurrentUser, loadUserFavorites])
  
  // Initialize audio manager - memoized to prevent recreation
  const initializeAudio = useCallback(async () => {
    try {
      // Get MPD configuration from environment
      const config = {
        host: process.env.NEXT_PUBLIC_MPD_HOST || 'localhost',
        port: parseInt(process.env.NEXT_PUBLIC_MPD_PORT || '6600'),
        password: process.env.MPD_PASSWORD || '',
        timeout: 5000,
        enableLogging: process.env.NODE_ENV === 'development'
      }
      
      // Initialize audio manager
      const manager = new AudioManager(config)
      await manager.initialize()
      setAudioManager(manager)
      setMpdStatus('connected')
      
      // Set up audio event listeners
      manager.on('trackChanged', (event) => {
        if (event.data) {
          setCurrentTrack(event.data)
        }
      })
      
      manager.on('statusUpdate', (event) => {
        if (event.data) {
          setIsPlaying(event.data.isPlaying)
          setVolume(event.data.volume)
          setProgress(event.data.elapsed)
          setDuration(event.data.duration)
          setCurrentTrack(event.data.currentTrack)
        }
      })
      
      manager.on('volumeChanged', (event) => {
        if (event.data && typeof event.data.volume === 'number') {
          setVolume(event.data.volume)
        }
      })
      
      manager.on('connected', () => {
        setMpdStatus('connected')
        toast.success('Audio system connected')
      })
      
      manager.on('disconnected', () => {
        setMpdStatus('disconnected')
        toast.warning('Audio system disconnected')
      })
      
      manager.on('error', (event) => {
        setMpdStatus('error')
        console.error('Audio manager error:', event.data)
        toast.error('Audio system error')
      })
      
    } catch (error) {
      console.error('Audio initialization failed:', error)
      setMpdStatus('error')
      toast.error('Audio system initialization failed')
    }
  }, [setCurrentTrack, setIsPlaying, setVolume, setProgress, setDuration])
  
  // Load queue from server - memoized
  const loadQueue = useCallback(async () => {
    try {
      // Get auth token
      const { ClientTokenManager } = await import('@/lib/client-auth')
      const token = ClientTokenManager.getToken()
      
      const headers: HeadersInit = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch('/api/mpd/queue', {
        credentials: 'include',
        headers
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setQueue(data.queue || [])
        }
      }
    } catch (error) {
      console.error('Failed to load queue:', error)
    }
  }, [setQueue])
  
  // Load suggestions - memoized
  const loadSuggestions = useCallback(async () => {
    try {
      const response = await fetch('/api/jukebox/suggestions', {
        credentials: 'include'
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSuggestions(data.suggestions || [])
        }
      }
    } catch (error) {
      console.error('Failed to load suggestions:', error)
    }
  }, [setSuggestions])
  
  // Load user favorites - memoized
  const loadUserFavorites = useCallback(async () => {
    if (!auth.user?.id) return
    
    try {
      const response = await fetch(`/api/jukebox/favorites?userId=${auth.user.id}`, {
        credentials: 'include'
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setUserFavorites(data.favorites || [])
        }
      }
    } catch (error) {
      console.error('Failed to load user favorites:', error)
    }
  }, [auth.user?.id, setUserFavorites])
  
  // Audio control handlers - simplified since most are now handled by components via Zustand
  
  const handleSkip = useCallback(async (direction: 'forward' | 'back') => {
    if (!audioManager) return
    
    try {
      if (direction === 'forward') {
        await audioManager.nextTrack()
      } else {
        await audioManager.previousTrack()
      }
    } catch (error) {
      console.error('Skip error:', error)
      toast.error('Skip failed')
    }
  }, [audioManager])
  
  // Volume and stop handlers are now handled by PlayerControls component through Zustand
  
  // Queue management
  const handleRemoveFromQueue = useCallback(async (songId: string) => {
    try {
      // Find the song position in the queue
      const songIndex = queue.findIndex(song => song.id === songId)
      if (songIndex === -1) {
        throw new Error('Song not found in queue')
      }
      
      // Get auth token
      const { ClientTokenManager } = await import('@/lib/client-auth')
      const token = ClientTokenManager.getToken()
      
      const headers: HeadersInit = { 'Content-Type': 'application/json' }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch('/api/mpd/queue', {
        method: 'DELETE',
        headers,
        credentials: 'include',
        body: JSON.stringify({ position: songIndex })
      })
      
      if (response.ok) {
        await loadQueue() // Refresh queue
        toast.success('Removed from queue')
      } else {
        throw new Error('Failed to remove from queue')
      }
    } catch (error) {
      console.error('Remove from queue error:', error)
      toast.error('Failed to remove from queue')
    }
  }, [queue, setQueue, loadQueue])
  
  const handleMoveInQueue = useCallback(async (songId: string, direction: 'up' | 'down') => {
    try {
      // Find the song position in the queue
      const songIndex = queue.findIndex(song => song.id === songId)
      if (songIndex === -1) {
        throw new Error('Song not found in queue')
      }
      
      const targetIndex = direction === 'up' ? songIndex - 1 : songIndex + 1
      
      // Check bounds
      if (targetIndex < 0 || targetIndex >= queue.length) {
        return // No-op if trying to move beyond bounds
      }
      
      // Get auth token
      const { ClientTokenManager } = await import('@/lib/client-auth')
      const token = ClientTokenManager.getToken()
      
      const headers: HeadersInit = { 'Content-Type': 'application/json' }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch('/api/mpd/queue', {
        method: 'PATCH',
        headers,
        credentials: 'include',
        body: JSON.stringify({ from: songIndex, to: targetIndex })
      })
      
      if (response.ok) {
        await loadQueue() // Refresh queue
        toast.success('Queue reordered')
      } else {
        throw new Error('Failed to reorder queue')
      }
    } catch (error) {
      console.error('Queue reorder error:', error)
      toast.error('Failed to reorder queue')
    }
  }, [queue, setQueue, loadQueue])
  
  const handlePlayTrack = useCallback(async (trackId: string) => {
    try {
      // Find the track in the queue
      const track = queue.find(song => song.id === trackId)
      if (!track) {
        throw new Error('Track not found in queue')
      }
      
      if (audioManager) {
        await audioManager.playTrack(track)
      }
    } catch (error) {
      console.error('Play track error:', error)
      toast.error('Failed to play track')
    }
  }, [audioManager, queue])
  
  // UI handlers
  const handleProfileClick = () => {
    setShowProfileScreen(true)
  }
  
  const handleBackFromProfile = () => {
    setShowProfileScreen(false)
  }
  
  // Manual refresh
  const handleRefresh = async () => {
    setIsLoading(true)
    try {
      await Promise.all([
        loadQueue(),
        loadSuggestions(),
        loadUserFavorites(),
        auth.refreshAuth()
      ])
      toast.success('Refreshed successfully')
    } catch (error) {
      console.error('Refresh error:', error)
      toast.error('Refresh failed')
    } finally {
      setIsLoading(false)
    }
  }
  
  // Handle songs added by auto queue filler
  const handleSongsAdded = useCallback(async (songs: Song[]) => {
    console.log(`Auto queue filler added ${songs.length} songs`)
    // Refresh the queue to show the new songs
    await loadQueue()
    toast.success(`Auto queue filler added ${songs.length} songs to queue`)
  }, [])
  
  // Role utilities - memoized
  const roleHelpers = useMemo(() => ({
    getRoleIcon: (role: string) => {
      switch (role) {
        case 'superuser': return <Crown className="h-4 w-4" />
        case 'dj': return <Settings className="h-4 w-4" />
        default: return null
      }
    },
    getRoleBadgeColor: (role: string) => {
      switch (role) {
        case 'superuser': return 'bg-yellow-500/10 text-yellow-600 border-yellow-500/20'
        case 'dj': return 'bg-purple-500/10 text-purple-600 border-purple-500/20'
        default: return 'bg-blue-500/10 text-blue-600 border-blue-500/20'
      }
    }
  }), [])
  
  // Show login form if not authenticated
  if (!auth.isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-muted/20 p-4">
        <div className="max-w-md mx-auto mt-20">
          <Card>
            <CardContent className="p-6">
              <div className="text-center mb-6">
                <h1 className="text-2xl font-bold mb-2">🎵 Jukebox</h1>
                <p className="text-muted-foreground">Sign in to access the music library</p>
              </div>
              <LoginForm />
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }
  
  // Show profile screen
  if (showProfileScreen) {
    return (
      <ProfileScreen 
        onBack={handleBackFromProfile}
        onRefreshRole={auth.refreshAuth}
      />
    )
  }
  
  // Loading state
  if (auth.isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-muted/20 p-4 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading jukebox...</p>
        </div>
      </div>
    )
  }
  
  // Main jukebox interface
  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20 p-4">
      {/* Auto Queue Notifications */}
      <AutoQueueNotifications 
        enabled={true} 
        showToasts={true}
        onNotification={(notification) => {
          // Refresh queue when songs are added
          if (notification.type === 'songs-added' && notification.details?.songsAdded) {
            loadQueue()
          }
        }}
      />
      
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </Link>
            <h1 className="text-3xl font-bold">🎵 Jukebox</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Active Users Display */}
            {sessionTracking.sessionStats && (
              <Badge variant="outline" className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                {sessionTracking.sessionStats.activeCount} online
              </Badge>
            )}
            
            {/* Session Status */}
            {sessionTracking.isSessionActive && (
              <Badge variant="outline" className="flex items-center gap-1 text-green-600">
                <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
                Connected
              </Badge>
            )}
            
            {/* User info */}
            <Badge 
              variant="secondary" 
              className={`${roleHelpers.getRoleBadgeColor(auth.userRole)} flex items-center gap-1`}
            >
              {roleHelpers.getRoleIcon(auth.userRole)}
              {auth.user?.username || 'User'}
            </Badge>
            
            {/* Refresh button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            
            {/* Profile button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleProfileClick}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Main content */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Left column - Player and Queue */}
          <div className="space-y-6 lg:col-span-1">
            {/* Player Controls */}
            <PlayerControls
              audioManager={audioManager}
              onNext={() => handleSkip('forward')}
              onPrevious={() => handleSkip('back')}
              userRole={auth.userRole}
            />
            
            {/* Queue */}
            <QueueManager
              userRole={auth.userRole}
              currentUser={auth.user}
              onRemoveFromQueue={handleRemoveFromQueue}
              onPlayTrack={handlePlayTrack}
              onToggleFavorite={async (songId: string, isFavorite: boolean) => {
                // TODO: Implement toggle favorite functionality
                console.log('Toggle favorite:', songId, isFavorite)
                toast.success(isFavorite ? 'Added to favorites' : 'Removed from favorites')
              }}
              onMoveInQueue={handleMoveInQueue}
            />
          </div>
          
          {/* Right columns - Library and Content */}
          <div className="lg:col-span-2 order-first lg:order-last">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4 grid w-full grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 h-auto">
                <TabsTrigger value="library" className="text-xs sm:text-sm">
                  <Music className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
                  <span className="hidden sm:inline">Library</span>
                </TabsTrigger>
                <TabsTrigger value="mobile-library" className="text-xs sm:text-sm">
                  <Smartphone className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
                  <span className="hidden sm:inline">Browse</span>
                </TabsTrigger>
                <TabsTrigger value="suggestions" className="text-xs sm:text-sm">
                  <Sparkles className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
                  <span className="hidden sm:inline">Suggestions</span>
                </TabsTrigger>
                <TabsTrigger value="search" className="text-xs sm:text-sm">
                  <Search className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
                  <span className="hidden sm:inline">Search</span>
                </TabsTrigger>
                {auth.hasDirectQueueAccess() && (
                  <TabsTrigger value="admin" className="text-xs sm:text-sm">
                    <Settings className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-1" />
                    <span className="hidden sm:inline">Admin</span>
                  </TabsTrigger>
                )}
              </TabsList>
              
              <TabsContent value="library">
                <ErrorBoundary>
                  <LibraryBrowser
                    userRole={auth.userRole}
                    isVisible={true}
                  />
                </ErrorBoundary>
              </TabsContent>
              
              <TabsContent value="mobile-library">
                <ErrorBoundary>
                  <div className="h-[600px] overflow-hidden rounded-lg border">
                    <MobileLibraryBrowser
                      userRole={auth.userRole}
                      isVisible={true}
                    />
                  </div>
                </ErrorBoundary>
              </TabsContent>
              
              <TabsContent value="suggestions">
                <ErrorBoundary>
                  <SuggestionsPanel
                    userRole={auth.userRole}
                    currentUser={auth.user}
                    onAddToQueue={async (song: Song) => {
                      try {
                        const response = await fetch('/api/mpd/queue', {
                          method: 'POST',
                          headers: { 'Content-Type': 'application/json' },
                          credentials: 'include',
                          body: JSON.stringify({
                            action: 'add',
                            filePath: song.filePath
                          })
                        })
                        
                        if (response.ok) {
                          await loadQueue() // Refresh queue
                          toast.success(`Added "${song.title}" to queue`)
                        } else {
                          throw new Error('Failed to add to queue')
                        }
                      } catch (error) {
                        console.error('Add to queue error:', error)
                        toast.error('Failed to add to queue')
                      }
                    }}
                    onPlayNow={async (song: Song) => {
                      try {
                        // Add to queue first
                        const response = await fetch('/api/mpd/queue', {
                          method: 'POST',
                          headers: { 'Content-Type': 'application/json' },
                          credentials: 'include',
                          body: JSON.stringify({
                            action: 'add',
                            filePath: song.filePath
                          })
                        })
                        
                        if (response.ok) {
                          await loadQueue() // Refresh queue
                          // Then play the track immediately
                          if (audioManager) {
                            await audioManager.playTrack(song as any)
                          }
                          toast.success(`Now playing "${song.title}"`)
                        } else {
                          throw new Error('Failed to play song')
                        }
                      } catch (error) {
                        console.error('Play now error:', error)
                        toast.error('Failed to play song')
                      }
                    }}
                  />
                </ErrorBoundary>
              </TabsContent>
              
              <TabsContent value="search">
                <ErrorBoundary>
                  <MusicSearch />
                </ErrorBoundary>
              </TabsContent>
              
              {auth.hasDirectQueueAccess() && (
                <TabsContent value="admin">
                  <ErrorBoundary>
                    <div className="space-y-6">
                      <AdminControls
                        userRole={auth.userRole}
                        currentUser={auth.user}
                        onVolumeOverride={async (volume: number) => {
                          if (audioManager) {
                            await audioManager.setVolume(volume)
                          }
                        }}
                        onSkipTrack={() => handleSkip('forward')}
                        onPauseAll={async () => {
                          if (audioManager) {
                            await audioManager.pause()
                          }
                        }}
                        onClearQueue={async () => {
                          try {
                            const response = await fetch('/api/mpd/queue', {
                              method: 'DELETE',
                              headers: { 'Content-Type': 'application/json' },
                              credentials: 'include',
                              body: JSON.stringify({ clearAll: true })
                            })
                            
                            if (response.ok) {
                              await loadQueue()
                              toast.success('Queue cleared')
                            } else {
                              throw new Error('Failed to clear queue')
                            }
                          } catch (error) {
                            console.error('Clear queue error:', error)
                            toast.error('Failed to clear queue')
                          }
                        }}
                      />
                      
                      <IntelligentAutoQueueAdmin
                        currentQueueLength={queue.length}
                        onSongsAdded={handleSongsAdded}
                        isAdmin={auth.hasDirectQueueAccess()}
                        userId={auth.user?.id || ''}
                      />
                    </div>
                  </ErrorBoundary>
                </TabsContent>
              )}
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
})

JukeboxContainer.displayName = 'JukeboxContainer' 