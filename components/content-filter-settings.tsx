'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'
import { Filter, Music, Calendar, Trophy, Sparkles, Database, Settings2, Loader2, Folder, ListMusic } from 'lucide-react'
import { ContentFilterService } from '@/lib/services/content-filter-service'
import type { ContentFilters, FilterPreset, GameFilterSettings } from '@/lib/types/filters'
import { FILTER_PRESETS } from '@/lib/types/filters'
import { PlaylistCategoryFilter } from './playlist-category-filter'

interface ContentFilterSettingsProps {
  onFiltersChange?: (filters: ContentFilters) => void
  gameMode?: string
  compact?: boolean
}

export function ContentFilterSettings({ 
  onFiltersChange, 
  gameMode,
  compact = false 
}: ContentFilterSettingsProps) {
  const filterService = ContentFilterService.getInstance()
  
  const [settings, setSettings] = useState<GameFilterSettings>(filterService.loadSettings())
  const [filters, setFilters] = useState<ContentFilters>(filterService.getCurrentFilters())
  const [selectedPreset, setSelectedPreset] = useState<string>('all-content')
  const [availableGenres, setAvailableGenres] = useState<string[]>([])
  const [availableFolders, setAvailableFolders] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  
  // Refresh filter state when component mounts or becomes visible
  useEffect(() => {
    const currentSettings = filterService.loadSettings()
    setSettings(currentSettings)
    
    // Load the current filters from the service
    const currentFilters = filterService.getCurrentFilters()
    setFilters(currentFilters)
    
    // Determine the active preset
    if (currentSettings.activePresetId) {
      setSelectedPreset(currentSettings.activePresetId)
    } else if (currentSettings.customFilters) {
      setSelectedPreset('custom')
    } else {
      setSelectedPreset('all-content')
    }
    
    loadAvailableGenres()
    loadAvailableFolders()
  }, [])
  
  const loadAvailableGenres = async () => {
    try {
      const response = await fetch('/api/library/genres')
      const data = await response.json()
      if (data.success && data.genres) {
        setAvailableGenres(data.genres)
      }
    } catch (error) {
      console.error('Failed to load genres:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const loadAvailableFolders = async () => {
    try {
      const response = await fetch('/api/library/folders')
      const data = await response.json()
      if (data.success && data.folders) {
        setAvailableFolders(data.folders)
      }
    } catch (error) {
      console.error('Failed to load folders:', error)
    }
  }
  
  const handlePresetChange = (presetId: string) => {
    setSelectedPreset(presetId)
    const preset = filterService.getPreset(presetId)
    if (preset) {
      setFilters(preset.filters)
      filterService.applyPreset(presetId)
      onFiltersChange?.(preset.filters)
      toast.success(`Applied "${preset.name}" filter preset`)
    }
  }
  
  const handleFilterToggle = (enabled: boolean) => {
    filterService.setFilteringEnabled(enabled)
    setSettings({ ...settings, enabled })
    
    if (enabled) {
      toast.success('Content filtering enabled', {
        description: 'Songs will be filtered based on your preferences'
      })
    } else {
      toast.info('Content filtering disabled', {
        description: 'All songs will be available'
      })
    }
  }
  
  const handleCustomFilterChange = (newFilters: Partial<ContentFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    filterService.applyCustomFilters(updatedFilters)
    onFiltersChange?.(updatedFilters)
    setSelectedPreset('custom')
  }
  
  const handleGenreToggle = (genre: string, checked: boolean) => {
    const newGenres = checked 
      ? [...filters.genres.values, genre]
      : filters.genres.values.filter(g => g !== genre)
    
    handleCustomFilterChange({
      genres: { ...filters.genres, values: newGenres }
    })
  }
  
  const handleFolderToggle = (folder: string, checked: boolean) => {
    const newFolders = checked 
      ? [...(filters.folders?.values || []), folder]
      : (filters.folders?.values || []).filter(f => f !== folder)
    
    handleCustomFilterChange({
      folders: { ...filters.folders, mode: filters.folders?.mode || 'include', values: newFolders }
    })
  }
  
  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    )
  }
  
  if (compact) {
    // Compact view for game settings
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Content Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="filter-enabled" className="text-sm">Enable Filtering</Label>
            <Switch
              id="filter-enabled"
              checked={settings.enabled}
              onCheckedChange={handleFilterToggle}
            />
          </div>
          
          {settings.enabled && (
            <>
              <div>
                <Label htmlFor="filter-preset" className="text-sm">Filter Preset</Label>
                <Select value={selectedPreset} onValueChange={handlePresetChange}>
                  <SelectTrigger id="filter-preset" className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FILTER_PRESETS.map(preset => (
                      <SelectItem key={preset.id} value={preset.id}>
                        {preset.name}
                      </SelectItem>
                    ))}
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={() => window.open('/settings/filters', '_blank')}
              >
                <Settings2 className="h-4 w-4 mr-2" />
                Advanced Settings
              </Button>
            </>
          )}
        </CardContent>
      </Card>
    )
  }
  
  // Full view for settings page
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Content Filters
        </CardTitle>
        <CardDescription>
          Control which songs appear in games and playlists
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Master Toggle */}
        <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div>
            <Label htmlFor="master-filter" className="text-base">Enable Content Filtering</Label>
            <p className="text-sm text-muted-foreground">
              Apply filters to all game modes and features
            </p>
          </div>
          <Switch
            id="master-filter"
            checked={settings.enabled}
            onCheckedChange={handleFilterToggle}
          />
        </div>
        
        {settings.enabled && (
          <>
            {/* Preset Selection */}
            <div>
              <Label htmlFor="preset-select">Filter Preset</Label>
              <Select value={selectedPreset} onValueChange={handlePresetChange}>
                <SelectTrigger id="preset-select" className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {FILTER_PRESETS.map(preset => (
                    <SelectItem key={preset.id} value={preset.id}>
                      <div>
                        <div className="font-medium">{preset.name}</div>
                        {preset.description && (
                          <div className="text-xs text-muted-foreground">{preset.description}</div>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">Custom Configuration</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Custom Filters */}
            <Tabs defaultValue="genres" className="mt-6">
              <TabsList className="grid grid-cols-3 md:grid-cols-6 w-full">
                <TabsTrigger value="genres">
                  <Music className="h-4 w-4 mr-1" />
                  Genres
                </TabsTrigger>
                <TabsTrigger value="playlists">
                  <ListMusic className="h-4 w-4 mr-1" />
                  Categories
                </TabsTrigger>
                <TabsTrigger value="folders">
                  <Folder className="h-4 w-4 mr-1" />
                  Folders
                </TabsTrigger>
                <TabsTrigger value="years">
                  <Calendar className="h-4 w-4 mr-1" />
                  Years
                </TabsTrigger>
                <TabsTrigger value="quality">
                  <Sparkles className="h-4 w-4 mr-1" />
                  Quality
                </TabsTrigger>
                <TabsTrigger value="metadata">
                  <Database className="h-4 w-4 mr-1" />
                  Metadata
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="genres" className="space-y-4 mt-4">
                <div>
                  <Label className="text-sm font-medium">Filter Mode</Label>
                  <Select 
                    value={filters.genres.mode} 
                    onValueChange={(mode: 'include' | 'exclude') => 
                      handleCustomFilterChange({ genres: { ...filters.genres, mode } })
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="include">Include selected genres</SelectItem>
                      <SelectItem value="exclude">Exclude selected genres</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label className="text-sm font-medium mb-2">Available Genres</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-64 overflow-y-auto p-1">
                    {availableGenres.map(genre => (
                      <div key={genre} className="flex items-center space-x-2">
                        <Checkbox
                          id={genre}
                          checked={filters.genres.values.includes(genre)}
                          onCheckedChange={(checked) => handleGenreToggle(genre, !!checked)}
                        />
                        <Label htmlFor={genre} className="text-sm cursor-pointer">
                          {genre}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="playlists" className="space-y-4 mt-4">
                <PlaylistCategoryFilter
                  selectedCategories={filters.playlists.values}
                  onCategoriesChange={(categories) => 
                    handleCustomFilterChange({ playlists: { ...filters.playlists, values: categories } })
                  }
                  includeMyItunes={filters.sources.includeMyItunes}
                  onMyItunesChange={(include) => 
                    handleCustomFilterChange({ sources: { ...filters.sources, includeMyItunes: include } })
                  }
                />
              </TabsContent>
              
              <TabsContent value="folders" className="space-y-4 mt-4">
                <div>
                  <Label className="text-sm font-medium">Filter Mode</Label>
                  <Select 
                    value={filters.folders?.mode || 'include'} 
                    onValueChange={(mode: 'include' | 'exclude') => 
                      handleCustomFilterChange({ folders: { ...filters.folders, mode, values: filters.folders?.values || [] } })
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="include">Include selected folders</SelectItem>
                      <SelectItem value="exclude">Exclude selected folders</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label className="text-sm font-medium mb-2">Import Folders</Label>
                  <p className="text-xs text-muted-foreground mb-3">
                    Folders preserved from your music import (e.g., DC2016, Billboard2020, Milestones)
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-64 overflow-y-auto p-1">
                    {availableFolders.map(folder => (
                      <div key={folder} className="flex items-center space-x-2">
                        <Checkbox
                          id={folder}
                          checked={(filters.folders?.values || []).includes(folder)}
                          onCheckedChange={(checked) => handleFolderToggle(folder, !!checked)}
                        />
                        <Label htmlFor={folder} className="text-sm cursor-pointer">
                          {folder}
                        </Label>
                      </div>
                    ))}
                  </div>
                  {availableFolders.length === 0 && (
                    <p className="text-sm text-muted-foreground italic">
                      No import folders found. Folders are detected from your music file structure.
                    </p>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="years" className="space-y-4 mt-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="year-filter">Enable Year Range Filter</Label>
                  <Switch
                    id="year-filter"
                    checked={filters.yearRange.enabled}
                    onCheckedChange={(enabled) => 
                      handleCustomFilterChange({ yearRange: { ...filters.yearRange, enabled } })
                    }
                  />
                </div>
                
                {filters.yearRange.enabled && (
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm">Year Range</Label>
                      <div className="flex items-center gap-4 mt-2">
                        <input
                          type="number"
                          className="w-24 px-3 py-1 border rounded"
                          placeholder="Min"
                          value={filters.yearRange.min || ''}
                          onChange={(e) => handleCustomFilterChange({
                            yearRange: { ...filters.yearRange, min: parseInt(e.target.value) || undefined }
                          })}
                        />
                        <span>to</span>
                        <input
                          type="number"
                          className="w-24 px-3 py-1 border rounded"
                          placeholder="Max"
                          value={filters.yearRange.max || ''}
                          onChange={(e) => handleCustomFilterChange({
                            yearRange: { ...filters.yearRange, max: parseInt(e.target.value) || undefined }
                          })}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="quality" className="space-y-4 mt-4">
                <div>
                  <Label className="text-sm">Difficulty Range</Label>
                  <div className="flex items-center gap-4 mt-2">
                    <span className="text-sm w-8">{filters.quality.minDifficulty || 1}</span>
                    <Slider
                      value={[filters.quality.minDifficulty || 1, filters.quality.maxDifficulty || 5]}
                      onValueChange={([min, max]) => handleCustomFilterChange({
                        quality: { ...filters.quality, minDifficulty: min, maxDifficulty: max }
                      })}
                      min={1}
                      max={5}
                      step={1}
                      className="flex-1"
                    />
                    <span className="text-sm w-8">{filters.quality.maxDifficulty || 5}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="require-art">Require Album Art</Label>
                  <Switch
                    id="require-art"
                    checked={filters.quality.requireAlbumArt || false}
                    onCheckedChange={(checked) => handleCustomFilterChange({
                      quality: { ...filters.quality, requireAlbumArt: checked }
                    })}
                  />
                </div>
              </TabsContent>
              
              <TabsContent value="metadata" className="space-y-4 mt-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="require-year">Require Year Information</Label>
                    <Switch
                      id="require-year"
                      checked={filters.metadata.requireYear || false}
                      onCheckedChange={(checked) => handleCustomFilterChange({
                        metadata: { ...filters.metadata, requireYear: checked }
                      })}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="require-genre">Require Genre Tag</Label>
                    <Switch
                      id="require-genre"
                      checked={filters.metadata.requireGenre || false}
                      onCheckedChange={(checked) => handleCustomFilterChange({
                        metadata: { ...filters.metadata, requireGenre: checked }
                      })}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="exclude-live">Exclude Live Recordings</Label>
                    <Switch
                      id="exclude-live"
                      checked={filters.metadata.excludeLiveRecordings || false}
                      onCheckedChange={(checked) => handleCustomFilterChange({
                        metadata: { ...filters.metadata, excludeLiveRecordings: checked }
                      })}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="exclude-remixes">Exclude Remixes</Label>
                    <Switch
                      id="exclude-remixes"
                      checked={filters.metadata.excludeRemixes || false}
                      onCheckedChange={(checked) => handleCustomFilterChange({
                        metadata: { ...filters.metadata, excludeRemixes: checked }
                      })}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </CardContent>
    </Card>
  )
}