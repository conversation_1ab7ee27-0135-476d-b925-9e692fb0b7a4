"use client"

import { useState, useEffect } from "react"
import { useUser } from "@/lib/user-context"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { ArrowLeft, User, Settings, Trophy, Music, Star, Crown, Zap, Edit3, Save, X } from "lucide-react"
import { toast } from "sonner"

interface ProfileScreenProps {
  onBackToMenu: () => void
}

const musicStarAvatars = [
  { id: "rockstar", name: "Rock Legend", emoji: "🎸", description: "Classic rock vibes" },
  { id: "popstar", name: "Pop Icon", emoji: "⭐", description: "Chart-topping energy" },
  { id: "dj", name: "DJ Master", emoji: "🎧", description: "Mix master" },
  { id: "rapper", name: "Hip-Hop Head", emoji: "🎤", description: "Mic drop master" },
  { id: "elvis", name: "The King", emoji: "🎤", description: "Rock and Roll Legend" },
  { id: "madonna", name: "Queen of Pop", emoji: "💃", description: "Pop Icon" },
  { id: "bowie", name: "Starman", emoji: "⭐", description: "Glam Rock Pioneer" },
  { id: "prince", name: "Purple One", emoji: "💜", description: "Musical Genius" },
  { id: "whitney", name: "The Voice", emoji: "🎵", description: "Vocal Powerhouse" },
  { id: "mj", name: "King of Pop", emoji: "🕺", description: "Entertainment Legend" },
  { id: "freddie", name: "Rock Legend", emoji: "👑", description: "Queen Frontman" },
  { id: "aretha", name: "Queen of Soul", emoji: "💫", description: "Soul Legend" },
]

export function ProfileScreen({ onBackToMenu }: ProfileScreenProps) {
  const { user, refreshUser } = useUser()
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [selectedAvatar, setSelectedAvatar] = useState(user?.avatar || "rockstar")
  
  const [profile, setProfile] = useState({
    username: user?.username || "",
    displayName: user?.displayName || "",
    favoriteGenres: user?.favoriteGenres || [],
    location: user?.location || "",
  })

  const [settings, setSettings] = useState({
    emailNotifications: user?.settings?.emailNotifications ?? true,
    soundEffects: user?.settings?.soundEffects ?? true,
    autoPlay: user?.settings?.autoPlay ?? false,
    showHints: user?.settings?.showHints ?? true,
    publicProfile: user?.settings?.publicProfile ?? true,
    shareStats: user?.settings?.shareStats ?? false,
  })

  // Sync profile state with user context when user changes
  useEffect(() => {
    if (user) {
      setProfile({
        username: user.username || "",
        displayName: user.displayName || "",
        favoriteGenres: user.favoriteGenres || [],
        location: user.location || "",
      })
      setSelectedAvatar(user.avatar || "rockstar")
      setSettings({
        emailNotifications: user.settings?.emailNotifications ?? true,
        soundEffects: user.settings?.soundEffects ?? true,
        autoPlay: user.settings?.autoPlay ?? false,
        showHints: user.settings?.showHints ?? true,
        publicProfile: user.settings?.publicProfile ?? true,
        shareStats: user.settings?.shareStats ?? false,
      })
    }
  }, [user])

  const handleSaveProfile = async () => {
    if (!user) return

    try {
      setIsSaving(true)
      
      // Get auth token
      const { ClientTokenManager } = await import('@/lib/client-auth')
      const token = ClientTokenManager.getToken()
      
      if (!token) {
        toast.error('Please log in again')
        return
      }

      // Update profile via API
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          username: profile.username,
          displayName: profile.displayName,
          avatarUrl: selectedAvatar,
          favoriteGenres: profile.favoriteGenres,
          location: profile.location
        })
      })

      const data = await response.json()
      
      if (data.success) {
        // Refresh user data from the database
        await refreshUser()
        setIsEditing(false)
        toast.success('Profile updated successfully!')
      } else {
        toast.error(data.error || 'Failed to update profile')
      }
    } catch (error) {
      console.error('Profile update error:', error)
      toast.error('Failed to update profile')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancelEdit = () => {
    // Reset to original values
    if (user) {
      setProfile({
        username: user.username || "",
        displayName: user.displayName || "",
        favoriteGenres: user.favoriteGenres || [],
        location: user.location || "",
      })
      setSelectedAvatar(user.avatar || "rockstar")
    }
    setIsEditing(false)
  }

  const selectedAvatarData = musicStarAvatars.find((avatar) => avatar.id === selectedAvatar)

  const userStats = user?.stats || {
    totalGames: 0,
    winRate: 0,
    averageScore: 0,
    bestStreak: 0,
    hoursPlayed: 0,
    achievements: 0,
    rank: 0,
    favoriteMode: "Classic Quiz",
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBackToMenu}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Menu
        </Button>
        <h1 className="text-3xl font-bold">Profile</h1>
        <div className="flex gap-2">
          {isEditing && (
            <Button variant="outline" onClick={handleCancelEdit} disabled={isSaving}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          )}
          <Button
            variant={isEditing ? "default" : "outline"}
            onClick={isEditing ? handleSaveProfile : () => setIsEditing(true)}
            disabled={isSaving}
          >
            {isEditing ? (
              <>
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? "Saving..." : "Save Changes"}
              </>
            ) : (
              <>
                <Edit3 className="h-4 w-4 mr-2" />
                Edit Profile
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="avatar">Avatar</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Profile Header */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row items-center gap-6">
                <div className="relative">
                  <div className="w-24 h-24 border-4 border-primary shadow-lg rounded-full bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900 dark:to-blue-900 flex items-center justify-center">
                    <span className="text-4xl">{selectedAvatarData?.emoji || "🎵"}</span>
                  </div>
                  <Badge className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-yellow-400 to-orange-500">
                    Level {user?.level || 1}
                  </Badge>
                </div>

                <div className="flex-1 space-y-4">
                  {isEditing ? (
                    <div className="space-y-3">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <Label htmlFor="username">Username</Label>
                          <Input
                            id="username"
                            value={profile.username}
                            onChange={(e) => setProfile({ ...profile, username: e.target.value })}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="displayName">Display Name</Label>
                          <Input
                            id="displayName"
                            value={profile.displayName}
                            onChange={(e) => setProfile({ ...profile, displayName: e.target.value })}
                            className="mt-1"
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="location">Location (optional)</Label>
                        <Input
                          id="location"
                          value={profile.location}
                          onChange={(e) => setProfile({ ...profile, location: e.target.value })}
                          className="mt-1"
                          placeholder="e.g. New York, USA"
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="text-center md:text-left">
                      <h2 className="text-2xl font-bold">{profile.displayName || profile.username}</h2>
                      <p className="text-muted-foreground">{selectedAvatarData?.description}</p>
                      {profile.location && (
                        <p className="text-sm text-muted-foreground mt-1">📍 {profile.location}</p>
                      )}
                      {profile.favoriteGenres.length > 0 && (
                        <div className="flex flex-wrap gap-2 justify-center md:justify-start mt-3">
                          {profile.favoriteGenres.map((genre) => (
                            <Badge key={genre} variant="secondary">
                              {genre}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  )}

                  {/* XP Progress */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Experience Points</span>
                      <span>
                        {user?.xp || 0} / {(user?.xp || 0) + (user?.xpToNext || 100)} XP
                      </span>
                    </div>
                    <Progress 
                      value={((user?.xp || 0) / ((user?.xp || 0) + (user?.xpToNext || 100))) * 100} 
                      className="h-2" 
                    />
                    <p className="text-xs text-muted-foreground">{user?.xpToNext || 100} XP to next level</p>
                  </div>
                </div>

                <div className="text-center">
                  <Badge className="bg-purple-600 mb-2">
                    <Crown className="h-3 w-3 mr-1" />
                    {user?.role === 'superuser' ? 'Admin' : user?.role === 'dj' ? 'DJ' : 'Music Fan'}
                  </Badge>
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <p className="text-lg font-bold text-green-600">#{userStats.rank || 999}</p>
                      <p className="text-xs text-muted-foreground">Global Rank</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <Music className="h-6 w-6 text-blue-600" />
                <CardTitle className="text-xl font-bold">{userStats.totalGames}</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground">Games Played</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <Trophy className="h-6 w-6 text-yellow-600" />
                <CardTitle className="text-xl font-bold">{userStats.averageScore}</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground">Avg Score</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <Zap className="h-6 w-6 text-orange-600" />
                <CardTitle className="text-xl font-bold">{userStats.bestStreak}</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground">Best Streak</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <Star className="h-6 w-6 text-purple-600" />
                <CardTitle className="text-xl font-bold">{userStats.achievements}</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground">Achievements</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="avatar" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Choose Your Avatar
              </CardTitle>
              <p className="text-muted-foreground">
                Select an avatar inspired by music legends
              </p>
            </CardHeader>
            <CardContent>
              {/* Current Selection */}
              <div className="mb-6 text-center">
                <div className="inline-block relative">
                  <div className="w-20 h-20 border-4 border-primary shadow-lg rounded-full bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900 dark:to-blue-900 flex items-center justify-center">
                    <span className="text-3xl">{selectedAvatarData?.emoji || "🎵"}</span>
                  </div>
                  <Badge className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">Current</Badge>
                </div>
                <h3 className="text-lg font-bold mt-2">{selectedAvatarData?.name}</h3>
                <p className="text-sm text-muted-foreground">{selectedAvatarData?.description}</p>
              </div>

              {/* Avatar Grid */}
              <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {musicStarAvatars.map((avatar) => (
                  <Card
                    key={avatar.id}
                    className={`cursor-pointer transition-all hover:shadow-lg ${
                      selectedAvatar === avatar.id ? "ring-2 ring-primary bg-primary/5" : ""
                    }`}
                    onClick={() => setSelectedAvatar(avatar.id)}
                  >
                    <CardContent className="p-3 text-center">
                      <div className="w-12 h-12 mx-auto mb-2 border-2 border-muted rounded-full bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900 dark:to-blue-900 flex items-center justify-center">
                        <span className="text-xl">{avatar.emoji}</span>
                      </div>
                      <h4 className="font-medium text-xs">{avatar.name}</h4>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Settings
              </CardTitle>
              <p className="text-muted-foreground">
                Customize your experience
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Sound Effects</p>
                    <p className="text-sm text-muted-foreground">Play sounds during gameplay</p>
                  </div>
                  <Switch
                    checked={settings.soundEffects}
                    onCheckedChange={(checked) => setSettings({ ...settings, soundEffects: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Auto Play</p>
                    <p className="text-sm text-muted-foreground">Auto-advance to next song</p>
                  </div>
                  <Switch
                    checked={settings.autoPlay}
                    onCheckedChange={(checked) => setSettings({ ...settings, autoPlay: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Show Hints</p>
                    <p className="text-sm text-muted-foreground">Display helpful hints</p>
                  </div>
                  <Switch
                    checked={settings.showHints}
                    onCheckedChange={(checked) => setSettings({ ...settings, showHints: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Public Profile</p>
                    <p className="text-sm text-muted-foreground">Visible to other users</p>
                  </div>
                  <Switch
                    checked={settings.publicProfile}
                    onCheckedChange={(checked) => setSettings({ ...settings, publicProfile: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}