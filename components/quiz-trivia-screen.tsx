"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Trophy, 
  Music, 
  Calendar, 
  Disc, 
  TrendingUp, 
  Star,
  Info,
  Users,
  Crown,
  Medal,
  Award
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import Image from "next/image"
import { FavoriteButton } from "./favorite-button"

interface Player {
  id: string
  name: string
  score: number
  position?: number
  scoreChange?: number
  isYou?: boolean
}

interface TrackInfo {
  id: number
  file: string
  title: string
  artist: string
  album?: string
  year?: number
  albumArtUrl?: string
  genre?: string
  duration?: number
  popularityScore?: number
  chartPosition?: number
  releaseDate?: string
  triviaFacts?: string[]
  interestingFacts?: any
}

interface QuizTriviaScreenProps {
  track: TrackInfo
  players: Player[]
  currentPlayerId?: string
  questionNumber: number
  totalQuestions: number
  onContinue?: () => void
  autoAdvanceTime?: number // seconds
}

export function QuizTriviaScreen({ 
  track, 
  players, 
  currentPlayerId,
  questionNumber,
  totalQuestions,
  onContinue,
  autoAdvanceTime = 10
}: QuizTriviaScreenProps) {
  const [timeRemaining, setTimeRemaining] = useState(autoAdvanceTime)
  
  // Parse trivia facts if they're a JSON string
  const triviaFacts = track.triviaFacts 
    ? (typeof track.triviaFacts === 'string' 
        ? JSON.parse(track.triviaFacts) 
        : track.triviaFacts)
    : []
    
  const interestingFacts = track.interestingFacts
    ? (typeof track.interestingFacts === 'string' 
        ? JSON.parse(track.interestingFacts) 
        : track.interestingFacts)
    : {}

  // Sort players by score for leaderboard
  const sortedPlayers = [...players]
    .sort((a, b) => b.score - a.score)
    .map((player, index) => ({
      ...player,
      position: index + 1,
      isYou: player.id === currentPlayerId
    }))

  // Auto-advance timer
  useEffect(() => {
    if (timeRemaining <= 0) {
      onContinue?.()
      return
    }

    const timer = setTimeout(() => {
      setTimeRemaining(prev => prev - 1)
    }, 1000)

    return () => clearTimeout(timer)
  }, [timeRemaining, onContinue])

  // Format duration from seconds to mm:ss
  const formatDuration = (seconds?: number) => {
    if (!seconds) return null
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Get position medal
  const getPositionIcon = (position: number) => {
    switch (position) {
      case 1:
        return <Crown className="h-5 w-5 text-yellow-500" />
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />
      case 3:
        return <Award className="h-5 w-5 text-orange-600" />
      default:
        return <span className="text-sm font-bold w-5 text-center">{position}</span>
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="max-w-6xl mx-auto space-y-6 p-4"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Round {questionNumber} Complete!</h2>
          <p className="text-gray-500">
            {questionNumber} of {totalQuestions} • Next round in {timeRemaining}s
          </p>
        </div>
        <Progress 
          value={(timeRemaining / autoAdvanceTime) * 100} 
          className="w-32 h-2"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Song Info Card */}
        <div className="lg:col-span-2">
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="flex flex-col md:flex-row">
                {/* Album Art */}
                <div className="md:w-64 h-64 relative bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800">
                  {track.albumArtUrl ? (
                    <Image
                      src={track.albumArtUrl}
                      alt={`${track.album || track.title} cover`}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Disc className="h-24 w-24 text-gray-400 animate-spin-slow" />
                    </div>
                  )}
                </div>

                {/* Track Details */}
                <div className="flex-1 p-6 space-y-4">
                  <div>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-2xl font-bold">{track.title}</h3>
                        <p className="text-lg text-gray-600 dark:text-gray-400">{track.artist}</p>
                        {track.album && (
                          <p className="text-sm text-gray-500 mt-1">from "{track.album}"</p>
                        )}
                      </div>
                      <FavoriteButton 
                        track={track} 
                        userId={currentPlayerId}
                        size="default"
                        variant="outline"
                        className="ml-4"
                      />
                    </div>
                  </div>

                  {/* Metadata */}
                  <div className="flex flex-wrap gap-3">
                    {track.year && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {track.year}
                      </Badge>
                    )}
                    {track.genre && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Music className="h-3 w-3" />
                        {track.genre}
                      </Badge>
                    )}
                    {formatDuration(track.duration) && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Info className="h-3 w-3" />
                        {formatDuration(track.duration)}
                      </Badge>
                    )}
                    {track.chartPosition && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <TrendingUp className="h-3 w-3" />
                        #{track.chartPosition} on charts
                      </Badge>
                    )}
                    {track.popularityScore && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Star className="h-3 w-3" />
                        {track.popularityScore}% popular
                      </Badge>
                    )}
                  </div>

                  {/* Trivia Facts */}
                  {(triviaFacts.length > 0 || interestingFacts.story) && (
                    <div className="space-y-3 pt-3 border-t">
                      <h4 className="font-semibold flex items-center gap-2">
                        <Info className="h-4 w-4" />
                        Did you know?
                      </h4>
                      {triviaFacts.slice(0, 2).map((fact: string, idx: number) => (
                        <p key={idx} className="text-sm text-gray-600 dark:text-gray-400">
                          • {fact}
                        </p>
                      ))}
                      {interestingFacts.story && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                          "{interestingFacts.story}"
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Leaderboard */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              Leaderboard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <AnimatePresence>
                {sortedPlayers.map((player, index) => (
                  <motion.div
                    key={player.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`
                      flex items-center justify-between p-3 rounded-lg transition-all
                      ${player.isYou 
                        ? 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 shadow-md' 
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                      }
                    `}
                  >
                    <div className="flex items-center gap-3">
                      {getPositionIcon(player.position!)}
                      <div>
                        <p className="font-medium">
                          {player.name}
                          {player.isYou && (
                            <span className="text-xs text-blue-600 dark:text-blue-400 ml-2">(You)</span>
                          )}
                        </p>
                        {player.scoreChange !== undefined && player.scoreChange > 0 && (
                          <p className="text-xs text-green-600">
                            +{player.scoreChange} this round
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-lg">{player.score}</p>
                      <p className="text-xs text-gray-500">points</p>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {/* Player count */}
            <div className="mt-4 pt-4 border-t flex items-center justify-center text-sm text-gray-500">
              <Users className="h-4 w-4 mr-1" />
              {players.length} players in this game
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Continue Button (optional manual advance) */}
      {onContinue && (
        <div className="flex justify-center">
          <button
            onClick={onContinue}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            Continue to Next Question
          </button>
        </div>
      )}
    </motion.div>
  )
}