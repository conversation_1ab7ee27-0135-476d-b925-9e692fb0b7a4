"use client"

import { useState, use<PERSON><PERSON><PERSON>, use<PERSON>ffect, use<PERSON><PERSON>back } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  Play,
  Pause,
  SkipForward,
  SkipBack,
  Volume2,
  Search,
  ThumbsUp,
  ThumbsDown,
  Plus,
  Music,
  Clock,
  Users,
  TrendingUp,
  Star,
  Shuffle,
  Repeat,
  Settings,
  Crown,
  Disc3,
  UserCheck,
  Shield,
  Home,
  ArrowLeft,
  Square,
  VolumeX,
  X,
  Alert<PERSON>riangle,
  <PERSON>ader2,
  List,
  Heart,
  CheckCircle,
  <PERSON>freshC<PERSON>,
  Filter,
  Trash2,
  User,
  GripVertical,
  ArrowUp,
  ArrowDown,
  Copy,
  Lightbulb
} from "lucide-react"
import Link from "next/link"
import { Progress } from "@/components/ui/progress"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import JukeboxLibraryBrowser from './jukebox-library-browser'
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"
import { LoginForm } from './login-form'
import { RegisterForm } from './register-form'
import { ProfileScreen } from './profile-screen';
import { ThemeSwitcher } from './theme-switcher'
import { AutoSuggestionAdder } from './auto-suggestion-adder'
import { FloatingStreamPlayer } from './floating-stream-player'

import { toast } from 'sonner'
import { AlbumArt } from './album-art'
import PlaylistQueueManager from './playlist-queue-manager'
import { useGlobalAudio } from '@/lib/services/global-audio-service'

// Import role management and types
import type { UserRole, Song, QueuedSong, SongSuggestion, TrackWithMetadata, JukeboxUser } from "@/lib/types"
// Removed: using JWT-only authentication now
import { useUser } from "@/lib/user-context"
import { useJukeboxAuth } from "@/hooks/useJukeboxAuth"
import { useMusicPlayer } from "@/hooks/useMusicPlayer"
import { useMusicLibrary } from "@/hooks/useMusicLibrary"
import { useUserFavorites } from "@/hooks/useUserFavorites"
import { useSuggestions } from "@/hooks/useSuggestions"
import { JukeboxService } from "@/services/jukeboxService"
import { AuthService } from "@/services/authService"
import { NowPlayingCard } from './NowPlayingCard'
import { LibraryTab } from './LibraryTab'
import { SuggestionsTab } from './SuggestionsTab'
import { QueueTab } from './QueueTab'
import { FavoritesTab } from './FavoritesTab'
import LoadingScreen from './LoadingScreen'

// Import performance utilities (cached loading now handled by useMusicLibrary hook)
import { jukeboxLibraryCache, jukeboxCategoriesCache } from "@/lib/jukebox-performance"

// Using centralized performance utilities from jukebox-performance.ts

// Production code - no mock data

function buildSongVoteId(song: Song): string {
  return `song-${song.id || song.title}-${song.artist}`.replace(/\s+/g, '-').toLowerCase()
}

// Define the Jukebox component
interface JukeboxProps {
  initialRole?: UserRole
  debug?: boolean
}

// Removed: using JWT-only authentication now

export default function Jukebox() {
  // Use the user context for database integration - MUST be declared first
  const { user: contextUser, isAuthenticated, refreshUser } = useUser()
  
  // Use extracted authentication hook
  const {
    userProfile,
    showProfileSetup,
    authMode,
    profileLoading,
    showProfileScreen,
    userRole,
    isAdmin,
    hasDirectQueueAccess,
    setShowProfileSetup,
    setAuthMode,
    setShowProfileScreen,
    setUserProfile,
    logout,
    refreshAuth
  } = useJukeboxAuth()
  
  // Use extracted music player hook
  const {
    currentTrack,
    isPlaying,
    volume,
    progress,
    duration,
    queue,
    mpdClient,
    mpdStatus,
    audioManager,
    handlePlayPause,
    handleSkip,
    handleVolumeChange,
    refreshState,
    mpdAddToQueue,
    setQueue,
    setCurrentTrack,
    setIsPlaying,
    setVolume,
    setProgress,
    setDuration
  } = useMusicPlayer()
  
  // Define refreshQueue function before library hook uses it
  const refreshQueue = async () => {
    // Use the refreshState from the music player hook to sync queue and player state
    await refreshState()
  }
  
  // Use extracted music library hook
  const {
    library,
    isLoading,
    libraryLastUpdated,
    availableCategories,
    availableGenres,
    selectedCategory,
    selectedGenre,
    hideChartSongs,
    hideMyItunes,
    searchTerm,
    sortBy,
    hasInitiallyLoaded,
    currentPage,
    hasMorePages,
    isLoadingMore,
    deduplicateEnabled,
    isLibraryBrowserOpen,
    debouncedSearchTerm,
    activeLibrary,
    filteredLibrary,
    duplicateStats,
    setSelectedCategory,
    setSelectedGenre,
    setHideChartSongs,
    setHideMyItunes,
    setSearchTerm,
    setSortBy,
    setDeduplicateEnabled,
    setIsLibraryBrowserOpen,
    loadMorePages,
    manualRefresh,
    refreshLibraryWithCache
  } = useMusicLibrary(mpdClient, mpdStatus, refreshQueue)
  
  // Use extracted user favorites hook
  const {
    userFavorites,
    userFavoritesData,
    favoritesLoading,
    isInitialFavoritesLoading,
    loadUserFavorites,
    handleToggleFavorite,
    isFavorite,
    isFavoriteLoading,
    setFavoriteLoading
  } = useUserFavorites(userProfile)
  
  // Use extracted suggestions hook
  const {
    suggestions,
    suggestionLoading,
    loadSuggestions,
    clearSuggestions,
    handleSuggestSong,
    handleSuggestionVote,
    handleSuggestionApprove,
    handleSuggestionReject,
    isSuggestionLoading
  } = useSuggestions(userProfile, userRole, isAdmin, refreshQueue)
  
  // Use global audio service for smooth transitions
  const globalAudio = useGlobalAudio()
  
  // Remaining states
  const [showMobileControls, setShowMobileControls] = useState(false)

  // Enhanced profile manager
  // Removed: using JWT-only authentication now

  // Add loading state for queue operations
  const [queueOperationLoading, setQueueOperationLoading] = useState<Set<string>>(new Set())

  // Add a function to track loading state per song
  const setQueueLoading = useCallback((songId: string, loading: boolean) => {
    setQueueOperationLoading(prev => {
      const newSet = new Set(prev)
      if (loading) {
        newSet.add(songId)
      } else {
        newSet.delete(songId)
      }
      return newSet
    })
  }, [])

  // Check if a song is currently being added to queue
  const isSongLoading = useCallback((songId: string) => {
    return queueOperationLoading.has(songId)
  }, [queueOperationLoading])
  
  // Combined loading check for any song operation (queue or suggestions)
  const isAnySongLoading = useCallback((songId: string) => {
    return isSongLoading(songId) || isSuggestionLoading(songId)
  }, [isSongLoading, isSuggestionLoading])

  // Note: User favorites state and functions are now provided by useUserFavorites hook


  // Note: handleToggleFavorite is now provided by useUserFavorites hook
  // Note: handleSuggestSong is now provided by useSuggestions hook

  // Note: Loading user favorites when profile is ready is now handled by useUserFavorites hook
  // Note: Loading suggestions when component mounts is now handled by useSuggestions hook

  // Note: Authentication initialization is now handled by useJukeboxAuth hook

  // Note: Library initialization and genre loading are now handled by useMusicLibrary hook

  // Note: refreshState, audio controls, mpdAddToQueue, and manualRefresh are now provided by respective hooks

  // Note: formatDuration moved to NowPlayingCard component

  // Note: Library management functions, effects, and computed values are now provided by useMusicLibrary hook
  

  const handleVote = useCallback(async (songId: number | string, voteType: "up" | "down") => {
    if (!userProfile?.id) {
      toast.error("Please set up your profile to vote");
      return;
    }

    try {
      const data = await JukeboxService.voteSong({
        songId: songId.toString(),
        userId: userProfile.id,
        voteType
      });

      if (data.success) {
        toast.success(`${voteType === 'up' ? 'Upvoted' : 'Downvoted'} track!`);
        // Invalidate cache to refresh data
        jukeboxLibraryCache.clear()
        // TODO: Refresh song data or update local state
      } else {
        toast.error(data.message || 'Failed to vote');
      }
    } catch (error) {
      console.error('Vote error:', error);
      toast.error('Failed to submit vote');
    }
  }, [userProfile]);

  // Note: handleAddSuggestion is replaced by handleSuggestSong from useSuggestions hook

  const handleAddToQueue = useCallback(async (song: Song) => {
    if (!song) {
      toast.error("Song not found")
      return
    }

    // Prevent duplicate submissions
    const songId = song.id?.toString() || song.filePath || `${song.artist}-${song.title}`
    if (isSongLoading(songId)) {
      return // Already processing this song
    }

    // Set loading state
    setQueueLoading(songId, true)

    // Show immediate feedback
    toast.info(`Adding "${song.title}" to queue...`, {
      duration: 2000,
    })

    try {
      // Add to MPD queue via service
      const data = await JukeboxService.addToQueue({
        filePath: song.filePath,
        action: 'add',
        addedBy: userProfile?.displayName || userProfile?.name || "Guest User",
        userId: userProfile?.id || 'guest',
        userRole: userRole || 'guest'
      })

      // Handle specific error cases using status code from service
      if (!data.success && (data as any).statusCode) {
        const statusCode = (data as any).statusCode
        if (statusCode === 429) {
          toast.error(data.message || "Rate limit exceeded. Please wait before adding more songs.", {
            duration: 5000,
          })
        } else if (statusCode === 400 && data.message?.includes('already in queue')) {
          toast.warning(`"${song.title}" is already in the queue`, {
            duration: 3000,
          })
        } else {
          toast.error(data.message || "Failed to add song to queue", {
            duration: 4000,
          })
        }
        return
      }

      if (!data.success) {
        toast.error(data.message || "Failed to add song to queue", {
          duration: 4000,
        })
        return
      }
      
      // Success - refresh queue and show confirmation
      try {
        await refreshQueue()
        toast.success(`✅ Added "${song.title}" to queue`, {
          description: `By ${song.artist}`,
          duration: 3000,
        })
      } catch (refreshError) {
        console.warn('Queue refresh failed after successful add:', refreshError)
        // Still show success since the add operation succeeded
        toast.success(`✅ Added "${song.title}" to queue`, {
          description: `By ${song.artist} (queue will refresh shortly)`,
          duration: 3000,
        })
      }
      
    } catch (error) {
      console.error('Failed to add to queue:', error)
      
      // Provide specific error messages based on error type
      if (error instanceof TypeError && error.message.includes('fetch')) {
        toast.error("Network error - please check your connection", {
          duration: 4000,
        })
      } else if (error instanceof Error && error.message.includes('timeout')) {
        toast.error("Request timed out - please try again", {
          duration: 4000,
        })
      } else {
        toast.error(`Failed to add "${song.title}" to queue`, {
          description: "Please try again or contact support if the issue persists",
          duration: 4000,
        })
      }
    } finally {
      // Always clear loading state
      setQueueLoading(songId, false)
    }
  }, [userProfile, userRole, isSongLoading, setQueueLoading, refreshQueue])




  const handleRemoveFromQueue = async (queuePosition: number) => {
    if (!isAdmin()) {
      toast.error("Only admins can remove songs from queue")
      return
    }

    try {
      await JukeboxService.removeFromQueue(queuePosition)
      
      // Refresh queue to get updated state
      await refreshQueue()
      
      toast.success("Song removed from queue")
    } catch (error) {
      console.error('Failed to remove from queue:', error)
      toast.error("Failed to remove song from queue")
    }
  }

  const handleMoveInQueue = async (from: number, to: number) => {
    if (!isAdmin()) {
      toast.error("Only admins can reorder queue")
      return
    }

    if (from === to) return

    try {
      await JukeboxService.moveInQueue(from, to)
      
      // Refresh queue to get updated state
      await refreshQueue()
      
      toast.success("Queue reordered")
    } catch (error) {
      console.error('Failed to move in queue:', error)
      toast.error("Failed to reorder queue")
    }
  }
  
  const handleRoleChange = (newRole: UserRole) => {
    // Role changes are now handled by the centralized authentication system
    console.log('Role change requests should go through the admin panel')
  };

  // Note: handlePlayPause, handleSkip, and handleVolumeChange are now provided by useMusicPlayer hook

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'superuser': return <Crown className="w-5 h-5 text-yellow-400" />;
      case 'dj': return <Disc3 className="w-5 h-5 text-purple-400" />;
      default: return <User className="w-5 h-5 text-gray-400" />;
    }
  }

  const getRoleDisplayName = (role: UserRole) => {
    switch (role) {
      case 'superuser': return 'Admin';
      case 'dj': return 'DJ';
      default: return 'User';
    }
  }

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case 'superuser': return 'bg-gradient-to-r from-yellow-500 to-amber-600 hover:from-yellow-600 hover:to-amber-700';
      case 'dj': return 'bg-gradient-to-r from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700';
      default: return 'bg-gradient-to-r from-gray-500 to-slate-600 hover:from-gray-600 hover:to-slate-700';
    }
  }

  const handleProfileClick = () => {
    setShowProfileScreen(true)
  }

  const handleBackFromProfile = () => {
    setShowProfileScreen(false)
  }

  // Role updates are now handled by the user context and session validation

  // Add state for role refresh
  const [isRefreshingRole, setIsRefreshingRole] = useState(false)
  const [lastRoleCheck, setLastRoleCheck] = useState<Date | null>(null)

  // Handle role refresh
  const handleRefreshRole = useCallback(async () => {
    if (isRefreshingRole) return
    
    setIsRefreshingRole(true)
    try {
      await refreshUser()
      setLastRoleCheck(new Date())
      toast.success('Role refreshed successfully!', {
        duration: 3000,
      })
    } catch (error) {
      console.error('Role refresh failed:', error)
      toast.error('Failed to refresh role. Please try again.')
    } finally {
      setIsRefreshingRole(false)
    }
  }, [refreshUser, isRefreshingRole])

  // Auto-refresh role every 5 minutes to detect admin changes
  useEffect(() => {
    if (!isAuthenticated) return

    const autoRefreshRole = async () => {
      try {
        await refreshUser()
        setLastRoleCheck(new Date())
      } catch (error) {
        console.log('Auto role refresh failed (silent):', error)
      }
    }

    // Check every 5 minutes
    const interval = setInterval(autoRefreshRole, 5 * 60 * 1000)
    
    return () => clearInterval(interval)
  }, [isAuthenticated, refreshUser])

  // Set audio mode when jukebox component mounts
  useEffect(() => {
    if (globalAudio.isInitialized && globalAudio.mode !== 'jukebox') {
      globalAudio.transitionToMode({ 
        from: globalAudio.mode, 
        to: 'jukebox',
        fadeInDuration: 1.0,
        pauseInsteadOfStop: false
      })
    }
  }, [globalAudio.isInitialized, globalAudio.mode, globalAudio.transitionToMode])
  
  // Listen for auth modal trigger events
  useEffect(() => {
    const handleShowAuthModal = () => {
      setShowProfileSetup(true)
      setAuthMode('register')
    }
    
    window.addEventListener('showAuthModal', handleShowAuthModal)
    return () => window.removeEventListener('showAuthModal', handleShowAuthModal)
  }, [])

  // Render logic
  // Show profile screen if requested
  if (showProfileScreen) {
    return <ProfileScreen onBackToMenu={handleBackFromProfile} />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-100 via-blue-100 to-indigo-100 text-gray-900 dark:from-purple-900 dark:via-blue-900 dark:to-indigo-900 dark:text-white">
      {showProfileSetup && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-900 rounded-lg p-6 max-w-md w-full mx-4">
            <h2 className="text-2xl font-bold mb-4 text-center">
              {authMode === 'login' ? 'Login to ulTimote' : 'Register for ulTimote'}
            </h2>
            {authMode === 'login' ? (
              <LoginForm
                onSuccess={async () => {
                  console.log('[Auth] Login successful, reloading...')
                  // Refresh the page to reinitialize with new token
                  window.location.reload()
                }}
                onCancel={() => {
                  console.log('[Auth] Login cancelled')
                  setShowProfileSetup(false)
                }}
                onSwitchToRegister={() => setAuthMode('register')}
              />
            ) : (
              <RegisterForm
                onSuccess={async () => {
                  console.log('[Auth] Registration successful, reloading...')
                  // Refresh the page to reinitialize with new token
                  window.location.reload()
                }}
                onCancel={() => {
                  console.log('[Auth] Registration cancelled')
                  setShowProfileSetup(false)
                }}
                onSwitchToLogin={() => setAuthMode('login')}
              />
            )}
          </div>
        </div>
      )}
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 gap-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="icon" className="text-white hover:bg-white/10">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            {/* Admin Button - Only visible to admins */}
            {isAdmin() && (
              <Link href="/admin/roles">
                <Button variant="ghost" size="icon" className="text-purple-300 hover:bg-purple-500/20">
                  <Shield className="h-4 w-4 mr-1" />
                  Admin
                </Button>
              </Link>
            )}

            <div>
              <div className="flex items-center space-x-2">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  ulTimote
                </h1>
                <Music className="h-6 w-6 text-purple-400" />
              </div>
              <p className="text-sm text-gray-300 font-medium">Jukebox</p>
            </div>
          </div>
          
          {userProfile && (
            <div className="flex items-center gap-3">
              <ThemeSwitcher />
              <button 
                onClick={handleProfileClick}
                className="flex items-center space-x-3 bg-white/10 rounded-lg px-4 py-2 hover:bg-white/20 transition-all duration-200 cursor-pointer"
              >
                <span className="text-2xl">
                  {userProfile.avatar === 'rockstar' ? '🎸' :
                   userProfile.avatar === 'popstar' ? '⭐' :
                   userProfile.avatar === 'dj' ? '🎧' :
                   userProfile.avatar === 'producer' ? '🎚️' :
                   userProfile.avatar === 'musician' ? '🎼' :
                   userProfile.avatar === 'singer' ? '🎤' :
                   userProfile.avatar === 'rapper' ? '🎵' :
                   userProfile.avatar === 'guitarist' ? '🎸' :
                   userProfile.avatar === 'drummer' ? '🥁' :
                   userProfile.avatar === 'bassist' ? '🎸' :
                   userProfile.avatar === 'pianist' ? '🎹' :
                   userProfile.avatar === 'violinist' ? '🎻' : '🎵'}
                </span>
                <span className="text-gray-300">{contextUser?.displayName || userProfile.name}</span>
                <Badge className={`${getRoleBadgeColor(userRole)} border-0 text-white`}>
                  <span className="flex items-center space-x-1">
                    {getRoleIcon(userRole)}
                    <span className="text-sm font-medium">{getRoleDisplayName(userRole)}</span>
                  </span>
                </Badge>
              </button>
              
              {/* Role Refresh Button - Now Outside Profile Button */}
              <Button 
                onClick={async (e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  try {
                    console.log('🔄 FORCE REFRESH - Current role:', userRole)
                    console.log('🔄 Current user from context:', contextUser)
                    
                    // Import auth service directly
                    const { ClientTokenManager } = await import('@/lib/client-auth')
                    const currentToken = ClientTokenManager.getToken()
                    
                    if (currentToken) {
                      console.log('🔄 Making direct API call...')
                      
                      // Call validation API via service
                      const data = await AuthService.validateToken({ token: currentToken })
                      console.log('🔄 API Response:', data)
                      
                      if (data.success && data.user) {
                        console.log('✅ Database role:', data.user.role)
                        
                        // Update token if refreshed
                        if (data.token) {
                          TokenManager.setToken(data.token)
                          console.log('✅ Token updated')
                        }
                        
                        toast.success(`FORCE REFRESH: Database role is ${data.user.role}!`, { duration: 3000 })
                        
                        // Force complete page reload
                        setTimeout(() => {
                          console.log('🔄 Forcing page reload...')
                          window.location.reload()
                        }, 1500)
                      } else {
                        console.error('❌ API Error:', data.message)
                        toast.error(`Refresh failed: ${data.message}`)
                      }
                    } else {
                      console.error('❌ No token found - redirecting to login')
                      toast.error('Please login to continue')
                      // Show login instead of error
                      window.location.href = '/login'
                    }
                  } catch (error) {
                    console.error('❌ Refresh error:', error)
                    toast.error('Refresh failed - check console')
                  }
                }} 
                variant="ghost" 
                size="icon"
                className="text-white/70 hover:text-white hover:bg-white/10"
                title="FORCE refresh role from database"
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          )}

        </div>

        {/* Main Content - Player and Library */}
        {profileLoading ? (
          <LoadingScreen
            messages={[
              "Tuning the jukebox...",
              "Loading your music collection...",
              "Connecting to the beat...",
              "Organizing your playlists...",
              "Preparing the perfect sound...",
              "Warming up the speakers...",
              "Syncing with the rhythm...",
              "Calibrating audio levels...",
              "Loading your favorites...",
              "Setting up the dance floor..."
            ]}
            subtitles={[
              "Almost ready to rock!",
              "Just a moment more...",
              "Getting everything in tune...",
              "Preparing your musical journey...",
              "Loading the best beats...",
              "Setting the perfect mood...",
              "Getting the party started...",
              "Mixing the perfect sound...",
              "Just a few more seconds...",
              "Ready to make some music!"
            ]}
            primaryColor="#8b5cf6"
            backgroundColor="#1a1a2e"
            textColor="#ffffff"
            messageInterval={2500}
            progressUpdateInterval={150}
            onComplete={() => {
              // Loading completed, this will hide the loading screen
            }}
            logo={
              <div className="w-20 h-20 mx-auto rounded-full flex items-center justify-center animate-pulse-slow bg-gradient-to-br from-purple-500 to-pink-500">
                <Music className="w-10 h-10 text-white" />
              </div>
            }
          />
        ) : userProfile && !showProfileSetup ? (
          <main className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-4">
            {/* Left Column: Player and Queue */}
            <div className="lg:col-span-1 flex flex-col gap-6">
              {/* Now Playing Card */}
              <NowPlayingCard
                currentTrack={currentTrack}
                isPlaying={isPlaying}
                volume={volume}
                progress={progress}
                duration={duration}
                onPlayPause={handlePlayPause}
                onSkip={handleSkip}
                onVolumeChange={handleVolumeChange}
                onToggleFavorite={handleToggleFavorite}
                isFavorite={isFavorite}
                isFavoriteLoading={isFavoriteLoading}
              />
            </div>

            {/* Right Column: Library and Suggestions */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="library" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="library" className="text-sm">Library</TabsTrigger>
                  <TabsTrigger value="favorites" className="text-sm flex items-center gap-1">
                    <Heart className="w-3 h-3" />
                    <span className="hidden sm:inline">Favorites</span>
                    <span className="sm:hidden">Fav</span>
                  </TabsTrigger>
                  <TabsTrigger value="suggestions" className="text-sm flex items-center gap-1">
                    <span className="hidden sm:inline">Suggestions</span>
                    <span className="sm:hidden">Suggest</span>
                    <Badge className="text-xs h-5 min-w-[20px] px-1">{suggestions.length}</Badge>
                  </TabsTrigger>
                  <TabsTrigger value="queue" className="text-sm flex items-center gap-1">
                    <span>Queue</span>
                    <Badge className="text-xs h-5 min-w-[20px] px-1">{queue.length}</Badge>
                  </TabsTrigger>
                </TabsList>

                {/* Library Tab */}
                <TabsContent value="library">
                  <LibraryTab
                    filteredLibrary={filteredLibrary}
                    availableCategories={availableCategories}
                    availableGenres={availableGenres}
                    isLoading={isLoading}
                    searchTerm={searchTerm}
                    sortBy={sortBy}
                    selectedCategory={selectedCategory}
                    selectedGenre={selectedGenre}
                    showMobileControls={showMobileControls}
                    hasMorePages={hasMorePages}
                    isLoadingMore={isLoadingMore}
                    userRole={userRole}
                    userProfile={userProfile}
                    setSearchTerm={setSearchTerm}
                    setSortBy={setSortBy}
                    setSelectedCategory={setSelectedCategory}
                    setSelectedGenre={setSelectedGenre}
                    setShowMobileControls={setShowMobileControls}
                    manualRefresh={manualRefresh}
                    refreshQueue={refreshQueue}
                    handleVote={handleVote}
                    handleSuggestSong={handleSuggestSong}
                    handleAddToQueue={handleAddToQueue}
                    handleToggleFavorite={handleToggleFavorite}
                    isSongLoading={isAnySongLoading}
                    isFavorite={isFavorite}
                    isFavoriteLoading={isFavoriteLoading}
                    hasDirectQueueAccess={hasDirectQueueAccess}
                    loadMorePages={loadMorePages}
                  />
                </TabsContent>

                {/* Favorites Tab */}
                <TabsContent value="favorites">
                  <FavoritesTab
                    userRole={userRole}
                    userProfile={userProfile}
                    isLoading={isInitialFavoritesLoading}
                    handleSuggestSong={handleSuggestSong}
                    handleAddToQueue={handleAddToQueue}
                    handleToggleFavorite={handleToggleFavorite}
                    isSongLoading={isAnySongLoading}
                    isFavoriteLoading={isFavoriteLoading}
                    hasDirectQueueAccess={hasDirectQueueAccess}
                    userFavoritesData={userFavoritesData}
                    onRefresh={loadUserFavorites}
                  />
                </TabsContent>

                {/* Suggestions Tab */}
                <TabsContent value="suggestions">
                  <SuggestionsTab
                    suggestions={suggestions}
                    isAdmin={isAdmin}
                    handleSuggestionVote={handleSuggestionVote}
                    handleSuggestionApprove={handleSuggestionApprove}
                    handleSuggestionReject={handleSuggestionReject}
                    onRefresh={loadSuggestions}
                    isRefreshing={false}
                  />
                </TabsContent>

                <TabsContent value="queue">
                  <QueueTab
                    queue={queue}
                    isAdmin={isAdmin}
                    handleRemoveFromQueue={handleRemoveFromQueue}
                    handleMoveInQueue={handleMoveInQueue}
                    onRefresh={refreshQueue}
                    isRefreshing={false}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </main>
        ) : null}

        {/* Library Browser (for when opened) */}
        <JukeboxLibraryBrowser
          library={filteredLibrary}
          onAddToQueue={handleAddToQueue}
          isOpen={isLibraryBrowserOpen}
          onOpenChange={setIsLibraryBrowserOpen}
          userRole={userRole}
        />
      </div>
      
      {/* Auto Suggestion Adder - adds top voted suggestion when queue is empty */}
      <AutoSuggestionAdder
        currentQueueLength={queue.length}
        suggestions={suggestions}
        isEnabled={true}
        onAutoAdd={(suggestion) => {
          // Refresh queue and suggestions after auto-add
          refreshQueue()
          loadSuggestions()
        }}
      />
      
      {/* Floating Stream Player */}
      <FloatingStreamPlayer />
    </div>
  )
} 
