'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>ertCircle, RefreshCw, WifiOff } from 'lucide-react'

interface MobileErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  isNetworkError: boolean
}

export class MobileErrorBoundary extends React.Component<
  { children: React.ReactNode },
  MobileErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      isNetworkError: false 
    }
  }

  static getDerivedStateFromError(error: Error): Partial<MobileErrorBoundaryState> {
    // Check if it's a network-related error
    const isNetworkError = 
      error.message.includes('fetch') ||
      error.message.includes('network') ||
      error.message.includes('Network') ||
      error.message.includes('Failed to fetch') ||
      error.message.includes('socket')

    return { hasError: true, error, isNetworkError }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log error details for debugging
    console.error('MobileErrorBoundary caught:', error, errorInfo)
    
    // Don't log "Script error" as they're usually cross-origin issues
    if (error.message !== 'Script error.') {
      this.setState({ errorInfo })
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 space-y-4">
            <div className="flex items-center gap-3 text-red-600 dark:text-red-400">
              {this.state.isNetworkError ? (
                <WifiOff className="h-8 w-8" />
              ) : (
                <AlertCircle className="h-8 w-8" />
              )}
              <h2 className="text-xl font-semibold">
                {this.state.isNetworkError ? 'Connection Error' : 'Something went wrong'}
              </h2>
            </div>
            
            <p className="text-gray-600 dark:text-gray-300">
              {this.state.isNetworkError
                ? 'Please check your internet connection and try again.'
                : 'An unexpected error occurred. Please refresh the page to continue.'}
            </p>

            <div className="flex gap-3">
              <Button
                onClick={() => window.location.reload()}
                className="flex-1 touch-manipulation"
                variant="default"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Page
              </Button>
              
              {this.state.isNetworkError && (
                <Button
                  onClick={() => this.setState({ hasError: false, error: null, errorInfo: null })}
                  className="flex-1 touch-manipulation"
                  variant="outline"
                >
                  Try Again
                </Button>
              )}
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4">
                <summary className="text-sm text-gray-500 cursor-pointer">
                  Error details
                </summary>
                <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto">
                  {this.state.error.toString()}
                  {this.state.errorInfo && this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}