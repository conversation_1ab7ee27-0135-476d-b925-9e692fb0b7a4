"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  ThumbsUp,
  Plus,
  RefreshCw,
  Loader2,
  Heart,
  Filter,
  Send,
  Music,
  ArrowLeft,
} from "lucide-react"
import { AlbumArt } from './album-art'
import PlaylistQueueManager from './playlist-queue-manager'
import { TextOverflowTooltip } from './ui/text-overflow-tooltip'
import { FillPlaylistButton } from './fill-playlist-button'
import type { Song, UserRole } from "@/lib/types"

interface LibraryTabProps {
  // Data
  filteredLibrary: Song[]
  availableCategories: string[]
  availableGenres: string[]
  
  // State
  isLoading: boolean
  searchTerm: string
  sortBy: "votes" | "title" | "artist" | "year"
  selectedCategory: string
  selectedGenre: string
  showMobileControls: boolean
  hasMorePages: boolean
  isLoadingMore: boolean
  
  // User state
  userRole: UserRole
  userProfile: { id?: string } | null
  
  // Actions
  setSearchTerm: (term: string) => void
  setSortBy: (sort: "votes" | "title" | "artist" | "year") => void
  setSelectedCategory: (category: string) => void
  setSelectedGenre: (genre: string) => void
  setShowMobileControls: (show: boolean) => void
  manualRefresh: () => Promise<void>
  refreshQueue: () => Promise<void>
  
  // Song actions
  handleVote: (songId: number | string, voteType: "up" | "down") => Promise<void>
  handleSuggestSong: (song: Song) => Promise<void>
  handleAddToQueue?: (song: Song) => Promise<void>
  handleToggleFavorite: (song: Song) => Promise<void>
  
  // Loading state functions
  isSongLoading: (songId: string) => boolean
  isFavorite: (song: Song) => boolean
  isFavoriteLoading: (songId: string) => boolean
  
  // Permission functions
  hasDirectQueueAccess: () => boolean
  
  // Infinite scroll function
  loadMorePages: () => Promise<void>
}

export function LibraryTab({
  filteredLibrary,
  availableCategories,
  availableGenres,
  isLoading,
  searchTerm,
  sortBy,
  selectedCategory,
  selectedGenre,
  showMobileControls,
  hasMorePages,
  isLoadingMore,
  userRole,
  userProfile,
  setSearchTerm,
  setSortBy,
  setSelectedCategory,
  setSelectedGenre,
  setShowMobileControls,
  manualRefresh,
  refreshQueue,
  handleVote,
  handleSuggestSong,
  handleAddToQueue,
  handleToggleFavorite,
  isSongLoading,
  isFavorite,
  isFavoriteLoading,
  hasDirectQueueAccess,
  loadMorePages
}: LibraryTabProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  // Infinite scroll handler
  const handleScroll = useCallback(() => {
    if (!scrollAreaRef.current || !hasMorePages || isLoadingMore) return
    
    const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement
    if (!scrollElement) return
    
    const { scrollTop, scrollHeight, clientHeight } = scrollElement
    const threshold = 200 // Load more when 200px from bottom
    
    // Check if we're near the bottom
    const isNearBottom = scrollHeight - scrollTop - clientHeight < threshold
    
    if (isNearBottom) {
      console.log('Infinite scroll triggered - loading more pages...', {
        scrollTop,
        scrollHeight,
        clientHeight,
        remaining: scrollHeight - scrollTop - clientHeight
      })
      loadMorePages()
    }
  }, [hasMorePages, isLoadingMore, loadMorePages])

  // Attach scroll listener to the ScrollArea viewport
  useEffect(() => {
    if (!scrollAreaRef.current) return
    
    const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement
    if (!scrollElement) return
    
    scrollElement.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      scrollElement.removeEventListener('scroll', handleScroll)
    }
  }, [handleScroll])
  return (
    <Card className="bg-white/10 backdrop-blur-md border-white/20">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Music Library</CardTitle>
            <CardDescription className="hidden sm:block">Browse, vote, and queue up songs.</CardDescription>
          </div>
          <div className="flex items-center gap-1 sm:gap-2">
            <Button 
              onClick={() => setShowMobileControls(!showMobileControls)} 
              variant="outline" 
              size="sm"
              className="sm:hidden text-xs"
            >
              <Filter className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
              {showMobileControls ? 'Hide' : 'Filter'}
            </Button>
            {/* Playlist Manager for Admins/DJs */}
            {hasDirectQueueAccess() && (
              <PlaylistQueueManager
                userRole={userRole}
                userId={userProfile?.id}
                onPlaylistAdded={refreshQueue}
              />
            )}
            <Button onClick={manualRefresh} variant="ghost" size="icon" disabled={isLoading} className="h-8 w-8 sm:h-9 sm:w-9">
              <RefreshCw className={`w-3 h-3 sm:w-4 sm:h-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        
        {/* Mobile Controls - Collapsible */}
        <div className={`sm:hidden transition-all duration-200 overflow-hidden ${showMobileControls ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
          <div className="flex flex-col gap-3 pt-4">
            <Input 
              placeholder="Search library..." 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
            <div className="grid grid-cols-2 gap-2">
              <Select value={sortBy} onValueChange={(v) => setSortBy(v as any)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="votes">Top Voted</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="artist">Artist</SelectItem>
                  <SelectItem value="year">Year</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {availableCategories.map(cat => (
                    <SelectItem key={cat} value={cat}>{cat.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-1 gap-2 mt-2">
              <Select value={selectedGenre} onValueChange={setSelectedGenre}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filter by genre" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Genres</SelectItem>
                  {availableGenres.map(genre => (
                    <SelectItem key={genre} value={genre}>{genre}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <FillPlaylistButton 
              selectedCategory={selectedCategory}
              refreshQueue={refreshQueue}
              className="w-full"
            />
          </div>
        </div>
        
        {/* Desktop Controls - Always Visible */}
        <div className="hidden sm:flex sm:flex-row gap-3 pt-4 items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
            <Input 
              placeholder="Search songs, artists, albums..." 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 bg-white/10 border-white/20 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:bg-white/20 focus:border-white/30"
            />
          </div>
          <Select value={sortBy} onValueChange={(v) => setSortBy(v as any)}>
            <SelectTrigger className="w-[140px] bg-white/10 border-white/20 text-gray-900 dark:text-white hover:bg-white/20">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="votes">Most Voted</SelectItem>
              <SelectItem value="title">Song Title</SelectItem>
              <SelectItem value="artist">Artist</SelectItem>
              <SelectItem value="year">Year</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
             <SelectTrigger className="w-[140px] bg-white/10 border-white/20 text-gray-900 dark:text-white hover:bg-white/20">
                 <SelectValue placeholder="All Categories" />
             </SelectTrigger>
             <SelectContent>
                 <SelectItem value="all">All Categories</SelectItem>
                 {availableCategories.map(cat => (
                     <SelectItem key={cat} value={cat}>{cat.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</SelectItem>
                 ))}
             </SelectContent>
         </Select>
         <Select value={selectedGenre} onValueChange={setSelectedGenre}>
           <SelectTrigger className="w-[140px] bg-white/10 border-white/20 text-gray-900 dark:text-white hover:bg-white/20">
             <SelectValue placeholder="All Genres" />
           </SelectTrigger>
           <SelectContent>
             <SelectItem value="all">All Genres</SelectItem>
             {availableGenres.map(genre => (
               <SelectItem key={genre} value={genre}>{genre}</SelectItem>
             ))}
           </SelectContent>
         </Select>
         <FillPlaylistButton 
           selectedCategory={selectedCategory}
           refreshQueue={refreshQueue}
         />
        </div>
      </CardHeader>
      <CardContent className="px-2 sm:px-6">
        <ScrollArea ref={scrollAreaRef} className="h-[65vh] sm:h-[60vh]">
          {isLoading && filteredLibrary.length === 0 ? (
             <div className="flex justify-center items-center h-full">
               <div className="text-center space-y-2">
                 <Loader2 className="w-8 h-8 animate-spin text-gray-400 mx-auto" />
                 <p className="text-sm text-gray-400">Loading music library...</p>
               </div>
             </div>
          ) : filteredLibrary.length === 0 ? (
             <div className="flex justify-center items-center h-full">
               <div className="text-center space-y-4">
                 <Music className="w-16 h-16 text-gray-400 mx-auto" />
                 <div>
                   <h3 className="text-lg font-semibold text-gray-300">No music found</h3>
                   <p className="text-sm text-gray-400">
                     {searchTerm ? 'Try a different search term' : 'Add some music to your library to get started'}
                   </p>
                 </div>
                 <Button onClick={manualRefresh} variant="ghost" size="sm">
                   <RefreshCw className="w-4 h-4 mr-2" />
                   Refresh Library
                 </Button>
               </div>
             </div>
          ) : (
            <div className="space-y-2 mr-2">
              {filteredLibrary.map((song) => (
                <Card key={song.id} className="p-3 bg-black/10">
                  {/* Mobile Layout - Stacked */}
                  <div className="sm:hidden">
                    {/* Song Info */}
                    <div className="flex items-center gap-2 mb-2">
                      <AlbumArt
                        trackId={song.id?.toString()}
                        fallbackUrl={song.albumArtUrl}
                        title={song.title}
                        artist={song.artist || 'Unknown Artist'}
                        album={song.album || 'Unknown Album'}
                        size="sm"
                        className="flex-shrink-0"
                      />
                      <div className="min-w-0 flex-1">
                        <TextOverflowTooltip text={song.title} className="font-semibold text-sm leading-tight">
                          <h4 className="font-semibold text-sm leading-tight line-clamp-1">
                            {song.title}
                          </h4>
                        </TextOverflowTooltip>
                        <TextOverflowTooltip text={song.artist || 'Unknown Artist'} className="text-xs text-gray-400">
                          <p className="text-xs text-gray-400 line-clamp-1">{song.artist}</p>
                        </TextOverflowTooltip>
                      </div>
                    </div>
                    
                    {/* Action buttons */}
                    <div className="flex items-center justify-between gap-1">
                      <Button 
                        size="sm" 
                        variant={isFavorite(song) ? "default" : "ghost"}
                        onClick={() => handleToggleFavorite(song)}
                        disabled={isFavoriteLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                        className="h-8 px-2.5 min-w-[60px]"
                        title={isFavorite(song) ? "Remove from favorites" : "Add to favorites"}
                      >
                        {isFavoriteLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                          <Loader2 className="w-3.5 h-3.5 animate-spin"/> 
                        ) : (
                          <>
                            <Heart className={`w-3.5 h-3.5 ${isFavorite(song) ? 'fill-current' : ''}`}/>
                            <span className="ml-1 text-xs">{isFavorite(song) ? 'Fav' : 'Fav'}</span>
                          </>
                        )}
                      </Button>
                      
                      {/* Suggest/Queue buttons based on role */}
                      {hasDirectQueueAccess() ? (
                        <>
                          <Button 
                            size="sm" 
                            className="h-8 px-2.5" 
                            onClick={() => handleSuggestSong(song)}
                            disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                            title="Suggest this song"
                          >
                            {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                              <Loader2 className="w-3.5 h-3.5 animate-spin"/> 
                            ) : (
                              <>
                                <Send className="w-3.5 h-3.5"/> 
                                <span className="ml-1 text-xs">Suggest</span>
                              </>
                            )}
                          </Button>
                          <Button 
                            size="sm" 
                            variant="secondary"
                            className="h-8 px-2.5" 
                            onClick={() => handleAddToQueue?.(song)}
                            disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                            title="Add directly to queue"
                          >
                            {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                              <Loader2 className="w-3.5 h-3.5 animate-spin"/> 
                            ) : (
                              <>
                                <Plus className="w-3.5 h-3.5"/> 
                                <span className="ml-1 text-xs">Queue</span>
                              </>
                            )}
                          </Button>
                        </>
                      ) : (
                        <Button 
                          size="sm" 
                          className="h-8 px-2.5" 
                          onClick={() => handleSuggestSong(song)}
                          disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                          title="Suggest this song"
                        >
                          {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                            <Loader2 className="w-3.5 h-3.5 animate-spin"/> 
                          ) : (
                            <>
                              <Send className="w-3.5 h-3.5"/> 
                              <span className="ml-1 text-xs">Suggest</span>
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  {/* Desktop Layout - Side by side */}
                  <div className="hidden sm:flex sm:items-center sm:justify-between">
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                      <AlbumArt
                        trackId={song.id?.toString()}
                        fallbackUrl={song.albumArtUrl}
                        title={song.title}
                        artist={song.artist || 'Unknown Artist'}
                        album={song.album || 'Unknown Album'}
                        size="sm"
                        className="flex-shrink-0"
                      />
                      <div className="min-w-0 flex-1">
                        <TextOverflowTooltip text={song.title} className="font-semibold">
                          <h4 className="font-semibold line-clamp-1">
                            {song.title}
                          </h4>
                        </TextOverflowTooltip>
                        <TextOverflowTooltip text={song.artist || 'Unknown Artist'} className="text-sm text-gray-400">
                          <p className="text-sm text-gray-400 line-clamp-1">{song.artist}</p>
                        </TextOverflowTooltip>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <Button 
                        size="sm" 
                        variant={isFavorite(song) ? "default" : "ghost"}
                        onClick={() => handleToggleFavorite(song)}
                        disabled={isFavoriteLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                        title={isFavorite(song) ? "Remove from favorites" : "Add to favorites"}
                      >
                        {isFavoriteLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                          <Loader2 className="w-4 h-4 animate-spin"/> 
                        ) : (
                          <Heart className={`w-4 h-4 ${isFavorite(song) ? 'fill-current' : ''}`}/>
                        )}
                      </Button>
                      
                      {/* Suggest/Queue buttons based on role */}
                      {hasDirectQueueAccess() ? (
                        <>
                          <Button 
                            size="sm" 
                            onClick={() => handleSuggestSong(song)}
                            disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                          >
                            {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                              <Loader2 className="w-4 h-4 mr-1 animate-spin"/> 
                            ) : (
                              <Send className="w-4 h-4 mr-1"/> 
                            )}
                            Suggest
                          </Button>
                          <Button 
                            size="sm" 
                            variant="secondary"
                            onClick={() => handleAddToQueue?.(song)}
                            disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                          >
                            {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                              <Loader2 className="w-4 h-4 mr-1 animate-spin"/> 
                            ) : (
                              <Plus className="w-4 h-4 mr-1"/> 
                            )}
                            Queue
                          </Button>
                        </>
                      ) : (
                        <Button 
                          size="sm" 
                          onClick={() => handleSuggestSong(song)}
                          disabled={isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`)}
                        >
                          {isSongLoading(song.id?.toString() || song.filePath || `${song.artist}-${song.title}`) ? (
                            <Loader2 className="w-4 h-4 mr-1 animate-spin"/> 
                          ) : (
                            <Send className="w-4 h-4 mr-1"/> 
                          )}
                          Suggest
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
              
              {/* Infinite scroll loading indicator */}
              {isLoadingMore && (
                <div className="flex justify-center items-center py-4">
                  <div className="text-center space-y-2">
                    <Loader2 className="w-6 h-6 animate-spin text-gray-400 mx-auto" />
                    <p className="text-sm text-gray-400">Loading more songs...</p>
                  </div>
                </div>
              )}
              
              {/* End of results indicator */}
              {!hasMorePages && filteredLibrary.length > 0 && !isLoading && (
                <div className="text-center py-4 text-gray-500 text-sm">
                  No more songs to load
                </div>
              )}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}