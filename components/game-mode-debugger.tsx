"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, Music, Brain } from "lucide-react"
import type { GameMode } from "./multiplayer-lobby"

interface GameModeDebuggerProps {
  gameMode: GameMode
  questionsPerGame: number
  timePerQuestion: number
  className?: string
}

export function GameModeDebugger({ gameMode, questionsPerGame, timePerQuestion, className }: GameModeDebuggerProps) {
  const getGameModeInfo = () => {
    const modes: Record<GameMode, { name: string; description: string; questionType: string; category: "music" | "mixed" }> = {
      classic: {
        name: "Classic",
        description: "Identify the artist of the song",
        questionType: "Multiple choice - Select the correct artist",
        category: "music"
      },
      chart_position: {
        name: "Chart Position",
        description: "Guess the highest chart position",
        questionType: "Multiple choice - Select the chart position",
        category: "music"
      },
      guess_the_year: {
        name: "Guess the Year",
        description: "When was the song released?",
        questionType: "Multiple choice - Select the release year",
        category: "music"
      },
      genre_specialist: {
        name: "Genre Specialist",
        description: "Identify the genre of the song",
        questionType: "Multiple choice - Select the genre",
        category: "music"
      },
      decade_challenge: {
        name: "Decade Challenge",
        description: "Which decade is the song from?",
        questionType: "Multiple choice - Select the decade",
        category: "music"
      },
      audio_manipulation: {
        name: "Audio Effects",
        description: "Identify the artist with audio effects applied",
        questionType: "Multiple choice - Select the artist (with audio effects)",
        category: "music"
      },
      ultimote: {
        name: "ulTimote",
        description: "Ultimate customizable quiz with mixed categories",
        questionType: "Various - Depends on selected categories",
        category: "mixed"
      }
    }
    
    return modes[gameMode] || { name: "Unknown", description: "", questionType: "", category: "music" }
  }

  const info = getGameModeInfo()
  const totalTime = questionsPerGame * timePerQuestion

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-sm flex items-center gap-2">
          <AlertCircle className="h-4 w-4" />
          Game Mode Debug Info
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 text-sm">
        {/* Mode Info */}
        <div>
          <p className="font-medium mb-2">Selected Mode:</p>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {info.category === "music" ? (
                <Music className="h-4 w-4 text-blue-500" />
              ) : (
                <Brain className="h-4 w-4 text-purple-500" />
              )}
              <span className="font-semibold">{info.name}</span>
              <Badge variant={info.category === "music" ? "default" : "secondary"}>
                {info.category}
              </Badge>
            </div>
            <p className="text-gray-600 dark:text-gray-400">{info.description}</p>
            <p className="text-xs text-gray-500">Question Type: {info.questionType}</p>
          </div>
        </div>

        {/* Game Settings */}
        <div className="border-t pt-4">
          <p className="font-medium mb-2">Game Settings:</p>
          <div className="space-y-1">
            <p>• Questions: {questionsPerGame}</p>
            <p>• Time per question: {timePerQuestion}s</p>
            <p>• Total game time: ~{Math.ceil(totalTime / 60)} minutes</p>
            <p>• Points per question: Variable (based on speed)</p>
          </div>
        </div>

        {/* Expected Behavior */}
        <div className="border-t pt-4">
          <p className="font-medium mb-2">Expected Behavior:</p>
          <div className="space-y-1 text-xs">
            {gameMode === "classic" && (
              <>
                <p>• Audio will play automatically</p>
                <p>• 4 artist options will be shown</p>
                <p>• Faster answers = more points</p>
              </>
            )}
            {gameMode === "chart_position" && (
              <>
                <p>• Audio will play automatically</p>
                <p>• 4 chart position options</p>
                <p>• Based on actual chart data</p>
              </>
            )}
            {gameMode === "audio_manipulation" && (
              <>
                <p>• Audio will have effects applied</p>
                <p>• Effects: speed changes, reverb, etc.</p>
                <p>• Same scoring as classic mode</p>
              </>
            )}
            {gameMode === "ultimote" && (
              <>
                <p>• Mixed question types</p>
                <p>• Configure in game settings</p>
                <p>• Can include general knowledge</p>
              </>
            )}
          </div>
        </div>

        {/* API Details */}
        <details className="border-t pt-4">
          <summary className="cursor-pointer font-medium text-xs">API Request Details</summary>
          <pre className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto">
{JSON.stringify({
  gameMode: gameMode,
  settings: {
    totalQuestions: questionsPerGame,
    timePerQuestion: timePerQuestion
  }
}, null, 2)}
          </pre>
        </details>
      </CardContent>
    </Card>
  )
}