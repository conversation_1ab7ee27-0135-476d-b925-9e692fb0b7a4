'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Radio, X } from 'lucide-react';
import { JukeboxStreamPlayer } from './jukebox-stream-player';

export const FloatingStreamPlayer = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Floating button */}
      <div className="fixed bottom-6 right-6 z-40">
        {!isOpen && (
          <Button
            onClick={() => setIsOpen(true)}
            size="lg"
            className="rounded-full shadow-lg hover:shadow-xl transition-shadow bg-purple-600 hover:bg-purple-700"
          >
            <Radio className="w-5 h-5 mr-2" />
            Stream
          </Button>
        )}
      </div>

      {/* Stream player panel */}
      {isOpen && (
        <div className="fixed bottom-6 right-6 z-50 w-96 max-w-[calc(100vw-3rem)]">
          <div className="relative">
            <Button
              onClick={() => setIsOpen(false)}
              size="sm"
              variant="ghost"
              className="absolute -top-2 -right-2 rounded-full bg-gray-800 hover:bg-gray-700 text-white z-10"
            >
              <X className="w-4 h-4" />
            </Button>
            <JukeboxStreamPlayer />
          </div>
        </div>
      )}
    </>
  );
};