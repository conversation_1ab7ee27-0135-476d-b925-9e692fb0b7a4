/**
 * ulTimote Game Handler - Manages configuration and gameplay flow
 * Supports both single-player and multiplayer modes
 */

"use client"

import { useState, useEffect } from "react"
import { UlTimoteGameConfig } from "./ultimote-game-config"
import { UlTimoteGame } from "./ultimote-game"
// import { UlTimoteMultiplayerGame } from "./ultimote-game-multiplayer" // Removed - using unified multiplayer
import type { UlTimoteGameConfig as UlTimoteConfig } from "./ultimote-game-config"
import { useUser } from "@/lib/user-context"
import { toast } from "sonner"

interface UlTimoteGameHandlerProps {
  onComplete: (results: any) => void
  onBackToMenu: () => void
  isMultiplayer?: boolean
}

export function UlTimoteGameHandler({ onComplete, onBackToMenu, isMultiplayer = false }: UlTimoteGameHandlerProps) {
  const { user } = useUser()
  const [gameState, setGameState] = useState<'config' | 'playing'>('config')
  const [gameConfig, setGameConfig] = useState<UlTimoteConfig | null>(null)
  const [multiplayerInfo, setMultiplayerInfo] = useState<{
    gameId: string
    gamePin: string
    playerId: string
    playerName: string
    isHost: boolean
  } | null>(null)

  // Initialize multiplayer info from URL/localStorage
  useEffect(() => {
    if (isMultiplayer) {
      const urlParams = new URLSearchParams(window.location.search)
      const gameCode = urlParams.get('gameCode') || localStorage.getItem('currentGameCode') || ''
      const playerId = user?.id || urlParams.get('playerId') || localStorage.getItem('currentPlayerId') || `player-${Date.now()}`
      const playerName = user?.name || urlParams.get('playerName') || localStorage.getItem('currentPlayerName') || 'Player'
      const role = urlParams.get('role') || localStorage.getItem('currentMultiplayerRole') || 'player'
      
      setMultiplayerInfo({
        gameId: gameCode,
        gamePin: gameCode,
        playerId,
        playerName,
        isHost: role === 'host'
      })
      
      // In multiplayer, skip config if not host
      if (role !== 'host') {
        setGameState('playing')
      }
    }
  }, [isMultiplayer, user])

  const handleStartGame = (config: UlTimoteConfig) => {
    setGameConfig(config)
    setGameState('playing')
    toast.success(`Starting ${config.gameName}!`)
  }

  const handleGameComplete = (results: any) => {
    // Convert ulTimote results to standard format
    const standardResults = {
      score: results.totalScore,
      totalQuestions: results.totalQuestions,
      correctAnswers: results.correctAnswers,
      maxStreak: results.maxStreak,
      gameMode: results.gameMode,
      gameName: results.gameName,
      duration: results.duration,
      roundResults: results.roundResults,
      categoryStats: results.categoryStats,
      achievements: results.achievements,
      answers: [], // ulTimote uses round-based results instead
      audioSystemStats: {
        connectionStatus: true,
        tracksPlayed: results.totalQuestions,
        audioErrors: 0
      }
    }
    onComplete(standardResults)
  }

  const handleBackFromGame = () => {
    setGameState('config')
    setGameConfig(null)
  }

  // Multiplayer mode
  if (isMultiplayer && multiplayerInfo && gameState === 'playing') {
    return (
      <div className="text-center p-8">UlTimote multiplayer temporarily disabled</div>
    )
  }

  // Single player mode - config screen
  if (gameState === 'config' && (!isMultiplayer || (isMultiplayer && multiplayerInfo?.isHost))) {
    return (
      <UlTimoteGameConfig
        onStartGame={handleStartGame}
        onBackToMenu={onBackToMenu}
      />
    )
  }

  // Single player mode - playing
  if (gameState === 'playing' && gameConfig && !isMultiplayer) {
    return (
      <UlTimoteGame
        config={gameConfig}
        onComplete={handleGameComplete}
        onBackToMenu={handleBackFromGame}
      />
    )
  }

  // Loading state for multiplayer
  if (isMultiplayer && !multiplayerInfo) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 p-4 
                      flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 
                        border-yellow-500/30 border-t-yellow-500 mx-auto mb-4"></div>
          <p className="text-lg text-gray-400">Connecting to multiplayer...</p>
        </div>
      </div>
    )
  }

  return null
}