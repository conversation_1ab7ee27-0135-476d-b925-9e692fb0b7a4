"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Music, TrendingUp, Headphones, Calendar, Radio, ImageIcon, Zap, Settings, Search, Clock } from "lucide-react"
import { motion } from "framer-motion"
import { GameMode } from "@/lib/types"
import Image from "next/image"

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.07,
      delayChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { y: 15, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring" as const,
      stiffness: 120,
      damping: 14,
    },
  },
}

interface GameModeSelectorProps {
  onSelectMode: (mode: GameMode) => void
}

interface GameModeConfig {
  id: GameMode
  title: string
  description: string
  icon: any
  customImage?: string
  difficulty: string
  duration: string
  iconColor: string
}

const gameModes: GameModeConfig[] = [
  {
    id: "ultimote" as GameMode,
    title: "ulTimote Game",
    description: "Ultimate customizable quiz experience with mixed categories.",
    icon: Settings,
    difficulty: "Ultimate",
    duration: "15-45 min",
    iconColor: "text-yellow-400",
  },
  {
    id: "classic" as GameMode,
    title: "Classic Quiz",
    description: "Artists, songs, and release years.",
    icon: Music,
    difficulty: "Easy",
    duration: "5-10 min",
    iconColor: "text-blue-400",
  },
  {
    id: "chart-position" as GameMode,
    title: "Chart Challenge",
    description: "Guess peak chart positions.",
    icon: TrendingUp,
    difficulty: "Medium",
    duration: "8-12 min",
    iconColor: "text-green-400",
  },
  {
    id: "audio-manipulation" as GameMode,
    title: "Audio Tricks",
    description: "Altered audio identification.",
    icon: Headphones,
    difficulty: "Hard",
    duration: "10-15 min",
    iconColor: "text-purple-400",
  },
  {
    id: "decade-challenge" as GameMode,
    title: "Decade Master",
    description: "Focus on specific eras.",
    icon: Calendar,
    difficulty: "Medium",
    duration: "6-10 min",
    iconColor: "text-orange-400",
  },
  {
    id: "genre-specialist" as GameMode,
    title: "Genre Expert",
    description: "Deep dive into genres.",
    icon: Radio,
    difficulty: "Medium",
    duration: "8-12 min",
    iconColor: "text-indigo-400",
  },
  {
    id: "guess-the-year" as GameMode,
    title: "Guess the Year",
    description: "How accurate is your release-year memory?",
    icon: Calendar,
    difficulty: "Easy",
    duration: "5-8 min",
    iconColor: "text-teal-400",
  },
  {
    id: "album-art" as GameMode,
    title: "Album Art Quiz",
    description: "Identify from album covers.",
    icon: ImageIcon,
    difficulty: "Medium",
    duration: "6-10 min",
    iconColor: "text-rose-400",
  },
  {
    id: "quick-fire" as GameMode,
    title: "Quick Fire",
    description: "Rapid-fire questions at lightning speed.",
    icon: Zap,
    difficulty: "Hard",
    duration: "2-3 min",
    iconColor: "text-yellow-400",
  },
  {
    id: "audio-fingerprint" as GameMode,
    title: "Audio Detective",
    description: "Expert recognition from micro-clips.",
    icon: Search,
    difficulty: "Expert",
    duration: "4-6 min",
    iconColor: "text-red-400",
  },
  {
    id: "hitster-timeline" as GameMode,
    title: "Hitster Timeline",
    description: "Place songs in chronological order on a timeline. Team-based gameplay!",
    icon: Clock,
    difficulty: "Team Game",
    duration: "20-30 min",
    iconColor: "text-indigo-400",
  },
  {
    id: "custom" as GameMode,
    title: "Custom Quiz",
    description: "Create your own quiz with full control over filters and question types.",
    icon: Settings,
    difficulty: "Variable",
    duration: "Custom",
    iconColor: "text-pink-400",
  },
]

export function GameModeSelector({ onSelectMode }: GameModeSelectorProps) {
  const getDifficultyBadgeColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "Medium":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30"
      case "Hard":
        return "bg-orange-500/20 text-orange-400 border-orange-500/30"
      case "Expert":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "Team Game":
        return "bg-indigo-500/20 text-indigo-400 border-indigo-500/30"
      case "Ultimate":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
    }
  }

  return (
    <motion.div className="space-y-8" variants={containerVariants} initial="hidden" animate="visible">
      <motion.div className="text-center" variants={itemVariants}>
        <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
          Choose Your Challenge
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Select a game mode to test your music knowledge
        </p>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        variants={containerVariants}
      >
        {gameModes.map((mode) => {
          const IconComponent = mode.icon
          return (
            <motion.div
              key={mode.id}
              variants={itemVariants}
              whileHover={{ y: -4, scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card
                className="group cursor-pointer h-full flex flex-col transition-all duration-300 bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30 hover:border-blue-400/50 hover:shadow-lg hover:shadow-blue-500/10"
                onClick={() => onSelectMode(mode.id)}
              >
                <CardHeader className="pb-4">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-gray-100/20 to-gray-200/20 dark:from-gray-800/20 dark:to-gray-900/20 flex items-center justify-center mb-3 transition-all duration-300 group-hover:scale-110">
                    {mode.customImage ? (
                      <Image
                        src={mode.customImage}
                        alt={mode.title}
                        width={24}
                        height={24}
                        className="transition-all group-hover:scale-110"
                      />
                    ) : (
                      <IconComponent className={`h-6 w-6 ${mode.iconColor} transition-colors group-hover:scale-110`} />
                    )}
                  </div>
                  <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                    {mode.title}
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-600 dark:text-gray-400 h-10">
                    {mode.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0 mt-auto">
                  <div className="flex items-center justify-between mb-4">
                    <Badge className={`${getDifficultyBadgeColor(mode.difficulty)} border`}>
                      {mode.difficulty}
                    </Badge>
                    <span className="text-xs text-gray-500 dark:text-gray-500">{mode.duration}</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full bg-transparent border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-blue-500 hover:text-white hover:border-blue-500 transition-all duration-300"
                  >
                    Start Game
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </motion.div>
    </motion.div>
  )
}
