"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { SkipForward, Users } from "lucide-react"
import { toast } from "sonner"
import type { SkipVoteStatus } from '@/lib/types'
import { getClientUserInfo } from '@/lib/auth-cookies'
import { useSkipVote } from '@/hooks/use-skip-vote'

interface SkipVoteButtonProps {
  currentTrack: {
    id?: string
    title: string
    artist: string
  } | null
  userId: string
  userName: string
  className?: string
}

export function SkipVoteButton({ 
  currentTrack, 
  userId, 
  userName,
  className = "" 
}: SkipVoteButtonProps) {
  const [isVoting, setIsVoting] = useState(false)

  // Get user info for authentication
  const userInfo = getClientUserInfo()
  
  // Use direct jukebox socket
  const {
    isConnected,
    vote: performSkipVote,
    voteStatus,
    hasVoted
  } = useSkipVote()

  // Reset vote status when track changes
  useEffect(() => {
    if (currentTrack?.id) {
      // Vote status is managed by the hook
    }
  }, [currentTrack?.id])

  const handleVote = async () => {
    if (!isConnected || !currentTrack || hasVoted || isVoting) {
      console.log('Cannot vote:', { 
        isConnected, 
        hasTrack: !!currentTrack, 
        hasVoted, 
        isVoting 
      })
      return
    }

    setIsVoting(true)
    try {
      performSkipVote()
      console.log('Skip vote submitted successfully')
      toast.success('Vote submitted!')
    } catch (error) {
      console.error('Failed to submit skip vote:', error)
      toast.error('Failed to submit skip vote')
    } finally {
      setIsVoting(false)
    }
  }

  // Don't show the button if no track is playing
  if (!currentTrack) {
    return null
  }

  // Show connection error if disconnected
  if (!isConnected && connectionError) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant="outline" 
              size="sm" 
              disabled
              className={`opacity-50 ${className}`}
            >
              <SkipForward className="w-4 h-4 mr-2" />
              Connection Error
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Unable to connect to server: {connectionError}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  // Show connecting state
  if (!isConnected) {
    return (
      <Button 
        variant="outline" 
        size="sm" 
        disabled
        className={`opacity-50 ${className}`}
      >
        <SkipForward className="w-4 h-4 mr-2" />
        Connecting...
      </Button>
    )
  }

  const votePercentage = voteStatus?.percentage || 0
  const votesNeeded = voteStatus?.votesNeeded || 0
  const totalVotes = voteStatus?.totalVotes || 0

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={handleVote}
              disabled={hasVoted || isVoting || !isConnected}
              variant={hasVoted ? "default" : "outline"}
              size="sm"
              className={`transition-all duration-200 ${
                hasVoted 
                  ? "bg-red-500 hover:bg-red-600 text-white border-red-500" 
                  : "hover:bg-red-50 hover:border-red-200 hover:text-red-600"
              }`}
            >
              <SkipForward className="w-4 h-4 mr-2" />
              {isVoting ? "Voting..." : hasVoted ? "Voted" : "Skip"}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {hasVoted ? (
              <p>You have voted to skip this track</p>
            ) : (
              <div className="text-center">
                <p className="font-medium">Vote to skip this track</p>
                <p className="text-sm text-muted-foreground">
                  {totalVotes > 0 ? `${totalVotes} vote${totalVotes === 1 ? '' : 's'} so far` : 'Be the first to vote'}
                </p>
                {votesNeeded > 0 && (
                  <p className="text-sm text-muted-foreground">
                    {votesNeeded} more needed to skip
                  </p>
                )}
              </div>
            )}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Vote Progress */}
      {voteStatus && totalVotes > 0 && (
        <div className="flex items-center gap-2 min-w-0">
          <Progress 
            value={votePercentage} 
            className="w-16 h-2" 
          />
          <div className="flex items-center gap-1 text-xs text-muted-foreground whitespace-nowrap">
            <Users className="w-3 h-3" />
            <span>{Math.round(votePercentage)}%</span>
          </div>
        </div>
      )}
    </div>
  )
}