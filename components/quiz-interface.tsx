"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { Play, Pause, Volume2, Clock, Star, ArrowLeft, Check, X, Wifi, WifiOff, Settings, Heart, Plus } from "lucide-react"
import type { GameMode } from "@/lib/types"
import type { QuizSettings } from "@/lib/database/quiz-data"
import { motion, AnimatePresence } from "framer-motion"
import { Label } from "@/components/ui/label"
import { RoundSummaryScreen } from "./round-summary-screen"
import type { Track } from "@/lib/music-database"
import { AudioManager, type AudioStatus, type AudioEvent } from "@/lib/audio-manager"
import { AudioMixer } from "@/lib/audio-mixer"
import { AudioMixerPane<PERSON> } from "@/components/audio-mixer-panel"
import { getUnifiedAudioController, UnifiedAudioController } from "@/lib/unified-audio-controller"
import { volumeStateManager } from "@/lib/volume-state-manager"
import { toast } from "sonner"
import { TeamQuizInterface } from "./team-quiz-interface"
import { TeamFormation } from "./team-formation"
import type { Team, TeamGameSettings, TeamChatMessage } from "@/lib/types"
import { getAudioConfig, validateAudioConfig } from "@/lib/env"
import { evaluateChartPosition, evaluateYear } from "@/lib/quiz-scoring"
import type { CustomQuizSettings } from "./custom-quiz-config"
import { useGameSocket } from "@/hooks/use-game-socket"
import { quizLogger, PerformanceLogger } from "@/lib/logger"
import { useUser } from "@/lib/user-context"

// Conditional debug logging for production cleanup
const DEBUG = process.env.NODE_ENV === 'development'
const debugLog = DEBUG ? console.log : () => {}
const infoLog = DEBUG ? console.info : () => {}
const warnLog = console.warn // Keep warnings in production
import { GameTransition } from './GameTransition'
import { useGameTransitions } from '@/hooks/useGameTransitions'
import type { PlaylistTrack } from "./playlist-manager"
import { QuickFireQuiz } from "./quick-fire-quiz"
import { AudioTricksQuiz } from "./audio-tricks-quiz"
import { AlbumArtQuiz } from "./album-art-quiz"
import { AudioFingerprintQuiz } from "./audio-fingerprint-quiz"
import { UlTimoteGameHandler } from "./ultimote-game-handler"
import { HitsterGameScreen } from "./hitster-game-screen"
// import { MultiplayerDebug } from "./multiplayer-debug" // Removed - debug component not needed
import { audioPerformanceOptimizer } from "@/lib/audio-performance-optimizer"
import dynamic from 'next/dynamic'

// Lazy load multiplayer component
const MultiplayerQuiz = dynamic(() => import('./multiplayer-quiz').then(mod => ({ default: mod.MultiplayerQuiz })), {
  ssr: false,
  loading: () => <div className="text-center p-8">Loading multiplayer quiz...</div>
})

interface QuizInterfaceProps {
  gameMode: GameMode
  onGameComplete: (results: any) => void
  onBackToMenu: () => void
  isMultiplayer: boolean
  multiplayerRole?: "host" | "player"
  genreFilter?: string
  customSettings?: CustomQuizSettings | null
  showBackButton?: boolean
}

export function QuizInterface({
  gameMode,
  genreFilter,
  customSettings,
  onGameComplete,
  onBackToMenu,
  isMultiplayer,
  multiplayerRole,
  showBackButton = true,
}: QuizInterfaceProps) {
  const { user, updateXP, unlockAchievement, favoriteTrack, addToPlaylist } = useUser();
  const { 
    isTransitioning, 
    transitionConfig, 
    hideTransition,
    showGameStart,
    showRoundStart,
    showGameEnd 
  } = useGameTransitions()
  
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [score, setScore] = useState(0)
  const [streak, setStreak] = useState(0)
  const [timeLeft, setTimeLeft] = useState(30)
  const [isPlaying, setIsPlaying] = useState(false)
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null)
  const [showFeedback, setShowFeedback] = useState(false)
  const [questions, setQuestions] = useState<any[]>([])
  const questionsRef = useRef<any[]>([])
  const [answers, setAnswers] = useState<any[]>([])
  const [isLoadingQuestions, setIsLoadingQuestions] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)
  const [startTime, setStartTime] = useState<number>(Date.now())

  // Audio system state
  const [audioManager, setAudioManager] = useState<AudioManager | null>(null)
  const [audioMixer, setAudioMixer] = useState<AudioMixer | null>(null)
  const [unifiedAudioController, setUnifiedAudioController] = useState<UnifiedAudioController | null>(null)
  const [showMixer, setShowMixer] = useState(false)
  const [audioStatus, setAudioStatus] = useState<AudioStatus>({
    isConnected: false,
    isPlaying: false,
    isPaused: false,
    currentTrack: null,
    volume: 70,
    elapsed: 0,
    duration: 0,
    position: '00:00 / 00:00'
  })
  const [audioError, setAudioError] = useState<string | null>(null)
  const [volume, setVolume] = useState(70)
  const audioManagerRef = useRef<AudioManager | null>(null)

  const [quizState, setQuizState] = useState<"waiting" | "playing" | "completed">("waiting")
  const [quizSubState, setQuizSubState] = useState<"answering" | "feedback" | "summary">("answering")
  const [currentQuestionTrack, setCurrentQuestionTrack] = useState<any | null>(null)
  const [pointsThisRound, setPointsThisRound] = useState(0)
  const [sliderValue, setSliderValue] = useState<number | null>(null)
  const [hasInteractedWithSlider, setHasInteractedWithSlider] = useState<boolean>(false)
  const [showSliderPrompt, setShowSliderPrompt] = useState<boolean>(false)
  const answerSubmittedRef = useRef<boolean>(false)

  // feedback detail state
  const [roundFeedbackDetail, setRoundFeedbackDetail] = useState<string>('')

  // Multiplayer state
  const [multiplayerGameId, setMultiplayerGameId] = useState<string | null>(null)
  const [playerId, setPlayerId] = useState<string | null>(null)
  const [playerName, setPlayerName] = useState<string | null>(null)
  const [waitingForPlayers, setWaitingForPlayers] = useState(false)
  const [multiplayerQuestions, setMultiplayerQuestions] = useState<any[]>([])
  const [leaderboard, setLeaderboard] = useState<any[]>([])

  // Team mode state
  const [isTeamMode, setIsTeamMode] = useState(false)
  const [teams, setTeams] = useState<Team[]>([])
  const [currentTeam, setCurrentTeam] = useState<Team | null>(null)
  const [teamSettings, setTeamSettings] = useState<TeamGameSettings | null>(null)
  const [teamChatMessages, setTeamChatMessages] = useState<TeamChatMessage[]>([])
  const [isCollaborationTime, setIsCollaborationTime] = useState(false)
  const [collaborationTimeRemaining, setCollaborationTimeRemaining] = useState(0)
  const [showTeamFormation, setShowTeamFormation] = useState(false)

  // Initialize multiplayer game info early
  const [initialGamePin] = useState(() => {
    if (typeof window !== 'undefined' && isMultiplayer) {
      const urlParams = new URLSearchParams(window.location.search)
      return urlParams.get('gameCode') || localStorage.getItem('currentGameCode') || ''
    }
    return ''
  })
  
  const [initialPlayerId] = useState(() => {
    if (typeof window !== 'undefined' && isMultiplayer) {
      return localStorage.getItem('currentPlayerId') || `player-${Date.now()}`
    }
    return ''
  })
  
  const [initialPlayerName] = useState(() => {
    if (typeof window !== 'undefined' && isMultiplayer) {
      return localStorage.getItem('currentPlayerName') || 'Player'
    }
    return ''
  })

  // Socket integration for multiplayer - use game PIN for connection
  // Commented out - using unified multiplayer system
  /*
  const { isConnected, gameCode, players, error, createGame, joinGame, toggleTeamMode, currentQuestion: multiplayerCurrentQuestion, timeRemaining: multiplayerTimeRemaining, gameId: socketGameId } = useMultiplayerSocket(
    isMultiplayer ? (initialGamePin || undefined) : undefined,
    isMultiplayer ? (playerId || initialPlayerId || undefined) : undefined,
    isMultiplayer ? (playerName || initialPlayerName || undefined) : undefined
  )
  */
  const isConnected = false
  const gameCode = null
  const players = []
  const error = null
  const multiplayerCurrentQuestion = null
  const multiplayerTimeRemaining = 0
  const socketGameId = null
  
  // Game socket for multiplayer answer submission and game flow
  const { 
    joinGame: joinGameSocket,
    submitAnswer: submitAnswerSocket,
    isConnected: gameSocketConnected 
  } = useGameSocket(
    { 
      gameId: multiplayerGameId || undefined,
      playerId: playerId || undefined,
      playerName: playerName || undefined
    },
    {
      onGameStarted: (data) => {
        debugLog('[Quiz] Game started event received:', data)
        // Initialize quiz state for multiplayer
        setIsLoadingQuestions(false)
        setQuestions([]) // Clear any existing questions
        questionsRef.current = []
        setCurrentQuestion(0)
        setScore(0)
        setStreak(0)
        setAnswers([])
        // Show game start transition if available
        if (showGameStart) {
          showGameStart({
            gameMode,
            totalQuestions: data.totalQuestions || 10
          })
        }
      },
      onQuestionResults: (data) => {
        debugLog('[Quiz] Question results received:', data)
        setWaitingForPlayers(false)
        // Move to feedback/summary state to show results
        setQuizSubState("feedback")
        setShowFeedback(true)
        
        // After showing results, move to next question
        setTimeout(() => {
          setShowFeedback(false)
          setQuizSubState("summary")
        }, 2000)
      },
      onNewQuestion: (data) => {
        debugLog('[Quiz] New question received:', data)
        
        // If this is the first question, initialize the questions array
        if (data.questionIndex === 0 && questions.length === 0) {
          // Create a placeholder questions array based on the question data
          const placeholderQuestions = [{
            id: data.question.id,
            type: data.question.type,
            question: data.question.question,
            correctAnswer: data.question.correctAnswer || '',
            options: data.question.options || [],
            track: data.question.track || data.question.audioFile ? {
              id: data.question.id,
              title: data.question.trackTitle || 'Unknown Track',
              artist: data.question.artist || 'Unknown Artist',
              album: data.question.album || 'Unknown Album',
              year: data.question.year || new Date().getFullYear(),
              genre: data.question.genre || 'Unknown',
              file: data.question.audioFile || '',
              duration: data.question.duration || 0,
              chartPosition: data.question.chartPosition
            } : null,
            difficulty: data.question.difficulty || 3,
            timeLimit: data.question.timeLimit || 30,
            points: data.question.points || 10
          }]
          setQuestions(placeholderQuestions)
          questionsRef.current = placeholderQuestions
        } else if (data.questionIndex >= questions.length) {
          // Add new question to the array if needed
          const newQuestion = {
            id: data.question.id,
            type: data.question.type,
            question: data.question.question,
            correctAnswer: data.question.correctAnswer || '',
            options: data.question.options || [],
            track: data.question.track || data.question.audioFile ? {
              id: data.question.id,
              title: data.question.trackTitle || 'Unknown Track',
              artist: data.question.artist || 'Unknown Artist',
              album: data.question.album || 'Unknown Album',
              year: data.question.year || new Date().getFullYear(),
              genre: data.question.genre || 'Unknown',
              file: data.question.audioFile || '',
              duration: data.question.duration || 0,
              chartPosition: data.question.chartPosition
            } : null,
            difficulty: data.question.difficulty || 3,
            timeLimit: data.question.timeLimit || 30,
            points: data.question.points || 10
          }
          setQuestions(prev => [...prev, newQuestion])
          questionsRef.current = [...questionsRef.current, newQuestion]
        }
        
        // Reset state for new question
        setCurrentQuestion(data.questionIndex)
        setTimeLeft(data.question.timeLimit || 30)
        setSelectedAnswer(null)
        // Set default slider value based on question type
        if (data.question.type === 'chart-position' || data.question.type === 'year') {
          let defaultValue = 50
          if (data.question.type === 'year' && data.question.metadata?.actualYear) {
            const year = parseInt(data.question.metadata.actualYear)
            defaultValue = Math.max(1900, Math.min(2025, year - 5 + Math.floor(Math.random() * 10)))
          }
          setSliderValue(defaultValue)
        } else {
          setSliderValue(null)
        }
        setHasInteractedWithSlider(false)
        setIsPlaying(false)
        setQuizSubState("answering")
        setPointsThisRound(0)
        setCurrentQuestionTrack(null)
        answerSubmittedRef.current = false
        setWaitingForPlayers(false)
      },
      onGameOver: (data) => {
        debugLog('[Quiz] Game over received:', data)
        // Handle game completion with final leaderboard
        onGameComplete({
          leaderboard: data.leaderboard,
          gameMode,
          isMultiplayer: true
        })
      }
    }
  )

  // Initialize multiplayer game state
  useEffect(() => {
    if (isMultiplayer) {
      // Get game info from URL params or localStorage
      const urlParams = new URLSearchParams(window.location.search)
      // For multiplayer, we need the game code (PIN), not the database ID
      const gamePin = urlParams.get('gameCode') || localStorage.getItem('currentGameCode') || ''
      const gameId = localStorage.getItem('currentGameId') || ''
      const pId = urlParams.get('playerId') || localStorage.getItem('currentPlayerId') || `player-${Date.now()}`
      const pName = urlParams.get('playerName') || localStorage.getItem('currentPlayerName') || 'Player'
      
      debugLog('[Quiz] Game info:', { gamePin, gameId, pId, pName })
      
      // Use the game ID from socket once connected
      const actualGameId = socketGameId || gameId
      
      setMultiplayerGameId(actualGameId)
      setPlayerId(pId)
      setPlayerName(pName)
      
      // Store in localStorage for persistence
      if (actualGameId) localStorage.setItem('currentGameId', actualGameId)
      if (pId) localStorage.setItem('currentPlayerId', pId)
      if (pName) localStorage.setItem('currentPlayerName', pName)

      debugLog('[Quiz] Multiplayer initialized:', { gamePin, actualGameId, pId, pName, isConnected, socketGameId })
    }
  }, [isMultiplayer, gameCode, isConnected, socketGameId])

  // Join the game when socket is connected
  useEffect(() => {
    if (isMultiplayer && multiplayerGameId && playerId && playerName && gameSocketConnected && joinGameSocket) {
      debugLog('[Quiz] Joining game:', { multiplayerGameId, playerId, playerName })
      try {
        joinGameSocket(multiplayerGameId, playerId, playerName)
      } catch (error) {
        console.error('[Quiz] Failed to join game:', error)
      }
    }
  }, [isMultiplayer, multiplayerGameId, playerId, playerName, gameSocketConnected, joinGameSocket])
  
  // Also ensure we're connected via the multiplayer socket with the game PIN
  useEffect(() => {
    if (isMultiplayer && initialGamePin && playerId && playerName && isConnected) {
      debugLog('[Quiz] Ensuring multiplayer socket connection with PIN:', initialGamePin)
      // The socket should already be connected, but ensure we've joined the game
      // Check if we have an error indicating the game has already started
      if (!players.includes(playerName) && !error?.includes('Game already in progress')) {
        debugLog('[Quiz] Player not in game, attempting to join...')
        joinGame(initialGamePin, playerName)
      } else if (error?.includes('Game already in progress')) {
        warnLog('[Quiz] Game already in progress - cannot join')
        toast.error('Cannot join game - it has already started', {
          description: 'Please wait for the next game or create a new one',
          duration: 5000
        })
      }
    }
  }, [isMultiplayer, initialGamePin, playerId, playerName, isConnected, players, joinGame, error])

  // Initialize team mode detection
  useEffect(() => {
    if (isMultiplayer && multiplayerGameId) {
      // Check if team mode is enabled for this game
      // This would typically come from game settings or URL params
      const urlParams = new URLSearchParams(window.location.search)
      const teamModeParam = urlParams.get('teamMode') || localStorage.getItem('teamMode')
      
      if (teamModeParam === 'true') {
        setIsTeamMode(true)
        setShowTeamFormation(true)
        debugLog('[Quiz] Team mode enabled')
      }
    }
  }, [isMultiplayer, multiplayerGameId])

  // Team event handlers
  const handleCreateTeam = (teamName: string, color: string, emoji: string) => {
    // TODO: Implement team creation via socket
    debugLog('Creating team:', { teamName, color, emoji })
  }

  const handleJoinTeam = (teamId: string) => {
    // TODO: Implement team joining via socket
    debugLog('Joining team:', teamId)
  }

  const handleLeaveTeam = () => {
    // TODO: Implement team leaving via socket
    debugLog('Leaving team')
  }

  const handleSendTeamMessage = (message: string) => {
    // TODO: Implement team chat via socket
    debugLog('Sending team message:', message)
  }

  const handleSubmitTeamAnswer = (answer: number) => {
    // TODO: Implement team answer submission via socket
    debugLog('Submitting team answer:', answer)
  }

  // Initialize audio manager
  useEffect(() => {
    const initializeAudio = async () => {
      try {
        const config = getAudioConfig()
        
        // Validate configuration
        const configErrors = validateAudioConfig(config)
        if (configErrors.length > 0) {
          console.error('[Quiz] Audio configuration errors:', configErrors)
          toast.error(`Audio configuration error: ${configErrors[0]}`)
          return
        }

        const manager = new AudioManager({
          host: config.mpdHost,
          port: config.mpdPort,
          password: config.mpdPassword,
          httpProxyPort: config.mpdHttpPort,
          statusUpdateInterval: config.statusUpdateInterval,
          enableLogging: config.enableLogging,
          autoReconnect: config.autoReconnect,
          maxReconnectAttempts: config.maxReconnectAttempts
        })

        // Set up event listeners
        manager.on('statusUpdate', (event: AudioEvent) => {
          setAudioStatus(event.data)
          setIsPlaying(event.data.isPlaying)
        })

        manager.on('connected', (event: AudioEvent) => {
          debugLog('[Quiz] Connected to MPD server:', event.data)
          setAudioError(null)
          toast.success('Connected to audio server')
        })

        manager.on('disconnected', (event: AudioEvent) => {
          debugLog('[Quiz] Disconnected from MPD server:', event.data)
          setAudioError('Disconnected from audio server')
          toast.error('Lost connection to audio server')
        })

        manager.on('error', (event: AudioEvent) => {
          console.error('[Quiz] Audio error:', event.data)
          setAudioError(event.data.message || 'Audio system error')
          toast.error(`Audio error: ${event.data.message || 'Unknown error'}`)
        })

        manager.on('trackChanged', (event: AudioEvent) => {
          debugLog('[Quiz] Track changed:', event.data)
          // Clear any previous audio errors when track successfully changes
          setAudioError(null)
        })

        manager.on('trackEnded', (event: AudioEvent) => {
          debugLog('[Quiz] Track ended:', event.data)
          if (event.data.reason === 'timeLimit' && quizSubState === "answering" && !answerSubmittedRef.current) {
            if (questionsRef.current.length === 0) return
            debugLog('[Quiz] Auto-submitting due to audio time limit')
            handleAnswer(null)
          }
        })

        manager.on('volumeChanged', (event: AudioEvent) => {
          // Only update UI if not currently fading
          if (!manager.isFadingVolume()) {
            setVolume(event.data.volume)
            // Volume UI updated (debug logging removed for production)
          } else {
            // Ignoring volume change during fade (debug logging removed)
          }
        })

        // Initialize and set initial volume
        await manager.initialize()
        await manager.setVolume(config.defaultVolume)
        setVolume(config.defaultVolume)
        // Initial volume set (debug logging removed for production)

        setAudioManager(manager)
        audioManagerRef.current = manager

        // Initialize audio mixer
        const mixer = new AudioMixer()
        setAudioMixer(mixer)

        // Initialize unified audio controller with low volume
        const controller = getUnifiedAudioController()
        controller.registerSystems({
          audioManager: manager,
          audioMixer: mixer
        })
        // Initialize with user's preferred volume for quiz mode
        const initialVolume = volumeStateManager.initializeForMode('quiz')
        await controller.initialize(initialVolume)
        setUnifiedAudioController(controller)

        debugLog('[Quiz] Audio manager, mixer, and unified controller initialized successfully')

      } catch (error) {
        console.error('[Quiz] Failed to initialize audio manager:', error)
        setAudioError(error instanceof Error ? error.message : 'Failed to initialize audio')
        toast.error('Audio initialization failed', {
          description: error instanceof Error ? error.message : 'Failed to connect to audio server. Please check your MPD setup.',
          duration: 6000
        })
      }
    }

    initializeAudio()

    // Cleanup on unmount
    return () => {
      if (audioManagerRef.current) {
        audioManagerRef.current.cleanup()
      }
    }
  }, [])

  // Optimized quiz initialization using QuizService
  useEffect(() => {
    const initializeQuiz = async () => {
      // Skip loading questions locally if in multiplayer mode
      if (isMultiplayer) {
        debugLog('[Quiz] Multiplayer mode - waiting for questions from server')
        setIsLoadingQuestions(false)
        return
      }
      
      setIsLoadingQuestions(true)
      try {
        quizLogger.info('Initializing quiz with optimized service', { gameMode, genreFilter })

        const settings: QuizSettings = {
          totalQuestions: customSettings?.totalQuestions || 10,
          difficultyLevel: customSettings?.difficultyLevel || 3,
          timeLimit: customSettings?.timeLimit || 30,
          genre: genreFilter || customSettings?.genre,
          decade: customSettings?.decade,
          artist: customSettings?.artist,
          enableHints: customSettings?.enableHints || false,
          autoPlay: true,
          volume: customSettings?.volume || 70,
          previewDuration: customSettings?.previewDuration || 30,
          questionTypes: customSettings?.questionTypes
        }

        // Generate questions using API
        const generatedQuestions = await PerformanceLogger.measureAsync(
          'quiz-initialization',
          async () => {
            const response = await fetch('/api/quiz/questions', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ gameMode, settings })
            })

            if (!response.ok) {
              throw new Error(`Failed to fetch questions: ${response.statusText}`)
            }

            const data = await response.json()
            if (!data.success) {
              throw new Error(data.error || 'Failed to generate questions')
            }

            return data.questions
          }
        )

        if (!generatedQuestions || generatedQuestions.length === 0) {
          throw new Error('No questions generated from database')
        }

        quizLogger.info('Quiz questions generated successfully', {
          count: generatedQuestions.length,
          gameMode,
          settings
        })
        setQuestions(generatedQuestions)
        questionsRef.current = generatedQuestions
        
        // Reset quiz state
        setCurrentQuestion(0)
        setScore(0)
        setStreak(0)
        setTimeLeft(30)
        setSelectedAnswer(null)
        setShowFeedback(false)
        setAnswers([])
        setQuizSubState("answering")
        setCurrentQuestionTrack(null)
        setPointsThisRound(0)
        answerSubmittedRef.current = false // Reset for new quiz
        
      } catch (error) {
        quizLogger.error('Failed to generate questions', error as Error, { gameMode, genreFilter })
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        toast.error(`Failed to load quiz questions: ${errorMessage}`)

        // Fallback to mock data as backup
        quizLogger.info('Falling back to mock data')
        try {
          const mockQuestions = generateMockQuestions(gameMode, customSettings?.totalQuestions || 10)
          setQuestions(mockQuestions)
          questionsRef.current = mockQuestions
          toast.info('Using demo questions (database unavailable)')
        } catch (mockError) {
          quizLogger.error('Failed to generate mock questions', mockError as Error)
          toast.error('Failed to initialize quiz')
        }
      } finally {
        setIsLoadingQuestions(false)
      }
    }

    // Don't initialize single-player quiz for multiplayer games
    if (!isMultiplayer) {
      initializeQuiz()
    } else {
      debugLog('[Quiz] Multiplayer mode - waiting for questions from server')
      setIsLoadingQuestions(false)
    }
  }, [gameMode, genreFilter, isMultiplayer])

  // Debug multiplayer state
  useEffect(() => {
    if (isMultiplayer) {
      debugLog('[Quiz] Multiplayer state:', {
        isConnected,
        gameCode,
        multiplayerGameId,
        playerId,
        playerName,
        error,
        players,
        hasCurrentQuestion: !!multiplayerCurrentQuestion,
        timeRemaining: multiplayerTimeRemaining
      })
    }
  }, [isMultiplayer, isConnected, gameCode, multiplayerGameId, playerId, playerName, error, players, multiplayerCurrentQuestion, multiplayerTimeRemaining])


  // Timer logic - improved coordination with audio manager
  useEffect(() => {
    // Use multiplayer timer if available
    if (isMultiplayer && multiplayerTimeRemaining > 0) {
      setTimeLeft(multiplayerTimeRemaining)
    }
  }, [isMultiplayer, multiplayerTimeRemaining])
  
  useEffect(() => {
    // Don't run timer if we're in multiplayer mode and waiting for questions
    if (isMultiplayer && (!multiplayerQuestions || multiplayerQuestions.length === 0)) {
      return
    }
    
    if (timeLeft > 0 && quizSubState === "answering" && !showFeedback) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (timeLeft === 0 && quizSubState === "answering" && !showFeedback) {
      // Quiz timer reached zero - validate questions exist before auto-submitting
      const questionArray = isMultiplayer ? multiplayerQuestions : questions
      if (!questionArray || questionArray.length === 0) {
        console.warn('[Quiz] Timer reached zero but no questions available - skipping auto-submit')
        // Stop the timer by not processing further
        setQuizSubState("feedback") // Change state to stop timer
        return
      }
      
      if (currentQuestion < 0 || currentQuestion >= questionArray.length) {
        console.warn('[Quiz] Timer reached zero but invalid question index - skipping auto-submit')
        setQuizSubState("feedback") // Change state to stop timer
        return
      }
      
      const currentQ = questionArray[currentQuestion]
      if (!currentQ) {
        console.warn('[Quiz] Timer reached zero but current question is undefined - skipping auto-submit')
        setQuizSubState("feedback") // Change state to stop timer
        return
      }
      
      debugLog('[Quiz] Timer reached zero, auto-submitting answer for question:', currentQ.id || currentQuestion)
      handleAnswer(null)
    }
  }, [timeLeft, quizSubState, showFeedback, selectedAnswer, isMultiplayer, multiplayerQuestions, questions, currentQuestion])

  // Auto-play track when question changes
  useEffect(() => {
    const playCurrentQuestion = async () => {
      if (audioManager && questions.length > 0 && currentQuestion < questions.length && quizSubState === "answering") {
        const question = questions[currentQuestion]
        if (question?.track?.file) { // Database tracks use 'file' field for MPD path
          try {
            const config = getAudioConfig()
            
            // Set quiz time limit
            audioManager.setQuizTimeLimit(config.quizTimeLimit)
            
            // Play track for quiz with enhanced metadata
            const trackWithMetadata = {
              ...question.track,
              quizId: question.id,
              difficulty: question.difficulty,
              category: gameMode,
              preview_start: question.trackPreviewStart || 30,
              preview_duration: config.quizTimeLimit
            }

            debugLog('[Quiz] Starting track for question', currentQuestion + 1)

            // For the first question, ensure we start with low volume and fade in
            if (currentQuestion === 0 && unifiedAudioController) {
              debugLog('[Quiz] First question - setting low volume before track start')
              await unifiedAudioController.setVolume(5) // Very low volume
            }

            // Start track and fade in - use user's current volume setting
            const userVolume = audioManager.getUserVolume()
            await audioManager.playQuizTrack(trackWithMetadata, config.quizTimeLimit)

            // Wait a moment for track to start, then fade in to user's volume
            setTimeout(async () => {
              try {
                if (unifiedAudioController) {
                  // Use unified controller for smooth fade-in
                  await unifiedAudioController.setVolume(userVolume)
                  debugLog(`[Quiz] Unified controller fade-in to user volume: ${userVolume}%`)
                } else {
                  // Fallback to audio manager
                  await audioManager.fadeIn(userVolume, 1.5)
                  debugLog(`[Quiz] Audio manager fade-in to user volume: ${userVolume}%`)
                }
                setIsPlaying(true)
              } catch (error) {
                console.error('[Quiz] Fade-in error:', error)
                toast.error('Audio fade-in failed', {
                  description: error instanceof Error ? error.message : 'Failed to adjust audio volume',
                  duration: 4000
                })
              }
            }, 200)
            
            // Clear any previous audio errors when playback starts successfully
            setAudioError(null)
            
          } catch (error) {
            console.error('[Quiz] Failed to play track:', error)
            toast.error('Track playback failed', {
              description: error instanceof Error ? error.message : 'Unable to play the current audio track',
              duration: 5000
            })
          }
        }
      }
    }

    if (!isLoadingQuestions) {
      playCurrentQuestion()
    }
  }, [audioManager, questions, currentQuestion, quizSubState, isLoadingQuestions])

  // Audio control handlers
  const handlePlayPause = async () => {
    if (!audioManager) {
      toast.error('Audio system not ready')
      return
    }

    try {
      if (audioStatus.isPlaying) {
        await audioManager.pause()
      } else {
        // If no track is playing, start the current question's track
        const question = questions[currentQuestion]
        if (question?.track?.file && !audioStatus.currentTrack) {
          const trackWithMetadata = {
            ...question.track,
            file: question.track.file,
            quizId: question.id,
            category: gameMode,
          }
          await audioManager.playTrack(trackWithMetadata)
        } else {
          await audioManager.resume()
        }
      }
    } catch (error) {
      console.error('[Quiz] Playback control error:', error)
      toast.error('Audio control failed', {
        description: error instanceof Error ? error.message : 'Unable to control audio playback',
        duration: 4000
      })
    }
  }

  const handleVolumeChange = async (newVolume: number[]) => {
    const vol = newVolume[0]
    setVolume(vol)

            // Volume change requested (logging removed for production)

    // Update volume state manager for consistency across modes
    volumeStateManager.setUserVolume(vol, 'quiz')

    if (unifiedAudioController) {
      try {
        await unifiedAudioController.setVolume(vol) // Set volume across all systems
        toast.success(`Volume set to ${vol}%`)
                  // Volume set via unified controller
      } catch (error) {
        console.error('[Quiz] ❌ Volume change error:', error)
        toast.error('Volume adjustment failed', {
          description: error instanceof Error ? error.message : 'Unable to change audio volume',
          duration: 4000
        })
      }
    } else {
              warnLog('[Quiz] No unified audio controller available')
      toast.error('Audio system not ready')
    }
  }

  const handleSeek = async (time: number) => {
    if (audioManager) {
      try {
        await audioManager.seek(time)
      } catch (error) {
        console.error('[Quiz] Seek error:', error)
        toast.error('Audio seek failed', {
          description: error instanceof Error ? error.message : 'Unable to jump to position in track',
          duration: 4000
        })
      }
    }
  }

  const handleSliderChange = (value: number[]) => {
    setSliderValue(value[0])
    setHasInteractedWithSlider(true)
    setShowSliderPrompt(false) // Hide prompt once user interacts
  }

  // Add a new function to handle slider prompt animation
  const showSliderInteractionPrompt = () => {
    if (!questions || questions.length === 0 || currentQuestion >= questions.length || currentQuestion < 0) return
    if (!hasInteractedWithSlider && (questions[currentQuestion]?.type === 'chart-position' || questions[currentQuestion]?.type === 'year')) {
      setShowSliderPrompt(true)
      // Auto-hide prompt after 3 seconds
      setTimeout(() => setShowSliderPrompt(false), 3000)
    }
  }

  // Reset slider state when question changes
  useEffect(() => {
    if (questions.length > 0 && currentQuestion >= 0 && currentQuestion < questions.length) {
      const question = questions[currentQuestion]
      if (question && (question.type === 'chart-position' || question.type === 'year')) {
        // Set a default value immediately to prevent jumping
        let defaultValue = 50
        if (question.type === 'year') {
          if (question.metadata?.actualYear) {
            const year = parseInt(question.metadata.actualYear)
            defaultValue = Math.max(1900, Math.min(2025, year - 5 + Math.floor(Math.random() * 10)))
          } else {
            defaultValue = 1990
          }
        }
        setSliderValue(defaultValue)
        setHasInteractedWithSlider(false)
        setShowSliderPrompt(false)
        // Show prompt after a short delay to draw attention
        setTimeout(() => showSliderInteractionPrompt(), 1500)
      }
    }
  }, [currentQuestion, questions])

  const getChartPositionRange = (position: number) => {
    if (position === 1) return "#1"
    if (position <= 5) return "#2-5"
    if (position <= 10) return "#6-10"
    if (position <= 20) return "#11-20"
    if (position <= 50) return "#21-50"
    return "#51-100"
  }

  // Diagnostic and fallback system
  useEffect(() => {
    if (isMultiplayer) {
      // Run diagnostics
      multiplayerDiagnostic.diagnoseMultiplayerState({
        isConnected,
        gameCode,
        playerId,
        playerName,
        multiplayerCurrentQuestion,
        multiplayerQuestions,
        quizState,
        quizSubState,
        timeRemaining: multiplayerTimeRemaining
      })

      // Check for fallback questions need
      const fallbackCheck = multiplayerQuestionFallback.checkAndProvideFallback(
        multiplayerQuestions,
        isMultiplayer,
        quizState,
        gameCode
      )

      if (fallbackCheck.needsFallback) {
        console.warn('[Quiz] Using fallback questions:', fallbackCheck.reason)
        
        // Apply fallback questions
        const mockQuestions = fallbackCheck.fallbackQuestions.map(q =>
          multiplayerQuestionFallback.createMockMultiplayerQuestion(q)
        )
        
        setMultiplayerQuestions(mockQuestions)
        
        if (mockQuestions.length > 0 && !multiplayerCurrentQuestion) {
          // Set first question as current if none exists
          // Note: This is a fallback - normally the server should provide current question
          debugLog('[Quiz] Setting fallback question as current')
        }
      }
    }
  }, [isMultiplayer, isConnected, gameCode, playerId, playerName, multiplayerCurrentQuestion, multiplayerQuestions, quizState, quizSubState, multiplayerTimeRemaining])

  // Handle accumulating multiplayer questions as they arrive
  useEffect(() => {
    if (isMultiplayer && multiplayerCurrentQuestion) {
      debugLog('[Quiz] New multiplayer question received:', multiplayerCurrentQuestion.id)
      
      // Convert multiplayer question to the expected format
      const formattedQuestion = {
        id: multiplayerCurrentQuestion.id || `q-${Date.now()}`,
        type: multiplayerCurrentQuestion.type || 'multiple-choice',
        question: multiplayerCurrentQuestion.question,
        correctAnswer: multiplayerCurrentQuestion.correctAnswer || '',
        options: multiplayerCurrentQuestion.options || [],
        track: multiplayerCurrentQuestion.track || multiplayerCurrentQuestion.audioFile ? {
          id: multiplayerCurrentQuestion.id,
          title: multiplayerCurrentQuestion.trackTitle || 'Unknown Track',
          artist: multiplayerCurrentQuestion.artist || 'Unknown Artist',
          album: multiplayerCurrentQuestion.album || 'Unknown Album',
          year: multiplayerCurrentQuestion.year || new Date().getFullYear(),
          genre: multiplayerCurrentQuestion.genre || 'Unknown',
          file: multiplayerCurrentQuestion.audioFile || multiplayerCurrentQuestion.track?.file || '',
          duration: multiplayerCurrentQuestion.duration || 0,
          chartPosition: multiplayerCurrentQuestion.chartPosition
        } : null,
        difficulty: multiplayerCurrentQuestion.difficulty || 3,
        timeLimit: multiplayerCurrentQuestion.timeLimit || multiplayerTimeRemaining || 30,
        points: multiplayerCurrentQuestion.points || 10
      }
      
      setMultiplayerQuestions(prevQuestions => {
        // Check if this question is already in the array
        const existingIndex = prevQuestions.findIndex(q => q.id === formattedQuestion.id)
        
        if (existingIndex === -1) {
          // New question - add it to the array
          const newQuestions = [...prevQuestions, formattedQuestion]
          debugLog('[Quiz] Added formatted question to array. Total questions:', newQuestions.length)
          return newQuestions
        } else {
          // Question already exists - update it in place
          const updatedQuestions = [...prevQuestions]
          updatedQuestions[existingIndex] = formattedQuestion
          debugLog('[Quiz] Updated existing question in array')
          return updatedQuestions
        }
      })
      
      // Update the current question index to match the latest question
      const newQuestionIndex = multiplayerQuestions.findIndex(q => q.id === formattedQuestion.id)
      if (newQuestionIndex !== -1 && newQuestionIndex !== currentQuestion) {
        debugLog('[Quiz] Updating current question index to:', newQuestionIndex)
        setCurrentQuestion(newQuestionIndex)
      } else if (newQuestionIndex === -1) {
        // If question not found in array yet, it will be added above, so set to last index
        setCurrentQuestion(multiplayerQuestions.length)
      }
      
      // Set quiz state to playing if we have a question
      if (quizState !== 'playing') {
        debugLog('[Quiz] Setting quiz state to playing')
        setQuizState('playing')
        setQuizSubState('answering')
      }
      
      // Update timers and interface state
      if (quizSubState !== "answering") {
        setQuizSubState("answering")
        setTimeLeft(formattedQuestion.timeLimit)
        setSelectedAnswer(null)
        setShowFeedback(false)
      }
    }
  }, [isMultiplayer, multiplayerCurrentQuestion, multiplayerTimeRemaining])

  const handleAnswer = async (answer: string | null) => {
    // Capture slider value and interaction state immediately to prevent race conditions
    const capturedSliderValue = sliderValue
    const capturedHasInteracted = hasInteractedWithSlider

    if (quizSubState !== "answering") return

    // Check if questions are loaded and current question index is valid
    const questionArray = isMultiplayer ? multiplayerQuestions : questions
    if (!questionArray || questionArray.length === 0) {
      console.error('[Quiz] handleAnswer: questions array is empty or undefined')
      
      // Try emergency recovery for multiplayer
      if (isMultiplayer) {
        console.warn('[Quiz] Attempting emergency question recovery')
        multiplayerQuestionFallback.emergencyQuizRecovery(
          setMultiplayerQuestions,
          (question) => {
            // This would need to be implemented if we had access to setMultiplayerCurrentQuestion
            debugLog('[Quiz] Emergency current question would be set:', question)
          },
          setQuizState,
          setQuizSubState
        )
      }
      return
    }
    
    if (currentQuestion >= questionArray.length || currentQuestion < 0) {
      console.error('[Quiz] handleAnswer: invalid question index', currentQuestion, 'length:', questionArray.length)
      return
    }

    const question = questionArray[currentQuestion]
    if (!question) {
      console.error('[Quiz] handleAnswer: question undefined for index', currentQuestion)
      return
    }

    // If slider-based and answer is null, check if user actually interacted
    if (!answer && question.options?.length === 0) {
      // Only use slider value if user actually moved the slider
      if (capturedHasInteracted && capturedSliderValue !== null) {
        answer = capturedSliderValue.toString()
        debugLog(`[Quiz] Using slider value: ${answer} (user interacted)`)
      } else {
        answer = null  // No interaction = no answer
        debugLog(`[Quiz] No slider interaction detected, treating as no answer`)
      }
    }

    debugLog('[Quiz] Answer submitted:', answer)
    
    // Prevent double submission now that question is verified
    if (answerSubmittedRef.current) return
    answerSubmittedRef.current = true

    // For multiplayer, send answer to server and wait for results
    if (isMultiplayer && isConnected && multiplayerGameId) {
      debugLog('[Quiz] Multiplayer answer submitted:', answer, 'Game:', multiplayerGameId)
      
      // Show waiting state
      setSelectedAnswer(answer)
      setQuizSubState("feedback")
      setWaitingForPlayers(true)
      
      // Fade out audio
      if (audioManager && audioStatus.isPlaying) {
        try {
          debugLog('[Quiz] Fading out audio after answer')
          await audioManager.fadeOutQuizTrack(1.2)
        } catch (error) {
          console.error('[Quiz] Failed to fade out audio:', error)
        }
      }
      
      // Submit answer to server
      try {
        const answerIndex = answer ? parseInt(answer) : -1 // -1 indicates no answer
        const timeTaken = 30 - timeLeft // Calculate time taken to answer
        
        // Get player ID from localStorage or generate one
        const playerId = localStorage.getItem('multiplayerPlayerId') || Math.random().toString(36).substring(7)
        localStorage.setItem('multiplayerPlayerId', playerId)
        
        submitAnswerSocket(multiplayerGameId, playerId, answerIndex, timeTaken * 1000) // Convert to milliseconds
        debugLog('[Quiz] Answer submitted to server:', { gameId: multiplayerGameId, playerId, answerIndex, timeTaken })
        
        // Don't continue with single player logic - wait for server response
        return
      } catch (error) {
        console.error('[Quiz] Failed to submit answer to server:', error)
        toast.error('Failed to submit answer')
        setWaitingForPlayers(false)
      }
    }

    // Single player logic (existing code)
    // Check if answer is correct with special handling for year and chart position questions
    let isCorrect = false
    let pointsMultiplier = 1.0
    let difference: number | null = null
    
    if (question.type === 'year' && answer) {
      const res = evaluateYear(answer, question.correctAnswer)
      isCorrect = res.isCorrect
      pointsMultiplier = res.pointsMultiplier
      difference = res.difference
    } else if (question.type === 'chart-position' && answer) {
      const res = evaluateChartPosition(answer, question.correctAnswer)
      isCorrect = res.isCorrect
      pointsMultiplier = res.pointsMultiplier
      difference = res.difference
    } else {
      isCorrect = answer === question.correctAnswer
    }
    
    let pointsEarned = 0

    if (isCorrect) {
      // Improved base points calculation for fairer scoring curve
      const basePoints = Math.max(5, timeLeft) + streak * 2
      pointsEarned = Math.round(basePoints * pointsMultiplier)
      setScore((prevScore) => prevScore + pointsEarned)
      setStreak((prevStreak) => prevStreak + 1)
      
      // Award XP to user if authenticated
      if (user) {
        updateXP(pointsEarned)
        
        // Check for achievements
        if (streak + 1 >= 5 && !user.achievements.find(a => a.id === 'speed-demon')?.earned) {
          unlockAchievement('speed-demon')
        }
        if (streak + 1 >= 15 && !user.achievements.find(a => a.id === 'streak-master')?.earned) {
          unlockAchievement('streak-master')
        }
        if (pointsEarned >= 100 && !user.achievements.find(a => a.id === 'music-novice')?.earned) {
          unlockAchievement('music-novice')
        }
      }
    } else {
      setStreak(0)
    }
    setPointsThisRound(pointsEarned)

    // Announce the result with TTS
    announceScore(pointsEarned, isCorrect)

    setSelectedAnswer(answer)
    setShowFeedback(true)
    setQuizSubState("feedback")

    // Fade out audio using the improved method
    if (audioManager && audioStatus.isPlaying) {
      try {
        debugLog('[Quiz] Fading out audio after answer')
        await audioManager.fadeOutQuizTrack(1.2) // Slightly faster fade
      } catch (error) {
        console.error('[Quiz] Failed to fade out audio:', error)
      }
    }

    const newAnswerRecord = {
      question: question.question,
      userAnswer: answer,
      correctAnswer: question.correctAnswer,
      isCorrect,
      difference,
      track: question.track,
      timeSpent: 30 - timeLeft,
      audioTrack: audioStatus.currentTrack
    }
    setAnswers((prevAnswers) => [...prevAnswers, newAnswerRecord])
    
    // Use track metadata from the question (database metadata)
    const trackForSummary = question.track || {
      title: question.question.replace(/.*"(.*)".*/,'$1') || 'Unknown Title',
      artist: question.type === 'artist' && question.options && typeof question.correctAnswer === 'number'
        ? question.options[question.correctAnswer]
        : 'Unknown Artist',
      album: 'Unknown Album',
    }
    setCurrentQuestionTrack(trackForSummary)

    // prepare feedback detail message
    let feedbackDetail = ''
    if (answer) {
      if (question.type === 'chart-position') {
        if (difference === 0) {
          feedbackDetail = `Perfect! You guessed #${question.correctAnswer} exactly.`
        } else if (difference !== null) {
          if (difference <= 2) {
            feedbackDetail = `Almost! Correct was #${question.correctAnswer}. You were ${difference} position${difference > 1 ? 's' : ''} off (you guessed #${answer}).`
          } else if (difference <= 4) {
            feedbackDetail = `Close! Correct was #${question.correctAnswer}. You were ${difference} positions off (you guessed #${answer}).`
          } else {
            feedbackDetail = `Incorrect. Correct was #${question.correctAnswer}. You were ${difference} positions off (you guessed #${answer}).`
          }
        }
      } else if (question.type === 'year') {
        if (difference === 0) {
          feedbackDetail = `Spot on! ${question.correctAnswer}.`
        } else if (difference !== null) {
          feedbackDetail = `Actual year: ${question.correctAnswer}. You were ${difference} years off (you guessed ${answer}).`
        }
      }
    } else {
      feedbackDetail = 'You did not provide an answer.'
    }

    const fd = feedbackDetail

    setTimeout(() => {
      setShowFeedback(false)
      setQuizSubState("summary")
      setRoundFeedbackDetail(fd)
    }, 1500)

    // Log play statistics to backend (non-blocking)
    if (question.track?.quizTrackId) {
      fetch('/api/quiz/track-played', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ trackId: question.track.quizTrackId, wasCorrect: isCorrect })
      }).catch(err => console.error('[Quiz] Failed to log track stats', err))
    }
  }

  const handleContinueFromSummary = () => {
    // In multiplayer mode, don't allow manual progression - wait for server
    if (isMultiplayer && isConnected) {
      debugLog('[Quiz] Multiplayer mode - waiting for server to advance')
      // The server will emit 'new-question' event when all players are ready
      return
    }
    
    if (currentQuestion < questions.length - 1) {
      debugLog('[Quiz] Continuing to next question')
      const nextQuestionIndex = currentQuestion + 1
      const nextQuestion = questions[nextQuestionIndex]
      setCurrentQuestion(nextQuestionIndex)
      setTimeLeft(30)
      setSelectedAnswer(null)
      // Set default slider value based on question type
      if (nextQuestion && (nextQuestion.type === 'chart-position' || nextQuestion.type === 'year')) {
        let defaultValue = 50
        if (nextQuestion.type === 'year' && nextQuestion.metadata?.actualYear) {
          const year = parseInt(nextQuestion.metadata.actualYear)
          defaultValue = Math.max(1900, Math.min(2025, year - 5 + Math.floor(Math.random() * 10)))
        }
        setSliderValue(defaultValue)
      } else {
        setSliderValue(null)
      }
      setHasInteractedWithSlider(false) // Reset interaction tracking
      setIsPlaying(false)
      setQuizSubState("answering")
      setPointsThisRound(0)
      setCurrentQuestionTrack(null)
      answerSubmittedRef.current = false // Reset for next question
      
      // Ensure any previous track is fully stopped
      if (audioManager) {
        audioManager.stopQuizTrack().catch(console.error)
      }
      
      // Announce next question
      announceQuestionStart(currentQuestion + 2)
    } else {
      // Game complete - stop audio properly
      debugLog('[Quiz] Game complete, stopping audio')
      if (audioManager) {
        audioManager.stopQuizTrack().catch(console.error)
      }

      const finalAnswers = answers
      const finalScore = score
      const finalCorrectAnswers = finalAnswers.filter((a) => a.isCorrect).length

      let maxOverallStreak = 0
      let currentOverallStreak = 0
      for (const ans of finalAnswers) {
        if (ans.isCorrect) {
          currentOverallStreak++
        } else {
          maxOverallStreak = Math.max(maxOverallStreak, currentOverallStreak)
          currentOverallStreak = 0
        }
      }
      maxOverallStreak = Math.max(maxOverallStreak, currentOverallStreak)

      const results = {
        score: finalScore,
        totalQuestions: questions.length,
        correctAnswers: finalCorrectAnswers,
        maxStreak: maxOverallStreak,
        gameMode,
        answers: finalAnswers,
        audioSystemStats: {
          connectionStatus: audioStatus.isConnected,
          tracksPlayed: answers.length,
          audioErrors: audioError ? 1 : 0
        }
      };

      // Add multiplayer-specific data if in multiplayer mode
      if (isMultiplayer) {
        const currentUser = user || { displayName: 'You', id: 'current', email: '<EMAIL>' };
        const accuracy = Math.round((finalCorrectAnswers / questions.length) * 100);
        const averageResponseTime = finalAnswers.reduce((acc, a) => acc + (a.responseTime || 0), 0) / finalAnswers.length;

        // Create a mock leaderboard with current player data
        // In real implementation, this would come from the multiplayer socket
        const multiplayerResults = {
          ...results,
          leaderboard: [
            {
              id: currentUser.id,
              name: currentUser.displayName,
              score: finalScore,
              correctAnswers: finalCorrectAnswers,
              totalQuestions: questions.length,
              averageResponseTime: Math.round(averageResponseTime * 10) / 10,
              maxStreak: maxOverallStreak,
              accuracy,
              position: 1,
              isCurrentUser: true,
              avatar: "/placeholder.svg"
            },
            // Add other players from the socket data
            ...(players || []).slice(0, 5).map((player: string, index: number) => ({
              id: `player_${index}`,
              name: player,
              score: Math.floor(Math.random() * finalScore),
              correctAnswers: Math.floor(Math.random() * finalCorrectAnswers),
              totalQuestions: questions.length,
              averageResponseTime: Math.round((Math.random() * 5 + 1) * 10) / 10,
              maxStreak: Math.floor(Math.random() * maxOverallStreak),
              accuracy: Math.floor(Math.random() * 30 + 60),
              position: index + 2,
              isCurrentUser: false,
              avatar: "/placeholder.svg"
            }))
          ].sort((a, b) => b.score - a.score).map((player, index) => ({ ...player, position: index + 1 })),
          gameStats: {
            totalQuestions: questions.length,
            gameDuration: Math.floor((Date.now() - (startTime || Date.now())) / 1000),
            gameMode,
            averageScore: finalScore,
            totalPlayers: (players?.length || 0) + 1
          }
        };
        
        onGameComplete(multiplayerResults);
      } else {
        onGameComplete(results);
      }
    }
  }

  // TTS announcements for quiz events
  const announceScore = (points: number, isCorrect: boolean) => {
    // Only announce if audioMixer exists AND TTS is enabled in settings
    if (!audioMixer || !audioMixer.getSettings().enableTTS) return
    
    if (isCorrect) {
      if (points >= 25) {
        audioMixer.speakText("Excellent! Perfect score!", { rate: 1.2, pitch: 1.2 })
      } else if (points >= 20) {
        audioMixer.speakText("Great job!", { rate: 1.1, pitch: 1.1 })
      } else {
        audioMixer.speakText("Correct!", { rate: 1.0, pitch: 1.0 })
      }
    } else {
      const encouragements = ["Better luck next time", "Keep trying", "You'll get the next one"]
      const message = encouragements[Math.floor(Math.random() * encouragements.length)]
      audioMixer.speakText(message, { rate: 0.9, pitch: 0.9 })
    }
  }

  const announceQuestionStart = (questionNumber: number) => {
    // Only announce if audioMixer exists AND TTS is enabled in settings
    if (!audioMixer || !audioMixer.getSettings().enableTTS) return
    audioMixer.speakText(`Question ${questionNumber}`, { rate: 1.1, pitch: 1.0 })
  }

  // Fallback mock question generation (backup only)
  const generateMockQuestions = (mode: GameMode, count: number) => {
    // Expanded mock data for fallback
    const mockTracks = [
      { id: '1', title: 'Bohemian Rhapsody', artist: 'Queen', album: 'A Night at the Opera', year: 1975, genre: 'Rock', file: '/test1.mp3', chartPosition: 1 },
      { id: '2', title: 'Billie Jean', artist: 'Michael Jackson', album: 'Thriller', year: 1982, genre: 'Pop', file: '/test2.mp3', chartPosition: 1 },
      { id: '3', title: 'Imagine', artist: 'John Lennon', album: 'Imagine', year: 1971, genre: 'Rock', file: '/test3.mp3', chartPosition: 3 },
      { id: '4', title: 'Like a Rolling Stone', artist: 'Bob Dylan', album: 'Highway 61 Revisited', year: 1965, genre: 'Rock', file: '/test4.mp3', chartPosition: 2 },
      { id: '5', title: 'What\'s Going On', artist: 'Marvin Gaye', album: 'What\'s Going On', year: 1971, genre: 'Soul', file: '/test5.mp3', chartPosition: 4 },
      { id: '6', title: 'Hotel California', artist: 'Eagles', album: 'Hotel California', year: 1976, genre: 'Rock', file: '/test6.mp3', chartPosition: 1 },
      { id: '7', title: 'Stairway to Heaven', artist: 'Led Zeppelin', album: 'Led Zeppelin IV', year: 1971, genre: 'Rock', file: '/test7.mp3', chartPosition: 37 },
      { id: '8', title: 'Yesterday', artist: 'The Beatles', album: 'Help!', year: 1965, genre: 'Pop', file: '/test8.mp3', chartPosition: 1 },
      { id: '9', title: 'Purple Haze', artist: 'Jimi Hendrix', album: 'Are You Experienced', year: 1967, genre: 'Rock', file: '/test9.mp3', chartPosition: 65 },
      { id: '10', title: 'Respect', artist: 'Aretha Franklin', album: 'I Never Loved a Man', year: 1967, genre: 'Soul', file: '/test10.mp3', chartPosition: 1 },
    ]
    
    const fallbackArtists = [
      'Adele', 'The Beatles', 'Queen', 'Michael Jackson', 'Madonna', 'Elvis Presley',
      'Bob Dylan', 'David Bowie', 'Pink Floyd', 'Led Zeppelin', 'The Rolling Stones',
      'U2', 'Coldplay', 'Ed Sheeran', 'Taylor Swift', 'Beyoncé', 'Eminem', 'Drake',
      'Rihanna', 'Lady Gaga', 'Kanye West', 'Bruno Mars', 'John Lennon', 'Paul McCartney',
      'Prince', 'Whitney Houston', 'Mariah Carey', 'Celine Dion', 'Elton John', 'Sting'
    ]
    
    return mockTracks.slice(0, count).map((track, index) => {
      if (mode === 'decade-challenge' || mode === 'guess-the-year') {
        // Generate year-based slider questions for decade challenge
        return {
          id: `mock_${index}`,
          type: 'year',
          question: `What year was "${track.title}" by ${track.artist} released?`,
          correctAnswer: track.year.toString(),
          options: [], // Empty array triggers slider interface
          track: {
            ...track,
            artist: track.artist // Ensure artist field is explicitly set
          },
          difficulty: 3
        }
      } else if (mode === 'chart-position') {
        // Generate chart position slider questions
        return {
          id: `mock_${index}`,
          type: 'chart-position',
          question: `What was the highest chart position of "${track.title}" by ${track.artist}?`,
          correctAnswer: track.chartPosition.toString(),
          options: [], // Empty array triggers slider interface
          track: {
            ...track,
            artist: track.artist // Ensure artist field is explicitly set
          },
          difficulty: 3
        }
      } else {
        // Create wrong options by excluding the correct artist
        const wrongOptions = fallbackArtists
          .filter(artist => artist !== track.artist)
          .sort(() => 0.5 - Math.random())
          .slice(0, 3)
        
        const options = [track.artist, ...wrongOptions].sort(() => 0.5 - Math.random())
        
        return {
          id: `mock_${index}`,
          type: 'artist',
          question: `Who is the artist of this song?`,
          correctAnswer: track.artist,
          options,
          track: {
            ...track,
            artist: track.artist // Ensure artist field is explicitly set
          },
          difficulty: 3
        }
      }
    })
  }

  const handleFavoriteTrack = async () => {
    if (!questions || questions.length === 0 || currentQuestion >= questions.length || currentQuestion < 0) return
    const question = questions[currentQuestion]
    if (!question?.track || !user) return

    const playlistTrack: PlaylistTrack = {
      id: question.track.quizTrackId || `track_${Date.now()}`,
      title: question.track.title || 'Unknown Title',
      artist: question.track.artist || 'Unknown Artist',
      album: question.track.album || 'Unknown Album',
      year: question.track.year || new Date().getFullYear(),
      duration: question.track.duration || 0,
      genre: question.track.genre || 'Unknown',
      chartPosition: question.track.chartPosition,
      addedAt: new Date(),
      addedFrom: 'quiz',
      quizScore: Math.round((score / Math.max(1, currentQuestion + 1)) * 100),
      isFavorite: true
    }

    try {
      await favoriteTrack(playlistTrack)
      setIsFavorited(true)
      toast.success('Added to your favorites! ❤️')
    } catch (error) {
      console.error('Failed to favorite track:', error)
      toast.error('Failed to add track to favorites')
    }
  }

  // Helper function for RoundSummaryScreen to favorite the current track
  const handleFavoriteFromSummary = async () => {
    if (!currentQuestionTrack || !user) return false
    if (!questions || questions.length === 0 || currentQuestion >= questions.length || currentQuestion < 0) return false

    const question = questions[currentQuestion]
    const playlistTrack: PlaylistTrack = {
      id: question?.track?.quizTrackId || `track_${Date.now()}`,
      title: currentQuestionTrack.title || 'Unknown Title',
      artist: currentQuestionTrack.artist || 'Unknown Artist', 
      album: currentQuestionTrack.album || 'Unknown Album',
      year: currentQuestionTrack.year || new Date().getFullYear(),
      duration: currentQuestionTrack.duration || 0,
      genre: currentQuestionTrack.genre || 'Unknown',
      chartPosition: currentQuestionTrack.chartPosition,
      addedAt: new Date(),
      addedFrom: 'quiz',
      quizScore: Math.round((score / Math.max(1, currentQuestion + 1)) * 100),
      isFavorite: true
    }

    try {
      await favoriteTrack(playlistTrack)
      toast.success('Added to your favorites! ❤️')
      return true
    } catch (error) {
      console.error('Failed to favorite track:', error)
      toast.error('Failed to add track to favorites')
      return false
    }
  }

  // Reset favorite state when question changes
  useEffect(() => {
    setIsFavorited(false)
  }, [currentQuestion])

  // Handle ulTimote Game mode with specialized handler
  if (gameMode === 'ultimote') {
    return (
      <UlTimoteGameHandler
        onComplete={onGameComplete}
        onBackToMenu={onBackToMenu}
        isMultiplayer={isMultiplayer}
      />
    )
  }

  if (isLoadingQuestions || (!isMultiplayer && questions.length === 0) || (isMultiplayer && quizState === "waiting" && multiplayerQuestions.length === 0) || (!isMultiplayer && questions.length > 0 && currentQuestion >= questions.length)) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68] flex items-center justify-center p-4">
        <motion.div 
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl p-8 shadow-xl"
        >
          <h1 className="text-2xl md:text-3xl font-extrabold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent mb-6">
            ulTimote
          </h1>
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500/30 border-t-blue-500 mx-auto mb-4"></div>
          <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {isLoadingQuestions ? 'Loading quiz from database...' : 'Preparing quiz...'}
          </p>
          {audioError && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-3 bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              Audio: {audioError}
            </p>
          )}
        </motion.div>
      </div>
    )
  }

  // Handle simplified multiplayer mode
  if (isMultiplayer) {
    return (
      <MultiplayerQuiz
        onGameComplete={onGameComplete}
        onBackToMenu={onBackToMenu}
      />
    )
  }

  // Handle Hitster Timeline mode with specialized component
  if (gameMode === 'hitster-timeline') {
    // Get game code from URL params or localStorage
    const urlParams = new URLSearchParams(window.location.search)
    const gameCode = urlParams.get('gameCode') || localStorage.getItem('currentGameCode') || ''
    const playerId = user?.id || 'player-' + Math.random().toString(36).substring(7)
    const playerName = user?.name || 'Player'
    
    return (
      <HitsterGameScreen
        gameCode={gameCode}
        playerId={playerId}
        playerName={playerName}
        isHost={multiplayerRole === 'host'}
        onBackToLobby={onBackToMenu}
      />
    )
  }

  // Handle Quick Fire mode with specialized component
  if (gameMode === 'quick-fire' && questions.length > 0) {
    return (
      <QuickFireQuiz
        questions={questions}
        onComplete={(results) => {
          // Convert Quick Fire results to standard quiz results format
          const standardResults = {
            score: results.totalScore,
            totalQuestions: results.totalQuestions,
            correctAnswers: results.correctAnswers,
            maxStreak: results.maxStreak,
            gameMode: results.gameMode,
            averageResponseTime: results.averageResponseTime,
            perfectAnswers: results.perfectAnswers,
            answers: [], // Quick Fire doesn't use the same answer format
            audioSystemStats: {
              connectionStatus: audioStatus.isConnected,
              tracksPlayed: results.totalQuestions,
              audioErrors: audioError ? 1 : 0
            }
          }
          onGameComplete(standardResults)
        }}
        onBackToMenu={onBackToMenu}
      />
    )
  }

  // Handle Audio Tricks mode with specialized component
  if (gameMode === 'audio-manipulation' && questions.length > 0) {
    return (
      <AudioTricksQuiz
        questions={questions}
        onComplete={(results) => {
          // Convert Audio Tricks results to standard quiz results format
          const standardResults = {
            score: results.totalScore,
            totalQuestions: results.totalQuestions,
            correctAnswers: results.correctAnswers,
            maxStreak: results.maxStreak,
            gameMode: results.gameMode,
            averageResponseTime: results.averageResponseTime,
            effectsGuessedCorrectly: results.effectsGuessedCorrectly,
            hardestEffectCompleted: results.hardestEffectCompleted,
            answers: [], // Audio Tricks doesn't use the same answer format
            audioSystemStats: {
              connectionStatus: audioStatus.isConnected,
              tracksPlayed: results.totalQuestions,
              audioErrors: audioError ? 1 : 0
            }
          }
          onGameComplete(standardResults)
        }}
        onBackToMenu={onBackToMenu}
      />
    )
  }

  // Handle Album Art mode with specialized component
  if (gameMode === 'album-art' && questions.length > 0) {
    return (
      <AlbumArtQuiz
        questions={questions}
        onComplete={(results) => {
          // Convert Album Art results to standard quiz results format
          const standardResults = {
            score: results.totalScore,
            totalQuestions: results.totalQuestions,
            correctAnswers: results.correctAnswers,
            maxStreak: results.maxStreak,
            gameMode: results.gameMode,
            averageResponseTime: results.averageResponseTime,
            visualEffectsCompleted: results.visualEffectsCompleted,
            perfectIdentifications: results.perfectIdentifications,
            answers: [], // Album Art doesn't use the same answer format
            audioSystemStats: {
              connectionStatus: audioStatus.isConnected,
              tracksPlayed: results.totalQuestions,
              audioErrors: audioError ? 1 : 0
            }
          }
          onGameComplete(standardResults)
        }}
        onBackToMenu={onBackToMenu}
      />
    )
  }

  // Handle Audio Fingerprint mode with specialized component
  if (gameMode === 'audio-fingerprint' && questions.length > 0) {
    return (
      <AudioFingerprintQuiz
        questions={questions}
        onComplete={(results) => {
          // Convert Audio Fingerprint results to standard quiz results format
          const standardResults = {
            score: results.totalScore,
            totalQuestions: results.totalQuestions,
            correctAnswers: results.correctAnswers,
            maxStreak: results.maxStreak,
            gameMode: results.gameMode,
            averageResponseTime: results.averageResponseTime,
            microClipsIdentified: results.microClipsIdentified,
            expertLevelReached: results.expertLevelReached,
            perfectFingerprints: results.perfectFingerprints,
            answers: [], // Audio Fingerprint doesn't use the same answer format
            audioSystemStats: {
              connectionStatus: audioStatus.isConnected,
              tracksPlayed: results.totalQuestions,
              audioErrors: audioError ? 1 : 0
            }
          }
          onGameComplete(standardResults)
        }}
        onBackToMenu={onBackToMenu}
      />
    )
  }

  // Safety check for questions array and current question index
  if (!questions || questions.length === 0 || currentQuestion >= questions.length || currentQuestion < 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68] p-4">
        <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6 relative flex items-center justify-center min-h-[50vh]">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-4 border-blue-500/30 border-t-blue-500 mx-auto mb-4"></div>
              <p className="text-lg font-medium mb-2">Loading Questions...</p>
              <p className="text-sm text-muted-foreground">Please wait while we prepare your quiz</p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const question = questions[currentQuestion]
  const progress = ((currentQuestion + 1) / questions.length) * 100

  // Show team formation if team mode is enabled and player hasn't joined a team yet
  if (isTeamMode && showTeamFormation && !currentTeam) {
    return (
      <TeamFormation
        gameId={multiplayerGameId || ''}
        teams={teams}
        currentPlayer={{
          id: playerId || '',
          name: playerName || 'Player',
          avatar: '/placeholder.svg',
          score: 0,
          isHost: false,
          hasAnswered: false,
          joinedAt: Date.now()
        }}
        onCreateTeam={handleCreateTeam}
        onJoinTeam={handleJoinTeam}
        onStartWithoutTeam={() => setShowTeamFormation(false)}
        onBackToLobby={onBackToMenu}
      />
    )
  }

  // Show team quiz interface if in team mode and game is active
  if (isTeamMode && currentTeam && !showTeamFormation && question) {
    return (
      <TeamQuizInterface
        teams={teams}
        currentPlayer={{
          id: playerId || '',
          name: playerName || 'Player',
          avatar: '/placeholder.svg',
          score: score,
          isHost: false,
          hasAnswered: false,
          joinedAt: Date.now()
        }}
        currentQuestion={question}
        teamSettings={teamSettings || {
          mode: 'collaborative',
          maxTeamSize: 4,
          collaborationTime: 30,
          scoringMode: 'average'
        }}
        isCollaborationTime={isCollaborationTime}
        collaborationTimeRemaining={collaborationTimeRemaining}
        teamChatMessages={teamChatMessages}
        selectedAnswer={selectedAnswer ? parseInt(selectedAnswer) : null}
        onSelectAnswer={(answerIndex) => setSelectedAnswer(answerIndex.toString())}
        onSubmitTeamAnswer={handleSubmitTeamAnswer}
        onSendTeamMessage={handleSendTeamMessage}
        onSendQuickMessage={handleSendTeamMessage}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68] p-2 sm:p-4">
      <div className="max-w-4xl mx-auto space-y-3 sm:space-y-4 md:space-y-6 relative">
        {/* ulTimote Branding Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-6"
        >
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-extrabold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent mb-1 sm:mb-2">
            ulTimote
          </h1>
          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 font-medium">
            Music Quiz Challenge
          </p>
        </motion.div>
        <AnimatePresence mode="wait">
          {quizSubState === "summary" && currentQuestionTrack && (
            <RoundSummaryScreen
              key={`summary-${currentQuestion}`}
              track={currentQuestionTrack}
              isCorrect={answers[answers.length - 1]?.isCorrect || false}
              playerScore={score}
              questionPoints={pointsThisRound}
              feedbackDetail={roundFeedbackDetail}
              multiplayerScores={
                multiplayerGameId
                  ? [
                      { name: "You", score: score, isCurrentUser: true, avatar: "/placeholder.svg?height=40&width=40" },
                      {
                        name: "Player 2 (Mock)",
                        score: score - Math.floor(Math.random() * 50) + 10,
                        avatar: "/placeholder.svg?height=40&width=40",
                      },
                    ]
                  : undefined
              }
              onContinue={handleContinueFromSummary}
              onFavorite={user ? handleFavoriteFromSummary : undefined}
              showFavoriteButton={!!user}
              isMultiplayer={isMultiplayer}
              waitingForServer={isMultiplayer && isConnected}
            />
          )}
        </AnimatePresence>

        <motion.div
          key={`question-content-${currentQuestion}`}
          initial={{ opacity: quizSubState === "summary" ? 0 : 1 }}
          animate={{ opacity: quizSubState === "summary" ? 0 : 1 }}
          transition={{ duration: 0.2 }}
          className={quizSubState === "summary" ? "pointer-events-none" : ""}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            {showBackButton ? (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onBackToMenu}
                className="bg-white/10 dark:bg-white/5 backdrop-blur-md border-white/20 dark:border-white/10 hover:bg-white/20 dark:hover:bg-white/10 text-gray-900 dark:text-white transition-all duration-200 hover:scale-105 shadow-lg"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Menu
              </Button>
            ) : (
              <div /> 
            )}
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="bg-white/10 dark:bg-white/5 backdrop-blur-md border-white/20 dark:border-white/10 text-gray-900 dark:text-white text-xs sm:text-sm">
                Q {currentQuestion + 1}/{questions.length}
              </Badge>
              <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 shadow-lg hover:scale-105 transition-transform text-xs sm:text-sm">
                Score: {score}
              </Badge>
              {streak > 0 && (
                <Badge className="bg-gradient-to-r from-amber-400 to-orange-500 text-white border-0 shadow-lg hover:scale-105 transition-transform animate-pulse text-xs sm:text-sm">
                  <Star className="h-3 w-3 mr-1" />
                  {streak}
                </Badge>
              )}
              {/* Audio Connection Status - Hide on mobile */}
              <Badge 
                variant="outline" 
                className={`hidden sm:flex bg-white/10 dark:bg-white/5 backdrop-blur-md border-white/20 dark:border-white/10 text-xs sm:text-sm ${
                  audioStatus.isConnected 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}
              >
                {audioStatus.isConnected ? (
                  <Wifi className="h-3 w-3 mr-1" />
                ) : (
                  <WifiOff className="h-3 w-3 mr-1" />
                )}
                Audio
              </Badge>
            </div>
          </div>

          {/* Progress */}
          <div className="space-y-2 mb-6">
            <div className="flex justify-between text-sm text-gray-700 dark:text-gray-300">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="h-2 bg-white/20 dark:bg-white/10 rounded-full overflow-hidden backdrop-blur-sm border-0">
              <div
                className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-500 ease-out border-0 shadow-none"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {/* Main Quiz Card */}
          <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-md border-white/20 dark:border-white/10 shadow-xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent leading-tight">
                  {question.question}
                </CardTitle>
                <div className="flex items-center gap-2 bg-white/20 dark:bg-white/10 rounded-full px-3 py-2 backdrop-blur-sm">
                  <Clock className="h-4 w-4 text-gray-700 dark:text-gray-300" />
                  <span
                    className={`font-mono text-lg font-bold ${
                      timeLeft <= 10 && timeLeft > 0
                        ? "text-red-500 animate-pulse"
                        : timeLeft === 0
                          ? "text-red-500"
                          : "text-gray-900 dark:text-white"
                    }`}
                  >
                    {timeLeft}s
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6 pb-6">
              {/* Enhanced Audio Player */}
              <Card className="bg-white/20 dark:bg-white/10 backdrop-blur-sm border-white/30 dark:border-white/20">
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <Button
                      size="icon"
                      onClick={handlePlayPause}
                      disabled={!audioStatus.isConnected}
                      className="rounded-full w-12 h-12 shrink-0 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg hover:scale-110 transition-all duration-200"
                      aria-label={audioStatus.isPlaying ? "Pause audio" : "Play audio"}
                    >
                      {audioStatus.isPlaying ? (
                        <Pause className="h-5 w-5" />
                      ) : (
                        <Play className="h-5 w-5" />
                      )}
                    </Button>
                    <div className="flex-1">
                      <div className="h-2 bg-white/30 dark:bg-white/20 rounded-full relative cursor-pointer border-0 overflow-hidden">
                        <div
                          className="h-2 bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300 border-0 shadow-none"
                          style={{
                            width: audioStatus.duration > 0
                              ? `${(audioStatus.elapsed / audioStatus.duration) * 100}%`
                              : '0%'
                          }}
                        ></div>
                      </div>
                    </div>
                    {/* Mobile controls: Essential audio controls for small screens */}
                    <div className="flex items-center gap-2 sm:hidden">
                      <Volume2 className="h-4 w-4 text-gray-700 dark:text-gray-300" />
                      <Slider
                        value={[volume]}
                        onValueChange={handleVolumeChange}
                        max={100}
                        step={1}
                        className="w-16"
                        disabled={!audioStatus.isConnected}
                      />
                      <span className="text-xs text-gray-700 dark:text-gray-300 min-w-[2rem] text-right">
                        {volume}%
                      </span>
                      {user && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleFavoriteTrack}
                          disabled={isFavorited || !questions[currentQuestion]?.track}
                          className={`min-h-[44px] min-w-[44px] bg-white/10 dark:bg-white/5 backdrop-blur-md border-white/20 dark:border-white/10 hover:bg-white/20 dark:hover:bg-white/10 ${
                            isFavorited 
                              ? 'text-red-500 border-red-500/30' 
                              : 'text-gray-900 dark:text-white'
                          }`}
                          title={isFavorited ? "Added to favorites" : "Add to favorites"}
                        >
                          <Heart className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`} />
                        </Button>
                      )}
                    </div>
                    
                    {/* Desktop controls: Full audio controls */}
                    <div className="hidden sm:flex items-center gap-3">
                      <Volume2 className="h-4 w-4 text-gray-700 dark:text-gray-300" />
                      <Slider
                        value={[volume]}
                        onValueChange={handleVolumeChange}
                        max={100}
                        step={1}
                        className="w-20"
                        disabled={!audioStatus.isConnected}
                      />
                      <span className="text-xs text-gray-700 dark:text-gray-300 w-8 text-right">
                        {volume}%
                      </span>
                      {user && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleFavoriteTrack}
                          disabled={isFavorited || !questions[currentQuestion]?.track}
                          className={`bg-white/10 dark:bg-white/5 backdrop-blur-md border-white/20 dark:border-white/10 hover:bg-white/20 dark:hover:bg-white/10 ${
                            isFavorited 
                              ? 'text-red-500 border-red-500/30' 
                              : 'text-gray-900 dark:text-white'
                          }`}
                          title={isFavorited ? "Added to favorites" : "Add to favorites"}
                        >
                          <Heart className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`} />
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowMixer(!showMixer)}
                        className="bg-white/10 dark:bg-white/5 backdrop-blur-md border-white/20 dark:border-white/10 hover:bg-white/20 dark:hover:bg-white/10 text-gray-900 dark:text-white"
                        title="Toggle Audio Mixer"
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Audio Error Display */}
                  {audioError && (
                    <div className="mt-3 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-sm text-red-600 dark:text-red-400 backdrop-blur-sm">
                      ⚠️ {audioError}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Answer section */}
              {(() => {
                // Debug logging to help identify the issue
                debugLog('[Quiz Debug] Current question:', {
                  type: question.type,
                  optionsLength: question.options?.length,
                  options: question.options,
                  gameMode,
                  shouldShowSlider: question.options?.length === 0 || question.type === 'chart-position' || question.type === 'year'
                })
                
                // Show slider for questions with empty options OR for chart-position/year question types
                return question.options?.length === 0 || question.type === 'chart-position' || question.type === 'year'
              })() ? (
                // Slider-based question (year or chart position)
                <motion.div 
                  className="flex flex-col items-center gap-4 sm:gap-6 p-4 sm:p-6 bg-white/10 dark:bg-white/5 rounded-xl backdrop-blur-sm border border-white/20 dark:border-white/10 relative"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {/* Interactive Prompt */}
                  <AnimatePresence>
                    {showSliderPrompt && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg z-10"
                      >
                        👆 Drag the slider to make your guess!
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* Question instruction */}
                  <div className="text-center mb-2">
                    <p className="text-base sm:text-lg font-medium text-gray-700 dark:text-gray-300">
                      {question.type === 'chart-position' ? 
                        'Drag the slider to guess the chart position (1-100)' : 
                        'Drag the slider to guess the release year'
                      }
                    </p>
                  </div>

                  {/* Enhanced Slider */}
                  <motion.div 
                    className="w-full max-w-md relative"
                    animate={!hasInteractedWithSlider ? { 
                      scale: [1, 1.02, 1],
                      transition: { 
                        duration: 2, 
                        repeat: Infinity, 
                        repeatType: "loop" 
                      }
                    } : {}}
                  >
                    <Slider
                      value={sliderValue !== null ? [sliderValue] : [50]}
                      onValueChange={handleSliderChange}
                      min={question.type === 'chart-position' ? 1 : (() => {
                        // Dynamic min for year questions
                        if (question.type === 'year' && question.metadata?.actualYear) {
                          const year = parseInt(question.metadata.actualYear)
                          // Set min to 50 years before the actual year, but not less than 1900
                          return Math.max(1900, year - 50)
                        }
                        return 1950
                      })()}
                      max={question.type === 'chart-position' ? 100 : (() => {
                        // Dynamic max for year questions
                        if (question.type === 'year' && question.metadata?.actualYear) {
                          const year = parseInt(question.metadata.actualYear)
                          // Set max to 30 years after the actual year, but not more than current year + 1
                          return Math.min(new Date().getFullYear() + 1, year + 30)
                        }
                        return new Date().getFullYear() + 1
                      })()}
                      step={1}
                      className={`w-full ${!hasInteractedWithSlider ? 'opacity-70' : 'opacity-100'} transition-opacity duration-300`}
                      disabled={showFeedback || quizSubState !== 'answering'}
                    />
                    
                    {/* Range indicators */}
                    <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
                      <span>{question.type === 'chart-position' ? '#1' : (() => {
                        if (question.type === 'year' && question.metadata?.actualYear) {
                          const year = parseInt(question.metadata.actualYear)
                          return Math.max(1900, year - 50)
                        }
                        return '1950'
                      })()}</span>
                      <span>{question.type === 'chart-position' ? '#100' : (() => {
                        if (question.type === 'year' && question.metadata?.actualYear) {
                          const year = parseInt(question.metadata.actualYear)
                          return Math.min(new Date().getFullYear() + 1, year + 30)
                        }
                        return new Date().getFullYear() + 1
                      })()}</span>
                    </div>
                  </motion.div>

                  {/* Dynamic Value Display */}
                  <motion.div
                    className="text-center"
                    animate={hasInteractedWithSlider ? { scale: [1, 1.1, 1] } : {}}
                    transition={{ duration: 0.3 }}
                  >
                    {sliderValue !== null ? (
                      <div className="space-y-2">
                        <p className="text-3xl sm:text-4xl font-bold text-transparent bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text">
                          {question.type === 'chart-position' ? `#${sliderValue}` : sliderValue}
                        </p>
                        {question.type === 'chart-position' && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {getChartPositionRange(sliderValue)}
                          </p>
                        )}
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <p className="text-3xl font-light text-gray-400 dark:text-gray-600">
                          {question.type === 'chart-position' ? '?' : '????'}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">
                          Move the slider to see your guess
                        </p>
                      </div>
                    )}
                  </motion.div>

                  {/* Submit Button with Enhanced UX */}
                  <Button 
                    disabled={showFeedback || quizSubState !== 'answering' || !hasInteractedWithSlider} 
                    onClick={() => handleAnswer(null)}
                    className={`px-6 sm:px-8 py-2.5 sm:py-3 text-base sm:text-lg font-semibold shadow-lg transition-all duration-300 ${
                      hasInteractedWithSlider 
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0' 
                        : 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {hasInteractedWithSlider ? 'Submit Guess' : 'Move slider first'}
                  </Button>

                  {/* Interaction Hint */}
                  {!hasInteractedWithSlider && (
                    <p className="text-xs text-gray-500 dark:text-gray-500 text-center">
                      💡 You must interact with the slider before submitting
                    </p>
                  )}
                </motion.div>
              ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                {question.options.map((option: string, index: number) => {
                  let buttonClass = "bg-white/10 dark:bg-white/5 backdrop-blur-md border-white/20 dark:border-white/10 hover:bg-white/20 dark:hover:bg-white/10 text-gray-900 dark:text-white"
                  let icon = null

                  if (showFeedback) {
                    if (option === question.correctAnswer) {
                      buttonClass =
                        "bg-green-500/90 hover:bg-green-500 text-white border-green-400 shadow-lg shadow-green-500/25"
                      icon = <Check className="h-4 w-4 ml-auto text-white" />
                    } else if (option === selectedAnswer && option !== question.correctAnswer) {
                      buttonClass = "bg-red-500/90 hover:bg-red-500 text-white border-red-400 shadow-lg shadow-red-500/25"
                      icon = <X className="h-4 w-4 ml-auto text-white" />
                    } else {
                      buttonClass = "bg-white/5 dark:bg-white/2 opacity-50 border-white/10"
                    }
                  } else if (selectedAnswer === option && quizSubState === "answering") {
                    buttonClass = "bg-gradient-to-r from-blue-500/20 to-purple-600/20 border-blue-400 ring-2 ring-blue-400 shadow-lg"
                  }

                  return (
                    <motion.div
                      key={index}
                      whileHover={{ scale: showFeedback || quizSubState === "summary" ? 1 : 1.02 }}
                      whileTap={{ scale: showFeedback || quizSubState === "summary" ? 1 : 0.98 }}
                    >
                      <Button
                        variant={"outline"}
                        className={`h-auto min-h-12 sm:min-h-14 text-left justify-start text-wrap p-3 sm:p-4 w-full transition-all duration-200 text-sm sm:text-base ${buttonClass}`}
                        onClick={() => !showFeedback && quizSubState === "answering" && handleAnswer(option)}
                        disabled={showFeedback || quizSubState === "summary"}
                      >
                        <div className="flex items-center gap-3 w-full">
                          <div
                            className={`w-8 h-8 rounded-lg ${
                              selectedAnswer === option && quizSubState === "answering"
                                ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white"
                                : "bg-white/20 dark:bg-white/10 text-gray-700 dark:text-gray-300"
                            } flex items-center justify-center text-sm font-bold shrink-0 backdrop-blur-sm`}
                          >
                            {String.fromCharCode(65 + index)}
                          </div>
                          <span className="flex-1 text-base font-medium">{option}</span>
                          {icon}
                        </div>
                      </Button>
                    </motion.div>
                  )
                })}
              </div>
              )}

              {/* Waiting for other players in multiplayer */}
              {waitingForPlayers && isMultiplayer && playerName && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="mt-6 p-6 bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-xl"
                >
                  <div className="flex flex-col items-center space-y-4">
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0s' }}></div>
                      <div className="w-3 h-3 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      <div className="w-3 h-3 bg-pink-500 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                    </div>
                    <p className="text-lg font-medium text-gray-900 dark:text-white">
                      Waiting for other players to answer...
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {players && players.length > 0 ? players.filter(p => p !== playerName).join(', ') || 'Other players' : 'Other players'} {players && players.length > 2 ? 'are' : 'is'} still thinking
                    </p>
                  </div>
                </motion.div>
              )}

              {/* Multiplayer slider section - updated styling */}
              {multiplayerGameId &&
                multiplayerRole === "player" &&
                (gameMode === "chart-position" || gameMode === "decade-challenge" || gameMode === "guess-the-year") && (
                  <motion.div
                    className="mt-6 p-6 bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl shadow-xl"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <Label htmlFor="value-slider" className="text-lg font-semibold mb-3 block text-center text-gray-900 dark:text-white">
                      {gameMode === "chart-position" ? "Guess Peak Chart Position" : gameMode === "decade-challenge" ? "Guess Decade" : "Guess Year"}
                    </Label>
                    <Slider
                      id="value-slider"
                      defaultValue={[gameMode === "chart-position" ? 50 : gameMode === "decade-challenge" ? 1970 : 1990]}
                      max={gameMode === "chart-position" ? 100 : gameMode === "decade-challenge" ? 2020 : 2024}
                      min={gameMode === "chart-position" ? 1 : gameMode === "decade-challenge" ? 1950 : 1950}
                      step={1}
                      className="my-4"
                    />
                    <p className="text-center text-2xl font-bold text-gray-900 dark:text-white">
                      {/* Display current slider value here */}
                    </p>
                    <Button
                      className="w-full mt-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg"
                    >
                      Submit Guess
                    </Button>
                    <p className="text-xs text-gray-600 dark:text-gray-400 text-center mt-2">
                      (Multiplayer slider input - simulated)
                    </p>
                  </motion.div>
                )}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Audio Mixer Panel */}
      <AnimatePresence>
        {showMixer && audioMixer && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="fixed top-0 right-0 h-full w-full sm:w-96 bg-white/95 dark:bg-gray-900/95 backdrop-blur-lg border-l border-white/20 dark:border-white/10 shadow-2xl z-50 overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">Audio Mixer</h2>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowMixer(false)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <AudioMixerPanel
                mixer={audioMixer}
                onSourceAdded={(source) => {
                  debugLog('Source added:', source)
                  toast.success(`Added audio source: ${source.id}`)
                }}
                onSourceRemoved={(sourceId) => {
                  debugLog('Source removed:', sourceId)
                  toast.success(`Removed audio source: ${sourceId}`)
                }}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Game Transitions */}
      {isTransitioning && transitionConfig && (
        <GameTransition
          {...transitionConfig}
          onComplete={hideTransition}
        />
      )}
      
      {/* Debug component for multiplayer - removed
      {isMultiplayer && process.env.NODE_ENV === 'development' && (
        <MultiplayerDebug 
          gameId={multiplayerGameId}
          playerId={playerId}
          playerName={playerName}
        />
      )}
      */}
    </div>
  )
}
