'use client';

import { useEffect, useState, useRef } from 'react';
import { X, AlertCircle } from 'lucide-react';

interface AppError {
  id: string;
  message: string;
  source: string;
  timestamp: string;
  count: number;
}

const MAX_ERRORS = 50; // Maximum number of errors to keep
const ERROR_EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes in milliseconds
const DUPLICATE_THRESHOLD = 1000; // 1 second for duplicate detection

export function ErrorMonitor() {
  const [errors, setErrors] = useState<AppError[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  const lastErrorRef = useRef<{ message: string; timestamp: number }>({ message: '', timestamp: 0 });

  useEffect(() => {
    // Auto-clear old errors every minute
    const cleanupInterval = setInterval(() => {
      const now = Date.now();
      setErrors(prev => prev.filter(error => {
        const errorTime = new Date(error.timestamp).getTime();
        return now - errorTime < ERROR_EXPIRY_TIME;
      }));
    }, 60000); // Check every minute

    // Monitor console errors
    const originalError = console.error;
    console.error = (...args) => {
      originalError(...args);
      const errorMessage = args.map(arg => {
        if (typeof arg === 'object') {
          try {
            return JSON.stringify(arg);
          } catch (e) {
            // Handle circular references or other JSON errors
            return arg instanceof Error ? arg.message : '[Object with circular reference]';
          }
        }
        return String(arg);
      }).join(' ');
      
      // Skip certain common non-critical errors
      if (shouldSkipError(errorMessage)) return;
      
      addError(errorMessage, 'console.error');
    };

    // Monitor unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const message = `Unhandled Promise Rejection: ${event.reason}`;
      if (!shouldSkipError(message)) {
        addError(message, 'promise');
      }
    };

    // Monitor window errors
    const handleError = (event: ErrorEvent) => {
      const message = `${event.message} at ${event.filename}:${event.lineno}:${event.colno}`;
      if (!shouldSkipError(message)) {
        addError(message, 'window.error');
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    // Only check for common issues once on mount
    checkCommonIssues();

    return () => {
      console.error = originalError;
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
      clearInterval(cleanupInterval);
    };
  }, []);

  const shouldSkipError = (message: string): boolean => {
    // Skip common non-critical errors
    const skipPatterns = [
      /ResizeObserver loop limit exceeded/i,
      /ResizeObserver loop completed/i,
      /Non-Error promise rejection captured/i,
      /Network request failed/i, // Often expected in offline mode
      /Failed to fetch/i, // Common during development
      /Hydration failed/i, // Common Next.js development warning
      /Text content does not match/i, // Hydration mismatch
      /Script error\./i, // Generic cross-origin errors common on mobile
      /^Script error at :0:0$/i, // Mobile script errors with no context
    ];
    
    return skipPatterns.some(pattern => pattern.test(message));
  };

  const addError = (message: string, source: string) => {
    const now = Date.now();
    
    // Check for duplicate errors within threshold
    if (
      lastErrorRef.current.message === message && 
      now - lastErrorRef.current.timestamp < DUPLICATE_THRESHOLD
    ) {
      // Update count for existing error instead of adding duplicate
      setErrors(prev => {
        const existing = prev.find(e => e.message === message);
        if (existing) {
          return prev.map(e => 
            e.id === existing.id 
              ? { ...e, count: e.count + 1 }
              : e
          );
        }
        return prev;
      });
      return;
    }
    
    lastErrorRef.current = { message, timestamp: now };
    
    setErrors(prev => {
      // Check if this exact error already exists
      const existingIndex = prev.findIndex(e => e.message === message && e.source === source);
      
      if (existingIndex !== -1) {
        // Update existing error count and timestamp
        const updated = [...prev];
        updated[existingIndex] = {
          ...updated[existingIndex],
          count: updated[existingIndex].count + 1,
          timestamp: new Date().toISOString()
        };
        return updated;
      }
      
      // Add new error
      const newError: AppError = {
        id: `${Date.now()}-${Math.random()}`,
        message,
        source,
        timestamp: new Date().toISOString(),
        count: 1
      };
      
      // Keep only the most recent MAX_ERRORS
      const updated = [...prev, newError];
      if (updated.length > MAX_ERRORS) {
        return updated.slice(-MAX_ERRORS);
      }
      return updated;
    });
    
    // Reset dismissed state when new errors arrive
    setIsDismissed(false);
  };

  const checkCommonIssues = async () => {
    const issues: Array<{ message: string; source: string }> = [];

    // Check Socket.IO connection
    try {
      const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001';
      const response = await fetch(socketUrl, { method: 'HEAD' }).catch(() => null);
      if (!response) {
        issues.push({
          message: 'Socket.IO server not reachable at ' + socketUrl,
          source: 'connection-check'
        });
      }
    } catch (e) {}

    // Check MPD proxy
    try {
      const mpdUrl = `http://${process.env.NEXT_PUBLIC_MPD_HOST || 'localhost'}:8001/status`;
      const response = await fetch(mpdUrl).catch(() => null);
      if (!response) {
        issues.push({
          message: 'MPD proxy not reachable',
          source: 'connection-check'
        });
      }
    } catch (e) {}

    // Only add issues if they exist
    issues.forEach(issue => {
      addError(issue.message, issue.source);
    });
  };

  const clearErrors = () => {
    setErrors([]);
    setIsDismissed(true);
  };

  const dismissMonitor = () => {
    setIsDismissed(true);
    setIsVisible(false);
  };

  // Don't show if no errors or dismissed
  if (errors.length === 0 || isDismissed) return null;

  return (
    <>
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-full shadow-lg z-50 flex items-center gap-2 transition-colors"
        aria-label={`Show ${errors.length} errors`}
      >
        <AlertCircle className="w-4 h-4" />
        <span className="font-medium">{errors.length}</span>
      </button>

      {isVisible && (
        <div className="fixed bottom-16 right-4 w-96 max-h-96 bg-white dark:bg-gray-800 border border-red-200 dark:border-red-800 rounded-lg shadow-xl z-50">
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-bold text-red-600 dark:text-red-400 flex items-center gap-2">
              <AlertCircle className="w-4 h-4" />
              Application Errors ({errors.length})
            </h3>
            <button
              onClick={dismissMonitor}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              aria-label="Close error monitor"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          
          <div className="overflow-auto max-h-64 p-4">
            <div className="space-y-3">
              {errors.map((error) => (
                <div key={error.id} className="text-sm border-b border-gray-100 dark:border-gray-700 pb-2">
                  <div className="flex items-center justify-between">
                    <div className="font-mono text-xs text-gray-500 dark:text-gray-400">
                      {new Date(error.timestamp).toLocaleTimeString()}
                    </div>
                    {error.count > 1 && (
                      <span className="text-xs bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 px-2 py-0.5 rounded-full">
                        ×{error.count}
                      </span>
                    )}
                  </div>
                  <div className="font-semibold text-blue-600 dark:text-blue-400 text-xs">
                    {error.source}
                  </div>
                  <div className="text-red-600 dark:text-red-400 break-words text-xs mt-1">
                    {error.message.length > 200 
                      ? error.message.substring(0, 200) + '...' 
                      : error.message
                    }
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Errors auto-clear after 5 minutes
            </span>
            <button
              onClick={clearErrors}
              className="text-sm text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
            >
              Clear All
            </button>
          </div>
        </div>
      )}
    </>
  );
}