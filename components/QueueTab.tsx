"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  ArrowUp,
  ArrowDown,
  Trash2,
  Heart,
  RefreshCw,
} from "lucide-react"
import { AlbumArt } from './album-art'
import { TextOverflowTooltip } from './ui/text-overflow-tooltip'
import type { QueuedSong } from "@/lib/types"

interface QueueTabProps {
  queue: QueuedSong[]
  isAdmin: () => boolean
  handleRemoveFromQueue: (queuePosition: number) => Promise<void>
  handleMoveInQueue: (from: number, to: number) => Promise<void>
  onRefresh?: () => Promise<void>
  isRefreshing?: boolean
}

export function QueueTab({
  queue,
  isAdmin,
  handleRemoveFromQueue,
  handleMoveInQueue,
  onRefresh,
  isRefreshing = false
}: QueueTabProps) {
  return (
    <Card className="bg-white/10 backdrop-blur-md border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between gap-2">
          <div className="flex-1 min-w-0">
            <CardTitle className="flex items-center gap-2 flex-wrap">
              <span>Up Next</span>
              {!isAdmin() && (
                <Badge variant="secondary" className="text-xs">
                  Rate Limit: 3 songs/min
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
          Songs that will be played soon.
          {isAdmin() 
            ? " As admin, you can reorder and remove songs." 
            : " You can add up to 3 songs per minute."
          }
            </CardDescription>
          </div>
          {onRefresh && (
            <Button 
              onClick={onRefresh} 
              variant="ghost" 
              size="icon" 
              disabled={isRefreshing}
              className="h-8 w-8 sm:h-9 sm:w-9 flex-shrink-0"
            >
              <RefreshCw className={`w-3 h-3 sm:w-4 sm:h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[50vh] sm:h-[60vh] max-h-[400px] sm:max-h-[500px]">
          {queue.length > 0 ? queue.map((song, index) => (
            <Card key={`queue-${song.queuePosition}-${song.id || index}`} className="p-3 bg-black/10 mb-2">
              <div className="flex items-start sm:items-center justify-between gap-2">
                <div className="flex items-start sm:items-center gap-3 min-w-0 flex-1">
                  {/* Album Art */}
                  <AlbumArt
                    trackId={song.id?.toString()}
                    fallbackUrl={song.albumArtUrl}
                    title={song.title}
                    artist={song.artist || 'Unknown Artist'}
                    album={song.album || 'Unknown Album'}
                    size="sm"
                    className="flex-shrink-0"
                    eager={index < 5}
                    priority={index === 0}
                  />
                  
                  {/* Song Info */}
                  <div className="min-w-0 flex-1">
                    <TextOverflowTooltip text={song.title}>
                      <h4 className="font-semibold line-clamp-1 break-words">{song.title}</h4>
                    </TextOverflowTooltip>
                    <TextOverflowTooltip text={song.artist || 'Unknown Artist'}>
                      <p className="text-sm text-gray-300 line-clamp-1 break-words">{song.artist}</p>
                    </TextOverflowTooltip>
                    <TextOverflowTooltip text={`Added by: ${song.addedBy}`}>
                      <p className="text-xs text-gray-400 line-clamp-1 break-words">Added by: {song.addedBy}</p>
                    </TextOverflowTooltip>
                  </div>
                  
                  {/* Queue Position */}
                  <div className="text-xs text-gray-500 font-mono bg-black/20 px-2 py-1 rounded flex-shrink-0">
                    #{song.queuePosition + 1}
                  </div>
                </div>
                
                {/* Admin Controls */}
                {isAdmin() && (
                  <div className="flex items-center gap-1 flex-shrink-0">
                    {/* Move Up */}
                    {song.queuePosition > 0 && (
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => handleMoveInQueue(song.queuePosition, song.queuePosition - 1)}
                        className="h-8 w-8 text-gray-400 hover:text-gray-300"
                        title="Move up in queue"
                      >
                        <ArrowUp className="w-4 h-4" />
                      </Button>
                    )}
                    
                    {/* Move Down */}
                    {song.queuePosition < queue.length - 1 && (
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => handleMoveInQueue(song.queuePosition, song.queuePosition + 1)}
                        className="h-8 w-8 text-gray-400 hover:text-gray-300"
                        title="Move down in queue"
                      >
                        <ArrowDown className="w-4 h-4" />
                      </Button>
                    )}
                    
                    {/* Remove */}
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => handleRemoveFromQueue(song.queuePosition)}
                      className="h-8 w-8 text-red-400 hover:text-red-300"
                      title="Remove from queue"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </div>
            </Card>
          )) : (
            <div className="text-center py-10 text-gray-500">
              <Heart className="mx-auto w-12 h-12 mb-2" />
              No songs in queue.
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}