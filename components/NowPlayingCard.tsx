"use client"

import React, { memo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON>lider } from "@/components/ui/slider"
import {
  <PERSON>,
  Pause,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Skip<PERSON>ack,
  Volume2,
  Music,
  Heart,
} from "lucide-react"
import { AlbumArt } from './album-art'
import { TextOverflowTooltip } from './ui/text-overflow-tooltip'
import { SkipVoteButton } from './skip-vote-button'
import { getClientUserInfo } from '@/lib/auth-cookies-client'
import type { QueuedSong, Song } from "@/lib/types"

interface NowPlayingCardProps {
  currentTrack: QueuedSong | null
  isPlaying: boolean
  volume: number
  progress: number
  duration: number
  onPlayPause: () => Promise<void>
  onSkip: (direction: 'forward' | 'back') => Promise<void>
  onVolumeChange: (volume: number) => Promise<void>
  onToggleFavorite?: (song: Song) => Promise<void>
  isFavorite?: (song: Song) => boolean
  isFavoriteLoading?: (songId: string) => boolean
}

const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

function NowPlayingCardComponent({
  currentTrack,
  isPlaying,
  volume,
  progress,
  duration,
  onPlayPause,
  onSkip,
  onVolumeChange,
  onToggleFavorite,
  isFavorite,
  isFavoriteLoading = false
}: NowPlayingCardProps) {
  return (
    <Card className="bg-white/10 backdrop-blur-md border-white/20 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="w-5 h-5" />
          Now Playing
        </CardTitle>
        <CardDescription>
          {currentTrack ? `${currentTrack.title} - ${currentTrack.artist}` : "Nothing is playing"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-center">
            <AlbumArt
              trackId={(currentTrack as any)?.dbId || currentTrack?.fingerprint}
              fallbackUrl={currentTrack?.albumArtUrl}
              title={currentTrack?.title}
              artist={currentTrack?.artist || 'Unknown Artist'}
              album={currentTrack?.album || 'Unknown Album'}
              size="large"
              className="shadow-lg"
              priority={true}
            />
          </div>
          {currentTrack && (
            <div className="text-center space-y-1 px-4">
              <TextOverflowTooltip text={currentTrack.title} maxLines={2}>
                <h3 className="font-bold text-lg line-clamp-2 break-words">{currentTrack.title}</h3>
              </TextOverflowTooltip>
              <TextOverflowTooltip text={currentTrack.artist || 'Unknown Artist'}>
                <p className="text-sm text-gray-400 line-clamp-1 break-words">{currentTrack.artist}</p>
              </TextOverflowTooltip>
              {currentTrack.album && currentTrack.album !== 'Unknown Album' && (
                <TextOverflowTooltip text={currentTrack.album}>
                  <p className="text-xs text-gray-500 line-clamp-1 break-words">{currentTrack.album}</p>
                </TextOverflowTooltip>
              )}
              {currentTrack.year && (
                <p className="text-xs text-gray-400">{currentTrack.year}</p>
              )}
            </div>
          )}
          <div className="space-y-1">
            <Progress value={progress} className="h-2" />
            <div className="flex justify-between text-xs text-gray-400">
              <span>{formatDuration(progress / 100 * duration)}</span>
              <span>{formatDuration(duration)}</span>
            </div>
          </div>
          <div className="flex justify-center items-center gap-4">
            <Button variant="ghost" size="icon" onClick={() => onSkip('back')}>
              <SkipBack />
            </Button>
            <Button 
              variant="default" 
              size="lg" 
              className="rounded-full w-16 h-16" 
              onClick={onPlayPause}
            >
              {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
            </Button>
            <Button variant="ghost" size="icon" onClick={() => onSkip('forward')}>
              <SkipForward />
            </Button>
          </div>
          {/* Favorite Button */}
          {currentTrack && onToggleFavorite && isFavorite && (() => {
            // Convert QueuedSong to Song for favorites API
            const songForFavorites: Song = {
              id: currentTrack.id,
              title: currentTrack.title,
              artist: currentTrack.artist,
              album: currentTrack.album,
              year: currentTrack.year,
              duration: currentTrack.duration,
              genre: currentTrack.genre || undefined,
              filePath: currentTrack.filePath,
              albumArtUrl: currentTrack.albumArtUrl
            }

            const songId = currentTrack.id || currentTrack.filePath || `${currentTrack.artist}-${currentTrack.title}`
            const isCurrentlyFavorite = isFavorite(songForFavorites)
            const isLoading = isFavoriteLoading ? isFavoriteLoading(songId) : false

            return (
              <div className="flex justify-center">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggleFavorite(songForFavorites)}
                  disabled={isLoading}
                  className="gap-2"
                >
                  <Heart 
                    className={`w-4 h-4 ${isCurrentlyFavorite ? 'fill-current text-red-500' : ''}`} 
                  />
                  {isCurrentlyFavorite ? 'Remove Favorite' : 'Add to Favorites'}
                </Button>
              </div>
            )
          })()}
          
          {/* Skip Vote Button */}
          {currentTrack && (() => {
            const userInfo = getClientUserInfo()
            if (!userInfo) return null
            
            return (
              <div className="flex justify-center py-2">
                <SkipVoteButton
                  currentTrack={{
                    id: currentTrack.id?.toString() || currentTrack.filePath,
                    title: currentTrack.title,
                    artist: currentTrack.artist
                  }}
                  userId={userInfo.id}
                  userName={userInfo.username}
                  className="w-full max-w-xs"
                />
              </div>
            )
          })()}
          
          <div className="flex items-center gap-4">
            <Volume2 className="w-5 h-5" />
            <Slider value={[volume]} onValueChange={([v]) => onVolumeChange(v)} max={100} />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Memoize with custom comparison to prevent re-renders
export const NowPlayingCard = memo(NowPlayingCardComponent, (prevProps, nextProps) => {
  // Only re-render if these specific props change
  const trackChanged = prevProps.currentTrack?.filePath !== nextProps.currentTrack?.filePath ||
                      prevProps.currentTrack?.id !== nextProps.currentTrack?.id
  const playingChanged = prevProps.isPlaying !== nextProps.isPlaying
  const volumeChanged = prevProps.volume !== nextProps.volume
  const progressChanged = Math.abs(prevProps.progress - nextProps.progress) > 1 // Only update if progress changes by more than 1%
  const durationChanged = prevProps.duration !== nextProps.duration
  
  // Don't re-render unless something significant changed
  return !trackChanged && !playingChanged && !volumeChanged && !progressChanged && !durationChanged
})