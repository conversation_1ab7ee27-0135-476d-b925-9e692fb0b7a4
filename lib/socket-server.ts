/**
 * Socket.IO Server for Multiplayer Music Quiz
 * 
 * Handles real-time multiplayer game management, player connections,
 * and game state synchronization with MPD integration.
 */

import { Server as HTTPServer } from 'http'
import { Server as SocketServer, Socket } from 'socket.io'
import { GameStore } from './game-store'
import { GameManager } from './game-manager'
import { teamSocketHandler } from './team-socket-handler'
import { teamGameIntegration } from './team-game-integration'
import type { GameState, Player, Question, Team, TeamGameSettings } from './types'

// More flexible event interfaces for type safety
interface ClientEvents {
  [event: string]: any // More flexible to handle various event shapes
}

interface ServerEvents {
  [event: string]: any // More flexible to handle various event shapes
}

interface SocketData {
  playerId?: string
  gameId?: string
  playerName?: string
}

type SocketWithData = Socket<ClientEvents, ServerEvents, {}, SocketData>

export class MultiplayerSocketServer {
  private io: SocketServer<ClientEvents, ServerEvents, {}, SocketData>
  private gameStore: GameStore
  private gameManagers: Map<string, GameManager> = new Map()
  private connectionCount: number = 0
  private ipConnectionCounts: Map<string, number> = new Map()
  private questionTimers: Map<string, NodeJS.Timeout> = new Map()

  constructor(httpServer: HTTPServer) {
    this.io = new SocketServer(httpServer, {
      cors: {
        origin: (origin, callback) => {
          // Allow all origins in development
          if (process.env.NODE_ENV !== 'production') {
            callback(null, true)
            return
          }
          
          // In production, check allowed origins
          const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || []
          if (!origin || allowedOrigins.includes(origin)) {
            callback(null, true)
          } else {
            callback(new Error('Not allowed by CORS'))
          }
        },
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 15000,
      pingInterval: 10000
    })

    this.gameStore = new GameStore()
    this.setupEventHandlers()
    
    console.log('🎮 Multiplayer Socket Server initialized')
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket: SocketWithData) => {
      console.log(`🔌 Client connected: ${socket.id}`)

      // Wrap all socket handlers with error boundary
      const wrapHandler = (handlerName: string, handler: (...args: any[]) => Promise<void>) => {
        return async (...args: any[]) => {
          try {
            await handler(...args)
          } catch (error) {
            console.error(`Socket handler error in ${handlerName}:`, {
              handler: handlerName,
              socketId: socket.id,
              playerId: socket.data.playerId,
              gameId: socket.data.gameId,
              error: error instanceof Error ? error.message : 'Unknown error',
              stack: error instanceof Error ? error.stack : undefined
            })
            socket.emit('error', { 
              message: `Failed to process ${handlerName}`,
              code: 'INTERNAL_ERROR',
              details: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : 'Unknown error') : undefined
            })
          }
        }
      }

      // Handle game creation
      socket.on('create-game', wrapHandler('create-game', async (data, callback) => {
        try {
          console.log('[SocketServer] create-game event received with data:', {
            hostId: data.hostId,
            hostName: data.hostName,
            gameMode: data.gameMode,
            settings: data.settings,
            ultimoteConfig: data.settings?.ultimoteConfig ? 'Present' : 'Missing',
            ultimoteConfigDetails: JSON.stringify(data.settings?.ultimoteConfig, null, 2)
          });

          const gameId = this.gameStore.generateGamePin();
          const settings = data.settings || {};

          console.log('[SocketServer] Creating GameManager...');
          const gameManager = new GameManager({
            gameId,
            hostId: data.hostId,
            gameMode: data.gameMode as any,
            maxPlayers: settings.maxPlayers || 6,
            timePerQuestion: settings.timePerQuestion || 30,
            totalQuestions: settings.totalQuestions || 10,
            ultimoteConfig: settings.ultimoteConfig
          });
          console.log('[SocketServer] GameManager created.');

          const host: Player = {
            id: data.hostId,
            name: data.hostName,
            avatar: '/placeholder-user.jpg',
            score: 0,
            isHost: true,
            hasAnswered: false,
            joinedAt: Date.now()
          };

          console.log('[SocketServer] Creating game in GameManager...');
          const gameState = await gameManager.createGame(host);
          console.log('[SocketServer] Game created in GameManager.');

          // Store additional settings that GameManager doesn't handle
          const enhancedGameState = {
            ...gameState,
            settings: {
              ...gameState.settings,
              ...settings,
              ultimoteConfig: settings.ultimoteConfig
            }
          };
          this.gameStore.setGame(gameId, enhancedGameState);
          this.gameManagers.set(gameId, gameManager);

          // Join host to game room
          socket.join(gameId);
          socket.data.gameId = gameId;
          socket.data.playerId = data.hostId;
          socket.data.playerName = data.hostName;

          // Send response with callback if provided
          if (callback) {
            console.log('[SocketServer] Sending create-game callback...');
            callback({
              success: true,
              gameId,
              gamePin: gameId,
              playerId: data.hostId
            });
            console.log('[SocketServer] create-game callback sent.');
          }

          socket.emit('game-created', { gameId, hostId: data.hostId });
          socket.emit('game-state', { ...enhancedGameState, pin: gameId });

          // Initialize team handlers for the host
          teamSocketHandler.handleConnection(
            socket as any,
            data.hostId,
            data.hostName,
            gameId
          );

          console.log(`🎮 Game created: ${gameId} by ${data.hostName}`);
        } catch (error) {
          console.error('Error creating game:', {
            error: error instanceof Error ? error.message : 'Unknown error',
            data,
            socketId: socket.id
          });
          socket.emit('error', { 
            message: 'Failed to create game',
            code: 'GAME_CREATION_FAILED',
            details: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : undefined) : undefined
          });
        }
      }));

      // Handle game creation with team settings (underscore version)
      socket.on('create_game', async (data, callback) => {
        try {
          const gameId = this.gameStore.generateGamePin()
          const settings = data.settings || {}
          
          // Create team settings if team mode is enabled
          let teamSettings: TeamGameSettings | undefined
          if (settings.teamMode) {
            teamSettings = {
              teamMode: true,
              teamGameMode: 'collaborative', // Default mode
              teamScoringMode: 'average', // Default scoring
              maxTeamSize: settings.maxTeamSize || 4,
              minTeamSize: 2,
              allowTeamChat: true,
              collaborationTime: 30, // 30 seconds for team discussion
              autoBalanceTeams: false // Don't auto-balance by default
            }
          }

          const gameManager = new GameManager({
            gameId,
            hostId: data.playerId,
            gameMode: settings.gameMode,
            maxPlayers: settings.maxPlayers || 6,
            timePerQuestion: settings.timePerQuestion || 30,
            teamMode: settings.teamMode || false,
            teamSettings,
            totalQuestions: settings.totalQuestions || 10
          })

          const host: Player = {
            id: data.playerId,
            name: data.playerName,
            avatar: '/placeholder-user.jpg',
            score: 0,
            isHost: true,
            hasAnswered: false,
            joinedAt: Date.now()
          }

          const gameState = await gameManager.createGame(host)
          // Store additional settings including ultimoteConfig
          const enhancedGameState = {
            ...gameState,
            settings: {
              ...gameState.settings,
              ...settings,
              ultimoteConfig: settings.ultimoteConfig
            }
          }
          this.gameStore.setGame(gameId, enhancedGameState)
          this.gameManagers.set(gameId, gameManager)

          // Join host to game room
          socket.join(gameId)
          socket.data.gameId = gameId
          socket.data.playerId = data.playerId
          socket.data.playerName = data.playerName

          // Initialize team handlers for the host
          teamSocketHandler.handleConnection(
            socket as any,
            data.playerId,
            data.playerName,
            gameId
          )

          // Send response with callback
          if (callback) {
            callback({
              success: true,
              data: {
                gameId,
                gamePin: gameId,
                teamMode: settings.teamMode
              }
            })
          }

          socket.emit('game-state', { ...enhancedGameState, pin: gameId })

          console.log(`🎮 Game created with settings: ${gameId} by ${data.playerName}`, { teamMode: settings.teamMode })
        } catch (error) {
          console.error('Error creating game with settings:', error)
          if (callback) {
            callback({
              success: false,
              message: 'Failed to create game'
            })
          }
          socket.emit('error', { message: 'Failed to create game' })
        }
      })

      // Handle joining a game
      socket.on('join-game', async (data, callback) => {
        try {
          // Support both gameId and gamePin
          const gameId = data.gameId || data.gamePin
          if (!gameId) {
            const error = { message: 'Game ID or PIN required' }
            if (callback) {
              callback({ success: false, error: error.message })
            } else {
              socket.emit('error', error)
            }
            return
          }

          const game = this.gameStore.getGame(gameId)
          if (!game) {
            const error = { message: 'Game not found' }
            if (callback) {
              callback({ success: false, error: error.message })
            } else {
              socket.emit('error', error)
            }
            return
          }

          if (game.status !== 'waiting') {
            const error = { message: 'Game already in progress' }
            if (callback) {
              callback({ success: false, error: error.message })
            } else {
              socket.emit('error', error)
            }
            return
          }

          if (game.players.length >= 6) {
            const error = { message: 'Game is full' }
            if (callback) {
              callback({ success: false, error: error.message })
            } else {
              socket.emit('error', error)
            }
            return
          }

          const gameManager = this.gameManagers.get(gameId)
          if (!gameManager) {
            const error = { message: 'Game manager not found' }
            if (callback) {
              callback({ success: false, error: error.message })
            } else {
              socket.emit('error', error)
            }
            return
          }

          // Generate player ID if not provided
          const playerId = data.playerId || `player-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

          const player: Player = {
            id: playerId,
            name: data.playerName,
            avatar: data.playerAvatar || '/placeholder-user.jpg',
            score: 0,
            isHost: false,
            hasAnswered: false,
            joinedAt: Date.now()
          }

          const updatedGame = gameManager.addPlayer(player)
          this.gameStore.setGame(gameId, updatedGame)

          // Join player to game room
          socket.join(gameId)
          socket.data.gameId = gameId
          socket.data.playerId = playerId
          socket.data.playerName = data.playerName

          // Send callback response if provided
          if (callback) {
            callback({
              success: true,
              gameId,
              playerId,
              players: updatedGame.players
            })
          }

          // Notify all players
          this.io.to(gameId).emit('player-joined', { 
            player, 
            players: updatedGame.players 
          })
          this.io.to(gameId).emit('game-state', { ...updatedGame, pin: gameId })

          // Initialize team handlers for the joined player
          teamSocketHandler.handleConnection(
            socket as any,
            data.playerId,
            data.playerName,
            data.gameId
          )

          console.log(`👤 Player ${data.playerName} joined game ${data.gameId}`)
        } catch (error) {
          console.error('Error joining game:', error)
          socket.emit('error', { message: 'Failed to join game' })
        }
      })

      // Handle game start
      socket.on('start-game', async (data, callback) => {
        try {
          const game = this.gameStore.getGame(data.gameId)
          if (!game) {
            socket.emit('error', { message: 'Game not found' })
            if (callback) callback({ success: false, error: 'Game not found' })
            return
          }

          const player = game.players.find(p => p.id === socket.data.playerId)
          if (!player?.isHost) {
            socket.emit('error', { message: 'Only host can start the game' })
            if (callback) callback({ success: false, error: 'Only host can start the game' })
            return
          }

          const gameManager = this.gameManagers.get(data.gameId)
          if (!gameManager) {
            socket.emit('error', { message: 'Game manager not found' })
            if (callback) callback({ success: false, error: 'Game manager not found' })
            return
          }

          // Send immediate callback response
          if (callback) callback({ success: true })

          // Start countdown
          const countdownTime = 3
          this.io.to(data.gameId).emit('game-countdown', {
            countdownTime,
            totalQuestions: game.totalQuestions,
            serverTime: Date.now()
          })

          // Start game after countdown
          setTimeout(async () => {
            try {
              let startedGame = gameManager.startGame()
              
              // Get the enhanced game state from store to preserve settings
              const storedGame = this.gameStore.getGame(data.gameId)
              const enhancedStartedGame = {
                ...startedGame,
                settings: storedGame?.settings || {
                  maxPlayers: 6,
                  allowSpectators: false,
                  autoNextQuestion: true
                },
                gameMode: storedGame?.gameMode || startedGame.gameMode
              }
              
              this.gameStore.setGame(data.gameId, enhancedStartedGame)

              if (startedGame.currentQuestion) {
                this.io.to(data.gameId).emit('game-started', {
                  question: startedGame.currentQuestion,
                  questionIndex: startedGame.currentQuestionIndex,
                  timeLimit: startedGame.timePerQuestion,
                  serverTime: Date.now()
                })
                this.io.to(data.gameId).emit('game-state', { ...enhancedStartedGame, pin: data.gameId })
                
                // Start question timer
                this.startQuestionTimer(data.gameId, startedGame.timePerQuestion)
              }
            } catch (error) {
              console.error('Error starting game:', error)
              socket.emit('error', { message: 'Failed to start game' })
            }
          }, countdownTime * 1000)

          console.log(`🚀 Game ${data.gameId} started by ${socket.data.playerName}`)
        } catch (error) {
          console.error('Error starting game:', error)
          socket.emit('error', { message: 'Failed to start game' })
          if (callback) callback({ success: false, error: 'Failed to start game' })
        }
      })

      // Handle answer submission
      socket.on('submit-answer', async (data, callback) => {
        try {
          const game = this.gameStore.getGame(data.gameId)
          if (!game) {
            socket.emit('error', { message: 'Game not found' })
            return
          }

          const gameManager = this.gameManagers.get(data.gameId)
          if (!gameManager) {
            socket.emit('error', { message: 'Game manager not found' })
            return
          }

          const updatedGame = gameManager.submitAnswer(data.playerId, data.answerIndex, data.timeTaken)
          this.gameStore.setGame(data.gameId, updatedGame)

          // Check if answer is correct and calculate points
          const currentQuestion = updatedGame.currentQuestion
          const isCorrect = currentQuestion && data.answerIndex === currentQuestion.correctAnswer
          let points = 0
          
          if (isCorrect && currentQuestion) {
            // Calculate points based on time taken (max 1000 points)
            const timeRatio = Math.max(0, 1 - (data.timeTaken / currentQuestion.timeLimit))
            points = Math.round(1000 * timeRatio)
          }

          // Send response WITHOUT revealing if answer is correct
          if (callback) {
            callback({
              success: true,
              // Don't reveal correctness or points yet
              acknowledged: true
            })
          }

          // Also acknowledge answer submission without revealing results
          socket.emit('answer-submitted', {
            acknowledged: true,
            questionIndex: updatedGame.currentQuestionIndex,
            answerIndex: data.answerIndex
          })

          // Update game state for all players
          this.io.to(data.gameId).emit('game-state', { ...updatedGame, pin: data.gameId })

          // Check if all players have answered
          const allAnswered = updatedGame.players.every(p => p.hasAnswered)
          if (allAnswered && updatedGame.currentQuestion) {
            // Cancel the timer since everyone answered
            const timer = this.questionTimers.get(data.gameId)
            if (timer) {
              clearTimeout(timer)
              this.questionTimers.delete(data.gameId)
            }
            
            // Process results after a short delay
            setTimeout(() => {
              const gameWithResults = gameManager.processQuestionResults()
              this.gameStore.setGame(data.gameId, gameWithResults)

              // Get detailed results
              let results = null
              
              this.io.to(data.gameId).emit('question-results', {
                questionIndex: gameWithResults.currentQuestionIndex,
                correctAnswerIndex: gameWithResults.currentQuestion?.correctAnswer || 0,
                correctAnswerText: results?.correctAnswerText || 
                                 gameWithResults.currentQuestion?.options[gameWithResults.currentQuestion?.correctAnswer || 0],
                leaderboard: gameWithResults.players.sort((a, b) => b.score - a.score),
                resultsStartTime: Date.now(),
                playerAnswers: results?.playerAnswers
              })
              
              // Auto-advance to next question after 5 seconds
              if (gameWithResults.settings?.autoNextQuestion !== false) {
                setTimeout(() => {
                  this.handleNextAction({ gameId: data.gameId }).catch(error => {
                    console.error('Error auto-advancing to next question:', error)
                  })
                }, 5000)
              }
            }, 1000)
          }

          console.log(`✅ Answer submitted by ${data.playerId} in game ${data.gameId}`)
        } catch (error) {
          console.error('Error submitting answer:', error)
          socket.emit('error', { message: 'Failed to submit answer' })
        }
      })

      // Handle next question/action
      socket.on('next-action', async (data) => {
        await this.handleNextAction(data)
      })

      // Handle player leaving
      socket.on('leave-game', (data) => {
        this.handlePlayerLeave(socket, data.gameId, data.playerId)
      })

      // Team event handlers
      socket.on('toggle-team-mode', (data) => {
        try {
          const { gameId, enabled, settings } = data
          const game = this.gameStore.getGame(gameId)
          if (!game) {
            socket.emit('error', { message: 'Game not found' })
            return
          }

          // Only host can toggle team mode
          if (socket.data.playerId !== game.hostId) {
            socket.emit('error', { message: 'Only host can toggle team mode' })
            return
          }

          // Update game with team mode settings using GameManager
          const gameManager = this.gameManagers.get(gameId)
          let updatedGame
          if (gameManager) {
            updatedGame = gameManager.toggleTeamMode(enabled, settings)
            this.gameStore.setGame(gameId, updatedGame)
          } else {
            // Fallback if GameManager not found
            updatedGame = { ...game, teamMode: enabled, teamSettings: settings }
            this.gameStore.setGame(gameId, updatedGame)
          }

          // Notify all players
          this.io.to(gameId).emit('team-mode-toggled', { enabled, settings })
          this.io.to(gameId).emit('game-state', { ...updatedGame, pin: gameId })

          console.log(`🏆 Team mode ${enabled ? 'enabled' : 'disabled'} for game ${gameId}`)
        } catch (error) {
          console.error('Error toggling team mode:', error)
          socket.emit('error', { message: 'Failed to toggle team mode' })
        }
      })

      // Handle getting available games
      socket.on('get-available-games', () => {
        try {
          const availableGames = this.gameStore.getGamesByStatus('waiting')
          const gameList = availableGames.map(game => ({
            gameId: game.gameId,
            pin: game.gameId, // The gameId is the PIN
            hostName: game.players.find(p => p.isHost)?.name || 'Unknown',
            gameMode: game.gameMode,
            playerCount: game.players.length,
            maxPlayers: game.settings.maxPlayers || 6,
            teamMode: game.teamMode || false
          }))
          
          socket.emit('available-games', { games: gameList })
          console.log(`📋 Sent ${gameList.length} available games to ${socket.id}`)
        } catch (error) {
          console.error('Error getting available games:', error)
          socket.emit('error', { message: 'Failed to get available games' })
        }
      })

      // Handle game state requests
      socket.on('request-game-state', (data) => {
        try {
          const { gameId, playerId } = data
          if (!gameId) {
            socket.emit('error', { message: 'Game ID required' })
            return
          }

          const game = this.gameStore.getGame(gameId)
          if (!game) {
            // Clear client's stored game info if game doesn't exist
            socket.emit('error', { message: 'Game not found' })
            console.log(`🔍 Game state requested for non-existent game: ${gameId}`)
            return
          }

          // Verify player is in the game
          const player = game.players.find(p => p.id === playerId)
          if (!player) {
            socket.emit('error', { message: 'Player not in game' })
            return
          }

          // Join the socket to the game room if not already joined
          if (!socket.rooms.has(gameId)) {
            socket.join(gameId)
            socket.data.gameId = gameId
            socket.data.playerId = playerId
            socket.data.playerName = player.name
          }

          // Send the current game state
          socket.emit('game-state', { ...game, pin: gameId })
          console.log(`📊 Sent game state for ${gameId} to player ${playerId}`)
        } catch (error) {
          console.error('Error handling game state request:', error)
          socket.emit('error', { message: 'Failed to get game state' })
        }
      })

      // Team handlers will be attached when player joins/creates a game

      // Handle disconnection
      socket.on('disconnect', async () => {
        // Update connection counts
        this.connectionCount--
        const clientIP = socket.handshake.address || 'unknown'
        const ipCount = this.ipConnectionCounts.get(clientIP) || 0
        if (ipCount > 1) {
          this.ipConnectionCounts.set(clientIP, ipCount - 1)
        } else {
          this.ipConnectionCounts.delete(clientIP)
        }
        
        console.log(`🔌 Client disconnected: ${socket.id} (${this.connectionCount} remaining connections)`)
        if (socket.data.gameId && socket.data.playerId) {
          try {
            await this.handlePlayerLeave(socket, socket.data.gameId, socket.data.playerId)
            
            // Handle team disconnection
            await teamSocketHandler.handleDisconnection(socket.data.playerId, socket.data.gameId)
          } catch (error) {
            console.error('Error during disconnect cleanup:', {
              socketId: socket.id,
              playerId: socket.data.playerId,
              gameId: socket.data.gameId,
              error: error instanceof Error ? error.message : 'Unknown error'
            })
          }
        }
      })
    })
  }

  private async handlePlayerLeave(socket: SocketWithData, gameId: string, playerId: string): Promise<void> {
    try {
      const game = this.gameStore.getGame(gameId)
      if (!game) return

      const gameManager = this.gameManagers.get(gameId)
      if (!gameManager) return

      const updatedGame = gameManager.removePlayer(playerId)
      
      if (updatedGame.players.length === 0) {
        // No players left, delete game
        this.gameStore.deleteGame(gameId)
        this.gameManagers.delete(gameId)
        console.log(`🗑️ Game ${gameId} deleted - no players remaining`)
      } else {
        this.gameStore.setGame(gameId, updatedGame)
        
        // Notify remaining players
        this.io.to(gameId).emit('player-left', {
          playerId,
          players: updatedGame.players
        })
        this.io.to(gameId).emit('game-state', { ...updatedGame, pin: gameId })
      }

      socket.leave(gameId)
      console.log(`👋 Player ${playerId} left game ${gameId}`)
    } catch (error) {
      console.error('Error handling player leave:', {
        gameId,
        playerId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      })
      // Try to notify other players even if there was an error
      try {
        this.io.to(gameId).emit('player-left', {
          playerId,
          players: []
        })
      } catch (notifyError) {
        console.error('Failed to notify players about disconnect:', notifyError)
      }
    }
  }

  private async handleNextAction(data: { gameId: string }): Promise<void> {
    try {
      const game = this.gameStore.getGame(data.gameId)
      if (!game) return

      const gameManager = this.gameManagers.get(data.gameId)
      if (!gameManager) return

      if (game.currentQuestionIndex < game.totalQuestions - 1) {
        // Next question
        const nextGame = gameManager.nextQuestion()
        this.gameStore.setGame(data.gameId, nextGame)

        if (nextGame.currentQuestion) {
          this.io.to(data.gameId).emit('question', {
            question: nextGame.currentQuestion,
            questionNumber: nextGame.currentQuestionIndex + 1,
            totalQuestions: nextGame.totalQuestions,
            timeLimit: nextGame.timePerQuestion,
            serverTime: Date.now(),
            endTime: Date.now() + (nextGame.timePerQuestion * 1000)
          })
          this.io.to(data.gameId).emit('game-state', { ...nextGame, pin: data.gameId })
          
          // Start timer for the new question
          this.startQuestionTimer(data.gameId, nextGame.timePerQuestion)
        }
      } else {
        // Game over
        const finishedGame = gameManager.endGame()
        this.gameStore.setGame(data.gameId, finishedGame)

        // Get final results including team leaderboard if applicable
        const finalResults: any = {
          leaderboard: finishedGame.players.sort((a, b) => b.score - a.score),
          gameStats: gameManager.calculateGameStats()
        }
        
        if (finishedGame.teamMode && gameManager) {
          // Team leaderboard functionality not available in base GameManager
        }

        this.io.to(data.gameId).emit('game-over', finalResults)

        // Clear any remaining timers
        const timer = this.questionTimers.get(data.gameId)
        if (timer) {
          clearTimeout(timer)
          this.questionTimers.delete(data.gameId)
        }

        // Clean up after game ends
        setTimeout(() => {
          // Cleanup if needed
          this.gameStore.deleteGame(data.gameId)
          this.gameManagers.delete(data.gameId)
          this.questionTimers.delete(data.gameId)
        }, 30000) // 30 seconds to view results
      }
    } catch (error) {
      console.error('Error handling next action:', error)
    }
  }

  private startQuestionTimer(gameId: string, questionTime: number): void {
    // Clear any existing timer for this game
    const existingTimer = this.questionTimers.get(gameId)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // Set a new timer
    const timer = setTimeout(() => {
      console.log(`[SocketServer] Question timer expired for game ${gameId}`)
      this.handleQuestionTimeout(gameId)
    }, questionTime * 1000)

    this.questionTimers.set(gameId, timer)
  }

  private handleQuestionTimeout(gameId: string): void {
    try {
      const game = this.gameStore.getGame(gameId)
      if (!game || game.status !== 'playing') return

      const gameManager = this.gameManagers.get(gameId)
      if (!gameManager) return

      // Process results even if not all players answered
      const gameWithResults = gameManager.processQuestionResults()
      this.gameStore.setGame(gameId, gameWithResults)

      // Emit results
      this.io.to(gameId).emit('question-results', {
        questionIndex: gameWithResults.currentQuestionIndex,
        correctAnswerIndex: gameWithResults.currentQuestion?.correctAnswer || 0,
        correctAnswerText: gameWithResults.currentQuestion?.options[gameWithResults.currentQuestion?.correctAnswer || 0],
        leaderboard: gameWithResults.players.sort((a, b) => b.score - a.score),
        resultsStartTime: Date.now(),
        timedOut: true
      })

      // Auto-advance to next question after showing results
      setTimeout(() => {
        this.handleNextAction({ gameId }).catch(error => {
          console.error('Error auto-advancing after timeout:', error)
        })
      }, 5000)
    } catch (error) {
      console.error('Error handling question timeout:', error)
    }
  }

  public getStats() {
    return {
      connectedClients: this.io.sockets.sockets.size,
      activeGames: this.gameStore.getAvailableGames().length,
      games: this.gameStore.getAvailableGames().map(game => ({
        id: game.gameId,
        status: game.status,
        players: game.players.length,
        host: game.players.find(p => p.isHost)?.name
      }))
    }
  }
}

// Initialize server function
export function initializeSocketServer(httpServer: HTTPServer): MultiplayerSocketServer {
  return new MultiplayerSocketServer(httpServer)
} 