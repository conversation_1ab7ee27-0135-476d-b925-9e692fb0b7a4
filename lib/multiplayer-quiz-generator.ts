/**
 * Multiplayer Quiz Generator
 * Generates quiz questions from the music library for multiplayer games
 */

import { PrismaClient, Prisma } from '@prisma/client'

import type { QuizTrack } from '@prisma/client'
import { ContentFilterService } from './services/content-filter-service'
import type { ContentFilters } from './types/filters'

export interface MultiplayerQuestion {
  id: string
  type: 'multiple-choice' | 'slider' | 'visual'
  question: string
  options?: string[]
  correctAnswer: string | number
  category: string
  timeLimit: number
  explanation?: string
  track?: {
    id: number
    file: string
    title: string
    artist: string
    album?: string
    year?: number
    previewStart?: number
    albumArtUrl?: string
    genre?: string
    duration?: number
    popularityScore?: number
    chartPosition?: number
    releaseDate?: string
    triviaFacts?: string[]
    interestingFacts?: any
    calculatedGain?: number | null
    lufsVolume?: number | null
  }
  minValue?: number // For slider questions
  maxValue?: number // For slider questions
}

export class MultiplayerQuizGenerator {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  /**
   * Create a track object for a question with all necessary fields including volume normalization
   */
  private createTrackObject(track: QuizTrack): MultiplayerQuestion['track'] {
    return {
      id: parseInt(track.id) || 0, // Convert string to number
      file: track.mpdFilePath,
      title: track.title!,
      artist: track.artist!,
      album: track.album || undefined,
      year: track.year || undefined,
      albumArtUrl: track.albumArtUrl || undefined,
      genre: track.genre || undefined,
      duration: track.duration || undefined,
      popularityScore: track.popularityScore || undefined,
      chartPosition: track.chartPosition || undefined,
      releaseDate: track.releaseDate ? track.releaseDate.toISOString().split('T')[0] : undefined, // Convert Date to string
      triviaFacts: track.triviaFacts ? (typeof track.triviaFacts === 'string' ? JSON.parse(track.triviaFacts) : track.triviaFacts) : undefined,
      interestingFacts: track.interestingFacts ? (typeof track.interestingFacts === 'string' ? JSON.parse(track.interestingFacts) : track.interestingFacts) : undefined,
      calculatedGain: track.calculatedGain,
      lufsVolume: track.lufsVolume
    }
  }

  async generateQuiz(settings: {
    gameMode: string
    totalQuestions: number
    timePerQuestion?: number
    filters?: ContentFilters
    ultimoteConfig?: any
  }): Promise<MultiplayerQuestion[]> {
    console.log('[MultiplayerQuizGenerator] generateQuiz called with:', {
      gameMode: settings.gameMode,
      totalQuestions: settings.totalQuestions,
      timePerQuestion: settings.timePerQuestion,
      hasFilters: !!settings.filters,
      hasUltimoteConfig: !!settings.ultimoteConfig
    })
    
    if (settings.ultimoteConfig) {
      console.log('[MultiplayerQuizGenerator] ultimoteConfig:', JSON.stringify(settings.ultimoteConfig, null, 2))
    }
    
    try {
      // First check if we have tracks in the database
      const trackCount = await this.prisma.quizTrack.count()
      
      if (trackCount === 0) {
        console.error('[MultiplayerQuizGenerator] No tracks in database')
        throw new Error('No tracks found in database. Please ensure your music library is imported.')
      }
      
      // Generate questions directly from the database
      return await this.generateDatabaseQuestions(settings)
    } catch (error) {
      console.error('[MultiplayerQuizGenerator] Error generating quiz:', error)
      // Re-throw the error instead of using fallback
      throw error
    }
  }

  private async generateDatabaseQuestions(settings: {
    gameMode: string
    totalQuestions: number
    timePerQuestion?: number
    filters?: ContentFilters
    ultimoteConfig?: any
  }): Promise<MultiplayerQuestion[]> {
    console.log(`[MultiplayerQuizGenerator] Generating ${settings.totalQuestions} questions for ${settings.gameMode}`)
    console.log('[MultiplayerQuizGenerator] Filters:', settings.filters ? 'Applied' : 'None')
    
    // Build where clause based on filters
    const where = this.buildWhereClause(settings.filters)
    console.log('[MultiplayerQuizGenerator] Final where clause:', JSON.stringify(where, null, 2))
    
    // First, let's count how many tracks match our criteria
    const totalMatchingTracks = await this.prisma.quizTrack.count({ where })
    console.log(`[MultiplayerQuizGenerator] Total matching tracks in database: ${totalMatchingTracks}`)
    
    if (totalMatchingTracks === 0) {
      // Get some debug info
      const totalTracks = await this.prisma.quizTrack.count()
      const tracksWithArtist = await this.prisma.quizTrack.count({ where: { artist: { not: null } } })
      const tracksWithFile = await this.prisma.quizTrack.count({ where: { mpdFilePath: { not: '' } } })
      
      console.error('[MultiplayerQuizGenerator] No tracks match the filter criteria!')
      console.error(`[MultiplayerQuizGenerator] Debug info:`)
      console.error(`  - Total tracks in DB: ${totalTracks}`)
      console.error(`  - Tracks with artist: ${tracksWithArtist}`)
      console.error(`  - Tracks with file path: ${tracksWithFile}`)
      console.error(`  - Applied filters:`, JSON.stringify(settings.filters, null, 2))
      
      throw new Error(`No tracks match the current filter criteria. Total tracks: ${totalTracks}, but 0 match your filters.`)
    }
    
    // Get random tracks from database with good artist diversity
    console.log('[MultiplayerQuizGenerator] Executing findMany with take:', Math.max(200, settings.totalQuestions * 20))
    const allTracks = await this.prisma.quizTrack.findMany({
      take: Math.max(500, settings.totalQuestions * 50), // Get many more tracks for better diversity
      orderBy: [
        { popularityScore: 'desc' },
        { artist: 'asc' }
      ],
      where
    }).catch(error => {
      console.error('[MultiplayerQuizGenerator] FindMany query error:', error)
      throw new Error(`Database query failed: ${error.message}`)
    })
    
    // Tracks already have calculatedGain from volume analysis
    // No need to recalculate - just use the existing data
    const tracksWithGain = allTracks
    
    // Shuffle tracks to add randomness while maintaining artist diversity
    const tracks = this.shuffleArray(tracksWithGain)
    
    console.log(`[MultiplayerQuizGenerator] Fetched ${tracks.length} tracks with ${new Set(tracks.map(t => t.artist)).size} unique artists`)
    
    if (tracks.length < 4) {
      console.error(`[MultiplayerQuizGenerator] Not enough tracks found: ${tracks.length} < 4. Need more tracks in database.`)
      console.error(`[MultiplayerQuizGenerator] Total matching in DB: ${totalMatchingTracks}, but only fetched: ${tracks.length}`)
      throw new Error(`Not enough tracks in database. Found ${tracks.length} but need at least 4.`)
    }
    
    // Ensure we have enough tracks for the number of questions
    if (tracks.length < settings.totalQuestions) {
      console.warn(`[MultiplayerQuizGenerator] Limited tracks: ${tracks.length} tracks for ${settings.totalQuestions} questions`)
    }
    
    const questions: MultiplayerQuestion[] = []
    const usedTrackIds = new Set<string>()
    
    for (let i = 0; i < settings.totalQuestions; i++) {
      // Get a track that hasn't been used yet
      const track = tracks.find(t => !usedTrackIds.has(t.id))
      if (!track) {
        console.error(`[MultiplayerQuizGenerator] No more unused tracks at question ${i + 1}/${settings.totalQuestions}`)
        break
      }
      
      usedTrackIds.add(track.id)
      
      let question: MultiplayerQuestion
      
      switch (settings.gameMode) {
        case 'ultimote':
          // For ultimote, delegate to a separate method to handle the complex logic
          return await this.generateUltimoteQuestionsInternal(settings, tracks)
          
        case 'classic':
          // Generate "Who is the artist?" question
          let wrongArtists: string[] = []
          
          // First, try to use similar artists from the database
          try {
            const similarArtistsJson = track.similarArtists || '[]'
            const similarArtists = JSON.parse(similarArtistsJson) as string[]
            
            if (similarArtists.length > 0) {
              console.log(`[MultiplayerQuizGenerator] Using similar artists for ${track.artist}: ${similarArtists.join(', ')}`)
              wrongArtists = this.shuffleArray(similarArtists).slice(0, 3)
            }
          } catch (e) {
            console.warn('[MultiplayerQuizGenerator] Failed to parse similar artists:', e)
          }
          
          // If we don't have enough similar artists, use other artists from the tracks
          if (wrongArtists.length < 3) {
            const allArtists = tracks
              .filter(t => t.artist && t.artist !== track.artist && !wrongArtists.includes(t.artist!))
              .map(t => t.artist!)
            
            const uniqueArtists = Array.from(new Set(allArtists))
            const additionalArtists = this.shuffleArray(uniqueArtists).slice(0, 3 - wrongArtists.length)
            wrongArtists = [...wrongArtists, ...additionalArtists]
          }
          
          // Only use fake artists if we really don't have enough real ones
          while (wrongArtists.length < 3) {
            console.warn(`[MultiplayerQuizGenerator] Only found ${wrongArtists.length} artists for question, using fallback`)
            const fakeArtist = `Unknown Artist ${wrongArtists.length + 1}`
            wrongArtists.push(fakeArtist)
          }
          
          question = {
            id: `mp_${Date.now()}_${i}`,
            type: 'multiple-choice',
            question: `Who is the artist?`,
            options: this.shuffleArray([track.artist!, ...wrongArtists.slice(0, 3)]),
            correctAnswer: track.artist!,
            category: 'Classic',
            timeLimit: settings.timePerQuestion || 30,
            track: this.createTrackObject(track)
          }
          break
          
        case 'guess_the_year':
          // Generate year question - don't reveal the artist!
          const correctYear = track.year || 2000
          const yearOptions = [
            correctYear,
            correctYear - 2,
            correctYear + 3,
            correctYear - 5
          ].sort()
          
          question = {
            id: `mp_${Date.now()}_${i}`,
            type: 'multiple-choice',
            question: `What year was this song released?`,
            options: yearOptions.map(y => y.toString()),
            correctAnswer: correctYear.toString(),
            category: 'Guess the Year',
            timeLimit: settings.timePerQuestion || 30,
            track: this.createTrackObject(track)
          }
          break
          
        case 'chart_position':
          // Generate chart position question
          const chartPos = track.chartPosition || Math.floor(Math.random() * 100) + 1
          const chartOptions = [
            chartPos,
            Math.max(1, chartPos - 10),
            Math.min(100, chartPos + 15),
            Math.max(1, chartPos - 25)
          ].sort((a, b) => a - b)
          
          question = {
            id: `mp_${Date.now()}_${i}`,
            type: 'multiple-choice',
            question: `What was the highest chart position of this song?`,
            options: chartOptions.map(p => `#${p}`),
            correctAnswer: `#${chartPos}`,
            category: 'Chart Position',
            timeLimit: settings.timePerQuestion || 30,
            track: this.createTrackObject(track)
          }
          break
          
        case 'genre_specialist':
          // Generate genre question
          const genres = ['Rock', 'Pop', 'Hip-Hop', 'R&B', 'Electronic', 'Country', 'Jazz', 'Blues', 'Folk', 'Alternative']
          const correctGenre = track.genre || genres[Math.floor(Math.random() * genres.length)]
          const wrongGenres = genres.filter(g => g !== correctGenre)
          const genreOptions = [correctGenre, ...this.shuffleArray(wrongGenres).slice(0, 3)]
          
          question = {
            id: `mp_${Date.now()}_${i}`,
            type: 'multiple-choice',
            question: `What genre is this song?`,
            options: this.shuffleArray(genreOptions),
            correctAnswer: correctGenre,
            category: 'Genre Specialist',
            timeLimit: settings.timePerQuestion || 30,
            track: this.createTrackObject(track)
          }
          break
          
        case 'decade_challenge':
          // Generate decade question
          const year = track.year || 2000
          const correctDecade = Math.floor(year / 10) * 10
          const decades = ['1960s', '1970s', '1980s', '1990s', '2000s', '2010s', '2020s']
          const correctDecadeStr = `${correctDecade}s`
          const wrongDecades = decades.filter(d => d !== correctDecadeStr)
          const decadeOptions = [correctDecadeStr, ...this.shuffleArray(wrongDecades).slice(0, 3)]
          
          question = {
            id: `mp_${Date.now()}_${i}`,
            type: 'multiple-choice',
            question: `What decade is this song from?`,
            options: this.shuffleArray(decadeOptions),
            correctAnswer: correctDecadeStr,
            category: 'Decade Challenge',
            timeLimit: settings.timePerQuestion || 30,
            track: this.createTrackObject(track)
          }
          break
          
        case 'audio_manipulation':
          // Generate audio manipulation question - still ask about artist but with audio effects
          let audioWrongArtists: string[] = []
          
          // Use similar artists for audio manipulation mode too
          try {
            const similarArtistsJson = track.similarArtists || '[]'
            const similarArtists = JSON.parse(similarArtistsJson) as string[]
            
            if (similarArtists.length > 0) {
              audioWrongArtists = this.shuffleArray(similarArtists).slice(0, 3)
            }
          } catch (e) {
            console.warn('[MultiplayerQuizGenerator] Failed to parse similar artists:', e)
          }
          
          // If we don't have enough similar artists, use other artists from the tracks
          if (audioWrongArtists.length < 3) {
            const audioAllArtists = tracks
              .filter(t => t.artist && t.artist !== track.artist && !audioWrongArtists.includes(t.artist!))
              .map(t => t.artist!)
            
            const audioUniqueArtists = Array.from(new Set(audioAllArtists))
            const additionalArtists = this.shuffleArray(audioUniqueArtists).slice(0, 3 - audioWrongArtists.length)
            audioWrongArtists = [...audioWrongArtists, ...additionalArtists]
          }
          
          while (audioWrongArtists.length < 3) {
            const fakeArtist = `Unknown Artist ${audioWrongArtists.length + 1}`
            audioWrongArtists.push(fakeArtist)
          }
          
          question = {
            id: `mp_${Date.now()}_${i}`,
            type: 'multiple-choice',
            question: `Who is the artist? (Audio effects applied)`,
            options: this.shuffleArray([track.artist!, ...audioWrongArtists.slice(0, 3)]),
            correctAnswer: track.artist!,
            category: 'Audio Manipulation',
            timeLimit: settings.timePerQuestion || 30,
            track: this.createTrackObject(track)
          }
          break
          
        default:
          // Default to classic mode - use similar artists
          let defaultWrongArtists: string[] = []
          
          // First, try to use similar artists from the database
          try {
            const similarArtistsJson = track.similarArtists || '[]'
            const similarArtists = JSON.parse(similarArtistsJson) as string[]
            
            if (similarArtists.length > 0) {
              console.log(`[MultiplayerQuizGenerator] Using similar artists for ${track.artist}: ${similarArtists.join(', ')}`)
              defaultWrongArtists = this.shuffleArray(similarArtists).slice(0, 3)
            }
          } catch (e) {
            console.warn('[MultiplayerQuizGenerator] Failed to parse similar artists:', e)
          }
          
          // If we don't have enough similar artists, use other artists from the tracks
          if (defaultWrongArtists.length < 3) {
            const defaultAllArtists = tracks
              .filter(t => t.artist && t.artist !== track.artist && !defaultWrongArtists.includes(t.artist!))
              .map(t => t.artist!)
            
            const defaultUniqueArtists = Array.from(new Set(defaultAllArtists))
            const additionalArtists = this.shuffleArray(defaultUniqueArtists).slice(0, 3 - defaultWrongArtists.length)
            defaultWrongArtists = [...defaultWrongArtists, ...additionalArtists]
          }
          
          while (defaultWrongArtists.length < 3) {
            console.warn(`[MultiplayerQuizGenerator] Only found ${defaultWrongArtists.length} artists for default question, using fallback`)
            const fakeArtist = `Unknown Artist ${defaultWrongArtists.length + 1}`
            defaultWrongArtists.push(fakeArtist)
          }
          
          question = {
            id: `mp_${Date.now()}_${i}`,
            type: 'multiple-choice',
            question: `Who is the artist?`,
            options: this.shuffleArray([track.artist!, ...defaultWrongArtists.slice(0, 3)]),
            correctAnswer: track.artist!,
            category: 'Classic',
            timeLimit: settings.timePerQuestion || 30,
            track: this.createTrackObject(track)
          }
      }
      
      questions.push(question)
    }
    
    console.log(`[MultiplayerQuizGenerator] Generated ${questions.length} questions`)
    
    // If we couldn't generate enough questions, throw error
    if (questions.length === 0) {
      console.error('[MultiplayerQuizGenerator] Failed to generate any questions')
      throw new Error('Failed to generate quiz questions. Please check your music library.')
    }
    
    return questions
  }
  
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  private buildWhereClause(filters?: ContentFilters): Prisma.QuizTrackWhereInput {
    const where: Prisma.QuizTrackWhereInput = {
      artist: { not: null },
      title: { not: null },
      mpdFilePath: { not: '' }
    }

    console.log('[MultiplayerQuizGenerator] Building where clause with filters:', filters ? 'YES' : 'NO')

    if (!filters) {
      console.log('[MultiplayerQuizGenerator] No filters, using basic where clause')
      return where
    }
    
    console.log('[MultiplayerQuizGenerator] Filter details:', JSON.stringify({
      genres: filters.genres,
      yearRange: filters.yearRange,
      charts: filters.charts,
      quality: filters.quality,
      metadata: filters.metadata,
      folders: filters.folders,
      playlists: filters.playlists
    }, null, 2))

    // Apply genre filters
    if (filters.genres.values.length > 0) {
      if (filters.genres.mode === 'include') {
        where.genre = { in: filters.genres.values }
      } else {
        where.genre = { notIn: filters.genres.values }
      }
    }

    // Apply year range filter
    if (filters.yearRange.enabled) {
      const yearConditions: any = {}
      if (filters.yearRange.min) {
        yearConditions.gte = filters.yearRange.min
      }
      if (filters.yearRange.max) {
        yearConditions.lte = filters.yearRange.max
      }
      if (Object.keys(yearConditions).length > 0) {
        where.year = yearConditions
      }
    }

    // Apply chart filters
    console.log('[MultiplayerQuizGenerator] Chart filter settings:', {
      includeChartMusic: filters.charts.includeChartMusic,
      includeNonChartMusic: filters.charts.includeNonChartMusic
    })
    
    if (!filters.charts.includeChartMusic && filters.charts.includeNonChartMusic) {
      console.log('[MultiplayerQuizGenerator] Filtering: Non-chart music only')
      where.chartPosition = null
    } else if (filters.charts.includeChartMusic && !filters.charts.includeNonChartMusic) {
      console.log('[MultiplayerQuizGenerator] Filtering: Chart music only')
      where.chartPosition = { not: null }
    } else {
      console.log('[MultiplayerQuizGenerator] Filtering: Both chart and non-chart music')
    }
    
    // Apply chart country filter
    if (filters.charts.countries && filters.charts.countries.length > 0) {
      where.chartCountry = { in: filters.charts.countries }
    }

    // Apply quality filters
    if (filters.quality.minDifficulty !== undefined) {
      where.difficultyRating = { gte: filters.quality.minDifficulty }
    }
    if (filters.quality.maxDifficulty !== undefined) {
      where.difficultyRating = { 
        ...(where.difficultyRating as any || {}),
        lte: filters.quality.maxDifficulty 
      }
    }
    if (filters.quality.minPopularity !== undefined) {
      where.popularityScore = { gte: filters.quality.minPopularity }
    }
    if (filters.quality.requireAlbumArt) {
      where.albumArtUrl = { not: null }
    }

    // Apply source filters
    if (!filters.sources.includeMyItunes) {
      where.mpdFilePath = { not: { startsWith: 'MyItunes/' } }
    }

    // Apply metadata filters
    if (filters.metadata.requireYear) {
      where.year = { ...((where.year as any) || {}), not: null }
    }
    if (filters.metadata.requireGenre) {
      where.genre = { ...((where.genre as any) || {}), not: null }
    }
    if (filters.metadata.requireAlbum) {
      where.album = { not: null }
    }

    // Apply folder filters
    if (filters.folders.values.length > 0) {
      const folderConditions = filters.folders.values.map(folder => ({
        mpdFilePath: { contains: `${folder}/` }
      }))
      
      if (filters.folders.mode === 'include') {
        where.OR = folderConditions
      } else {
        where.NOT = folderConditions
      }
    }

    // Apply category/playlist filters
    if (filters.playlists.values.length > 0) {
      const categoryConditions = filters.playlists.values.map(category => ({
        quizCategories: { contains: category }
      }))
      
      if (filters.playlists.mode === 'include') {
        if (where.OR) {
          where.AND = [{ OR: where.OR }, { OR: categoryConditions }]
          delete where.OR
        } else {
          where.OR = categoryConditions
        }
      } else {
        if (where.NOT) {
          where.NOT = Array.isArray(where.NOT) ? [...where.NOT, ...categoryConditions] : [where.NOT, ...categoryConditions]
        } else {
          where.NOT = categoryConditions
        }
      }
    }

    console.log('[MultiplayerQuizGenerator] Final built where clause:', JSON.stringify(where, null, 2))
    return where
  }


  async generateUltimoteQuestions(count: number, ultimoteConfig?: any, timePerQuestion: number = 30): Promise<MultiplayerQuestion[]> {
    console.log('[MultiplayerQuizGenerator] generateUltimoteQuestions called with:')
    console.log('  - count:', count)
    console.log('  - timePerQuestion:', timePerQuestion)
    console.log('  - ultimoteConfig:', JSON.stringify(ultimoteConfig, null, 2))
    console.log('  - ultimoteConfig is null/undefined?', ultimoteConfig == null)
    
    // Configure ultimote with both music and general knowledge categories
    const settings = {
      gameMode: 'ultimote',
      totalQuestions: count,
      timePerQuestion: timePerQuestion,
      ultimoteConfig: ultimoteConfig || {
        questionsPerRound: 1, // Generate 1 question per round
        categories: {
          classic: { enabled: true, rounds: Math.ceil(count / 3) },
          quickFire: { enabled: true, rounds: Math.floor(count / 3) },
          generalKnowledge: {
            enabled: true,
            rounds: Math.ceil(count / 3),
            categories: ['film', 'sport', 'geography', 'history', 'science'] // Popular categories
          }
        }
      }
    }
    
    if (!ultimoteConfig) {
      console.warn('[MultiplayerQuizGenerator] No ultimote config provided, using default mixed mode')
      console.warn('[MultiplayerQuizGenerator] This should not happen in production - config should always be provided')
      console.trace('[MultiplayerQuizGenerator] Stack trace for missing config')
    }
    
    // Get tracks for questions - fetch more to ensure variety
    const tracks = await this.prisma.quizTrack.findMany({
      take: 1000, // Fetch more tracks to ensure we have enough variety
      orderBy: { id: 'asc' }
    })
    
    if (tracks.length === 0) {
      console.error('[MultiplayerQuizGenerator] No tracks found in database')
      throw new Error('No tracks available for quiz generation')
    }
    
    console.log(`[MultiplayerQuizGenerator] Fetched ${tracks.length} tracks for question generation`)
    
    return this.generateUltimoteQuestionsInternal(settings, tracks)
  }

  private async generateUltimoteQuestionsInternal(settings: {
    gameMode: string
    totalQuestions: number
    timePerQuestion?: number
    filters?: ContentFilters
    ultimoteConfig?: any
  }, tracks: any[]): Promise<MultiplayerQuestion[]> {
    console.log('[MultiplayerQuizGenerator] Generating ulTimote questions')
    console.log('[MultiplayerQuizGenerator] ulTimote config received:', JSON.stringify(settings.ultimoteConfig, null, 2))
    console.log('[MultiplayerQuizGenerator] General knowledge config specifically:', JSON.stringify(settings.ultimoteConfig?.categories?.generalKnowledge, null, 2))
    
    if (!settings.ultimoteConfig) {
      console.warn('[MultiplayerQuizGenerator] No ulTimote config provided, using gameMode:', settings.gameMode)
      // Don't change the gameMode - use what was requested
      return this.generateDatabaseQuestions(settings)
    }

    const config = settings.ultimoteConfig
    const questions: MultiplayerQuestion[] = []
    
    // Calculate total rounds based on category settings
    const categoryRounds: { category: string; rounds: number; type: string }[] = []
    
    // Debug: Log the exact config we're working with
    console.log('[MultiplayerQuizGenerator] CRITICAL DEBUG - Full config object:')
    console.log(JSON.stringify(config, null, 2))
    console.log('[MultiplayerQuizGenerator] CRITICAL DEBUG - Categories object:')
    console.log(JSON.stringify(config.categories, null, 2))
    
    // Add music quiz categories
    console.log('[MultiplayerQuizGenerator] Processing categories:', Object.keys(config.categories || {}))
    for (const [key, cat] of Object.entries(config.categories || {})) {
      // Type guard to ensure cat has the expected properties
      const category = cat as any // Cast to any to avoid type issues - this is configuration data
      console.log(`[MultiplayerQuizGenerator] Category ${key}:`, { enabled: category?.enabled, rounds: category?.rounds })
      // Extra safety check: ensure both enabled AND rounds > 0
      if (category?.enabled === true && category?.rounds && category.rounds > 0 && key !== 'generalKnowledge') {
        console.log(`[MultiplayerQuizGenerator] Adding ${category.rounds} rounds for music category: ${key}`)
        for (let i = 0; i < category.rounds; i++) {
          categoryRounds.push({ category: key, rounds: 1, type: 'music' })
        }
      } else if (key !== 'generalKnowledge') {
        console.log(`[MultiplayerQuizGenerator] Skipping music category ${key} (enabled: ${category?.enabled}, rounds: ${category?.rounds})`)
      }
    }
    
    // Add general knowledge rounds
    const generalKnowledge = config.categories?.generalKnowledge
    console.log('[MultiplayerQuizGenerator] General Knowledge config:', JSON.stringify(generalKnowledge, null, 2))
    
    if (generalKnowledge?.enabled && generalKnowledge.rounds > 0) {
      // Validate categories array
      if (!Array.isArray(generalKnowledge.categories)) {
        console.error('[MultiplayerQuizGenerator] General knowledge categories is not an array:', generalKnowledge.categories)
        generalKnowledge.categories = []
      }
      console.log(`[MultiplayerQuizGenerator] General knowledge categories (${generalKnowledge.categories.length}):`, generalKnowledge.categories)
      
      for (let i = 0; i < generalKnowledge.rounds; i++) {
        categoryRounds.push({ category: 'generalKnowledge', rounds: 1, type: 'general' })
      }
    }
    
    // Shuffle rounds to mix categories
    const shuffledRounds = this.shuffleArray(categoryRounds)
    
    console.log(`[MultiplayerQuizGenerator] Total rounds: ${shuffledRounds.length}`)
    console.log('[MultiplayerQuizGenerator] Round distribution:', shuffledRounds.map(r => r.category))
    console.log('[MultiplayerQuizGenerator] Music rounds:', shuffledRounds.filter(r => r.type === 'music').length)
    console.log('[MultiplayerQuizGenerator] General rounds:', shuffledRounds.filter(r => r.type === 'general').length)
    
    // If no rounds are configured, throw an error
    if (shuffledRounds.length === 0) {
      console.error('[MultiplayerQuizGenerator] No rounds configured for ulTimote mode')
      console.error('[MultiplayerQuizGenerator] Config categories:', JSON.stringify(config.categories, null, 2))
      throw new Error('No categories enabled for ulTimote mode. Please enable at least one category with rounds > 0.')
    }
    
    // Generate questions for each round
    for (const round of shuffledRounds) {
      console.log(`[MultiplayerQuizGenerator] Processing round - category: ${round.category}, type: ${round.type}`)
      
      // Safety check: skip music categories that shouldn't be here
      if (round.type === 'music' && (!config.categories[round.category] || !config.categories[round.category].enabled || config.categories[round.category].rounds === 0)) {
        console.error(`[MultiplayerQuizGenerator] ERROR: Attempting to generate questions for disabled category ${round.category}`)
        continue
      }
      
      if (round.type === 'general') {
        // Generate general knowledge questions
        console.log(`[MultiplayerQuizGenerator] Generating general knowledge questions with:`)
        console.log(`  - Count: ${config.questionsPerRound || 5}`)
        console.log(`  - Categories: ${JSON.stringify(generalKnowledge.categories || [])}`)
        console.log(`  - Difficulty: ${generalKnowledge.difficulty || 3}`)
        console.log(`  - Time per question: ${settings.timePerQuestion || 30}`)
        
        const generalQuestions = await this.generateGeneralKnowledgeQuestions(
          config.questionsPerRound || 5,
          generalKnowledge.categories || [],
          generalKnowledge.difficulty || 3,
          settings.timePerQuestion || 30
        )
        console.log(`[MultiplayerQuizGenerator] Generated ${generalQuestions.length} general knowledge questions`)
        questions.push(...generalQuestions)
      } else {
        // Generate music questions based on category
        console.log(`[MultiplayerQuizGenerator] Generating music questions for category: ${round.category}`)
        console.log(`  - Questions per round: ${config.questionsPerRound || 5}`)
        console.log(`  - Time per question: ${settings.timePerQuestion || 30}`)
        console.log(`  - Category config:`, JSON.stringify(config.categories[round.category]))
        
        const requestedCount = config.questionsPerRound || 1
        console.log(`[MultiplayerQuizGenerator] Requesting ${requestedCount} questions for ${round.category}`)
        
        const categoryQuestions = await this.generateCategoryQuestions(
          round.category,
          requestedCount,
          settings.timePerQuestion || 30,
          tracks,
          config.categories[round.category]
        )
        console.log(`[MultiplayerQuizGenerator] Generated ${categoryQuestions.length} questions for ${round.category} (requested: ${requestedCount})`)
        if (categoryQuestions.length !== requestedCount) {
          console.warn(`[MultiplayerQuizGenerator] WARNING: Generated ${categoryQuestions.length} questions but requested ${requestedCount}`)
        }
        questions.push(...categoryQuestions)
      }
    }
    
    console.log(`[MultiplayerQuizGenerator] Generated ${questions.length} total ulTimote questions`)
    console.log(`[MultiplayerQuizGenerator] Expected ${settings.totalQuestions} questions`)
    
    // If no questions were generated (e.g., only GK enabled but no categories selected)
    if (questions.length === 0) {
      console.error('[MultiplayerQuizGenerator] No questions generated in ultimote mode!')
      console.error('[MultiplayerQuizGenerator] This can happen when only General Knowledge is enabled but no GK categories are selected')
      throw new Error('No questions could be generated. Please select at least one General Knowledge category or enable a music quiz category.')
    }
    
    // If we generated fewer questions than requested, log a warning but continue
    if (questions.length < settings.totalQuestions) {
      console.warn(`[MultiplayerQuizGenerator] Generated fewer questions (${questions.length}) than requested (${settings.totalQuestions})`)
      console.warn('[MultiplayerQuizGenerator] This happens when the enabled categories cannot provide enough questions')
    }
    
    return questions
  }
  
  async generateGeneralKnowledgeQuestions(
    count: number,
    categories: string[],
    difficulty: number,
    timeLimit: number
  ): Promise<MultiplayerQuestion[]> {
    console.log(`[MultiplayerQuizGenerator] Generating ${count} general knowledge questions with timeLimit: ${timeLimit}s`)
    console.log(`[MultiplayerQuizGenerator] Using categories:`, categories)
    
    try {
      // Fetch questions from general quiz database
      const whereClause: any = { isActive: true }
      
      if (categories.length > 0) {
        whereClause.category = {
          slug: { in: categories }
        }
      } else {
        // If no categories are selected, use some default categories
        console.log('[MultiplayerQuizGenerator] No general knowledge categories selected, using defaults')
        const defaultCategories = ['science', 'history', 'film', 'technology', 'geography', 'sport']
        whereClause.category = {
          slug: { in: defaultCategories }
        }
      }
      
      // DEBUGGING: Difficulty filtering disabled to diagnose "no questions" issue
      // if (difficulty) {
      //   // Use a range around the requested difficulty for more flexibility
      //   // This allows for questions within ±1 difficulty level
      //   whereClause.difficulty = {
      //     gte: Math.max(1, difficulty - 1),
      //     lte: Math.min(5, difficulty + 1)
      //   }
      // }
      console.log('[MultiplayerQuizGenerator] DIFFICULTY FILTERING DISABLED FOR DEBUGGING')
      
      console.log('[MultiplayerQuizGenerator] General Knowledge query whereClause:', JSON.stringify(whereClause, null, 2))
      
      console.log('[MultiplayerQuizGenerator] About to query database with whereClause:', JSON.stringify(whereClause, null, 2))
      const startTime = Date.now()
      
      // Don't use 'take' with multiple categories as it causes Prisma to return uneven distribution
      // Instead, fetch all and then sample
      console.log('[MultiplayerQuizGenerator] Executing query...')
      const generalQuestions = await this.prisma.generalQuizQuestion.findMany({
        where: whereClause,
        include: { category: true }
      }).catch(error => {
        console.error('[MultiplayerQuizGenerator] Query error:', error)
        console.error('[MultiplayerQuizGenerator] Query details:', {
          whereClause: JSON.stringify(whereClause, null, 2),
          model: 'generalQuizQuestion'
        })
        throw error
      })
      
      const queryTime = Date.now() - startTime
      console.log(`[MultiplayerQuizGenerator] Database query took ${queryTime}ms`)
      
      console.log(`[MultiplayerQuizGenerator] Found ${generalQuestions.length} general questions`)
      console.log(`[MultiplayerQuizGenerator] Query result:`, {
        totalFound: generalQuestions.length,
        requestedCategories: categories,
        requestedDifficulty: difficulty
      })
      if (generalQuestions.length > 0) {
        const categoryCounts = generalQuestions.reduce((acc, q) => {
          acc[q.category.slug] = (acc[q.category.slug] || 0) + 1
          return acc
        }, {} as Record<string, number>)
        console.log('[MultiplayerQuizGenerator] Questions by category:', categoryCounts)
        
        // Debug: Show difficulty distribution
        const difficultyDistribution = generalQuestions.reduce((acc, q) => {
          acc[q.difficulty] = (acc[q.difficulty] || 0) + 1
          return acc
        }, {} as Record<number, number>)
        console.log('[MultiplayerQuizGenerator] Difficulty distribution:', difficultyDistribution)
        
        // Debug: Show sample questions
        console.log('[MultiplayerQuizGenerator] Sample questions (first 3):', 
          generalQuestions.slice(0, 3).map(q => ({
            category: q.category.slug,
            difficulty: q.difficulty,
            question: q.question.substring(0, 50) + '...'
          }))
        )
      }
      
      // Check if we have enough questions
      if (generalQuestions.length === 0) {
        console.error(`[MultiplayerQuizGenerator] No general knowledge questions found for categories: ${categories.join(', ')}`)
        
        // Get detailed information about what went wrong
        const categoryDetails = await this.prisma.quizCategory.findMany({
          where: { slug: { in: categories } },
          include: {
            _count: {
              select: { questions: true }
            }
          }
        })
        
        const detailInfo = categoryDetails.map(cat => 
          `${cat.name} (${cat.slug}): ${cat._count.questions} total questions`
        ).join(', ')
        
        throw new Error(
          `No general knowledge questions found.\n` +
          `Selected categories: ${categories.join(', ')}\n` +
          `Category details: ${detailInfo}\n` +
          `Query filters: isActive=true, difficulty=${difficulty} (DISABLED FOR DEBUGGING)`
        )
      }
      
      // For better distribution across categories, group by category first
      const questionsByCategory: { [key: string]: any[] } = {}
      generalQuestions.forEach(q => {
        const slug = q.category.slug
        if (!questionsByCategory[slug]) {
          questionsByCategory[slug] = []
        }
        questionsByCategory[slug].push(q)
      })
      
      // Calculate questions per category for even distribution
      const categoryCount = Object.keys(questionsByCategory).length
      const questionsPerCategory = Math.ceil(count / categoryCount)
      
      console.log(`[MultiplayerQuizGenerator] Distributing ${count} questions across ${categoryCount} categories (${questionsPerCategory} per category)`)
      
      // Take questions evenly from each category
      const selectedQuestions: any[] = []
      Object.entries(questionsByCategory).forEach(([slug, questions]) => {
        const shuffledCategory = this.shuffleArray(questions)
        const selected = shuffledCategory.slice(0, questionsPerCategory)
        selectedQuestions.push(...selected)
        console.log(`[MultiplayerQuizGenerator] Selected ${selected.length} questions from ${slug}`)
      })
      
      // Final shuffle to mix categories
      const shuffled = this.shuffleArray(selectedQuestions).slice(0, count)
      
      return shuffled.map((q, index) => ({
        id: `mp_gk_${Date.now()}_${index}`,
        type: 'multiple-choice' as const,
        question: q.question,
        options: q.options ? (typeof q.options === 'string' ? JSON.parse(q.options) : q.options) : [],
        correctAnswer: q.correctAnswer,
        category: q.category.name,
        timeLimit: timeLimit,
        explanation: q.explanation || undefined
      }))
    } catch (error) {
      console.error('[MultiplayerQuizGenerator] Error generating general knowledge questions:', error)
      console.error('[MultiplayerQuizGenerator] Error details:', {
        count,
        categories,
        difficulty,
        timeLimit,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined
      })
      // Return empty array if general knowledge fails
      return []
    }
  }
  
  private async generateCategoryQuestions(
    category: string,
    count: number,
    timeLimit: number,
    tracks: any[],
    categoryConfig: any
  ): Promise<MultiplayerQuestion[]> {
    console.log(`[MultiplayerQuizGenerator] Generating ${count} questions for category: ${category}`)
    
    // Map ulTimote categories to multiplayer game modes
    const categoryMap: { [key: string]: string } = {
      classic: 'classic',
      quickFire: 'classic', // Quick fire is just faster classic
      audioTricks: 'audio_manipulation',
      albumArt: 'classic', // Would need visual mode implementation
      audioFingerprint: 'classic', // Would need short clip implementation
      chartPosition: 'chart_position',
      decadeChallenge: 'guess_the_year',
      genreSpecialist: 'genre_specialist'
    }
    
    const mappedGameMode = categoryMap[category] || 'classic'
    
    // Generate questions using the existing logic for each game mode
    const tempSettings = {
      gameMode: mappedGameMode,
      totalQuestions: count,
      timePerQuestion: timeLimit
    }
    
    // Generate questions using the existing logic for each game mode
    // by delegating to generateDatabaseQuestions with the mapped game mode
    console.log(`[MultiplayerQuizGenerator] Generating ${mappedGameMode} questions for ulTimote category ${category}`)
    
    const generatedQuestions = await this.generateDatabaseQuestions(tempSettings)
    
    // Take only the requested number of questions and update their category
    const questions = generatedQuestions.slice(0, count).map(q => ({
      ...q,
      category: this.getCategoryDisplayName(category)
    }))
    
    return questions
  }
  
  private async generateSingleQuestion(
    track: any,
    settings: { gameMode: string; timePerQuestion?: number },
    tracks: any[],
    index: number
  ): Promise<MultiplayerQuestion | null> {
    // This reuses the existing switch logic for individual questions
    switch (settings.gameMode) {
      case 'classic':
        // Reuse classic question generation logic
        let wrongArtists: string[] = []
        try {
          const similarArtistsJson = track.similarArtists || '[]'
          const similarArtists = JSON.parse(similarArtistsJson) as string[]
          if (similarArtists.length > 0) {
            wrongArtists = this.shuffleArray(similarArtists).slice(0, 3)
          }
        } catch (e) {}
        
        if (wrongArtists.length < 3) {
          const allArtists = tracks
            .filter(t => t.artist && t.artist !== track.artist && !wrongArtists.includes(t.artist!))
            .map(t => t.artist!)
          const uniqueArtists = Array.from(new Set(allArtists))
          const additionalArtists = this.shuffleArray(uniqueArtists).slice(0, 3 - wrongArtists.length)
          wrongArtists = [...wrongArtists, ...additionalArtists]
        }
        
        while (wrongArtists.length < 3) {
          wrongArtists.push(`Unknown Artist ${wrongArtists.length + 1}`)
        }
        
        return {
          id: `mp_${Date.now()}_${index}`,
          type: 'multiple-choice',
          question: `Who is the artist?`,
          options: this.shuffleArray([track.artist!, ...wrongArtists.slice(0, 3)]),
          correctAnswer: track.artist!,
          category: 'Classic',
          timeLimit: settings.timePerQuestion || 30,
          track: {
            id: parseInt(track.id),
            file: track.mpdFilePath,
            title: track.title!,
            artist: track.artist!,
            album: track.album || undefined,
            year: track.year || undefined,
            albumArtUrl: track.albumArtUrl || undefined,
            genre: track.genre || undefined,
            duration: track.duration || undefined,
            popularityScore: track.popularityScore || undefined,
            chartPosition: track.chartPosition || undefined,
            releaseDate: track.releaseDate || undefined,
            triviaFacts: track.triviaFacts ? (typeof track.triviaFacts === 'string' ? JSON.parse(track.triviaFacts) : track.triviaFacts) : undefined,
            interestingFacts: track.interestingFacts ? (typeof track.interestingFacts === 'string' ? JSON.parse(track.interestingFacts) : track.interestingFacts) : undefined
          }
        }
        
      case 'guess_the_year':
        const correctYear = track.year || 2000
        const yearOptions = [
          correctYear,
          correctYear - 2,
          correctYear + 3,
          correctYear - 5
        ].sort()
        
        return {
          id: `mp_${Date.now()}_${index}`,
          type: 'multiple-choice',
          question: `What year was this song released?`,
          options: yearOptions.map(y => y.toString()),
          correctAnswer: correctYear.toString(),
          category: 'Guess the Year',
          timeLimit: settings.timePerQuestion || 30,
          track: {
            id: parseInt(track.id),
            file: track.mpdFilePath,
            title: track.title!,
            artist: track.artist!,
            album: track.album || undefined,
            year: track.year || undefined,
            albumArtUrl: track.albumArtUrl || undefined,
            genre: track.genre || undefined,
            duration: track.duration || undefined,
            popularityScore: track.popularityScore || undefined,
            chartPosition: track.chartPosition || undefined,
            releaseDate: track.releaseDate || undefined,
            triviaFacts: track.triviaFacts ? (typeof track.triviaFacts === 'string' ? JSON.parse(track.triviaFacts) : track.triviaFacts) : undefined,
            interestingFacts: track.interestingFacts ? (typeof track.interestingFacts === 'string' ? JSON.parse(track.interestingFacts) : track.interestingFacts) : undefined
          }
        }
        
      // Add other cases as needed...
      default:
        return null
    }
  }
  
  private getCategoryDisplayName(category: string): string {
    const displayNames: { [key: string]: string } = {
      classic: 'Classic Quiz',
      quickFire: 'Quick Fire',
      audioTricks: 'Audio Tricks',
      albumArt: 'Album Art',
      audioFingerprint: 'Audio Fingerprint',
      chartPosition: 'Chart Position',
      decadeChallenge: 'Guess the Year',
      genreSpecialist: 'Genre Specialist',
      generalKnowledge: 'General Knowledge'
    }
    return displayNames[category] || category
  }

  async cleanup() {
    await this.prisma.$disconnect()
  }
}