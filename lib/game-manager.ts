/**
 * Game Manager for Multiplayer Music Quiz
 * 
 * Handles game logic, state management, question flow, and voting
 */

import type { Player, Question, GameState, Team, TeamGameSettings, VotingSession } from './types'
import { votingSocketHandler } from './voting-socket-handler'
import { MultiplayerQuizGenerator } from './multiplayer-quiz-generator'

export interface GameConfig {
  gameId: string
  hostId: string
  gameMode: string
  maxPlayers?: number
  timePerQuestion?: number
  totalQuestions?: number
  teamMode?: boolean
  teamSettings?: TeamGameSettings
  enableVoting?: boolean
  ultimoteConfig?: any
}

export interface VotingPhase {
  type: 'category' | 'decade' | 'game-mode' | 'theme'
  title: string
  description: string
  sessionId?: string
  isActive: boolean
}

export class GameManager {
  private gameState: GameState
  private votingEnabled: boolean
  private currentVotingPhase: VotingPhase | null = null
  private quizGenerator: MultiplayerQuizGenerator
  private ultimoteConfig: any

  constructor(config: GameConfig) {
    console.log('[GameManager] Initializing... Attempting to create Prisma client.');
    console.log('[GameManager] Constructor received config:', {
      gameId: config.gameId,
      gameMode: config.gameMode,
      ultimoteConfig: config.ultimoteConfig ? 'Present' : 'Missing',
      ultimoteConfigDetails: JSON.stringify(config.ultimoteConfig, null, 2)
    });
    try {
      this.quizGenerator = new MultiplayerQuizGenerator();
      console.log('[GameManager] Prisma client created successfully.');
    } catch (error) {
      console.error('[GameManager] FATAL: Failed to create Prisma client:', error);
      throw error; // Re-throw to prevent server from starting in a broken state
    }
    this.votingEnabled = config.enableVoting || false;
    this.ultimoteConfig = config.ultimoteConfig;
    this.gameState = {
      gameId: config.gameId,
      status: 'waiting',
      gameMode: config.gameMode,
      players: [],
      teams: config.teamMode ? [] : undefined,
      teamMode: config.teamMode || false,
      teamSettings: config.teamSettings,
      questions: [],
      currentQuestionIndex: -1,
      currentQuestion: null,
      totalQuestions: config.totalQuestions || 10,
      timePerQuestion: config.timePerQuestion || 30,
      createdAt: Date.now(),
      hostId: config.hostId,
      settings: {
        maxPlayers: config.maxPlayers || 6,
        allowSpectators: false,
        autoNextQuestion: true
      }
    };
  }

  /**
   * Create a new game with host player
   */
  async createGame(host: Player): Promise<GameState> {
    this.gameState.players = [host]
    
    // Generate questions based on game mode
    console.log('[GameManager] Creating game with mode:', this.gameState.gameMode)
    
    try {
      if (this.gameState.gameMode === 'ultimote') {
        console.log('[GameManager] Generating ultimote questions...')
        console.log('[GameManager] UltimoteConfig received:', JSON.stringify(this.ultimoteConfig, null, 2))
        const multiplayerQuestions = await this.quizGenerator.generateUltimoteQuestions(this.gameState.totalQuestions, this.ultimoteConfig, this.gameState.timePerQuestion)
        console.log('[GameManager] Generated questions:', multiplayerQuestions.length)
        this.gameState.questions = this.convertToGameQuestions(multiplayerQuestions)
        console.log('[GameManager] Converted questions:', this.gameState.questions.length)
      } else if (this.gameState.gameMode === 'general') {
        console.log('[GameManager] Generating general knowledge questions...')
        // Create a simple ultimote config for general knowledge only
        const generalOnlyConfig = {
          questionsPerRound: this.gameState.totalQuestions,
          categories: {
            generalKnowledge: {
              enabled: true,
              rounds: 1,
              categories: ['general', 'science', 'history', 'geography'],
              difficulty: 3
            },
            // Disable all music categories
            decades: { enabled: false, rounds: 0 },
            genres: { enabled: false, rounds: 0 },
            artists: { enabled: false, rounds: 0 }
          }
        }
        const multiplayerQuestions = await this.quizGenerator.generateUltimoteQuestions(
          this.gameState.totalQuestions, 
          generalOnlyConfig, 
          this.gameState.timePerQuestion
        )
        console.log('[GameManager] Generated general questions:', multiplayerQuestions.length)
        this.gameState.questions = this.convertToGameQuestions(multiplayerQuestions)
      } else {
        // For all other game modes (classic, guess-the-year, chart-position, etc.), use the quiz generator
        console.log('[GameManager] Generating music quiz questions for mode:', this.gameState.gameMode)
        const multiplayerQuestions = await this.quizGenerator.generateQuiz({
          gameMode: this.gameState.gameMode,
          totalQuestions: this.gameState.totalQuestions,
          timePerQuestion: this.gameState.timePerQuestion,
          filters: undefined // TODO: Add filter support for multiplayer
        })
        console.log('[GameManager] Generated music questions:', multiplayerQuestions.length)
        this.gameState.questions = this.convertToGameQuestions(multiplayerQuestions)
      }
    } catch (error) {
      console.error(`[GameManager] Failed to generate questions for mode ${this.gameState.gameMode}, using mock questions:`, error)
      this.gameState.questions = this.generateMockQuestions()
    }
    
    this.gameState.totalQuestions = this.gameState.questions.length
    return { ...this.gameState }
  }

  /**
   * Add a player to the game
   */
  addPlayer(player: Player): GameState {
    if (this.gameState.status !== 'waiting') {
      throw new Error('Cannot add player to game in progress')
    }
    this.gameState.players.push(player)
    return { ...this.gameState }
  }

  /**
   * Remove a player from the game
   */
  removePlayer(playerId: string): GameState {
    const index = this.gameState.players.findIndex(p => p.id === playerId)
    if (index !== -1) {
      this.gameState.players.splice(index, 1)
    }
    return { ...this.gameState }
  }

  /**
   * Start the game
   */
  startGame(): GameState {
    this.gameState.status = 'playing'
    this.gameState.startedAt = Date.now()
    this.gameState.currentQuestionIndex = 0
    this.gameState.currentQuestion = this.gameState.questions[0]
    return { ...this.gameState }
  }

  /**
   * Submit an answer for a player
   */
  submitAnswer(playerId: string, answerIndex: number, timeTaken: number): GameState {
    const player = this.gameState.players.find(p => p.id === playerId)
    if (player) {
      player.hasAnswered = true
      player.lastAnswer = answerIndex
      player.lastAnswerTime = timeTaken
    }
    return { ...this.gameState }
  }

  /**
   * Process question results and calculate scores
   */
  processQuestionResults(): GameState {
    this.gameState.status = 'question-results'
    
    // Process team scoring if team mode is enabled
    if (this.gameState.teamMode) {
      this.processTeamScoring()
    } else {
      // Process individual scoring
      const currentQuestion = this.gameState.currentQuestion
      if (!currentQuestion) return { ...this.gameState }
      
      this.gameState.players.forEach(player => {
        if (player.hasAnswered && player.lastAnswer === currentQuestion.correctAnswer) {
          // Calculate points based on time taken
          const timeTaken = player.lastAnswerTime || currentQuestion.timeLimit
          const timeRatio = Math.max(0, 1 - (timeTaken / currentQuestion.timeLimit))
          const points = Math.round(1000 * timeRatio)
          
          player.score += points
          player.lastScoreChange = points
          console.log(`[GameManager] Player ${player.name} scored ${points} points (total: ${player.score})`)
        } else {
          player.lastScoreChange = 0
        }
      })
    }
    
    return { ...this.gameState }
  }

  /**
   * Move to the next question
   */
  nextQuestion(): GameState {
    this.gameState.currentQuestionIndex++
    this.gameState.currentQuestion = this.gameState.questions[this.gameState.currentQuestionIndex]
    this.gameState.status = 'playing'
    this.gameState.players.forEach(p => p.hasAnswered = false)
    
    // Reset team answer states
    if (this.gameState.teams) {
      this.gameState.teams.forEach(team => {
        team.hasAnswered = false
        team.lastAnswer = undefined
        team.lastAnswerTime = undefined
      })
    }
    
    return { ...this.gameState }
  }

  /**
   * End the game
   */
  endGame(): GameState {
    this.gameState.status = 'finished'
    this.gameState.finishedAt = Date.now()
    return { ...this.gameState }
  }

  /**
   * Calculate game statistics
   */
  calculateGameStats(): {
    totalQuestions: number
    gameMode: string
    duration?: number
    quickestAnswer?: { player: string; time: number; question: string }
    mostAccurate?: { player: string; accuracy: number }
    longestStreak?: { player: string; streak: number }
    perfectRounds?: string[]
  } {
    const stats: any = {
      totalQuestions: this.gameState.totalQuestions,
      gameMode: this.gameState.gameMode
    }
    
    // Calculate game duration if we have start and finish times
    if (this.gameState.startedAt && this.gameState.finishedAt) {
      stats.duration = Math.floor((this.gameState.finishedAt - this.gameState.startedAt) / 1000) // in seconds
    }
    
    // Track player statistics
    const playerStats: Map<string, {
      correctAnswers: number
      totalAnswers: number
      currentStreak: number
      longestStreak: number
      fastestTime: number
      fastestQuestion: string
    }> = new Map()

    // Initialize player stats
    this.gameState.players.forEach(player => {
      playerStats.set(player.id, {
        correctAnswers: 0,
        totalAnswers: 0,
        currentStreak: 0,
        longestStreak: 0,
        fastestTime: Infinity,
        fastestQuestion: ''
      })
    })

    // Analyze answer history (this would need to be tracked during the game)
    // For now, we'll calculate based on available data
    
    // Find quickest answer
    let quickestAnswer: { player: string; time: number; question: string } | undefined
    let quickestTime = Infinity
    
    this.gameState.players.forEach(player => {
      if (player.lastAnswerTime && player.lastAnswerTime < quickestTime) {
        quickestTime = player.lastAnswerTime
        quickestAnswer = {
          player: player.name,
          time: player.lastAnswerTime / 1000, // Convert to seconds
          question: this.gameState.currentQuestion?.question || ''
        }
      }
    })
    
    if (quickestAnswer) {
      stats.quickestAnswer = quickestAnswer
    }

    // Find players with perfect rounds (all questions correct)
    const perfectRoundPlayers = this.gameState.players
      .filter(player => {
        // A simple heuristic: if their score equals max possible score
        const maxPossibleScore = this.gameState.totalQuestions * 1000
        return player.score === maxPossibleScore
      })
      .map(player => player.name)
    
    if (perfectRoundPlayers.length > 0) {
      stats.perfectRounds = perfectRoundPlayers
    }

    // Calculate accuracy
    const playerAccuracy: Array<{ player: string; accuracy: number }> = []
    this.gameState.players.forEach(player => {
      // Estimate accuracy based on score vs questions
      const avgPointsPerQuestion = player.score / this.gameState.totalQuestions
      const accuracy = (avgPointsPerQuestion / 1000) * 100 // Convert to percentage
      if (accuracy > 0) {
        playerAccuracy.push({ player: player.name, accuracy })
      }
    })

    if (playerAccuracy.length > 0) {
      playerAccuracy.sort((a, b) => b.accuracy - a.accuracy)
      stats.mostAccurate = playerAccuracy[0]
    }

    return stats
  }

  /**
   * Get current game state
   */
  getGameState(): GameState {
    return { ...this.gameState }
  }

  /**
   * Enable or disable team mode
   */
  toggleTeamMode(enabled: boolean, settings?: TeamGameSettings): GameState {
    this.gameState.teamMode = enabled
    this.gameState.teamSettings = enabled ? settings : undefined
    this.gameState.teams = enabled ? (this.gameState.teams || []) : undefined
    return { ...this.gameState }
  }

  /**
   * Add a team to the game
   */
  addTeam(team: Team): GameState {
    if (!this.gameState.teamMode) {
      throw new Error('Team mode is not enabled')
    }
    if (!this.gameState.teams) {
      this.gameState.teams = []
    }
    this.gameState.teams.push(team)
    return { ...this.gameState }
  }

  /**
   * Remove a team from the game
   */
  removeTeam(teamId: string): GameState {
    if (!this.gameState.teams) {
      return { ...this.gameState }
    }
    const index = this.gameState.teams.findIndex(t => t.id === teamId)
    if (index !== -1) {
      this.gameState.teams.splice(index, 1)
    }
    return { ...this.gameState }
  }

  /**
   * Add a player to a team
   */
  addPlayerToTeam(playerId: string, teamId: string): GameState {
    if (!this.gameState.teams) {
      throw new Error('No teams available')
    }
    
    const team = this.gameState.teams.find(t => t.id === teamId)
    const player = this.gameState.players.find(p => p.id === playerId)
    
    if (!team || !player) {
      throw new Error('Team or player not found')
    }

    // Check team size limit
    if (this.gameState.teamSettings?.maxTeamSize && 
        team.players.length >= this.gameState.teamSettings.maxTeamSize) {
      throw new Error('Team is full')
    }

    // Remove player from any existing team
    this.removePlayerFromAllTeams(playerId)
    
    // Add player to the new team
    team.players.push(player)
    
    return { ...this.gameState }
  }

  /**
   * Remove a player from their current team
   */
  removePlayerFromTeam(playerId: string): GameState {
    this.removePlayerFromAllTeams(playerId)
    return { ...this.gameState }
  }

  /**
   * Submit a team answer
   */
  submitTeamAnswer(teamId: string, answerIndex: number, timeTaken: number, submittedBy: string): GameState {
    if (!this.gameState.teams) {
      throw new Error('Team mode is not enabled')
    }

    const team = this.gameState.teams.find(t => t.id === teamId)
    if (!team) {
      throw new Error('Team not found')
    }

    // Check if player is in the team
    const isPlayerInTeam = team.players.some(p => p.id === submittedBy)
    if (!isPlayerInTeam) {
      throw new Error('Player is not in this team')
    }

    // Update team answer
    team.hasAnswered = true
    team.lastAnswer = answerIndex
    team.lastAnswerTime = timeTaken

    // Mark all team members as having answered
    team.players.forEach(player => {
      const gamePlayer = this.gameState.players.find(p => p.id === player.id)
      if (gamePlayer) {
        gamePlayer.hasAnswered = true
        gamePlayer.lastAnswer = answerIndex
        gamePlayer.lastAnswerTime = timeTaken
      }
    })

    return { ...this.gameState }
  }

  /**
   * Process team scoring for current question
   */
  processTeamScoring(): GameState {
    if (!this.gameState.teamMode || !this.gameState.teams || !this.gameState.currentQuestion) {
      return { ...this.gameState }
    }

    const correctAnswer = this.gameState.currentQuestion.correctAnswer
    const scoringMode = this.gameState.teamSettings?.teamScoringMode || 'average'

    this.gameState.teams.forEach(team => {
      if (team.hasAnswered && team.lastAnswer === correctAnswer) {
        const basePoints = this.calculateBasePoints(team.lastAnswerTime || 30)
        let teamPoints = 0

        switch (scoringMode) {
          case 'average':
            teamPoints = basePoints
            break
          case 'sum':
            teamPoints = basePoints * team.players.length
            break
          case 'best':
            teamPoints = basePoints * 1.2 // Bonus for teamwork
            break
          case 'captain':
            teamPoints = basePoints
            break
        }

        // Update team score
        team.score += teamPoints

        // Update individual player scores
        team.players.forEach(player => {
          const gamePlayer = this.gameState.players.find(p => p.id === player.id)
          if (gamePlayer) {
            gamePlayer.score += teamPoints
          }
        })
      }

      // Reset team answer state for next question
      team.hasAnswered = false
      team.lastAnswer = undefined
      team.lastAnswerTime = undefined
    })

    return { ...this.gameState }
  }

  /**
   * Get teams leaderboard
   */
  getTeamsLeaderboard(): Team[] {
    if (!this.gameState.teams) {
      return []
    }
    return [...this.gameState.teams].sort((a, b) => b.score - a.score)
  }

  /**
   * Helper method to remove player from all teams
   */
  private removePlayerFromAllTeams(playerId: string): void {
    if (!this.gameState.teams) {
      return
    }

    this.gameState.teams.forEach(team => {
      const playerIndex = team.players.findIndex(p => p.id === playerId)
      if (playerIndex !== -1) {
        team.players.splice(playerIndex, 1)
        
        // If team becomes empty, remove it
        if (team.players.length === 0) {
          const teamIndex = this.gameState.teams!.findIndex(t => t.id === team.id)
          if (teamIndex !== -1) {
            this.gameState.teams!.splice(teamIndex, 1)
          }
        }
        // If captain leaves, promote another player
        else if (team.captainId === playerId && team.players.length > 0) {
          team.captainId = team.players[0].id
        }
      }
    })
  }

  /**
   * Calculate base points for an answer based on time taken
   */
  private calculateBasePoints(timeTaken: number): number {
    const maxTime = this.gameState.timePerQuestion
    const timeBonus = Math.max(0, (maxTime - timeTaken) / maxTime)
    return Math.round(100 + (timeBonus * 50)) // Base 100 points + up to 50 time bonus
  }

  private generateMockQuestions(): Question[] {
    const questions: Question[] = []
    for (let i = 0; i < 5; i++) {
      questions.push({
        id: `q${i + 1}`,
        type: 'multiple-choice',
        audioFile: `/audio/sample-${i + 1}.mp3`,
        question: `What is this song?`,
        options: ['Song A', 'Song B', 'Song C', 'Song D'],
        correctAnswer: 0,
        points: 100,
        timeLimit: 30
      })
    }
    return questions
  }

  private convertToGameQuestions(multiplayerQuestions: any[]): Question[] {
    return multiplayerQuestions.map((mq, index) => ({
      id: mq.id || `q${index + 1}`,
      type: mq.type || 'multiple-choice',
      audioFile: mq.track?.file || (mq.track ? `/audio/sample-${index + 1}.mp3` : undefined),
      question: mq.question,
      options: mq.options || [],
      correctAnswer: typeof mq.correctAnswer === 'number' 
        ? mq.correctAnswer 
        : mq.options?.indexOf(mq.correctAnswer) ?? 0,
      points: 100,
      timeLimit: mq.timeLimit || 30,
      track: mq.track,
      category: mq.category
    }))
  }

  /**
   * Check if all players have answered
   */
  allPlayersAnswered(): boolean {
    return this.gameState.players.every(player => player.hasAnswered)
  }

  /**
   * Get leaderboard
   */
  getLeaderboard(): Player[] {
    return [...this.gameState.players].sort((a, b) => b.score - a.score)
  }

  /**
   * VOTING SYSTEM INTEGRATION
   */

  /**
   * Start a voting phase between rounds
   */
  startVotingPhase(
    type: VotingPhase['type'], 
    socket: any, 
    customOptions?: { title?: string, description?: string }
  ): VotingPhase | null {
    if (!this.votingEnabled) {
      return null
    }

    const totalPlayers = this.gameState.players.length
    if (totalPlayers === 0) {
      return null
    }

    let sessionId: string
    const votingPhase: VotingPhase = {
      type,
      title: customOptions?.title || this.getDefaultVotingTitle(type),
      description: customOptions?.description || this.getDefaultVotingDescription(type),
      isActive: true
    }

    // Start appropriate voting session
    switch (type) {
      case 'category':
        sessionId = votingSocketHandler.createCategoryVoting(this.gameState.gameId, totalPlayers, socket)
        break
      case 'decade':
        sessionId = votingSocketHandler.createDecadeVoting(this.gameState.gameId, totalPlayers, socket)
        break
      case 'game-mode':
        sessionId = votingSocketHandler.createGameModeVoting(this.gameState.gameId, totalPlayers, socket)
        break
      default:
        return null
    }

    votingPhase.sessionId = sessionId
    this.currentVotingPhase = votingPhase

    return votingPhase
  }

  /**
   * Complete voting phase and apply results
   */
  completeVotingPhase(): { result: any, nextAction: string } | null {
    if (!this.currentVotingPhase || !this.currentVotingPhase.sessionId) {
      return null
    }

    const votingSession = votingSocketHandler.getCurrentVotingSession(this.gameState.gameId)
    if (!votingSession || !votingSession.result) {
      return null
    }

    const result = votingSession.result
    let nextAction = ''

    // Apply voting results based on type
    switch (this.currentVotingPhase.type) {
      case 'category':
        // Update game category filter or next question category
        nextAction = `Next round will feature ${result.winnerOption.label}`
        this.applyCategory(result.winnerOption.value)
        break
        
      case 'decade':
        // Update decade filter for next questions
        nextAction = `Next round will feature music from ${result.winnerOption.label}`
        this.applyDecade(result.winnerOption.value)
        break
        
      case 'game-mode':
        // Change game mode for next round
        nextAction = `Game mode changed to ${result.winnerOption.label}`
        this.applyGameMode(result.winnerOption.value)
        break
        
      default:
        nextAction = `${result.winnerOption.label} was selected`
    }

    // Clear current voting phase
    this.currentVotingPhase = null

    return { result, nextAction }
  }

  /**
   * Get current voting session
   */
  getCurrentVotingSession(): VotingSession | null {
    return votingSocketHandler.getCurrentVotingSession(this.gameState.gameId)
  }

  /**
   * Check if a player has voted in current session
   */
  hasPlayerVoted(playerId: string): boolean {
    return votingSocketHandler.hasPlayerVoted(this.gameState.gameId, playerId)
  }

  /**
   * Get player's vote in current session
   */
  getPlayerVote(playerId: string): number | null {
    return votingSocketHandler.getPlayerVote(this.gameState.gameId, playerId)
  }

  /**
   * Check if voting is enabled and should trigger
   */
  shouldTriggerVoting(questionIndex: number): boolean {
    if (!this.votingEnabled) return false
    
    // Trigger voting every 3 questions (after question 2, 5, 8, etc.)
    return (questionIndex + 1) % 3 === 0 && questionIndex > 0
  }

  /**
   * Determine what type of voting to trigger
   */
  getVotingType(questionIndex: number): VotingPhase['type'] {
    const cycle = Math.floor(questionIndex / 3) % 3
    
    switch (cycle) {
      case 0: return 'category'
      case 1: return 'decade'
      case 2: return 'game-mode'
      default: return 'category'
    }
  }

  /**
   * Get current voting phase info
   */
  getCurrentVotingPhase(): VotingPhase | null {
    return this.currentVotingPhase
  }

  /**
   * Enable/disable voting system
   */
  setVotingEnabled(enabled: boolean): void {
    this.votingEnabled = enabled
  }

  /**
   * Private helper methods for voting
   */
  private getDefaultVotingTitle(type: VotingPhase['type']): string {
    switch (type) {
      case 'category': return 'Choose Next Category'
      case 'decade': return 'Choose Time Period'
      case 'game-mode': return 'Choose Game Mode'
      case 'theme': return 'Choose Theme'
      default: return 'Vote Now'
    }
  }

  private getDefaultVotingDescription(type: VotingPhase['type']): string {
    switch (type) {
      case 'category': return 'Vote for the music category for the next round'
      case 'decade': return 'Vote for the era of music for the next round'
      case 'game-mode': return 'Vote for the game mode for the next round'
      case 'theme': return 'Vote for the theme for the next round'
      default: return 'Cast your vote to decide what happens next'
    }
  }

  private applyCategory(category: string): void {
    // Implementation would filter next questions by category
    // For now, just store the preference
    console.log(`Applying category filter: ${category}`)
  }

  private applyDecade(decade: string): void {
    // Implementation would filter next questions by decade
    console.log(`Applying decade filter: ${decade}`)
  }

  private applyGameMode(gameMode: string): void {
    // Implementation would change game mechanics
    console.log(`Applying game mode: ${gameMode}`)
    
    // Could modify time limits, scoring, etc. based on selected mode
    switch (gameMode) {
      case 'speed':
        this.gameState.timePerQuestion = 15
        break
      case 'challenge':
        this.gameState.timePerQuestion = 45
        break
      default:
        this.gameState.timePerQuestion = 30
    }
  }

  /**
   * Clean up voting data when game ends
   */
  cleanupVoting(): void {
    votingSocketHandler.cleanupGame(this.gameState.gameId)
    this.currentVotingPhase = null
  }
} 