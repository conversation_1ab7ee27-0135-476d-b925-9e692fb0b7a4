import prisma, { <PERSON><PERSON>rror, QuizUtils, type GameSession, type GameAnswer, type QuizTrack, type User, Prisma } from './prisma'
import { MPDLibrarySync, type MPDLibraryTrack } from './mpd-sync'

// Simple Fisher-Yates shuffle function for non-async contexts
function simpleShuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

export interface QuizQuestion {
  id: string
  type: 'artist' | 'title' | 'album' | 'year' | 'genre' | 'chart-position' | 'decade' | 'timeline-placement'
  question: string
  correctAnswer: string
  options: string[]
  track: MPDLibraryTrack
  trackPreviewStart?: number
  difficulty: number
  timeLimit: number
  hints?: string[]
  audioEffect?: 'rewind' | 'fast' | 'slow' | null
  metadata?: any
}

export interface QuizSession {
  id: string
  userId?: string
  gameMode: string
  questions: QuizQuestion[]
  currentQuestionIndex: number
  answers: QuizAnswer[]
  score: number
  streak: number
  maxStreak: number
  startTime: Date
  endTime?: Date
  settings: QuizSettings
  metadata?: any
}

export interface QuizAnswer {
  questionId: string
  userAnswer: string
  isCorrect: boolean
  timeSpent: number
  pointsEarned: number
  hintsUsed: number
  streak: number
  metadata?: any
}

export interface QuizSettings {
  totalQuestions: number
  difficultyLevel: number
  timeLimit: number
  genre?: string
  decade?: string
  artist?: string
  enableHints: boolean
  autoPlay: boolean
  volume: number
  previewDuration: number
  // Custom quiz settings
  questionTypes?: {
    artist?: boolean
    title?: boolean
    album?: boolean
    year?: boolean
    genre?: boolean
    chartPosition?: boolean
  }
}

export interface QuizSessionResult {
  session: QuizSession
  stats: {
    totalQuestions: number
    correctAnswers: number
    accuracy: number
    totalScore: number
    averageTime: number
    fastestAnswer: number
    slowestAnswer: number
    streakRecord: number
    difficulty: number
  }
  achievements?: string[]
}

export class QuizDataManager {
  private librarySync: MPDLibrarySync
  
  constructor(librarySync: MPDLibrarySync) {
    this.librarySync = librarySync
  }
  
  /**
   * Generate quiz questions based on game mode and settings
   */
  async generateQuizQuestions(
    gameMode: string,
    settings: QuizSettings
  ): Promise<QuizQuestion[]> {
    try {
      const perfStart = Date.now()
      // Handle both totalQuestions and questionCount for backward compatibility
      const questionCount = settings.totalQuestions || (settings as any).questionCount || 10
      console.log(`[Quiz-Data] Generating ${questionCount} questions for ${gameMode} mode`)
      
      // Get tracks directly from database instead of using broken QuizUtils
      let tracks: any[] = []
      try {
        // Build where clause based on settings
        const where: any = {}
        
        // Filter by genre if specified
        if (settings.genre && settings.genre !== 'all') {
          where.genre = settings.genre
        }
        
        // Filter by decade if specified
        if (settings.decade) {
          const startYear = parseInt(settings.decade)
          const endYear = startYear + 9
          where.year = {
            gte: startYear,
            lte: endYear
          }
        }
        
        // Filter by category if specified
        const category = (settings as any).category
        if (category && category !== 'all') {
          where.quizCategories = { contains: `"${category}"` }
        }
        
        // For year-based game modes, only get tracks with valid years
        if (gameMode === 'guess-the-year' || gameMode === 'decade-challenge') {
          where.year = {
            not: null,
            gte: 1900,  // Reasonable minimum year
            lte: new Date().getFullYear() + 1  // Allow current year + 1 for recent releases
          }
        }
        
        // For album art mode, only get tracks with album art
        if (gameMode === 'album-art') {
          where.OR = [
            { albumArtUrl: { not: null } },
            { localAlbumArtThumbnail: { not: null } },
            { localAlbumArtCover: { not: null } },
            { localAlbumArtOriginal: { not: null } }
          ]
        }
        
        // Get tracks from database with proper filters
        const dbTracks = await prisma.quizTrack.findMany({
          where,
          take: Math.min(questionCount * 50, 2000), // Get much more tracks for better variety
          orderBy: [
            // Randomize order using a combination of fields
            { updatedAt: 'desc' },
            { createdAt: 'desc' }
          ]
        })
        
        if (dbTracks.length > 0) {
          // Convert database tracks to MPD format for compatibility
          tracks = dbTracks.map((track: any) => ({
            file: track.mpdFilePath,
            title: track.title,
            artist: track.artist,
            album: track.album,
            date: track.year?.toString() || '',
            genre: track.genre,
            time: track.duration,
            quizTrackId: track.id,
            albumArtUrl: track.albumArtUrl,
            localAlbumArtCover: track.localAlbumArtCover,
            localAlbumArtThumbnail: track.localAlbumArtThumbnail,
            localAlbumArtOriginal: track.localAlbumArtOriginal,
            difficultyRating: track.difficultyRating,
            popularityScore: track.popularityScore,
            chartPosition: track.chartPosition,
            chartCountry: track.chartCountry,
            // Parse similarArtists from JSON string
            similarArtists: track.similarArtists ? JSON.parse(track.similarArtists) : []
          }))
          
          // Shuffle for randomness
          const { RandomUtils } = await import('@/lib/utils')
          tracks = RandomUtils.shuffle(tracks)
          console.log(`[Quiz-Data] Got ${tracks.length} tracks from database`)
        }
      } catch (error) {
        console.warn('[Quiz-Data] Database query failed:', error)
      }
      
      // If database query failed or returned no results, try MPD library sync as fallback
      if (tracks.length === 0) {
        console.log('[Quiz-Data] No tracks from database, trying MPD library sync')
        try {
          tracks = await this.librarySync.getRandomTracks(
            questionCount * 20, // Much larger multiplier for better variety
            {
              genre: settings.genre,
              decade: settings.decade,
              difficulty: settings.difficultyLevel,
              minPopularity: this.getMinPopularityForMode(gameMode),
              minLastPlayedMinutes: 180, // avoid repeats within last 3 hours
              category: (settings as any).category,
              thematicTag: (settings as any).thematicTag,
              specialList: (settings as any).specialList
            }
          )
        } catch (error) {
          console.warn('[Quiz-Data] MPD library sync also failed:', error)
        }
      }
      
      if (tracks.length === 0) {
        console.warn('[Quiz-Data] No tracks found from database, falling back to mock questions')
        // Fall back to mock questions instead of throwing error
        return this.generateMockQuestions(gameMode, questionCount)
      }
      
      // Filter tracks to ensure we have proper metadata and file paths
      console.log(`[Quiz-Data] Filtering ${tracks.length} tracks for ${gameMode} mode`)
      
      const usableTracks = tracks.filter((t, index) => {
        const track = t as any
        const hasFile = !!(track.file)
        const hasArtist = track.artist && track.artist !== 'Unknown Artist' && track.artist !== ''
        const hasTitle = track.title && track.title !== 'Unknown Title' && track.title !== ''
        const hasBasicData = hasFile && hasArtist && hasTitle
        
        // Log first few tracks for debugging
        if (index < 5) {
          console.log(`[Quiz-Data] Track ${index}: file="${track.file}", artist="${track.artist}", title="${track.title}", album="${track.album}"`)
          console.log(`[Quiz-Data] Track ${index}: hasFile=${hasFile}, hasArtist=${hasArtist}, hasTitle=${hasTitle}, hasBasicData=${hasBasicData}`)
        }
        
        // Additional validation for album art quiz mode
        if (gameMode === 'album-art') {
          const hasAlbumArt = track.albumArtUrl || 
                            track.localAlbumArtThumbnail || 
                            track.localAlbumArtCover || 
                            track.localAlbumArtOriginal
          if (index < 3) {
            console.log(`[Quiz-Data] Track ${index}: albumArtUrl=${!!track.albumArtUrl}, hasAlbumArt=${hasAlbumArt}`)
          }
          return hasBasicData && hasAlbumArt
        }
        
        // Additional validation for audio tricks mode when album questions might be asked
        if (gameMode === 'audio-manipulation') {
          // For audio tricks, we should have album data since it can ask album questions
          const hasAlbum = track.album && track.album !== 'Unknown Album'
          return hasBasicData && hasAlbum
        }
        
        // Additional validation for Hitster timeline mode
        if (gameMode === 'hitster-timeline') {
          const hasYear = !!(track.year || (track.date && parseInt(track.date.split('-')[0])))
          if (index < 5) {
            console.log(`[Quiz-Data] Track ${index} year check: year=${track.year}, date=${track.date}, hasYear=${hasYear}`)
          }
          return hasBasicData && hasYear
        }
        
        // Additional validation for year-based modes
        if (gameMode === 'guess-the-year' || gameMode === 'decade-challenge') {
          const yearStr = track.date ? track.date.toString() : ''
          const year = parseInt(yearStr)
          const hasValidYear = !isNaN(year) && year >= 1900 && year <= new Date().getFullYear() + 1
          if (index < 5) {
            console.log(`[Quiz-Data] Track ${index} year validation: year=${year}, valid=${hasValidYear}`)
          }
          return hasBasicData && hasValidYear
        }
        
        return hasBasicData
      })

      if (usableTracks.length === 0) {
        if (gameMode === 'album-art') {
          console.warn('[Quiz-Data] No tracks with album art found for album art quiz, falling back to mock questions')
          console.warn('[Quiz-Data] Consider running sync with --update-cover-art to fetch missing album artwork')
          console.warn(`[Quiz-Data] Album art validation: out of ${tracks.length} tracks, 0 passed album art filter`)
        } else if (gameMode === 'audio-manipulation') {
          console.warn('[Quiz-Data] No tracks with proper album metadata found for audio tricks quiz, falling back to mock questions')
          console.warn('[Quiz-Data] Audio tricks requires tracks with valid album names to generate album questions')
        } else if (gameMode === 'hitster-timeline') {
          console.warn('[Quiz-Data] No tracks with year information found for Hitster timeline quiz')
          console.warn('[Quiz-Data] Hitster timeline requires tracks with year metadata')
          console.warn(`[Quiz-Data] Year validation: out of ${tracks.length} tracks, 0 had year information`)
        } else if (gameMode === 'guess-the-year' || gameMode === 'decade-challenge') {
          console.warn('[Quiz-Data] No tracks with valid year data found for year-based quiz')
          console.warn('[Quiz-Data] Year-based modes require tracks with valid year between 1900 and current year')
          console.warn(`[Quiz-Data] Year validation: out of ${tracks.length} tracks, 0 passed year validation`)
        } else {
          console.warn('[Quiz-Data] No tracks with proper metadata found, falling back to mock questions')
          console.warn(`[Quiz-Data] Basic validation: out of ${tracks.length} tracks, 0 passed basic validation`)
        }
        return this.generateMockQuestions(gameMode, questionCount)
      }
      
      // Log album art availability stats for album art quiz
      if (gameMode === 'album-art') {
        const totalOriginalTracks = tracks.length
        const tracksWithArt = usableTracks.length
        console.log(`[Quiz-Data] Album Art Quiz: ${tracksWithArt}/${totalOriginalTracks} tracks have album artwork (${((tracksWithArt/totalOriginalTracks)*100).toFixed(1)}%)`)
      }

      let finalQuestions: QuizQuestion[] = []

      if (usableTracks.length < questionCount) {
        console.warn(`[Quiz-Data] Only ${usableTracks.length} tracks with file paths found (need ${questionCount}). Using mix of real tracks and mock questions.`)
        
        // Use all available real tracks with proper shuffling
        const { RandomUtils } = await import('@/lib/utils')
        const selectedTracks = RandomUtils.shuffle(usableTracks)
        
        // Generate questions from real tracks
        for (let i = 0; i < selectedTracks.length; i++) {
          const track = selectedTracks[i]
          try {
            const question = await this.generateQuestionForTrack(track, gameMode, settings)
            finalQuestions.push(question)
          } catch (error) {
            console.warn(`[Quiz-Data] Failed to generate question for track "${track.title}":`, error.message)
            // Skip this track and continue with the next one
          }
        }
        
        // Fill remaining with mock questions
        const remainingCount = questionCount - selectedTracks.length
        const mockQuestions = this.generateMockQuestions(gameMode, remainingCount)
        finalQuestions.push(...mockQuestions)
        
        // Shuffle the final mix with proper algorithm
        finalQuestions = RandomUtils.shuffle(finalQuestions)
      } else {
        // Properly shuffle and select required number from usable tracks
        const { RandomUtils } = await import('@/lib/utils')
        const selectedTracks = RandomUtils.sample(usableTracks, questionCount)
        
        // Generate questions based on game mode
        for (let i = 0; i < selectedTracks.length; i++) {
          const track = selectedTracks[i]
          try {
            const question = await this.generateQuestionForTrack(track, gameMode, settings)
            finalQuestions.push(question)
          } catch (error) {
            console.warn(`[Quiz-Data] Failed to generate question for track "${track.title}":`, error.message)
            // Skip this track and continue with the next one
          }
        }
        
        // If we didn't get enough questions due to validation failures, add more
        if (finalQuestions.length < questionCount) {
          const additionalNeeded = questionCount - finalQuestions.length
          console.warn(`[Quiz-Data] Only generated ${finalQuestions.length} questions, need ${additionalNeeded} more`)
          const mockQuestions = this.generateMockQuestions(gameMode, additionalNeeded)
          finalQuestions.push(...mockQuestions)
        }
      }
      
      const duration = Date.now() - perfStart
      console.log(`[Quiz-Data] Generated ${finalQuestions.length} questions successfully in ${duration} ms`)
      return finalQuestions
      
    } catch (error) {
      console.error('[Quiz-Data] Question generation failed:', error)
      console.warn('[Quiz-Data] Falling back to mock questions due to error')
      // Fall back to mock questions on any error
      return this.generateMockQuestions(gameMode, questionCount)
    }
  }
  
  // Private helper methods
  private async generateQuestionForTrack(
    track: MPDLibraryTrack,
    gameMode: string,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    const questionId = `${track.quizTrackId || track.file}_${Date.now()}_${Math.random()}`

    // Handle custom mode with specific question types
    if (gameMode === 'custom' && settings.questionTypes) {
      const enabledTypes = Object.entries(settings.questionTypes)
        .filter(([_, enabled]) => enabled)
        .map(([type, _]) => type)

      if (enabledTypes.length === 0) {
        // Fallback to artist question if no types selected
        return this.generateArtistQuestion(questionId, track, settings)
      }

      // Randomly select one of the enabled question types
      const randomType = enabledTypes[Math.floor(Math.random() * enabledTypes.length)]

      switch (randomType) {
        case 'artist':
          return this.generateArtistQuestion(questionId, track, settings)
        case 'title':
          return this.generateTitleQuestion(questionId, track, settings)
        case 'album':
          return this.generateAlbumQuestion(questionId, track, settings)
        case 'year':
          return this.generateYearQuestion(questionId, track, settings)
        case 'genre':
          return this.generateGenreQuestion(questionId, track, settings)
        case 'chartPosition':
          return this.generateChartPositionQuestion(questionId, track, settings)
        default:
          return this.generateArtistQuestion(questionId, track, settings)
      }
    }

    // Handle predefined game modes
    switch (gameMode) {
      case 'classic':
        return this.generateArtistQuestion(questionId, track, settings)
      case 'chart-position':
        return this.generateChartPositionQuestion(questionId, track, settings)
      case 'decade-challenge':
        return this.generateDecadeQuestion(questionId, track, settings)
      case 'genre-specialist':
        return this.generateGenreQuestion(questionId, track, settings)
      case 'guess-the-year':
        return this.generateYearQuestion(questionId, track, settings)
      case 'quick-fire':
        return this.generateQuickFireQuestion(questionId, track, settings)
      case 'audio-manipulation':
        return this.generateAudioTricksQuestion(questionId, track, settings)
      case 'album-art':
        return this.generateAlbumArtQuestion(questionId, track, settings)
      case 'audio-fingerprint':
        return this.generateAudioFingerprintQuestion(questionId, track, settings)
      case 'hitster-timeline':
        return this.generateHitsterQuestion(questionId, track, settings)
      case 'ultimote':
        // ulTimote uses dynamic categories, default to artist question
        return this.generateArtistQuestion(questionId, track, settings)
      default:
        return this.generateArtistQuestion(questionId, track, settings)
    }
  }
  
  private async generateArtistQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    const wrongOptions: string[] = []
    
    // First, try to use similar artists from the track data for more intelligent distractors
    if (track.similarArtists && Array.isArray(track.similarArtists)) {
      for (const artist of track.similarArtists) {
        if (wrongOptions.length >= 3) break
        if (artist && artist !== (track.artist || '') && !wrongOptions.includes(artist)) {
          wrongOptions.push(artist)
        }
      }
    }
    
    // Fill remaining options with random artists if we don't have enough similar artists
    if (wrongOptions.length < 3) {
      const additionalArtists = await this.getRandomArtists(3 - wrongOptions.length, [(track.artist || ''), ...wrongOptions])
      wrongOptions.push(...additionalArtists)
    }
    
    // Ensure we have exactly 3 wrong options
    const finalWrongOptions = wrongOptions.slice(0, 3)
    const { RandomUtils } = await import('@/lib/utils')
    
    // Since we've already filtered out tracks with 'Unknown Artist', we can safely use track.artist
    const artistName = track.artist || 'Unknown Artist'
    const options = RandomUtils.shuffle([artistName, ...finalWrongOptions])
    
    return {
      id: questionId,
      type: 'artist',
      question: 'Who is the artist of this song?',
      correctAnswer: artistName,
      options,
      track,
      difficulty: track.difficultyRating || 3,
      timeLimit: settings.timeLimit,
      trackPreviewStart: this.getRandomPreviewStart(track.time || 0)
    }
  }
  
  private async generateChartPositionQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    const chartPosition = track.chartPosition || Math.floor(Math.random() * 100) + 1
    const yearStr = track.date ? ` in ${track.date}` : ''
    
    return {
      id: questionId,
      type: 'chart-position',
      question: `What was the highest chart position of "${track.title || 'this song'}" by ${track.artist || 'the artist'}${yearStr}?`,
      correctAnswer: chartPosition.toString(),
      options: [], // Empty array triggers slider interface
      track,
      difficulty: track.difficultyRating || 3,
      timeLimit: settings.timeLimit,
      trackPreviewStart: this.getRandomPreviewStart(track.time || 0)
    }
  }
  
  private async generateDecadeQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    // Extract year from track data with validation
    let year: number | null = null
    
    if (track.date) {
      const parsedYear = parseInt(track.date.toString())
      if (!isNaN(parsedYear) && parsedYear >= 1900 && parsedYear <= new Date().getFullYear() + 1) {
        year = parsedYear
      }
    }
    
    // If no valid year, throw error to skip this track
    if (!year) {
      throw new Error(`Track "${track.title}" has no valid year data for year-based question`)
    }
    
    return {
      id: questionId,
      type: 'year',
      question: `What year was "${track.title || 'this song'}" by ${track.artist || 'the artist'} released?`,
      correctAnswer: year.toString(),
      options: [], // Empty array triggers slider interface
      track,
      difficulty: track.difficultyRating || 3,
      timeLimit: settings.timeLimit,
      trackPreviewStart: this.getRandomPreviewStart(track.time || 0),
      metadata: {
        actualYear: year
      }
    }
  }
  
  private async generateGenreQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    const wrongOptions = await this.getRandomGenres(3, [track.genre || ''])
    const { RandomUtils } = await import('@/lib/utils')
    const options = RandomUtils.shuffle([(track.genre || 'Unknown'), ...wrongOptions])

    return {
      id: questionId,
      type: 'genre',
      question: `What genre is "${track.title || 'this song'}" by ${track.artist || 'the artist'}?`,
      correctAnswer: track.genre || 'Unknown',
      options,
      track,
      difficulty: track.difficultyRating || 3,
      timeLimit: settings.timeLimit,
      trackPreviewStart: this.getRandomPreviewStart(track.time || 0)
    }
  }

  private async generateTitleQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    const wrongOptions = await this.getRandomTitles(3, [track.title || ''])
    const { RandomUtils } = await import('@/lib/utils')
    
    // Since we've already filtered out tracks with 'Unknown Title', we can safely use track.title
    const titleName = track.title || 'Unknown Title'
    const artistName = track.artist || 'Unknown Artist'
    const options = RandomUtils.shuffle([titleName, ...wrongOptions])

    return {
      id: questionId,
      type: 'title',
      question: `What is the title of this song by ${artistName}?`,
      correctAnswer: titleName,
      options,
      track,
      difficulty: track.difficultyRating || 3,
      timeLimit: settings.timeLimit,
      trackPreviewStart: this.getRandomPreviewStart(track.time || 0)
    }
  }

  private async generateAlbumQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    // If track has no album, fall back to a different question type
    if (!track.album || track.album === 'Unknown Album') {
      console.warn(`[Quiz-Data] Track "${track.title}" has no album metadata, generating artist question instead`)
      return this.generateArtistQuestion(questionId, track, settings)
    }
    
    const wrongOptions = await this.getRandomAlbums(3, [track.album])
    const { RandomUtils } = await import('@/lib/utils')
    
    // Use track data with validated album name
    const albumName = track.album
    const titleName = track.title || 'Unknown Title'
    const artistName = track.artist || 'Unknown Artist'
    const options = RandomUtils.shuffle([albumName, ...wrongOptions])

    return {
      id: questionId,
      type: 'album',
      question: `Which album is "${titleName}" by ${artistName} from?`,
      correctAnswer: albumName,
      options,
      track,
      difficulty: track.difficultyRating || 3,
      timeLimit: settings.timeLimit,
      trackPreviewStart: this.getRandomPreviewStart(track.time || 0)
    }
  }
  
  private async getRandomArtists(count: number, exclude: string[] = []): Promise<string[]> {
    const artists = await this.librarySync.getAvailableArtists(100)
    
    // If no artists available from database, use fallback artists
    const fallbackArtists = [
      'Adele', 'The Beatles', 'Queen', 'Michael Jackson', 'Madonna', 'Elvis Presley',
      'Bob Dylan', 'David Bowie', 'Pink Floyd', 'Led Zeppelin', 'The Rolling Stones',
      'U2', 'Coldplay', 'Ed Sheeran', 'Taylor Swift', 'Beyoncé', 'Eminem', 'Drake',
      'Rihanna', 'Lady Gaga', 'Kanye West', 'Bruno Mars', 'John Lennon', 'Paul McCartney',
      'Prince', 'Whitney Houston', 'Mariah Carey', 'Celine Dion', 'Elton John', 'Sting'
    ]
    
    const availableArtists = artists.length > 0 ? artists : fallbackArtists
    
    const { RandomUtils } = await import('@/lib/utils')
    const filtered = availableArtists.filter(artist => !exclude.includes(artist))
    const shuffled = RandomUtils.shuffle(filtered)
    const filteredArtists = shuffled.slice(0, count)
    
    return filteredArtists
  }
  
  private async generateQuickFireQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    // Quick Fire uses a mix of fast-answerable question types
    const quickFireTypes = ['artist', 'title', 'album', 'genre']
    const randomType = quickFireTypes[Math.floor(Math.random() * quickFireTypes.length)]
    
    switch (randomType) {
      case 'artist':
        return this.generateArtistQuestion(questionId, track, settings)
      case 'title':
        return this.generateTitleQuestion(questionId, track, settings)
      case 'album':
        return this.generateAlbumQuestion(questionId, track, settings)
      case 'genre':
        return this.generateGenreQuestion(questionId, track, settings)
      default:
        return this.generateArtistQuestion(questionId, track, settings)
    }
  }

  private async generateAudioTricksQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    // Audio Tricks primarily focuses on artist recognition with modified audio
    // But can also include title and album questions for variety
    const audioTricksTypes = ['artist', 'title', 'album']
    const randomType = audioTricksTypes[Math.floor(Math.random() * audioTricksTypes.length)]
    
    // Generate the base question
    let question: QuizQuestion
    switch (randomType) {
      case 'artist':
        question = await this.generateArtistQuestion(questionId, track, settings)
        break
      case 'title':
        question = await this.generateTitleQuestion(questionId, track, settings)
        break
      case 'album':
        question = await this.generateAlbumQuestion(questionId, track, settings)
        break
      default:
        question = await this.generateArtistQuestion(questionId, track, settings)
    }
    
    // Assign random audio effect for audio manipulation mode
    const audioEffects = ['rewind', 'fast', 'slow', null] as const
    const randomEffect = audioEffects[Math.floor(Math.random() * audioEffects.length)]
    
    // Add audio effect to the question
    return {
      ...question,
      audioEffect: randomEffect
    }
  }

  private async generateAlbumArtQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    // Album Art Quiz can focus on artist, album, or even release year recognition
    const albumArtTypes = ['artist', 'album', 'year']
    const randomType = albumArtTypes[Math.floor(Math.random() * albumArtTypes.length)]
    
    let baseQuestion: QuizQuestion
    
    switch (randomType) {
      case 'artist':
        baseQuestion = await this.generateArtistQuestion(questionId, track, settings)
        baseQuestion.question = `Who is the artist of this album?`
        break
      case 'album':
        baseQuestion = await this.generateAlbumQuestion(questionId, track, settings)
        baseQuestion.question = `What is the name of this album?`
        break
      case 'year':
        baseQuestion = await this.generateYearQuestion(questionId, track, settings)
        baseQuestion.question = `When was this album released?`
        break
      default:
        baseQuestion = await this.generateArtistQuestion(questionId, track, settings)
        baseQuestion.question = `Who is the artist of this album?`
        break
    }
    
    // Enhance the question with album art metadata
    return {
      ...baseQuestion,
      metadata: {
        ...baseQuestion.metadata,
        isAlbumArtQuiz: true,
        albumArtUrl: track.albumArtUrl || track.localAlbumArtCover || null,
        showAlbumArt: true,
        hideAudioPlayer: true // Album art quiz should be visual-first
      }
    }
  }

  private async generateAudioFingerprintQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    // Audio Fingerprint is expert-level, primarily artist recognition from micro-clips
    // Occasionally includes title recognition for variety
    const fingerprintTypes = ['artist', 'artist', 'artist', 'title'] // Weighted toward artist
    const randomType = fingerprintTypes[Math.floor(Math.random() * fingerprintTypes.length)]
    
    switch (randomType) {
      case 'artist':
        return this.generateArtistQuestion(questionId, track, settings)
      case 'title':
        return this.generateTitleQuestion(questionId, track, settings)
      default:
        return this.generateArtistQuestion(questionId, track, settings)
    }
  }

  private async generateHitsterQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    // Extract year from track - handle both 'year' field and 'date' field
    let year: number | undefined
    if (track.year) {
      year = track.year
    } else if (track.date) {
      // Date might be in format "YYYY-MM-DD" or just "YYYY"
      const yearMatch = track.date.match(/^(\d{4})/)
      if (yearMatch) {
        year = parseInt(yearMatch[1])
      }
    }

    if (!year) {
      console.warn(`[Quiz-Data] Track missing year for Hitster: ${track.title} by ${track.artist}`)
      // Fallback to a regular artist question if no year available
      return this.generateArtistQuestion(questionId, track, settings)
    }

    return {
      id: questionId,
      type: 'timeline-placement' as const,
      question: `Place this song in the timeline: "${track.title}" by ${track.artist}`,
      correctAnswer: year.toString(),
      options: [], // Timeline placement doesn't use multiple choice options
      track: {
        ...track,
        year // Ensure year is included
      },
      difficulty: track.difficultyRating || 3,
      timeLimit: settings.timeLimit,
      trackPreviewStart: this.getRandomPreviewStart(track.time || 0),
      metadata: {
        year,
        decade: Math.floor(year / 10) * 10,
        isHitsterQuestion: true,
        albumArt: track.albumArtUrl || track.localAlbumArtCover || null
      }
    }
  }

  private async getRandomGenres(count: number, exclude: string[] = []): Promise<string[]> {
    const genres = await this.librarySync.getAvailableGenres()
    const { RandomUtils } = await import('@/lib/utils')
    const filtered = genres.filter(genre => !exclude.includes(genre))
    const shuffled = RandomUtils.shuffle(filtered)
    return shuffled.slice(0, count)
  }

  private async getRandomTitles(count: number, exclude: string[] = []): Promise<string[]> {
    // Try to get titles from database first
    try {
      const titles = await this.librarySync.getAvailableTitles?.(100) || []
      if (titles.length > 0) {
        const { RandomUtils } = await import('@/lib/utils')
        const filtered = titles.filter(title => !exclude.includes(title))
        const shuffled = RandomUtils.shuffle(filtered)
        return shuffled.slice(0, count)
      }
    } catch (error) {
      console.warn('[Quiz-Data] Could not get titles from database, using fallback')
    }

    // Fallback titles
    const fallbackTitles = [
      'Yesterday', 'Bohemian Rhapsody', 'Stairway to Heaven', 'Imagine', 'Hotel California',
      'Sweet Child O\' Mine', 'Billie Jean', 'Like a Rolling Stone', 'Smells Like Teen Spirit',
      'Purple Haze', 'What\'s Going On', 'Respect', 'Good Vibrations', 'Johnny B. Goode',
      'I Want to Hold Your Hand', 'Satisfaction', 'My Generation', 'Born to Run'
    ]

    const { RandomUtils } = await import('@/lib/utils')
    const filtered = fallbackTitles.filter(title => !exclude.includes(title))
    const shuffled = RandomUtils.shuffle(filtered)
    return shuffled.slice(0, count)
  }

  private async getRandomAlbums(count: number, exclude: string[] = []): Promise<string[]> {
    // Try to get albums from database first
    try {
      const albums = await this.librarySync.getAvailableAlbums?.(100) || []
      if (albums.length > 0) {
        const { RandomUtils } = await import('@/lib/utils')
        const filtered = albums.filter(album => !exclude.includes(album))
        const shuffled = RandomUtils.shuffle(filtered)
        return shuffled.slice(0, count)
      }
    } catch (error) {
      console.warn('[Quiz-Data] Could not get albums from database, using fallback')
    }

    // Fallback albums
    const fallbackAlbums = [
      'Abbey Road', 'A Night at the Opera', 'Led Zeppelin IV', 'The Dark Side of the Moon',
      'Thriller', 'Nevermind', 'Sgt. Pepper\'s Lonely Hearts Club Band', 'Pet Sounds',
      'Revolver', 'Highway 61 Revisited', 'Are You Experienced', 'What\'s Going On',
      'Born to Run', 'London Calling', 'OK Computer', 'The Velvet Underground & Nico'
    ]

    const { RandomUtils } = await import('@/lib/utils')
    const filtered = fallbackAlbums.filter(album => !exclude.includes(album))
    const shuffled = RandomUtils.shuffle(filtered)
    return shuffled.slice(0, count)
  }
  
  private generateChartPositionOptions(correctPosition: number): string[] {
    const options: string[] = []
    const usedPositions = new Set([correctPosition])
    
    while (options.length < 3) {
      let position: number
      if (correctPosition <= 10) {
        position = Math.floor(Math.random() * 20) + 1
      } else if (correctPosition <= 50) {
        position = Math.floor(Math.random() * 80) + 1
      } else {
        position = Math.floor(Math.random() * 100) + 1
      }
      
      if (!usedPositions.has(position)) {
        options.push(position.toString())
        usedPositions.add(position)
      }
    }
    
    return options
  }
  
  private generateDecadeOptions(correctDecade: string): string[] {
    const decades = ['1960s', '1970s', '1980s', '1990s', '2000s', '2010s', '2020s']
    // Use simple array shuffle without dynamic import
    const filtered = decades.filter(decade => decade !== correctDecade)
    // Fisher-Yates shuffle
    for (let i = filtered.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [filtered[i], filtered[j]] = [filtered[j], filtered[i]]
    }
    return filtered.slice(0, 3)
  }
  
  private getRandomPreviewStart(duration: number): number {
    if (duration <= 30) return 0
    const maxStart = Math.max(0, duration - 30)
    return Math.floor(Math.random() * maxStart)
  }
  
  private getMinPopularityForMode(gameMode: string): number {
    // Reduced all minimum popularity requirements to allow much more variety
    switch (gameMode) {
      case 'chart-position':
        return 0 // No minimum - allow all tracks
      case 'classic':
        return 0 // No minimum - allow all tracks
      case 'decade-challenge':
        return 0 // No minimum - allow all tracks
      case 'genre-specialist':
        return 0 // No minimum - allow all tracks
      case 'ultimote':
        return 0 // No minimum - allow all tracks
      default:
        return 0 // No minimum - allow all tracks
    }
  }

  private generateMockQuestions(gameMode: string, count: number, settings?: QuizSettings): QuizQuestion[] {
    console.log(`[Quiz-Data] Generating ${count} mock questions for ${gameMode} mode`)
    
    const mockTracks = [
      { id: '1', title: 'Bohemian Rhapsody', artist: 'Queen', album: 'A Night at the Opera', year: 1975, genre: 'Rock', file: '/test1.mp3', chartPosition: 1 },
      { id: '2', title: 'Billie Jean', artist: 'Michael Jackson', album: 'Thriller', year: 1982, genre: 'Pop', file: '/test2.mp3', chartPosition: 1 },
      { id: '3', title: 'Imagine', artist: 'John Lennon', album: 'Imagine', year: 1971, genre: 'Rock', file: '/test3.mp3', chartPosition: 3 },
      { id: '4', title: 'Like a Rolling Stone', artist: 'Bob Dylan', album: 'Highway 61 Revisited', year: 1965, genre: 'Rock', file: '/test4.mp3', chartPosition: 2 },
      { id: '5', title: 'What\'s Going On', artist: 'Marvin Gaye', album: 'What\'s Going On', year: 1971, genre: 'Soul', file: '/test5.mp3', chartPosition: 4 },
      { id: '6', title: 'Hotel California', artist: 'Eagles', album: 'Hotel California', year: 1976, genre: 'Rock', file: '/test6.mp3', chartPosition: 1 },
      { id: '7', title: 'Stairway to Heaven', artist: 'Led Zeppelin', album: 'Led Zeppelin IV', year: 1971, genre: 'Rock', file: '/test7.mp3', chartPosition: 37 },
      { id: '8', title: 'Yesterday', artist: 'The Beatles', album: 'Help!', year: 1965, genre: 'Pop', file: '/test8.mp3', chartPosition: 1 },
      { id: '9', title: 'Purple Haze', artist: 'Jimi Hendrix', album: 'Are You Experienced', year: 1967, genre: 'Rock', file: '/test9.mp3', chartPosition: 65 },
      { id: '10', title: 'Respect', artist: 'Aretha Franklin', album: 'I Never Loved a Man', year: 1967, genre: 'Soul', file: '/test10.mp3', chartPosition: 1 },
    ]
    
    const fallbackArtists = [
      'Adele', 'The Beatles', 'Queen', 'Michael Jackson', 'Madonna', 'Elvis Presley',
      'Bob Dylan', 'David Bowie', 'Pink Floyd', 'Led Zeppelin', 'The Rolling Stones',
      'U2', 'Coldplay', 'Ed Sheeran', 'Taylor Swift', 'Beyoncé', 'Eminem', 'Drake',
      'Rihanna', 'Lady Gaga', 'Kanye West', 'Bruno Mars', 'John Lennon', 'Paul McCartney',
      'Prince', 'Whitney Houston', 'Mariah Carey', 'Celine Dion', 'Elton John', 'Sting'
    ]
    
    return mockTracks.slice(0, count).map((track, index) => {
      const questionId = `mock_${index}_${Date.now()}`
      
      if (gameMode === 'decade-challenge') {
        return {
          id: questionId,
          type: 'year' as const,
          question: `What year was "${track.title}" by ${track.artist} released?`,
          correctAnswer: track.year.toString(),
          options: [],
          track: {
            ...track,
            artist: track.artist,
            title: track.title,
            album: track.album,
            file: track.file
          } as any,
          difficulty: 3,
          timeLimit: settings?.timeLimit || 30,
          trackPreviewStart: 0
        }
      } else if (gameMode === 'chart-position') {
        return {
          id: questionId,
          type: 'chart-position' as const,
          question: `What was the highest chart position of "${track.title}" by ${track.artist}?`,
          correctAnswer: track.chartPosition.toString(),
          options: [],
          track: {
            ...track,
            artist: track.artist,
            title: track.title,
            album: track.album,
            file: track.file
          } as any,
          difficulty: 3,
          timeLimit: settings?.timeLimit || 30,
          trackPreviewStart: 0
        }
      } else if (gameMode === 'quick-fire') {
        // Generate mixed question types for Quick Fire mode
        const quickFireTypes = ['artist', 'title', 'album']
        const randomType = quickFireTypes[index % quickFireTypes.length]
        
        const filtered = fallbackArtists.filter(artist => artist !== track.artist)
        // Fisher-Yates shuffle
        for (let i = filtered.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [filtered[i], filtered[j]] = [filtered[j], filtered[i]]
        }
        const wrongOptions = filtered.slice(0, 3)
        
        if (randomType === 'title') {
          const wrongTitles = mockTracks
            .filter(t => t.title !== track.title)
            .map(t => t.title)
          wrongTitles = simpleShuffleArray(wrongTitles).slice(0, 3)
          
          const options = simpleShuffleArray([track.title, ...wrongTitles])
          
          return {
            id: questionId,
            type: 'title' as const,
            question: `What is the title of this song by ${track.artist}?`,
            correctAnswer: track.title,
            options,
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 3,
            timeLimit: settings?.timeLimit || 30,
            trackPreviewStart: 0
          }
        } else if (randomType === 'album') {
          const wrongAlbums = mockTracks
            .filter(t => t.album !== track.album)
            .map(t => t.album)
          wrongAlbums = simpleShuffleArray(wrongAlbums).slice(0, 3)
          
          const options = simpleShuffleArray([track.album, ...wrongAlbums])
          
          return {
            id: questionId,
            type: 'album' as const,
            question: `Which album is "${track.title}" by ${track.artist} from?`,
            correctAnswer: track.album,
            options,
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 3,
            timeLimit: settings?.timeLimit || 30,
            trackPreviewStart: 0
          }
        } else {
          // Default to artist
          const options = simpleShuffleArray([track.artist, ...wrongOptions])
          
          return {
            id: questionId,
            type: 'artist' as const,
            question: `Who is the artist of this song?`,
            correctAnswer: track.artist,
            options,
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 3,
            timeLimit: settings?.timeLimit || 30,
            trackPreviewStart: 0
          }
        }
      } else if (gameMode === 'audio-manipulation') {
        // Generate questions suitable for audio tricks (primarily artist recognition)
        const audioTricksTypes = ['artist', 'title', 'album']
        const randomType = audioTricksTypes[index % audioTricksTypes.length]
        
        if (randomType === 'title') {
          const wrongTitles = mockTracks
            .filter(t => t.title !== track.title)
            .map(t => t.title)
          wrongTitles = simpleShuffleArray(wrongTitles).slice(0, 3)
          
          const options = simpleShuffleArray([track.title, ...wrongTitles])
          
          const audioEffects = ['rewind', 'fast', 'slow', null] as const
          const randomEffect = audioEffects[index % audioEffects.length]
          
          return {
            id: questionId,
            type: 'title' as const,
            question: `What is the title of this song? (Audio effects applied)`,
            correctAnswer: track.title,
            options,
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 4, // Higher difficulty for audio tricks
            timeLimit: settings?.timeLimit || 30,
            trackPreviewStart: 0,
            audioEffect: randomEffect
          }
        } else if (randomType === 'album') {
          const wrongAlbums = mockTracks
            .filter(t => t.album !== track.album)
            .map(t => t.album)
          wrongAlbums = simpleShuffleArray(wrongAlbums).slice(0, 3)
          
          const options = simpleShuffleArray([track.album, ...wrongAlbums])
          
          const audioEffects = ['rewind', 'fast', 'slow', null] as const
          const randomEffect = audioEffects[index % audioEffects.length]
          
          return {
            id: questionId,
            type: 'album' as const,
            question: `Which album is this song from? (Audio effects applied)`,
            correctAnswer: track.album,
            options,
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 4,
            timeLimit: settings?.timeLimit || 30,
            trackPreviewStart: 0,
            audioEffect: randomEffect
          }
        } else {
          // Default to artist
          const wrongOptions = fallbackArtists
            .filter(artist => artist !== track.artist)
            .slice(0, 3)
          
          const options = simpleShuffleArray([track.artist, ...wrongOptions])
          
          const audioEffects = ['rewind', 'fast', 'slow', null] as const
          const randomEffect = audioEffects[index % audioEffects.length]
          
          return {
            id: questionId,
            type: 'artist' as const,
            question: `Who is the artist of this song? (Audio effects applied)`,
            correctAnswer: track.artist,
            options,
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 4,
            trackPreviewStart: 0,
            audioEffect: randomEffect
          }
        }
      } else if (gameMode === 'album-art') {
        // Generate questions suitable for album art recognition
        const albumArtTypes = ['artist', 'album', 'year']
        const randomType = albumArtTypes[index % albumArtTypes.length]
        
        if (randomType === 'album') {
          const wrongAlbums = mockTracks
            .filter(t => t.album !== track.album)
            .map(t => t.album)
          wrongAlbums = simpleShuffleArray(wrongAlbums).slice(0, 3)
          
          const options = simpleShuffleArray([track.album, ...wrongAlbums])
          
          return {
            id: questionId,
            type: 'album' as const,
            question: `Which album cover is shown?`,
            correctAnswer: track.album,
            options,
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 3,
            trackPreviewStart: 0
          }
        } else if (randomType === 'year') {
          return {
            id: questionId,
            type: 'year' as const,
            question: `What year was this album released?`,
            correctAnswer: track.year.toString(),
            options: [],
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 3,
            trackPreviewStart: 0
          }
        } else {
          // Default to artist
          const wrongOptions = fallbackArtists
            .filter(artist => artist !== track.artist)
            .slice(0, 3)
          
          const options = simpleShuffleArray([track.artist, ...wrongOptions])
          
          return {
            id: questionId,
            type: 'artist' as const,
            question: `Who is the artist of this album?`,
            correctAnswer: track.artist,
            options,
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 3,
            trackPreviewStart: 0
          }
        }
      } else if (gameMode === 'audio-fingerprint') {
        // Generate expert-level questions for audio fingerprint mode
        const fingerprintTypes = ['artist', 'title']
        const randomType = fingerprintTypes[index % fingerprintTypes.length]
        
        if (randomType === 'title') {
          const wrongTitles = mockTracks
            .filter(t => t.title !== track.title)
            .map(t => t.title)
          wrongTitles = simpleShuffleArray(wrongTitles).slice(0, 3)
          
          const options = simpleShuffleArray([track.title, ...wrongTitles])
          
          return {
            id: questionId,
            type: 'title' as const,
            question: `Identify this song from the micro-clip`,
            correctAnswer: track.title,
            options,
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 5, // Maximum difficulty for fingerprint mode
            trackPreviewStart: 0
          }
        } else {
          // Default to artist (most common for fingerprint mode)
          const wrongOptions = fallbackArtists
            .filter(artist => artist !== track.artist)
            .slice(0, 3)
          
          const options = simpleShuffleArray([track.artist, ...wrongOptions])
          
          return {
            id: questionId,
            type: 'artist' as const,
            question: `Who is the artist? (Expert audio fingerprint)`,
            correctAnswer: track.artist,
            options,
            track: {
              ...track,
              artist: track.artist,
              title: track.title,
              album: track.album,
              file: track.file
            } as any,
            difficulty: 5,
            trackPreviewStart: 0
          }
        }
      } else if (gameMode === 'hitster-timeline') {
        // Generate Hitster timeline placement questions
        return {
          id: questionId,
          type: 'timeline-placement' as const,
          question: `Place this song in the timeline: "${track.title}" by ${track.artist}`,
          correctAnswer: track.year.toString(),
          options: [], // Timeline placement doesn't use multiple choice options
          track: {
            ...track,
            artist: track.artist,
            title: track.title,
            album: track.album,
            file: track.file,
            year: track.year
          } as any,
          difficulty: 3,
          trackPreviewStart: 0,
          metadata: {
            year: track.year,
            decade: Math.floor(track.year / 10) * 10,
            isHitsterQuestion: true,
            albumArt: null // Mock questions don't have album art
          }
        }
      } else {
        const filtered = fallbackArtists.filter(artist => artist !== track.artist)
        // Fisher-Yates shuffle
        for (let i = filtered.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [filtered[i], filtered[j]] = [filtered[j], filtered[i]]
        }
        const wrongOptions = filtered.slice(0, 3)
        
        const options = simpleShuffleArray([track.artist, ...wrongOptions])
        
        return {
          id: questionId,
          type: 'artist' as const,
          question: `Who is the artist of this song?`,
          correctAnswer: track.artist,
          options,
          track: {
            ...track,
            artist: track.artist,
            title: track.title,
            album: track.album,
            file: track.file
          } as any,
          difficulty: 3,
          trackPreviewStart: 0
        }
      }
    })
  }

  // Alias for clarity – reuse decade method
  private async generateYearQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    return this.generateDecadeQuestion(questionId, track, settings)
  }
  
  private async generateHitsterQuestion(
    questionId: string,
    track: MPDLibraryTrack,
    settings: QuizSettings
  ): Promise<QuizQuestion> {
    // For Hitster mode, we need tracks with years - check both year and date fields
    const trackData = track as any
    const year = trackData.year || (trackData.date ? parseInt(trackData.date.split('-')[0]) : null)
    
    if (!year) {
      // Fallback to regular artist question if no year available
      console.warn('[Quiz-Data] Track has no year for Hitster mode, falling back to artist question')
      return this.generateArtistQuestion(questionId, track, settings)
    }
    
    // Return a timeline placement question
    return {
      id: questionId,
      type: 'timeline-placement' as any,
      question: 'Place this song in the correct position on the timeline',
      correctAnswer: year.toString(),
      options: [], // No options for timeline placement
      track: {
        ...trackData,
        year // Ensure year is included
      },
      trackPreviewStart: track.preview_start || this.getRandomPreviewStart(track.duration || 180),
      difficulty: settings.difficultyLevel || 3,
      timeLimit: settings.timeLimit,
      hints: [
        track.genre ? `Genre: ${track.genre}` : '',
        `Decade: ${Math.floor(year / 10) * 10}s`
      ].filter(h => h),
      audioEffect: null,
      metadata: {
        year,
        decade: `${Math.floor(year / 10) * 10}s`,
        exactYear: year,
        artist: trackData.artist,
        title: trackData.title,
        album: trackData.album
      }
    }
  }
} 