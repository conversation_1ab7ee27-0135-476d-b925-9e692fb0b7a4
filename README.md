# Music Quiz Application

A real-time multiplayer music quiz application with jukebox functionality, built with Next.js, Socket.IO, and MPD.

## Features

- **Multiplayer Quiz**: Real-time quiz games with multiple players
- **Multiple Game Modes**: 
  - UlTimote: Customizable quiz with mixed categories (music + general knowledge)
  - Classic: Traditional music identification quiz
  - General Knowledge: Trivia questions only
  - Genre Specialist, Decade Challenge, and more
- **Jukebox Mode**: Collaborative music player with voting
- **Smart Scoring**: Points based on answer speed (max 1000 points)
- **Live Leaderboard**: Real-time score updates
- **Audio Integration**: MPD-based music playback with crossfade support

## Quick Start

```bash
# Install dependencies
npm install

# Development mode
npm run dev

# Production mode with PM2
npm run prod:pm2
```

## Available Scripts

### Development

| Command | Description |
|---------|-------------|
| `npm run dev` | Start Next.js in development mode |
| `npm run dev:fast` | Start Next.js with telemetry disabled |
| `npm run dev:all` | Start all services (Next.js, Socket server, MPD proxy) |
| `npm run dev:pm2` | Start all services with PM2 monitoring |

### Production

| Command | Description |
|---------|-------------|
| `npm run build` | Build Next.js for production |
| `npm run start` | Start Next.js in production mode |
| `npm run prod:pm2` | Start all services in production with PM2 |
| `npm run build:production` | Build and optimize for production deployment |

### Socket Server (Multiplayer)

| Command | Description |
|---------|-------------|
| `npm run socket` | Start the multiplayer socket server |
| `npm run socket:prod` | Start socket server in production mode |
| `npm run mp:monitor` | Start socket server with debug monitoring |
| `npm run test-socket` | Test socket connection |

**Note**: The socket server uses `lib/socket-server.ts` as the main implementation, initialized through `scripts/socket-server.js`.

### MPD (Music Player Daemon)

| Command | Description |
|---------|-------------|
| `npm run mpd-proxy` | Start the MPD HTTP proxy server |
| `npm run sync` | Sync music library with database |
| `npm run quick-sync` | Quick sync (new content only) |
| `npm run debug-mpd` | Debug MPD connection issues |

### PM2 Process Management

| Command | Description |
|---------|-------------|
| `npm run pm2:start` | Start all services with PM2 |
| `npm run pm2:stop` | Stop all PM2 services |
| `npm run pm2:status` | Check PM2 service status |
| `npm run pm2:logs` | View PM2 logs |
| `npm run pm2:monit` | Open PM2 monitoring dashboard |

### Database

| Command | Description |
|---------|-------------|
| `npm run db:push` | Push Prisma schema to database |
| `npm run db:migrate` | Run database migrations |
| `npm run db:studio` | Open Prisma Studio GUI |

### Testing

| Command | Description |
|---------|-------------|
| `npm test` | Run all tests |
| `npm run test:unit` | Run unit tests |
| `npm run test:integration` | Run integration tests |
| `npm run test:e2e` | Run end-to-end tests |
| `npm run mp:test` | Run multiplayer tests |
| `npm run mp:mock` | Run mock multiplayer game |

### Utilities

| Command | Description |
|---------|-------------|
| `npm run lint` | Run ESLint |
| `npm run typecheck` | Run TypeScript type checking |
| `npm run analyze-data-quality` | Analyze music library data quality |
| `npm run process-missing-album-art` | Process missing album artwork |

### Audio Output Control

The `scripts/audio-switch.sh` script allows switching between onboard speaker and ALSA/external audio output:

```bash
# Switch to ALSA/external output (mutes onboard speaker)
./scripts/audio-switch.sh alsa

# Switch to onboard speaker
./scripts/audio-switch.sh speaker

# Check current audio settings
./scripts/audio-switch.sh status
```

This is useful when you need to route audio through different outputs depending on your setup.

## Environment Setup

1. **Copy environment template:**
   ```bash
   cp env.example .env.local
   ```

2. **Required environment variables:**
   ```env
   # Database
   DATABASE_URL="postgresql://user:password@localhost:5432/music_quiz"
   
   # MPD Configuration
   MPD_HOST=localhost
   MPD_PORT=6600
   MPD_HTTP_PORT=8000
   
   # Socket Server
   NEXT_PUBLIC_SOCKET_URL=http://localhost:3001
   SOCKET_PORT=3001
   
   # APIs (for metadata)
   SPOTIFY_CLIENT_ID=your_spotify_client_id
   SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
   LASTFM_API_KEY=your_lastfm_api_key
   ```

## Service Architecture

### 1. Next.js Application (Port 3000)
The main web application providing the UI and API endpoints.

### 2. Socket Server (Port 3001)
Real-time WebSocket server for multiplayer functionality.
- Manages game rooms and player sessions
- Synchronizes game state across clients
- Handles real-time quiz events

### 3. MPD Proxy (Port 8001)
HTTP proxy for MPD audio streaming and control.
- Provides web-friendly API for MPD control
- Handles audio streaming to clients
- Manages playback queue

### 4. MPD Server (Port 6600)
Music Player Daemon for audio playback.
- Manages music library
- Handles audio output
- Provides music database

## Deployment Options

### Option 1: PM2 (Recommended)
```bash
# Start all services
npm run prod:pm2

# Enable auto-start on boot
./scripts/install-pm2-startup.sh
```

### Option 2: Manual Start
```bash
# Terminal 1: Next.js
npm run build && npm start

# Terminal 2: Socket server
npm run socket

# Terminal 3: MPD proxy
npm run mpd-proxy
```

### Option 3: Docker
```bash
# Start with docker-compose
docker-compose up -d
```

## Troubleshooting

### Socket Connection Issues
```bash
# Test socket connection
npm run test-socket

# Run with debug logging
npm run mp:monitor
```

### MPD Connection Issues
```bash
# Debug MPD connection
npm run debug-mpd

# Check MPD status
mpc status
```

### Database Issues
```bash
# Reset database (development only)
npm run db:push --force-reset

# View database
npm run db:studio
```

## Documentation

- [Setup Guide](docs/SETUP.md) - Complete setup instructions
- [Multiplayer Guide](docs/MULTIPLAYER.md) - Multiplayer architecture
- [Socket Specifications](docs/SOCKET_SPECS.md) - WebSocket technical details
- [PM2 Guide](docs/PM2-README.md) - Process management with PM2

## Development Workflow

1. **Start development environment:**
   ```bash
   npm run dev:all
   ```

2. **Make changes and test:**
   - Frontend changes: Hot reload automatically
   - Socket server changes: Restart with `npm run socket`
   - Database changes: Run `npm run db:push`

3. **Run tests:**
   ```bash
   npm test
   ```

4. **Build for production:**
   ```bash
   npm run build:production
   ```

## License

This project is private and proprietary.