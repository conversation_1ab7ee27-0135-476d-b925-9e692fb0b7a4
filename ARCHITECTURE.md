# Music Quiz Application Architecture

## System Overview

The Music Quiz application is a real-time multiplayer quiz game with music identification and general knowledge questions.

## Core Components

### 1. Frontend (Next.js)
- **Framework**: Next.js 15.2 with React 19
- **Port**: 3000
- **Key Features**:
  - Server-side rendering
  - API routes for data access
  - Real-time UI updates via Socket.IO client

### 2. Socket Server
- **Implementation**: `lib/socket-server.ts`
- **Entry Point**: `scripts/socket-server.js`
- **Port**: 3001
- **Key Features**:
  - Real-time multiplayer game management
  - WebSocket communication via Socket.IO
  - Game state synchronization
  - Timer management
  - Auto-advancement of questions

### 3. Database
- **Type**: PostgreSQL
- **ORM**: Prisma
- **Models**:
  - Tracks (music library)
  - Users
  - Favorites
  - General knowledge questions

### 4. Audio System
- **MPD (Music Player Daemon)**: Port 6600
- **MPD HTTP Proxy**: Port 8001
- **Audio Manager**: Client-side audio control

## Game Flow

### Multiplayer Quiz
1. **Lobby Phase**
   - Host creates game with settings
   - Players join using 4-character PIN
   - Host configures game mode (UlTimote, Classic, etc.)

2. **Game Phase**
   - Questions sent simultaneously to all players
   - Timer countdown on server
   - Answers submitted without immediate feedback
   - Points calculated based on speed

3. **Results Phase**
   - Correct answer revealed after time expires
   - Scores updated and displayed
   - Auto-advance to next question
   - Final leaderboard at game end

### Game Modes
- **UlTimote**: Customizable rounds with mixed categories
- **Classic**: Traditional music identification
- **General Knowledge**: Trivia questions only
- **Guess the Year**: Identify song release years
- **And more...**

## Process Management

PM2 manages three main processes:
```
music-quiz-nextjs  - Next.js application
music-quiz-socket  - Socket.IO server
music-quiz-mpd-proxy - MPD HTTP proxy
```

## Key Files

### Configuration
- `ecosystem.config.js` - PM2 configuration
- `.env.local` - Environment variables
- `prisma/schema.prisma` - Database schema

### Core Logic
- `lib/socket-server.ts` - Main multiplayer server
- `lib/game-manager.ts` - Game state management
- `lib/multiplayer-quiz-generator.ts` - Question generation
- `hooks/use-multiplayer.ts` - Client-side multiplayer hook

### Components
- `components/multiplayer-quiz.tsx` - Active game UI
- `components/multiplayer-lobby.tsx` - Pre-game lobby
- `components/multiplayer-results.tsx` - Game results

## Development Workflow

1. **Local Development**
   ```bash
   npm run dev:all  # Starts all services
   ```

2. **Production Deployment**
   ```bash
   npm run prod:pm2  # Starts with PM2
   ```

3. **Testing**
   ```bash
   npm test  # Run all tests
   npm run mp:test  # Test multiplayer
   ```

## Architecture Decisions

1. **Single Socket Server**: No clustering to avoid Socket.IO state sync issues
2. **In-Memory Game Store**: Fast access, games cleaned up after completion
3. **Server-Side Timers**: Authoritative timing to prevent cheating
4. **Deferred Feedback**: Answers validated after question ends
5. **MPD for Audio**: Professional-grade audio playback with crossfade

## Security Considerations

- Input validation on all user data
- Rate limiting on game creation
- CORS configured for production domains
- No sensitive data in client state
- Server-authoritative game logic