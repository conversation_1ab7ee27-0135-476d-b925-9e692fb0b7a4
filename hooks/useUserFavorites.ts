import { useState, useCallback, useEffect } from 'react'
import { toast } from 'sonner'
import type { Song, JukeboxUser } from '@/lib/types'
// FavoritesDebugger removed - debug utility no longer needed

interface UserFavoriteData {
  id: string
  userId: string
  songId?: string | null
  trackId?: string
  filePath: string
  title: string
  artist?: string
  album?: string
  duration?: number
  genre?: string
  year?: number | string | null
  albumArtUrl?: string
  addedAt: Date | string
}

export interface UseUserFavoritesReturn {
  // State
  userFavorites: Set<string>
  userFavoritesData: UserFavoriteData[] // Full favorite objects
  favoritesLoading: Set<string>
  
  // Actions
  loadUserFavorites: () => Promise<void>
  handleToggleFavorite: (song: Song) => Promise<void>
  isFavorite: (song: Song) => boolean
  isFavoriteLoading: (songId: string) => boolean
  setFavoriteLoading: (songId: string, loading: boolean) => void
}

export function useUserFavorites(userProfile: JukeboxUser | null): UseUserFavoritesReturn {
  // Favorites state
  const [userFavorites, setUserFavorites] = useState<Set<string>>(new Set())
  const [userFavoritesData, setUserFavoritesData] = useState<UserFavoriteData[]>([]) // Store full data
  const [favoritesLoading, setFavoritesLoading] = useState<Set<string>>(new Set())
  const [isInitialFavoritesLoading, setIsInitialFavoritesLoading] = useState(true)
  
  // Check if song is in favorites
  const isFavorite = useCallback((song: Song) => {
    return song.filePath ? userFavorites.has(song.filePath) : false
  }, [userFavorites])

  // Check if a song is being added/removed from favorites
  const isFavoriteLoading = useCallback((songId: string) => {
    return favoritesLoading.has(songId)
  }, [favoritesLoading])

  // Set favorites loading state
  const setFavoriteLoading = useCallback((songId: string, loading: boolean) => {
    setFavoritesLoading(prev => {
      const newSet = new Set(prev)
      if (loading) {
        newSet.add(songId)
      } else {
        newSet.delete(songId)
      }
      return newSet
    })
  }, [])

  // Load user favorites
  const loadUserFavorites = useCallback(async () => {
    if (!userProfile?.id) return

    try {
      const response = await fetch(`/api/jukebox/favorites?userId=${userProfile.id}`)
      
      if (!response.ok) {
        console.error(`Favorites API error: ${response.status} ${response.statusText}`)
        return
      }
      
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        console.error('Favorites API returned non-JSON response')
        return
      }
      
      const data = await response.json()

      if (data.success) {
        // Store full favorite data
        setUserFavoritesData(data.favorites || [])
        
        // Also maintain the Set for quick lookups
        const favoriteFilePaths = new Set<string>(
          data.favorites
            .map((fav: { filePath: string }) => fav.filePath)
            .filter((path: string) => path && typeof path === 'string')
        )
        setUserFavorites(favoriteFilePaths)
      }
    } catch (error) {
      console.error('Failed to load favorites:', error)
    } finally {
      setIsInitialFavoritesLoading(false)
    }
  }, [userProfile])

  // Handle adding/removing favorites
  const handleToggleFavorite = useCallback(async (song: Song) => {
    if (!userProfile?.id) {
      toast.error("Please set up your profile to save favorites")
      return
    }

    if (!song.filePath) {
      toast.error("Cannot add song to favorites - missing file path")
      return
    }

    // Validate song before proceeding
    if (!song.filePath || !song.title || !song.artist) {
      toast.error("Cannot add song to favorites - invalid song data")
      return
    }

    const songId = song.id?.toString() || song.filePath || `${song.artist}-${song.title}`
    if (isFavoriteLoading(songId)) {
      return // Already processing
    }

    setFavoriteLoading(songId, true)
    const isCurrentlyFavorite = isFavorite(song)

    // Optimistic update
    if (isCurrentlyFavorite) {
      // Remove from both Set and array
      setUserFavorites(prev => {
        const newSet = new Set(prev)
        newSet.delete(song.filePath!)
        return newSet
      })
      setUserFavoritesData(prev => prev.filter(fav => fav.filePath !== song.filePath))
    } else {
      // Add to both Set and array
      setUserFavorites(prev => {
        const newSet = new Set([...prev, song.filePath!])
        return newSet
      })
      // Create a temporary favorite object for optimistic update
      const tempFavorite: UserFavoriteData = {
        id: `temp-${Date.now()}`,
        userId: userProfile.id,
        filePath: song.filePath!,
        title: song.title,
        artist: song.artist,
        album: song.album,
        duration: song.duration,
        genre: (song as any).genre,
        year: (song as any).year,
        albumArtUrl: (song as any).albumArtUrl,
        addedAt: new Date().toISOString()
      }
      setUserFavoritesData(prev => [...prev, tempFavorite])
    }

    try {
      if (isCurrentlyFavorite) {
        // Remove from favorites
        const url = `/api/jukebox/favorites?userId=${encodeURIComponent(userProfile.id)}&filePath=${encodeURIComponent(song.filePath)}`

        const response = await fetch(url, {
          method: 'DELETE'
        })

        const data = await response.json()

        if (data.success) {
          toast.success(`Removed "${song.title}" from favorites`, {
            duration: 2000,
          })
        } else {
          console.error('Remove favorite failed:', data)
          // Rollback optimistic update
          setUserFavorites(prev => {
            const newSet = new Set([...prev, song.filePath!])
            return newSet
          })
          // Reload full data on error
          loadUserFavorites()
          toast.error(data.message || 'Failed to remove from favorites')
        }
      } else {
        // Add to favorites
        const payload = {
          userId: userProfile.id,
          song
        }

        const response = await fetch('/api/jukebox/favorites', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        })

        const data = await response.json()

        if (data.success) {
          // Update the temporary favorite with the real data from server
          if (data.favorite) {
            setUserFavoritesData(prev => [
              ...prev.filter(f => !f.id.startsWith('temp-')),
              data.favorite
            ])
          }
          toast.success(`Added "${song.title}" to favorites`, {
            duration: 2000,
          })
        } else if (data.alreadyExists) {
          toast.warning(`"${song.title}" is already in your favorites`)
        } else {
          console.error('Add favorite failed:', data)
          // Rollback optimistic update
          setUserFavorites(prev => {
            const newSet = new Set(prev)
            newSet.delete(song.filePath!)
            return newSet
          })
          setUserFavoritesData(prev => prev.filter(fav => fav.filePath !== song.filePath))
          toast.error(data.message || 'Failed to add to favorites')
        }
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error)
      // Rollback optimistic update on network error
      if (isCurrentlyFavorite) {
        // Was trying to remove, add it back
        setUserFavorites(prev => {
          const newSet = new Set([...prev, song.filePath!])
          return newSet
        })
      } else {
        // Was trying to add, remove it
        setUserFavorites(prev => {
          const newSet = new Set(prev)
          newSet.delete(song.filePath!)
          return newSet
        })
        setUserFavoritesData(prev => prev.filter(fav => fav.filePath !== song.filePath))
      }
      // Reload to get correct state
      loadUserFavorites()
      toast.error('Failed to update favorites - network error')
    } finally {
      setFavoriteLoading(songId, false)
    }
  }, [userProfile, isFavorite, isFavoriteLoading, setFavoriteLoading])

  // Load user favorites when profile is ready
  useEffect(() => {
    if (userProfile?.id) {
      loadUserFavorites()
    }
  }, [userProfile?.id, loadUserFavorites])

  return {
    // State
    userFavorites,
    userFavoritesData, // Export the full data
    favoritesLoading,
    isInitialFavoritesLoading,
    
    // Actions
    loadUserFavorites,
    handleToggleFavorite,
    isFavorite,
    isFavoriteLoading,
    setFavoriteLoading,
  }
}