import { NextRequest, NextResponse } from 'next/server'
import { jwtVerify } from 'jose'

// Get JWT secret for verification
function getJWTSecret(): Uint8Array {
  const secret = process.env.JWT_SECRET
  if (!secret) {
    console.error('[Middleware] JWT_SECRET not configured')
    return new TextEncoder().encode('fallback-key-for-development-only')
  }
  return new TextEncoder().encode(secret)
}

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/reset-password', // Password reset endpoint
  '/api/auth/validate', // Session validation endpoint
  '/api/auth/logout', // Logout endpoint
  '/api/auth/csrf',
  '/api/health',
  '/api/socket-url',
  '/api/mpd/status', // MPD status needs to be public for UI polling
  '/api/images', // Album art images should be public
  '/api/album-art', // Album art API should be public
  '/api/library/folders', // Folder list for content filters
  '/api/quiz/genres', // Genres for jukebox library browser
  '/api/quiz/tracks', // Tracks for jukebox library browser
  '/api/jukebox/suggestions', // Viewing suggestions should be public
  '/api/general-quiz/categories', // General knowledge categories for game config
  '/api/general-quiz/questions', // General knowledge questions for game creation
  '/api/test', // Test routes for development
  '/api/music/search', // Music search should be public (download still requires auth)
  '/api/debug/check-audio-settings', // Debug endpoint
  '/api/debug/force-restore-audio', // Debug endpoint
  '/api/debug/test-crossfade', // Debug endpoint
  '/api/stream', // Audio stream proxy
]

// Routes that require specific roles
const ADMIN_ROUTES = [
  '/api/admin',
  '/api/debug',
]

const DJ_ROUTES = [
  '/api/jukebox/suggestions/*/approve',
  '/api/mpd/control',
]

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  
  // Skip auth for public routes
  // Special handling for routes that are public for GET but require auth for POST/DELETE
  const method = request.method
  
  // Routes that are public for GET only
  const PUBLIC_GET_ONLY_ROUTES = [
    '/api/mpd/queue',
    '/api/mpd/playlists',
  ]
  
  // Check if this is a GET request to a public GET-only route
  if (method === 'GET' && PUBLIC_GET_ONLY_ROUTES.some(route => pathname.startsWith(route))) {
    return NextResponse.next()
  }
  
  // Check regular public routes
  if (PUBLIC_ROUTES.some(route => pathname.startsWith(route))) {
    return NextResponse.next()
  }
  
  // Skip auth for non-API routes
  if (!pathname.startsWith('/api/')) {
    return NextResponse.next()
  }
  
  // Check for auth token in cookies
  const authToken = request.cookies.get('auth_token')
  
  if (!authToken) {
    return NextResponse.json(
      { success: false, message: 'Authentication required' },
      { status: 401 }
    )
  }
  
  // Verify JWT token
  try {
    const { payload } = await jwtVerify(authToken.value, getJWTSecret())
    
    // The token payload is directly the user data (id, email, username, role)
    // Check if token has required fields - handle both nested and flat structures
    const tokenData = payload.user || payload
    
    if (!tokenData.id || !tokenData.role) {
      console.error('[Middleware] Invalid token payload structure:', Object.keys(payload))
      return NextResponse.json(
        { success: false, message: 'Invalid authentication token' },
        { status: 401 }
      )
    }
    
    // Check role-based access for admin routes
    if (ADMIN_ROUTES.some(route => pathname.startsWith(route))) {
      if (tokenData.role !== 'superuser') {
        return NextResponse.json(
          { success: false, message: 'Admin access required' },
          { status: 403 }
        )
      }
    }
    
    // Check role-based access for DJ routes
    if (DJ_ROUTES.some(route => pathname.includes(route))) {
      if (tokenData.role !== 'dj' && tokenData.role !== 'superuser') {
        return NextResponse.json(
          { success: false, message: 'DJ access required' },
          { status: 403 }
        )
      }
    }
    
    // Add user info to request headers for downstream use
    const headers = new Headers(request.headers)
    headers.set('x-user-id', tokenData.id as string)
    headers.set('x-user-role', tokenData.role as string)
    if (tokenData.email) headers.set('x-user-email', tokenData.email as string)
    if (tokenData.username) headers.set('x-user-username', tokenData.username as string)
    
    return NextResponse.next({
      request: {
        headers,
      },
    })
  } catch (error) {
    console.error('[Middleware] Token verification failed:', error)
    return NextResponse.json(
      { success: false, message: 'Invalid or expired token' },
      { status: 401 }
    )
  }
}

export const config = {
  matcher: [
    // Only match API routes but exclude images
    '/api/((?!images).+)'
  ],
}