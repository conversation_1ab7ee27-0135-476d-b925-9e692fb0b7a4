"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MultiplayerGameMonitor = void 0;
exports.monitorMultiplayerGame = monitorMultiplayerGame;
exports.runGameStressTest = runGameStressTest;
var playwright_utils_1 = require("./playwright-utils");
var MultiplayerGameMonitor = /** @class */ (function () {
    function MultiplayerGameMonitor() {
        this.socketClients = new Map();
        this.gameEvents = [];
        this.playerMetrics = new Map();
        this.automation = new playwright_utils_1.MusicQuizAutomation({ headless: true });
    }
    MultiplayerGameMonitor.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.automation.initialize()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    MultiplayerGameMonitor.prototype.close = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _i, _a, _b, client;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        // Disconnect all socket clients
                        for (_i = 0, _a = this.socketClients; _i < _a.length; _i++) {
                            _b = _a[_i], client = _b[1];
                            client.disconnect();
                        }
                        return [4 /*yield*/, this.automation.close()];
                    case 1:
                        _c.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    // Monitor a complete multiplayer game session
    MultiplayerGameMonitor.prototype.monitorGameSession = function (config) {
        return __awaiter(this, void 0, void 0, function () {
            var screenshots, pages, i, playerId, page, socketClient, roomCode, j, otherPage, monitoringInterval, summary, _i, pages_1, page;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        screenshots = [];
                        pages = [];
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, , 17, 22]);
                        i = 0;
                        _a.label = 2;
                    case 2:
                        if (!(i < config.playerCount)) return [3 /*break*/, 15];
                        playerId = "player-".concat(i + 1);
                        return [4 /*yield*/, this.automation.createPage()];
                    case 3:
                        page = _a.sent();
                        pages.push(page);
                        // Enable WebSocket interception
                        return [4 /*yield*/, this.setupWebSocketInterception(page, playerId)];
                    case 4:
                        // Enable WebSocket interception
                        _a.sent();
                        socketClient = new playwright_utils_1.SocketTestClient();
                        return [4 /*yield*/, socketClient.connect(config.socketUrl, {
                                query: { playerId: playerId }
                            })];
                    case 5:
                        _a.sent();
                        this.socketClients.set(playerId, socketClient);
                        // Initialize player metrics
                        this.playerMetrics.set(playerId, {
                            playerId: playerId,
                            joinTime: Date.now(),
                            score: 0,
                            correctAnswers: 0,
                            responseTime: [],
                            connectionQuality: 'good'
                        });
                        // Navigate to game
                        return [4 /*yield*/, page.goto(config.gameUrl)];
                    case 6:
                        // Navigate to game
                        _a.sent();
                        return [4 /*yield*/, page.fill('[data-testid="player-name"]', "Player ".concat(i + 1))];
                    case 7:
                        _a.sent();
                        if (!(i === 0)) return [3 /*break*/, 14];
                        return [4 /*yield*/, page.click('[data-testid="create-room"]')];
                    case 8:
                        _a.sent();
                        return [4 /*yield*/, page.textContent('[data-testid="room-code-display"]')];
                    case 9:
                        roomCode = _a.sent();
                        j = 1;
                        _a.label = 10;
                    case 10:
                        if (!(j < config.playerCount)) return [3 /*break*/, 14];
                        otherPage = pages[j];
                        return [4 /*yield*/, otherPage.fill('[data-testid="room-code"]', roomCode)];
                    case 11:
                        _a.sent();
                        return [4 /*yield*/, otherPage.click('[data-testid="join-room"]')];
                    case 12:
                        _a.sent();
                        _a.label = 13;
                    case 13:
                        j++;
                        return [3 /*break*/, 10];
                    case 14:
                        i++;
                        return [3 /*break*/, 2];
                    case 15:
                        monitoringInterval = setInterval(function () { return __awaiter(_this, void 0, void 0, function () {
                            var _i, _a, _b, playerId, client, messages, _c, messages_1, msg, screenshotPath;
                            return __generator(this, function (_d) {
                                switch (_d.label) {
                                    case 0:
                                        // Collect events from all socket clients
                                        for (_i = 0, _a = this.socketClients; _i < _a.length; _i++) {
                                            _b = _a[_i], playerId = _b[0], client = _b[1];
                                            messages = client.getMessages();
                                            for (_c = 0, messages_1 = messages; _c < messages_1.length; _c++) {
                                                msg = messages_1[_c];
                                                this.gameEvents.push({
                                                    event: msg.event,
                                                    timestamp: msg.timestamp,
                                                    data: __assign(__assign({}, msg.data), { playerId: playerId })
                                                });
                                            }
                                        }
                                        // Update player metrics
                                        return [4 /*yield*/, this.updatePlayerMetrics(pages)];
                                    case 1:
                                        // Update player metrics
                                        _d.sent();
                                        if (!config.captureScreenshots) return [3 /*break*/, 3];
                                        screenshotPath = "./screenshots/game-".concat(Date.now(), ".png");
                                        return [4 /*yield*/, pages[0].screenshot({ path: screenshotPath })];
                                    case 2:
                                        _d.sent();
                                        screenshots.push(screenshotPath);
                                        _d.label = 3;
                                    case 3: return [2 /*return*/];
                                }
                            });
                        }); }, 1000);
                        // Wait for game duration
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, config.duration); })];
                    case 16:
                        // Wait for game duration
                        _a.sent();
                        clearInterval(monitoringInterval);
                        summary = this.generateGameSummary();
                        return [2 /*return*/, {
                                events: this.gameEvents,
                                metrics: Array.from(this.playerMetrics.values()),
                                screenshots: screenshots,
                                summary: summary
                            }];
                    case 17:
                        _i = 0, pages_1 = pages;
                        _a.label = 18;
                    case 18:
                        if (!(_i < pages_1.length)) return [3 /*break*/, 21];
                        page = pages_1[_i];
                        return [4 /*yield*/, page.close()];
                    case 19:
                        _a.sent();
                        _a.label = 20;
                    case 20:
                        _i++;
                        return [3 /*break*/, 18];
                    case 21: return [7 /*endfinally*/];
                    case 22: return [2 /*return*/];
                }
            });
        });
    };
    // Set up WebSocket interception on a page
    MultiplayerGameMonitor.prototype.setupWebSocketInterception = function (page, playerId) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, page.evaluateOnNewDocument(function (playerId) {
                            window.__wsMessages = [];
                            window.__playerId = playerId;
                            var originalWebSocket = window.WebSocket;
                            window.WebSocket = new Proxy(originalWebSocket, {
                                construct: function (target, args) {
                                    var ws = new (target.bind.apply(target, __spreadArray([void 0], args, false)))();
                                    // Intercept send
                                    var originalSend = ws.send.bind(ws);
                                    ws.send = function (data) {
                                        try {
                                            var parsed = JSON.parse(data);
                                            window.__wsMessages.push({
                                                type: 'sent',
                                                event: parsed.event || 'unknown',
                                                data: parsed,
                                                timestamp: Date.now(),
                                                playerId: window.__playerId
                                            });
                                        }
                                        catch (_a) {
                                            // Handle non-JSON messages
                                        }
                                        return originalSend(data);
                                    };
                                    // Intercept receive
                                    ws.addEventListener('message', function (event) {
                                        try {
                                            var parsed = JSON.parse(event.data);
                                            window.__wsMessages.push({
                                                type: 'received',
                                                event: parsed.event || 'unknown',
                                                data: parsed,
                                                timestamp: Date.now(),
                                                playerId: window.__playerId
                                            });
                                        }
                                        catch (_a) {
                                            // Handle non-JSON messages
                                        }
                                    });
                                    return ws;
                                }
                            });
                        }, playerId)];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    // Update player metrics based on page state
    MultiplayerGameMonitor.prototype.updatePlayerMetrics = function (pages) {
        return __awaiter(this, void 0, void 0, function () {
            var i, page, playerId, metrics, scoreText, wsMessages, recentMessages, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        i = 0;
                        _b.label = 1;
                    case 1:
                        if (!(i < pages.length)) return [3 /*break*/, 7];
                        page = pages[i];
                        playerId = "player-".concat(i + 1);
                        metrics = this.playerMetrics.get(playerId);
                        if (!metrics)
                            return [3 /*break*/, 6];
                        _b.label = 2;
                    case 2:
                        _b.trys.push([2, 5, , 6]);
                        return [4 /*yield*/, page.textContent('[data-testid="player-score"]')];
                    case 3:
                        scoreText = _b.sent();
                        if (scoreText) {
                            metrics.score = parseInt(scoreText) || 0;
                        }
                        return [4 /*yield*/, page.evaluate(function () { return window.__wsMessages || []; })];
                    case 4:
                        wsMessages = _b.sent();
                        recentMessages = wsMessages.filter(function (m) {
                            return Date.now() - m.timestamp < 5000;
                        });
                        if (recentMessages.length === 0) {
                            metrics.connectionQuality = 'poor';
                        }
                        else if (recentMessages.length < 5) {
                            metrics.connectionQuality = 'fair';
                        }
                        else {
                            metrics.connectionQuality = 'good';
                        }
                        return [3 /*break*/, 6];
                    case 5:
                        _a = _b.sent();
                        // Player might have disconnected
                        metrics.connectionQuality = 'poor';
                        return [3 /*break*/, 6];
                    case 6:
                        i++;
                        return [3 /*break*/, 1];
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    // Generate game summary
    MultiplayerGameMonitor.prototype.generateGameSummary = function () {
        var eventCounts = {};
        var playerScores = {};
        // Count events
        for (var _i = 0, _a = this.gameEvents; _i < _a.length; _i++) {
            var event_1 = _a[_i];
            eventCounts[event_1.event] = (eventCounts[event_1.event] || 0) + 1;
        }
        // Get final scores
        for (var _b = 0, _c = this.playerMetrics; _b < _c.length; _b++) {
            var _d = _c[_b], playerId = _d[0], metrics = _d[1];
            playerScores[playerId] = metrics.score;
        }
        // Calculate average response times
        var avgResponseTimes = {};
        for (var _e = 0, _f = this.playerMetrics; _e < _f.length; _e++) {
            var _g = _f[_e], playerId = _g[0], metrics = _g[1];
            if (metrics.responseTime.length > 0) {
                avgResponseTimes[playerId] =
                    metrics.responseTime.reduce(function (a, b) { return a + b; }, 0) / metrics.responseTime.length;
            }
        }
        return {
            totalEvents: this.gameEvents.length,
            eventCounts: eventCounts,
            playerScores: playerScores,
            avgResponseTimes: avgResponseTimes,
            connectionIssues: Array.from(this.playerMetrics.values())
                .filter(function (m) { return m.connectionQuality !== 'good'; })
                .map(function (m) { return ({ playerId: m.playerId, quality: m.connectionQuality }); })
        };
    };
    // Simulate specific game scenarios
    MultiplayerGameMonitor.prototype.simulateScenarios = function (scenarios) {
        return __awaiter(this, void 0, void 0, function () {
            var results, _i, scenarios_1, scenario, pages, success, _a, pages_2, page;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        results = [];
                        _i = 0, scenarios_1 = scenarios;
                        _b.label = 1;
                    case 1:
                        if (!(_i < scenarios_1.length)) return [3 /*break*/, 13];
                        scenario = scenarios_1[_i];
                        return [4 /*yield*/, this.automation.simulateMultiplayerGame({
                                gameUrl: 'http://localhost:3000/multiplayer',
                                playerCount: 4
                            })];
                    case 2:
                        pages = _b.sent();
                        _b.label = 3;
                    case 3:
                        _b.trys.push([3, , 7, 12]);
                        // Setup scenario
                        return [4 /*yield*/, scenario.setup(pages)];
                    case 4:
                        // Setup scenario
                        _b.sent();
                        // Wait for scenario to play out
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 5000); })];
                    case 5:
                        // Wait for scenario to play out
                        _b.sent();
                        return [4 /*yield*/, scenario.verify(pages)];
                    case 6:
                        success = _b.sent();
                        results.push({
                            scenario: scenario.name,
                            success: success,
                            timestamp: Date.now()
                        });
                        return [3 /*break*/, 12];
                    case 7:
                        _a = 0, pages_2 = pages;
                        _b.label = 8;
                    case 8:
                        if (!(_a < pages_2.length)) return [3 /*break*/, 11];
                        page = pages_2[_a];
                        return [4 /*yield*/, page.close()];
                    case 9:
                        _b.sent();
                        _b.label = 10;
                    case 10:
                        _a++;
                        return [3 /*break*/, 8];
                    case 11: return [7 /*endfinally*/];
                    case 12:
                        _i++;
                        return [3 /*break*/, 1];
                    case 13: return [2 /*return*/, results];
                }
            });
        });
    };
    // Performance stress test
    MultiplayerGameMonitor.prototype.stressTest = function (config) {
        return __awaiter(this, void 0, void 0, function () {
            var performanceData, pages, currentPlayers, errors, totalRequests, playerJoinInterval, joinInterval, monitorInterval, _i, pages_3, page, latencies, averageLatency;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        performanceData = [];
                        pages = [];
                        currentPlayers = 0;
                        errors = 0;
                        totalRequests = 0;
                        playerJoinInterval = config.rampUpTime / config.maxPlayers;
                        joinInterval = setInterval(function () { return __awaiter(_this, void 0, void 0, function () {
                            var page, error_1;
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0:
                                        if (currentPlayers >= config.maxPlayers) {
                                            clearInterval(joinInterval);
                                            return [2 /*return*/];
                                        }
                                        _a.label = 1;
                                    case 1:
                                        _a.trys.push([1, 4, , 5]);
                                        return [4 /*yield*/, this.automation.createPage()];
                                    case 2:
                                        page = _a.sent();
                                        return [4 /*yield*/, page.goto(config.gameUrl)];
                                    case 3:
                                        _a.sent();
                                        pages.push(page);
                                        currentPlayers++;
                                        return [3 /*break*/, 5];
                                    case 4:
                                        error_1 = _a.sent();
                                        errors++;
                                        return [3 /*break*/, 5];
                                    case 5: return [2 /*return*/];
                                }
                            });
                        }); }, playerJoinInterval);
                        monitorInterval = setInterval(function () { return __awaiter(_this, void 0, void 0, function () {
                            var _i, pages_4, page, metrics, _a;
                            return __generator(this, function (_b) {
                                switch (_b.label) {
                                    case 0:
                                        _i = 0, pages_4 = pages;
                                        _b.label = 1;
                                    case 1:
                                        if (!(_i < pages_4.length)) return [3 /*break*/, 6];
                                        page = pages_4[_i];
                                        _b.label = 2;
                                    case 2:
                                        _b.trys.push([2, 4, , 5]);
                                        return [4 /*yield*/, this.automation.measureGamePerformance(page)];
                                    case 3:
                                        metrics = _b.sent();
                                        performanceData.push(__assign({ timestamp: Date.now(), players: currentPlayers }, metrics));
                                        totalRequests++;
                                        return [3 /*break*/, 5];
                                    case 4:
                                        _a = _b.sent();
                                        errors++;
                                        totalRequests++;
                                        return [3 /*break*/, 5];
                                    case 5:
                                        _i++;
                                        return [3 /*break*/, 1];
                                    case 6: return [2 /*return*/];
                                }
                            });
                        }); }, 1000);
                        // Wait for test duration
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, config.testDuration); })];
                    case 1:
                        // Wait for test duration
                        _a.sent();
                        // Clean up
                        clearInterval(joinInterval);
                        clearInterval(monitorInterval);
                        _i = 0, pages_3 = pages;
                        _a.label = 2;
                    case 2:
                        if (!(_i < pages_3.length)) return [3 /*break*/, 5];
                        page = pages_3[_i];
                        return [4 /*yield*/, page.close()];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        _i++;
                        return [3 /*break*/, 2];
                    case 5:
                        latencies = performanceData.map(function (d) { return d.fps ? 1000 / d.fps : 0; });
                        averageLatency = latencies.reduce(function (a, b) { return a + b; }, 0) / latencies.length;
                        return [2 /*return*/, {
                                maxConcurrentPlayers: currentPlayers,
                                averageLatency: averageLatency,
                                errorRate: errors / totalRequests,
                                performanceData: performanceData
                            }];
                }
            });
        });
    };
    return MultiplayerGameMonitor;
}());
exports.MultiplayerGameMonitor = MultiplayerGameMonitor;
// Example usage functions
function monitorMultiplayerGame() {
    return __awaiter(this, void 0, void 0, function () {
        var monitor, results;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    monitor = new MultiplayerGameMonitor();
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, , 4, 6]);
                    return [4 /*yield*/, monitor.initialize()];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, monitor.monitorGameSession({
                            gameUrl: 'http://localhost:3000/multiplayer',
                            socketUrl: 'http://localhost:3001',
                            playerCount: 4,
                            duration: 60000, // 1 minute
                            captureScreenshots: true
                        })];
                case 3:
                    results = _a.sent();
                    console.log('Game Summary:', results.summary);
                    console.log('Total Events:', results.events.length);
                    console.log('Player Metrics:', results.metrics);
                    return [2 /*return*/, results];
                case 4: return [4 /*yield*/, monitor.close()];
                case 5:
                    _a.sent();
                    return [7 /*endfinally*/];
                case 6: return [2 /*return*/];
            }
        });
    });
}
function runGameStressTest() {
    return __awaiter(this, void 0, void 0, function () {
        var monitor, results;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    monitor = new MultiplayerGameMonitor();
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, , 4, 6]);
                    return [4 /*yield*/, monitor.initialize()];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, monitor.stressTest({
                            gameUrl: 'http://localhost:3000/multiplayer',
                            socketUrl: 'http://localhost:3001',
                            maxPlayers: 20,
                            rampUpTime: 30000, // 30 seconds
                            testDuration: 120000 // 2 minutes
                        })];
                case 3:
                    results = _a.sent();
                    console.log('Stress Test Results:');
                    console.log('Max Concurrent Players:', results.maxConcurrentPlayers);
                    console.log('Average Latency:', results.averageLatency, 'ms');
                    console.log('Error Rate:', (results.errorRate * 100).toFixed(2), '%');
                    return [2 /*return*/, results];
                case 4: return [4 /*yield*/, monitor.close()];
                case 5:
                    _a.sent();
                    return [7 /*endfinally*/];
                case 6: return [2 /*return*/];
            }
        });
    });
}
