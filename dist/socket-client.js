"use strict";
/**
 * Client-side Socket.IO Manager for Multiplayer Music Quiz
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketClient = void 0;
exports.getSocketClient = getSocketClient;
var socket_io_client_1 = require("socket.io-client");
var network_1 = require("./utils/network");
var network_fallback_1 = require("./utils/network-fallback");
var SocketClient = /** @class */ (function () {
    function SocketClient() {
        this.socket = null;
        this.connectionAttempts = 0;
        this.maxReconnectAttempts = 3;
        this.isConnecting = false;
        // Initialize network monitoring
        (0, network_fallback_1.initNetworkMonitoring)();
        this.initializeConnection();
    }
    SocketClient.prototype.initializeConnection = function () {
        return __awaiter(this, void 0, void 0, function () {
            var socketUrl, isMobile, transports, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.isConnecting)
                            return [2 /*return*/];
                        this.isConnecting = true;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, this.getSocketUrl()];
                    case 2:
                        socketUrl = _a.sent();
                        console.log('🔌 Connecting to socket server:', socketUrl);
                        isMobile = (0, network_1.isMobileNetwork)();
                        transports = isMobile ? ['polling'] : ['polling', 'websocket'];
                        console.log('🔌 Network detection:', {
                            isMobileNetwork: isMobile,
                            transports: transports,
                            hostname: window.location.hostname
                        });
                        this.socket = (0, socket_io_client_1.io)(socketUrl, {
                            // Use polling-only on mobile networks, allow upgrade on regular networks
                            transports: transports,
                            upgrade: !isMobile, // Don't upgrade to websocket on mobile networks
                            timeout: isMobile ? 30000 : 15000, // Longer timeout for mobile networks
                            autoConnect: true,
                            reconnection: true,
                            reconnectionAttempts: this.maxReconnectAttempts,
                            reconnectionDelay: isMobile ? 5000 : 2000, // Longer delay for mobile
                            // Add explicit Engine.IO version for better compatibility
                            forceNew: true,
                            // Additional debugging options
                            auth: {
                                timestamp: Date.now()
                            }
                        });
                        this.setupEventListeners();
                        this.isConnecting = false;
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        console.error('Failed to initialize socket connection:', error_1);
                        this.isConnecting = false;
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    SocketClient.prototype.getSocketUrl = function () {
        return __awaiter(this, void 0, void 0, function () {
            var networkStatus, response, data, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        networkStatus = (0, network_fallback_1.getNetworkStatus)();
                        if (networkStatus.fallbackMode) {
                            console.log('🔌 Network in fallback mode, using adaptive URL');
                            return [2 /*return*/, (0, network_fallback_1.getAdaptiveSocketUrl)()];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 4, , 5]);
                        return [4 /*yield*/, fetch('/api/socket-url')];
                    case 2:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 3:
                        data = _a.sent();
                        if (data.success && data.url) {
                            console.log('🔌 Socket URL from API:', data.url);
                            return [2 /*return*/, data.url];
                        }
                        // Fallback to adaptive URL
                        return [2 /*return*/, (0, network_fallback_1.getAdaptiveSocketUrl)()];
                    case 4:
                        error_2 = _a.sent();
                        console.warn('Failed to get socket URL from API, using adaptive fallback');
                        return [2 /*return*/, (0, network_fallback_1.getAdaptiveSocketUrl)()];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    SocketClient.prototype.setupEventListeners = function () {
        var _this = this;
        if (!this.socket)
            return;
        this.socket.on('connect', function () {
            var _a;
            console.log('🔌 Connected to server:', (_a = _this.socket) === null || _a === void 0 ? void 0 : _a.id);
            _this.connectionAttempts = 0;
        });
        this.socket.on('disconnect', function (reason) {
            console.log('🔌 Disconnected from server:', reason);
        });
        this.socket.on('connect_error', function (error) {
            console.error('🔌 Connection error:', error);
            _this.connectionAttempts++;
            if (_this.connectionAttempts >= _this.maxReconnectAttempts) {
                console.error('Max reconnection attempts reached');
            }
        });
    };
    // Game actions
    SocketClient.prototype.createGame = function (hostId, hostName, gameMode) {
        var _a;
        if (!((_a = this.socket) === null || _a === void 0 ? void 0 : _a.connected)) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('create-game', { hostId: hostId, hostName: hostName, gameMode: gameMode });
    };
    SocketClient.prototype.joinGame = function (gameId, playerId, playerName, playerAvatar) {
        var _a;
        if (!((_a = this.socket) === null || _a === void 0 ? void 0 : _a.connected)) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('join-game', { gameId: gameId, playerId: playerId, playerName: playerName, playerAvatar: playerAvatar });
    };
    SocketClient.prototype.startGame = function (gameId) {
        var _a;
        if (!((_a = this.socket) === null || _a === void 0 ? void 0 : _a.connected)) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('start-game', { gameId: gameId });
    };
    SocketClient.prototype.submitAnswer = function (gameId, playerId, answerIndex, timeTaken) {
        var _a;
        if (!((_a = this.socket) === null || _a === void 0 ? void 0 : _a.connected)) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('submit-answer', { gameId: gameId, playerId: playerId, answerIndex: answerIndex, timeTaken: timeTaken });
    };
    SocketClient.prototype.nextQuestion = function (gameId) {
        var _a;
        if (!((_a = this.socket) === null || _a === void 0 ? void 0 : _a.connected)) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('next-question', { gameId: gameId });
    };
    SocketClient.prototype.leaveGame = function (gameId, playerId) {
        var _a;
        if (!((_a = this.socket) === null || _a === void 0 ? void 0 : _a.connected)) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('leave-game', { gameId: gameId, playerId: playerId });
    };
    // Event listeners
    SocketClient.prototype.on = function (event, callback) {
        if (!this.socket)
            return;
        this.socket.on(event, callback);
    };
    SocketClient.prototype.off = function (event, callback) {
        if (!this.socket)
            return;
        this.socket.off(event, callback);
    };
    // Connection status
    SocketClient.prototype.isConnected = function () {
        var _a;
        return ((_a = this.socket) === null || _a === void 0 ? void 0 : _a.connected) || false;
    };
    SocketClient.prototype.getConnectionId = function () {
        var _a;
        return (_a = this.socket) === null || _a === void 0 ? void 0 : _a.id;
    };
    // Cleanup
    SocketClient.prototype.disconnect = function () {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
    };
    return SocketClient;
}());
exports.SocketClient = SocketClient;
// Singleton instance
var socketClientInstance = null;
function getSocketClient() {
    if (!socketClientInstance) {
        socketClientInstance = new SocketClient();
    }
    return socketClientInstance;
}
