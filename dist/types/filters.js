/**
 * Content Filter Types for Music Quiz
 *
 * Allows filtering content by:
 * - Genres
 * - Playlists/Categories
 * - Metadata (year ranges, chart status, etc.)
 */
// Default filter configuration
export const DEFAULT_FILTERS = {
    genres: {
        mode: 'include',
        values: [] // Empty means all genres
    },
    playlists: {
        mode: 'include',
        values: [] // Empty means all playlists
    },
    yearRange: {
        enabled: false
    },
    charts: {
        includeChartMusic: true,
        includeNonChartMusic: true
    },
    quality: {},
    sources: {
        includeMyItunes: true,
        includeSharedLibrary: true,
        includePlaylists: []
    },
    metadata: {},
    folders: {
        mode: 'include',
        values: [] // Empty means all folders
    }
};
// Common filter presets
export const FILTER_PRESETS = [
    {
        id: 'all-content',
        name: 'All Content',
        description: 'No filters applied',
        filters: DEFAULT_FILTERS,
        isDefault: true
    },
    {
        id: 'charts-only',
        name: 'Chart Hits Only',
        description: 'Only songs that charted',
        filters: {
            ...DEFAULT_FILTERS,
            charts: {
                includeChartMusic: true,
                includeNonChartMusic: false
            }
        }
    },
    {
        id: 'recent-music',
        name: 'Recent Music',
        description: 'Songs from the last 10 years',
        filters: {
            ...DEFAULT_FILTERS,
            yearRange: {
                enabled: true,
                min: new Date().getFullYear() - 10
            }
        }
    },
    {
        id: 'classic-rock',
        name: 'Classic Rock',
        description: 'Rock music from 1960-1990',
        filters: {
            ...DEFAULT_FILTERS,
            genres: {
                mode: 'include',
                values: ['Rock', 'Classic Rock', 'Hard Rock', 'Progressive Rock']
            },
            yearRange: {
                enabled: true,
                min: 1960,
                max: 1990
            }
        }
    },
    {
        id: 'high-quality',
        name: 'High Quality Only',
        description: 'Well-tagged songs with album art',
        filters: {
            ...DEFAULT_FILTERS,
            quality: {
                requireAlbumArt: true
            },
            metadata: {
                requireYear: true,
                requireGenre: true,
                requireAlbum: true
            }
        }
    },
    {
        id: 'all-time-favorites',
        name: 'All Time Favorites',
        description: 'Only the most iconic and beloved songs',
        filters: {
            ...DEFAULT_FILTERS,
            playlists: {
                mode: 'include',
                values: ['all-time-favorites']
            }
        }
    },
    {
        id: 'myitunes-only',
        name: 'MyItunes Only',
        description: 'Only songs from your personal iTunes library',
        filters: {
            ...DEFAULT_FILTERS,
            sources: {
                includeMyItunes: true,
                includeSharedLibrary: false,
                includePlaylists: []
            }
        }
    },
    {
        id: 'no-myitunes',
        name: 'No MyItunes',
        description: 'Exclude songs from personal iTunes library',
        filters: {
            ...DEFAULT_FILTERS,
            sources: {
                includeMyItunes: false,
                includeSharedLibrary: true,
                includePlaylists: []
            }
        }
    }
];
