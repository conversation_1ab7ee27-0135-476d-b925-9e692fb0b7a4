"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildArtistOptions = buildArtistOptions;
var prisma_1 = __importDefault(require("@/lib/database/prisma"));
function buildArtistOptions(opts) {
    return __awaiter(this, void 0, void 0, function () {
        var total, excludeSet, options, _i, _a, a, needed, randoms, _b, randoms_1, r, RandomUtils;
        var _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    total = (_c = opts.totalOptions) !== null && _c !== void 0 ? _c : 4;
                    excludeSet = new Set(__spreadArray([opts.correctArtist], ((_d = opts.exclude) !== null && _d !== void 0 ? _d : []), true));
                    options = new Set();
                    options.add(opts.correctArtist);
                    // 1. Prefer similar artists
                    if (opts.similarArtists && opts.similarArtists.length > 0) {
                        for (_i = 0, _a = opts.similarArtists; _i < _a.length; _i++) {
                            a = _a[_i];
                            if (options.size >= total)
                                break;
                            if (!excludeSet.has(a)) {
                                options.add(a);
                                excludeSet.add(a);
                            }
                        }
                    }
                    if (!(options.size < total)) return [3 /*break*/, 2];
                    needed = total - options.size;
                    return [4 /*yield*/, prisma_1.default.quizTrack.findMany({
                            where: {
                                artist: {
                                    notIn: Array.from(excludeSet)
                                }
                            },
                            select: { artist: true },
                            distinct: ['artist'],
                            take: needed * 3 // fetch extra then filter unique
                        })];
                case 1:
                    randoms = _e.sent();
                    for (_b = 0, randoms_1 = randoms; _b < randoms_1.length; _b++) {
                        r = randoms_1[_b];
                        if (options.size >= total)
                            break;
                        if (r.artist) {
                            options.add(r.artist);
                            excludeSet.add(r.artist);
                        }
                    }
                    _e.label = 2;
                case 2: return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/lib/utils')); })];
                case 3:
                    RandomUtils = (_e.sent()).RandomUtils;
                    return [2 /*return*/, RandomUtils.shuffle(Array.from(options))];
            }
        });
    });
}
