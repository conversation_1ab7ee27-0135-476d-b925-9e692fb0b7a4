"use strict";
// --- Mock Web Audio API for Audio Manipulation ---
// This mock simulates the Web Audio API for setting up audio graphs and effects.
// It does not produce actual audio output.
// Comments highlight where real audio processing or MPD integration would occur.
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockAudioContext = void 0;
exports.loadTrackAndApplyEffects = loadTrackAndApplyEffects;
exports.updateEffectParameter = updateEffectParameter;
/**
 * Represents a mockable AudioParam.
 * In a real Web Audio API, these control specific aspects of an AudioNode.
 */
var MockAudioParam = /** @class */ (function () {
    function MockAudioParam(defaultValue, minValue, maxValue) {
        this._defaultValue = defaultValue;
        this.value = defaultValue;
        this.minValue = minValue;
        this.maxValue = maxValue;
        console.log("MockAudioParam created with default value: ".concat(defaultValue));
    }
    MockAudioParam.prototype.setValueAtTime = function (value, startTime) {
        // HINT FOR AI/Dev: In a real implementation, this schedules a value change.
        // For MPD, this might translate to an effect parameter change command if supported,
        // or trigger a client-side effect on a stream if MPD is just streaming raw audio.
        console.log("MockAudioParam: setValueAtTime(".concat(value, ", ").concat(startTime, ") called. Current time: ").concat(MockAudioContext.currentTime));
        if (MockAudioContext.currentTime >= startTime) {
            this.value = value;
        }
        // Else, it's scheduled for the future (not simulated in this basic mock).
    };
    MockAudioParam.prototype.linearRampToValueAtTime = function (value, endTime) {
        console.log("MockAudioParam: linearRampToValueAtTime(".concat(value, ", ").concat(endTime, ") called."));
        // HINT FOR AI/Dev: Schedules a gradual linear change.
        // Complex for direct MPD mapping unless MPD has equivalent ramp commands for specific effects.
        this.value = value; // Simplified: jump to value for mock
    };
    MockAudioParam.prototype.exponentialRampToValueAtTime = function (value, endTime) {
        console.log("MockAudioParam: exponentialRampToValueAtTime(".concat(value, ", ").concat(endTime, ") called."));
        // HINT FOR AI/Dev: Schedules a gradual exponential change.
        this.value = value; // Simplified
    };
    MockAudioParam.prototype.setTargetAtTime = function (target, startTime, timeConstant) {
        console.log("MockAudioParam: setTargetAtTime(".concat(target, ", ").concat(startTime, ", ").concat(timeConstant, ") called."));
        // HINT FOR AI/Dev: Schedules an exponential approach to the target value.
        this.value = target; // Simplified
    };
    Object.defineProperty(MockAudioParam.prototype, "defaultValue", {
        get: function () {
            return this._defaultValue;
        },
        enumerable: false,
        configurable: true
    });
    return MockAudioParam;
}());
/**
 * Base class for mock AudioNodes.
 */
var MockAudioNode = /** @class */ (function () {
    function MockAudioNode(context) {
        this.connectedTo = [];
        this.numberOfInputs = 1; // Default, can be overridden
        this.numberOfOutputs = 1; // Default, can be overridden
        this.context = context;
        console.log("MockAudioNode (".concat(this.constructor.name, ") created."));
    }
    MockAudioNode.prototype.connect = function (destinationNode, outputIndex, inputIndex) {
        if (destinationNode instanceof MockAudioNode) {
            console.log("MockAudioNode (".concat(this.constructor.name, "): connect to ").concat(destinationNode.constructor.name));
            this.connectedTo.push(destinationNode);
            // HINT FOR AI/Dev: In a real system, this establishes an audio processing path.
            // For MPD, if effects are server-side, this graph setup would be conceptual
            // unless you're building a sophisticated MPD client with its own DSP chain.
            return destinationNode;
        }
        else if (destinationNode instanceof MockAudioParam) {
            console.log("MockAudioNode (".concat(this.constructor.name, "): connect to AudioParam of value ").concat(destinationNode.value));
            // HINT FOR AI/Dev: This connects an AudioNode's output to modulate an AudioParam.
            // Example: LFO (OscillatorNode) controlling the gain of a GainNode.
        }
    };
    MockAudioNode.prototype.disconnect = function (destination, output, input) {
        console.log("MockAudioNode (".concat(this.constructor.name, "): disconnect called."));
        if (destination instanceof MockAudioNode) {
            this.connectedTo = this.connectedTo.filter(function (node) { return node !== destination; });
        }
        else {
            this.connectedTo = []; // Simplified: disconnect all
        }
        // HINT FOR AI/Dev: Removes connections in the audio graph.
    };
    return MockAudioNode;
}());
/**
 * Mock for AudioBufferSourceNode - plays audio data.
 */
var MockAudioBufferSourceNode = /** @class */ (function (_super) {
    __extends(MockAudioBufferSourceNode, _super);
    function MockAudioBufferSourceNode(context) {
        var _this = _super.call(this, context) || this;
        _this.buffer = null;
        _this.loop = false;
        _this.loopStart = 0;
        _this.loopEnd = 0;
        _this.onended = null;
        _this._isPlaying = false;
        _this.numberOfInputs = 0; // Source node has no inputs
        return _this;
    }
    MockAudioBufferSourceNode.prototype.start = function (when, offset, duration) {
        var _this = this;
        console.log("MockAudioBufferSourceNode: start(when=".concat(when, ", offset=").concat(offset, ", duration=").concat(duration, ") called."));
        this._isPlaying = true;
        // HINT FOR AI/Dev: This would begin playback of the audio buffer.
        // For MPD: This is analogous to 'mpc play'. The 'when', 'offset', 'duration'
        // might map to seek commands or specific playback instructions if MPD supports them.
        // If there's a duration, simulate onended.
        if (duration) {
            setTimeout(function () {
                _this.stop();
                if (_this.onended)
                    _this.onended();
            }, duration * 1000);
        }
    };
    MockAudioBufferSourceNode.prototype.stop = function (when) {
        console.log("MockAudioBufferSourceNode: stop(when=".concat(when, ") called."));
        this._isPlaying = false;
        // HINT FOR AI/Dev: This stops playback.
        // For MPD: Analogous to 'mpc stop' or 'mpc pause'.
    };
    return MockAudioBufferSourceNode;
}(MockAudioNode));
/**
 * Mock for GainNode - controls volume.
 */
var MockGainNode = /** @class */ (function (_super) {
    __extends(MockGainNode, _super);
    function MockGainNode(context) {
        var _this = _super.call(this, context) || this;
        _this.gain = new MockAudioParam(1, 0); // Default gain is 1 (no change)
        return _this;
    }
    return MockGainNode;
}(MockAudioNode));
/**
 * Mock for BiquadFilterNode - implements common filter types.
 */
var MockBiquadFilterNode = /** @class */ (function (_super) {
    __extends(MockBiquadFilterNode, _super);
    function MockBiquadFilterNode(context) {
        var _this = _super.call(this, context) || this;
        _this.type = "lowpass";
        _this.frequency = new MockAudioParam(350, 0, context.sampleRate / 2);
        _this.Q = new MockAudioParam(1, 0.0001, 1000);
        _this.gain = new MockAudioParam(0, -40, 40); // Gain for certain filter types
        return _this;
    }
    MockBiquadFilterNode.prototype.getFrequencyResponse = function (frequencyHz, magResponse, phaseResponse) {
        console.log("MockBiquadFilterNode: getFrequencyResponse called.");
        // HINT FOR AI/Dev: This would calculate the filter's response.
        // For mock, fill with dummy data.
        for (var i = 0; i < magResponse.length; i++)
            magResponse[i] = 1;
        for (var i = 0; i < phaseResponse.length; i++)
            phaseResponse[i] = 0;
    };
    return MockBiquadFilterNode;
}(MockAudioNode));
/**
 * Mock for PannerNode - spatializes audio.
 * This is a simplified panner, focusing on stereo panning.
 * A full PannerNode supports 3D audio.
 */
var MockPannerNode = /** @class */ (function (_super) {
    __extends(MockPannerNode, _super);
    function MockPannerNode(context) {
        var _this = _super.call(this, context) || this;
        _this.panningModel = "equalpower";
        _this.distanceModel = "inverse";
        _this.refDistance = 1;
        _this.maxDistance = 10000;
        _this.rolloffFactor = 1;
        _this.coneInnerAngle = 360;
        _this.coneOuterAngle = 360;
        _this.coneOuterGain = 0;
        _this.pan = new MockAudioParam(0, -1, 1);
        return _this;
        // HINT FOR AI/Dev: For 3D audio, you'd use setPosition, setOrientation.
        // MPD typically handles stereo output. Advanced 3D spatialization would likely
        // be a client-side effect on a multi-channel stream or require a specialized MPD setup.
    }
    MockPannerNode.prototype.setPosition = function (x, y, z) {
        console.log("MockPannerNode: setPosition(".concat(x, ", ").concat(y, ", ").concat(z, ") called."));
    };
    MockPannerNode.prototype.setOrientation = function (x, y, z) {
        console.log("MockPannerNode: setOrientation(".concat(x, ", ").concat(y, ", ").concat(z, ") called."));
    };
    return MockPannerNode;
}(MockAudioNode));
/**
 * Mock for DelayNode - introduces a delay.
 */
var MockDelayNode = /** @class */ (function (_super) {
    __extends(MockDelayNode, _super);
    function MockDelayNode(context, maxDelayTime) {
        if (maxDelayTime === void 0) { maxDelayTime = 1.0; }
        var _this = _super.call(this, context) || this;
        _this.delayTime = new MockAudioParam(0, 0, maxDelayTime);
        return _this;
    }
    return MockDelayNode;
}(MockAudioNode));
/**
 * Mock for ConvolverNode - applies reverb or other convolution effects.
 */
var MockConvolverNode = /** @class */ (function (_super) {
    __extends(MockConvolverNode, _super);
    function MockConvolverNode(context) {
        var _this = _super.call(this, context) || this;
        _this.buffer = null; // Impulse response
        _this.normalize = true;
        return _this;
    }
    return MockConvolverNode;
}(MockAudioNode));
/**
 * Mock for AnalyserNode - provides frequency and time-domain analysis.
 */
var MockAnalyserNode = /** @class */ (function (_super) {
    __extends(MockAnalyserNode, _super);
    function MockAnalyserNode(context) {
        var _this = _super.call(this, context) || this;
        _this.fftSize = 2048;
        _this.minDecibels = -100;
        _this.maxDecibels = -30;
        _this.smoothingTimeConstant = 0.8;
        _this.frequencyBinCount = _this.fftSize / 2;
        return _this;
    }
    MockAnalyserNode.prototype.getByteFrequencyData = function (array) {
        // HINT FOR AI/Dev: Fills array with frequency data.
        for (var i = 0; i < array.length; i++)
            array[i] = Math.random() * 255; // Dummy data
    };
    MockAnalyserNode.prototype.getFloatFrequencyData = function (array) {
        // HINT FOR AI/Dev: Fills array with frequency data in dB.
        for (var i = 0; i < array.length; i++)
            array[i] = this.minDecibels + Math.random() * (this.maxDecibels - this.minDecibels);
    };
    MockAnalyserNode.prototype.getByteTimeDomainData = function (array) {
        // HINT FOR AI/Dev: Fills array with waveform data.
        for (var i = 0; i < array.length; i++)
            array[i] = 128 + (Math.random() - 0.5) * 255; // Dummy data
    };
    return MockAnalyserNode;
}(MockAudioNode));
/**
 * Mock for AudioContext - the main entry point for Web Audio API.
 */
var MockAudioContext = /** @class */ (function () {
    function MockAudioContext() {
        this.sampleRate = 44100; // Common sample rate
        this._state = "suspended";
        this.destination = new MockAudioNode(this) // A generic node for destination
        ;
        this.destination.constructor.name = "AudioDestinationNode"; // Make it identifiable
        console.log("MockAudioContext created.");
        // Simulate time passing
        setInterval(function () {
            MockAudioContext.currentTime += 0.1;
        }, 100);
    }
    Object.defineProperty(MockAudioContext.prototype, "state", {
        get: function () {
            return this._state;
        },
        enumerable: false,
        configurable: true
    });
    MockAudioContext.prototype.resume = function () {
        console.log("MockAudioContext: resume() called.");
        this._state = "running";
        // HINT FOR AI/Dev: In a real browser, this is often needed due to autoplay policies.
        // For MPD, this might relate to ensuring the MPD service is running or unpaused.
        return Promise.resolve();
    };
    MockAudioContext.prototype.suspend = function () {
        console.log("MockAudioContext: suspend() called.");
        this._state = "suspended";
        return Promise.resolve();
    };
    MockAudioContext.prototype.close = function () {
        console.log("MockAudioContext: close() called.");
        this._state = "closed";
        return Promise.resolve();
    };
    MockAudioContext.prototype.createBufferSource = function () {
        return new MockAudioBufferSourceNode(this);
    };
    MockAudioContext.prototype.createGain = function () {
        return new MockGainNode(this);
    };
    MockAudioContext.prototype.createBiquadFilter = function () {
        return new MockBiquadFilterNode(this);
    };
    MockAudioContext.prototype.createPanner = function () {
        // This mock uses a simplified stereo panner.
        // A full PannerNode is more complex.
        return new MockPannerNode(this);
    };
    MockAudioContext.prototype.createDelay = function (maxDelayTime) {
        return new MockDelayNode(this, maxDelayTime);
    };
    MockAudioContext.prototype.createConvolver = function () {
        return new MockConvolverNode(this);
    };
    MockAudioContext.prototype.createAnalyser = function () {
        return new MockAnalyserNode(this);
    };
    MockAudioContext.prototype.decodeAudioData = function (audioData, successCallback, errorCallback) {
        var _this = this;
        console.log("MockAudioContext: decodeAudioData called.");
        // HINT FOR AI/Dev: This would decode raw audio data (e.g., from a file) into an AudioBuffer.
        // For MPD, MPD handles decoding. This function might be used if the client
        // fetches an audio file directly and wants to process it before potentially
        // streaming it to a custom MPD input or playing locally.
        return new Promise(function (resolve, reject) {
            // Simulate decoding
            setTimeout(function () {
                var mockBuffer = {
                    sampleRate: _this.sampleRate,
                    length: _this.sampleRate * 3, // Simulate 3 seconds of audio
                    duration: 3,
                    numberOfChannels: 2,
                    getChannelData: function (channel) { return new Float32Array(_this.sampleRate * 3); },
                    copyFromChannel: function () { },
                    copyToChannel: function () { },
                };
                if (successCallback)
                    successCallback(mockBuffer);
                resolve(mockBuffer);
            }, 50); // Simulate async decoding
        });
    };
    MockAudioContext.currentTime = 0; // Static to simulate global time
    return MockAudioContext;
}());
exports.MockAudioContext = MockAudioContext;
var audioContext = null;
var sourceNode = null;
var gainNode = null;
var filterNode = null;
var pannerNode = null;
var delayNode = null;
var convolverNode = null;
// ... other effect nodes
/**
 * Simulates loading a track and applying effects.
 * In a real MPD scenario, MPD plays the track, and effects might be
 * applied by MPD itself (if supported) or by a client processing MPD's output stream.
 */
function loadTrackAndApplyEffects(track, effects) {
    return __awaiter(this, void 0, void 0, function () {
        var mockDecodedBuffer, currentNode;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!audioContext) {
                        audioContext = new MockAudioContext();
                    }
                    return [4 /*yield*/, audioContext.resume()
                        // Stop any existing playback
                    ]; // Important for browser autoplay policies
                case 1:
                    _a.sent(); // Important for browser autoplay policies
                    // Stop any existing playback
                    if (sourceNode) {
                        try {
                            sourceNode.stop();
                        }
                        catch (e) {
                            /* ignore if already stopped */
                        }
                        sourceNode.disconnect();
                    }
                    // 1. Create Source Node (simulates loading audio for the track)
                    sourceNode = audioContext.createBufferSource();
                    return [4 /*yield*/, audioContext.decodeAudioData(new ArrayBuffer(0), function () { })]; // Pass dummy data
                case 2:
                    mockDecodedBuffer = _a.sent() // Pass dummy data
                    ;
                    sourceNode.buffer = mockDecodedBuffer;
                    sourceNode.loop = true; // Example: loop the track
                    console.log("Mock: Loaded track \"".concat(track.title, "\" into buffer source."));
                    currentNode = sourceNode;
                    // 2. Apply Gain (Volume)
                    if (effects.gain !== undefined) {
                        if (!gainNode)
                            gainNode = audioContext.createGain();
                        gainNode.gain.setValueAtTime(effects.gain, audioContext.currentTime);
                        currentNode.connect(gainNode);
                        currentNode = gainNode;
                        console.log("Mock: Applied gain: ".concat(effects.gain));
                    }
                    else if (gainNode) {
                        // If no gain in config, but node exists, reset it or disconnect
                        gainNode.gain.setValueAtTime(gainNode.gain.defaultValue, audioContext.currentTime);
                    }
                    // 3. Apply Filter
                    if (effects.filter) {
                        if (!filterNode)
                            filterNode = audioContext.createBiquadFilter();
                        filterNode.type = effects.filter.type;
                        filterNode.frequency.setValueAtTime(effects.filter.frequency, audioContext.currentTime);
                        if (effects.filter.Q) {
                            filterNode.Q.setValueAtTime(effects.filter.Q, audioContext.currentTime);
                        }
                        currentNode.connect(filterNode);
                        currentNode = filterNode;
                        console.log("Mock: Applied filter: type=".concat(effects.filter.type, ", freq=").concat(effects.filter.frequency));
                    }
                    else if (filterNode) {
                        // Reset filter if not in current effects
                        filterNode.type = "lowpass";
                        filterNode.frequency.setValueAtTime(filterNode.frequency.defaultValue, audioContext.currentTime);
                        filterNode.Q.setValueAtTime(filterNode.Q.defaultValue, audioContext.currentTime);
                    }
                    // 4. Apply Panner (Stereo Pan)
                    if (effects.pan !== undefined) {
                        if (!pannerNode)
                            pannerNode = audioContext.createPanner();
                        pannerNode.pan.setValueAtTime(effects.pan, audioContext.currentTime);
                        currentNode.connect(pannerNode);
                        currentNode = pannerNode;
                        console.log("Mock: Applied pan: ".concat(effects.pan));
                    }
                    else if (pannerNode) {
                        pannerNode.pan.setValueAtTime(pannerNode.pan.defaultValue, audioContext.currentTime);
                    }
                    // 5. Apply Delay
                    if (effects.delay) {
                        if (!delayNode)
                            delayNode = audioContext.createDelay();
                        delayNode.delayTime.setValueAtTime(effects.delay.delayTime, audioContext.currentTime);
                        currentNode.connect(delayNode);
                        currentNode = delayNode;
                        console.log("Mock: Applied delay: ".concat(effects.delay.delayTime, "s"));
                        // --- MISSING FUNCTION: Delay Feedback Loop ---
                        // HINT FOR AI/Dev: For feedback delay, you'd connect delayNode back to itself via a GainNode:
                        // const feedbackGain = audioContext.createGain();
                        // feedbackGain.gain.value = effects.delay.feedback || 0.5;
                        // delayNode.connect(feedbackGain);
                        // feedbackGain.connect(delayNode);
                    }
                    else if (delayNode) {
                        delayNode.delayTime.setValueAtTime(delayNode.delayTime.defaultValue, audioContext.currentTime);
                    }
                    // 6. Apply Reverb (Convolver)
                    if (effects.reverb && effects.reverb.impulseResponseUrl) {
                        if (!convolverNode)
                            convolverNode = audioContext.createConvolver();
                        // --- MISSING FUNCTION: Fetch and Decode Impulse Response ---
                        // HINT FOR AI/Dev: Similar to track loading, fetch and decode the impulse response:
                        // const irResponse = await fetch(effects.reverb.impulseResponseUrl);
                        // const irArrayBuffer = await irResponse.arrayBuffer();
                        // const irAudioBuffer = await audioContext.decodeAudioData(irArrayBuffer);
                        // convolverNode.buffer = irAudioBuffer;
                        console.log("Mock: (Concept) Would load impulse response from ".concat(effects.reverb.impulseResponseUrl));
                        // For mock, assume it's loaded and connect
                        currentNode.connect(convolverNode);
                        currentNode = convolverNode;
                        console.log("Mock: Applied reverb.");
                    }
                    // Connect the final node in the chain to the destination (speakers)
                    currentNode.connect(audioContext.destination);
                    console.log("Mock: Effect chain connected to destination.");
                    // Start playback
                    sourceNode.start(0);
                    console.log("Mock: Started playback of \"".concat(track.title, "\"."));
                    return [2 /*return*/];
            }
        });
    });
}
/**
 * Updates a specific effect parameter.
 */
function updateEffectParameter(paramName, value) {
    if (!audioContext || audioContext.state !== "running") {
        console.warn("MockAudioContext not running. Cannot update parameters.");
        return;
    }
    console.log("Mock: updateEffectParameter called for ".concat(paramName, " with value ").concat(value));
    switch (paramName) {
        case "gain":
            if (gainNode)
                gainNode.gain.setValueAtTime(value, audioContext.currentTime);
            break;
        case "filter.frequency":
            if (filterNode)
                filterNode.frequency.setValueAtTime(value, audioContext.currentTime);
            break;
        case "filter.Q":
            if (filterNode)
                filterNode.Q.setValueAtTime(value, audioContext.currentTime);
            break;
        case "filter.type":
            if (filterNode)
                filterNode.type = value;
            break;
        case "pan":
            if (pannerNode)
                pannerNode.pan.setValueAtTime(value, audioContext.currentTime);
            break;
        case "delay.delayTime":
            if (delayNode)
                delayNode.delayTime.setValueAtTime(value, audioContext.currentTime);
            break;
        // Add more cases for other parameters as needed
        default:
            console.warn("Mock: Unknown effect parameter: ".concat(paramName));
    }
}
// Example Usage (you can call these from your components)
/*
const exampleTrack: MockMPDTrack = {
  id: "track1",
  url: "/audio/example.mp3", // Placeholder
  title: "Example Song",
  artist: "The Mockers"
};

const initialEffects: EffectsConfig = {
  gain: 0.8,
  filter: { type: "lowpass", frequency: 5000 },
  pan: -0.5 // Pan to the left
};

// To start:
// loadTrackAndApplyEffects(exampleTrack, initialEffects);

// To update gain later:
// updateEffectParameter('gain', 0.5);
// To change filter frequency:
// updateEffectParameter('filter.frequency', 1000);
*/
