"use strict";
/**
 * Socket.IO Singleton Manager
 * Ensures only one socket connection is created and shared across all components
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSocketInstance = getSocketInstance;
exports.disconnectSocket = disconnectSocket;
var socketInstance = null;
var socketPromise = null;
function getSocketInstance() {
    return __awaiter(this, void 0, void 0, function () {
        var socket;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    // If we already have a socket, return it
                    if (socketInstance && socketInstance.connected) {
                        return [2 /*return*/, socketInstance];
                    }
                    // If we're already creating a socket, wait for it
                    if (socketPromise) {
                        return [2 /*return*/, socketPromise];
                    }
                    // Create new socket
                    socketPromise = createSocket();
                    return [4 /*yield*/, socketPromise];
                case 1:
                    socket = _a.sent();
                    socketPromise = null;
                    return [2 /*return*/, socket];
            }
        });
    });
}
function createSocket() {
    return __awaiter(this, void 0, void 0, function () {
        var socketIOClient, io, currentHost, configuredHost, port, socketHost, SOCKET_URL, error_1;
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    if (typeof window === 'undefined') {
                        return [2 /*return*/, null];
                    }
                    _b.label = 1;
                case 1:
                    _b.trys.push([1, 3, , 4]);
                    return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('socket.io-client')); })];
                case 2:
                    socketIOClient = _b.sent();
                    io = socketIOClient.io || ((_a = socketIOClient.default) === null || _a === void 0 ? void 0 : _a.io);
                    if (!io) {
                        console.error('Failed to import Socket.IO');
                        return [2 /*return*/, null];
                    }
                    currentHost = window.location.hostname;
                    configuredHost = process.env.NEXT_PUBLIC_SOCKET_HOST || '************';
                    port = process.env.NEXT_PUBLIC_WEBSOCKET_PORT || '3001';
                    socketHost = configuredHost !== 'localhost' ? configuredHost : currentHost;
                    SOCKET_URL = "http://".concat(socketHost, ":").concat(port);
                    console.log('🔌 Creating singleton socket connection to:', SOCKET_URL);
                    socketInstance = io(SOCKET_URL, {
                        transports: ['polling', 'websocket'],
                        reconnectionAttempts: 3,
                        timeout: 15000,
                        forceNew: false, // Important: reuse existing connection
                        multiplex: true, // Allow multiple namespaces on same connection
                    });
                    // Set up base event handlers
                    socketInstance.on('connect', function () {
                        console.log('🔌 Singleton socket connected');
                    });
                    socketInstance.on('disconnect', function (reason) {
                        console.log('🔌 Singleton socket disconnected:', reason);
                    });
                    socketInstance.on('error', function (error) {
                        console.error('🔌 Singleton socket error:', error);
                    });
                    return [2 /*return*/, socketInstance];
                case 3:
                    error_1 = _b.sent();
                    console.error('🔌 Failed to create socket:', error_1);
                    return [2 /*return*/, null];
                case 4: return [2 /*return*/];
            }
        });
    });
}
function disconnectSocket() {
    if (socketInstance) {
        socketInstance.disconnect();
        socketInstance = null;
    }
}
