"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.volumeStateManager = void 0;
exports.useVolumeState = useVolumeState;
var react_1 = __importDefault(require("react"));
/**
 * Centralized Volume State Manager
 * Manages volume state across quiz and jukebox modes with persistence
 */
var VOLUME_STORAGE_KEY = 'music-quiz-volume-preference';
var DEFAULT_VOLUME = 70;
var VolumeStateManager = /** @class */ (function () {
    function VolumeStateManager() {
        this.listeners = new Set();
        this.currentState = this.loadVolumeState();
    }
    /**
     * Load volume state from localStorage with fallback to defaults
     */
    VolumeStateManager.prototype.loadVolumeState = function () {
        // Check if we're in a browser environment
        if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
            return {
                userVolume: DEFAULT_VOLUME,
                lastSetAt: Date.now(),
                mode: 'unknown'
            };
        }
        try {
            var stored = localStorage.getItem(VOLUME_STORAGE_KEY);
            if (stored) {
                var parsed = JSON.parse(stored);
                // Validate stored data
                if (parsed.userVolume >= 0 && parsed.userVolume <= 100) {
                    return __assign(__assign({}, parsed), { mode: 'unknown' // Reset mode on load
                     });
                }
            }
        }
        catch (error) {
            console.warn('[VolumeState] Failed to load from storage:', error);
        }
        return {
            userVolume: DEFAULT_VOLUME,
            lastSetAt: Date.now(),
            mode: 'unknown'
        };
    };
    /**
     * Save volume state to localStorage
     */
    VolumeStateManager.prototype.saveVolumeState = function () {
        // Check if we're in a browser environment
        if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
            return;
        }
        try {
            localStorage.setItem(VOLUME_STORAGE_KEY, JSON.stringify(this.currentState));
        }
        catch (error) {
            console.warn('[VolumeState] Failed to save to storage:', error);
        }
    };
    /**
     * Get current volume state
     */
    VolumeStateManager.prototype.getVolumeState = function () {
        return __assign({}, this.currentState);
    };
    /**
     * Get current user volume (0-100)
     */
    VolumeStateManager.prototype.getUserVolume = function () {
        return this.currentState.userVolume;
    };
    /**
     * Set user volume and notify listeners
     */
    VolumeStateManager.prototype.setUserVolume = function (volume, mode) {
        if (mode === void 0) { mode = 'unknown'; }
        // Clamp volume to valid range
        var clampedVolume = Math.max(0, Math.min(100, volume));
        var wasChanged = this.currentState.userVolume !== clampedVolume || this.currentState.mode !== mode;
        this.currentState = {
            userVolume: clampedVolume,
            lastSetAt: Date.now(),
            mode: mode
        };
        if (wasChanged) {
            this.saveVolumeState();
            this.notifyListeners();
            console.log("[VolumeState] Volume set to ".concat(clampedVolume, "% in ").concat(mode, " mode"));
        }
    };
    /**
     * Update mode without changing volume
     */
    VolumeStateManager.prototype.setMode = function (mode) {
        if (this.currentState.mode !== mode) {
            this.currentState.mode = mode;
            this.saveVolumeState();
            console.log("[VolumeState] Mode changed to ".concat(mode, ", volume: ").concat(this.currentState.userVolume, "%"));
        }
    };
    /**
     * Add volume change listener
     */
    VolumeStateManager.prototype.addListener = function (listener) {
        var _this = this;
        this.listeners.add(listener);
        return function () { return _this.listeners.delete(listener); };
    };
    /**
     * Notify all listeners of volume state changes
     */
    VolumeStateManager.prototype.notifyListeners = function () {
        var _this = this;
        this.listeners.forEach(function (listener) {
            try {
                listener(_this.getVolumeState());
            }
            catch (error) {
                console.error('[VolumeState] Listener error:', error);
            }
        });
    };
    /**
     * Initialize volume for a specific mode
     * Returns the volume that should be used for initialization
     */
    VolumeStateManager.prototype.initializeForMode = function (mode) {
        this.setMode(mode);
        // Return the user's preferred volume, not a forced default
        return this.currentState.userVolume;
    };
    /**
     * Handle transition between modes
     * Ensures smooth volume continuity
     */
    VolumeStateManager.prototype.transitionToMode = function (fromMode, toMode) {
        console.log("[VolumeState] Transitioning from ".concat(fromMode, " to ").concat(toMode));
        // Update mode
        this.setMode(toMode);
        // Return current user volume for smooth transition
        return this.currentState.userVolume;
    };
    /**
     * Reset to default volume (for emergency/debugging)
     */
    VolumeStateManager.prototype.resetToDefault = function () {
        this.setUserVolume(DEFAULT_VOLUME);
        console.log('[VolumeState] Reset to default volume');
    };
    /**
     * Get debug information
     */
    VolumeStateManager.prototype.getDebugInfo = function () {
        return {
            state: this.getVolumeState(),
            storage: typeof window !== 'undefined' && typeof localStorage !== 'undefined'
                ? localStorage.getItem(VOLUME_STORAGE_KEY)
                : null,
            listeners: this.listeners.size
        };
    };
    return VolumeStateManager;
}());
// Export singleton instance
exports.volumeStateManager = new VolumeStateManager();
// Export React hook for easy component usage
function useVolumeState() {
    var _a = react_1.default.useState(exports.volumeStateManager.getVolumeState()), volumeState = _a[0], setVolumeState = _a[1];
    react_1.default.useEffect(function () {
        return exports.volumeStateManager.addListener(setVolumeState);
    }, []);
    return __assign(__assign({}, volumeState), { setUserVolume: exports.volumeStateManager.setUserVolume.bind(exports.volumeStateManager), setMode: exports.volumeStateManager.setMode.bind(exports.volumeStateManager), getUserVolume: exports.volumeStateManager.getUserVolume.bind(exports.volumeStateManager), initializeForMode: exports.volumeStateManager.initializeForMode.bind(exports.volumeStateManager), transitionToMode: exports.volumeStateManager.transitionToMode.bind(exports.volumeStateManager) });
}
