"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JukeboxSocketHandler = void 0;
var skip_vote_manager_1 = require("./skip-vote-manager");
var mpd_client_1 = require("./mpd-client");
var env_1 = require("./env");
var auth_service_1 = require("./auth-service");
var mpd_status_cache_1 = require("./mpd-status-cache");
var JukeboxSocketHandler = /** @class */ (function () {
    function JukeboxSocketHandler(io) {
        this.statusPollingInterval = null;
        this.currentTrack = null;
        this.io = io;
        this.skipVoteManager = new skip_vote_manager_1.SkipVoteManager(io);
        var config = (0, env_1.getAudioConfig)();
        this.mpdClient = new mpd_client_1.MPDClient({
            host: config.mpdHost,
            port: config.mpdPort,
            password: config.mpdPassword,
            httpProxyPort: config.mpdHttpPort
        });
        this.setupNamespace();
        this.startStatusPolling();
    }
    JukeboxSocketHandler.prototype.setupNamespace = function () {
        var _this = this;
        var jukeboxNamespace = this.io.of('/jukebox');
        jukeboxNamespace.on('connection', function (socket) {
            console.log("\uD83C\uDFB5 Jukebox client connected: ".concat(socket.id));
            // Authenticate user
            socket.on('authenticate', function (data) { return __awaiter(_this, void 0, void 0, function () {
                var userInfo, error_1;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 7, , 8]);
                            userInfo = null;
                            if (!data.token) return [3 /*break*/, 2];
                            return [4 /*yield*/, auth_service_1.AuthService.verifyToken(data.token)];
                        case 1:
                            userInfo = _a.sent();
                            return [3 /*break*/, 3];
                        case 2:
                            if (data.userId) {
                                // Direct user info from cookie-based auth
                                userInfo = {
                                    id: data.userId,
                                    role: data.role || 'user'
                                };
                            }
                            _a.label = 3;
                        case 3:
                            if (!userInfo) return [3 /*break*/, 5];
                            socket.data.userId = userInfo.id;
                            socket.data.userName = userInfo.username || userInfo.id;
                            socket.data.role = userInfo.role;
                            // Track connected user
                            this.skipVoteManager.setConnectedUser(userInfo.id, true);
                            // Send current status
                            return [4 /*yield*/, this.sendCurrentStatus(socket)];
                        case 4:
                            // Send current status
                            _a.sent();
                            socket.emit('authenticated', { success: true, user: userInfo });
                            return [3 /*break*/, 6];
                        case 5:
                            socket.emit('authenticated', { success: false, error: 'Invalid token' });
                            _a.label = 6;
                        case 6: return [3 /*break*/, 8];
                        case 7:
                            error_1 = _a.sent();
                            console.error('Authentication error:', error_1);
                            socket.emit('authenticated', { success: false, error: 'Authentication failed' });
                            return [3 /*break*/, 8];
                        case 8: return [2 /*return*/];
                    }
                });
            }); });
            // Handle skip vote
            socket.on('skip-vote', function (data) { return __awaiter(_this, void 0, void 0, function () {
                var status_1, error_2;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            console.log('Received skip vote:', data);
                            if (!socket.data.userId) {
                                socket.emit('skip-vote-error', { message: 'Not authenticated' });
                                return [2 /*return*/];
                            }
                            return [4 /*yield*/, this.skipVoteManager.addVote(socket.data.userId, socket.data.userName || 'Anonymous')];
                        case 1:
                            status_1 = _a.sent();
                            console.log('Skip vote added, current status:', {
                                votes: status_1.totalVotes,
                                needed: status_1.votesNeeded,
                                percentage: status_1.percentage
                            });
                            return [3 /*break*/, 3];
                        case 2:
                            error_2 = _a.sent();
                            console.error('Skip vote error:', error_2);
                            socket.emit('error', { message: 'Failed to register vote' });
                            return [3 /*break*/, 3];
                        case 3: return [2 /*return*/];
                    }
                });
            }); });
            // Get current skip vote status
            socket.on('get-skip-vote-status', function () {
                var status = _this.skipVoteManager.getVoteStatus();
                socket.emit('skip-vote-update', status);
            });
            // Handle disconnect
            socket.on('disconnect', function () {
                console.log("\uD83C\uDFB5 Jukebox client disconnected: ".concat(socket.id));
                if (socket.data.userId) {
                    _this.skipVoteManager.setConnectedUser(socket.data.userId, false);
                }
            });
            // MPD control events (for DJs and superusers)
            socket.on('mpd-control', function (data) { return __awaiter(_this, void 0, void 0, function () {
                var error_3;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            if (!socket.data.role || !['dj', 'superuser'].includes(socket.data.role)) {
                                socket.emit('error', { message: 'Insufficient permissions' });
                                return [2 /*return*/];
                            }
                            _a.label = 1;
                        case 1:
                            _a.trys.push([1, 4, , 5]);
                            return [4 /*yield*/, this.handleMPDControl(data.action, data.params)
                                // Broadcast status update to all clients
                            ];
                        case 2:
                            _a.sent();
                            // Broadcast status update to all clients
                            return [4 /*yield*/, this.broadcastStatus()];
                        case 3:
                            // Broadcast status update to all clients
                            _a.sent();
                            return [3 /*break*/, 5];
                        case 4:
                            error_3 = _a.sent();
                            console.error('MPD control error:', error_3);
                            socket.emit('error', { message: "Failed to execute ".concat(data.action) });
                            return [3 /*break*/, 5];
                        case 5: return [2 /*return*/];
                    }
                });
            }); });
        });
    };
    JukeboxSocketHandler.prototype.handleMPDControl = function (action, params) {
        return __awaiter(this, void 0, void 0, function () {
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.mpdClient.connect()];
                    case 1:
                        _b.sent();
                        _b.label = 2;
                    case 2:
                        _b.trys.push([2, , 19, 21]);
                        _a = action;
                        switch (_a) {
                            case 'play': return [3 /*break*/, 3];
                            case 'pause': return [3 /*break*/, 5];
                            case 'next': return [3 /*break*/, 7];
                            case 'previous': return [3 /*break*/, 9];
                            case 'volume': return [3 /*break*/, 11];
                            case 'seek': return [3 /*break*/, 14];
                        }
                        return [3 /*break*/, 17];
                    case 3: return [4 /*yield*/, this.mpdClient.play()];
                    case 4:
                        _b.sent();
                        return [3 /*break*/, 18];
                    case 5: return [4 /*yield*/, this.mpdClient.pause()];
                    case 6:
                        _b.sent();
                        return [3 /*break*/, 18];
                    case 7: return [4 /*yield*/, this.mpdClient.nextTrack()];
                    case 8:
                        _b.sent();
                        return [3 /*break*/, 18];
                    case 9: return [4 /*yield*/, this.mpdClient.previousTrack()];
                    case 10:
                        _b.sent();
                        return [3 /*break*/, 18];
                    case 11:
                        if (!((params === null || params === void 0 ? void 0 : params.volume) !== undefined)) return [3 /*break*/, 13];
                        return [4 /*yield*/, this.mpdClient.setVolume(params.volume)];
                    case 12:
                        _b.sent();
                        _b.label = 13;
                    case 13: return [3 /*break*/, 18];
                    case 14:
                        if (!((params === null || params === void 0 ? void 0 : params.position) !== undefined)) return [3 /*break*/, 16];
                        return [4 /*yield*/, this.mpdClient.seek(params.position)];
                    case 15:
                        _b.sent();
                        _b.label = 16;
                    case 16: return [3 /*break*/, 18];
                    case 17: throw new Error("Unknown action: ".concat(action));
                    case 18: return [3 /*break*/, 21];
                    case 19: return [4 /*yield*/, this.mpdClient.disconnect()];
                    case 20:
                        _b.sent();
                        return [7 /*endfinally*/];
                    case 21: return [2 /*return*/];
                }
            });
        });
    };
    JukeboxSocketHandler.prototype.startStatusPolling = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                // Poll MPD status every 5 seconds (reduced from 2s to decrease server load)
                this.statusPollingInterval = setInterval(function () { return __awaiter(_this, void 0, void 0, function () {
                    var error_4;
                    return __generator(this, function (_a) {
                        switch (_a.label) {
                            case 0:
                                _a.trys.push([0, 2, , 3]);
                                return [4 /*yield*/, this.broadcastStatus()];
                            case 1:
                                _a.sent();
                                return [3 /*break*/, 3];
                            case 2:
                                error_4 = _a.sent();
                                console.error('Status polling error:', error_4);
                                return [3 /*break*/, 3];
                            case 3: return [2 /*return*/];
                        }
                    });
                }); }, 5000);
                return [2 /*return*/];
            });
        });
    };
    JukeboxSocketHandler.prototype.broadcastStatus = function () {
        return __awaiter(this, void 0, void 0, function () {
            var cache, cached, status_2, currentSong, queue, trackId, error_5;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 11, , 12]);
                        cache = mpd_status_cache_1.MPDStatusCache.getInstance();
                        cached = cache.get();
                        currentSong = void 0, queue = void 0;
                        if (!cached) return [3 /*break*/, 4];
                        // Use cached status and current song
                        status_2 = cached.status;
                        currentSong = cached.currentSong;
                        // Still need to fetch queue as it's not cached
                        return [4 /*yield*/, this.mpdClient.connect()];
                    case 1:
                        // Still need to fetch queue as it's not cached
                        _b.sent();
                        return [4 /*yield*/, this.mpdClient.getCurrentPlaylist()];
                    case 2:
                        queue = _b.sent();
                        return [4 /*yield*/, this.mpdClient.disconnect()];
                    case 3:
                        _b.sent();
                        return [3 /*break*/, 10];
                    case 4: 
                    // No cache, fetch everything
                    return [4 /*yield*/, this.mpdClient.connect()];
                    case 5:
                        // No cache, fetch everything
                        _b.sent();
                        return [4 /*yield*/, this.mpdClient.getStatus()];
                    case 6:
                        status_2 = _b.sent();
                        return [4 /*yield*/, this.mpdClient.getCurrentSong()];
                    case 7:
                        currentSong = _b.sent();
                        return [4 /*yield*/, this.mpdClient.getCurrentPlaylist()];
                    case 8:
                        queue = _b.sent();
                        return [4 /*yield*/, this.mpdClient.disconnect()
                            // Update cache
                        ];
                    case 9:
                        _b.sent();
                        // Update cache
                        cache.set(status_2, currentSong);
                        _b.label = 10;
                    case 10:
                        // Check if track changed
                        if (currentSong) {
                            trackId = ((_a = currentSong.id) === null || _a === void 0 ? void 0 : _a.toString()) || currentSong.file;
                            if (!this.currentTrack || this.currentTrack.id !== trackId) {
                                this.currentTrack = {
                                    id: trackId,
                                    title: currentSong.title || 'Unknown Title',
                                    artist: currentSong.artist || 'Unknown Artist'
                                };
                                // Update skip vote manager with new track
                                this.skipVoteManager.setCurrentTrack(trackId, this.currentTrack.title, this.currentTrack.artist);
                            }
                        }
                        // Broadcast to all connected clients
                        this.io.of('/jukebox').emit('mpd-status', {
                            status: status_2,
                            currentSong: currentSong,
                            queue: queue,
                            timestamp: Date.now()
                        });
                        return [3 /*break*/, 12];
                    case 11:
                        error_5 = _b.sent();
                        console.error('Failed to broadcast MPD status:', error_5);
                        return [3 /*break*/, 12];
                    case 12: return [2 /*return*/];
                }
            });
        });
    };
    JukeboxSocketHandler.prototype.sendCurrentStatus = function (socket) {
        return __awaiter(this, void 0, void 0, function () {
            var cache, cached, status_3, currentSong, queue, skipVoteStatus, error_6;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 11, , 12]);
                        cache = mpd_status_cache_1.MPDStatusCache.getInstance();
                        cached = cache.get();
                        currentSong = void 0, queue = void 0;
                        if (!cached) return [3 /*break*/, 4];
                        // Use cached status and current song
                        status_3 = cached.status;
                        currentSong = cached.currentSong;
                        // Still need to fetch queue as it's not cached
                        return [4 /*yield*/, this.mpdClient.connect()];
                    case 1:
                        // Still need to fetch queue as it's not cached
                        _a.sent();
                        return [4 /*yield*/, this.mpdClient.getCurrentPlaylist()];
                    case 2:
                        queue = _a.sent();
                        return [4 /*yield*/, this.mpdClient.disconnect()];
                    case 3:
                        _a.sent();
                        return [3 /*break*/, 10];
                    case 4: 
                    // No cache, fetch everything
                    return [4 /*yield*/, this.mpdClient.connect()];
                    case 5:
                        // No cache, fetch everything
                        _a.sent();
                        return [4 /*yield*/, this.mpdClient.getStatus()];
                    case 6:
                        status_3 = _a.sent();
                        return [4 /*yield*/, this.mpdClient.getCurrentSong()];
                    case 7:
                        currentSong = _a.sent();
                        return [4 /*yield*/, this.mpdClient.getCurrentPlaylist()];
                    case 8:
                        queue = _a.sent();
                        return [4 /*yield*/, this.mpdClient.disconnect()
                            // Update cache
                        ];
                    case 9:
                        _a.sent();
                        // Update cache
                        cache.set(status_3, currentSong);
                        _a.label = 10;
                    case 10:
                        socket.emit('mpd-status', {
                            status: status_3,
                            currentSong: currentSong,
                            queue: queue,
                            timestamp: Date.now()
                        });
                        skipVoteStatus = this.skipVoteManager.getVoteStatus();
                        socket.emit('skip-vote-update', skipVoteStatus);
                        return [3 /*break*/, 12];
                    case 11:
                        error_6 = _a.sent();
                        console.error('Failed to send current status:', error_6);
                        socket.emit('error', { message: 'Failed to get current status' });
                        return [3 /*break*/, 12];
                    case 12: return [2 /*return*/];
                }
            });
        });
    };
    JukeboxSocketHandler.prototype.cleanup = function () {
        if (this.statusPollingInterval) {
            clearInterval(this.statusPollingInterval);
            this.statusPollingInterval = null;
        }
    };
    return JukeboxSocketHandler;
}());
exports.JukeboxSocketHandler = JukeboxSocketHandler;
