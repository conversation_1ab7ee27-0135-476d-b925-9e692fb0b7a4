"use strict";
/**
 * Network utilities for handling dynamic host detection
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentHost = getCurrentHost;
exports.isRestrictedNetwork = isRestrictedNetwork;
exports.isMobileNetwork = isMobileNetwork;
exports.getBaseUrl = getBaseUrl;
exports.getSocketUrl = getSocketUrl;
exports.getMpdProxyUrl = getMpdProxyUrl;
/**
 * Get the current host from the browser
 */
function getCurrentHost() {
    if (typeof window === 'undefined') {
        // Server-side
        return process.env.NEXT_PUBLIC_HOST || 'localhost';
    }
    // Client-side - use the actual host from the browser
    return window.location.hostname;
}
/**
 * Detect if we're on a restricted network (mobile hotspot, cellular, or problematic WiFi)
 * This is a heuristic based on common patterns and connection characteristics
 */
function isRestrictedNetwork() {
    if (typeof window === 'undefined') {
        return false;
    }
    var hostname = window.location.hostname;
    // Common hotspot IP patterns (both mobile and WiFi hotspots)
    var hotspotPatterns = [
        /^192\.168\.43\.\d+$/, // Android mobile hotspot
        /^172\.20\.10\.\d+$/, // iOS Personal Hotspot
        /^192\.168\.137\.\d+$/, // Windows Mobile hotspot
        /^192\.168\.\d+\.1$/, // Common router pattern (when device is the gateway)
        /^10\.42\.0\.\d+$/, // Ubuntu/Linux hotspot
        /^192\.168\.42\.\d+$/, // Some WiFi hotspot devices
        /^192\.168\.2\.\d+$/, // Some mobile routers
        /^10\.\d+\.\d+\.\d+$/, // Carrier-grade NAT range (often problematic)
    ];
    // Check if hostname matches any hotspot pattern
    var isHotspotIP = hotspotPatterns.some(function (pattern) { return pattern.test(hostname); });
    // Check Network Information API if available
    var connection = navigator.connection ||
        navigator.mozConnection ||
        navigator.webkitConnection;
    if (connection) {
        var connectionType = connection.type;
        var effectiveType = connection.effectiveType;
        // Even on WiFi, some connections are restricted (like WiFi hotspots)
        // Check for slow connections or specific types
        if (effectiveType && ['slow-2g', '2g', '3g'].includes(effectiveType)) {
            return true;
        }
        // Cellular connections are always restricted
        if (connectionType && ['cellular', 'wimax'].includes(connectionType)) {
            return true;
        }
    }
    // If we detected a hotspot IP pattern, treat it as restricted
    if (isHotspotIP) {
        return true;
    }
    // Additional check: if we're not on localhost and the hostname is an IP
    // This catches many hotspot scenarios that use IP addresses instead of domains
    var isIP = /^\d+\.\d+\.\d+\.\d+$/.test(hostname);
    var isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
    // Be conservative - if accessing via IP (not localhost), assume it might be restricted
    if (isIP && !isLocalhost) {
        return true;
    }
    return false;
}
/**
 * Legacy function name for compatibility
 * @deprecated Use isRestrictedNetwork() instead
 */
function isMobileNetwork() {
    return isRestrictedNetwork();
}
/**
 * Get the base URL for API calls
 */
function getBaseUrl() {
    if (typeof window === 'undefined') {
        // Server-side
        return process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    }
    // Client-side - use the current protocol and host
    var protocol = window.location.protocol;
    var host = window.location.host;
    return "".concat(protocol, "//").concat(host);
}
/**
 * Get the socket URL based on current environment
 */
function getSocketUrl() {
    var host = getCurrentHost();
    var socketPort = process.env.NEXT_PUBLIC_SOCKET_PORT || '3001';
    // If we're on localhost or development, use the configured socket URL
    if (host === 'localhost' || host === '127.0.0.1') {
        return process.env.NEXT_PUBLIC_SOCKET_URL || "http://".concat(host, ":").concat(socketPort);
    }
    // For other hosts (like when accessed via IP on mobile), use the same host
    // This handles mobile hotspot scenarios where the host is an IP address
    var protocol = typeof window !== 'undefined' ? window.location.protocol : 'http:';
    // Remove the port from host if it exists (e.g., "************:3000" -> "************")
    var hostWithoutPort = host.split(':')[0];
    return "".concat(protocol, "//").concat(hostWithoutPort, ":").concat(socketPort);
}
/**
 * Get the MPD proxy URL based on current environment
 */
function getMpdProxyUrl() {
    var host = getCurrentHost();
    var mpdProxyPort = process.env.NEXT_PUBLIC_MPD_HTTP_PORT || '8001';
    // For MPD, we might need to use a specific host
    var mpdHost = process.env.NEXT_PUBLIC_MPD_HOST || host;
    // If accessing from mobile, ensure we use the server's IP, not localhost
    if (host !== 'localhost' && host !== '127.0.0.1') {
        // Use the MPD host from env if available, otherwise use current host
        var protocol = typeof window !== 'undefined' ? window.location.protocol : 'http:';
        var hostWithoutPort = mpdHost.split(':')[0];
        return "".concat(protocol, "//").concat(hostWithoutPort, ":").concat(mpdProxyPort);
    }
    return "http://".concat(mpdHost, ":").concat(mpdProxyPort);
}
