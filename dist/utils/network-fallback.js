"use strict";
/**
 * Enhanced network fallback utilities for handling disconnected LAN scenarios
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkServerReachability = checkServerReachability;
exports.checkMpdReachability = checkMpdReachability;
exports.isLocalhostMode = isLocalhostMode;
exports.getFallbackUrls = getFallbackUrls;
exports.initNetworkMonitoring = initNetworkMonitoring;
exports.getNetworkStatus = getNetworkStatus;
exports.fetchWithFallback = fetchWithFallback;
exports.getAdaptiveSocketUrl = getAdaptiveSocketUrl;
exports.getAdaptiveMpdUrl = getAdaptiveMpdUrl;
var sonner_1 = require("sonner");
var networkStatus = {
    isOnline: true,
    isLanConnected: true,
    canReachServer: true,
    canReachMpd: true,
    fallbackMode: false
};
/**
 * Check if we can reach the configured server
 */
function checkServerReachability() {
    return __awaiter(this, void 0, void 0, function () {
        var controller_1, timeoutId, response, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    controller_1 = new AbortController();
                    timeoutId = setTimeout(function () { return controller_1.abort(); }, 3000);
                    return [4 /*yield*/, fetch('/api/health', {
                            method: 'HEAD',
                            signal: controller_1.signal
                        })];
                case 1:
                    response = _a.sent();
                    clearTimeout(timeoutId);
                    return [2 /*return*/, response.ok];
                case 2:
                    error_1 = _a.sent();
                    return [2 /*return*/, false];
                case 3: return [2 /*return*/];
            }
        });
    });
}
/**
 * Check if we can reach the MPD proxy
 */
function checkMpdReachability() {
    return __awaiter(this, void 0, void 0, function () {
        var mpdHost, mpdPort, mpdProxyUrl, controller_2, timeoutId, response, error_2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    mpdHost = process.env.NEXT_PUBLIC_MPD_HOST || window.location.hostname;
                    mpdPort = process.env.NEXT_PUBLIC_MPD_HTTP_PORT || '8001';
                    mpdProxyUrl = "http://".concat(mpdHost, ":").concat(mpdPort);
                    controller_2 = new AbortController();
                    timeoutId = setTimeout(function () { return controller_2.abort(); }, 3000);
                    return [4 /*yield*/, fetch("".concat(mpdProxyUrl, "/status"), {
                            method: 'GET',
                            signal: controller_2.signal
                        })];
                case 1:
                    response = _a.sent();
                    clearTimeout(timeoutId);
                    return [2 /*return*/, response.ok];
                case 2:
                    error_2 = _a.sent();
                    return [2 /*return*/, false];
                case 3: return [2 /*return*/];
            }
        });
    });
}
/**
 * Check if we're in localhost mode (development or local access)
 */
function isLocalhostMode() {
    if (typeof window === 'undefined')
        return true;
    var hostname = window.location.hostname;
    return hostname === 'localhost' || hostname === '127.0.0.1';
}
/**
 * Get fallback URLs for different services
 */
function getFallbackUrls() {
    var isLocalhost = isLocalhostMode();
    return {
        api: isLocalhost ? 'http://localhost:3000' : window.location.origin,
        socket: isLocalhost ? 'http://localhost:3001' : "".concat(window.location.protocol, "//").concat(window.location.hostname, ":3001"),
        mpd: isLocalhost ? 'http://localhost:8001' : "".concat(window.location.protocol, "//").concat(window.location.hostname, ":8001")
    };
}
/**
 * Initialize network monitoring
 */
function initNetworkMonitoring() {
    if (typeof window === 'undefined')
        return;
    // Monitor online/offline status
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    // Check initial status
    checkNetworkStatus();
    // Periodic health check
    setInterval(checkNetworkStatus, 30000); // Every 30 seconds
}
function handleOnline() {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log('🌐 Network: Online event detected');
                    networkStatus.isOnline = true;
                    return [4 /*yield*/, checkNetworkStatus()];
                case 1:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    });
}
function handleOffline() {
    console.log('🌐 Network: Offline event detected');
    networkStatus.isOnline = false;
    networkStatus.canReachServer = false;
    networkStatus.canReachMpd = false;
    networkStatus.fallbackMode = true;
    sonner_1.toast.warning('Network disconnected', {
        description: 'Running in offline mode. Some features may be limited.',
        duration: 5000
    });
}
function checkNetworkStatus() {
    return __awaiter(this, void 0, void 0, function () {
        var wasReachable, wasMpdReachable, _a, _b, shouldFallback;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    wasReachable = networkStatus.canReachServer;
                    wasMpdReachable = networkStatus.canReachMpd;
                    // Check both server and MPD reachability
                    _a = networkStatus;
                    return [4 /*yield*/, checkServerReachability()];
                case 1:
                    // Check both server and MPD reachability
                    _a.canReachServer = _c.sent();
                    _b = networkStatus;
                    return [4 /*yield*/, checkMpdReachability()
                        // Determine fallback mode based on both checks
                    ];
                case 2:
                    _b.canReachMpd = _c.sent();
                    shouldFallback = !networkStatus.canReachServer || !networkStatus.canReachMpd;
                    if (!wasReachable && networkStatus.canReachServer && !wasMpdReachable && networkStatus.canReachMpd) {
                        // Both services became reachable
                        networkStatus.fallbackMode = false;
                        sonner_1.toast.success('All services restored', {
                            description: 'Server and MPD connections are now available.',
                            duration: 3000
                        });
                    }
                    else if (wasReachable && !networkStatus.canReachServer) {
                        // Server became unreachable
                        networkStatus.fallbackMode = true;
                        sonner_1.toast.warning('Server unreachable', {
                            description: 'Some features may be limited. Check your network connection.',
                            duration: 5000
                        });
                    }
                    else if (wasMpdReachable && !networkStatus.canReachMpd) {
                        // MPD became unreachable
                        networkStatus.fallbackMode = true;
                        sonner_1.toast.warning('MPD service unreachable', {
                            description: 'Music playback may be limited. Check MPD connection.',
                            duration: 5000
                        });
                    }
                    else if (shouldFallback !== networkStatus.fallbackMode) {
                        // Update fallback mode
                        networkStatus.fallbackMode = shouldFallback;
                    }
                    return [2 /*return*/];
            }
        });
    });
}
/**
 * Get current network status
 */
function getNetworkStatus() {
    // Check if network fallback is disabled
    if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_DISABLE_NETWORK_FALLBACK === 'true') {
        return {
            isOnline: true,
            isLanConnected: true,
            canReachServer: true,
            canReachMpd: true,
            fallbackMode: false
        };
    }
    return __assign({}, networkStatus);
}
/**
 * Wrapper for fetch with automatic fallback
 */
function fetchWithFallback(url, options) {
    return __awaiter(this, void 0, void 0, function () {
        var response, error_3;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    return [4 /*yield*/, fetch(url, __assign(__assign({}, options), { 
                            // Add timeout for all requests
                            signal: (options === null || options === void 0 ? void 0 : options.signal) || AbortSignal.timeout(10000), 
                            // Preserve credentials for cross-origin requests
                            credentials: (options === null || options === void 0 ? void 0 : options.credentials) || 'include' }))];
                case 1:
                    response = _a.sent();
                    if (!response.ok && response.status >= 500) {
                        // Server error, might be unreachable
                        checkNetworkStatus();
                    }
                    return [2 /*return*/, response];
                case 2:
                    error_3 = _a.sent();
                    // Network error
                    checkNetworkStatus();
                    // Don't retry with different URLs - this causes CORS issues
                    // Just let the error propagate so the app can handle it
                    throw error_3;
                case 3: return [2 /*return*/];
            }
        });
    });
}
/**
 * Get appropriate socket URL based on network status
 */
function getAdaptiveSocketUrl() {
    var fallbackMode = networkStatus.fallbackMode;
    if (fallbackMode || !networkStatus.canReachServer) {
        // Use localhost in fallback mode
        return 'http://localhost:3001';
    }
    // Use the configured URL from environment or dynamic detection
    var host = window.location.hostname;
    var protocol = window.location.protocol;
    var socketPort = process.env.NEXT_PUBLIC_SOCKET_PORT || '3001';
    // If accessing via IP, use that IP for socket too
    if (host.match(/^\d+\.\d+\.\d+\.\d+$/)) {
        return "".concat(protocol, "//").concat(host, ":").concat(socketPort);
    }
    // Default to environment variable or localhost
    return process.env.NEXT_PUBLIC_SOCKET_URL || "http://localhost:".concat(socketPort);
}
/**
 * Get appropriate MPD proxy URL based on network status
 */
function getAdaptiveMpdUrl() {
    var fallbackMode = networkStatus.fallbackMode;
    if (fallbackMode || !networkStatus.canReachServer) {
        // Use localhost in fallback mode
        return 'http://localhost:8001';
    }
    // Use the configured URL from environment
    var mpdHost = process.env.NEXT_PUBLIC_MPD_HOST || window.location.hostname;
    var mpdPort = process.env.NEXT_PUBLIC_MPD_HTTP_PORT || '8001';
    return "http://".concat(mpdHost, ":").concat(mpdPort);
}
