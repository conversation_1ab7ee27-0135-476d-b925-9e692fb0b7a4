"use strict";
/**
 * Team Game Integration
 * Integrates team functionality with the core quiz game system
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.teamGameIntegration = exports.TeamGameIntegration = void 0;
const team_scoring_1 = require("./team-scoring");
const team_game_modes_1 = require("./team-game-modes");
class TeamGameIntegration {
    constructor() {
        this.activeGames = new Map();
        this.questionTimers = new Map();
        this.collaborationTimers = new Map();
    }
    /**
     * Initialize team game session
     */
    initializeTeamGame(gameId, teams, settings, totalQuestions) {
        const gameState = team_game_modes_1.teamGameModeManager.initializeTeamGame(gameId, teams, settings);
        const session = {
            gameId,
            teams: [...teams],
            settings,
            gameState,
            currentQuestion: 0,
            totalQuestions,
            questionStartTime: Date.now(),
            teamAnswers: new Map(),
            teamChatMessages: [],
            scoreHistory: [],
            achievements: new Map()
        };
        this.activeGames.set(gameId, session);
        return session;
    }
    /**
     * Start a new question for team game
     */
    startQuestion(gameId, questionNumber, questionData, timeLimit) {
        var _a;
        const session = this.activeGames.get(gameId);
        if (!session)
            return { success: false, activeTeams: [] };
        // Clear previous question state
        session.teamAnswers.clear();
        session.currentQuestion = questionNumber;
        session.questionStartTime = Date.now();
        // Reset team ready states
        team_game_modes_1.teamGameModeManager.resetForNewQuestion(gameId);
        // Start collaboration time for collaborative mode
        if (session.settings.teamGameMode === 'collaborative') {
            session.collaborationStartTime = Date.now();
            this.startCollaborationTimer(gameId, session.settings.collaborationTime);
        }
        // Get active teams for this question
        const turnInfo = team_game_modes_1.teamGameModeManager.getCurrentTurnInfo(gameId, questionData.category);
        const activeTeams = turnInfo.map(info => info.teamId);
        // Start question timer
        this.startQuestionTimer(gameId, timeLimit);
        return {
            success: true,
            collaborationTime: session.settings.teamGameMode === 'collaborative'
                ? session.settings.collaborationTime
                : undefined,
            activeTeams,
            currentTurn: (_a = turnInfo.find(info => info.canAnswer)) === null || _a === void 0 ? void 0 : _a.activePlayerId
        };
    }
    /**
     * Handle team answer submission
     */
    submitTeamAnswer(gameId, teamAnswer) {
        var _a;
        const session = this.activeGames.get(gameId);
        if (!session)
            return { success: false, reason: 'Game not found', allTeamsAnswered: false };
        // Check if team can answer
        const player = (_a = session.teams
            .find(t => t.id === teamAnswer.teamId)) === null || _a === void 0 ? void 0 : _a.players.find(p => p.id === teamAnswer.submittedBy);
        if (!player) {
            return { success: false, reason: 'Player not found', allTeamsAnswered: false };
        }
        // Handle answer based on game mode
        const result = team_game_modes_1.teamGameModeManager.handleTeamAnswer(gameId, teamAnswer, session.teams);
        if (result.accepted) {
            // Store the answer
            session.teamAnswers.set(teamAnswer.teamId, teamAnswer);
            // Update team state
            const teamIndex = session.teams.findIndex(t => t.id === teamAnswer.teamId);
            if (teamIndex !== -1) {
                session.teams[teamIndex] = {
                    ...session.teams[teamIndex],
                    hasAnswered: true,
                    lastAnswer: teamAnswer.answer,
                    lastAnswerTime: teamAnswer.timestamp
                };
            }
        }
        // Check if all teams have answered
        const allAnswered = this.checkAllTeamsAnswered(session);
        return {
            success: result.accepted,
            reason: result.reason,
            nextTurn: result.nextTurn,
            allTeamsAnswered: allAnswered
        };
    }
    /**
     * Process question results and calculate scores
     */
    processQuestionResults(gameId, correctAnswer, questionDifficulty, timeLimit) {
        const session = this.activeGames.get(gameId);
        if (!session)
            return { scoreResults: [], updatedTeams: [], achievements: [] };
        // Convert team answers to array
        const teamAnswers = Array.from(session.teamAnswers.values());
        // Calculate scores
        const scoreResults = team_scoring_1.teamScoringSystem.calculateTeamScores(session.teams, teamAnswers, correctAnswer, questionDifficulty, timeLimit, session.settings.teamScoringMode, session.settings.teamGameMode);
        // Apply scores to teams
        const updatedTeams = team_scoring_1.teamScoringSystem.applyScoreResults(session.teams, scoreResults);
        session.teams = updatedTeams;
        // Store score history
        session.scoreHistory.push(scoreResults);
        // Check for achievements
        const achievements = this.checkTeamAchievements(session, scoreResults);
        // Clear question timers
        this.clearQuestionTimers(gameId);
        return {
            scoreResults,
            updatedTeams,
            achievements
        };
    }
    /**
     * Handle team chat message
     */
    handleTeamChat(gameId, message) {
        const session = this.activeGames.get(gameId);
        if (!session)
            return false;
        // Verify player is in the team
        const team = session.teams.find(t => t.id === message.teamId);
        const player = team === null || team === void 0 ? void 0 : team.players.find(p => p.id === message.playerId);
        if (!team || !player)
            return false;
        // Add message to session
        session.teamChatMessages.push(message);
        // Limit chat history
        if (session.teamChatMessages.length > 100) {
            session.teamChatMessages = session.teamChatMessages.slice(-100);
        }
        return true;
    }
    /**
     * Get team game session
     */
    getTeamGameSession(gameId) {
        return this.activeGames.get(gameId);
    }
    /**
     * Get team leaderboard
     */
    getTeamLeaderboard(gameId) {
        const session = this.activeGames.get(gameId);
        if (!session)
            return [];
        return team_scoring_1.teamScoringSystem.getTeamLeaderboard(session.teams);
    }
    /**
     * Get team chat messages
     */
    getTeamChatMessages(gameId, teamId) {
        const session = this.activeGames.get(gameId);
        if (!session)
            return [];
        if (teamId) {
            return session.teamChatMessages.filter(msg => msg.teamId === teamId);
        }
        return session.teamChatMessages;
    }
    /**
     * Check if collaboration time is active
     */
    isCollaborationTimeActive(gameId) {
        const session = this.activeGames.get(gameId);
        if (!session || !session.collaborationStartTime) {
            return { active: false, timeRemaining: 0 };
        }
        const elapsed = (Date.now() - session.collaborationStartTime) / 1000;
        const timeRemaining = Math.max(0, session.settings.collaborationTime - elapsed);
        return {
            active: timeRemaining > 0,
            timeRemaining: Math.ceil(timeRemaining)
        };
    }
    /**
     * Finalize team game and get results
     */
    finalizeTeamGame(gameId) {
        const session = this.activeGames.get(gameId);
        if (!session) {
            return {
                finalTeams: [],
                finalScores: [],
                gameStats: {},
                achievements: new Map()
            };
        }
        // Calculate final scores and stats
        const finalScores = session.scoreHistory.flat();
        const gameStats = this.calculateGameStats(session);
        // Clean up
        this.cleanupGame(gameId);
        return {
            finalTeams: session.teams,
            finalScores,
            gameStats,
            achievements: session.achievements
        };
    }
    /**
     * Clean up game session
     */
    cleanupGame(gameId) {
        this.clearQuestionTimers(gameId);
        this.activeGames.delete(gameId);
        team_game_modes_1.teamGameModeManager.cleanupGame(gameId);
    }
    /**
     * Private helper methods
     */
    startQuestionTimer(gameId, timeLimit) {
        const timer = setTimeout(() => {
            this.handleQuestionTimeout(gameId);
        }, timeLimit * 1000);
        this.questionTimers.set(gameId, timer);
    }
    startCollaborationTimer(gameId, collaborationTime) {
        const timer = setTimeout(() => {
            this.handleCollaborationTimeout(gameId);
        }, collaborationTime * 1000);
        this.collaborationTimers.set(gameId, timer);
    }
    clearQuestionTimers(gameId) {
        const questionTimer = this.questionTimers.get(gameId);
        if (questionTimer) {
            clearTimeout(questionTimer);
            this.questionTimers.delete(gameId);
        }
        const collaborationTimer = this.collaborationTimers.get(gameId);
        if (collaborationTimer) {
            clearTimeout(collaborationTimer);
            this.collaborationTimers.delete(gameId);
        }
    }
    handleQuestionTimeout(gameId) {
        // Handle when question time runs out
        const session = this.activeGames.get(gameId);
        if (session) {
            // Auto-submit null answers for teams that haven't answered
            // This would be handled by the socket layer
        }
    }
    handleCollaborationTimeout(gameId) {
        // Handle when collaboration time runs out
        const session = this.activeGames.get(gameId);
        if (session) {
            delete session.collaborationStartTime;
        }
    }
    checkAllTeamsAnswered(session) {
        const activeTeams = session.teams.filter(team => team.players.length > 0);
        return activeTeams.every(team => team.hasAnswered);
    }
    checkTeamAchievements(session, scoreResults) {
        const achievements = [];
        // Check for perfect team achievement
        const perfectTeams = scoreResults.filter(result => {
            var _a, _b;
            return result.breakdown.accuracyBonus > 0 &&
                ((_a = session.teams.find(t => t.id === result.teamId)) === null || _a === void 0 ? void 0 : _a.players.length) ===
                    ((_b = session.teams.find(t => t.id === result.teamId)) === null || _b === void 0 ? void 0 : _b.players.length);
        });
        if (perfectTeams.length > 0) {
            achievements.push('perfect-team');
        }
        // Check for speed achievements
        const speedAchievements = scoreResults.filter(result => result.breakdown.speedBonus > 30);
        if (speedAchievements.length > 0) {
            achievements.push('speed-demons');
        }
        // Store achievements per team
        perfectTeams.forEach(result => {
            if (!session.achievements.has(result.teamId)) {
                session.achievements.set(result.teamId, []);
            }
            session.achievements.get(result.teamId).push('perfect-team');
        });
        return achievements;
    }
    calculateGameStats(session) {
        const totalQuestions = session.currentQuestion + 1;
        const totalTime = Date.now() - session.questionStartTime;
        const avgResponseTime = session.teamAnswers.size > 0
            ? Array.from(session.teamAnswers.values())
                .reduce((sum, answer) => sum + (answer.timestamp - session.questionStartTime), 0) / session.teamAnswers.size
            : 0;
        return {
            totalQuestions,
            totalTime,
            avgResponseTime,
            totalTeams: session.teams.length,
            totalPlayers: session.teams.reduce((sum, team) => sum + team.players.length, 0),
            chatMessages: session.teamChatMessages.length
        };
    }
}
exports.TeamGameIntegration = TeamGameIntegration;
exports.teamGameIntegration = new TeamGameIntegration();
