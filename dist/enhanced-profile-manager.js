"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedProfileManager = void 0;
var EnhancedProfileManager = /** @class */ (function () {
    function EnhancedProfileManager() {
    }
    EnhancedProfileManager.getInstance = function () {
        if (!Enhanced) {
            Enhanced = new EnhancedProfileManager();
        }
        return Enhanced;
    };
    /**
     * Get current user profile from localStorage
     */
    EnhancedProfileManager.prototype.getCurrentProfile = function () {
        if (typeof window === 'undefined')
            return null;
        // Try ProfileManager format first
        try {
            var profileJson = localStorage.getItem('jukebox-profile');
            if (profileJson) {
                var profile = JSON.parse(profileJson);
                if (profile && profile.id && profile.name && profile.avatar) {
                    return this.normalizeProfile(profile);
                }
            }
        }
        catch (error) {
            console.error('Failed to get jukebox profile:', error);
        }
        // Try UserContext format
        try {
            var userJson = localStorage.getItem('user');
            if (userJson) {
                var user = JSON.parse(userJson);
                if (user && user.id) {
                    return this.normalizeProfile({
                        id: user.id,
                        name: user.username || user.displayName || 'User',
                        displayName: user.displayName,
                        avatar: user.avatar || '👤',
                        role: user.role,
                        email: user.email,
                        username: user.username
                    });
                }
            }
        }
        catch (error) {
            console.error('Failed to get user context profile:', error);
        }
        return null;
    };
    /**
     * Save profile to localStorage (both formats for compatibility)
     */
    EnhancedProfileManager.prototype.saveProfile = function (profile) {
        if (typeof window === 'undefined')
            return;
        try {
            // Save in ProfileManager format
            var profileManagerFormat = {
                id: profile.id,
                name: profile.name,
                displayName: profile.displayName || profile.name,
                avatar: profile.avatar,
                soundPreference: profile.soundPreference || 'default',
                isGuest: profile.isGuest || false
            };
            // Save in UserContext format
            var userContextFormat = {
                id: profile.id,
                username: profile.username || profile.name,
                displayName: profile.displayName || profile.name,
                email: profile.email || "".concat(profile.id, "@local.user"),
                avatar: profile.avatar,
                role: profile.role || 'user',
                level: 1,
                xp: 0,
                xpToNext: 100,
                joinDate: new Date().toLocaleDateString(),
                stats: {
                    totalGames: 0,
                    winRate: 0,
                    averageScore: 0,
                    bestStreak: 0,
                    hoursPlayed: 0,
                    achievements: 0,
                    rank: 0,
                    favoriteMode: 'Classic Quiz'
                },
                achievements: [],
                playlists: [],
                settings: {
                    emailNotifications: true,
                    soundEffects: true,
                    autoPlay: true,
                    showHints: true,
                    publicProfile: true,
                    shareStats: false
                }
            };
            console.log('Profile saved to localStorage:', profile.displayName || profile.name);
        }
        catch (error) {
            console.error('Failed to save profile:', error);
        }
    };
    /**
     * Sync profile to database via API
     */
    EnhancedProfileManager.prototype.syncToDatabase = function (profile) {
        return __awaiter(this, void 0, void 0, function () {
            var existingUsersResponse, existingUser, data, response, _a, _b, _c, response, data, _d, _e, _f, error_1;
            return __generator(this, function (_g) {
                switch (_g.label) {
                    case 0:
                        _g.trys.push([0, 14, , 15]);
                        return [4 /*yield*/, fetch('/api/admin/roles')];
                    case 1:
                        existingUsersResponse = _g.sent();
                        existingUser = null;
                        if (!existingUsersResponse.ok) return [3 /*break*/, 3];
                        return [4 /*yield*/, existingUsersResponse.json()];
                    case 2:
                        data = _g.sent();
                        if (data.success && data.users) {
                            existingUser = data.users.find(function (u) {
                                return u.id === profile.id ||
                                    u.username === profile.username ||
                                    u.email === profile.email;
                            });
                        }
                        _g.label = 3;
                    case 3:
                        if (!existingUser) return [3 /*break*/, 8];
                        // Update existing user
                        console.log("[ProfileManager] Updating existing user: ".concat(existingUser.username));
                        return [4 /*yield*/, fetch('/api/admin/roles', {
                                method: 'PATCH',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    userId: existingUser.id,
                                    role: profile.role || 'user'
                                })
                            })];
                    case 4:
                        response = _g.sent();
                        if (!response.ok) return [3 /*break*/, 5];
                        console.log('Profile updated in database:', profile.displayName || profile.name);
                        return [2 /*return*/, true];
                    case 5:
                        _b = (_a = console).error;
                        _c = ['Failed to update profile in database:'];
                        return [4 /*yield*/, response.text()];
                    case 6:
                        _b.apply(_a, _c.concat([_g.sent()]));
                        return [2 /*return*/, false];
                    case 7: return [3 /*break*/, 13];
                    case 8:
                        // Create new user
                        console.log("[ProfileManager] Creating new user: ".concat(profile.username || profile.name));
                        return [4 /*yield*/, fetch('/api/admin/roles', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    displayName: profile.displayName || profile.name,
                                    username: profile.username || profile.name || "user_".concat(profile.id),
                                    email: profile.email || "".concat(profile.id, "@local.user"),
                                    role: profile.role || 'user',
                                    adminUserId: 'system'
                                })
                            })];
                    case 9:
                        response = _g.sent();
                        if (!response.ok) return [3 /*break*/, 11];
                        return [4 /*yield*/, response.json()];
                    case 10:
                        data = _g.sent();
                        if (data.success && data.user) {
                            // Update profile with database ID
                            profile.id = data.user.id;
                            this.saveProfile(profile);
                        }
                        console.log('Profile synced to database:', profile.displayName || profile.name);
                        return [2 /*return*/, true];
                    case 11:
                        _e = (_d = console).error;
                        _f = ['Failed to sync profile to database:'];
                        return [4 /*yield*/, response.text()];
                    case 12:
                        _e.apply(_d, _f.concat([_g.sent()]));
                        return [2 /*return*/, false];
                    case 13: return [3 /*break*/, 15];
                    case 14:
                        error_1 = _g.sent();
                        console.error('Error syncing profile to database:', error_1);
                        return [2 /*return*/, false];
                    case 15: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Create a guest profile
     */
    EnhancedProfileManager.prototype.createGuestProfile = function () {
        var guestProfile = {
            id: "guest-".concat(Date.now(), "-").concat(Math.random().toString(36).substring(2, 9)),
            name: 'Guest',
            displayName: 'Guest',
            avatar: '👤',
            soundPreference: 'default',
            isGuest: true,
            role: 'user',
            createdAt: new Date()
        };
        this.saveProfile(guestProfile);
        return guestProfile;
    };
    /**
     * Update user role
     */
    EnhancedProfileManager.prototype.updateUserRole = function (userId, newRole) {
        return __awaiter(this, void 0, void 0, function () {
            var response, currentProfile, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, fetch('/api/admin/roles', {
                                method: 'PATCH',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    userId: userId,
                                    role: newRole
                                })
                            })];
                    case 1:
                        response = _a.sent();
                        if (response.ok) {
                            currentProfile = this.getCurrentProfile();
                            if (currentProfile && currentProfile.id === userId) {
                                currentProfile.role = newRole;
                                this.saveProfile(currentProfile);
                            }
                            return [2 /*return*/, true];
                        }
                        return [2 /*return*/, false];
                    case 2:
                        error_2 = _a.sent();
                        console.error('Failed to update user role:', error_2);
                        return [2 /*return*/, false];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get all users from database
     */
    EnhancedProfileManager.prototype.getAllUsers = function () {
        return __awaiter(this, void 0, void 0, function () {
            var response, data, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, fetch('/api/admin/roles')];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        if (data.success) {
                            return [2 /*return*/, data.users];
                        }
                        return [2 /*return*/, []];
                    case 3:
                        error_3 = _a.sent();
                        console.error('Failed to get users:', error_3);
                        return [2 /*return*/, []];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Normalize profile format
     */
    EnhancedProfileManager.prototype.normalizeProfile = function (profile) {
        return {
            id: profile.id,
            name: profile.name || profile.username || 'User',
            displayName: profile.displayName || profile.name || profile.username,
            avatar: profile.avatar || '👤',
            soundPreference: profile.soundPreference || 'default',
            isGuest: profile.isGuest || false,
            role: profile.role || 'user',
            email: profile.email,
            username: profile.username || profile.name,
            createdAt: profile.createdAt ? new Date(profile.createdAt) : new Date(),
            lastActive: profile.lastActive ? new Date(profile.lastActive) : new Date()
        };
    };
    /**
     * Reload user role from database and sync to localStorage
     */
    EnhancedProfileManager.prototype.reloadUserRole = function (userId) {
        return __awaiter(this, void 0, void 0, function () {
            var response, data, currentProfile_1, targetUserId_1, dbUser, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, fetch('/api/admin/roles')];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        if (data.success && data.users) {
                            currentProfile_1 = this.getCurrentProfile();
                            if (!currentProfile_1 && !userId)
                                return [2 /*return*/, null];
                            targetUserId_1 = userId || (currentProfile_1 === null || currentProfile_1 === void 0 ? void 0 : currentProfile_1.id);
                            dbUser = data.users.find(function (u) {
                                return u.id === targetUserId_1 ||
                                    u.username === (currentProfile_1 === null || currentProfile_1 === void 0 ? void 0 : currentProfile_1.username) ||
                                    u.email === (currentProfile_1 === null || currentProfile_1 === void 0 ? void 0 : currentProfile_1.email);
                            });
                            if (dbUser && currentProfile_1) {
                                // Update profile with database role
                                currentProfile_1.role = dbUser.role;
                                this.saveProfile(currentProfile_1);
                                console.log("[ProfileManager] Role reloaded from database: ".concat(dbUser.role));
                                return [2 /*return*/, dbUser.role];
                            }
                        }
                        return [2 /*return*/, null];
                    case 3:
                        error_4 = _a.sent();
                        console.error('Failed to reload user role:', error_4);
                        return [2 /*return*/, null];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Initialize profile system
     */
    EnhancedProfileManager.prototype.initializeProfile = function () {
        return __awaiter(this, void 0, void 0, function () {
            var profile;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        profile = this.getCurrentProfile();
                        if (!profile) {
                            // Create guest profile if none exists
                            profile = this.createGuestProfile();
                        }
                        // Sync to database
                        return [4 /*yield*/, this.syncToDatabase(profile)];
                    case 1:
                        // Sync to database
                        _a.sent();
                        return [2 /*return*/, profile];
                }
            });
        });
    };
    return EnhancedProfileManager;
}());
exports.EnhancedProfileManager = EnhancedProfileManager;
