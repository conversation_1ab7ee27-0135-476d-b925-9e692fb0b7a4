"use strict";
/**
 * Team Game Modes Implementation
 * Handles different team gameplay mechanics: Collaborative, Relay, and Specialist
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.teamGameModeManager = exports.TeamGameModeManager = void 0;
class TeamGameModeManager {
    constructor() {
        this.gameStates = new Map();
    }
    /**
     * Initialize team game mode for a game
     */
    initializeTeamGame(gameId, teams, settings) {
        const gameState = {
            currentMode: settings.teamGameMode,
            collaborationPhase: false,
            relaySequence: [],
            specialistAssignments: new Map(),
            teamReadyStates: new Map()
        };
        // Initialize mode-specific state
        switch (settings.teamGameMode) {
            case 'relay':
                this.initializeRelayMode(gameState, teams);
                break;
            case 'specialist':
                this.initializeSpecialistMode(gameState, teams);
                break;
            case 'collaborative':
                this.initializeCollaborativeMode(gameState, teams);
                break;
        }
        this.gameStates.set(gameId, gameState);
        return gameState;
    }
    /**
     * Initialize Collaborative Mode
     * Teams discuss together and submit one answer per team
     */
    initializeCollaborativeMode(gameState, teams) {
        gameState.collaborationPhase = true;
        teams.forEach(team => {
            gameState.teamReadyStates.set(team.id, false);
        });
    }
    /**
     * Initialize Relay Mode
     * Team members take turns answering questions in sequence
     */
    initializeRelayMode(gameState, teams) {
        // Set up relay sequence for each team
        teams.forEach(team => {
            const shuffledPlayers = [...team.players].sort(() => Math.random() - 0.5);
            team.players.forEach((player, index) => {
                gameState.relaySequence.push(player.id);
            });
        });
        // Start with first player of first team
        if (gameState.relaySequence.length > 0) {
            gameState.currentTeamTurn = gameState.relaySequence[0];
        }
    }
    /**
     * Initialize Specialist Mode
     * Each team member specializes in different categories
     */
    initializeSpecialistMode(gameState, teams) {
        const specialties = ['decade', 'genre', 'artist', 'chart-position', 'year'];
        teams.forEach(team => {
            team.players.forEach((player, index) => {
                const specialty = specialties[index % specialties.length];
                gameState.specialistAssignments.set(player.id, specialty);
            });
        });
    }
    /**
     * Get current team turn information
     */
    getCurrentTurnInfo(gameId, questionCategory) {
        const gameState = this.gameStates.get(gameId);
        if (!gameState)
            return [];
        const turnInfos = [];
        switch (gameState.currentMode) {
            case 'collaborative':
                // All teams can participate simultaneously
                Array.from(gameState.teamReadyStates.keys()).forEach(teamId => {
                    turnInfos.push({
                        teamId,
                        timeRemaining: 0, // Would be set by game timer
                        canAnswer: !gameState.teamReadyStates.get(teamId),
                        waitingFor: []
                    });
                });
                break;
            case 'relay':
                // Only current relay player can answer
                if (gameState.currentTeamTurn) {
                    turnInfos.push({
                        teamId: gameState.currentTeamTurn,
                        activePlayerId: gameState.currentTeamTurn,
                        timeRemaining: 0,
                        canAnswer: true,
                        waitingFor: []
                    });
                }
                break;
            case 'specialist':
                // Only specialists for current category can answer
                if (questionCategory) {
                    Array.from(gameState.specialistAssignments.entries()).forEach(([playerId, specialty]) => {
                        if (specialty === questionCategory) {
                            turnInfos.push({
                                teamId: playerId, // Would need team lookup
                                activePlayerId: playerId,
                                timeRemaining: 0,
                                canAnswer: true,
                                waitingFor: []
                            });
                        }
                    });
                }
                break;
        }
        return turnInfos;
    }
    /**
     * Handle team answer submission based on game mode
     */
    handleTeamAnswer(gameId, teamAnswer, teams) {
        const gameState = this.gameStates.get(gameId);
        if (!gameState) {
            return { accepted: false, reason: 'Game state not found', phaseComplete: false };
        }
        switch (gameState.currentMode) {
            case 'collaborative':
                return this.handleCollaborativeAnswer(gameState, teamAnswer);
            case 'relay':
                return this.handleRelayAnswer(gameState, teamAnswer, teams);
            case 'specialist':
                return this.handleSpecialistAnswer(gameState, teamAnswer);
            default:
                return { accepted: false, reason: 'Unknown game mode', phaseComplete: false };
        }
    }
    /**
     * Handle collaborative mode answer
     */
    handleCollaborativeAnswer(gameState, teamAnswer) {
        // Mark team as ready
        gameState.teamReadyStates.set(teamAnswer.teamId, true);
        // Check if all teams have answered
        const allReady = Array.from(gameState.teamReadyStates.values()).every(ready => ready);
        return {
            accepted: true,
            reason: 'Team answer recorded',
            phaseComplete: allReady
        };
    }
    /**
     * Handle relay mode answer
     */
    handleRelayAnswer(gameState, teamAnswer, teams) {
        // Check if it's the correct player's turn
        const currentPlayerId = gameState.currentTeamTurn;
        if (teamAnswer.submittedBy !== currentPlayerId) {
            return {
                accepted: false,
                reason: 'Not your turn in relay',
                phaseComplete: false
            };
        }
        // Move to next player in relay
        const currentIndex = gameState.relaySequence.indexOf(currentPlayerId);
        const nextIndex = (currentIndex + 1) % gameState.relaySequence.length;
        gameState.currentTeamTurn = gameState.relaySequence[nextIndex];
        // Check if round is complete (all players have answered)
        const totalPlayers = teams.reduce((sum, team) => sum + team.players.length, 0);
        const phaseComplete = currentIndex === totalPlayers - 1;
        return {
            accepted: true,
            reason: 'Relay answer recorded',
            nextTurn: gameState.currentTeamTurn,
            phaseComplete
        };
    }
    /**
     * Handle specialist mode answer
     */
    handleSpecialistAnswer(gameState, teamAnswer) {
        // Verify the player is the specialist for this category
        const specialty = gameState.specialistAssignments.get(teamAnswer.submittedBy);
        return {
            accepted: true,
            reason: `Specialist (${specialty}) answer recorded`,
            phaseComplete: true // Each specialist answers once per question
        };
    }
    /**
     * Start collaboration phase for collaborative mode
     */
    startCollaborationPhase(gameId, duration) {
        const gameState = this.gameStates.get(gameId);
        if (!gameState || gameState.currentMode !== 'collaborative')
            return false;
        gameState.collaborationPhase = true;
        // Reset team ready states
        gameState.teamReadyStates.forEach((_, teamId) => {
            gameState.teamReadyStates.set(teamId, false);
        });
        // Set timer for collaboration phase
        setTimeout(() => {
            gameState.collaborationPhase = false;
        }, duration * 1000);
        return true;
    }
    /**
     * Get specialist assignments for display
     */
    getSpecialistAssignments(gameId) {
        const gameState = this.gameStates.get(gameId);
        return (gameState === null || gameState === void 0 ? void 0 : gameState.specialistAssignments) || new Map();
    }
    /**
     * Get relay sequence for display
     */
    getRelaySequence(gameId) {
        const gameState = this.gameStates.get(gameId);
        return (gameState === null || gameState === void 0 ? void 0 : gameState.relaySequence) || [];
    }
    /**
     * Check if player can answer in current mode
     */
    canPlayerAnswer(gameId, playerId, questionCategory) {
        const gameState = this.gameStates.get(gameId);
        if (!gameState) {
            return { canAnswer: false, reason: 'Game not found' };
        }
        switch (gameState.currentMode) {
            case 'collaborative':
                // Any team member can suggest answers during collaboration
                return { canAnswer: gameState.collaborationPhase, reason: 'Collaboration phase' };
            case 'relay':
                // Only current relay player can answer
                const isCurrentTurn = gameState.currentTeamTurn === playerId;
                return {
                    canAnswer: isCurrentTurn,
                    reason: isCurrentTurn ? 'Your turn' : 'Wait for your turn'
                };
            case 'specialist':
                // Only specialists for current category
                const playerSpecialty = gameState.specialistAssignments.get(playerId);
                const isSpecialist = playerSpecialty === questionCategory;
                return {
                    canAnswer: isSpecialist,
                    reason: isSpecialist ? `Your specialty: ${playerSpecialty}` : `Not your specialty (${playerSpecialty})`
                };
            default:
                return { canAnswer: false, reason: 'Unknown mode' };
        }
    }
    /**
     * Reset game state for new question
     */
    resetForNewQuestion(gameId) {
        const gameState = this.gameStates.get(gameId);
        if (!gameState)
            return;
        switch (gameState.currentMode) {
            case 'collaborative':
                gameState.collaborationPhase = true;
                gameState.teamReadyStates.forEach((_, teamId) => {
                    gameState.teamReadyStates.set(teamId, false);
                });
                break;
            case 'relay':
                // Relay continues with next player
                break;
            case 'specialist':
                // Specialist assignments remain the same
                break;
        }
    }
    /**
     * Get game mode description for UI
     */
    getModeDescription(mode) {
        switch (mode) {
            case 'collaborative':
                return {
                    name: 'Collaborative',
                    description: 'Teams discuss together and submit one answer',
                    icon: 'MessageCircle',
                    features: [
                        'Team chat for strategy',
                        'Discussion time before answers',
                        'Captain submits final answer',
                        'Team consensus encouraged'
                    ]
                };
            case 'relay':
                return {
                    name: 'Relay',
                    description: 'Team members take turns answering questions',
                    icon: 'Zap',
                    features: [
                        'Players answer in sequence',
                        'Individual time pressure',
                        'Team coordination required',
                        'Fair participation for all'
                    ]
                };
            case 'specialist':
                return {
                    name: 'Specialist',
                    description: 'Each member specializes in different categories',
                    icon: 'Target',
                    features: [
                        'Category-based expertise',
                        'Specialized knowledge focus',
                        'Strategic team composition',
                        'Domain expert advantages'
                    ]
                };
            default:
                return {
                    name: 'Unknown',
                    description: '',
                    icon: 'Help',
                    features: []
                };
        }
    }
    /**
     * Cleanup game state
     */
    cleanupGame(gameId) {
        this.gameStates.delete(gameId);
    }
}
exports.TeamGameModeManager = TeamGameModeManager;
exports.teamGameModeManager = new TeamGameModeManager();
