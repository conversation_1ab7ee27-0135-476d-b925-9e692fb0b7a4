/**
 * Voting Manager
 * Handles voting sessions, vote tracking, and result calculation
 */
export class VotingManager {
    constructor() {
        this.votingSessions = new Map();
        this.sessionVotes = new Map();
        this.sessionTimers = new Map();
    }
    /**
     * Create a new voting session
     */
    createVotingSession(sessionId, type, title, description, options, timeLimit = 30, totalPlayers, hostId) {
        const session = {
            id: sessionId,
            type,
            title,
            description,
            options,
            timeLimit,
            totalPlayers,
            hostId,
            status: 'active',
            votes: [],
            result: null,
            createdAt: Date.now(),
            expiresAt: Date.now() + (timeLimit * 1000)
        };
        this.votingSessions.set(sessionId, session);
        this.sessionVotes.set(sessionId, []);
        // Set auto-complete timer
        const timer = setTimeout(() => {
            this.completeVoting(sessionId);
        }, timeLimit * 1000);
        this.sessionTimers.set(sessionId, timer);
        return session;
    }
    /**
     * Submit a vote for a player
     */
    submitVote(sessionId, playerId, playerName, optionIndex) {
        const session = this.votingSessions.get(sessionId);
        if (!session || session.status !== 'active') {
            return false;
        }
        if (optionIndex < 0 || optionIndex >= session.options.length) {
            return false;
        }
        const votes = this.sessionVotes.get(sessionId) || [];
        // Check if player already voted
        const existingVoteIndex = votes.findIndex(vote => vote.playerId === playerId);
        const vote = {
            playerId,
            playerName,
            optionIndex,
            timestamp: Date.now()
        };
        if (existingVoteIndex >= 0) {
            // Update existing vote
            votes[existingVoteIndex] = vote;
        }
        else {
            // Add new vote
            votes.push(vote);
        }
        this.sessionVotes.set(sessionId, votes);
        // Update session votes
        session.votes = votes;
        // Check if all players have voted
        if (votes.length >= session.totalPlayers) {
            this.completeVoting(sessionId);
        }
        return true;
    }
    /**
     * Get current voting session state
     */
    getVotingSession(sessionId) {
        const session = this.votingSessions.get(sessionId);
        if (!session)
            return null;
        // Update votes from current state
        const votes = this.sessionVotes.get(sessionId) || [];
        return {
            ...session,
            votes
        };
    }
    /**
     * Get player's vote for a session
     */
    getPlayerVote(sessionId, playerId) {
        const votes = this.sessionVotes.get(sessionId) || [];
        const playerVote = votes.find(vote => vote.playerId === playerId);
        return playerVote ? playerVote.optionIndex : null;
    }
    /**
     * Complete voting session and calculate results
     */
    completeVoting(sessionId) {
        const session = this.votingSessions.get(sessionId);
        if (!session)
            return null;
        // Clear timer if it exists
        const timer = this.sessionTimers.get(sessionId);
        if (timer) {
            clearTimeout(timer);
            this.sessionTimers.delete(sessionId);
        }
        const votes = this.sessionVotes.get(sessionId) || [];
        const result = this.calculateResult(session.options, votes);
        // Update session
        session.status = 'completed';
        session.result = result;
        session.votes = votes;
        return result;
    }
    /**
     * Skip voting (host only)
     */
    skipVoting(sessionId, hostId) {
        const session = this.votingSessions.get(sessionId);
        if (!session || session.hostId !== hostId) {
            return null;
        }
        return this.completeVoting(sessionId);
    }
    /**
     * Calculate voting results
     */
    calculateResult(options, votes) {
        const voteCounts = new Array(options.length).fill(0);
        const voteDetails = {};
        // Initialize vote details
        options.forEach((_, index) => {
            voteDetails[index] = [];
        });
        // Count votes
        votes.forEach(vote => {
            if (vote.optionIndex >= 0 && vote.optionIndex < options.length) {
                voteCounts[vote.optionIndex]++;
                voteDetails[vote.optionIndex].push(vote);
            }
        });
        // Find winner(s)
        const maxVotes = Math.max(...voteCounts);
        const winningIndices = voteCounts
            .map((count, index) => ({ count, index }))
            .filter(item => item.count === maxVotes)
            .map(item => item.index);
        // Handle ties - pick first option in case of tie
        const winnerIndex = winningIndices[0];
        const isTie = winningIndices.length > 1;
        const result = {
            winnerIndex,
            winnerOption: options[winnerIndex],
            totalVotes: votes.length,
            voteCounts,
            voteDetails,
            isTie,
            tiedOptions: isTie ? winningIndices : undefined
        };
        return result;
    }
    /**
     * Get voting statistics
     */
    getVotingStats(sessionId) {
        const session = this.getVotingSession(sessionId);
        if (!session)
            return null;
        const votes = session.votes;
        const totalVotes = votes.length;
        const voteCounts = new Array(session.options.length).fill(0);
        votes.forEach(vote => {
            if (vote.optionIndex >= 0 && vote.optionIndex < session.options.length) {
                voteCounts[vote.optionIndex]++;
            }
        });
        const votePercentages = voteCounts.map(count => totalVotes > 0 ? (count / totalVotes) * 100 : 0);
        const maxVotes = Math.max(...voteCounts);
        const topOptionIndex = voteCounts.indexOf(maxVotes);
        return {
            totalVotes,
            votePercentages,
            topOption: {
                index: topOptionIndex,
                votes: maxVotes,
                percentage: totalVotes > 0 ? (maxVotes / totalVotes) * 100 : 0
            }
        };
    }
    /**
     * Get active voting sessions
     */
    getActiveSessions() {
        return Array.from(this.votingSessions.values())
            .filter(session => session.status === 'active');
    }
    /**
     * Clean up expired sessions
     */
    cleanupExpiredSessions() {
        const now = Date.now();
        const expiredSessions = [];
        this.votingSessions.forEach((session, sessionId) => {
            if (session.expiresAt < now && session.status === 'active') {
                this.completeVoting(sessionId);
                expiredSessions.push(sessionId);
            }
        });
    }
    /**
     * Remove voting session
     */
    removeSession(sessionId) {
        // Clear timer
        const timer = this.sessionTimers.get(sessionId);
        if (timer) {
            clearTimeout(timer);
            this.sessionTimers.delete(sessionId);
        }
        this.votingSessions.delete(sessionId);
        this.sessionVotes.delete(sessionId);
    }
    /**
     * Get time remaining for a voting session
     */
    getTimeRemaining(sessionId) {
        const session = this.votingSessions.get(sessionId);
        if (!session || session.status !== 'active')
            return 0;
        const remaining = Math.max(0, session.expiresAt - Date.now());
        return Math.ceil(remaining / 1000);
    }
    /**
     * Check if player has voted
     */
    hasPlayerVoted(sessionId, playerId) {
        const votes = this.sessionVotes.get(sessionId) || [];
        return votes.some(vote => vote.playerId === playerId);
    }
    /**
     * Get vote participation rate
     */
    getParticipationRate(sessionId) {
        const session = this.votingSessions.get(sessionId);
        if (!session)
            return 0;
        const votes = this.sessionVotes.get(sessionId) || [];
        return session.totalPlayers > 0 ? (votes.length / session.totalPlayers) * 100 : 0;
    }
    /**
     * Create predefined voting options for common scenarios
     */
    static createCategoryVotingOptions() {
        return [
            { label: 'Rock & Metal', value: 'rock', description: 'Hard rock, metal, punk', emoji: '🎸' },
            { label: 'Pop & Dance', value: 'pop', description: 'Pop hits, dance, electronic', emoji: '🎵' },
            { label: 'Hip Hop & R&B', value: 'hiphop', description: 'Rap, hip hop, R&B', emoji: '🎤' },
            { label: 'Classic & Jazz', value: 'classic', description: 'Classical, jazz, blues', emoji: '🎼' }
        ];
    }
    static createDecadeVotingOptions() {
        return [
            { label: '80s Hits', value: '80s', description: 'Music from the 1980s', emoji: '📻' },
            { label: '90s Classics', value: '90s', description: 'Music from the 1990s', emoji: '💿' },
            { label: '2000s Pop', value: '2000s', description: 'Music from the 2000s', emoji: '💽' },
            { label: '2010s & Beyond', value: '2010s', description: 'Modern hits', emoji: '📱' }
        ];
    }
    static createGameModeVotingOptions() {
        return [
            { label: 'Classic Quiz', value: 'classic', description: 'Traditional music quiz', emoji: '🎯' },
            { label: 'Speed Round', value: 'speed', description: 'Fast-paced questions', emoji: '⚡' },
            { label: 'Challenge Mode', value: 'challenge', description: 'Harder questions', emoji: '🏆' },
            { label: 'Collaborative', value: 'collaborative', description: 'Team-based play', emoji: '🤝' }
        ];
    }
}
export const votingManager = new VotingManager();
