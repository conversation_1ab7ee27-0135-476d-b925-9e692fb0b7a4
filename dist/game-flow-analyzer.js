"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameFlowAnalyzer = void 0;
exports.analyzeGameMode = analyzeGameMode;
var playwright_utils_1 = require("./playwright-utils");
var GameFlowAnalyzer = /** @class */ (function () {
    function GameFlowAnalyzer(config) {
        this.currentRound = null;
        this.rounds = [];
        this.gameStartTime = 0;
        this.pages = [];
        this.automation = new playwright_utils_1.MusicQuizAutomation({ headless: false });
        this.config = config;
    }
    GameFlowAnalyzer.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.automation.initialize()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.close = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _i, _a, page;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _i = 0, _a = this.pages;
                        _b.label = 1;
                    case 1:
                        if (!(_i < _a.length)) return [3 /*break*/, 4];
                        page = _a[_i];
                        return [4 /*yield*/, page.close()];
                    case 2:
                        _b.sent();
                        _b.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4: return [4 /*yield*/, this.automation.close()];
                    case 5:
                        _b.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.analyzeGameFlow = function () {
        return __awaiter(this, arguments, void 0, function (playerCount) {
            var analysis, validationResults, error_1;
            if (playerCount === void 0) { playerCount = 2; }
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        // 1. Setup game with configured settings
                        return [4 /*yield*/, this.setupGame(playerCount)];
                    case 1:
                        // 1. Setup game with configured settings
                        _a.sent();
                        return [4 /*yield*/, this.monitorGame()];
                    case 2:
                        analysis = _a.sent();
                        validationResults = this.validateGameBehavior(analysis);
                        return [2 /*return*/, __assign(__assign({}, analysis), { validationResults: validationResults })];
                    case 3:
                        error_1 = _a.sent();
                        console.error('Game flow analysis error:', error_1);
                        throw error_1;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.setupGame = function (playerCount) {
        return __awaiter(this, void 0, void 0, function () {
            var hostPage, multiplayerLink, currentUrl, hostGameButton, hostButtonSelectors, _i, hostButtonSelectors_1, selector, errorMessage, errorText, connectingMessage, e_1, nameInputSelectors, playerNameInput, _a, nameInputSelectors_1, selector, roomCode, roomCodeElement, gameModeSelect, roundsInput, secondsInput, _b, _c, category, categoryCheckbox, _d, _e, _f, key, value, selector, element, i, playerPage, multiplayerBtn, joinGameButton, playerNameInput_1, roomCodeInput, joinButton;
            return __generator(this, function (_g) {
                switch (_g.label) {
                    case 0: return [4 /*yield*/, this.automation.createPage()];
                    case 1:
                        hostPage = _g.sent();
                        this.pages.push(hostPage);
                        return [4 /*yield*/, hostPage.goto('http://localhost:3000')];
                    case 2:
                        _g.sent();
                        // Handle onboarding screen if present
                        return [4 /*yield*/, this.handleOnboarding(hostPage)];
                    case 3:
                        // Handle onboarding screen if present
                        _g.sent();
                        return [4 /*yield*/, hostPage.locator('a[href="/multiplayer"]').first()];
                    case 4:
                        multiplayerLink = _g.sent();
                        return [4 /*yield*/, multiplayerLink.count()];
                    case 5:
                        if (!((_g.sent()) > 0)) return [3 /*break*/, 9];
                        console.log('Found multiplayer link, clicking...');
                        return [4 /*yield*/, multiplayerLink.click()];
                    case 6:
                        _g.sent();
                        // Wait for navigation to complete
                        return [4 /*yield*/, hostPage.waitForURL('**/multiplayer', { timeout: 10000 })];
                    case 7:
                        // Wait for navigation to complete
                        _g.sent();
                        return [4 /*yield*/, hostPage.waitForLoadState('networkidle')];
                    case 8:
                        _g.sent();
                        console.log('Navigated to multiplayer page');
                        return [3 /*break*/, 12];
                    case 9:
                        // Try direct navigation if link not found
                        console.log('Multiplayer link not found, trying direct navigation...');
                        return [4 /*yield*/, hostPage.goto('http://localhost:3000/multiplayer')];
                    case 10:
                        _g.sent();
                        return [4 /*yield*/, hostPage.waitForLoadState('networkidle')];
                    case 11:
                        _g.sent();
                        _g.label = 12;
                    case 12:
                        currentUrl = hostPage.url();
                        console.log("Current URL: ".concat(currentUrl));
                        // Debug: Take a screenshot to see what's on the page
                        return [4 /*yield*/, hostPage.screenshot({ path: 'debug-multiplayer-page.png' })];
                    case 13:
                        // Debug: Take a screenshot to see what's on the page
                        _g.sent();
                        console.log('Screenshot saved as debug-multiplayer-page.png');
                        hostGameButton = null;
                        hostButtonSelectors = [
                            'button:has-text("Host Game")',
                            'button:has(svg.lucide-tv)',
                            'button.bg-gradient-to-r.from-blue-500.to-purple-600',
                            'div.grid button:first-child',
                            'text=Host Game'
                        ];
                        _i = 0, hostButtonSelectors_1 = hostButtonSelectors;
                        _g.label = 14;
                    case 14:
                        if (!(_i < hostButtonSelectors_1.length)) return [3 /*break*/, 17];
                        selector = hostButtonSelectors_1[_i];
                        console.log("Trying selector: ".concat(selector));
                        return [4 /*yield*/, hostPage.$(selector)];
                    case 15:
                        hostGameButton = _g.sent();
                        if (hostGameButton) {
                            console.log("Found host button with selector: ".concat(selector));
                            return [3 /*break*/, 17];
                        }
                        _g.label = 16;
                    case 16:
                        _i++;
                        return [3 /*break*/, 14];
                    case 17:
                        if (!!hostGameButton) return [3 /*break*/, 23];
                        console.log('Host button not found immediately, waiting...');
                        return [4 /*yield*/, hostPage.waitForTimeout(3000)];
                    case 18:
                        _g.sent();
                        return [4 /*yield*/, hostPage.$('text=Error, text=error, .error-message')];
                    case 19:
                        errorMessage = _g.sent();
                        if (!errorMessage) return [3 /*break*/, 21];
                        return [4 /*yield*/, errorMessage.textContent()];
                    case 20:
                        errorText = _g.sent();
                        console.error("Error on page: ".concat(errorText));
                        _g.label = 21;
                    case 21: return [4 /*yield*/, hostPage.$('button:has-text("Host Game")')];
                    case 22:
                        // Try again
                        hostGameButton = _g.sent();
                        _g.label = 23;
                    case 23:
                        if (!hostGameButton) return [3 /*break*/, 26];
                        console.log('Selecting host role...');
                        return [4 /*yield*/, hostGameButton.click()];
                    case 24:
                        _g.sent();
                        return [4 /*yield*/, hostPage.waitForTimeout(1000)];
                    case 25:
                        _g.sent();
                        return [3 /*break*/, 33];
                    case 26:
                        console.error('Could not find Host Game button!');
                        // Try direct navigation as fallback
                        console.log('Trying direct URL navigation to multiplayer...');
                        return [4 /*yield*/, hostPage.goto('http://localhost:3000/multiplayer')];
                    case 27:
                        _g.sent();
                        return [4 /*yield*/, hostPage.waitForLoadState('networkidle')];
                    case 28:
                        _g.sent();
                        return [4 /*yield*/, hostPage.waitForTimeout(2000)];
                    case 29:
                        _g.sent();
                        return [4 /*yield*/, hostPage.$('button:has-text("Host Game")')];
                    case 30:
                        // Try finding button again
                        hostGameButton = _g.sent();
                        if (!hostGameButton) return [3 /*break*/, 33];
                        return [4 /*yield*/, hostGameButton.click()];
                    case 31:
                        _g.sent();
                        return [4 /*yield*/, hostPage.waitForTimeout(1000)];
                    case 32:
                        _g.sent();
                        _g.label = 33;
                    case 33: return [4 /*yield*/, hostPage.locator('text=Connecting to game server').count()];
                    case 34:
                        connectingMessage = _g.sent();
                        if (!(connectingMessage > 0)) return [3 /*break*/, 38];
                        console.log('Waiting for WebSocket connection...');
                        _g.label = 35;
                    case 35:
                        _g.trys.push([35, 37, , 38]);
                        return [4 /*yield*/, hostPage.waitForSelector('text=Connecting to game server', { state: 'hidden', timeout: 15000 })];
                    case 36:
                        _g.sent();
                        console.log('WebSocket connected');
                        return [3 /*break*/, 38];
                    case 37:
                        e_1 = _g.sent();
                        console.error('WebSocket connection timeout - is the socket server running?');
                        console.error('Run: npm run socket-server');
                        throw new Error('WebSocket connection failed');
                    case 38:
                        nameInputSelectors = [
                            'input#host-name',
                            'input[placeholder*="name"]',
                            'input[placeholder*="Name"]'
                        ];
                        playerNameInput = null;
                        _a = 0, nameInputSelectors_1 = nameInputSelectors;
                        _g.label = 39;
                    case 39:
                        if (!(_a < nameInputSelectors_1.length)) return [3 /*break*/, 42];
                        selector = nameInputSelectors_1[_a];
                        return [4 /*yield*/, hostPage.$(selector)];
                    case 40:
                        playerNameInput = _g.sent();
                        if (playerNameInput) {
                            console.log("Found name input with selector: ".concat(selector));
                            return [3 /*break*/, 42];
                        }
                        _g.label = 41;
                    case 41:
                        _a++;
                        return [3 /*break*/, 39];
                    case 42:
                        if (!playerNameInput) return [3 /*break*/, 45];
                        console.log('Setting player name...');
                        return [4 /*yield*/, playerNameInput.fill('Host')];
                    case 43:
                        _g.sent();
                        // The game auto-creates when name is entered due to useEffect
                        return [4 /*yield*/, hostPage.waitForTimeout(1000)];
                    case 44:
                        // The game auto-creates when name is entered due to useEffect
                        _g.sent();
                        return [3 /*break*/, 46];
                    case 45:
                        console.log('No name input found - user might be logged in');
                        _g.label = 46;
                    case 46:
                        // Wait for game to be created (room code should appear)
                        console.log('Waiting for game creation...');
                        // Wait for the game to be auto-created (happens automatically in lobby)
                        console.log('Waiting for game to be created...');
                        return [4 /*yield*/, hostPage.waitForSelector('[data-testid="room-code"], .room-code, text=/[A-Z0-9]{4,6}/', { timeout: 10000 })];
                    case 47:
                        _g.sent();
                        return [4 /*yield*/, hostPage.textContent('[data-testid="room-code"], .room-code')];
                    case 48:
                        roomCode = _g.sent();
                        if (!!roomCode) return [3 /*break*/, 51];
                        return [4 /*yield*/, hostPage.locator('text=/[A-Z0-9]{4,6}/').first()];
                    case 49:
                        roomCodeElement = _g.sent();
                        return [4 /*yield*/, roomCodeElement.textContent()];
                    case 50:
                        roomCode = _g.sent();
                        _g.label = 51;
                    case 51:
                        console.log("Room code: ".concat(roomCode));
                        return [4 /*yield*/, hostPage.$('select[name="gameMode"], [data-testid="game-mode"]')];
                    case 52:
                        gameModeSelect = _g.sent();
                        if (!gameModeSelect) return [3 /*break*/, 55];
                        return [4 /*yield*/, gameModeSelect.selectOption(this.config.mode)];
                    case 53:
                        _g.sent();
                        return [4 /*yield*/, hostPage.waitForTimeout(500)];
                    case 54:
                        _g.sent();
                        _g.label = 55;
                    case 55: return [4 /*yield*/, hostPage.$('input[name="rounds"], [data-testid="rounds-per-game"]')];
                    case 56:
                        roundsInput = _g.sent();
                        if (!roundsInput) return [3 /*break*/, 59];
                        return [4 /*yield*/, roundsInput.fill('')];
                    case 57:
                        _g.sent();
                        return [4 /*yield*/, roundsInput.fill(this.config.roundsPerGame.toString())];
                    case 58:
                        _g.sent();
                        _g.label = 59;
                    case 59: return [4 /*yield*/, hostPage.$('input[name="secondsPerRound"], [data-testid="seconds-per-round"]')];
                    case 60:
                        secondsInput = _g.sent();
                        if (!secondsInput) return [3 /*break*/, 63];
                        return [4 /*yield*/, secondsInput.fill('')];
                    case 61:
                        _g.sent();
                        return [4 /*yield*/, secondsInput.fill(this.config.secondsPerRound.toString())];
                    case 62:
                        _g.sent();
                        _g.label = 63;
                    case 63:
                        if (!(this.config.categories && this.config.categories.length > 0)) return [3 /*break*/, 68];
                        _b = 0, _c = this.config.categories;
                        _g.label = 64;
                    case 64:
                        if (!(_b < _c.length)) return [3 /*break*/, 68];
                        category = _c[_b];
                        return [4 /*yield*/, hostPage.$("input[value=\"".concat(category, "\"], [data-testid=\"category-").concat(category, "\"]"))];
                    case 65:
                        categoryCheckbox = _g.sent();
                        if (!categoryCheckbox) return [3 /*break*/, 67];
                        return [4 /*yield*/, categoryCheckbox.check()];
                    case 66:
                        _g.sent();
                        _g.label = 67;
                    case 67:
                        _b++;
                        return [3 /*break*/, 64];
                    case 68:
                        if (!(this.config.mode === 'ultimote' && this.config.customSettings)) return [3 /*break*/, 78];
                        _d = 0, _e = Object.entries(this.config.customSettings);
                        _g.label = 69;
                    case 69:
                        if (!(_d < _e.length)) return [3 /*break*/, 78];
                        _f = _e[_d], key = _f[0], value = _f[1];
                        selector = "[data-testid=\"setting-".concat(key, "\"], input[name=\"").concat(key, "\"]");
                        return [4 /*yield*/, hostPage.$(selector)];
                    case 70:
                        element = _g.sent();
                        if (!element) return [3 /*break*/, 77];
                        if (!(typeof value === 'boolean')) return [3 /*break*/, 75];
                        if (!value) return [3 /*break*/, 72];
                        return [4 /*yield*/, element.check()];
                    case 71:
                        _g.sent();
                        return [3 /*break*/, 74];
                    case 72: return [4 /*yield*/, element.uncheck()];
                    case 73:
                        _g.sent();
                        _g.label = 74;
                    case 74: return [3 /*break*/, 77];
                    case 75: return [4 /*yield*/, element.fill(value.toString())];
                    case 76:
                        _g.sent();
                        _g.label = 77;
                    case 77:
                        _d++;
                        return [3 /*break*/, 69];
                    case 78:
                        i = 1;
                        _g.label = 79;
                    case 79:
                        if (!(i < playerCount)) return [3 /*break*/, 105];
                        return [4 /*yield*/, this.automation.createPage()];
                    case 80:
                        playerPage = _g.sent();
                        this.pages.push(playerPage);
                        return [4 /*yield*/, playerPage.goto('http://localhost:3000')];
                    case 81:
                        _g.sent();
                        // Handle onboarding screen if present
                        return [4 /*yield*/, this.handleOnboarding(playerPage)];
                    case 82:
                        // Handle onboarding screen if present
                        _g.sent();
                        return [4 /*yield*/, playerPage.$('a[href="/multiplayer"], button:has-text("Multiplayer")')];
                    case 83:
                        multiplayerBtn = _g.sent();
                        if (!multiplayerBtn) return [3 /*break*/, 86];
                        return [4 /*yield*/, multiplayerBtn.click()];
                    case 84:
                        _g.sent();
                        return [4 /*yield*/, playerPage.waitForLoadState('networkidle')];
                    case 85:
                        _g.sent();
                        _g.label = 86;
                    case 86: return [4 /*yield*/, playerPage.$('button:has-text("Join Game")')];
                    case 87:
                        joinGameButton = _g.sent();
                        if (!joinGameButton) return [3 /*break*/, 90];
                        return [4 /*yield*/, joinGameButton.click()];
                    case 88:
                        _g.sent();
                        return [4 /*yield*/, playerPage.waitForTimeout(1000)];
                    case 89:
                        _g.sent();
                        _g.label = 90;
                    case 90: return [4 /*yield*/, playerPage.$('input[placeholder*="name"], input[placeholder*="Name"], input#player-name')];
                    case 91:
                        playerNameInput_1 = _g.sent();
                        if (!playerNameInput_1) return [3 /*break*/, 95];
                        return [4 /*yield*/, playerNameInput_1.fill("Player".concat(i))];
                    case 92:
                        _g.sent();
                        return [4 /*yield*/, playerPage.keyboard.press('Enter')];
                    case 93:
                        _g.sent();
                        return [4 /*yield*/, playerPage.waitForTimeout(500)];
                    case 94:
                        _g.sent();
                        _g.label = 95;
                    case 95: return [4 /*yield*/, playerPage.$('input[placeholder*="code"], input[placeholder*="Code"], input#room-code')];
                    case 96:
                        roomCodeInput = _g.sent();
                        if (!roomCodeInput) return [3 /*break*/, 99];
                        return [4 /*yield*/, roomCodeInput.fill(roomCode)];
                    case 97:
                        _g.sent();
                        return [4 /*yield*/, playerPage.waitForTimeout(500)];
                    case 98:
                        _g.sent();
                        _g.label = 99;
                    case 99: return [4 /*yield*/, playerPage.$('button:has-text("Join"), button:has-text("Enter Room")')];
                    case 100:
                        joinButton = _g.sent();
                        if (!joinButton) return [3 /*break*/, 102];
                        return [4 /*yield*/, joinButton.click()];
                    case 101:
                        _g.sent();
                        _g.label = 102;
                    case 102: 
                    // Wait for player to be in lobby
                    return [4 /*yield*/, playerPage.waitForSelector('[data-testid="game-lobby"], .game-lobby, text=Lobby', { timeout: 10000 })];
                    case 103:
                        // Wait for player to be in lobby
                        _g.sent();
                        _g.label = 104;
                    case 104:
                        i++;
                        return [3 /*break*/, 79];
                    case 105: 
                    // Wait for all players to be ready
                    return [4 /*yield*/, hostPage.waitForTimeout(2000)];
                    case 106:
                        // Wait for all players to be ready
                        _g.sent();
                        // Start the game
                        return [4 /*yield*/, hostPage.click('[data-testid="start-game"]')];
                    case 107:
                        // Start the game
                        _g.sent();
                        this.gameStartTime = Date.now();
                        return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.handleOnboarding = function (page) {
        return __awaiter(this, void 0, void 0, function () {
            var continueAsGuestButton, guestModeButton, welcomeText, welcomeHeading, playerNameInput, attempts, maxAttempts, skipButton, nextButton, finishButton, startPlayingButton, allSetButton, finalButton, nameInput, nextAfterName, error_2, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 56, , 63]);
                        return [4 /*yield*/, page.$('button:has-text("Continue as Guest")')];
                    case 1:
                        continueAsGuestButton = _b.sent();
                        return [4 /*yield*/, page.$('button:has-text("Guest Mode")')];
                    case 2:
                        guestModeButton = _b.sent();
                        if (!continueAsGuestButton) return [3 /*break*/, 5];
                        console.log('Clicking "Continue as Guest" button...');
                        return [4 /*yield*/, continueAsGuestButton.click()];
                    case 3:
                        _b.sent();
                        return [4 /*yield*/, page.waitForTimeout(1000)];
                    case 4:
                        _b.sent();
                        return [3 /*break*/, 8];
                    case 5:
                        if (!guestModeButton) return [3 /*break*/, 8];
                        console.log('Clicking "Guest Mode" button...');
                        return [4 /*yield*/, guestModeButton.click()];
                    case 6:
                        _b.sent();
                        return [4 /*yield*/, page.waitForTimeout(1000)];
                    case 7:
                        _b.sent();
                        _b.label = 8;
                    case 8: return [4 /*yield*/, page.locator('text=Welcome to Music Quiz').count()];
                    case 9:
                        welcomeText = _b.sent();
                        return [4 /*yield*/, page.locator('h1:has-text("Welcome"), h2:has-text("Welcome")').count()];
                    case 10:
                        welcomeHeading = _b.sent();
                        if (!(welcomeText === 0 && welcomeHeading === 0)) return [3 /*break*/, 12];
                        return [4 /*yield*/, page.$('input[placeholder*="name"], input[placeholder*="Name"]')];
                    case 11:
                        playerNameInput = _b.sent();
                        if (playerNameInput) {
                            console.log('Already past onboarding');
                            return [2 /*return*/];
                        }
                        _b.label = 12;
                    case 12:
                        console.log('Onboarding screen detected, handling it...');
                        attempts = 0;
                        maxAttempts = 10;
                        _b.label = 13;
                    case 13:
                        if (!(attempts < maxAttempts)) return [3 /*break*/, 53];
                        return [4 /*yield*/, page.waitForTimeout(500)];
                    case 14:
                        _b.sent(); // Small delay to let page render
                        return [4 /*yield*/, page.locator('button:has-text("Skip")').first()];
                    case 15:
                        skipButton = _b.sent();
                        return [4 /*yield*/, skipButton.count()];
                    case 16:
                        if (!((_b.sent()) > 0)) return [3 /*break*/, 19];
                        console.log('Found Skip button, clicking it...');
                        return [4 /*yield*/, skipButton.click()];
                    case 17:
                        _b.sent();
                        return [4 /*yield*/, page.waitForTimeout(1000)];
                    case 18:
                        _b.sent();
                        return [3 /*break*/, 53];
                    case 19: return [4 /*yield*/, page.locator('button:has-text("Next"):visible').first()];
                    case 20:
                        nextButton = _b.sent();
                        return [4 /*yield*/, page.locator('button:has-text("Finish"):visible').first()];
                    case 21:
                        finishButton = _b.sent();
                        return [4 /*yield*/, page.locator('button:has-text("Start Playing"):visible').first()];
                    case 22:
                        startPlayingButton = _b.sent();
                        return [4 /*yield*/, page.locator('text=All Set').first()];
                    case 23:
                        allSetButton = _b.sent();
                        return [4 /*yield*/, nextButton.count()];
                    case 24:
                        if (!((_b.sent()) > 0)) return [3 /*break*/, 27];
                        console.log('Clicking Next button...');
                        return [4 /*yield*/, nextButton.click()];
                    case 25:
                        _b.sent();
                        return [4 /*yield*/, page.waitForTimeout(500)];
                    case 26:
                        _b.sent();
                        return [3 /*break*/, 52];
                    case 27: return [4 /*yield*/, finishButton.count()];
                    case 28:
                        if (!((_b.sent()) > 0)) return [3 /*break*/, 31];
                        console.log('Clicking Finish button...');
                        return [4 /*yield*/, finishButton.click()];
                    case 29:
                        _b.sent();
                        return [4 /*yield*/, page.waitForTimeout(500)];
                    case 30:
                        _b.sent();
                        return [3 /*break*/, 53];
                    case 31: return [4 /*yield*/, startPlayingButton.count()];
                    case 32:
                        if (!((_b.sent()) > 0)) return [3 /*break*/, 35];
                        console.log('Clicking Start Playing button...');
                        return [4 /*yield*/, startPlayingButton.click()];
                    case 33:
                        _b.sent();
                        return [4 /*yield*/, page.waitForTimeout(500)];
                    case 34:
                        _b.sent();
                        return [3 /*break*/, 53];
                    case 35: return [4 /*yield*/, allSetButton.count()];
                    case 36:
                        if (!((_b.sent()) > 0)) return [3 /*break*/, 42];
                        // We might be on the last step
                        console.log('Found "All Set" text, looking for final button...');
                        return [4 /*yield*/, page.locator('button:visible').last()];
                    case 37:
                        finalButton = _b.sent();
                        return [4 /*yield*/, finalButton.count()];
                    case 38:
                        if (!((_b.sent()) > 0)) return [3 /*break*/, 41];
                        return [4 /*yield*/, finalButton.click()];
                    case 39:
                        _b.sent();
                        return [4 /*yield*/, page.waitForTimeout(500)];
                    case 40:
                        _b.sent();
                        _b.label = 41;
                    case 41: return [3 /*break*/, 53];
                    case 42: return [4 /*yield*/, page.locator('input[placeholder*="name"], input[placeholder*="Name"]').first()];
                    case 43:
                        nameInput = _b.sent();
                        return [4 /*yield*/, nameInput.count()];
                    case 44:
                        if (!((_b.sent()) > 0)) return [3 /*break*/, 51];
                        console.log('Found name input, filling it...');
                        return [4 /*yield*/, nameInput.fill('TestPlayer')];
                    case 45:
                        _b.sent();
                        return [4 /*yield*/, page.locator('button:has-text("Next"):visible').first()];
                    case 46:
                        nextAfterName = _b.sent();
                        return [4 /*yield*/, nextAfterName.count()];
                    case 47:
                        if (!((_b.sent()) > 0)) return [3 /*break*/, 50];
                        return [4 /*yield*/, nextAfterName.click()];
                    case 48:
                        _b.sent();
                        return [4 /*yield*/, page.waitForTimeout(500)];
                    case 49:
                        _b.sent();
                        _b.label = 50;
                    case 50: return [3 /*break*/, 52];
                    case 51:
                        // No more buttons found, we might be done
                        console.log('No more onboarding buttons found');
                        return [3 /*break*/, 53];
                    case 52:
                        attempts++;
                        return [3 /*break*/, 13];
                    case 53: 
                    // Wait a bit for any transitions
                    return [4 /*yield*/, page.waitForTimeout(1000)];
                    case 54:
                        // Wait a bit for any transitions
                        _b.sent();
                        // Set localStorage to skip onboarding in future runs
                        return [4 /*yield*/, page.evaluate(function () {
                                localStorage.setItem('music-quiz-onboarding-complete', 'true');
                                localStorage.setItem('tour-completed-landing-tour', 'true');
                            })];
                    case 55:
                        // Set localStorage to skip onboarding in future runs
                        _b.sent();
                        console.log('Onboarding handling completed');
                        return [3 /*break*/, 63];
                    case 56:
                        error_2 = _b.sent();
                        console.error('Error handling onboarding:', error_2);
                        _b.label = 57;
                    case 57:
                        _b.trys.push([57, 61, , 62]);
                        return [4 /*yield*/, page.evaluate(function () {
                                localStorage.setItem('music-quiz-onboarding-complete', 'true');
                                localStorage.setItem('tour-completed-landing-tour', 'true');
                            })];
                    case 58:
                        _b.sent();
                        return [4 /*yield*/, page.reload()];
                    case 59:
                        _b.sent();
                        return [4 /*yield*/, page.waitForTimeout(2000)];
                    case 60:
                        _b.sent();
                        return [3 /*break*/, 62];
                    case 61:
                        _a = _b.sent();
                        return [3 /*break*/, 62];
                    case 62: return [3 /*break*/, 63];
                    case 63: return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.monitorGame = function () {
        return __awaiter(this, void 0, void 0, function () {
            var categoryDistribution, questionTypes, playerPerformance, roundNumber, gameEnded, i, playerId, roundData, _i, _a, question, questionType, _b, _c, _d, playerId, answer, perf, responseTime, totalGameDuration, actualRounds, totalSeconds, averageSecondsPerRound, expectedRoundDuration, roundTimingVariances, roundTimingVariance, expectedTotalDuration, totalTimingAccuracy, _e, playerPerformance_1, _f, playerId, perf, finalScore, totalResponseTime, responseCount, _g, _h, round, _j, _k, question, answer;
            return __generator(this, function (_l) {
                switch (_l.label) {
                    case 0:
                        categoryDistribution = new Map();
                        questionTypes = new Map();
                        playerPerformance = new Map();
                        roundNumber = 0;
                        gameEnded = false;
                        // Initialize player performance tracking
                        for (i = 0; i < this.pages.length; i++) {
                            playerId = i === 0 ? 'Host' : "Player".concat(i);
                            playerPerformance.set(playerId, {
                                playerId: playerId,
                                totalScore: 0,
                                correctAnswers: 0,
                                averageResponseTime: 0,
                                fastestResponse: Infinity,
                                slowestResponse: 0
                            });
                        }
                        _l.label = 1;
                    case 1:
                        if (!(!gameEnded && roundNumber < this.config.roundsPerGame)) return [3 /*break*/, 4];
                        roundNumber++;
                        return [4 /*yield*/, this.monitorRound(roundNumber)];
                    case 2:
                        roundData = _l.sent();
                        if (!roundData) {
                            gameEnded = true;
                            return [3 /*break*/, 4];
                        }
                        this.rounds.push(roundData);
                        // Analyze questions in this round
                        for (_i = 0, _a = roundData.questions; _i < _a.length; _i++) {
                            question = _a[_i];
                            // Track category distribution
                            if (question.category) {
                                categoryDistribution.set(question.category, (categoryDistribution.get(question.category) || 0) + 1);
                            }
                            questionType = this.identifyQuestionType(question.questionText);
                            questionTypes.set(questionType, (questionTypes.get(questionType) || 0) + 1);
                            // Update player performance
                            for (_b = 0, _c = question.playerAnswers; _b < _c.length; _b++) {
                                _d = _c[_b], playerId = _d[0], answer = _d[1];
                                perf = playerPerformance.get(playerId);
                                if (answer.answer === question.correctAnswer) {
                                    perf.correctAnswers++;
                                }
                                responseTime = answer.time - question.timeStarted;
                                perf.fastestResponse = Math.min(perf.fastestResponse, responseTime);
                                perf.slowestResponse = Math.max(perf.slowestResponse, responseTime);
                            }
                        }
                        return [4 /*yield*/, this.checkGameEnded()];
                    case 3:
                        // Check if game has ended
                        gameEnded = _l.sent();
                        return [3 /*break*/, 1];
                    case 4:
                        totalGameDuration = Date.now() - this.gameStartTime;
                        actualRounds = this.rounds.length;
                        totalSeconds = this.rounds.reduce(function (sum, round) { return sum + (round.duration || 0); }, 0);
                        averageSecondsPerRound = totalSeconds / actualRounds / 1000;
                        expectedRoundDuration = this.config.secondsPerRound * 1000;
                        roundTimingVariances = this.rounds.map(function (round) {
                            return Math.abs((round.duration || 0) - expectedRoundDuration);
                        });
                        roundTimingVariance = roundTimingVariances.reduce(function (a, b) { return a + b; }, 0) / roundTimingVariances.length;
                        expectedTotalDuration = this.config.roundsPerGame * this.config.secondsPerRound * 1000;
                        totalTimingAccuracy = 1 - Math.abs(totalGameDuration - expectedTotalDuration) / expectedTotalDuration;
                        _e = 0, playerPerformance_1 = playerPerformance;
                        _l.label = 5;
                    case 5:
                        if (!(_e < playerPerformance_1.length)) return [3 /*break*/, 8];
                        _f = playerPerformance_1[_e], playerId = _f[0], perf = _f[1];
                        return [4 /*yield*/, this.getPlayerScore(playerId)];
                    case 6:
                        finalScore = _l.sent();
                        perf.totalScore = finalScore;
                        totalResponseTime = 0;
                        responseCount = 0;
                        for (_g = 0, _h = this.rounds; _g < _h.length; _g++) {
                            round = _h[_g];
                            for (_j = 0, _k = round.questions; _j < _k.length; _j++) {
                                question = _k[_j];
                                answer = question.playerAnswers.get(playerId);
                                if (answer) {
                                    totalResponseTime += (answer.time - question.timeStarted);
                                    responseCount++;
                                }
                            }
                        }
                        perf.averageResponseTime = responseCount > 0 ? totalResponseTime / responseCount : 0;
                        _l.label = 7;
                    case 7:
                        _e++;
                        return [3 /*break*/, 5];
                    case 8: return [2 /*return*/, {
                            gameMode: this.config.mode,
                            configuredRounds: this.config.roundsPerGame,
                            actualRounds: actualRounds,
                            configuredSecondsPerRound: this.config.secondsPerRound,
                            averageSecondsPerRound: averageSecondsPerRound,
                            totalGameDuration: totalGameDuration,
                            rounds: this.rounds,
                            categoryDistribution: categoryDistribution,
                            questionTypes: questionTypes,
                            playerPerformance: playerPerformance,
                            timingAccuracy: {
                                roundTimingVariance: roundTimingVariance,
                                totalTimingAccuracy: totalTimingAccuracy
                            }
                        }];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.monitorRound = function (roundNumber) {
        return __awaiter(this, void 0, void 0, function () {
            var hostPage, _a, roundData, questionNumber, roundEnded, questionData, _b, i, playerId, score;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        hostPage = this.pages[0];
                        _c.label = 1;
                    case 1:
                        _c.trys.push([1, 3, , 4]);
                        // Wait for round to start
                        return [4 /*yield*/, hostPage.waitForSelector("[data-testid=\"round-".concat(roundNumber, "\"]"), { timeout: 10000 })];
                    case 2:
                        // Wait for round to start
                        _c.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        _a = _c.sent();
                        return [2 /*return*/, null]; // Game ended
                    case 4:
                        roundData = {
                            roundNumber: roundNumber,
                            startTime: Date.now(),
                            questions: [],
                            scores: new Map()
                        };
                        questionNumber = 0;
                        roundEnded = false;
                        _c.label = 5;
                    case 5:
                        if (!!roundEnded) return [3 /*break*/, 14];
                        questionNumber++;
                        _c.label = 6;
                    case 6:
                        _c.trys.push([6, 12, , 13]);
                        // Wait for question to appear
                        return [4 /*yield*/, hostPage.waitForSelector('[data-testid="question-text"]', { timeout: 5000 })];
                    case 7:
                        // Wait for question to appear
                        _c.sent();
                        return [4 /*yield*/, this.captureQuestionData(questionNumber)];
                    case 8:
                        questionData = _c.sent();
                        if (!questionData) return [3 /*break*/, 10];
                        roundData.questions.push(questionData);
                        // Wait for all players to answer or timeout
                        return [4 /*yield*/, this.waitForAnswers(questionData)];
                    case 9:
                        // Wait for all players to answer or timeout
                        _c.sent();
                        _c.label = 10;
                    case 10: return [4 /*yield*/, this.checkRoundEnded(roundNumber)];
                    case 11:
                        // Check if round has ended
                        roundEnded = _c.sent();
                        return [3 /*break*/, 13];
                    case 12:
                        _b = _c.sent();
                        roundEnded = true;
                        return [3 /*break*/, 13];
                    case 13: return [3 /*break*/, 5];
                    case 14:
                        roundData.endTime = Date.now();
                        roundData.duration = roundData.endTime - roundData.startTime;
                        i = 0;
                        _c.label = 15;
                    case 15:
                        if (!(i < this.pages.length)) return [3 /*break*/, 18];
                        playerId = i === 0 ? 'Host' : "Player".concat(i);
                        return [4 /*yield*/, this.getPlayerScore(playerId)];
                    case 16:
                        score = _c.sent();
                        roundData.scores.set(playerId, score);
                        _c.label = 17;
                    case 17:
                        i++;
                        return [3 /*break*/, 15];
                    case 18: return [2 /*return*/, roundData];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.captureQuestionData = function (questionNumber) {
        return __awaiter(this, void 0, void 0, function () {
            var hostPage, questionText, category, options, questionData, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        hostPage = this.pages[0];
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 6, , 7]);
                        return [4 /*yield*/, hostPage.textContent('[data-testid="question-text"]')];
                    case 2:
                        questionText = _a.sent();
                        if (!questionText)
                            return [2 /*return*/, null];
                        return [4 /*yield*/, hostPage.textContent('[data-testid="question-category"]').catch(function () { return null; })];
                    case 3:
                        category = _a.sent();
                        return [4 /*yield*/, hostPage.$$eval('[data-testid="answer-option"]', function (elements) { return elements.map(function (el) { var _a; return ((_a = el.textContent) === null || _a === void 0 ? void 0 : _a.trim()) || ''; }); })];
                    case 4:
                        options = _a.sent();
                        questionData = {
                            questionNumber: questionNumber,
                            questionText: questionText,
                            category: category || undefined,
                            options: options,
                            timeStarted: Date.now(),
                            playerAnswers: new Map()
                        };
                        // Monitor player answers
                        return [4 /*yield*/, this.monitorPlayerAnswers(questionData)];
                    case 5:
                        // Monitor player answers
                        _a.sent();
                        return [2 /*return*/, questionData];
                    case 6:
                        error_3 = _a.sent();
                        console.error('Error capturing question data:', error_3);
                        return [2 /*return*/, null];
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.monitorPlayerAnswers = function (questionData) {
        return __awaiter(this, void 0, void 0, function () {
            var answerPromises, correctAnswer, _a;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        answerPromises = this.pages.map(function (page, index) { return __awaiter(_this, void 0, void 0, function () {
                            var playerId, selectedAnswer, answerTime, _a;
                            return __generator(this, function (_b) {
                                switch (_b.label) {
                                    case 0:
                                        playerId = index === 0 ? 'Host' : "Player".concat(index);
                                        _b.label = 1;
                                    case 1:
                                        _b.trys.push([1, 4, , 5]);
                                        // Wait for player to select an answer
                                        return [4 /*yield*/, page.waitForSelector('[data-testid="answer-option"].selected', {
                                                timeout: this.config.secondsPerRound * 1000
                                            })];
                                    case 2:
                                        // Wait for player to select an answer
                                        _b.sent();
                                        return [4 /*yield*/, page.textContent('[data-testid="answer-option"].selected')];
                                    case 3:
                                        selectedAnswer = _b.sent();
                                        answerTime = Date.now();
                                        questionData.playerAnswers.set(playerId, {
                                            answer: selectedAnswer || '',
                                            time: answerTime
                                        });
                                        return [3 /*break*/, 5];
                                    case 4:
                                        _a = _b.sent();
                                        // Player didn't answer in time
                                        questionData.playerAnswers.set(playerId, {
                                            answer: '',
                                            time: questionData.timeStarted + (this.config.secondsPerRound * 1000)
                                        });
                                        return [3 /*break*/, 5];
                                    case 5: return [2 /*return*/];
                                }
                            });
                        }); });
                        return [4 /*yield*/, Promise.all(answerPromises)];
                    case 1:
                        _b.sent();
                        _b.label = 2;
                    case 2:
                        _b.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, this.pages[0].textContent('[data-testid="correct-answer"]')];
                    case 3:
                        correctAnswer = _b.sent();
                        questionData.correctAnswer = correctAnswer || undefined;
                        return [3 /*break*/, 5];
                    case 4:
                        _a = _b.sent();
                        return [3 /*break*/, 5];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.waitForAnswers = function (questionData) {
        return __awaiter(this, void 0, void 0, function () {
            var maxWaitTime, startTime, timerExpired;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        maxWaitTime = this.config.secondsPerRound * 1000;
                        startTime = Date.now();
                        _a.label = 1;
                    case 1:
                        if (!(Date.now() - startTime < maxWaitTime)) return [3 /*break*/, 4];
                        // Check if all players have answered
                        if (questionData.playerAnswers.size === this.pages.length) {
                            return [3 /*break*/, 4];
                        }
                        return [4 /*yield*/, this.pages[0].$('[data-testid="timer-expired"]')];
                    case 2:
                        timerExpired = _a.sent();
                        if (timerExpired) {
                            return [3 /*break*/, 4];
                        }
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                    case 3:
                        _a.sent();
                        return [3 /*break*/, 1];
                    case 4:
                        questionData.timeAnswered = Date.now();
                        questionData.responseTime = questionData.timeAnswered - questionData.timeStarted;
                        return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.checkRoundEnded = function (roundNumber) {
        return __awaiter(this, void 0, void 0, function () {
            var roundEndIndicator, nextRoundIndicator, gameEndIndicator, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 4, , 5]);
                        return [4 /*yield*/, this.pages[0].$('[data-testid="round-end"]')];
                    case 1:
                        roundEndIndicator = _b.sent();
                        return [4 /*yield*/, this.pages[0].$("[data-testid=\"round-".concat(roundNumber + 1, "\"]"))];
                    case 2:
                        nextRoundIndicator = _b.sent();
                        return [4 /*yield*/, this.pages[0].$('[data-testid="game-end"]')];
                    case 3:
                        gameEndIndicator = _b.sent();
                        return [2 /*return*/, !!(roundEndIndicator || nextRoundIndicator || gameEndIndicator)];
                    case 4:
                        _a = _b.sent();
                        return [2 /*return*/, true];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.checkGameEnded = function () {
        return __awaiter(this, void 0, void 0, function () {
            var gameEndIndicator, finalScoresIndicator, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, this.pages[0].$('[data-testid="game-end"]')];
                    case 1:
                        gameEndIndicator = _b.sent();
                        return [4 /*yield*/, this.pages[0].$('[data-testid="final-scores"]')];
                    case 2:
                        finalScoresIndicator = _b.sent();
                        return [2 /*return*/, !!(gameEndIndicator || finalScoresIndicator)];
                    case 3:
                        _a = _b.sent();
                        return [2 /*return*/, false];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.getPlayerScore = function (playerId) {
        return __awaiter(this, void 0, void 0, function () {
            var playerIndex, page, scoreText, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        playerIndex = playerId === 'Host' ? 0 : parseInt(playerId.replace('Player', ''));
                        page = this.pages[playerIndex];
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, page.textContent('[data-testid="player-score"]')];
                    case 2:
                        scoreText = _b.sent();
                        return [2 /*return*/, parseInt(scoreText || '0') || 0];
                    case 3:
                        _a = _b.sent();
                        return [2 /*return*/, 0];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    GameFlowAnalyzer.prototype.identifyQuestionType = function (questionText) {
        var text = questionText.toLowerCase();
        if (text.includes('who') || text.includes('artist') || text.includes('band')) {
            return 'artist';
        }
        else if (text.includes('year') || text.includes('when')) {
            return 'year';
        }
        else if (text.includes('album')) {
            return 'album';
        }
        else if (text.includes('genre') || text.includes('style')) {
            return 'genre';
        }
        else if (text.includes('lyrics') || text.includes('words')) {
            return 'lyrics';
        }
        else if (text.includes('chart') || text.includes('position')) {
            return 'chart';
        }
        else {
            return 'general';
        }
    };
    GameFlowAnalyzer.prototype.validateGameBehavior = function (analysis) {
        var _this = this;
        var results = [];
        // Validate number of rounds
        results.push({
            check: 'Number of rounds',
            expected: this.config.roundsPerGame,
            actual: analysis.actualRounds,
            passed: analysis.actualRounds === this.config.roundsPerGame,
            message: analysis.actualRounds !== this.config.roundsPerGame
                ? "Game ended with ".concat(analysis.actualRounds, " rounds instead of ").concat(this.config.roundsPerGame)
                : undefined
        });
        // Validate round timing
        var timingTolerance = 2; // 2 seconds tolerance
        var avgRoundTime = analysis.averageSecondsPerRound;
        var expectedTime = this.config.secondsPerRound;
        var timingPassed = Math.abs(avgRoundTime - expectedTime) <= timingTolerance;
        results.push({
            check: 'Average seconds per round',
            expected: expectedTime,
            actual: avgRoundTime,
            passed: timingPassed,
            message: !timingPassed
                ? "Average round time ".concat(avgRoundTime.toFixed(1), "s differs from configured ").concat(expectedTime, "s")
                : undefined
        });
        // Validate categories if specified
        if (this.config.categories && this.config.categories.length > 0) {
            var actualCategories = Array.from(analysis.categoryDistribution.keys());
            var unexpectedCategories = actualCategories.filter(function (cat) { return !_this.config.categories.includes(cat); });
            results.push({
                check: 'Category selection',
                expected: this.config.categories,
                actual: actualCategories,
                passed: unexpectedCategories.length === 0,
                message: unexpectedCategories.length > 0
                    ? "Found unexpected categories: ".concat(unexpectedCategories.join(', '))
                    : undefined
            });
        }
        // Validate game mode specific rules
        results.push.apply(results, this.validateGameModeRules(analysis));
        // Validate timing accuracy
        results.push({
            check: 'Overall timing accuracy',
            expected: 0.9, // 90% accuracy
            actual: analysis.timingAccuracy.totalTimingAccuracy,
            passed: analysis.timingAccuracy.totalTimingAccuracy >= 0.9,
            message: analysis.timingAccuracy.totalTimingAccuracy < 0.9
                ? "Timing accuracy ".concat((analysis.timingAccuracy.totalTimingAccuracy * 100).toFixed(1), "% is below 90%")
                : undefined
        });
        return results;
    };
    GameFlowAnalyzer.prototype.validateGameModeRules = function (analysis) {
        var results = [];
        switch (this.config.mode) {
            case 'quick-fire':
                // Quick-fire should have shorter question times
                var avgResponseTime = Array.from(analysis.playerPerformance.values())
                    .reduce(function (sum, perf) { return sum + perf.averageResponseTime; }, 0) / analysis.playerPerformance.size;
                results.push({
                    check: 'Quick-fire response time',
                    expected: '< 5000ms',
                    actual: "".concat(avgResponseTime.toFixed(0), "ms"),
                    passed: avgResponseTime < 5000,
                    message: avgResponseTime >= 5000
                        ? 'Quick-fire mode should have faster response times'
                        : undefined
                });
                break;
            case 'album-art':
                // Should only have album-related questions
                var nonAlbumQuestions = Array.from(analysis.questionTypes.entries())
                    .filter(function (_a) {
                    var type = _a[0];
                    return type !== 'album';
                })
                    .reduce(function (sum, _a) {
                    var count = _a[1];
                    return sum + count;
                }, 0);
                results.push({
                    check: 'Album art mode questions',
                    expected: 'All album questions',
                    actual: "".concat(nonAlbumQuestions, " non-album questions"),
                    passed: nonAlbumQuestions === 0,
                    message: nonAlbumQuestions > 0
                        ? 'Album art mode should only contain album-related questions'
                        : undefined
                });
                break;
            case 'decade-challenge':
                // Should have year-related questions
                var yearQuestions = analysis.questionTypes.get('year') || 0;
                var totalQuestions = Array.from(analysis.questionTypes.values())
                    .reduce(function (sum, count) { return sum + count; }, 0);
                var yearQuestionRatio = yearQuestions / totalQuestions;
                results.push({
                    check: 'Decade challenge year questions',
                    expected: '> 50%',
                    actual: "".concat((yearQuestionRatio * 100).toFixed(1), "%"),
                    passed: yearQuestionRatio > 0.5,
                    message: yearQuestionRatio <= 0.5
                        ? 'Decade challenge should have mostly year-related questions'
                        : undefined
                });
                break;
        }
        return results;
    };
    return GameFlowAnalyzer;
}());
exports.GameFlowAnalyzer = GameFlowAnalyzer;
// Export convenience function
function analyzeGameMode(config_1) {
    return __awaiter(this, arguments, void 0, function (config, playerCount) {
        var analyzer, analysis, _i, _a, result, status_1;
        if (playerCount === void 0) { playerCount = 2; }
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    analyzer = new GameFlowAnalyzer(config);
                    _b.label = 1;
                case 1:
                    _b.trys.push([1, , 4, 6]);
                    return [4 /*yield*/, analyzer.initialize()];
                case 2:
                    _b.sent();
                    return [4 /*yield*/, analyzer.analyzeGameFlow(playerCount)];
                case 3:
                    analysis = _b.sent();
                    // Log summary
                    console.log('\n=== Game Flow Analysis Summary ===');
                    console.log("Game Mode: ".concat(analysis.gameMode));
                    console.log("Rounds: ".concat(analysis.actualRounds, "/").concat(analysis.configuredRounds));
                    console.log("Avg Round Time: ".concat(analysis.averageSecondsPerRound.toFixed(1), "s (configured: ").concat(analysis.configuredSecondsPerRound, "s)"));
                    console.log("Total Duration: ".concat((analysis.totalGameDuration / 1000).toFixed(1), "s"));
                    console.log("\nValidation Results:");
                    for (_i = 0, _a = analysis.validationResults; _i < _a.length; _i++) {
                        result = _a[_i];
                        status_1 = result.passed ? '✓' : '✗';
                        console.log("".concat(status_1, " ").concat(result.check, ": ").concat(result.actual, " (expected: ").concat(result.expected, ")"));
                        if (result.message) {
                            console.log("  \u2192 ".concat(result.message));
                        }
                    }
                    return [2 /*return*/, analysis];
                case 4: return [4 /*yield*/, analyzer.close()];
                case 5:
                    _b.sent();
                    return [7 /*endfinally*/];
                case 6: return [2 /*return*/];
            }
        });
    });
}
