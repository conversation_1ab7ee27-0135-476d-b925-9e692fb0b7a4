{"version": 3, "file": "quiz-generator.js", "sourceRoot": "", "sources": ["../../lib/quiz-generator.ts"], "names": [], "mappings": ";;;AAgBA,MAAa,mBAAmB;IAAhC;QACU,mBAAc,GAAqB;YACzC;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,6CAA6C;gBACvD,OAAO,EAAE,CAAC,aAAa,EAAE,oBAAoB,EAAE,cAAc,EAAE,YAAY,CAAC;gBAC5E,aAAa,EAAE,aAAa;gBAC5B,QAAQ,EAAE,cAAc;gBACxB,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,2EAA2E;aACzF;YACD;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,oCAAoC;gBAC9C,OAAO,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,QAAQ,EAAE,mBAAmB,CAAC;gBAC5E,aAAa,EAAE,iBAAiB;gBAChC,QAAQ,EAAE,WAAW;gBACrB,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,oGAAoG;aAClH;YACD;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,+CAA+C;gBACzD,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;gBAC9C,aAAa,EAAE,OAAO;gBACtB,QAAQ,EAAE,iBAAiB;gBAC3B,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,0EAA0E;aACxF;YACD;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,wCAAwC;gBAClD,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;gBACzC,aAAa,EAAE,MAAM;gBACrB,QAAQ,EAAE,eAAe;gBACzB,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,uFAAuF;aACrG;YACD;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,wEAAwE;gBAClF,OAAO,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE,sBAAsB,CAAC;gBAClF,aAAa,EAAE,aAAa;gBAC5B,QAAQ,EAAE,WAAW;gBACrB,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,yFAAyF;aACvG;YACD;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,6CAA6C;gBACvD,OAAO,EAAE,CAAC,eAAe,EAAE,2BAA2B,EAAE,UAAU,EAAE,SAAS,CAAC;gBAC9E,aAAa,EAAE,UAAU;gBACzB,QAAQ,EAAE,eAAe;gBACzB,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,kFAAkF;aAChG;YACD;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,kDAAkD;gBAC5D,OAAO,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,CAAC;gBAC1D,aAAa,EAAE,aAAa;gBAC5B,QAAQ,EAAE,eAAe;gBACzB,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,0FAA0F;aACxG;YACD;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,wCAAwC;gBAClD,OAAO,EAAE,CAAC,sBAAsB,EAAE,yBAAyB,EAAE,uBAAuB,EAAE,gBAAgB,CAAC;gBACvG,aAAa,EAAE,yBAAyB;gBACxC,QAAQ,EAAE,iBAAiB;gBAC3B,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,sEAAsE;aACpF;YACD;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,+DAA+D;gBACzE,OAAO,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;gBACnD,aAAa,EAAE,SAAS;gBACxB,QAAQ,EAAE,eAAe;gBACzB,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,sGAAsG;aACpH;YACD;gBACE,EAAE,EAAE,KAAK;gBACT,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,2BAA2B;gBACrC,OAAO,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;gBAC5E,aAAa,EAAE,aAAa;gBAC5B,QAAQ,EAAE,mBAAmB;gBAC7B,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,iGAAiG;aAC/G;SACF,CAAA;IASH,CAAC;IAPC,KAAK,CAAC,YAAY,CAAC,QAAqC;QACtD,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,IAAI,EAAE,CAAA;QAGpD,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAA;QACzE,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAA;IAChF,CAAC;CACF;AA/GD,kDA+GC"}