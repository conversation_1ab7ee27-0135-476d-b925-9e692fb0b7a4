{"version": 3, "file": "environment-check.js", "sourceRoot": "", "sources": ["../../lib/environment-check.ts"], "names": [], "mappings": ";;AAgBA,kDAqDC;AAED,kDAkBC;AAED,gDAuCC;AAED,0DA2BC;AAGD,oCAcC;AAhKD,SAAgB,mBAAmB;IACjC,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;IAC9B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAG3C,IAAI,cAAc,GAAG,IAAI,CAAC;IAE1B,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE,CAAC;QACnC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACzC,cAAc,GAAG,KAAK,CAAC;IACzB,CAAC;SAAM,CAAC;QAEN,MAAM,eAAe,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAEtF,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;YACrC,IAAI,OAAQ,OAAe,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE,CAAC;gBACnD,MAAM,CAAC,IAAI,CAAC,WAAW,MAAM,gBAAgB,CAAC,CAAC;gBAC/C,cAAc,GAAG,KAAK,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAGD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAElC,MAAM,SAAS,GAAG,MAAa,CAAC;QAEhC,IAAI,SAAS,CAAC,cAAc,EAAE,CAAC;YAC7B,QAAQ,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;YACnB,QAAQ,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAGD,MAAM,IAAI,GAAG;QACX,SAAS,EAAE,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS;QAClF,WAAW,EAAE,OAAO,OAAO,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;QACzE,cAAc;QACd,SAAS;KACV,CAAC;IAEF,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;IAEpC,OAAO;QACL,OAAO;QACP,MAAM;QACN,QAAQ;QACR,IAAI;KACL,CAAC;AACJ,CAAC;AAED,SAAgB,mBAAmB;IACjC,MAAM,MAAM,GAAG,mBAAmB,EAAE,CAAC;IAErC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QAE5D,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE;YAChD,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,IAAI,EAAE,MAAM,CAAC,IAAI;SAClB,CAAC,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAgB,kBAAkB;IAChC,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,OAAO;IAG1C,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;QACzC,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,eAAe,CAAC;QAE9E,IAAI,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9E,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAC9C,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK;gBACzB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,SAAS;aACvC,CAAC,CAAC;YAGH,MAAM,UAAU,GAAG,mBAAmB,EAAE,CAAC;YACzC,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,UAAU,CAAC,CAAC;QAClE,CAAC;IACH,CAAC,CAAC,CAAC;IAGH,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,KAAK,EAAE,EAAE;QACtD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,MAAM,aAAa,GAAG,MAAM,EAAE,OAAO,IAAI,MAAM,EAAE,QAAQ,EAAE,IAAI,mBAAmB,CAAC;QAEnF,IAAI,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAChF,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBACjD,MAAM,EAAE,aAAa;gBACrB,KAAK,EAAE,MAAM,EAAE,KAAK;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;AACtE,CAAC;AAED,SAAgB,uBAAuB;IACrC,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,OAAO,IAAI,CAAC;IAE/C,IAAI,CAAC;QAEH,MAAM,KAAK,GAAI,MAAc,CAAC,KAAK,CAAC;QACpC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,EAAE,CAAC,CAAC;YAG5C,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAGD,SAAgB,YAAY;IAC1B,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAE5C,MAAM,SAAS,GAAG,mBAAmB,EAAE,CAAC;IACxC,MAAM,OAAO,GAAG,uBAAuB,EAAE,CAAC;IAE1C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE;QACzB,gBAAgB,EAAE,SAAS,CAAC,OAAO;QACnC,eAAe,EAAE,OAAO;QACxB,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,MAAM;QACpC,aAAa,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM;KACzC,CAAC,CAAC;IAEH,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrB,CAAC"}