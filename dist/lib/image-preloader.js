"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.preloadQueueAlbumArt = exports.preloadImages = exports.preloadImage = exports.imagePreloader = void 0;
class ImagePreloader {
    constructor() {
        this.preloadQueue = new Map();
        this.loadedImages = new Set();
        this.isProcessing = false;
        this.maxConcurrent = 3;
        this.activeLoads = 0;
        this.processQueue();
    }
    static getInstance() {
        if (!ImagePreloader.instance) {
            ImagePreloader.instance = new ImagePreloader();
        }
        return ImagePreloader.instance;
    }
    preload(url, priority = 'medium') {
        if (!url || this.loadedImages.has(url)) {
            return;
        }
        this.preloadQueue.set(url, {
            url,
            priority,
            timestamp: Date.now()
        });
        if (!this.isProcessing) {
            this.processQueue();
        }
    }
    preloadBatch(urls, priority = 'medium') {
        urls.forEach(url => this.preload(url, priority));
    }
    preloadQueueAlbumArt(queueItems) {
        const urls = [];
        queueItems.forEach((item, index) => {
            const priority = index < 3 ? 'high' : index < 10 ? 'medium' : 'low';
            if (item.albumArtUrl) {
                urls.push(item.albumArtUrl);
            }
            if (item.id) {
                urls.push(`/api/album-art/${item.id}/thumbnail.webp`);
                urls.push(`/api/album-art/${item.id}/cover.webp`);
            }
            if (item.fingerprint) {
                urls.push(`/api/images/album-art/${item.fingerprint}/thumbnail.webp`);
                urls.push(`/api/images/album-art/${item.fingerprint}/cover.webp`);
            }
        });
        const uniqueUrls = Array.from(new Set(urls));
        uniqueUrls.forEach((url, index) => {
            const priority = index < 5 ? 'high' : index < 15 ? 'medium' : 'low';
            this.preload(url, priority);
        });
    }
    clear() {
        this.preloadQueue.clear();
        this.loadedImages.clear();
    }
    async processQueue() {
        if (this.isProcessing)
            return;
        this.isProcessing = true;
        while (this.preloadQueue.size > 0 && this.activeLoads < this.maxConcurrent) {
            const nextItem = this.getNextItem();
            if (!nextItem)
                break;
            this.activeLoads++;
            this.loadImage(nextItem).finally(() => {
                this.activeLoads--;
            });
        }
        if (this.preloadQueue.size > 0) {
            setTimeout(() => {
                this.isProcessing = false;
                this.processQueue();
            }, 100);
        }
        else {
            this.isProcessing = false;
        }
    }
    getNextItem() {
        let highPriorityItem = null;
        let mediumPriorityItem = null;
        let lowPriorityItem = null;
        for (const item of this.preloadQueue.values()) {
            if (item.priority === 'high' && !highPriorityItem) {
                highPriorityItem = item;
                break;
            }
            else if (item.priority === 'medium' && !mediumPriorityItem) {
                mediumPriorityItem = item;
            }
            else if (item.priority === 'low' && !lowPriorityItem) {
                lowPriorityItem = item;
            }
        }
        const selectedItem = highPriorityItem || mediumPriorityItem || lowPriorityItem;
        if (selectedItem) {
            this.preloadQueue.delete(selectedItem.url);
        }
        return selectedItem;
    }
    async loadImage(item) {
        try {
            const img = new Image();
            await new Promise((resolve, reject) => {
                img.onload = () => {
                    this.loadedImages.add(item.url);
                    resolve();
                };
                img.onerror = () => {
                    console.warn(`Failed to preload image: ${item.url}`);
                    this.loadedImages.add(item.url);
                    resolve();
                };
                img.crossOrigin = 'anonymous';
                img.src = item.url;
            });
        }
        catch (error) {
            console.error('Error preloading image:', error);
        }
    }
    isLoaded(url) {
        return this.loadedImages.has(url);
    }
    getStats() {
        return {
            queueSize: this.preloadQueue.size,
            loadedCount: this.loadedImages.size,
            activeLoads: this.activeLoads
        };
    }
}
exports.imagePreloader = ImagePreloader.getInstance();
const preloadImage = (url, priority) => exports.imagePreloader.preload(url, priority);
exports.preloadImage = preloadImage;
const preloadImages = (urls, priority) => exports.imagePreloader.preloadBatch(urls, priority);
exports.preloadImages = preloadImages;
const preloadQueueAlbumArt = (queueItems) => exports.imagePreloader.preloadQueueAlbumArt(queueItems);
exports.preloadQueueAlbumArt = preloadQueueAlbumArt;
//# sourceMappingURL=image-preloader.js.map