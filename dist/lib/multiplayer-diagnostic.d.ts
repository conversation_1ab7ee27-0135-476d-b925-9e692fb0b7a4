export interface MultiplayerDiagnostic {
    timestamp: number;
    issue: string;
    details: any;
    severity: 'low' | 'medium' | 'high' | 'critical';
}
export declare class MultiplayerDiagnosticTool {
    private diagnostics;
    private readonly MAX_DIAGNOSTICS;
    record(issue: string, details?: any, severity?: MultiplayerDiagnostic['severity']): void;
    diagnoseMultiplayerState(state: {
        isConnected: boolean;
        gameCode: string | null;
        playerId: string | null;
        playerName: string | null;
        multiplayerCurrentQuestion: any;
        multiplayerQuestions: any[];
        quizState: string;
        quizSubState: string;
        timeRemaining: number;
    }): void;
    suggestFixes(): string[];
    generateReport(): {
        summary: string;
        criticalIssues: number;
        highIssues: number;
        suggestions: string[];
        recentDiagnostics: MultiplayerDiagnostic[];
    };
    clear(): void;
    autoReport(): void;
}
export declare const multiplayerDiagnostic: MultiplayerDiagnosticTool;
