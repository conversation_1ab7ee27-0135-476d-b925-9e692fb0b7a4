"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MPDRateLimiter = void 0;
class MPDRateLimiter {
    constructor() {
        this.clients = new Map();
        this.SERVER_MIN_INTERVAL_MS = 200;
        this.SERVER_MAX_REQUESTS_PER_MINUTE = 120;
        this.CLIENT_MIN_INTERVAL_MS = 1000;
        this.CLIENT_MAX_REQUESTS_PER_MINUTE = 40;
        this.WINDOW_SIZE_MS = 60000;
    }
    static getInstance() {
        if (!MPDRateLimiter.instance) {
            MPDRateLimiter.instance = new MPDRateLimiter();
        }
        return MPDRateLimiter.instance;
    }
    isServerRequest(clientId) {
        return clientId === 'server' ||
            clientId === '127.0.0.1' ||
            clientId === '::1' ||
            clientId.startsWith('server-') ||
            clientId.includes('localhost');
    }
    canMakeRequest(clientId) {
        const now = Date.now();
        const entry = this.clients.get(clientId);
        const isServer = this.isServerRequest(clientId);
        const minInterval = isServer ? this.SERVER_MIN_INTERVAL_MS : this.CLIENT_MIN_INTERVAL_MS;
        const maxRequests = isServer ? this.SERVER_MAX_REQUESTS_PER_MINUTE : this.CLIENT_MAX_REQUESTS_PER_MINUTE;
        if (!entry) {
            this.clients.set(clientId, {
                lastRequest: now,
                requestCount: 1,
                resetTime: now + this.WINDOW_SIZE_MS
            });
            return true;
        }
        if (now >= entry.resetTime) {
            entry.requestCount = 1;
            entry.lastRequest = now;
            entry.resetTime = now + this.WINDOW_SIZE_MS;
            return true;
        }
        if (now - entry.lastRequest < minInterval) {
            return false;
        }
        if (entry.requestCount >= maxRequests) {
            return false;
        }
        entry.lastRequest = now;
        entry.requestCount++;
        return true;
    }
    getTimeUntilNextRequest(clientId) {
        const now = Date.now();
        const entry = this.clients.get(clientId);
        const isServer = this.isServerRequest(clientId);
        const minInterval = isServer ? this.SERVER_MIN_INTERVAL_MS : this.CLIENT_MIN_INTERVAL_MS;
        const maxRequests = isServer ? this.SERVER_MAX_REQUESTS_PER_MINUTE : this.CLIENT_MAX_REQUESTS_PER_MINUTE;
        if (!entry) {
            return 0;
        }
        if (entry.requestCount >= maxRequests) {
            return Math.max(0, entry.resetTime - now);
        }
        return Math.max(0, minInterval - (now - entry.lastRequest));
    }
    cleanup() {
        const now = Date.now();
        const staleThreshold = now - this.WINDOW_SIZE_MS * 2;
        for (const [clientId, entry] of this.clients.entries()) {
            if (entry.lastRequest < staleThreshold) {
                this.clients.delete(clientId);
            }
        }
    }
}
exports.MPDRateLimiter = MPDRateLimiter;
if (typeof setInterval !== 'undefined') {
    setInterval(() => {
        MPDRateLimiter.getInstance().cleanup();
    }, 5 * 60 * 1000);
}
//# sourceMappingURL=mpd-rate-limiter.js.map