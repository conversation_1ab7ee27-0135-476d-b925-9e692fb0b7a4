{"version": 3, "file": "mpd-config.js", "sourceRoot": "", "sources": ["../../lib/mpd-config.ts"], "names": [], "mappings": ";;;AAGa,QAAA,UAAU,GAAc;IACnC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,WAAW;IACzC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,MAAM,CAAC;IAC9C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY;CAGnC,CAAA;AAGY,QAAA,YAAY,GAAG;IAE1B,oBAAoB,EAAE,CAAC;IACvB,cAAc,EAAE,IAAI;IAGpB,aAAa,EAAE,EAAE;IACjB,SAAS,EAAE,GAAG;IAGd,YAAY,EAAE,IAAI;IAGlB,WAAW,EAAE,GAAG;IAGhB,kBAAkB,EAAE,IAAI;IACxB,cAAc,EAAE,MAAM;CACvB,CAAA;AAGM,MAAM,oBAAoB,GAAG,GAAc,EAAE;IAClD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAA;IAEjD,QAAQ,GAAG,EAAE,CAAC;QACZ,KAAK,YAAY;YACf,OAAO;gBACL,GAAG,kBAAU;gBACb,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,YAAY;gBAC1C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,MAAM,CAAC;aAC/C,CAAA;QAEH,KAAK,aAAa;YAChB,OAAO;gBACL,GAAG,kBAAU;gBACb,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,WAAW;gBACzC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,MAAM,CAAC;aAC/C,CAAA;QAEH,KAAK,MAAM;YACT,OAAO;gBACL,GAAG,kBAAU;gBACb,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,IAAI;aACX,CAAA;QAEH;YACE,OAAO,kBAAU,CAAA;IACrB,CAAC;AACH,CAAC,CAAA;AA5BY,QAAA,oBAAoB,wBA4BhC;AAGM,MAAM,iBAAiB,GAAG,CAAC,MAAiB,EAAW,EAAE;IAC9D,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAEhB,OAAO,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;IAClE,CAAC;SAAM,CAAC;QAEN,OAAO,CACL,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;YAC/B,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;YACtB,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;YAC/B,MAAM,CAAC,IAAI,GAAG,CAAC;YACf,MAAM,CAAC,IAAI,IAAI,KAAK,CACrB,CAAA;IACH,CAAC;AACH,CAAC,CAAA;AAdY,QAAA,iBAAiB,qBAc7B;AAGM,MAAM,sBAAsB,GAAG,CAAC,MAAiB,EAAU,EAAE;IAClE,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAChB,OAAO,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAA;IAC9B,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAA;QAC1C,OAAO,SAAS,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,CAAA;IACrD,CAAC;AACH,CAAC,CAAA;AAPY,QAAA,sBAAsB,0BAOlC;AAED,kBAAe,kBAAU,CAAA"}