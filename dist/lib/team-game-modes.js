"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.teamGameModeManager = exports.TeamGameModeManager = void 0;
class TeamGameModeManager {
    constructor() {
        this.gameStates = new Map();
    }
    initializeTeamGame(gameId, teams, settings) {
        const gameState = {
            currentMode: settings.teamGameMode,
            collaborationPhase: false,
            relaySequence: [],
            specialistAssignments: new Map(),
            teamReadyStates: new Map()
        };
        switch (settings.teamGameMode) {
            case 'relay':
                this.initializeRelayMode(gameState, teams);
                break;
            case 'specialist':
                this.initializeSpecialistMode(gameState, teams);
                break;
            case 'collaborative':
                this.initializeCollaborativeMode(gameState, teams);
                break;
        }
        this.gameStates.set(gameId, gameState);
        return gameState;
    }
    initializeCollaborativeMode(gameState, teams) {
        gameState.collaborationPhase = true;
        teams.forEach(team => {
            gameState.teamReadyStates.set(team.id, false);
        });
    }
    initializeRelayMode(gameState, teams) {
        teams.forEach(team => {
            const shuffledPlayers = [...team.players].sort(() => Math.random() - 0.5);
            team.players.forEach((player, index) => {
                gameState.relaySequence.push(player.id);
            });
        });
        if (gameState.relaySequence.length > 0) {
            gameState.currentTeamTurn = gameState.relaySequence[0];
        }
    }
    initializeSpecialistMode(gameState, teams) {
        const specialties = ['decade', 'genre', 'artist', 'chart-position', 'year'];
        teams.forEach(team => {
            team.players.forEach((player, index) => {
                const specialty = specialties[index % specialties.length];
                gameState.specialistAssignments.set(player.id, specialty);
            });
        });
    }
    getCurrentTurnInfo(gameId, questionCategory) {
        const gameState = this.gameStates.get(gameId);
        if (!gameState)
            return [];
        const turnInfos = [];
        switch (gameState.currentMode) {
            case 'collaborative':
                Array.from(gameState.teamReadyStates.keys()).forEach(teamId => {
                    turnInfos.push({
                        teamId,
                        timeRemaining: 0,
                        canAnswer: !gameState.teamReadyStates.get(teamId),
                        waitingFor: []
                    });
                });
                break;
            case 'relay':
                if (gameState.currentTeamTurn) {
                    turnInfos.push({
                        teamId: gameState.currentTeamTurn,
                        activePlayerId: gameState.currentTeamTurn,
                        timeRemaining: 0,
                        canAnswer: true,
                        waitingFor: []
                    });
                }
                break;
            case 'specialist':
                if (questionCategory) {
                    Array.from(gameState.specialistAssignments.entries()).forEach(([playerId, specialty]) => {
                        if (specialty === questionCategory) {
                            turnInfos.push({
                                teamId: playerId,
                                activePlayerId: playerId,
                                timeRemaining: 0,
                                canAnswer: true,
                                waitingFor: []
                            });
                        }
                    });
                }
                break;
        }
        return turnInfos;
    }
    handleTeamAnswer(gameId, teamAnswer, teams) {
        const gameState = this.gameStates.get(gameId);
        if (!gameState) {
            return { accepted: false, reason: 'Game state not found', phaseComplete: false };
        }
        switch (gameState.currentMode) {
            case 'collaborative':
                return this.handleCollaborativeAnswer(gameState, teamAnswer);
            case 'relay':
                return this.handleRelayAnswer(gameState, teamAnswer, teams);
            case 'specialist':
                return this.handleSpecialistAnswer(gameState, teamAnswer);
            default:
                return { accepted: false, reason: 'Unknown game mode', phaseComplete: false };
        }
    }
    handleCollaborativeAnswer(gameState, teamAnswer) {
        gameState.teamReadyStates.set(teamAnswer.teamId, true);
        const allReady = Array.from(gameState.teamReadyStates.values()).every(ready => ready);
        return {
            accepted: true,
            reason: 'Team answer recorded',
            phaseComplete: allReady
        };
    }
    handleRelayAnswer(gameState, teamAnswer, teams) {
        const currentPlayerId = gameState.currentTeamTurn;
        if (teamAnswer.submittedBy !== currentPlayerId) {
            return {
                accepted: false,
                reason: 'Not your turn in relay',
                phaseComplete: false
            };
        }
        const currentIndex = gameState.relaySequence.indexOf(currentPlayerId);
        const nextIndex = (currentIndex + 1) % gameState.relaySequence.length;
        gameState.currentTeamTurn = gameState.relaySequence[nextIndex];
        const totalPlayers = teams.reduce((sum, team) => sum + team.players.length, 0);
        const phaseComplete = currentIndex === totalPlayers - 1;
        return {
            accepted: true,
            reason: 'Relay answer recorded',
            nextTurn: gameState.currentTeamTurn,
            phaseComplete
        };
    }
    handleSpecialistAnswer(gameState, teamAnswer) {
        const specialty = gameState.specialistAssignments.get(teamAnswer.submittedBy);
        return {
            accepted: true,
            reason: `Specialist (${specialty}) answer recorded`,
            phaseComplete: true
        };
    }
    startCollaborationPhase(gameId, duration) {
        const gameState = this.gameStates.get(gameId);
        if (!gameState || gameState.currentMode !== 'collaborative')
            return false;
        gameState.collaborationPhase = true;
        gameState.teamReadyStates.forEach((_, teamId) => {
            gameState.teamReadyStates.set(teamId, false);
        });
        setTimeout(() => {
            gameState.collaborationPhase = false;
        }, duration * 1000);
        return true;
    }
    getSpecialistAssignments(gameId) {
        const gameState = this.gameStates.get(gameId);
        return gameState?.specialistAssignments || new Map();
    }
    getRelaySequence(gameId) {
        const gameState = this.gameStates.get(gameId);
        return gameState?.relaySequence || [];
    }
    canPlayerAnswer(gameId, playerId, questionCategory) {
        const gameState = this.gameStates.get(gameId);
        if (!gameState) {
            return { canAnswer: false, reason: 'Game not found' };
        }
        switch (gameState.currentMode) {
            case 'collaborative':
                return { canAnswer: gameState.collaborationPhase, reason: 'Collaboration phase' };
            case 'relay':
                const isCurrentTurn = gameState.currentTeamTurn === playerId;
                return {
                    canAnswer: isCurrentTurn,
                    reason: isCurrentTurn ? 'Your turn' : 'Wait for your turn'
                };
            case 'specialist':
                const playerSpecialty = gameState.specialistAssignments.get(playerId);
                const isSpecialist = playerSpecialty === questionCategory;
                return {
                    canAnswer: isSpecialist,
                    reason: isSpecialist ? `Your specialty: ${playerSpecialty}` : `Not your specialty (${playerSpecialty})`
                };
            default:
                return { canAnswer: false, reason: 'Unknown mode' };
        }
    }
    resetForNewQuestion(gameId) {
        const gameState = this.gameStates.get(gameId);
        if (!gameState)
            return;
        switch (gameState.currentMode) {
            case 'collaborative':
                gameState.collaborationPhase = true;
                gameState.teamReadyStates.forEach((_, teamId) => {
                    gameState.teamReadyStates.set(teamId, false);
                });
                break;
            case 'relay':
                break;
            case 'specialist':
                break;
        }
    }
    getModeDescription(mode) {
        switch (mode) {
            case 'collaborative':
                return {
                    name: 'Collaborative',
                    description: 'Teams discuss together and submit one answer',
                    icon: 'MessageCircle',
                    features: [
                        'Team chat for strategy',
                        'Discussion time before answers',
                        'Captain submits final answer',
                        'Team consensus encouraged'
                    ]
                };
            case 'relay':
                return {
                    name: 'Relay',
                    description: 'Team members take turns answering questions',
                    icon: 'Zap',
                    features: [
                        'Players answer in sequence',
                        'Individual time pressure',
                        'Team coordination required',
                        'Fair participation for all'
                    ]
                };
            case 'specialist':
                return {
                    name: 'Specialist',
                    description: 'Each member specializes in different categories',
                    icon: 'Target',
                    features: [
                        'Category-based expertise',
                        'Specialized knowledge focus',
                        'Strategic team composition',
                        'Domain expert advantages'
                    ]
                };
            default:
                return {
                    name: 'Unknown',
                    description: '',
                    icon: 'Help',
                    features: []
                };
        }
    }
    cleanupGame(gameId) {
        this.gameStates.delete(gameId);
    }
}
exports.TeamGameModeManager = TeamGameModeManager;
exports.teamGameModeManager = new TeamGameModeManager();
//# sourceMappingURL=team-game-modes.js.map