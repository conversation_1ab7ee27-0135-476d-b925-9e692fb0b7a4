"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.multiplayerDiagnostic = exports.MultiplayerDiagnosticTool = void 0;
class MultiplayerDiagnosticTool {
    constructor() {
        this.diagnostics = [];
        this.MAX_DIAGNOSTICS = 100;
    }
    record(issue, details = {}, severity = 'medium') {
        const diagnostic = {
            timestamp: Date.now(),
            issue,
            details,
            severity
        };
        this.diagnostics.unshift(diagnostic);
        if (this.diagnostics.length > this.MAX_DIAGNOSTICS) {
            this.diagnostics = this.diagnostics.slice(0, this.MAX_DIAGNOSTICS);
        }
        if (severity === 'critical') {
            console.error(`[MultiplayerDiagnostic] CRITICAL: ${issue}`, details);
        }
        else if (severity === 'high') {
            console.warn(`[MultiplayerDiagnostic] HIGH: ${issue}`, details);
        }
        else {
            console.log(`[MultiplayerDiagnostic] ${severity.toUpperCase()}: ${issue}`, details);
        }
    }
    diagnoseMultiplayerState(state) {
        if (!state.isConnected) {
            this.record('Socket not connected', {
                gameCode: state.gameCode,
                playerId: state.playerId
            }, 'critical');
        }
        if (!state.gameCode && !state.playerId) {
            this.record('Not joined to any game', state, 'high');
        }
        if (state.isConnected && state.gameCode && !state.multiplayerCurrentQuestion) {
            this.record('No current question received from server', {
                gameCode: state.gameCode,
                questionsCount: state.multiplayerQuestions.length,
                quizState: state.quizState
            }, 'high');
        }
        if (state.multiplayerQuestions.length === 0 && state.quizState === 'playing') {
            this.record('Questions array empty during active quiz', {
                quizState: state.quizState,
                quizSubState: state.quizSubState,
                hasCurrentQuestion: !!state.multiplayerCurrentQuestion
            }, 'critical');
        }
        if (state.quizState === 'playing' && state.timeRemaining <= 0 && !state.multiplayerCurrentQuestion) {
            this.record('Timer running with no question', {
                timeRemaining: state.timeRemaining,
                quizState: state.quizState,
                quizSubState: state.quizSubState
            }, 'high');
        }
        if (typeof window !== 'undefined' && window.performance) {
            const audioEntries = performance.getEntriesByName('getAudioStatus');
            if (audioEntries.length > 0) {
                const recentEntries = audioEntries.slice(-5);
                const avgDuration = recentEntries.reduce((sum, entry) => sum + entry.duration, 0) / recentEntries.length;
                if (avgDuration > 1000) {
                    this.record('Slow audio operations blocking quiz', {
                        averageDuration: avgDuration,
                        recentCalls: recentEntries.length
                    }, 'high');
                }
            }
        }
    }
    suggestFixes() {
        const suggestions = [];
        const criticalIssues = this.diagnostics.filter(d => d.severity === 'critical');
        const highIssues = this.diagnostics.filter(d => d.severity === 'high');
        if (criticalIssues.some(d => d.issue.includes('not connected'))) {
            suggestions.push('Check network connection and socket server availability');
            suggestions.push('Verify authentication token is valid');
            suggestions.push('Try refreshing the page to reconnect');
        }
        if (criticalIssues.some(d => d.issue.includes('Questions array empty'))) {
            suggestions.push('Check if game host has started the quiz properly');
            suggestions.push('Verify question loading API is working');
            suggestions.push('Check browser console for question loading errors');
        }
        if (highIssues.some(d => d.issue.includes('current question'))) {
            suggestions.push('Check socket event listeners for question updates');
            suggestions.push('Verify server is sending question data correctly');
            suggestions.push('Check if game is in the correct state to receive questions');
        }
        if (highIssues.some(d => d.issue.includes('Slow audio'))) {
            suggestions.push('Check MPD server connection and performance');
            suggestions.push('Consider reducing audio status polling frequency');
            suggestions.push('Check network latency to audio server');
        }
        return suggestions;
    }
    generateReport() {
        const criticalCount = this.diagnostics.filter(d => d.severity === 'critical').length;
        const highCount = this.diagnostics.filter(d => d.severity === 'high').length;
        let summary = 'Multiplayer system appears healthy';
        if (criticalCount > 0) {
            summary = `${criticalCount} critical issue(s) detected`;
        }
        else if (highCount > 0) {
            summary = `${highCount} high priority issue(s) detected`;
        }
        return {
            summary,
            criticalIssues: criticalCount,
            highIssues: highCount,
            suggestions: this.suggestFixes(),
            recentDiagnostics: this.diagnostics.slice(0, 10)
        };
    }
    clear() {
        this.diagnostics = [];
    }
    autoReport() {
        const report = this.generateReport();
        console.group('[MultiplayerDiagnostic] System Report');
        console.log('Summary:', report.summary);
        console.log('Critical Issues:', report.criticalIssues);
        console.log('High Priority Issues:', report.highIssues);
        if (report.suggestions.length > 0) {
            console.log('Suggestions:');
            report.suggestions.forEach((suggestion, index) => {
                console.log(`  ${index + 1}. ${suggestion}`);
            });
        }
        if (report.recentDiagnostics.length > 0) {
            console.log('Recent Issues:');
            report.recentDiagnostics.forEach(diagnostic => {
                console.log(`  [${diagnostic.severity.toUpperCase()}] ${diagnostic.issue}`, diagnostic.details);
            });
        }
        console.groupEnd();
    }
}
exports.MultiplayerDiagnosticTool = MultiplayerDiagnosticTool;
exports.multiplayerDiagnostic = new MultiplayerDiagnosticTool();
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    setInterval(() => {
        const report = exports.multiplayerDiagnostic.generateReport();
        if (report.criticalIssues > 0 || report.highIssues > 0) {
            exports.multiplayerDiagnostic.autoReport();
        }
    }, 30000);
}
//# sourceMappingURL=multiplayer-diagnostic.js.map