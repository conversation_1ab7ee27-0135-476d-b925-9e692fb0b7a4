{"version": 3, "file": "profile-manager.js", "sourceRoot": "", "sources": ["../../lib/profile-manager.ts"], "names": [], "mappings": ";;;AAEA,MAAM,WAAW,GAAG,sBAAsB,CAAC;AAE9B,QAAA,cAAc,GAAG;IAC5B,UAAU;QACR,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gBAExC,IAAI,OAAO,IAAI,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBAC5D,OAAO,OAAO,CAAC;gBACjB,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,OAAoB;QAC9B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QACD,IAAI,CAAC;YACH,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC;IACpC,CAAC;IAED,eAAe,CAAC,IAAY,EAAE,MAAc;QAC1C,OAAO;YACL,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACtE,IAAI;YACJ,WAAW,EAAE,IAAI;YACjB,MAAM;YACN,eAAe,EAAE,SAAS;SAC3B,CAAC;IACJ,CAAC;IAED,kBAAkB;QAChB,MAAM,YAAY,GAAgB;YAChC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACvE,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,OAAO;YACpB,MAAM,EAAE,IAAI;YACZ,eAAe,EAAE,SAAS;YAC1B,OAAO,EAAE,IAAI;SACd,CAAC;QACF,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAC/B,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,eAAe,CAAC,QAAgB,SAAS;QAEvC,OAAO,CAAC,GAAG,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;IACzC,CAAC;CACF,CAAC"}