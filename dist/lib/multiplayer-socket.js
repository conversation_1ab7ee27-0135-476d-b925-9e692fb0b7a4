"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMultiplayerSocket = getMultiplayerSocket;
exports.connectMultiplayerSocket = connectMultiplayerSocket;
exports.disconnectMultiplayerSocket = disconnectMultiplayerSocket;
exports.isMultiplayerSocketConnected = isMultiplayerSocketConnected;
exports.cleanupMultiplayerSocket = cleanupMultiplayerSocket;
exports.getSocketId = getSocketId;
exports.emitMultiplayerEvent = emitMultiplayerEvent;
exports.onMultiplayerEvent = onMultiplayerEvent;
const socket_io_client_1 = require("socket.io-client");
const socketManager = {
    socket: null,
    isInitialized: false,
};
function getMultiplayerSocket() {
    if (!socketManager.socket || !socketManager.isInitialized) {
        initializeSocket();
    }
    return socketManager.socket;
}
function initializeSocket() {
    if (socketManager.isInitialized && socketManager.socket) {
        return;
    }
    const socketHost = typeof window !== 'undefined'
        ? window.location.hostname
        : process.env.NEXT_PUBLIC_SOCKET_HOST || 'localhost';
    const socketPort = process.env.NEXT_PUBLIC_SOCKET_PORT || '3001';
    const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || `http://${socketHost}:${socketPort}`;
    console.log('[MultiplayerSocket] Connecting to:', socketUrl);
    socketManager.socket = (0, socket_io_client_1.io)(socketUrl, {
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        transports: ['websocket', 'polling'],
    });
    socketManager.socket.on('connect', () => {
        console.log('[MultiplayerSocket] Connected to server');
    });
    socketManager.socket.on('disconnect', (reason) => {
        console.log('[MultiplayerSocket] Disconnected:', reason);
    });
    socketManager.socket.on('connect_error', (error) => {
        console.error('[MultiplayerSocket] Connection error:', error.message);
    });
    socketManager.socket.on('reconnect', (attemptNumber) => {
        console.log('[MultiplayerSocket] Reconnected after', attemptNumber, 'attempts');
    });
    socketManager.socket.on('reconnect_attempt', (attemptNumber) => {
        console.log('[MultiplayerSocket] Reconnection attempt', attemptNumber);
    });
    socketManager.socket.on('reconnect_failed', () => {
        console.error('[MultiplayerSocket] Reconnection failed');
    });
    socketManager.isInitialized = true;
}
function connectMultiplayerSocket() {
    const socket = getMultiplayerSocket();
    if (!socket.connected) {
        socket.connect();
    }
}
function disconnectMultiplayerSocket() {
    if (socketManager.socket && socketManager.socket.connected) {
        socketManager.socket.disconnect();
    }
}
function isMultiplayerSocketConnected() {
    return socketManager.socket?.connected ?? false;
}
function cleanupMultiplayerSocket() {
    if (socketManager.socket) {
        socketManager.socket.removeAllListeners();
        socketManager.socket.disconnect();
        socketManager.socket = null;
        socketManager.isInitialized = false;
    }
}
function getSocketId() {
    return socketManager.socket?.id;
}
function emitMultiplayerEvent(event, ...args) {
    const socket = getMultiplayerSocket();
    if (socket.connected) {
        socket.emit(event, ...args);
    }
    else {
        console.warn(`[MultiplayerSocket] Cannot emit "${event}" - socket not connected`);
    }
}
function onMultiplayerEvent(event, handler) {
    const socket = getMultiplayerSocket();
    socket.on(event, handler);
    return () => {
        socket.off(event, handler);
    };
}
//# sourceMappingURL=multiplayer-socket.js.map