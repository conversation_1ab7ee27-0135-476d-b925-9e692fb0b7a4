"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestManager = void 0;
class RequestManagerClass {
    constructor() {
        this.pendingRequests = new Map();
        this.requestCounts = new Map();
        this.MAX_CONCURRENT_PER_ENDPOINT = 1;
        this.RATE_LIMIT_WINDOW = 1000;
        this.MAX_REQUESTS_PER_WINDOW = 3;
    }
    async fetch(url, options) {
        const key = `${options?.method || 'GET'}:${url}`;
        const pending = this.pendingRequests.get(key);
        if (pending) {
            console.log(`[RequestManager] Reusing pending request for ${key}`);
            return pending.then(response => response.clone());
        }
        const now = Date.now();
        const countKey = `${key}:${Math.floor(now / this.RATE_LIMIT_WINDOW)}`;
        const currentCount = this.requestCounts.get(countKey) || 0;
        if (currentCount >= this.MAX_REQUESTS_PER_WINDOW) {
            console.warn(`[RequestManager] Rate limit exceeded for ${key}`);
            throw new Error('Rate limit exceeded');
        }
        this.requestCounts.set(countKey, currentCount + 1);
        this.cleanOldCounts();
        const requestPromise = fetch(url, options)
            .finally(() => {
            this.pendingRequests.delete(key);
        });
        this.pendingRequests.set(key, requestPromise);
        return requestPromise;
    }
    cleanOldCounts() {
        const now = Date.now();
        const oldestValid = Math.floor((now - this.RATE_LIMIT_WINDOW * 2) / this.RATE_LIMIT_WINDOW);
        for (const [key, _] of this.requestCounts) {
            const windowTime = parseInt(key.split(':').pop() || '0');
            if (windowTime < oldestValid) {
                this.requestCounts.delete(key);
            }
        }
    }
    clear() {
        this.pendingRequests.clear();
        this.requestCounts.clear();
    }
}
exports.requestManager = new RequestManagerClass();
//# sourceMappingURL=request-manager.js.map