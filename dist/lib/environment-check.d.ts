export interface EnvironmentCheckResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    info: {
        userAgent?: string;
        nodeVersion?: string;
        reflectSupport: boolean;
        timestamp: string;
    };
}
export declare function validateEnvironment(): EnvironmentCheckResult;
export declare function logEnvironmentCheck(): EnvironmentCheckResult;
export declare function setupErrorTracking(): void;
export declare function checkReactCompatibility(): boolean;
export declare function runAllChecks(): void;
