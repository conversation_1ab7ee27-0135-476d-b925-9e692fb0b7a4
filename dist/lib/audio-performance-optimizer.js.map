{"version": 3, "file": "audio-performance-optimizer.js", "sourceRoot": "", "sources": ["../../lib/audio-performance-optimizer.ts"], "names": [], "mappings": ";;;AAuBA,MAAa,yBAAyB;IAAtC;QACU,gBAAW,GAA4B,IAAI,CAAA;QAClC,cAAS,GAAG,IAAI,CAAA;QAChB,6BAAwB,GAAG,GAAG,CAAA;QACvC,sBAAiB,GAAG,IAAI,GAAG,EAAwB,CAAA;IAyM7D,CAAC;IApMC,KAAK,CAAC,uBAAuB,CAAC,YAAiB;QAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAGtB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;YAClF,OAAO;gBACL,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM;gBAC1B,MAAM,EAAE,IAAI;aACb,CAAA;QACH,CAAC;QAGD,MAAM,YAAY,GAAG,gBAAgB,CAAA;QACrC,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;gBAC7D,OAAO;oBACL,GAAG,MAAM;oBACT,MAAM,EAAE,KAAK;iBACd,CAAA;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAA;YACnE,CAAC;QACH,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;QACpE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAA;QAEnD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,CAAA;YAG9B,IAAI,CAAC,WAAW,GAAG;gBACjB,MAAM;gBACN,SAAS,EAAE,GAAG;gBACd,GAAG,EAAE,IAAI,CAAC,SAAS;aACpB,CAAA;YAGD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAE3C,OAAO;gBACL,GAAG,MAAM;gBACT,MAAM,EAAE,KAAK;aACd,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAA;YACpE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAG3C,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACjC,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,yBAAyB,CAAC,YAAiB,EAAE,SAAiB;QAC1E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAE5B,MAAM,cAAc,GAAG,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;YACtD,UAAU,CAAC,GAAG,EAAE;gBACd,MAAM,CAAC,IAAI,KAAK,CAAC,8BAA8B,SAAS,IAAI,CAAC,CAAC,CAAA;YAChE,CAAC,EAAE,SAAS,CAAC,CAAA;QACf,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBAChC,YAAY,CAAC,cAAc,EAAE;gBAC7B,cAAc;aACf,CAAC,CAAA;YAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YAGvC,IAAI,QAAQ,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAC7C,OAAO,CAAC,IAAI,CAAC,0CAA0C,QAAQ,IAAI,CAAC,CAAA;YACtE,CAAC;YAED,OAAO;gBACL,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,KAAK;gBACpC,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,IAAI;gBACzC,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,EAAE;gBAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC;gBAC9B,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC;gBAC9B,MAAM,EAAE,KAAK;aACd,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtC,OAAO,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAA;gBAC7F,OAAO,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAA;YAC7D,CAAC;YACD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,YAAiB;QAEzC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEtB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,EAAE,CAAC;YAClE,OAAO;gBACL,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM;gBAC1B,MAAM,EAAE,IAAI;aACb,CAAA;QACH,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;YACvE,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAA;YACpE,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACjC,CAAC;IACH,CAAC;IAKD,kBAAkB,CAAC,YAAiB;QAClC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3C,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,IAAI,CAAC;iBACxE,IAAI,CAAC,MAAM,CAAC,EAAE;gBACb,IAAI,CAAC,WAAW,GAAG;oBACjB,MAAM;oBACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,GAAG,EAAE,IAAI,CAAC,SAAS;iBACpB,CAAA;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAA;YACzD,CAAC,CAAC;iBACD,OAAO,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;YAC1C,CAAC,CAAC,CAAA;YAEJ,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAA;QACzD,CAAC;IACH,CAAC;IAKO,iBAAiB;QACvB,OAAO;YACL,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,CAAC;YACX,MAAM,EAAE,IAAI;SACb,CAAA;IACH,CAAC;IAKD,UAAU;QACR,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;IAChC,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,YAAiB;QAC7C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC5B,MAAM,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;YACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YAEvC,OAAO,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAA;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAKD,qBAAqB;QAKnB,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;YAC3E,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,CAAC;YACrD,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;SACnD,CAAA;IACH,CAAC;CACF;AA7MD,8DA6MC;AAGY,QAAA,yBAAyB,GAAG,IAAI,yBAAyB,EAAE,CAAA"}