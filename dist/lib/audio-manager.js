"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioManager = void 0;
const mpd_client_1 = require("./mpd-client");
const logger_1 = require("./logger");
class AudioManager {
    constructor(config) {
        this.isInitialized = false;
        this.isConnected = false;
        this.statusInterval = null;
        this.lastStatus = null;
        this.lastStatusTime = 0;
        this.eventCallbacks = new Map();
        this.currentTrack = null;
        this.trackHistory = [];
        this.trackQueue = [];
        this.reconnectAttempts = 0;
        this.reconnectTimer = null;
        this.fadeOutTimer = null;
        this.quizTimeLimit = 30;
        this.quizTimeoutTimer = null;
        this.isFading = false;
        this.userVolume = 85;
        this.volumeNormalizationEnabled = true;
        this.config = {
            host: config.host,
            port: config.port,
            password: config.password || '',
            httpProxyPort: config.httpProxyPort || 8001,
            timeout: config.timeout || 5000,
            statusUpdateInterval: config.statusUpdateInterval || 10000,
            enableLogging: config.enableLogging || false,
            autoReconnect: config.autoReconnect !== false,
            maxReconnectAttempts: config.maxReconnectAttempts || 3
        };
        this.mpdClient = new mpd_client_1.MPDClient({
            host: this.config.host,
            port: this.config.port,
            password: this.config.password,
            httpProxyPort: this.config.httpProxyPort,
            timeout: this.config.timeout
        });
        const eventTypes = [
            'statusUpdate', 'trackChanged', 'trackEnded', 'connected',
            'disconnected', 'error', 'volumeChanged'
        ];
        eventTypes.forEach(type => this.eventCallbacks.set(type, []));
    }
    async initialize() {
        try {
            logger_1.audioLogger.info('Initializing AudioManager...');
            console.log('[AudioManager] Starting initialization with config:', {
                host: this.config.host,
                port: this.config.port,
                httpProxyPort: this.config.httpProxyPort
            });
            try {
                await logger_1.PerformanceLogger.measureAsync('audio-manager-init', async () => {
                    console.log('[AudioManager] Attempting MPD connection...');
                    await this.mpdClient.connect();
                    console.log('[AudioManager] MPD connection successful');
                });
            }
            catch (connectError) {
                console.error('[AudioManager] MPD connection failed:', {
                    error: connectError,
                    message: connectError instanceof Error ? connectError.message : String(connectError),
                    type: connectError?.constructor?.name
                });
                throw connectError;
            }
            this.isConnected = true;
            this.reconnectAttempts = 0;
            try {
                this.startStatusMonitoring();
            }
            catch (monitorError) {
                console.error('[AudioManager] Failed to start status monitoring:', monitorError);
            }
            this.isInitialized = true;
            logger_1.audioLogger.info('AudioManager initialized successfully');
            this.emitEvent('connected', {
                host: this.config.host,
                port: this.config.port
            });
        }
        catch (error) {
            this.isConnected = false;
            const errorDetails = {
                message: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
                config: {
                    host: this.config.host,
                    port: this.config.port,
                    httpProxyPort: this.config.httpProxyPort
                }
            };
            logger_1.audioLogger.error('Failed to initialize AudioManager', error instanceof Error ? error : new Error(String(error)));
            console.error('[AudioManager] Initialization error details:', errorDetails);
            this.emitEvent('error', {
                message: 'Failed to connect to MPD server',
                error: error instanceof Error ? error.message : String(error)
            });
            if (this.config.autoReconnect) {
                this.scheduleReconnect();
            }
            throw error;
        }
    }
    isReady() {
        return this.isInitialized && this.isConnected;
    }
    getConnectionStatus() {
        return {
            connected: this.isConnected,
            host: this.config.host,
            port: this.config.port
        };
    }
    async playTrack(track, options = {}) {
        console.log('[AudioManager] playTrack START - method called!');
        console.log('[AudioManager] playTrack parameters:', {
            track: typeof track === 'string' ? track : track.file,
            options,
            isReady: this.isReady(),
            isInitialized: this.isInitialized,
            isConnected: this.isConnected
        });
        if (!this.isReady()) {
            throw new Error('AudioManager not ready. Call initialize() first.');
        }
        try {
            const { clearQueue = true, addToHistory = true, startTime = 0 } = options;
            const trackObj = typeof track === 'string'
                ? { file: track, quizId: `track_${Date.now()}`, time: 0 }
                : { ...track, quizId: track.quizId || `track_${Date.now()}` };
            this.log(`Playing track: ${trackObj.title || trackObj.file}`);
            const isMock = !trackObj.file || trackObj.file.startsWith('/test') || trackObj.file.startsWith('http');
            if (!isMock) {
                try {
                    try {
                        await this.mpdClient.stop().catch(() => { });
                        if (clearQueue) {
                            await this.mpdClient.clearPlaylist();
                        }
                        await this.mpdClient.addTrack(trackObj.file);
                        if (startTime > 0) {
                            await this.mpdClient.play(0);
                            await this.mpdClient.pause();
                            await new Promise(resolve => setTimeout(resolve, 100));
                            await this.mpdClient.seek(startTime);
                            await new Promise(resolve => setTimeout(resolve, 50));
                            await this.mpdClient.resume();
                        }
                        else {
                            await this.mpdClient.play(0);
                        }
                    }
                    catch (error) {
                        console.error('[AudioManager] Playback sequence error:', error);
                        await this.mpdClient.clearPlaylist();
                        await this.mpdClient.addTrack(trackObj.file);
                        await this.mpdClient.play(0);
                        if (startTime > 0) {
                            await new Promise(resolve => setTimeout(resolve, 200));
                            await this.mpdClient.seek(startTime);
                        }
                    }
                }
                catch (error) {
                    this.log('Standard playback sequence failed, trying alternative:', error);
                    await this.mpdClient.clearPlaylist();
                    await new Promise(resolve => setTimeout(resolve, 100));
                    await this.mpdClient.addTrack(trackObj.file);
                    await this.mpdClient.play(0);
                    if (startTime > 0) {
                        await this.mpdClient.seek(startTime);
                    }
                }
            }
            else {
                this.log('[AudioManager] Mock track detected – skipping MPD playback');
            }
            this.currentTrack = trackObj;
            if (addToHistory) {
                this.trackHistory.push(trackObj);
                if (this.trackHistory.length > 50) {
                    this.trackHistory = this.trackHistory.slice(-50);
                }
            }
            this.emitEvent('trackChanged', trackObj);
        }
        catch (error) {
            this.log('Failed to play track:', error);
            this.emitEvent('error', {
                message: 'Failed to play track',
                track: typeof track === 'string' ? track : track.file,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    async pause() {
        if (!this.isReady())
            return;
        try {
            await this.mpdClient.pause();
            this.log('Playback paused');
        }
        catch (error) {
            this.log('Failed to pause:', error);
            throw error;
        }
    }
    async resume() {
        if (!this.isReady())
            return;
        try {
            await this.mpdClient.resume();
            this.log('Playback resumed');
        }
        catch (error) {
            this.log('Failed to resume:', error);
            throw error;
        }
    }
    async stop() {
        if (!this.isReady())
            return;
        try {
            await this.mpdClient.stop();
            await this.mpdClient.clearPlaylist();
            this.currentTrack = null;
            this.clearFadeTimer();
            this.log('Playback stopped and playlist cleared');
        }
        catch (error) {
            this.log('Failed to stop:', error);
            throw error;
        }
    }
    async togglePlayback() {
        if (!this.isReady())
            return;
        const status = await this.getAudioStatus();
        if (status.isPlaying) {
            await this.pause();
        }
        else {
            await this.resume();
        }
    }
    async nextTrack() {
        if (!this.isReady())
            return;
        try {
            if (this.trackQueue.length > 0) {
                const nextTrack = this.trackQueue.shift();
                await this.playTrack(nextTrack);
            }
            else {
                await this.mpdClient.next();
            }
        }
        catch (error) {
            this.log('Failed to skip to next track:', error);
            throw error;
        }
    }
    async previousTrack() {
        if (!this.isReady())
            return;
        try {
            await this.mpdClient.previous();
        }
        catch (error) {
            this.log('Failed to go to previous track:', error);
            throw error;
        }
    }
    async seek(time) {
        if (!this.isReady())
            return;
        try {
            await this.mpdClient.seek(time);
            this.log(`Seeked to ${time}s`);
        }
        catch (error) {
            this.log('Failed to seek:', error);
            throw error;
        }
    }
    async setVolume(volume, isInternalFade = false) {
        if (!this.isReady())
            return;
        try {
            const clampedVolume = Math.max(0, Math.min(100, Math.round(volume)));
            await this.mpdClient.setVolume(clampedVolume);
            if (!isInternalFade) {
                this.userVolume = clampedVolume;
                this.emitEvent('volumeChanged', { volume: clampedVolume });
                this.log(`User volume set to ${clampedVolume}`);
            }
            else {
                this.log(`Internal fade volume set to ${clampedVolume}`);
            }
        }
        catch (error) {
            this.log('Failed to set volume:', error);
            throw error;
        }
    }
    calculateNormalizedVolume(track, userVolume) {
        if (!this.volumeNormalizationEnabled || track.calculatedGain === undefined || track.calculatedGain === null) {
            console.log('[AudioManager] Volume normalization skipped:', {
                enabled: this.volumeNormalizationEnabled,
                hasGain: track.calculatedGain !== undefined && track.calculatedGain !== null,
                gain: track.calculatedGain
            });
            return userVolume;
        }
        const gainLinear = Math.pow(10, track.calculatedGain / 20);
        const rawNormalizedVolume = userVolume * gainLinear;
        const normalizedVolume = Math.round(rawNormalizedVolume);
        const minVolume = Math.round(userVolume * 0.6);
        const finalVolume = Math.max(minVolume, Math.min(100, normalizedVolume));
        console.log('[AudioManager] Volume normalization calculation:', {
            userVolume,
            calculatedGain: track.calculatedGain,
            gainLinear: gainLinear.toFixed(3),
            rawNormalizedVolume: rawNormalizedVolume.toFixed(1),
            normalizedVolume,
            minVolume,
            finalVolume,
            title: track.title,
            artist: track.artist
        });
        return finalVolume;
    }
    setVolumeNormalizationEnabled(enabled) {
        this.volumeNormalizationEnabled = enabled;
        this.log(`Volume normalization ${enabled ? 'enabled' : 'disabled'}`);
    }
    isVolumeNormalizationEnabled() {
        return this.volumeNormalizationEnabled;
    }
    async fadeOut(duration = 2) {
        if (!this.isReady())
            return;
        this.isFading = true;
        const status = await this.getAudioStatus();
        const startVolume = status.volume;
        const steps = 20;
        const stepDuration = (duration * 1000) / steps;
        const volumeStep = startVolume / steps;
        this.clearFadeTimer();
        let currentStep = 0;
        this.fadeOutTimer = setInterval(async () => {
            currentStep++;
            const newVolume = Math.max(0, startVolume - (volumeStep * currentStep));
            try {
                await this.setVolume(newVolume, true);
                if (currentStep >= steps) {
                    this.clearFadeTimer();
                    this.isFading = false;
                    await this.pause();
                }
            }
            catch (error) {
                this.log('Fade out error:', error);
                this.clearFadeTimer();
                this.isFading = false;
            }
        }, stepDuration);
    }
    async fadeIn(targetVolume = 70, duration = 2) {
        if (!this.isReady())
            return;
        this.isFading = true;
        this.userVolume = targetVolume;
        await this.setVolume(0, true);
        const steps = 20;
        const stepDuration = (duration * 1000) / steps;
        const volumeStep = targetVolume / steps;
        let currentStep = 0;
        const fadeTimer = setInterval(async () => {
            currentStep++;
            const newVolume = Math.min(targetVolume, volumeStep * currentStep);
            try {
                await this.setVolume(newVolume, true);
                if (currentStep >= steps) {
                    clearInterval(fadeTimer);
                    this.isFading = false;
                    this.emitEvent('volumeChanged', { volume: targetVolume });
                    this.log(`Fade in complete at ${targetVolume}%`);
                }
            }
            catch (error) {
                this.log('Fade in error:', error);
                clearInterval(fadeTimer);
                this.isFading = false;
            }
        }, stepDuration);
    }
    async playQuizTrack(track, timeLimit = this.quizTimeLimit, previewStart) {
        console.log('[AudioManager] playQuizTrack START - method called!');
        console.log('[AudioManager] playQuizTrack parameters:', {
            track: typeof track === 'string' ? track : track.file,
            timeLimit,
            previewStart,
            isReady: this.isReady(),
            isInitialized: this.isInitialized,
            isConnected: this.isConnected
        });
        const trackObj = typeof track === 'string'
            ? { file: track, quizId: `quiz_${Date.now()}`, time: 0 }
            : { ...track, quizId: track.quizId || `quiz_${Date.now()}` };
        this.log(`Playing quiz track: ${trackObj.title || trackObj.file} with ${timeLimit}s limit`);
        console.log('[AudioManager] playQuizTrack trackObj:', {
            trackFile: trackObj.file,
            trackTitle: trackObj.title,
            trackArtist: trackObj.artist,
            timeLimit,
            previewStart
        });
        if (this.quizTimeoutTimer) {
            clearTimeout(this.quizTimeoutTimer);
            this.quizTimeoutTimer = null;
        }
        const startTime = previewStart || trackObj.preview_start || 30;
        let targetVolume = this.userVolume;
        if (this.volumeNormalizationEnabled && trackObj.calculatedGain !== undefined && trackObj.calculatedGain !== null) {
            targetVolume = this.calculateNormalizedVolume(trackObj, this.userVolume);
            console.log('[AudioManager] Will normalize volume:', {
                userVolume: this.userVolume,
                targetVolume,
                gain: trackObj.calculatedGain,
                lufs: trackObj.lufsVolume
            });
        }
        await this.setVolume(0, true);
        await this.playTrack(trackObj, { startTime });
        const fadeSteps = 10;
        const fadeStepDuration = 50;
        const volumeStep = targetVolume / fadeSteps;
        for (let i = 1; i <= fadeSteps; i++) {
            setTimeout(async () => {
                try {
                    const currentVolume = Math.min(volumeStep * i, targetVolume);
                    await this.setVolume(currentVolume, true);
                    if (i === fadeSteps) {
                        this.log(`Quiz track started with smooth fade-in to ${targetVolume}%`);
                    }
                }
                catch (error) {
                    this.log('Error during fade-in:', error);
                }
            }, fadeStepDuration * i);
        }
        this.quizTimeoutTimer = setTimeout(() => {
            this.emitEvent('trackEnded', {
                track: trackObj,
                reason: 'timeLimit',
                timeLimit
            });
            this.quizTimeoutTimer = null;
        }, timeLimit * 1000);
    }
    async stopQuizTrack() {
        if (this.quizTimeoutTimer) {
            clearTimeout(this.quizTimeoutTimer);
            this.quizTimeoutTimer = null;
        }
        await this.stop();
    }
    getUserVolume() {
        return this.userVolume;
    }
    isFadingVolume() {
        return this.isFading;
    }
    async fadeOutQuizTrack(duration = 2) {
        if (this.quizTimeoutTimer) {
            clearTimeout(this.quizTimeoutTimer);
            this.quizTimeoutTimer = null;
        }
        await this.fadeOut(duration);
    }
    addToQueue(tracks) {
        this.trackQueue.push(...tracks);
        this.log(`Added ${tracks.length} tracks to queue. Queue length: ${this.trackQueue.length}`);
    }
    clearQueue() {
        this.trackQueue = [];
        this.log('Track queue cleared');
    }
    getTrackHistory(limit = 10) {
        return this.trackHistory.slice(-limit);
    }
    setQuizTimeLimit(seconds) {
        this.quizTimeLimit = Math.max(10, Math.min(120, seconds));
        this.log(`Quiz time limit set to ${this.quizTimeLimit}s`);
    }
    async getAudioStatus() {
        if (!this.isReady()) {
            return this.generateDefaultStatus();
        }
        try {
            logger_1.PerformanceLogger.start('getAudioStatus');
            if (this.lastStatus && (Date.now() - this.lastStatusTime) < 2000) {
                logger_1.PerformanceLogger.end('getAudioStatus');
                return this.lastStatus;
            }
            const [status, currentSong] = await Promise.all([
                this.mpdClient.getStatus(),
                this.mpdClient.getCurrentSong()
            ]);
            this.log('[getAudioStatus] MPD Status:', status);
            this.log('[getAudioStatus] Current Song:', currentSong);
            const isPlaying = status.state === 'play';
            const isPaused = status.state === 'pause';
            let currentTrack = null;
            if (currentSong && this.currentTrack) {
                currentTrack = {
                    ...this.currentTrack,
                    file: currentSong.file,
                    time: currentSong.time || this.currentTrack.time,
                    duration_formatted: this.formatTime(currentSong.time || this.currentTrack.time || 0)
                };
            }
            else if (this.currentTrack) {
                currentTrack = {
                    ...this.currentTrack,
                    duration_formatted: this.formatTime(this.currentTrack.time || 0)
                };
            }
            const newStatus = {
                isConnected: true,
                isPlaying: isPlaying,
                isPaused: isPaused,
                currentTrack,
                volume: status.volume,
                elapsed: status.elapsed || 0,
                duration: status.duration || 0,
                position: this.formatPosition(status.elapsed || 0, status.duration || 0),
                mpdStatus: status
            };
            if (this.lastStatus) {
                if (this.lastStatus.currentTrack?.quizId !== newStatus.currentTrack?.quizId) {
                    this.emitEvent('trackChanged', newStatus.currentTrack);
                }
                if (this.lastStatus.volume !== newStatus.volume) {
                    this.emitEvent('volumeChanged', { volume: newStatus.volume });
                }
            }
            this.lastStatus = newStatus;
            this.lastStatusTime = Date.now();
            const duration = logger_1.PerformanceLogger.end('getAudioStatus');
            this.log(`[getAudioStatus] Total time: ${duration.toFixed(2)}ms`);
            this.emitEvent('statusUpdate', newStatus);
            return newStatus;
        }
        catch (error) {
            this.log('Failed to get audio status:', error);
            return {
                isConnected: false,
                isPlaying: false,
                isPaused: false,
                currentTrack: null,
                volume: 0,
                elapsed: 0,
                duration: 0,
                position: '00:00 / 00:00',
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    getCurrentTrack() {
        return this.currentTrack;
    }
    on(eventType, callback) {
        const callbacks = this.eventCallbacks.get(eventType) || [];
        callbacks.push(callback);
        this.eventCallbacks.set(eventType, callbacks);
    }
    off(eventType, callback) {
        const callbacks = this.eventCallbacks.get(eventType) || [];
        const index = callbacks.indexOf(callback);
        if (index > -1) {
            callbacks.splice(index, 1);
            this.eventCallbacks.set(eventType, callbacks);
        }
    }
    emitEvent(type, data) {
        const event = {
            type,
            data,
            timestamp: Date.now()
        };
        const callbacks = this.eventCallbacks.get(type) || [];
        callbacks.forEach(callback => {
            try {
                callback(event);
            }
            catch (error) {
                this.log(`Event callback error for ${type}:`, error);
            }
        });
    }
    startStatusMonitoring() {
        if (this.statusInterval) {
            clearInterval(this.statusInterval);
        }
        this.statusInterval = setInterval(async () => {
            try {
                const status = await this.getAudioStatus();
                if (this.lastStatus &&
                    this.lastStatus.currentTrack?.file !== status.currentTrack?.file) {
                    this.emitEvent('trackChanged', status.currentTrack);
                }
                if (this.lastStatus &&
                    this.lastStatus.isPlaying &&
                    !status.isPlaying &&
                    status.elapsed >= status.duration - 1) {
                    this.emitEvent('trackEnded', {
                        track: status.currentTrack,
                        reason: 'finished'
                    });
                }
                this.emitEvent('statusUpdate', status);
            }
            catch (error) {
                this.log('Status monitoring error:', error);
                if (this.isConnected) {
                    this.isConnected = false;
                    this.emitEvent('disconnected', { error });
                    if (this.config.autoReconnect) {
                        this.scheduleReconnect();
                    }
                }
            }
        }, this.config.statusUpdateInterval);
    }
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
            this.log(`Max reconnection attempts (${this.config.maxReconnectAttempts}) reached`);
            return;
        }
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        const delay = Math.min(5000 * Math.pow(2, this.reconnectAttempts), 30000);
        this.reconnectAttempts++;
        this.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
        this.reconnectTimer = setTimeout(async () => {
            try {
                this.log(`Reconnection attempt ${this.reconnectAttempts}...`);
                await this.mpdClient.connect();
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.log('Reconnected successfully');
                this.emitEvent('connected', { reconnection: true });
            }
            catch (error) {
                this.log(`Reconnection attempt ${this.reconnectAttempts} failed:`, error);
                this.scheduleReconnect();
            }
        }, delay);
    }
    clearFadeTimer() {
        if (this.fadeOutTimer) {
            clearInterval(this.fadeOutTimer);
            this.fadeOutTimer = null;
        }
    }
    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    formatPosition(elapsed, duration) {
        return `${this.formatTime(elapsed)} / ${this.formatTime(duration)}`;
    }
    log(...args) {
        if (this.config.enableLogging) {
            logger_1.audioLogger.info('[AudioManager]', ...args);
        }
    }
    async cleanup() {
        this.log('Cleaning up AudioManager...');
        if (this.statusInterval) {
            clearInterval(this.statusInterval);
            this.statusInterval = null;
        }
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        if (this.quizTimeoutTimer) {
            clearTimeout(this.quizTimeoutTimer);
            this.quizTimeoutTimer = null;
        }
        this.clearFadeTimer();
        try {
            if (this.isConnected) {
                await this.stop();
            }
        }
        catch (error) {
            this.log('Error stopping playback during cleanup:', error);
        }
        this.isInitialized = false;
        this.isConnected = false;
        this.currentTrack = null;
        this.trackQueue = [];
        this.eventCallbacks.clear();
        this.log('AudioManager cleanup complete');
    }
    generateDefaultStatus() {
        return {
            isConnected: false,
            isPlaying: false,
            isPaused: false,
            currentTrack: null,
            volume: 0,
            elapsed: 0,
            duration: 0,
            position: '00:00 / 00:00'
        };
    }
}
exports.AudioManager = AudioManager;
//# sourceMappingURL=audio-manager.js.map