"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.volumeStateManager = void 0;
exports.useVolumeState = useVolumeState;
const react_1 = __importDefault(require("react"));
const VOLUME_STORAGE_KEY = 'music-quiz-volume-preference';
const DEFAULT_VOLUME = 70;
class VolumeStateManager {
    constructor() {
        this.listeners = new Set();
        this.currentState = this.loadVolumeState();
    }
    loadVolumeState() {
        if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
            return {
                userVolume: DEFAULT_VOLUME,
                lastSetAt: Date.now(),
                mode: 'unknown'
            };
        }
        try {
            const stored = localStorage.getItem(VOLUME_STORAGE_KEY);
            if (stored) {
                const parsed = JSON.parse(stored);
                if (parsed.userVolume >= 0 && parsed.userVolume <= 100) {
                    return {
                        ...parsed,
                        mode: 'unknown'
                    };
                }
            }
        }
        catch (error) {
            console.warn('[VolumeState] Failed to load from storage:', error);
        }
        return {
            userVolume: DEFAULT_VOLUME,
            lastSetAt: Date.now(),
            mode: 'unknown'
        };
    }
    saveVolumeState() {
        if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
            return;
        }
        try {
            localStorage.setItem(VOLUME_STORAGE_KEY, JSON.stringify(this.currentState));
        }
        catch (error) {
            console.warn('[VolumeState] Failed to save to storage:', error);
        }
    }
    getVolumeState() {
        return { ...this.currentState };
    }
    getUserVolume() {
        return this.currentState.userVolume;
    }
    setUserVolume(volume, mode = 'unknown') {
        const clampedVolume = Math.max(0, Math.min(100, volume));
        const wasChanged = this.currentState.userVolume !== clampedVolume || this.currentState.mode !== mode;
        this.currentState = {
            userVolume: clampedVolume,
            lastSetAt: Date.now(),
            mode
        };
        if (wasChanged) {
            this.saveVolumeState();
            this.notifyListeners();
            console.log(`[VolumeState] Volume set to ${clampedVolume}% in ${mode} mode`);
        }
    }
    setMode(mode) {
        if (this.currentState.mode !== mode) {
            this.currentState.mode = mode;
            this.saveVolumeState();
            console.log(`[VolumeState] Mode changed to ${mode}, volume: ${this.currentState.userVolume}%`);
        }
    }
    addListener(listener) {
        this.listeners.add(listener);
        return () => this.listeners.delete(listener);
    }
    notifyListeners() {
        this.listeners.forEach(listener => {
            try {
                listener(this.getVolumeState());
            }
            catch (error) {
                console.error('[VolumeState] Listener error:', error);
            }
        });
    }
    initializeForMode(mode) {
        this.setMode(mode);
        return this.currentState.userVolume;
    }
    transitionToMode(fromMode, toMode) {
        console.log(`[VolumeState] Transitioning from ${fromMode} to ${toMode}`);
        this.setMode(toMode);
        return this.currentState.userVolume;
    }
    resetToDefault() {
        this.setUserVolume(DEFAULT_VOLUME);
        console.log('[VolumeState] Reset to default volume');
    }
    getDebugInfo() {
        return {
            state: this.getVolumeState(),
            storage: typeof window !== 'undefined' && typeof localStorage !== 'undefined'
                ? localStorage.getItem(VOLUME_STORAGE_KEY)
                : null,
            listeners: this.listeners.size
        };
    }
}
exports.volumeStateManager = new VolumeStateManager();
function useVolumeState() {
    const [volumeState, setVolumeState] = react_1.default.useState(exports.volumeStateManager.getVolumeState());
    react_1.default.useEffect(() => {
        return exports.volumeStateManager.addListener(setVolumeState);
    }, []);
    return {
        ...volumeState,
        setUserVolume: exports.volumeStateManager.setUserVolume.bind(exports.volumeStateManager),
        setMode: exports.volumeStateManager.setMode.bind(exports.volumeStateManager),
        getUserVolume: exports.volumeStateManager.getUserVolume.bind(exports.volumeStateManager),
        initializeForMode: exports.volumeStateManager.initializeForMode.bind(exports.volumeStateManager),
        transitionToMode: exports.volumeStateManager.transitionToMode.bind(exports.volumeStateManager)
    };
}
//# sourceMappingURL=volume-state-manager.js.map