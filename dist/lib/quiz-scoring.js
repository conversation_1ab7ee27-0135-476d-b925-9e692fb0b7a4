"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.evaluateChartPosition = evaluateChartPosition;
exports.evaluateYear = evaluateYear;
function evaluateChartPosition(userAnswer, correctAnswer) {
    const userPos = parseInt(String(userAnswer));
    const correctPos = parseInt(String(correctAnswer));
    if (isNaN(userPos) || isNaN(correctPos)) {
        return { isCorrect: false, pointsMultiplier: 0, difference: NaN };
    }
    const diff = Math.abs(userPos - correctPos);
    let multiplier = 0;
    if (diff === 0) {
        multiplier = 1.0;
    }
    else if (diff <= 2) {
        multiplier = 0.7;
    }
    else if (diff <= 4) {
        multiplier = 0.4;
    }
    return {
        isCorrect: diff === 0,
        pointsMultiplier: multiplier,
        difference: diff
    };
}
function evaluateYear(userAnswer, correctAnswer) {
    const userYear = parseInt(String(userAnswer));
    const correctYear = parseInt(String(correctAnswer));
    if (isNaN(userYear) || isNaN(correctYear)) {
        return { isCorrect: false, pointsMultiplier: 0, difference: NaN };
    }
    const diff = Math.abs(userYear - correctYear);
    let multiplier = 0;
    if (diff === 0)
        multiplier = 1.0;
    else if (diff <= 1)
        multiplier = 0.8;
    else if (diff <= 2)
        multiplier = 0.6;
    else if (diff <= 3)
        multiplier = 0.4;
    return { isCorrect: multiplier > 0, pointsMultiplier: multiplier, difference: diff };
}
//# sourceMappingURL=quiz-scoring.js.map