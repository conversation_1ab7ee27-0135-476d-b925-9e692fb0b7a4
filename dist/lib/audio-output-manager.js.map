{"version": 3, "file": "audio-output-manager.js", "sourceRoot": "", "sources": ["../../lib/audio-output-manager.ts"], "names": [], "mappings": ";;;AAyBA,MAAa,kBAAkB;IAM7B,YAAY,SAAoB,EAAE,gBAAuC,EAAE;QAHnE,qBAAgB,GAAkB,EAAE,CAAA;QACpC,kBAAa,GAA4B,IAAI,CAAA;QAGnD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,MAAM,GAAG;YACZ,IAAI,EAAE,MAAM;YACZ,eAAe,EAAE,EAAE;YACnB,UAAU,EAAE,EAAE;YACd,YAAY,EAAE,EAAE;YAChB,GAAG,aAAa;SACjB,CAAA;IACH,CAAC;IAKD,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;QAC5B,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAC/B,CAAC;IAKO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;YACnE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAA;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;YACvD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKO,YAAY,CAAC,QAAgB;QACnC,MAAM,OAAO,GAAkB,EAAE,CAAA;QACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAClC,IAAI,aAAa,GAAyB,EAAE,CAAA;QAE5C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAClC,IAAI,aAAa,CAAC,EAAE,EAAE,CAAC;oBACrB,OAAO,CAAC,IAAI,CAAC,aAA4B,CAAC,CAAA;gBAC5C,CAAC;gBACD,aAAa,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YAC7C,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC3C,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1C,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC9C,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAA;YACrD,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAQ,CAAA;YACjD,CAAC;QACH,CAAC;QAED,IAAI,aAAa,CAAC,EAAE,EAAE,CAAC;YACrB,OAAO,CAAC,IAAI,CAAC,aAA4B,CAAC,CAAA;QAC5C,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAKO,KAAK,CAAC,gBAAgB;QAC5B,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACzB,KAAK,MAAM;gBACT,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;gBAC7B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;gBAChC,MAAK;YACP,KAAK,QAAQ;gBACX,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;gBAC9B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;gBAC/B,MAAK;YACP,KAAK,MAAM;gBACT,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;gBAC7B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;gBAC/B,MAAK;QACT,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,gBAAgB;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACnD,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,CACxC,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;gBAC7D,OAAO,CAAC,GAAG,CAAC,wBAAwB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;YACpD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,gCAAgC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAA;YACrE,CAAC;QACH,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACnD,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,CACxC,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,iBAAiB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;gBAC9D,OAAO,CAAC,GAAG,CAAC,yBAAyB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;YACrD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,iCAAiC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAA;YACtE,CAAC;QACH,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,kBAAkB;QAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAA;QAE3E,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;gBAC7D,OAAO,CAAC,GAAG,CAAC,0BAA0B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;gBAGpD,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;YACtC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,kCAAkC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAA;YACvE,CAAC;QACH,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,mBAAmB;QAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAA;QAE3E,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,iBAAiB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;gBAC9D,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;YACvD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,mCAAmC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAA;YACxE,CAAC;QACH,CAAC;QAGD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;YAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QAC3B,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,iBAAiB,CAAC,MAAmB;QAEjD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS;YACrC,UAAU,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,WAAW,QAAQ,CAAA;QAEnE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,IAAI,CAAC,aAAa,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,CAAA;YACzC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,WAAW,CAAA;YAC5C,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,GAAG,CAAA;YAC1D,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAA;YAElC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gBACjD,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAA;YAC5C,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,EAAE;gBACpD,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAA;YACvC,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,IAAgB;QAClC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAA;QACvB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAC/B,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,IAAuB,EAAE,MAAc;QACrD,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAA;QAExD,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,aAAa,CAAA;YACtC,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,aAAa,CAAA;YACxC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,aAAa,GAAG,GAAG,CAAA;YACjD,CAAC;QACH,CAAC;IACH,CAAC;IAKD,mBAAmB;QACjB,OAAO,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAA;IACnC,CAAC;IAKD,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;IAC3B,CAAC;IAKD,oBAAoB;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAA;IAC5D,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;YACzF,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAA;YACjC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;gBAC/C,MAAM,KAAK,CAAA;YACb,CAAC;QACH,CAAC;IACH,CAAC;IAKD,UAAU;QACR,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;YAC1B,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,CAAC,CAAA;QACpC,CAAC;IACH,CAAC;IAKD,eAAe;QACb,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAA;QAC7C,CAAC;QAED,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,CAAC;YAC7C,OAAO,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM;YACnC,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG;SAC5B,CAAA;IACH,CAAC;CACF;AAjRD,gDAiRC"}