"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.measure = exports.measureAsync = exports.recordRender = exports.recordMetric = exports.performanceMonitor = void 0;
exports.useRenderMetrics = useRenderMetrics;
const react_1 = require("react");
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.renderMetrics = [];
        this.isEnabled = process.env.NODE_ENV === 'development';
        this.maxMetrics = 1000;
        this.maxRenderMetrics = 500;
        if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
            this.initializeObservers();
        }
    }
    static getInstance() {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor();
        }
        return PerformanceMonitor.instance;
    }
    initializeObservers() {
        try {
            const longTaskObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordMetric('longTask', entry.duration, 'ms');
                }
            });
            longTaskObserver.observe({ entryTypes: ['longtask'] });
        }
        catch (e) {
        }
        try {
            const layoutShiftObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if ('value' in entry) {
                        this.recordMetric('layoutShift', entry.value);
                    }
                }
            });
            layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
        }
        catch (e) {
        }
    }
    recordMetric(name, value, unit) {
        if (!this.isEnabled)
            return;
        const metric = {
            name,
            value,
            timestamp: Date.now(),
            unit
        };
        if (!this.metrics.has(name)) {
            this.metrics.set(name, []);
        }
        const metricArray = this.metrics.get(name);
        metricArray.push(metric);
        if (metricArray.length > this.maxMetrics) {
            metricArray.splice(0, metricArray.length - this.maxMetrics);
        }
    }
    recordRender(componentName, renderTime, props) {
        if (!this.isEnabled)
            return;
        this.renderMetrics.push({
            componentName,
            renderTime,
            timestamp: Date.now(),
            props
        });
        if (this.renderMetrics.length > this.maxRenderMetrics) {
            this.renderMetrics.splice(0, this.renderMetrics.length - this.maxRenderMetrics);
        }
        this.recordMetric(`render_${componentName}`, renderTime, 'ms');
    }
    async measureAsync(name, fn) {
        if (!this.isEnabled)
            return fn();
        const start = performance.now();
        try {
            const result = await fn();
            const duration = performance.now() - start;
            this.recordMetric(name, duration, 'ms');
            return result;
        }
        catch (error) {
            const duration = performance.now() - start;
            this.recordMetric(`${name}_error`, duration, 'ms');
            throw error;
        }
    }
    measure(name, fn) {
        if (!this.isEnabled)
            return fn();
        const start = performance.now();
        try {
            const result = fn();
            const duration = performance.now() - start;
            this.recordMetric(name, duration, 'ms');
            return result;
        }
        catch (error) {
            const duration = performance.now() - start;
            this.recordMetric(`${name}_error`, duration, 'ms');
            throw error;
        }
    }
    getMetricsSummary(metricName) {
        if (metricName) {
            const metrics = this.metrics.get(metricName) || [];
            return this.calculateStats(metrics.map(m => m.value));
        }
        const summary = {};
        for (const [name, metrics] of this.metrics.entries()) {
            summary[name] = this.calculateStats(metrics.map(m => m.value));
        }
        return summary;
    }
    getRenderSummary(componentName) {
        let renderTimes = this.renderMetrics;
        if (componentName) {
            renderTimes = renderTimes.filter(m => m.componentName === componentName);
        }
        const grouped = renderTimes.reduce((acc, metric) => {
            if (!acc[metric.componentName]) {
                acc[metric.componentName] = [];
            }
            acc[metric.componentName].push(metric.renderTime);
            return acc;
        }, {});
        const summary = {};
        for (const [component, times] of Object.entries(grouped)) {
            summary[component] = this.calculateStats(times);
        }
        return summary;
    }
    calculateStats(values) {
        if (values.length === 0) {
            return { count: 0, mean: 0, min: 0, max: 0, p50: 0, p90: 0, p99: 0 };
        }
        const sorted = [...values].sort((a, b) => a - b);
        const sum = values.reduce((a, b) => a + b, 0);
        return {
            count: values.length,
            mean: sum / values.length,
            min: sorted[0],
            max: sorted[sorted.length - 1],
            p50: sorted[Math.floor(sorted.length * 0.5)],
            p90: sorted[Math.floor(sorted.length * 0.9)],
            p99: sorted[Math.floor(sorted.length * 0.99)]
        };
    }
    logReport() {
        if (!this.isEnabled)
            return;
        console.group('🎵 Music Quiz Performance Report');
        console.group('📊 Metrics Summary');
        console.table(this.getMetricsSummary());
        console.groupEnd();
        console.group('🎨 Render Performance');
        console.table(this.getRenderSummary());
        console.groupEnd();
        console.group('🖼️ Image Preloader Stats');
        if (typeof window !== 'undefined' && window.imagePreloader) {
            console.log(window.imagePreloader.getStats());
        }
        console.groupEnd();
        console.groupEnd();
    }
    clear() {
        this.metrics.clear();
        this.renderMetrics = [];
    }
    setEnabled(enabled) {
        this.isEnabled = enabled;
    }
}
exports.performanceMonitor = PerformanceMonitor.getInstance();
const recordMetric = (name, value, unit) => exports.performanceMonitor.recordMetric(name, value, unit);
exports.recordMetric = recordMetric;
const recordRender = (componentName, renderTime, props) => exports.performanceMonitor.recordRender(componentName, renderTime, props);
exports.recordRender = recordRender;
const measureAsync = (name, fn) => exports.performanceMonitor.measureAsync(name, fn);
exports.measureAsync = measureAsync;
const measure = (name, fn) => exports.performanceMonitor.measure(name, fn);
exports.measure = measure;
function useRenderMetrics(componentName, props) {
    (0, react_1.useEffect)(() => {
        const renderTime = performance.now();
        return () => {
            const duration = performance.now() - renderTime;
            (0, exports.recordRender)(componentName, duration, props);
        };
    }, [componentName, props]);
}
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    window.performanceMonitor = exports.performanceMonitor;
}
//# sourceMappingURL=performance-monitor.js.map