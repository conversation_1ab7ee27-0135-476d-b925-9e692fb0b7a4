import { VotingOption, VotingType } from './types';
export interface VotingPreset {
    id: string;
    type: VotingType;
    title: string;
    description: string;
    options: VotingOption[];
    defaultTimeLimit: number;
    tags: string[];
}
export declare const CATEGORY_PRESETS: VotingPreset[];
export declare const DECADE_PRESETS: VotingPreset[];
export declare const GAME_MODE_PRESETS: VotingPreset[];
export declare const THEME_PRESETS: VotingPreset[];
export declare const CUSTOM_PRESETS: VotingPreset[];
export declare const ALL_PRESETS: VotingPreset[];
export declare const getPresetsByType: (type: VotingType) => VotingPreset[];
export declare const getPresetById: (id: string) => VotingPreset | undefined;
export declare const getPresetsByTag: (tag: string) => VotingPreset[];
export declare const getRandomPreset: (type?: VotingType) => VotingPreset;
export declare const createCustomVoting: (type: VotingType, title: string, description: string, options: VotingOption[], timeLimit?: number) => VotingPreset;
