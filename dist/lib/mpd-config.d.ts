import type { MPDConfig } from './mpd-client';
export declare const MPD_CONFIG: MPDConfig;
export declare const MPD_SETTINGS: {
    maxReconnectAttempts: number;
    reconnectDelay: number;
    defaultVolume: number;
    maxVolume: number;
    maxQueueSize: number;
    searchLimit: number;
    autoUpdateDatabase: boolean;
    updateInterval: number;
};
export declare const getEnvironmentConfig: () => MPDConfig;
export declare const validateMPDConfig: (config: MPDConfig) => boolean;
export declare const getMPDConnectionString: (config: MPDConfig) => string;
export default MPD_CONFIG;
