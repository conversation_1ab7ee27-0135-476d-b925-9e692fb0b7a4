export interface Achievement {
    id: string;
    name: string;
    description: string;
    icon: string;
    category: 'quiz' | 'jukebox' | 'social' | 'special';
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
    points: number;
    unlockedAt?: Date;
    progress?: number;
    maxProgress?: number;
}
export declare const ACHIEVEMENTS: Record<string, Omit<Achievement, 'unlockedAt' | 'progress'>>;
export declare function checkQuizAchievements(gameResults: any, userStats: any): string[];
export declare function checkJukeboxAchievements(action: string, userStats: any): string[];
