export interface PlaylistAnalytics {
    playlistId: string;
    totalPlays: number;
    uniqueListeners: number;
    averageSessionLength: number;
    skipRate: number;
    completionRate: number;
    topTracks: Array<{
        trackId: string;
        title: string;
        artist: string;
        playCount: number;
        skipCount: number;
        skipRate: number;
    }>;
    genreDistribution: Record<string, number>;
    temporalPatterns: {
        hourlyPlayCounts: number[];
        dailyPlayCounts: number[];
        weeklyPlayCounts: number[];
    };
    userEngagement: {
        averageRating: number;
        favoriteCount: number;
        shareCount: number;
        collaboratorCount: number;
    };
    recommendations: {
        similarPlaylists: Array<{
            playlistId: string;
            name: string;
            similarity: number;
            sharedTracks: number;
        }>;
        suggestedTracks: Array<{
            trackId: string;
            title: string;
            artist: string;
            confidence: number;
            reason: string;
        }>;
    };
}
export interface PlaylistRecommendation {
    trackId: string;
    title: string;
    artist: string;
    genre?: string;
    year?: number;
    confidence: number;
    reasons: string[];
    similarityScore: number;
}
export declare class PlaylistAnalyticsEngine {
    private static instance;
    private constructor();
    static getInstance(): PlaylistAnalyticsEngine;
    generateAnalytics(playlistId: string): Promise<PlaylistAnalytics>;
    generateRecommendations(playlistId: string): Promise<PlaylistAnalytics['recommendations']>;
    generateTrackRecommendations(playlistId: string): Promise<PlaylistRecommendation[]>;
    private findSimilarPlaylists;
    private getPlaylistDetails;
    private getPlayStatistics;
    private getTrackStatistics;
    private getUserEngagement;
    private getTemporalPatterns;
}
export declare const playlistAnalytics: PlaylistAnalyticsEngine;
