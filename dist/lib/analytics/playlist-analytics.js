"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.playlistAnalytics = exports.PlaylistAnalyticsEngine = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
const connection_recovery_1 = require("@/lib/database/connection-recovery");
const logger_1 = require("@/lib/logger");
class PlaylistAnalyticsEngine {
    constructor() { }
    static getInstance() {
        if (!PlaylistAnalyticsEngine.instance) {
            PlaylistAnalyticsEngine.instance = new PlaylistAnalyticsEngine();
        }
        return PlaylistAnalyticsEngine.instance;
    }
    async generateAnalytics(playlistId) {
        try {
            logger_1.databaseLogger.info('Generating playlist analytics', { playlistId });
            const [playlist, playStats, trackStats, userEngagement, temporalData] = await Promise.all([
                this.getPlaylistDetails(playlistId),
                this.getPlayStatistics(playlistId),
                this.getTrackStatistics(playlistId),
                this.getUserEngagement(playlistId),
                this.getTemporalPatterns(playlistId)
            ]);
            if (!playlist) {
                throw new Error('Playlist not found');
            }
            const recommendations = await this.generateRecommendations(playlistId);
            const analytics = {
                playlistId,
                totalPlays: playStats.totalPlays,
                uniqueListeners: playStats.uniqueListeners,
                averageSessionLength: playStats.averageSessionLength,
                skipRate: playStats.skipRate,
                completionRate: playStats.completionRate,
                topTracks: trackStats.topTracks,
                genreDistribution: trackStats.genreDistribution,
                temporalPatterns: temporalData,
                userEngagement,
                recommendations
            };
            logger_1.databaseLogger.info('Playlist analytics generated successfully', {
                playlistId,
                totalPlays: analytics.totalPlays,
                recommendationCount: analytics.recommendations.suggestedTracks.length
            });
            return analytics;
        }
        catch (error) {
            logger_1.databaseLogger.error('Failed to generate playlist analytics', error, { playlistId });
            throw error;
        }
    }
    async generateRecommendations(playlistId) {
        const [similarPlaylists, suggestedTracks] = await Promise.all([
            this.findSimilarPlaylists(playlistId),
            this.generateTrackRecommendations(playlistId)
        ]);
        return {
            similarPlaylists,
            suggestedTracks
        };
    }
    async generateTrackRecommendations(playlistId) {
        return await connection_recovery_1.dbRecovery.executeWithRecovery(async () => {
            const playlistTracks = await prisma_1.default.playlistTrack.findMany({
                where: { playlistId },
                include: {
                    track: true
                }
            });
            if (playlistTracks.length === 0) {
                return [];
            }
            const genres = playlistTracks
                .map(pt => pt.track?.genre)
                .filter(Boolean);
            const years = playlistTracks
                .map(pt => pt.track?.year)
                .filter(Boolean);
            const artists = playlistTracks
                .map(pt => pt.track?.artist)
                .filter(Boolean);
            const genreCounts = genres.reduce((acc, genre) => {
                acc[genre] = (acc[genre] || 0) + 1;
                return acc;
            }, {});
            const dominantGenres = Object.entries(genreCounts)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 3)
                .map(([genre]) => genre);
            const minYear = Math.min(...years);
            const maxYear = Math.max(...years);
            const yearRange = maxYear - minYear;
            const uniqueArtists = new Set(artists);
            const excludeTrackIds = playlistTracks
                .map(pt => pt.trackId)
                .filter(Boolean);
            const candidateTracks = await prisma_1.default.quizTrack.findMany({
                where: {
                    AND: [
                        { id: { notIn: excludeTrackIds } },
                        { mpdFilePath: { not: null } },
                        {
                            OR: [
                                { genre: { in: dominantGenres } },
                                { artist: { in: artists.slice(0, 10) } },
                                {
                                    AND: [
                                        { year: { gte: minYear - 5 } },
                                        { year: { lte: maxYear + 5 } }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                take: 100,
                orderBy: [
                    { popularityScore: 'desc' },
                    { timesPlayed: 'asc' }
                ]
            });
            const recommendations = candidateTracks.map(track => {
                const reasons = [];
                let confidence = 0;
                if (track.genre && dominantGenres.includes(track.genre)) {
                    confidence += 0.4;
                    reasons.push(`Matches ${track.genre} genre`);
                }
                if (track.artist && artists.includes(track.artist)) {
                    confidence += 0.3;
                    reasons.push(`Same artist: ${track.artist}`);
                }
                if (track.year && track.year >= minYear && track.year <= maxYear) {
                    confidence += 0.2;
                    reasons.push(`Era match (${track.year})`);
                }
                if (uniqueArtists.size > 10 && track.popularityScore > 70) {
                    confidence += 0.1;
                    reasons.push('Popular track');
                }
                return {
                    trackId: track.id,
                    title: track.title || 'Unknown',
                    artist: track.artist || 'Unknown',
                    genre: track.genre,
                    year: track.year,
                    confidence: Math.min(confidence, 1.0),
                    reasons,
                    similarityScore: confidence
                };
            });
            return recommendations
                .filter(rec => rec.confidence > 0.3)
                .sort((a, b) => b.confidence - a.confidence)
                .slice(0, 20);
        }, 'generate-track-recommendations');
    }
    async findSimilarPlaylists(playlistId) {
        return await connection_recovery_1.dbRecovery.executeWithRecovery(async () => {
            const currentTracks = await prisma_1.default.playlistTrack.findMany({
                where: { playlistId },
                select: { trackId: true }
            });
            const currentTrackIds = currentTracks
                .map(t => t.trackId)
                .filter(Boolean);
            if (currentTrackIds.length === 0) {
                return [];
            }
            const similarPlaylists = await prisma_1.default.playlist.findMany({
                where: {
                    AND: [
                        { id: { not: playlistId } },
                        { isPublic: true },
                        {
                            tracks: {
                                some: {
                                    trackId: { in: currentTrackIds }
                                }
                            }
                        }
                    ]
                },
                include: {
                    tracks: {
                        select: { trackId: true }
                    },
                    _count: {
                        select: { tracks: true }
                    }
                },
                take: 10
            });
            return similarPlaylists.map(playlist => {
                const playlistTrackIds = playlist.tracks
                    .map(t => t.trackId)
                    .filter(Boolean);
                const sharedTracks = currentTrackIds.filter(id => playlistTrackIds.includes(id)).length;
                const totalUniqueItems = new Set([
                    ...currentTrackIds,
                    ...playlistTrackIds
                ]).size;
                const similarity = sharedTracks / totalUniqueItems;
                return {
                    playlistId: playlist.id,
                    name: playlist.name,
                    similarity: Math.round(similarity * 100) / 100,
                    sharedTracks
                };
            })
                .filter(p => p.sharedTracks > 0)
                .sort((a, b) => b.similarity - a.similarity)
                .slice(0, 5);
        }, 'find-similar-playlists');
    }
    async getPlaylistDetails(playlistId) {
        return await connection_recovery_1.dbRecovery.executeWithRecovery(async () => {
            return await prisma_1.default.playlist.findUnique({
                where: { id: playlistId },
                include: {
                    tracks: {
                        include: { track: true }
                    }
                }
            });
        }, 'get-playlist-details');
    }
    async getPlayStatistics(playlistId) {
        return {
            totalPlays: Math.floor(Math.random() * 1000),
            uniqueListeners: Math.floor(Math.random() * 500),
            averageSessionLength: Math.floor(Math.random() * 60) + 30,
            skipRate: Math.round(Math.random() * 30) / 100,
            completionRate: Math.round((70 + Math.random() * 30)) / 100
        };
    }
    async getTrackStatistics(playlistId) {
        return await connection_recovery_1.dbRecovery.executeWithRecovery(async () => {
            const tracks = await prisma_1.default.playlistTrack.findMany({
                where: { playlistId },
                include: { track: true },
                orderBy: { position: 'asc' }
            });
            const topTracks = tracks.slice(0, 10).map(pt => ({
                trackId: pt.trackId || '',
                title: pt.title,
                artist: pt.artist,
                playCount: pt.track?.timesPlayed || 0,
                skipCount: Math.floor(Math.random() * 10),
                skipRate: Math.round(Math.random() * 20) / 100
            }));
            const genreDistribution = tracks.reduce((acc, pt) => {
                const genre = pt.track?.genre || pt.genre || 'Unknown';
                acc[genre] = (acc[genre] || 0) + 1;
                return acc;
            }, {});
            return { topTracks, genreDistribution };
        }, 'get-track-statistics');
    }
    async getUserEngagement(playlistId) {
        return await connection_recovery_1.dbRecovery.executeWithRecovery(async () => {
            const [favoriteCount, shareCount, collaboratorCount] = await Promise.all([
                prisma_1.default.userFavorite.count({
                    where: {
                        filePath: {
                            in: await prisma_1.default.playlistTrack.findMany({
                                where: { playlistId },
                                select: { filePath: true }
                            }).then(tracks => tracks.map(t => t.filePath))
                        }
                    }
                }),
                prisma_1.default.playlistShare.count({
                    where: { playlistId, isActive: true }
                }),
                prisma_1.default.playlistCollaborator.count({
                    where: { playlistId, status: 'accepted' }
                })
            ]);
            return {
                averageRating: 4.2 + Math.random() * 0.8,
                favoriteCount,
                shareCount,
                collaboratorCount
            };
        }, 'get-user-engagement');
    }
    async getTemporalPatterns(playlistId) {
        return {
            hourlyPlayCounts: Array.from({ length: 24 }, () => Math.floor(Math.random() * 50)),
            dailyPlayCounts: Array.from({ length: 7 }, () => Math.floor(Math.random() * 200)),
            weeklyPlayCounts: Array.from({ length: 52 }, () => Math.floor(Math.random() * 1000))
        };
    }
}
exports.PlaylistAnalyticsEngine = PlaylistAnalyticsEngine;
PlaylistAnalyticsEngine.instance = null;
exports.playlistAnalytics = PlaylistAnalyticsEngine.getInstance();
//# sourceMappingURL=playlist-analytics.js.map