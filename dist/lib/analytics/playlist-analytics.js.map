{"version": 3, "file": "playlist-analytics.js", "sourceRoot": "", "sources": ["../../../lib/analytics/playlist-analytics.ts"], "names": [], "mappings": ";;;;;;AAKA,mEAA0C;AAC1C,4EAA+D;AAC/D,yCAA6C;AAyD7C,MAAa,uBAAuB;IAGlC,gBAAuB,CAAC;IAExB,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC;YACtC,uBAAuB,CAAC,QAAQ,GAAG,IAAI,uBAAuB,EAAE,CAAA;QAClE,CAAC;QACD,OAAO,uBAAuB,CAAC,QAAQ,CAAA;IACzC,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QACxC,IAAI,CAAC;YACH,uBAAc,CAAC,IAAI,CAAC,+BAA+B,EAAE,EAAE,UAAU,EAAE,CAAC,CAAA;YAEpE,MAAM,CACJ,QAAQ,EACR,SAAS,EACT,UAAU,EACV,cAAc,EACd,YAAY,CACb,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;gBACnC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;gBAClC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;gBACnC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;gBAClC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;aACrC,CAAC,CAAA;YAEF,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;YACvC,CAAC;YAGD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAA;YAEtE,MAAM,SAAS,GAAsB;gBACnC,UAAU;gBACV,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,eAAe,EAAE,SAAS,CAAC,eAAe;gBAC1C,oBAAoB,EAAE,SAAS,CAAC,oBAAoB;gBACpD,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,cAAc,EAAE,SAAS,CAAC,cAAc;gBACxC,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,iBAAiB,EAAE,UAAU,CAAC,iBAAiB;gBAC/C,gBAAgB,EAAE,YAAY;gBAC9B,cAAc;gBACd,eAAe;aAChB,CAAA;YAED,uBAAc,CAAC,IAAI,CAAC,2CAA2C,EAAE;gBAC/D,UAAU;gBACV,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,mBAAmB,EAAE,SAAS,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM;aACtE,CAAC,CAAA;YAEF,OAAO,SAAS,CAAA;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAc,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAc,EAAE,EAAE,UAAU,EAAE,CAAC,CAAA;YAC7F,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,UAAkB;QAC9C,MAAM,CAAC,gBAAgB,EAAE,eAAe,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC5D,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;YACrC,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC;SAC9C,CAAC,CAAA;QAEF,OAAO;YACL,gBAAgB;YAChB,eAAe;SAChB,CAAA;IACH,CAAC;IAKD,KAAK,CAAC,4BAA4B,CAAC,UAAkB;QACnD,OAAO,MAAM,gCAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YAErD,MAAM,cAAc,GAAG,MAAM,gBAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;gBACzD,KAAK,EAAE,EAAE,UAAU,EAAE;gBACrB,OAAO,EAAE;oBACP,KAAK,EAAE,IAAI;iBACZ;aACF,CAAC,CAAA;YAEF,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,OAAO,EAAE,CAAA;YACX,CAAC;YAGD,MAAM,MAAM,GAAG,cAAc;iBAC1B,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;iBAC1B,MAAM,CAAC,OAAO,CAAa,CAAA;YAE9B,MAAM,KAAK,GAAG,cAAc;iBACzB,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;iBACzB,MAAM,CAAC,OAAO,CAAa,CAAA;YAE9B,MAAM,OAAO,GAAG,cAAc;iBAC3B,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;iBAC3B,MAAM,CAAC,OAAO,CAAa,CAAA;YAG9B,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBAC/C,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;gBAClC,OAAO,GAAG,CAAA;YACZ,CAAC,EAAE,EAA4B,CAAC,CAAA;YAEhC,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;iBAC/C,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;iBAC3B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;iBACX,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;YAG1B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YAClC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YAClC,MAAM,SAAS,GAAG,OAAO,GAAG,OAAO,CAAA;YAGnC,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAA;YAGtC,MAAM,eAAe,GAAG,cAAc;iBACnC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC;iBACrB,MAAM,CAAC,OAAO,CAAa,CAAA;YAE9B,MAAM,eAAe,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;gBACtD,KAAK,EAAE;oBACL,GAAG,EAAE;wBACH,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE;wBAClC,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;wBAC9B;4BACE,EAAE,EAAE;gCACF,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE;gCACjC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;gCACxC;oCACE,GAAG,EAAE;wCACH,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,OAAO,GAAG,CAAC,EAAE,EAAE;wCAC9B,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,OAAO,GAAG,CAAC,EAAE,EAAE;qCAC/B;iCACF;6BACF;yBACF;qBACF;iBACF;gBACD,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE;oBACP,EAAE,eAAe,EAAE,MAAM,EAAE;oBAC3B,EAAE,WAAW,EAAE,KAAK,EAAE;iBACvB;aACF,CAAC,CAAA;YAGF,MAAM,eAAe,GAA6B,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBAC5E,MAAM,OAAO,GAAa,EAAE,CAAA;gBAC5B,IAAI,UAAU,GAAG,CAAC,CAAA;gBAGlB,IAAI,KAAK,CAAC,KAAK,IAAI,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBACxD,UAAU,IAAI,GAAG,CAAA;oBACjB,OAAO,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC,KAAK,QAAQ,CAAC,CAAA;gBAC9C,CAAC;gBAGD,IAAI,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;oBACnD,UAAU,IAAI,GAAG,CAAA;oBACjB,OAAO,CAAC,IAAI,CAAC,gBAAgB,KAAK,CAAC,MAAM,EAAE,CAAC,CAAA;gBAC9C,CAAC;gBAGD,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI,IAAI,OAAO,EAAE,CAAC;oBACjE,UAAU,IAAI,GAAG,CAAA;oBACjB,OAAO,CAAC,IAAI,CAAC,cAAc,KAAK,CAAC,IAAI,GAAG,CAAC,CAAA;gBAC3C,CAAC;gBAGD,IAAI,aAAa,CAAC,IAAI,GAAG,EAAE,IAAI,KAAK,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;oBAC1D,UAAU,IAAI,GAAG,CAAA;oBACjB,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;gBAC/B,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,KAAK,CAAC,EAAE;oBACjB,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,SAAS;oBAC/B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,SAAS;oBACjC,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;oBACrC,OAAO;oBACP,eAAe,EAAE,UAAU;iBAC5B,CAAA;YACH,CAAC,CAAC,CAAA;YAGF,OAAO,eAAe;iBACnB,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;iBACnC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;iBAC3C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QAEjB,CAAC,EAAE,gCAAgC,CAAC,CAAA;IACtC,CAAC;IAKO,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QACnD,OAAO,MAAM,gCAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YAErD,MAAM,aAAa,GAAG,MAAM,gBAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;gBACxD,KAAK,EAAE,EAAE,UAAU,EAAE;gBACrB,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAA;YAEF,MAAM,eAAe,GAAG,aAAa;iBAClC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;iBACnB,MAAM,CAAC,OAAO,CAAa,CAAA;YAE9B,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,OAAO,EAAE,CAAA;YACX,CAAC;YAGD,MAAM,gBAAgB,GAAG,MAAM,gBAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACtD,KAAK,EAAE;oBACL,GAAG,EAAE;wBACH,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE;wBAC3B,EAAE,QAAQ,EAAE,IAAI,EAAE;wBAClB;4BACE,MAAM,EAAE;gCACN,IAAI,EAAE;oCACJ,OAAO,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE;iCACjC;6BACF;yBACF;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;qBAC1B;oBACD,MAAM,EAAE;wBACN,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;qBACzB;iBACF;gBACD,IAAI,EAAE,EAAE;aACT,CAAC,CAAA;YAGF,OAAO,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACrC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM;qBACrC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;qBACnB,MAAM,CAAC,OAAO,CAAa,CAAA;gBAE9B,MAAM,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAC/C,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAC9B,CAAC,MAAM,CAAA;gBAER,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC;oBAC/B,GAAG,eAAe;oBAClB,GAAG,gBAAgB;iBACpB,CAAC,CAAC,IAAI,CAAA;gBAEP,MAAM,UAAU,GAAG,YAAY,GAAG,gBAAgB,CAAA;gBAElD,OAAO;oBACL,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;oBAC9C,YAAY;iBACb,CAAA;YACH,CAAC,CAAC;iBACD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC;iBAC/B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;iBAC3C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAEd,CAAC,EAAE,wBAAwB,CAAC,CAAA;IAC9B,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,UAAkB;QACjD,OAAO,MAAM,gCAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YACrD,OAAO,MAAM,gBAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACtC,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;gBACzB,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;qBACzB;iBACF;aACF,CAAC,CAAA;QACJ,CAAC,EAAE,sBAAsB,CAAC,CAAA;IAC5B,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QAEhD,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;YAC5C,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;YAChD,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;YACzD,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG;YAC9C,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG;SAC5D,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,UAAkB;QACjD,OAAO,MAAM,gCAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YACrD,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;gBACjD,KAAK,EAAE,EAAE,UAAU,EAAE;gBACrB,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;gBACxB,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;aAC7B,CAAC,CAAA;YAEF,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC/C,OAAO,EAAE,EAAE,CAAC,OAAO,IAAI,EAAE;gBACzB,KAAK,EAAE,EAAE,CAAC,KAAK;gBACf,MAAM,EAAE,EAAE,CAAC,MAAM;gBACjB,SAAS,EAAE,EAAE,CAAC,KAAK,EAAE,WAAW,IAAI,CAAC;gBACrC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC;gBACzC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG;aAC/C,CAAC,CAAC,CAAA;YAEH,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE;gBAClD,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,SAAS,CAAA;gBACtD,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;gBAClC,OAAO,GAAG,CAAA;YACZ,CAAC,EAAE,EAA4B,CAAC,CAAA;YAEhC,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,CAAA;QACzC,CAAC,EAAE,sBAAsB,CAAC,CAAA;IAC5B,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QAChD,OAAO,MAAM,gCAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YACrD,MAAM,CAAC,aAAa,EAAE,UAAU,EAAE,iBAAiB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAEvE,gBAAM,CAAC,YAAY,CAAC,KAAK,CAAC;oBACxB,KAAK,EAAE;wBACL,QAAQ,EAAE;4BACR,EAAE,EAAE,MAAM,gBAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;gCACtC,KAAK,EAAE,EAAE,UAAU,EAAE;gCACrB,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;6BAC3B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;yBAC/C;qBACF;iBACF,CAAC;gBAGF,gBAAM,CAAC,aAAa,CAAC,KAAK,CAAC;oBACzB,KAAK,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE;iBACtC,CAAC;gBAGF,gBAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC;oBAChC,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE;iBAC1C,CAAC;aACH,CAAC,CAAA;YAEF,OAAO;gBACL,aAAa,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;gBACxC,aAAa;gBACb,UAAU;gBACV,iBAAiB;aAClB,CAAA;QACH,CAAC,EAAE,qBAAqB,CAAC,CAAA;IAC3B,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,UAAkB;QAElD,OAAO;YACL,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;YAClF,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;YACjF,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;SACrF,CAAA;IACH,CAAC;;AA9XH,0DA+XC;AA9XgB,gCAAQ,GAAmC,IAAI,CAAA;AAiYnD,QAAA,iBAAiB,GAAG,uBAAuB,CAAC,WAAW,EAAE,CAAA"}