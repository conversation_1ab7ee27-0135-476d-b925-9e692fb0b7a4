"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.teamManager = exports.TeamManager = void 0;
class TeamManager {
    constructor() {
        this.teams = new Map();
        this.teamColors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
            '#FECA57', '#FF9FF3', '#54A0FF', '#5F27CD',
            '#00D2D3', '#FF9F43', '#C44569', '#40407A'
        ];
        this.teamEmojis = [
            '🔥', '⚡', '🌟', '🎯', '🚀', '💎', '🏆', '⭐',
            '🎵', '🎸', '🥇', '🎪', '🌈', '💫', '🎭', '🎨'
        ];
    }
    createTeam(name, captainId, captainName) {
        const teamId = this.generateTeamId();
        const availableColor = this.getAvailableColor();
        const availableEmoji = this.getAvailableEmoji();
        const captain = {
            id: captainId,
            name: captain<PERSON><PERSON>,
            avatar: '',
            score: 0,
            isHost: false,
            hasAnswered: false,
            joinedAt: Date.now(),
            teamId: teamId,
            isTeamCaptain: true
        };
        const team = {
            id: teamId,
            name: name,
            color: availableColor,
            emoji: availableEmoji,
            players: [captain],
            score: 0,
            captainId: captainId,
            hasAnswered: false,
            createdAt: Date.now()
        };
        this.teams.set(teamId, team);
        return team;
    }
    addPlayerToTeam(teamId, player) {
        const team = this.teams.get(teamId);
        if (!team)
            return false;
        player.teamId = teamId;
        player.isTeamCaptain = false;
        team.players.push(player);
        this.teams.set(teamId, team);
        return true;
    }
    removePlayerFromTeam(playerId) {
        for (const [teamId, team] of this.teams.entries()) {
            const playerIndex = team.players.findIndex(p => p.id === playerId);
            if (playerIndex !== -1) {
                const removedPlayer = team.players[playerIndex];
                team.players.splice(playerIndex, 1);
                if (team.players.length === 0) {
                    this.teams.delete(teamId);
                    return null;
                }
                if (removedPlayer.isTeamCaptain && team.players.length > 0) {
                    team.players[0].isTeamCaptain = true;
                    team.captainId = team.players[0].id;
                }
                this.teams.set(teamId, team);
                return team;
            }
        }
        return null;
    }
    getAllTeams() {
        return Array.from(this.teams.values());
    }
    getTeam(teamId) {
        return this.teams.get(teamId);
    }
    getPlayerTeam(playerId) {
        for (const team of this.teams.values()) {
            if (team.players.some(p => p.id === playerId)) {
                return team;
            }
        }
        return undefined;
    }
    updateTeam(teamId, updates) {
        const team = this.teams.get(teamId);
        if (!team)
            return false;
        Object.assign(team, updates);
        this.teams.set(teamId, team);
        return true;
    }
    promoteToCaption(teamId, playerId) {
        const team = this.teams.get(teamId);
        if (!team)
            return false;
        const player = team.players.find(p => p.id === playerId);
        if (!player)
            return false;
        team.players.forEach(p => p.isTeamCaptain = false);
        player.isTeamCaptain = true;
        team.captainId = playerId;
        this.teams.set(teamId, team);
        return true;
    }
    submitTeamAnswer(teamAnswer) {
        const team = this.teams.get(teamAnswer.teamId);
        if (!team)
            return false;
        team.hasAnswered = true;
        team.lastAnswer = teamAnswer.answer;
        team.lastAnswerTime = teamAnswer.timestamp;
        this.teams.set(teamAnswer.teamId, team);
        return true;
    }
    calculateTeamScores(teamAnswers, correctAnswer, points, scoringMode) {
        for (const teamAnswer of teamAnswers) {
            const team = this.teams.get(teamAnswer.teamId);
            if (!team)
                continue;
            const isCorrect = teamAnswer.answer === correctAnswer;
            if (!isCorrect)
                continue;
            let teamPoints = 0;
            switch (scoringMode) {
                case 'sum':
                    teamPoints = points * team.players.length;
                    break;
                case 'average':
                    teamPoints = points;
                    break;
                case 'best':
                    teamPoints = points;
                    break;
                case 'captain':
                    if (teamAnswer.submittedBy === team.captainId) {
                        teamPoints = points;
                    }
                    break;
            }
            team.score += teamPoints;
            this.teams.set(teamAnswer.teamId, team);
        }
    }
    resetTeamAnswers() {
        for (const [teamId, team] of this.teams.entries()) {
            team.hasAnswered = false;
            team.lastAnswer = undefined;
            team.lastAnswerTime = undefined;
            this.teams.set(teamId, team);
        }
    }
    autoBalanceTeams(players, maxTeamSize = 3) {
        this.teams.clear();
        const numTeams = Math.ceil(players.length / maxTeamSize);
        const shuffledPlayers = [...players].sort(() => Math.random() - 0.5);
        for (let i = 0; i < numTeams; i++) {
            const teamName = `Team ${String.fromCharCode(65 + i)}`;
            const captain = shuffledPlayers[i * maxTeamSize];
            if (captain) {
                const team = this.createTeam(teamName, captain.id, captain.name);
                for (let j = 1; j < maxTeamSize && (i * maxTeamSize + j) < shuffledPlayers.length; j++) {
                    const player = shuffledPlayers[i * maxTeamSize + j];
                    this.addPlayerToTeam(team.id, player);
                }
            }
        }
        return this.getAllTeams();
    }
    disbandTeam(teamId) {
        const team = this.teams.get(teamId);
        if (!team)
            return false;
        team.players.forEach(player => {
            player.teamId = undefined;
            player.isTeamCaptain = false;
        });
        this.teams.delete(teamId);
        return true;
    }
    getTeamLeaderboard() {
        return this.getAllTeams().sort((a, b) => b.score - a.score);
    }
    allTeamsAnswered() {
        const teams = this.getAllTeams();
        return teams.length > 0 && teams.every(team => team.hasAnswered);
    }
    getAvailableColor() {
        const usedColors = new Set(Array.from(this.teams.values()).map(t => t.color));
        return this.teamColors.find(color => !usedColors.has(color)) || this.teamColors[0];
    }
    getAvailableEmoji() {
        const usedEmojis = new Set(Array.from(this.teams.values()).map(t => t.emoji));
        return this.teamEmojis.find(emoji => !usedEmojis.has(emoji)) || this.teamEmojis[0];
    }
    generateTeamId() {
        return `team_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    getTeamStats(teamId) {
        const team = this.teams.get(teamId);
        if (!team)
            return null;
        const allTeams = this.getTeamLeaderboard();
        const teamRank = allTeams.findIndex(t => t.id === teamId) + 1;
        return {
            totalPlayers: team.players.length,
            averageScore: team.players.length > 0 ? team.score / team.players.length : 0,
            answeredQuestions: team.hasAnswered ? 1 : 0,
            teamRank
        };
    }
}
exports.TeamManager = TeamManager;
exports.teamManager = new TeamManager();
//# sourceMappingURL=team-manager.js.map