"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.api = exports.apiClient = exports.APIError = void 0;
const auth_cookies_client_1 = require("./auth-cookies-client");
const network_fallback_1 = require("./utils/network-fallback");
class APIError extends Error {
    constructor(status, message, data) {
        super(message);
        this.status = status;
        this.data = data;
        this.name = 'APIError';
    }
}
exports.APIError = APIError;
class APIClient {
    constructor() {
        this.csrfToken = null;
        this.csrfTokenPromise = null;
    }
    static getInstance() {
        if (!APIClient.instance) {
            APIClient.instance = new APIClient();
        }
        return APIClient.instance;
    }
    async getCSRFToken() {
        if (this.csrfTokenPromise) {
            return this.csrfTokenPromise;
        }
        if (this.csrfToken) {
            return this.csrfToken;
        }
        this.csrfTokenPromise = this.fetchCSRFToken();
        try {
            this.csrfToken = await this.csrfTokenPromise;
            return this.csrfToken;
        }
        finally {
            this.csrfTokenPromise = null;
        }
    }
    async fetchCSRFToken() {
        const response = await (0, network_fallback_1.fetchWithFallback)('/api/auth/csrf', {
            credentials: 'include'
        });
        if (!response.ok) {
            throw new APIError(response.status, 'Failed to get CSRF token');
        }
        const data = await response.json();
        if (!data.success || !data.csrfToken) {
            throw new APIError(500, 'Invalid CSRF token response');
        }
        return data.csrfToken;
    }
    invalidateCSRFToken() {
        this.csrfToken = null;
        this.csrfTokenPromise = null;
    }
    async request(url, options = {}) {
        const userInfo = (0, auth_cookies_client_1.getClientUserInfo)();
        const needsCSRF = ['POST', 'PUT', 'DELETE', 'PATCH'].includes(options.method?.toUpperCase() || 'GET');
        let headers = {
            ...options.headers
        };
        if (needsCSRF) {
            try {
                const csrfToken = await this.getCSRFToken();
                headers['x-csrf-token'] = csrfToken;
            }
            catch (error) {
                console.error('Failed to get CSRF token:', error);
                throw error;
            }
        }
        const response = await (0, network_fallback_1.fetchWithFallback)(url, {
            ...options,
            headers,
            credentials: 'include'
        });
        if (response.status === 403) {
            const data = await response.json().catch(() => ({}));
            if (data.error?.includes('CSRF')) {
                this.invalidateCSRFToken();
                if (needsCSRF) {
                    const newToken = await this.getCSRFToken();
                    headers['x-csrf-token'] = newToken;
                    const retryResponse = await (0, network_fallback_1.fetchWithFallback)(url, {
                        ...options,
                        headers,
                        credentials: 'include'
                    });
                    if (!retryResponse.ok) {
                        throw new APIError(retryResponse.status, data.error || 'Request failed', data);
                    }
                    return retryResponse.json();
                }
            }
        }
        if (!response.ok) {
            const data = await response.json().catch(() => ({}));
            const networkStatus = (0, network_fallback_1.getNetworkStatus)();
            let errorMessage = data.error || data.message || 'Request failed';
            if (networkStatus.fallbackMode) {
                errorMessage = `Network issue: ${errorMessage}`;
            }
            throw new APIError(response.status, errorMessage, { ...data, networkFallback: networkStatus.fallbackMode });
        }
        return response.json();
    }
    async get(url, options) {
        return this.request(url, { ...options, method: 'GET' });
    }
    async post(url, body, options) {
        return this.request(url, {
            ...options,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            },
            body: body ? JSON.stringify(body) : undefined
        });
    }
    async put(url, body, options) {
        return this.request(url, {
            ...options,
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            },
            body: body ? JSON.stringify(body) : undefined
        });
    }
    async delete(url, options) {
        return this.request(url, { ...options, method: 'DELETE' });
    }
    async patch(url, body, options) {
        return this.request(url, {
            ...options,
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            },
            body: body ? JSON.stringify(body) : undefined
        });
    }
}
exports.apiClient = APIClient.getInstance();
exports.api = {
    get: (url, options) => exports.apiClient.get(url, options),
    post: (url, body, options) => exports.apiClient.post(url, body, options),
    put: (url, body, options) => exports.apiClient.put(url, body, options),
    delete: (url, options) => exports.apiClient.delete(url, options),
    patch: (url, body, options) => exports.apiClient.patch(url, body, options),
    request: (url, options) => exports.apiClient.request(url, options)
};
//# sourceMappingURL=api-client.js.map