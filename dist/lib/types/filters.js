"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FILTER_PRESETS = exports.DEFAULT_FILTERS = void 0;
exports.DEFAULT_FILTERS = {
    genres: {
        mode: 'include',
        values: []
    },
    playlists: {
        mode: 'include',
        values: []
    },
    yearRange: {
        enabled: false
    },
    charts: {
        includeChartMusic: true,
        includeNonChartMusic: true
    },
    quality: {},
    sources: {
        includeMyItunes: true,
        includeSharedLibrary: true,
        includePlaylists: []
    },
    metadata: {},
    folders: {
        mode: 'include',
        values: []
    }
};
exports.FILTER_PRESETS = [
    {
        id: 'all-content',
        name: 'All Content',
        description: 'No filters applied',
        filters: exports.DEFAULT_FILTERS,
        isDefault: true
    },
    {
        id: 'charts-only',
        name: 'Chart Hits Only',
        description: 'Only songs that charted',
        filters: {
            ...exports.DEFAULT_FILTERS,
            charts: {
                includeChartMusic: true,
                includeNonChartMusic: false
            }
        }
    },
    {
        id: 'recent-music',
        name: 'Recent Music',
        description: 'Songs from the last 10 years',
        filters: {
            ...exports.DEFAULT_FILTERS,
            yearRange: {
                enabled: true,
                min: new Date().getFullYear() - 10
            }
        }
    },
    {
        id: 'classic-rock',
        name: 'Classic Rock',
        description: 'Rock music from 1960-1990',
        filters: {
            ...exports.DEFAULT_FILTERS,
            genres: {
                mode: 'include',
                values: ['Rock', 'Classic Rock', 'Hard Rock', 'Progressive Rock']
            },
            yearRange: {
                enabled: true,
                min: 1960,
                max: 1990
            }
        }
    },
    {
        id: 'high-quality',
        name: 'High Quality Only',
        description: 'Well-tagged songs with album art',
        filters: {
            ...exports.DEFAULT_FILTERS,
            quality: {
                requireAlbumArt: true
            },
            metadata: {
                requireYear: true,
                requireGenre: true,
                requireAlbum: true
            }
        }
    },
    {
        id: 'all-time-favorites',
        name: 'All Time Favorites',
        description: 'Only the most iconic and beloved songs',
        filters: {
            ...exports.DEFAULT_FILTERS,
            playlists: {
                mode: 'include',
                values: ['all-time-favorites']
            }
        }
    },
    {
        id: 'myitunes-only',
        name: 'MyItunes Only',
        description: 'Only songs from your personal iTunes library',
        filters: {
            ...exports.DEFAULT_FILTERS,
            sources: {
                includeMyItunes: true,
                includeSharedLibrary: false,
                includePlaylists: []
            }
        }
    },
    {
        id: 'no-myitunes',
        name: 'No MyItunes',
        description: 'Exclude songs from personal iTunes library',
        filters: {
            ...exports.DEFAULT_FILTERS,
            sources: {
                includeMyItunes: false,
                includeSharedLibrary: true,
                includePlaylists: []
            }
        }
    }
];
//# sourceMappingURL=filters.js.map