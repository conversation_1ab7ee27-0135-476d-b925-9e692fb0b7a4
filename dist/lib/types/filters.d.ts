export interface ContentFilters {
    genres: {
        mode: 'include' | 'exclude';
        values: string[];
    };
    playlists: {
        mode: 'include' | 'exclude';
        values: string[];
    };
    yearRange: {
        enabled: boolean;
        min?: number;
        max?: number;
    };
    charts: {
        includeChartMusic: boolean;
        includeNonChartMusic: boolean;
        countries?: string[];
    };
    quality: {
        minDifficulty?: number;
        maxDifficulty?: number;
        minPopularity?: number;
        requireAlbumArt?: boolean;
    };
    sources: {
        includeMyItunes: boolean;
        includeSharedLibrary: boolean;
        includePlaylists: string[];
    };
    metadata: {
        requireYear?: boolean;
        requireGenre?: boolean;
        requireAlbum?: boolean;
        excludeCompilations?: boolean;
        excludeLiveRecordings?: boolean;
        excludeRemixes?: boolean;
    };
    folders: {
        mode: 'include' | 'exclude';
        values: string[];
    };
}
export interface FilterPreset {
    id: string;
    name: string;
    description?: string;
    filters: ContentFilters;
    isDefault?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface GameFilterSettings {
    enabled: boolean;
    activePresetId?: string;
    customFilters?: ContentFilters;
    applyToAllModes: boolean;
    modeSpecificFilters?: {
        [gameMode: string]: ContentFilters;
    };
}
export declare const DEFAULT_FILTERS: ContentFilters;
export declare const FILTER_PRESETS: FilterPreset[];
