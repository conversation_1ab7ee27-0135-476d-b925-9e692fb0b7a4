{"version": 3, "file": "bluetooth-manager.js", "sourceRoot": "", "sources": ["../../lib/bluetooth-manager.ts"], "names": [], "mappings": ";;;AAAA,iDAA2C;AAC3C,+BAAgC;AAChC,mCAAqC;AAErC,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,oBAAI,CAAC,CAAA;AAkBjC,MAAa,gBAAiB,SAAQ,qBAAY;IAIhD;QACE,KAAK,EAAE,CAAA;QAJD,aAAQ,GAAG,KAAK,CAAA;QAChB,kBAAa,GAA2B,IAAI,CAAA;IAIpD,CAAC;IAGD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YAEH,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;gBAClC,OAAO,KAAK,CAAA;YACd,CAAC;YAGD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,oDAAoD,CAAC,CAAA;YACxF,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;QACtC,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YAEH,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,SAAS,CAAC,wBAAwB,CAAC,CAAA;YACxE,IAAI,UAAU,CAAC,QAAQ,CAAC,uBAAuB,CAAC,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC7E,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA;gBAC5C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,CAAA;YAC/C,CAAC;YAED,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,mBAAmB,CAAC,CAAA;YACvD,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAA;YAC/C,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAA;YACvD,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,CAAA;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;YACrD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,CAAA;QAC/C,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,QAAQ,CAAC,MAAe;QAC5B,IAAI,CAAC;YAEH,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,SAAS,CAAC,wBAAwB,CAAC,CAAA;YACxE,IAAI,UAAU,CAAC,QAAQ,CAAC,uBAAuB,CAAC,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC7E,OAAO,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAA;gBAChE,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAA;YAChG,CAAC;YAED,MAAM,SAAS,CAAC,sBAAsB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;YAC9D,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;YACtD,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBAC7E,MAAM,KAAK,CAAA;YACb,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,SAAS,CAAC,WAAmB,EAAE;QACnC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAA;QACrC,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QACpB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAEvB,IAAI,CAAC;YAEH,MAAM,SAAS,CAAC,sBAAsB,CAAC,CAAA;YAGvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAA;YAGlE,MAAM,SAAS,CAAC,uBAAuB,CAAC,CAAA;YAGxC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;YAEvC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;YACrB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAA;YAEnC,OAAO,OAAO,CAAA;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;YACrB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;YAC9B,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,sBAAsB,CAAC,CAAA;YAC1D,MAAM,OAAO,GAAsB,EAAE,CAAA;YAErC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAA;gBACxD,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,KAAK,CAAA;oBAC3B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;oBAC5C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBACtB,CAAC;YACH,CAAC;YAED,OAAO,OAAO,CAAA;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;YAC9C,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,aAAa,CAAC,GAAW;QAC7B,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,qBAAqB,GAAG,EAAE,CAAC,CAAA;YAE9D,OAAO;gBACL,GAAG;gBACH,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,gBAAgB;gBAC3D,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC;gBAC5C,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC;gBACtC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC;gBACxC,WAAW,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;aAC1E,CAAA;QACH,CAAC;QAAC,MAAM,CAAC;YACP,OAAO;gBACL,GAAG;gBACH,IAAI,EAAE,gBAAgB;gBACtB,SAAS,EAAE,KAAK;gBAChB,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,KAAK;aACf,CAAA;QACH,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,aAAa,CAAC,GAAW;QAC7B,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAA;YAGnC,MAAM,SAAS,CAAC,sBAAsB,GAAG,EAAE,CAAC,CAAA;YAG5C,IAAI,CAAC;gBACH,MAAM,SAAS,CAAC,qBAAqB,GAAG,EAAE,CAAC,CAAA;YAC7C,CAAC;YAAC,MAAM,CAAC;YAET,CAAC;YAGD,MAAM,SAAS,CAAC,wBAAwB,GAAG,EAAE,CAAC,CAAA;YAG9C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAA;YAGvD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;YAC5C,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAA;gBAC3B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAA;gBAGrC,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;oBACvB,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA;gBACzC,CAAC;gBAED,OAAO,IAAI,CAAA;YACb,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAA;YAC9C,OAAO,KAAK,CAAA;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAA;YAChC,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,gBAAgB,CAAC,GAAY;QACjC,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC/B,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAA;YAC9B,CAAC;YAED,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,OAAO,KAAK,CAAA;YACd,CAAC;YAED,MAAM,SAAS,CAAC,2BAA2B,GAAG,EAAE,CAAC,CAAA;YACjD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;YACzB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAA;YAErC,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAA;YAC7C,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,oBAAoB,CAAC,MAAuB;QACxD,IAAI,CAAC;YAEH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,wBAAwB,CAAC,CAAA;YAC5D,MAAM,aAAa,GAAG,MAAM;iBACzB,KAAK,CAAC,IAAI,CAAC;iBACX,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACrC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;YAElB,IAAI,aAAa,EAAE,CAAC;gBAElB,MAAM,SAAS,CAAC,0BAA0B,aAAa,EAAE,CAAC,CAAA;gBAG1D,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAA;gBAEzC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAA;YAChE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAA;YACzD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;QACjC,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,eAAe,CAAC,IAAY;QAKxC,IAAI,CAAC;YAEH,MAAM,SAAS,CAAC,4BAA4B,CAAC,CAAA;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;QAChD,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,SAAS;QACb,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC7C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;QACvC,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QAEtD,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,OAAO;YACP,eAAe;SAChB,CAAA;IACH,CAAC;IAGO,YAAY,CAAC,MAAc,EAAE,GAAW;QAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAA;QACzD,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;IACvC,CAAC;IAGD,KAAK,CAAC,YAAY,CAAC,GAAW;QAC5B,IAAI,CAAC;YACH,MAAM,SAAS,CAAC,uBAAuB,GAAG,EAAE,CAAC,CAAA;YAC7C,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAA;YAChC,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;YAChD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;YACvC,MAAM,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,WAAW,CAAC,CAAA;YAEtE,IAAI,iBAAiB,EAAE,CAAC;gBACtB,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;YACxD,CAAC;YAED,OAAO,KAAK,CAAA;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAA;YAC5C,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;CACF;AAzSD,4CAySC;AAGY,QAAA,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAA"}