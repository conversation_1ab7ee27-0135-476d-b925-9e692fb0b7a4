{"version": 3, "file": "voting-manager.js", "sourceRoot": "", "sources": ["../../lib/voting-manager.ts"], "names": [], "mappings": ";;;AAOA,MAAa,aAAa;IAA1B;QACU,mBAAc,GAA+B,IAAI,GAAG,EAAE,CAAA;QACtD,iBAAY,GAA8B,IAAI,GAAG,EAAE,CAAA;QACnD,kBAAa,GAAgC,IAAI,GAAG,EAAE,CAAA;IA2UhE,CAAC;IAtUC,mBAAmB,CACjB,SAAiB,EACjB,IAAgB,EAChB,KAAa,EACb,WAAmB,EACnB,OAAuB,EACvB,YAAoB,EAAE,EACtB,YAAoB,EACpB,MAAe;QAEf,MAAM,OAAO,GAAkB;YAC7B,EAAE,EAAE,SAAS;YACb,IAAI;YACJ,KAAK;YACL,WAAW;YACX,OAAO;YACP,SAAS;YACT,YAAY;YACZ,MAAM;YACN,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;SAC3C,CAAA;QAED,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QAC3C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;QAGpC,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAChC,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,CAAA;QAEpB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;QAExC,OAAO,OAAO,CAAA;IAChB,CAAC;IAKD,UAAU,CAAC,SAAiB,EAAE,QAAgB,EAAE,UAAkB,EAAE,WAAmB;QACrF,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAClD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC5C,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC7D,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;QAGpD,MAAM,iBAAiB,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAA;QAE7E,MAAM,IAAI,GAAe;YACvB,QAAQ;YACR,UAAU;YACV,WAAW;YACX,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;QAED,IAAI,iBAAiB,IAAI,CAAC,EAAE,CAAC;YAE3B,KAAK,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAA;QACjC,CAAC;aAAM,CAAC;YAEN,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClB,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;QAGvC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;QAGrB,IAAI,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAChC,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,gBAAgB,CAAC,SAAiB;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAClD,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAA;QAGzB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;QACpD,OAAO;YACL,GAAG,OAAO;YACV,KAAK;SACN,CAAA;IACH,CAAC;IAKD,aAAa,CAAC,SAAiB,EAAE,QAAgB;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;QACpD,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAA;QACjE,OAAO,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAA;IACnD,CAAC;IAKD,cAAc,CAAC,SAAiB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAClD,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAA;QAGzB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC/C,IAAI,KAAK,EAAE,CAAC;YACV,YAAY,CAAC,KAAK,CAAC,CAAA;YACnB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QACtC,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QAG3D,OAAO,CAAC,MAAM,GAAG,WAAW,CAAA;QAC5B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAA;QACvB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;QAErB,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,UAAU,CAAC,SAAiB,EAAE,MAAc;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAClD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAA;QACb,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;IACvC,CAAC;IAKO,eAAe,CAAC,OAAuB,EAAE,KAAmB;QAClE,MAAM,UAAU,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACpD,MAAM,WAAW,GAA4C,EAAE,CAAA;QAG/D,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;YAC3B,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;QACzB,CAAC,CAAC,CAAA;QAGF,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;gBAC/D,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAA;gBAC9B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1C,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAA;QACxC,MAAM,cAAc,GAAG,UAAU;aAC9B,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;aACzC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;aACvC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAG1B,MAAM,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,CAAA;QACrC,MAAM,KAAK,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAA;QAEvC,MAAM,MAAM,GAAiB;YAC3B,WAAW;YACX,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC;YAClC,UAAU,EAAE,KAAK,CAAC,MAAM;YACxB,UAAU;YACV,WAAW;YACX,KAAK;YACL,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS;SAChD,CAAA;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,cAAc,CAAC,SAAiB;QAK9B,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;QAChD,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAA;QAEzB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAA;QAC/B,MAAM,UAAU,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAE5D,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACvE,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAA;YAChC,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,MAAM,eAAe,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAC7C,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAChD,CAAA;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAA;QACxC,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAEnD,OAAO;YACL,UAAU;YACV,eAAe;YACf,SAAS,EAAE;gBACT,KAAK,EAAE,cAAc;gBACrB,KAAK,EAAE,QAAQ;gBACf,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;aAC/D;SACF,CAAA;IACH,CAAC;IAKD,iBAAiB;QACf,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;aAC5C,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAA;IACnD,CAAC;IAKD,sBAAsB;QACpB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,eAAe,GAAa,EAAE,CAAA;QAEpC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;YACjD,IAAI,OAAO,CAAC,SAAS,GAAG,GAAG,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC3D,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;gBAC9B,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACjC,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAKD,aAAa,CAAC,SAAiB;QAE7B,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC/C,IAAI,KAAK,EAAE,CAAC;YACV,YAAY,CAAC,KAAK,CAAC,CAAA;YACnB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QACtC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QACrC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IACrC,CAAC;IAKD,gBAAgB,CAAC,SAAiB;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAClD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ;YAAE,OAAO,CAAC,CAAA;QAErD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;QAC7D,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAA;IACpC,CAAC;IAKD,cAAc,CAAC,SAAiB,EAAE,QAAgB;QAChD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;QACpD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAA;IACvD,CAAC;IAKD,oBAAoB,CAAC,SAAiB;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAClD,IAAI,CAAC,OAAO;YAAE,OAAO,CAAC,CAAA;QAEtB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;QACpD,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IACnF,CAAC;IAKD,MAAM,CAAC,2BAA2B;QAChC,OAAO;YACL,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,wBAAwB,EAAE,KAAK,EAAE,IAAI,EAAE;YAC5F,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,6BAA6B,EAAE,KAAK,EAAE,IAAI,EAAE;YAC/F,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAE;YAC1F,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,wBAAwB,EAAE,KAAK,EAAE,IAAI,EAAE;SAClG,CAAA;IACH,CAAC;IAED,MAAM,CAAC,yBAAyB;QAC9B,OAAO;YACL,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,sBAAsB,EAAE,KAAK,EAAE,IAAI,EAAE;YACrF,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,sBAAsB,EAAE,KAAK,EAAE,IAAI,EAAE;YACzF,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,sBAAsB,EAAE,KAAK,EAAE,IAAI,EAAE;YACxF,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE;SACrF,CAAA;IACH,CAAC;IAED,MAAM,CAAC,2BAA2B;QAChC,OAAO;YACL,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,wBAAwB,EAAE,KAAK,EAAE,IAAI,EAAE;YAC/F,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,sBAAsB,EAAE,KAAK,EAAE,GAAG,EAAE;YACzF,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,EAAE,KAAK,EAAE,IAAI,EAAE;YAC7F,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,iBAAiB,EAAE,KAAK,EAAE,IAAI,EAAE;SAChG,CAAA;IACH,CAAC;CACF;AA9UD,sCA8UC;AAEY,QAAA,aAAa,GAAG,IAAI,aAAa,EAAE,CAAA"}