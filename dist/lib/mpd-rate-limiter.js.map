{"version": 3, "file": "mpd-rate-limiter.js", "sourceRoot": "", "sources": ["../../lib/mpd-rate-limiter.ts"], "names": [], "mappings": ";;;AAWA,MAAa,cAAc;IAazB;QAXQ,YAAO,GAAgC,IAAI,GAAG,EAAE,CAAA;QAGvC,2BAAsB,GAAG,GAAG,CAAA;QAC5B,mCAA8B,GAAG,GAAG,CAAA;QAEpC,2BAAsB,GAAG,IAAI,CAAA;QAC7B,mCAA8B,GAAG,EAAE,CAAA;QAEnC,mBAAc,GAAG,KAAK,CAAA;IAEhB,CAAC;IAExB,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAA;QAChD,CAAC;QACD,OAAO,cAAc,CAAC,QAAQ,CAAA;IAChC,CAAC;IAOO,eAAe,CAAC,QAAgB;QAEtC,OAAO,QAAQ,KAAK,QAAQ;YACrB,QAAQ,KAAK,WAAW;YACxB,QAAQ,KAAK,KAAK;YAClB,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC;YAC9B,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;IACvC,CAAC;IAOD,cAAc,CAAC,QAAgB;QAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;QAG/C,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAA;QACxF,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAA;QAExG,IAAI,CAAC,KAAK,EAAE,CAAC;YAEX,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;gBACzB,WAAW,EAAE,GAAG;gBAChB,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,cAAc;aACrC,CAAC,CAAA;YACF,OAAO,IAAI,CAAA;QACb,CAAC;QAGD,IAAI,GAAG,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YAC3B,KAAK,CAAC,YAAY,GAAG,CAAC,CAAA;YACtB,KAAK,CAAC,WAAW,GAAG,GAAG,CAAA;YACvB,KAAK,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAA;YAC3C,OAAO,IAAI,CAAA;QACb,CAAC;QAGD,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,GAAG,WAAW,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAA;QACd,CAAC;QAGD,IAAI,KAAK,CAAC,YAAY,IAAI,WAAW,EAAE,CAAC;YACtC,OAAO,KAAK,CAAA;QACd,CAAC;QAGD,KAAK,CAAC,WAAW,GAAG,GAAG,CAAA;QACvB,KAAK,CAAC,YAAY,EAAE,CAAA;QACpB,OAAO,IAAI,CAAA;IACb,CAAC;IAOD,uBAAuB,CAAC,QAAgB;QACtC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;QAG/C,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAA;QACxF,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAA;QAExG,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,CAAC,CAAA;QACV,CAAC;QAGD,IAAI,KAAK,CAAC,YAAY,IAAI,WAAW,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,CAAA;QAC3C,CAAC;QAGD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAA;IAC7D,CAAC;IAKD,OAAO;QACL,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,cAAc,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;QAEpD,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,KAAK,CAAC,WAAW,GAAG,cAAc,EAAE,CAAC;gBACvC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC/B,CAAC;QACH,CAAC;IACH,CAAC;CACF;AA5HD,wCA4HC;AAGD,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE,CAAC;IACvC,WAAW,CAAC,GAAG,EAAE;QACf,cAAc,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAA;IACxC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;AACnB,CAAC"}