export interface JukeboxUser {
    id: string;
    name: string;
    displayName: string;
    username: string;
    email: string;
    avatar: string;
    role: User<PERSON><PERSON>;
    soundPreference: string;
    isGuest: boolean;
}
export interface Player {
    id: string;
    name: string;
    avatar: string;
    score: number;
    isHost: boolean;
    hasAnswered: boolean;
    joinedAt: number;
    lastAnswer?: number;
    lastAnswerTime?: number;
    teamId?: string;
    isTeamCaptain?: boolean;
    lastScoreChange?: number;
}
export interface Team {
    id: string;
    name: string;
    color: string;
    emoji: string;
    players: Player[];
    score: number;
    captainId: string;
    hasAnswered: boolean;
    lastAnswer?: number;
    lastAnswerTime?: number;
    createdAt: number;
}
export type TeamGameMode = 'collaborative' | 'relay' | 'specialist';
export type TeamScoringMode = 'average' | 'sum' | 'best' | 'captain';
export interface TeamGameSettings {
    teamMode: boolean;
    teamGameMode: TeamGameMode;
    teamScoringMode: TeamScoringMode;
    maxTeamSize: number;
    minTeamSize: number;
    allowTeamChat: boolean;
    collaborationTime: number;
    autoBalanceTeams: boolean;
}
export interface Question {
    id: string;
    type: 'multiple-choice' | 'slider' | 'true-false' | 'timeline-placement';
    audioFile: string;
    audioTitle?: string;
    audioArtist?: string;
    question: string;
    options?: string[];
    correctAnswer: number;
    correctAnswerText?: string;
    points: number;
    timeLimit: number;
    category?: string;
    difficulty?: number;
    year?: number;
    metadata?: any;
    track?: any;
}
export interface GameState {
    gameId: string;
    status: 'waiting' | 'countdown' | 'playing' | 'question-results' | 'finished';
    gameMode: string;
    players: Player[];
    teams?: Team[];
    teamMode?: boolean;
    questions: Question[];
    currentQuestionIndex: number;
    currentQuestion: Question | null;
    totalQuestions: number;
    timePerQuestion: number;
    createdAt: number;
    startedAt?: number;
    finishedAt?: number;
    hostId: string;
    teamSettings?: TeamGameSettings;
    settings: {
        maxPlayers: number;
        allowSpectators: boolean;
        autoNextQuestion: boolean;
    };
    hitsterState?: HitsterGameState;
}
export type GameMode = 'ultimote' | 'classic' | 'chart-position' | 'audio-manipulation' | 'decade-challenge' | 'genre-specialist' | 'guess-the-year' | 'album-art' | 'audio-fingerprint' | 'quick-fire' | 'hitster-timeline' | 'custom';
export type UserRole = "user" | "dj" | "superuser";
export interface UserProfile {
    id: string;
    name: string;
    displayName?: string;
    avatar: string;
    soundPreference?: string;
    isGuest?: boolean;
}
export interface Song {
    id: number | string;
    title: string;
    artist: string;
    album?: string;
    duration: number;
    genre?: string;
    year?: number;
    votes: number;
    suggested: boolean;
    suggestedBy?: string;
    addedBy: string;
    addedAt: number;
    filePath?: string;
    categories?: string[];
    albumArtUrl?: string;
}
export interface QueuedSong extends Song {
    queuePosition: number;
    addedBy: string;
    addedAt: number;
    timeLeft: number;
}
export interface SongSuggestion extends Song {
    suggestedBy: string;
    status: 'pending' | 'approved' | 'rejected';
    createdAt: number;
    timeRemaining: number;
}
export interface MPDTrack {
    file: string;
    time: number;
    title?: string;
    artist?: string;
    album?: string;
    genre?: string;
    date?: string;
    pos?: number;
    id?: number;
    votes?: number;
}
export interface TrackWithMetadata extends MPDTrack {
    quizId?: string;
    difficulty?: number;
    category?: string;
    year?: number;
    duration?: number;
    duration_formatted?: string;
    preview_start?: number;
    preview_duration?: number;
    calculatedGain?: number;
    peakVolume?: number;
    rmsVolume?: number;
    lufsVolume?: number;
    volumeAnalyzed?: boolean;
}
export interface MPDStatus {
    state: 'play' | 'pause' | 'stop';
    song?: number;
    songid?: number;
    time?: string;
    elapsed?: number;
    duration?: number;
    volume: number;
    repeat: boolean;
    random: boolean;
    single: boolean;
    consume: boolean;
    playlist: number;
    playlistlength: number;
    bitrate?: number;
    audio?: string;
    updating_db?: number;
    error?: string;
    xfade?: number;
}
export interface MPDPlaylist {
    name: string;
    last_modified: string;
}
export interface MPDConnectionConfig {
    host: string;
    port: number;
    password?: string;
    httpProxyPort?: number;
    timeout?: number;
}
export interface TeamSocketEvents {
    'create-team': (data: {
        teamName: string;
        color: string;
        emoji: string;
    }) => void;
    'join-team': (data: {
        teamId: string;
    }) => void;
    'leave-team': () => void;
    'update-team': (data: {
        teamId: string;
        name?: string;
        color?: string;
        emoji?: string;
    }) => void;
    'promote-to-captain': (data: {
        teamId: string;
        playerId: string;
    }) => void;
    'team-chat': (data: {
        teamId: string;
        message: string;
    }) => void;
    'team-answer': (data: {
        teamId: string;
        answer: number;
        collaborative?: boolean;
    }) => void;
    'team-ready': (data: {
        teamId: string;
    }) => void;
    'disband-team': (data: {
        teamId: string;
    }) => void;
    'auto-balance-teams': () => void;
    'toggle-team-mode': (data: {
        enabled: boolean;
        settings?: TeamGameSettings;
    }) => void;
}
export interface TeamChatMessage {
    id: string;
    teamId: string;
    playerId: string;
    playerName: string;
    message: string;
    timestamp: number;
    type: 'normal' | 'system' | 'strategy';
}
export interface TeamAnswer {
    teamId: string;
    answer: number;
    submittedBy: string;
    collaborative: boolean;
    discussionTime: number;
    timestamp: number;
}
export type VotingType = 'category' | 'decade' | 'game-mode' | 'theme' | 'next-round' | 'custom';
export interface VotingOption {
    label: string;
    value: string;
    description?: string;
    emoji?: string;
}
export interface PlayerVote {
    playerId: string;
    playerName: string;
    optionIndex: number;
    timestamp: number;
}
export interface VotingResult {
    winnerIndex: number;
    winnerOption: VotingOption;
    totalVotes: number;
    voteCounts: number[];
    voteDetails: {
        [optionIndex: number]: PlayerVote[];
    };
    isTie: boolean;
    tiedOptions?: number[];
}
export interface VotingSession {
    id: string;
    type: VotingType;
    title: string;
    description: string;
    options: VotingOption[];
    timeLimit: number;
    totalPlayers: number;
    hostId?: string;
    status: 'active' | 'completed' | 'cancelled';
    votes: PlayerVote[];
    result: VotingResult | null;
    createdAt: number;
    expiresAt: number;
}
export interface SkipVote {
    userId: string;
    userName: string;
    timestamp: number;
}
export interface SkipVoteStatus {
    trackId: string;
    trackTitle: string;
    trackArtist: string;
    votes: SkipVote[];
    totalVotes: number;
    connectedUsers: number;
    votesNeeded: number;
    percentage: number;
    isSkipping: boolean;
}
export interface HitsterGameState {
    timeline: TimelineEntry[];
    currentTeamIndex: number;
    teamTurns: string[];
    currentSong: TimelineSong | null;
    placementPhase: boolean;
    revealPhase: boolean;
    correctPlacements: number;
    totalPlacements: number;
    tokensPerTeam: Map<string, number>;
    roundNumber: number;
}
export interface TimelineEntry {
    id: string;
    song: TimelineSong;
    placedByTeamId: string;
    placedByPlayerName: string;
    position: number;
    isCorrect?: boolean;
    placedAt: number;
}
export interface TimelineSong {
    id: string;
    title: string;
    artist: string;
    year: number;
    audioFile: string;
    albumArt?: string;
    genre?: string;
    decade?: string;
}
export interface TimelinePlacement {
    songId: string;
    position: number;
    teamId: string;
    playerId: string;
    confidence?: number;
}
export interface HitsterSettings {
    startingTokens: number;
    yearsToGuess: boolean;
    showDecadeHints: boolean;
    penaltyForWrongPlacement: boolean;
    bonusForPerfectPlacement: boolean;
    maxTimelineLength: number;
    turnTimeLimit: number;
}
