export declare class ApiError extends Error {
    statusCode: number;
    code?: string | undefined;
    constructor(statusCode: number, message: string, code?: string | undefined);
}
export declare class ValidationError extends ApiError {
    errors?: Record<string, string[]> | undefined;
    constructor(message: string, errors?: Record<string, string[]> | undefined);
}
export declare class AuthenticationError extends ApiError {
    constructor(message?: string);
}
export declare class AuthorizationError extends ApiError {
    constructor(message?: string);
}
export declare class NotFoundError extends ApiError {
    constructor(resource: string);
}
export declare class ConflictError extends ApiError {
    constructor(message: string);
}
export declare class RateLimitError extends ApiError {
    constructor(message?: string);
}
export declare class ServerError extends ApiError {
    constructor(message?: string);
}
