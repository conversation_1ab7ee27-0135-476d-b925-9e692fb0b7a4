"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceLogger = exports.apiLogger = exports.multiplayerLogger = exports.quizLogger = exports.databaseLogger = exports.audioLogger = exports.logger = exports.Logger = exports.LogLevel = void 0;
exports.logError = logError;
exports.logUnhandledError = logUnhandledError;
exports.logUnhandledRejection = logUnhandledRejection;
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
    LogLevel[LogLevel["FATAL"] = 4] = "FATAL";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    constructor(config = {}) {
        this.storageKey = 'music-quiz-logs';
        this.config = {
            level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
            enableConsole: true,
            enableStorage: process.env.NODE_ENV === 'development',
            maxStorageEntries: 1000,
            ...config
        };
        this.sessionId = this.generateSessionId();
    }
    generateSessionId() {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    shouldLog(level) {
        return level >= this.config.level;
    }
    formatMessage(entry) {
        const levelName = LogLevel[entry.level];
        const context = entry.context ? `[${entry.context}]` : '';
        return `${entry.timestamp} ${levelName} ${context} ${entry.message}`;
    }
    log(level, message, data, error) {
        if (!this.shouldLog(level))
            return;
        const entry = {
            timestamp: new Date().toISOString(),
            level,
            message,
            context: this.config.context,
            data,
            error,
            sessionId: this.sessionId
        };
        if (this.config.enableConsole) {
            this.logToConsole(entry);
        }
        if (this.config.enableStorage && typeof window !== 'undefined') {
            this.logToStorage(entry);
        }
    }
    logToConsole(entry) {
        const formattedMessage = this.formatMessage(entry);
        switch (entry.level) {
            case LogLevel.DEBUG:
                console.debug(formattedMessage, entry.data);
                break;
            case LogLevel.INFO:
                console.info(formattedMessage, entry.data);
                break;
            case LogLevel.WARN:
                console.warn(formattedMessage, entry.data);
                break;
            case LogLevel.ERROR:
            case LogLevel.FATAL:
                console.error(formattedMessage, entry.data, entry.error);
                break;
        }
    }
    logToStorage(entry) {
        try {
            const existingLogs = this.getStoredLogs();
            const newLogs = [...existingLogs, entry].slice(-this.config.maxStorageEntries);
            localStorage.setItem(this.storageKey, JSON.stringify(newLogs));
        }
        catch (error) {
            console.warn('Failed to store log entry:', error);
        }
    }
    getStoredLogs() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : [];
        }
        catch {
            return [];
        }
    }
    debug(message, data) {
        this.log(LogLevel.DEBUG, message, data);
    }
    info(message, data) {
        this.log(LogLevel.INFO, message, data);
    }
    warn(message, data) {
        this.log(LogLevel.WARN, message, data);
    }
    error(message, error, data) {
        this.log(LogLevel.ERROR, message, data, error);
    }
    fatal(message, error, data) {
        this.log(LogLevel.FATAL, message, data, error);
    }
    createChild(context) {
        return new Logger({
            ...this.config,
            context: this.config.context ? `${this.config.context}:${context}` : context
        });
    }
    getLogs() {
        return this.getStoredLogs();
    }
    clearLogs() {
        if (typeof window !== 'undefined') {
            localStorage.removeItem(this.storageKey);
        }
    }
    exportLogs() {
        const logs = this.getLogs();
        return logs.map(entry => this.formatMessage(entry)).join('\n');
    }
}
exports.Logger = Logger;
exports.logger = new Logger();
exports.audioLogger = exports.logger.createChild('Audio');
exports.databaseLogger = exports.logger.createChild('Database');
exports.quizLogger = exports.logger.createChild('Quiz');
exports.multiplayerLogger = exports.logger.createChild('Multiplayer');
exports.apiLogger = exports.logger.createChild('API');
class PerformanceLogger {
    static start(operation) {
        this.timers.set(operation, performance.now());
        if (operation !== 'getAudioStatus') {
            exports.logger.debug(`Performance: Started ${operation}`);
        }
    }
    static end(operation, data) {
        const startTime = this.timers.get(operation);
        if (!startTime) {
            if (operation !== 'getAudioStatus') {
                exports.logger.warn(`Performance: No start time found for ${operation}`, data);
            }
            return 0;
        }
        const duration = performance.now() - startTime;
        this.timers.delete(operation);
        if (operation === 'getAudioStatus') {
            exports.logger.debug(`Performance: ${operation} completed in ${duration.toFixed(2)}ms`, {
                operation,
                duration,
                ...data
            });
        }
        else {
            exports.logger.info(`Performance: ${operation} completed in ${duration.toFixed(2)}ms`, {
                operation,
                duration,
                ...data
            });
        }
        if (typeof window !== 'undefined') {
            Promise.resolve().then(() => __importStar(require('./services/performance-monitor'))).then(({ performanceMonitor }) => {
                performanceMonitor.recordTiming(operation, duration, data);
            }).catch(() => {
            });
        }
        return duration;
    }
    static measure(operation, fn) {
        this.start(operation);
        try {
            const result = fn();
            this.end(operation);
            return result;
        }
        catch (error) {
            this.end(operation, { error: true });
            throw error;
        }
    }
    static async measureAsync(operation, fn) {
        this.start(operation);
        try {
            const result = await fn();
            this.end(operation);
            return result;
        }
        catch (error) {
            this.end(operation, { error: true });
            throw error;
        }
    }
}
exports.PerformanceLogger = PerformanceLogger;
PerformanceLogger.timers = new Map();
function logError(error, context, data) {
    const errorLogger = context ? exports.logger.createChild(context) : exports.logger;
    errorLogger.error(error.message, error, {
        stack: error.stack,
        ...data
    });
}
function logUnhandledError(event) {
    exports.logger.fatal('Unhandled JavaScript error', new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
    });
}
function logUnhandledRejection(event) {
    exports.logger.fatal('Unhandled promise rejection', event.reason instanceof Error ? event.reason : new Error(String(event.reason)));
}
if (typeof window !== 'undefined') {
    window.addEventListener('error', logUnhandledError);
    window.addEventListener('unhandledrejection', logUnhandledRejection);
}
//# sourceMappingURL=logger.js.map