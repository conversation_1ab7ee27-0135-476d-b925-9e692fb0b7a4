"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MPDStatusCache = void 0;
class MPDStatusCache {
    constructor() {
        this.cache = new Map();
        this.CACHE_TTL_MS = 3000;
    }
    static getInstance() {
        if (!MPDStatusCache.instance) {
            MPDStatusCache.instance = new MPDStatusCache();
        }
        return MPDStatusCache.instance;
    }
    get(key = 'global') {
        const cached = this.cache.get(key);
        if (!cached) {
            return null;
        }
        const now = Date.now();
        if (now - cached.timestamp > this.CACHE_TTL_MS) {
            this.cache.delete(key);
            return null;
        }
        return {
            status: cached.status,
            currentSong: cached.currentSong
        };
    }
    set(status, currentSong, key = 'global') {
        this.cache.set(key, {
            status,
            currentSong,
            timestamp: Date.now()
        });
    }
    clear() {
        this.cache.clear();
    }
    cleanup() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > this.CACHE_TTL_MS) {
                this.cache.delete(key);
            }
        }
    }
}
exports.MPDStatusCache = MPDStatusCache;
if (typeof setInterval !== 'undefined') {
    setInterval(() => {
        MPDStatusCache.getInstance().cleanup();
    }, 60000);
}
//# sourceMappingURL=mpd-status-cache.js.map