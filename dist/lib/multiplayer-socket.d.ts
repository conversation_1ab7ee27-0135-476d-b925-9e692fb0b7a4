import { Socket } from 'socket.io-client';
export declare function getMultiplayerSocket(): Socket;
export declare function connectMultiplayerSocket(): void;
export declare function disconnectMultiplayerSocket(): void;
export declare function isMultiplayerSocketConnected(): boolean;
export declare function cleanupMultiplayerSocket(): void;
export declare function getSocketId(): string | undefined;
export declare function emitMultiplayerEvent(event: string, ...args: any[]): void;
export declare function onMultiplayerEvent(event: string, handler: (...args: any[]) => void): () => void;
export type { Socket } from 'socket.io-client';
