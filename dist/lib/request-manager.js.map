{"version": 3, "file": "request-manager.js", "sourceRoot": "", "sources": ["../../lib/request-manager.ts"], "names": [], "mappings": ";;;AAKA,MAAM,mBAAmB;IAAzB;QACU,oBAAe,GAAG,IAAI,GAAG,EAA6B,CAAA;QACtD,kBAAa,GAAG,IAAI,GAAG,EAAkB,CAAA;QAChC,gCAA2B,GAAG,CAAC,CAAA;QAC/B,sBAAiB,GAAG,IAAI,CAAA;QACxB,4BAAuB,GAAG,CAAC,CAAA;IAmE9C,CAAC;IA9DC,KAAK,CAAC,KAAK,CAAC,GAAW,EAAE,OAAqB;QAC5C,MAAM,GAAG,GAAG,GAAG,OAAO,EAAE,MAAM,IAAI,KAAK,IAAI,GAAG,EAAE,CAAA;QAGhD,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,gDAAgD,GAAG,EAAE,CAAC,CAAA;YAElE,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAA;QACnD,CAAC;QAGD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAA;QACrE,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAE1D,IAAI,YAAY,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,4CAA4C,GAAG,EAAE,CAAC,CAAA;YAC/D,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAA;QACxC,CAAC;QAGD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,GAAG,CAAC,CAAC,CAAA;QAGlD,IAAI,CAAC,cAAc,EAAE,CAAA;QAGrB,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC;aACvC,OAAO,CAAC,GAAG,EAAE;YAEZ,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QAClC,CAAC,CAAC,CAAA;QAGJ,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,CAAA;QAE7C,OAAO,cAAc,CAAA;IACvB,CAAC;IAKO,cAAc;QACpB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAE3F,KAAK,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC1C,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAA;YACxD,IAAI,UAAU,GAAG,WAAW,EAAE,CAAC;gBAC7B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAKD,KAAK;QACH,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;QAC5B,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;IAC5B,CAAC;CACF;AAGY,QAAA,cAAc,GAAG,IAAI,mBAAmB,EAAE,CAAA"}