{"version": 3, "file": "auth-cookies.js", "sourceRoot": "", "sources": ["../../lib/auth-cookies.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2IA,8CAoBC;AA9JD,MAAM,UAAU,GAAG,KAAK,IAAI,EAAE;IAC5B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,MAAM,EAAE,OAAO,EAAE,GAAG,wDAAa,cAAc,GAAC,CAAA;QAChD,OAAO,OAAO,CAAA;IAChB,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AACD,+BAAyC;AAGzC,MAAM,YAAY,GAAG,GAAG,EAAE;IACxB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAGlC,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAA;IACvD,CAAC;IACD,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAA;IACrC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAA;IAC1E,CAAC;IACD,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;AACzC,CAAC,CAAA;AAID,MAAM,sBAAsB,GAAG,GAAG,EAAE;IAClC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;QAAE,OAAO,KAAK,CAAC;IAGxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAAE,OAAO,KAAK,CAAC;IAGnD,OAAO,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AAChE,CAAC,CAAA;AAED,MAAM,cAAc,GAAG;IACrB,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,sBAAsB,EAAE;IAChC,QAAQ,EAAE,KAAc;IACxB,IAAI,EAAE,GAAG;IACT,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;CACzB,CAAA;AASD,MAAa,UAAU;IAOrB,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAqB;QAC9C,MAAM,WAAW,GAAG,MAAM,UAAU,EAAE,CAAA;QACtC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAA;QAC7D,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,IAAI,cAAO,CAAC,OAAO,CAAC;aACrC,kBAAkB,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;aACpC,WAAW,EAAE;aACb,iBAAiB,CAAC,IAAI,CAAC;aACvB,IAAI,CAAC,YAAY,EAAE,CAAC,CAAA;QAEvB,WAAW,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,cAAc,CAAC,CAAA;QAGzD,MAAM,QAAQ,GAAG;YACf,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;SACnB,CAAA;QACD,WAAW,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;YAC/D,GAAG,cAAc;YACjB,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAA;IACJ,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,iBAAiB;QAC5B,MAAM,WAAW,GAAG,MAAM,UAAU,EAAE,CAAA;QACtC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,WAAW,EAAE,CAAA;QACvC,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,CAAA;QAErD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,IAAA,gBAAS,EAAC,KAAK,EAAE,YAAY,EAAE,CAAC,CAAA;YAC1D,OAAO,OAAkC,CAAA;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAA;YACtC,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,gBAAgB;QAC3B,MAAM,WAAW,GAAG,MAAM,UAAU,EAAE,CAAA;QACtC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAA;QAC7D,CAAC;QAED,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACrC,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;IAC3C,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,YAAY;QACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC9C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;QACjC,OAAO,IAAI,CAAA;IACb,CAAC;;AAlFH,gCAmFC;AAlFiB,qBAAU,GAAG,YAAY,CAAA;AACzB,yBAAc,GAAG,WAAW,CAAA;AAsF9C,SAAgB,iBAAiB;IAC/B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM;aACnC,KAAK,CAAC,IAAI,CAAC;aACX,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC1C,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAEjB,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAA;IACvD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAA;QAClD,OAAO,IAAI,CAAA;IACb,CAAC;AACH,CAAC"}