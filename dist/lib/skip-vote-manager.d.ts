import { Server as SocketServer } from 'socket.io';
import type { SkipVoteStatus } from './types';
export declare class SkipVoteManager {
    private votes;
    private currentTrackId;
    private currentTrackInfo;
    private connectedUsers;
    private io;
    private skipThreshold;
    constructor(io: SocketServer);
    setConnectedUser(userId: string, connected: boolean): void;
    getConnectedUsersCount(): number;
    setCurrentTrack(trackId: string, title: string, artist: string): void;
    addVote(userId: string, userName: string): Promise<SkipVoteStatus>;
    removeVote(trackId: string, userId: string): boolean;
    getVoteStatus(): SkipVoteStatus;
    private executeSkip;
    clearVotes(): void;
    setSkipThreshold(threshold: number): void;
}
