"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RandomUtils = void 0;
exports.cn = cn;
const clsx_1 = require("clsx");
const tailwind_merge_1 = require("tailwind-merge");
function cn(...inputs) {
    return (0, tailwind_merge_1.twMerge)((0, clsx_1.clsx)(inputs));
}
class RandomUtils {
    static secureRandom() {
        if (typeof window !== 'undefined' && typeof document !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
            const array = new Uint32Array(1);
            window.crypto.getRandomValues(array);
            return array[0] / (0xFFFFFFFF + 1);
        }
        return Math.random();
    }
    static shuffle(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(this.secureRandom() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    static sample(array, count) {
        if (count >= array.length)
            return this.shuffle(array);
        const shuffled = this.shuffle(array);
        return shuffled.slice(0, count);
    }
    static randomInt(min, max) {
        return Math.floor(this.secureRandom() * (max - min + 1)) + min;
    }
    static generateSeed() {
        const timestamp = Date.now();
        const random1 = Math.floor(this.secureRandom() * 1000000);
        const random2 = Math.floor(this.secureRandom() * 1000000);
        return `${timestamp}-${random1}-${random2}`;
    }
}
exports.RandomUtils = RandomUtils;
//# sourceMappingURL=utils.js.map