import { EventEmitter } from 'events';
export interface BluetoothDevice {
    mac: string;
    name: string;
    connected: boolean;
    paired: boolean;
    trusted: boolean;
    audioDevice?: boolean;
}
export interface BluetoothStatus {
    enabled: boolean;
    discovering: boolean;
    devices: BluetoothDevice[];
    connectedDevice?: BluetoothDevice;
}
export declare class BluetoothManager extends EventEmitter {
    private scanning;
    private currentDevice;
    constructor();
    isAvailable(): Promise<boolean>;
    getAdapterStatus(): Promise<{
        powered: boolean;
        discovering: boolean;
    }>;
    setPower(enable: boolean): Promise<boolean>;
    startScan(duration?: number): Promise<BluetoothDevice[]>;
    getDevices(): Promise<BluetoothDevice[]>;
    getDeviceInfo(mac: string): Promise<BluetoothDevice>;
    connectDevice(mac: string): Promise<boolean>;
    disconnectDevice(mac?: string): Promise<boolean>;
    private configureAudioOutput;
    private updateMPDConfig;
    getStatus(): Promise<BluetoothStatus>;
    private extractValue;
    removeDevice(mac: string): Promise<boolean>;
    autoConnect(): Promise<boolean>;
}
export declare const bluetoothManager: BluetoothManager;
