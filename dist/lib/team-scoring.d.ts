import { Team, TeamAnswer, TeamScoringMode, TeamGameMode } from './types';
export interface ScoreResult {
    teamId: string;
    pointsAwarded: number;
    bonusPoints: number;
    reason: string;
    breakdown: {
        basePoints: number;
        difficultyMultiplier: number;
        speedBonus: number;
        collaborationBonus: number;
        accuracyBonus: number;
    };
}
export interface TeamScoreConfig {
    basePoints: number;
    difficultyMultiplier: number;
    speedBonusThreshold: number;
    maxSpeedBonus: number;
    collaborationBonusPoints: number;
    accuracyStreakBonus: number;
    perfectTeamBonus: number;
}
export declare class TeamScoringSystem {
    private config;
    constructor(config?: Partial<TeamScoreConfig>);
    calculateTeamScores(teams: Team[], teamAnswers: TeamAnswer[], correctAnswer: number, questionDifficulty: number, timeLimit: number, scoringMode: TeamScoringMode, gameMode: TeamGameMode): ScoreResult[];
    private calculateCorrectAnswerScore;
    calculateStreakBonuses(teams: Team[], streakCounts: Map<string, number>): ScoreResult[];
    calculatePerfectTeamBonus(teams: Team[], individualAnswers: Map<string, {
        playerId: string;
        answer: number;
        correct: boolean;
    }>): ScoreResult[];
    applyScoreResults(teams: Team[], scoreResults: ScoreResult[]): Team[];
    getTeamLeaderboard(teams: Team[]): Array<Team & {
        rank: number;
        rankChange: number;
    }>;
    calculateIndividualContributions(team: Team, teamAnswers: TeamAnswer[], scoringMode: TeamScoringMode): Array<{
        playerId: string;
        contribution: number;
        percentage: number;
    }>;
    generateScoringSummary(scoreResults: ScoreResult[], teams: Team[]): {
        totalPointsAwarded: number;
        averageScore: number;
        highestScore: number;
        teamPerformance: Array<{
            teamName: string;
            score: number;
            bonuses: number;
            efficiency: number;
        }>;
    };
}
export declare const teamScoringSystem: TeamScoringSystem;
