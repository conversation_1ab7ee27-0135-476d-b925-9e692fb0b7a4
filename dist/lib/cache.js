"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cache = void 0;
exports.cachedResponse = cachedResponse;
class MemoryCache {
    constructor() {
        this.cache = new Map();
        this.maxSize = 50 * 1024 * 1024;
        this.maxEntries = 1000;
        this.currentSize = 0;
        this.cleanupInterval = null;
    }
    set(key, data, ttlSeconds) {
        const expiry = Date.now() + (ttlSeconds * 1000);
        const size = this.estimateSize(data);
        if (this.cache.has(key)) {
            const oldEntry = this.cache.get(key);
            this.currentSize -= oldEntry.size;
        }
        while (this.currentSize + size > this.maxSize && this.cache.size > 0) {
            this.evictOldest();
        }
        while (this.cache.size >= this.maxEntries && this.cache.size > 0) {
            this.evictOldest();
        }
        this.cache.set(key, { data, expiry, size });
        this.currentSize += size;
    }
    get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        if (Date.now() > entry.expiry) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    delete(key) {
        const entry = this.cache.get(key);
        if (entry) {
            this.currentSize -= entry.size;
            this.cache.delete(key);
        }
    }
    clear() {
        this.cache.clear();
        this.currentSize = 0;
    }
    estimateSize(data) {
        const str = JSON.stringify(data);
        return str.length * 2;
    }
    evictOldest() {
        const firstKey = this.cache.keys().next().value;
        if (firstKey) {
            this.delete(firstKey);
        }
    }
    startCleanup(intervalMs = 60000) {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.cleanupInterval = setInterval(() => {
            const now = Date.now();
            for (const [key, entry] of this.cache.entries()) {
                if (now > entry.expiry) {
                    this.delete(key);
                }
            }
            if (this.cache.size > 0) {
                console.log(`[Cache] Entries: ${this.cache.size}, Size: ${Math.round(this.currentSize / 1024 / 1024)}MB`);
            }
        }, intervalMs);
    }
    stopCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
    }
    getStats() {
        return {
            entries: this.cache.size,
            sizeBytes: this.currentSize,
            sizeMB: Math.round(this.currentSize / 1024 / 1024 * 100) / 100,
            maxSizeMB: this.maxSize / 1024 / 1024,
            maxEntries: this.maxEntries
        };
    }
}
exports.cache = new MemoryCache();
exports.cache.startCleanup();
if (typeof process !== 'undefined') {
    process.on('SIGINT', () => exports.cache.stopCleanup());
    process.on('SIGTERM', () => exports.cache.stopCleanup());
}
async function cachedResponse(key, fetcher, ttlSeconds) {
    const cached = exports.cache.get(key);
    if (cached !== null) {
        return cached;
    }
    const data = await fetcher();
    exports.cache.set(key, data, ttlSeconds);
    return data;
}
//# sourceMappingURL=cache.js.map