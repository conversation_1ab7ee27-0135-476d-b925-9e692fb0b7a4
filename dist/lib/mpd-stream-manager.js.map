{"version": 3, "file": "mpd-stream-manager.js", "sourceRoot": "", "sources": ["../../lib/mpd-stream-manager.ts"], "names": [], "mappings": ";;;AA2TA,wDAWC;AA3SD,MAAa,gBAAgB;IAQ3B,YAAY,MAAoB;QALxB,iBAAY,GAA4B,IAAI,CAAA;QAC5C,kBAAa,GAAY,KAAK,CAAA;QAC9B,sBAAiB,GAAW,CAAC,CAAA;QAC7B,oBAAe,GAAuC,EAAE,CAAA;QAG9D,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,kBAAkB,EAAE,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;YACtE,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,IAAI,IAAI;YACjD,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,IAAI;YACrC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC;SACnC,CAAA;QAED,IAAI,CAAC,SAAS,GAAG,UAAU,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAA;IAC7E,CAAC;IAKD,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YACpC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;YAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;YACzB,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAA;QAC9D,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,uBAAuB;QACnC,MAAM,IAAI,GAAG;YACX,IAAI,CAAC,SAAS;YACd,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;gBAClC,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SAC7E,CAAA;QAED,IAAI,SAAS,GAAiB,IAAI,CAAA;QAElC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;oBAChC,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;iBAClC,CAAC,CAAA;gBAEF,IAAI,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC3C,IAAI,CAAC,SAAS,GAAG,GAAG,CAAA;oBACpB,OAAM;gBACR,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAc,CAAA;gBAC1B,SAAQ;YACV,CAAC;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,6BAA6B,SAAS,EAAE,OAAO,EAAE,CAAC,CAAA;IACpE,CAAC;IAKO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAA;QAC5B,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,KAAK,EAAE,CAAA;QAC/B,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,WAAW,CAAA;QAC3C,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,MAAM,CAAA;QAGlC,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAEjC,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,SAAS,CAAA;YAE9C,IAAI,CAAC,YAAY,CAAC,2BAA2B,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA;QACxE,CAAC;QAGD,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,EAAE;YACnD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;QAC7C,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE;YACjD,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;YACzC,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC3B,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE;YACjD,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;YACnC,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC3B,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAChD,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAA;YACvC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAA;QAC3B,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE;YACjD,OAAO,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAA;YAChE,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC1B,CAAC,CAAC,CAAA;QAGF,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAA;QACtC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;IAC1B,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;QAC3C,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;YAC9B,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAA;QACrD,CAAC;IACH,CAAC;IAKD,UAAU;QACR,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;YACzB,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,CAAC,CAAA;YACjC,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC3B,CAAC;IACH,CAAC;IAKD,SAAS,CAAC,MAAc;QACtB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAA;YAC3D,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC3B,CAAC;IACH,CAAC;IAKD,SAAS;QACP,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAA;QACpE,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,UAAU,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAA;QAEpF,OAAO;YACL,WAAW;YACX,WAAW;YACX,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC/B,OAAO,EAAE,GAAG;YACZ,OAAO,EAAE,CAAC;YACV,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE;SAC3B,CAAA;IACH,CAAC;IAKO,KAAK,CAAC,iBAAiB,CAAC,KAAY;QAC1C,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACrD,OAAO,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAA;YAC/D,OAAM;QACR,CAAC;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,OAAO,CAAC,GAAG,CAAC,wCAAwC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAA;QAGvG,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAA;QAEhF,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YACpC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAA;gBACtC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;YAC1B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAA;QAC3D,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAEtB,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAA;YACjD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;YAExB,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE;gBACjD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtB,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,WAAW,CAAA;oBAC3C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;gBAC1B,CAAC;YACH,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;QACpB,CAAC;IACH,CAAC;IAKO,eAAe;QAGrB,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;IACjD,CAAC;IAKO,YAAY;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC;YAC7B,QAAQ,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;gBACrC,KAAK,UAAU,CAAC,iBAAiB;oBAC/B,OAAO,8BAA8B,CAAA;gBACvC,KAAK,UAAU,CAAC,gBAAgB;oBAC9B,OAAO,qBAAqB,CAAA;gBAC9B,KAAK,UAAU,CAAC,2BAA2B;oBACzC,OAAO,6BAA6B,CAAA;gBACtC;oBACE,OAAO,sBAAsB,CAAA;YACjC,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAA;IAClB,CAAC;IAKD,cAAc,CAAC,QAAwC;QACrD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IACrC,CAAC;IAKD,eAAe,CAAC,QAAwC;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QACpD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QACvC,CAAC;IACH,CAAC;IAKO,kBAAkB;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;QAC/B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;IAC5D,CAAC;IAKD,OAAO;QACL,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;YACzB,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAA;YAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QAC1B,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,EAAE,CAAA;QACzB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;IAC5B,CAAC;CACF;AA3RD,4CA2RC;AAKD,SAAgB,sBAAsB,CAAC,MAA6B;IAClE,MAAM,aAAa,GAAiB;QAClC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,WAAW;QACxD,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,MAAM,CAAC;QAC7D,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,MAAM,CAAC;QACvE,gBAAgB,EAAE,IAAI;QACtB,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC;KACd,CAAA;IAED,OAAO,IAAI,gBAAgB,CAAC,EAAE,GAAG,aAAa,EAAE,GAAG,MAAM,EAAE,CAAC,CAAA;AAC9D,CAAC"}