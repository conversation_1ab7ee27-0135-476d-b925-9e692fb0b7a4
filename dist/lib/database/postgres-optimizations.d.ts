export declare class PostgreSQLOptimizations {
    static createIndexes(): Promise<void>;
    static analyzeDatabase(): Promise<void>;
    static getOptimizedConnectionString(baseUrl: string): string;
    static createMaterializedViews(): Promise<void>;
    static refreshMaterializedViews(): Promise<void>;
    static configureAutovacuum(): Promise<void>;
    static runAllOptimizations(): Promise<void>;
}
