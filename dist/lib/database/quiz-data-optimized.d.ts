interface PreloadedData {
    artists: string[];
    genres: string[];
    albums: string[];
    titles: string[];
    years: number[];
}
export declare class OptimizedQuizData {
    private preloadedData;
    private lastPreloadTime;
    private readonly PRELOAD_CACHE_DURATION;
    preloadData(): Promise<PreloadedData>;
    private getDistinctArtists;
    private getDistinctGenres;
    private getDistinctAlbums;
    private getDistinctTitles;
    private getDistinctYears;
    getRandomArtists(count: number, exclude?: string): Promise<string[]>;
    getRandomGenres(count: number, exclude?: string): Promise<string[]>;
    getRandomAlbums(count: number, exclude?: string): Promise<string[]>;
    getRandomTitles(count: number, exclude?: string): Promise<string[]>;
    getRandomYears(count: number, exclude?: number): Promise<number[]>;
    getQuizTracks(count: number, filters?: {
        genre?: string;
        yearFrom?: number;
        yearTo?: number;
        category?: string;
    }): Promise<unknown>;
    generateQuizQuestions(tracks: any[], gameMode: string, preloadedData?: PreloadedData): Promise<{
        track: any;
        wrongArtists: string[];
        wrongAlbums: string[];
        wrongTitles: string[];
        wrongYears: number[];
    }[]>;
    clearCache(): void;
    private shuffleArray;
}
export {};
