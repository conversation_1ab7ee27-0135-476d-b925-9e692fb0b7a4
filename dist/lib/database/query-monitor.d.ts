export interface QueryMetrics {
    query: string;
    duration: number;
    timestamp: Date;
    parameters?: any[];
    error?: string;
    stackTrace?: string;
}
export interface PerformanceStats {
    totalQueries: number;
    averageResponseTime: number;
    slowQueries: number;
    errorRate: number;
    topSlowQueries: QueryMetrics[];
}
export declare class QueryMonitor {
    private static instance;
    private metrics;
    private readonly maxMetrics;
    private readonly slowQueryThreshold;
    private readonly reportInterval;
    private constructor();
    static getInstance(): QueryMonitor;
    logQuery(query: string, duration: number, parameters?: any[], error?: Error): void;
    getPerformanceStats(): PerformanceStats;
    getSlowQueries(limit?: number): QueryMetrics[];
    getErrorQueries(limit?: number): QueryMetrics[];
    clear(): void;
    private sanitizeQuery;
    private sanitizeParameters;
    private startPeriodicReporting;
}
export declare const queryMonitor: QueryMonitor;
export declare function monitorQuery<T>(queryName: string, queryFunction: () => Promise<T>): Promise<T>;
