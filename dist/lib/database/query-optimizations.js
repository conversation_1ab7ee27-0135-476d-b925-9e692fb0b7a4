"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optimizedSelects = void 0;
exports.buildOptimizedQuery = buildOptimizedQuery;
exports.optimizedSelects = {
    queueTrack: {
        id: true,
        fileFingerprint: true,
        mpdFilePath: true,
        title: true,
        artist: true,
        album: true,
        albumArtUrl: true,
        localAlbumArtThumbnail: true,
        localAlbumArtCover: true,
        localAlbumArtOriginal: true,
        duration: true,
        calculatedGain: true,
    },
    suggestionWithVotes: {
        id: true,
        title: true,
        artist: true,
        album: true,
        duration: true,
        genre: true,
        year: true,
        filePath: true,
        albumArtUrl: true,
        suggestedById: true,
        suggestedBy: true,
        status: true,
        createdAt: true,
        _count: {
            select: { votes: true }
        }
    },
    userMinimal: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        role: true,
    },
    mpdTrack: {
        id: true,
        mpdFilePath: true,
        title: true,
        artist: true,
        albumArtUrl: true,
        genre: true,
        year: true,
    },
    quizTrackMinimal: {
        id: true,
        mpdFilePath: true,
        title: true,
        artist: true,
        album: true,
        year: true,
        genre: true,
        duration: true,
        albumArtUrl: true,
    },
    quizTrackQuestion: {
        id: true,
        mpdFilePath: true,
        title: true,
        artist: true,
        album: true,
        year: true,
        genre: true,
        duration: true,
        albumArtUrl: true,
        triviaFacts: true,
        interestingFacts: true,
        chartData: true,
    },
    gameSessionMinimal: {
        id: true,
        userId: true,
        gameMode: true,
        totalQuestions: true,
        correctAnswers: true,
        totalScore: true,
        startedAt: true,
        completedAt: true,
    },
};
function buildOptimizedQuery(baseQuery, selectFields) {
    if (selectFields) {
        return {
            ...baseQuery,
            select: selectFields
        };
    }
    return baseQuery;
}
//# sourceMappingURL=query-optimizations.js.map