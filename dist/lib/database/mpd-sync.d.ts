import type { MPDTrack } from '@/lib/mpd-client';
export interface MPDLibraryTrack extends MPDTrack {
    quizTrackId?: string;
    difficultyRating?: number;
    popularityScore?: number;
    triviaFacts?: string[];
    albumArtUrl?: string;
    artistImageUrl?: string;
    chartPosition?: number;
    chartCountry?: string;
    interestingFacts?: any;
    similarArtists?: string[];
    timesPlayed?: number;
    correctAnswers?: number;
    lastPlayed?: Date;
}
export interface SyncProgress {
    phase: 'discovering' | 'processing' | 'updating' | 'complete' | 'error';
    current: number;
    total: number;
    message: string;
}
export interface SyncResult {
    success: boolean;
    tracksDiscovered: number;
    tracksProcessed: number;
    tracksUpdated: number;
    tracksCreated: number;
    errors: string[];
    duration: number;
}
export declare class MPDLibrarySync {
    private mpdClient;
    private isRunning;
    private progressCallback?;
    constructor(mpdClient: any);
    onProgress(callback: (progress: SyncProgress) => void): void;
    syncMPDLibrary(options?: {
        fullSync?: boolean;
        batchSize?: number;
        forceUpdate?: boolean;
        preview?: boolean;
    }): Promise<SyncResult>;
    getEnhancedTrack(mpdFilePath: string): Promise<MPDLibraryTrack | null>;
    searchEnhancedTracks(query: string, filters?: {
        genre?: string;
        decade?: string;
        difficulty?: number;
        artist?: string;
        limit?: number;
        minPopularity?: number;
        maxPopularity?: number;
    }): Promise<MPDLibraryTrack[]>;
    getRandomTracks(count: number, filters?: {
        genre?: string;
        decade?: string;
        difficulty?: number;
        excludeIds?: string[];
        minPopularity?: number;
        minLastPlayedMinutes?: number;
    }): Promise<MPDLibraryTrack[]>;
    updateTrackStats(trackId: string, wasCorrect: boolean): Promise<void>;
    getLibraryStats(): Promise<{
        totalTracks: number;
        byGenre: Record<string, number>;
        byDecade: Record<string, number>;
        byDifficulty: Record<number, number>;
        lastSyncDate: Date | null;
    }>;
    private processBatch;
    private extractTitleFromPath;
    private reportProgress;
    isSyncRunning(): boolean;
    getAvailableGenres(): Promise<string[]>;
    getAvailableDecades(): Promise<string[]>;
    getAvailableArtists(limit?: number): Promise<string[]>;
    cleanupOrphanedTracks(): Promise<number>;
}
