import { MPDLibrarySync, type MPDLibraryTrack } from './mpd-sync';
export interface QuizQuestion {
    id: string;
    type: 'artist' | 'title' | 'album' | 'year' | 'genre' | 'chart-position' | 'decade' | 'timeline-placement';
    question: string;
    correctAnswer: string;
    options: string[];
    track: MPDLibraryTrack;
    trackPreviewStart?: number;
    difficulty: number;
    hints?: string[];
    audioEffect?: 'rewind' | 'fast' | 'slow' | null;
    metadata?: any;
}
export interface QuizSession {
    id: string;
    userId?: string;
    gameMode: string;
    questions: QuizQuestion[];
    currentQuestionIndex: number;
    answers: QuizAnswer[];
    score: number;
    streak: number;
    maxStreak: number;
    startTime: Date;
    endTime?: Date;
    settings: QuizSettings;
    metadata?: any;
}
export interface QuizAnswer {
    questionId: string;
    userAnswer: string;
    isCorrect: boolean;
    timeSpent: number;
    pointsEarned: number;
    hintsUsed: number;
    streak: number;
    metadata?: any;
}
export interface QuizSettings {
    totalQuestions: number;
    difficultyLevel: number;
    timeLimit: number;
    genre?: string;
    decade?: string;
    artist?: string;
    enableHints: boolean;
    autoPlay: boolean;
    volume: number;
    previewDuration: number;
    questionTypes?: {
        artist?: boolean;
        title?: boolean;
        album?: boolean;
        year?: boolean;
        genre?: boolean;
        chartPosition?: boolean;
    };
}
export interface QuizSessionResult {
    session: QuizSession;
    stats: {
        totalQuestions: number;
        correctAnswers: number;
        accuracy: number;
        totalScore: number;
        averageTime: number;
        fastestAnswer: number;
        slowestAnswer: number;
        streakRecord: number;
        difficulty: number;
    };
    achievements?: string[];
}
export declare class QuizDataManager {
    private librarySync;
    constructor(librarySync: MPDLibrarySync);
    generateQuizQuestions(gameMode: string, settings: QuizSettings): Promise<QuizQuestion[]>;
    private generateQuestionForTrack;
    private generateArtistQuestion;
    private generateChartPositionQuestion;
    private generateDecadeQuestion;
    private generateGenreQuestion;
    private generateTitleQuestion;
    private generateAlbumQuestion;
    private getRandomArtists;
    private generateQuickFireQuestion;
    private generateAudioTricksQuestion;
    private generateAlbumArtQuestion;
    private generateAudioFingerprintQuestion;
    private getRandomGenres;
    private getRandomTitles;
    private getRandomAlbums;
    private generateChartPositionOptions;
    private generateDecadeOptions;
    private getRandomPreviewStart;
    private getMinPopularityForMode;
    private generateMockQuestions;
    private generateYearQuestion;
}
