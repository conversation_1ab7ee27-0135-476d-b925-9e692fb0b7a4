"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostgreSQLOptimizations = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
const logger_1 = require("@/lib/logger");
class PostgreSQLOptimizations {
    static async createIndexes() {
        const isPostgreSQL = process.env.DATABASE_URL?.includes('postgresql');
        if (!isPostgreSQL) {
            logger_1.databaseLogger.info('Skipping PostgreSQL optimizations - not using PostgreSQL');
            return;
        }
        try {
            logger_1.databaseLogger.info('Creating PostgreSQL indexes...');
            const indexes = [
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quiz_track_genre ON "QuizTrack"(genre) WHERE genre IS NOT NULL',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quiz_track_year ON "QuizTrack"(year) WHERE year IS NOT NULL',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quiz_track_popularity ON "QuizTrack"("popularityScore" DESC)',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quiz_track_last_played ON "QuizTrack"("lastPlayed" DESC NULLS LAST)',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quiz_track_mpd_path ON "QuizTrack"("mpdFilePath") WHERE "mpdFilePath" IS NOT NULL',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quiz_track_genre_year ON "QuizTrack"(genre, year)',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quiz_track_difficulty_popularity ON "QuizTrack"("difficultyRating", "popularityScore" DESC)',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_favorite_user ON "UserFavorite"("userId")',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_favorite_added ON "UserFavorite"("addedAt" DESC)',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_playlist_user ON "Playlist"("userId")',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_playlist_public ON "Playlist"("isPublic") WHERE "isPublic" = true',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_playlist_mpd ON "Playlist"("mpdName") WHERE "mpdName" IS NOT NULL',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_playlist_track_playlist ON "PlaylistTrack"("playlistId")',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_playlist_track_position ON "PlaylistTrack"("playlistId", position)',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_game_session_user ON "GameSession"("userId")',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_game_session_created ON "GameSession"("createdAt" DESC)',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_game_session_mode ON "GameSession"("gameMode")',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quiz_track_title_trgm ON "QuizTrack" USING gin(title gin_trgm_ops)',
                'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quiz_track_artist_trgm ON "QuizTrack" USING gin(artist gin_trgm_ops)',
            ];
            await prisma_1.default.$executeRawUnsafe('CREATE EXTENSION IF NOT EXISTS pg_trgm');
            for (const indexSql of indexes) {
                try {
                    await prisma_1.default.$executeRawUnsafe(indexSql);
                    logger_1.databaseLogger.debug(`Created index: ${indexSql.match(/idx_\w+/)?.[0]}`);
                }
                catch (error) {
                    if (!error.message?.includes('already exists')) {
                        logger_1.databaseLogger.warn(`Failed to create index: ${error.message}`);
                    }
                }
            }
            logger_1.databaseLogger.info('PostgreSQL indexes created successfully');
        }
        catch (error) {
            logger_1.databaseLogger.error('Failed to create PostgreSQL indexes', error);
        }
    }
    static async analyzeDatabase() {
        const isPostgreSQL = process.env.DATABASE_URL?.includes('postgresql');
        if (!isPostgreSQL)
            return;
        try {
            logger_1.databaseLogger.info('Running PostgreSQL ANALYZE...');
            const tables = [
                'QuizTrack',
                'UserFavorite',
                'Playlist',
                'PlaylistTrack',
                'GameSession',
                'GameAnswer',
                'UserStats'
            ];
            for (const table of tables) {
                await prisma_1.default.$executeRawUnsafe(`ANALYZE "${table}"`);
            }
            logger_1.databaseLogger.info('PostgreSQL ANALYZE completed');
        }
        catch (error) {
            logger_1.databaseLogger.error('Failed to analyze database', error);
        }
    }
    static getOptimizedConnectionString(baseUrl) {
        if (!baseUrl.includes('postgresql'))
            return baseUrl;
        const url = new URL(baseUrl);
        const params = new URLSearchParams(url.search);
        params.set('connection_limit', '10');
        params.set('pool_timeout', '30');
        params.set('statement_cache_size', '100');
        params.set('keepalives', '1');
        params.set('keepalives_idle', '30');
        url.search = params.toString();
        return url.toString();
    }
    static async createMaterializedViews() {
        const isPostgreSQL = process.env.DATABASE_URL?.includes('postgresql');
        if (!isPostgreSQL)
            return;
        try {
            logger_1.databaseLogger.info('Creating materialized views...');
            await prisma_1.default.$executeRawUnsafe(`
        CREATE MATERIALIZED VIEW IF NOT EXISTS mv_genre_stats AS
        SELECT 
          genre,
          COUNT(*) as track_count,
          AVG("popularityScore") as avg_popularity,
          AVG("difficultyRating") as avg_difficulty,
          COUNT(DISTINCT artist) as artist_count
        FROM "QuizTrack"
        WHERE genre IS NOT NULL
        GROUP BY genre
      `);
            await prisma_1.default.$executeRawUnsafe(`
        CREATE INDEX IF NOT EXISTS idx_mv_genre_stats_genre 
        ON mv_genre_stats(genre)
      `);
            await prisma_1.default.$executeRawUnsafe(`
        CREATE MATERIALIZED VIEW IF NOT EXISTS mv_user_performance AS
        SELECT 
          u.id as user_id,
          u.username,
          COUNT(DISTINCT gs.id) as games_played,
          AVG(gs."totalScore") as avg_score,
          MAX(gs."totalScore") as best_score,
          AVG(gs."averageResponseTime") as avg_response_time
        FROM "User" u
        LEFT JOIN "GameSession" gs ON u.id = gs."userId"
        WHERE gs."completedAt" IS NOT NULL
        GROUP BY u.id, u.username
      `);
            logger_1.databaseLogger.info('Materialized views created successfully');
        }
        catch (error) {
            if (!error.message?.includes('already exists')) {
                logger_1.databaseLogger.error('Failed to create materialized views', error);
            }
        }
    }
    static async refreshMaterializedViews() {
        const isPostgreSQL = process.env.DATABASE_URL?.includes('postgresql');
        if (!isPostgreSQL)
            return;
        try {
            await prisma_1.default.$executeRawUnsafe('REFRESH MATERIALIZED VIEW CONCURRENTLY mv_genre_stats');
            await prisma_1.default.$executeRawUnsafe('REFRESH MATERIALIZED VIEW CONCURRENTLY mv_user_performance');
            logger_1.databaseLogger.info('Materialized views refreshed');
        }
        catch (error) {
            logger_1.databaseLogger.warn('Failed to refresh materialized views', error);
        }
    }
    static async configureAutovacuum() {
        const isPostgreSQL = process.env.DATABASE_URL?.includes('postgresql');
        if (!isPostgreSQL)
            return;
        try {
            const configs = [
                'ALTER TABLE "QuizTrack" SET (autovacuum_vacuum_scale_factor = 0.1)',
                'ALTER TABLE "UserFavorite" SET (autovacuum_vacuum_scale_factor = 0.1)',
                'ALTER TABLE "GameSession" SET (autovacuum_analyze_scale_factor = 0.05)',
                'ALTER TABLE "GameAnswer" SET (autovacuum_analyze_scale_factor = 0.05)',
            ];
            for (const config of configs) {
                try {
                    await prisma_1.default.$executeRawUnsafe(config);
                }
                catch (error) {
                    logger_1.databaseLogger.debug(`Autovacuum config: ${error.message}`);
                }
            }
            logger_1.databaseLogger.info('Autovacuum configured');
        }
        catch (error) {
            logger_1.databaseLogger.error('Failed to configure autovacuum', error);
        }
    }
    static async runAllOptimizations() {
        await this.createIndexes();
        await this.analyzeDatabase();
        await this.createMaterializedViews();
        await this.configureAutovacuum();
    }
}
exports.PostgreSQLOptimizations = PostgreSQLOptimizations;
//# sourceMappingURL=postgres-optimizations.js.map