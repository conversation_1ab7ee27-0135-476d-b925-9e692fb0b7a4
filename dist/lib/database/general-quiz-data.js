"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeneralQuizService = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
class GeneralQuizService {
    static getInstance() {
        if (!GeneralQuizService.instance) {
            GeneralQuizService.instance = new GeneralQuizService();
        }
        return GeneralQuizService.instance;
    }
    async getCategories() {
        try {
            return await prisma.quizCategory.findMany({
                where: { isActive: true },
                orderBy: { sortOrder: 'asc' }
            });
        }
        catch (error) {
            console.error('Error fetching quiz categories:', error);
            return [];
        }
    }
    async getCategoryById(id) {
        try {
            return await prisma.quizCategory.findFirst({
                where: {
                    OR: [
                        { id: id },
                        { slug: id }
                    ],
                    isActive: true
                }
            });
        }
        catch (error) {
            console.error('Error fetching category:', error);
            return null;
        }
    }
    async getQuestionsByCategory(categoryId, count = 10, difficulty) {
        try {
            const whereClause = {
                category: {
                    OR: [
                        { id: categoryId },
                        { slug: categoryId }
                    ]
                },
                isActive: true
            };
            if (difficulty) {
                whereClause.difficulty = difficulty;
            }
            const questions = await prisma.generalQuizQuestion.findMany({
                where: whereClause,
                include: {
                    category: true
                },
                orderBy: { createdAt: 'desc' }
            });
            const shuffled = questions.sort(() => Math.random() - 0.5);
            const limited = shuffled.slice(0, count);
            return limited.map(this.mapToQuizQuestion);
        }
        catch (error) {
            console.error('Error fetching questions by category:', error);
            return [];
        }
    }
    async getMixedQuestions(count = 10, difficulty) {
        try {
            const whereClause = {
                isActive: true
            };
            if (difficulty) {
                whereClause.difficulty = difficulty;
            }
            const questions = await prisma.generalQuizQuestion.findMany({
                where: whereClause,
                include: {
                    category: true
                },
                orderBy: { createdAt: 'desc' }
            });
            const shuffled = questions.sort(() => Math.random() - 0.5);
            const limited = shuffled.slice(0, count);
            return limited.map(this.mapToQuizQuestion);
        }
        catch (error) {
            console.error('Error fetching mixed questions:', error);
            return [];
        }
    }
    async getQuestionsByCategories(categoryIds, count = 10, difficulty) {
        try {
            const whereClause = {
                category: {
                    slug: { in: categoryIds }
                },
                isActive: true
            };
            if (difficulty) {
                whereClause.difficulty = difficulty;
            }
            const questions = await prisma.generalQuizQuestion.findMany({
                where: whereClause,
                include: {
                    category: true
                },
                orderBy: { createdAt: 'desc' }
            });
            const shuffled = questions.sort(() => Math.random() - 0.5);
            const limited = shuffled.slice(0, count);
            return limited.map(this.mapToQuizQuestion);
        }
        catch (error) {
            console.error('Error fetching questions by categories:', error);
            return [];
        }
    }
    async getRandomQuestion(categoryId, difficulty) {
        try {
            const questions = categoryId
                ? await this.getQuestionsByCategory(categoryId, 1, difficulty)
                : await this.getMixedQuestions(1, difficulty);
            return questions.length > 0 ? questions[0] : null;
        }
        catch (error) {
            console.error('Error fetching random question:', error);
            return null;
        }
    }
    async validateAnswer(questionId, answer) {
        try {
            const question = await prisma.generalQuizQuestion.findUnique({
                where: { id: questionId }
            });
            if (!question)
                return false;
            return question.correctAnswer === String(answer);
        }
        catch (error) {
            console.error('Error validating answer:', error);
            return false;
        }
    }
    async recordQuestionUsage(questionId, isCorrect, timeSpent) {
        try {
            await prisma.generalQuizQuestion.update({
                where: { id: questionId },
                data: {
                    timesPlayed: { increment: 1 },
                    correctAnswers: isCorrect ? { increment: 1 } : undefined,
                    averageTime: {
                        set: await this.calculateNewAverageTime(questionId, timeSpent)
                    }
                }
            });
        }
        catch (error) {
            console.error('Error recording question usage:', error);
        }
    }
    async getQuestionStats(categoryId) {
        try {
            const whereClause = {
                isActive: true
            };
            if (categoryId) {
                whereClause.category = {
                    OR: [
                        { id: categoryId },
                        { slug: categoryId }
                    ]
                };
            }
            const questions = await prisma.generalQuizQuestion.findMany({
                where: whereClause,
                select: {
                    difficulty: true,
                    type: true,
                    category: {
                        select: {
                            name: true,
                            slug: true
                        }
                    }
                }
            });
            const stats = {
                total: questions.length,
                byDifficulty: {
                    1: questions.filter(q => q.difficulty === 1).length,
                    2: questions.filter(q => q.difficulty === 2).length,
                    3: questions.filter(q => q.difficulty === 3).length,
                    4: questions.filter(q => q.difficulty === 4).length,
                    5: questions.filter(q => q.difficulty === 5).length
                },
                byType: {
                    'multiple-choice': questions.filter(q => q.type === 'multiple-choice').length,
                    'true-false': questions.filter(q => q.type === 'true-false').length,
                    'slider': questions.filter(q => q.type === 'slider').length
                },
                byCategory: {}
            };
            questions.forEach(q => {
                const categorySlug = q.category.slug;
                stats.byCategory[categorySlug] = (stats.byCategory[categorySlug] || 0) + 1;
            });
            return stats;
        }
        catch (error) {
            console.error('Error fetching question stats:', error);
            return {
                total: 0,
                byDifficulty: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
                byType: { 'multiple-choice': 0, 'true-false': 0, 'slider': 0 },
                byCategory: {}
            };
        }
    }
    async calculateNewAverageTime(questionId, newTime) {
        const question = await prisma.generalQuizQuestion.findUnique({
            where: { id: questionId },
            select: { averageTime: true, timesPlayed: true }
        });
        if (!question)
            return newTime;
        const currentAverage = question.averageTime;
        const timesPlayed = question.timesPlayed;
        return (currentAverage * timesPlayed + newTime) / (timesPlayed + 1);
    }
    mapToQuizQuestion(dbQuestion) {
        return {
            id: dbQuestion.id,
            type: dbQuestion.type,
            category: dbQuestion.category.slug,
            difficulty: dbQuestion.difficulty,
            question: dbQuestion.question,
            options: dbQuestion.options || undefined,
            correctAnswer: dbQuestion.correctAnswer,
            explanation: dbQuestion.explanation || undefined,
            timeLimit: dbQuestion.timeLimit,
            points: dbQuestion.points,
            hints: dbQuestion.hints || []
        };
    }
}
exports.GeneralQuizService = GeneralQuizService;
exports.default = GeneralQuizService.getInstance();
//# sourceMappingURL=general-quiz-data.js.map