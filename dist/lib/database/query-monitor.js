"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.queryMonitor = exports.QueryMonitor = void 0;
exports.monitorQuery = monitorQuery;
const logger_1 = require("@/lib/logger");
class QueryMonitor {
    constructor() {
        this.metrics = [];
        this.maxMetrics = 1000;
        this.slowQueryThreshold = 1000;
        this.reportInterval = 60000;
        this.startPeriodicReporting();
    }
    static getInstance() {
        if (!QueryMonitor.instance) {
            QueryMonitor.instance = new QueryMonitor();
        }
        return QueryMonitor.instance;
    }
    logQuery(query, duration, parameters, error) {
        const metric = {
            query: this.sanitizeQuery(query),
            duration,
            timestamp: new Date(),
            parameters: parameters ? this.sanitizeParameters(parameters) : undefined,
            error: error?.message,
            stackTrace: error?.stack
        };
        this.metrics.push(metric);
        if (this.metrics.length > this.maxMetrics) {
            this.metrics.shift();
        }
        if (duration > this.slowQueryThreshold) {
            logger_1.databaseLogger.warn('Slow query detected', {
                query: metric.query,
                duration: `${duration}ms`,
                parameters: metric.parameters
            });
        }
        if (error) {
            logger_1.databaseLogger.error('Query failed', error, {
                query: metric.query,
                duration: `${duration}ms`,
                parameters: metric.parameters
            });
        }
    }
    getPerformanceStats() {
        if (this.metrics.length === 0) {
            return {
                totalQueries: 0,
                averageResponseTime: 0,
                slowQueries: 0,
                errorRate: 0,
                topSlowQueries: []
            };
        }
        const totalQueries = this.metrics.length;
        const totalTime = this.metrics.reduce((sum, m) => sum + m.duration, 0);
        const averageResponseTime = totalTime / totalQueries;
        const slowQueries = this.metrics.filter(m => m.duration > this.slowQueryThreshold).length;
        const errorQueries = this.metrics.filter(m => m.error).length;
        const errorRate = (errorQueries / totalQueries) * 100;
        const topSlowQueries = [...this.metrics]
            .sort((a, b) => b.duration - a.duration)
            .slice(0, 10);
        return {
            totalQueries,
            averageResponseTime: Math.round(averageResponseTime * 100) / 100,
            slowQueries,
            errorRate: Math.round(errorRate * 100) / 100,
            topSlowQueries
        };
    }
    getSlowQueries(limit = 10) {
        return this.metrics
            .filter(m => m.duration > this.slowQueryThreshold)
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
            .slice(0, limit);
    }
    getErrorQueries(limit = 10) {
        return this.metrics
            .filter(m => m.error)
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
            .slice(0, limit);
    }
    clear() {
        this.metrics = [];
        logger_1.databaseLogger.info('Query metrics cleared');
    }
    sanitizeQuery(query) {
        return query
            .replace(/password\s*=\s*'[^']*'/gi, "password='***'")
            .replace(/token\s*=\s*'[^']*'/gi, "token='***'")
            .trim();
    }
    sanitizeParameters(params) {
        return params.map(param => {
            if (typeof param === 'string') {
                if (param.length > 20 && /^[a-zA-Z0-9+/=]+$/.test(param)) {
                    return '***';
                }
            }
            return param;
        });
    }
    startPeriodicReporting() {
        setInterval(() => {
            const stats = this.getPerformanceStats();
            if (stats.totalQueries > 0) {
                logger_1.databaseLogger.info('Query performance report', {
                    totalQueries: stats.totalQueries,
                    averageResponseTime: `${stats.averageResponseTime}ms`,
                    slowQueries: stats.slowQueries,
                    errorRate: `${stats.errorRate}%`
                });
                const recentSlowQueries = this.getSlowQueries(3);
                if (recentSlowQueries.length > 0) {
                    logger_1.databaseLogger.warn('Recent slow queries', {
                        count: recentSlowQueries.length,
                        queries: recentSlowQueries.map(q => ({
                            query: q.query.substring(0, 100),
                            duration: `${q.duration}ms`,
                            timestamp: q.timestamp
                        }))
                    });
                }
            }
        }, this.reportInterval);
    }
}
exports.QueryMonitor = QueryMonitor;
QueryMonitor.instance = null;
exports.queryMonitor = QueryMonitor.getInstance();
function monitorQuery(queryName, queryFunction) {
    const startTime = Date.now();
    return queryFunction()
        .then(result => {
        const duration = Date.now() - startTime;
        exports.queryMonitor.logQuery(queryName, duration);
        return result;
    })
        .catch(error => {
        const duration = Date.now() - startTime;
        exports.queryMonitor.logQuery(queryName, duration, undefined, error);
        throw error;
    });
}
//# sourceMappingURL=query-monitor.js.map