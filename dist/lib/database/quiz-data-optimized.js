"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimizedQuizData = void 0;
const prisma_1 = __importDefault(require("./prisma"));
const client_1 = require("@prisma/client");
class OptimizedQuizData {
    constructor() {
        this.preloadedData = null;
        this.lastPreloadTime = 0;
        this.PRELOAD_CACHE_DURATION = 5 * 60 * 1000;
    }
    async preloadData() {
        const now = Date.now();
        if (this.preloadedData && (now - this.lastPreloadTime) < this.PRELOAD_CACHE_DURATION) {
            return this.preloadedData;
        }
        console.log('[OptimizedQuizData] Preloading data...');
        const [artists, genres, albums, titles, years] = await Promise.all([
            this.getDistinctArtists(),
            this.getDistinctGenres(),
            this.getDistinctAlbums(),
            this.getDistinctTitles(),
            this.getDistinctYears()
        ]);
        this.preloadedData = {
            artists,
            genres,
            albums,
            titles,
            years
        };
        this.lastPreloadTime = now;
        console.log('[OptimizedQuizData] Preloaded data:', {
            artists: artists.length,
            genres: genres.length,
            albums: albums.length,
            titles: titles.length,
            years: years.length
        });
        return this.preloadedData;
    }
    async getDistinctArtists() {
        const result = await prisma_1.default.$queryRaw `
      SELECT DISTINCT artist 
      FROM quiz_tracks 
      WHERE artist IS NOT NULL 
        AND artist != ''
        AND artist != 'Unknown Artist'
      ORDER BY artist
      LIMIT 1000
    `;
        return result.map(r => r.artist);
    }
    async getDistinctGenres() {
        const result = await prisma_1.default.$queryRaw `
      SELECT DISTINCT genre 
      FROM quiz_tracks 
      WHERE genre IS NOT NULL 
        AND genre != ''
        AND genre != 'Unknown'
      ORDER BY genre
      LIMIT 100
    `;
        return result.map(r => r.genre);
    }
    async getDistinctAlbums() {
        const result = await prisma_1.default.$queryRaw `
      SELECT DISTINCT album 
      FROM quiz_tracks 
      WHERE album IS NOT NULL 
        AND album != ''
        AND album != 'Unknown Album'
      ORDER BY album
      LIMIT 1000
    `;
        return result.map(r => r.album);
    }
    async getDistinctTitles() {
        const result = await prisma_1.default.$queryRaw `
      SELECT DISTINCT title 
      FROM quiz_tracks 
      WHERE title IS NOT NULL 
        AND title != ''
        AND title != 'Unknown Title'
      ORDER BY title
      LIMIT 1000
    `;
        return result.map(r => r.title);
    }
    async getDistinctYears() {
        const result = await prisma_1.default.$queryRaw `
      SELECT DISTINCT year 
      FROM quiz_tracks 
      WHERE year IS NOT NULL 
        AND year > 1900
        AND year <= EXTRACT(YEAR FROM CURRENT_DATE)
      ORDER BY year DESC
      LIMIT 200
    `;
        return result.map(r => r.year);
    }
    async getRandomArtists(count, exclude) {
        const data = await this.preloadData();
        const filtered = exclude
            ? data.artists.filter(a => a !== exclude)
            : data.artists;
        return this.shuffleArray(filtered).slice(0, count);
    }
    async getRandomGenres(count, exclude) {
        const data = await this.preloadData();
        const filtered = exclude
            ? data.genres.filter(g => g !== exclude)
            : data.genres;
        return this.shuffleArray(filtered).slice(0, count);
    }
    async getRandomAlbums(count, exclude) {
        const data = await this.preloadData();
        const filtered = exclude
            ? data.albums.filter(a => a !== exclude)
            : data.albums;
        return this.shuffleArray(filtered).slice(0, count);
    }
    async getRandomTitles(count, exclude) {
        const data = await this.preloadData();
        const filtered = exclude
            ? data.titles.filter(t => t !== exclude)
            : data.titles;
        return this.shuffleArray(filtered).slice(0, count);
    }
    async getRandomYears(count, exclude) {
        const data = await this.preloadData();
        const filtered = exclude
            ? data.years.filter(y => y !== exclude)
            : data.years;
        return this.shuffleArray(filtered).slice(0, count);
    }
    async getQuizTracks(count, filters) {
        const whereClause = {
            AND: [
                { artist: { not: null } },
                { title: { not: null } },
                { mpdFilePath: { not: null } }
            ]
        };
        if (filters?.genre) {
            whereClause.genre = filters.genre;
        }
        if (filters?.yearFrom || filters?.yearTo) {
            whereClause.year = {
                gte: filters.yearFrom || 1900,
                lte: filters.yearTo || new Date().getFullYear()
            };
        }
        if (filters?.category) {
            whereClause.quizCategories = {
                contains: filters.category
            };
        }
        const tracks = await prisma_1.default.$queryRaw `
      SELECT * FROM quiz_tracks
      WHERE artist IS NOT NULL 
        AND title IS NOT NULL
        AND mpd_file_path IS NOT NULL
        ${filters?.genre ? client_1.Prisma.sql `AND genre = ${filters.genre}` : client_1.Prisma.sql ``}
        ${filters?.yearFrom ? client_1.Prisma.sql `AND year >= ${filters.yearFrom}` : client_1.Prisma.sql ``}
        ${filters?.yearTo ? client_1.Prisma.sql `AND year <= ${filters.yearTo}` : client_1.Prisma.sql ``}
        ${filters?.category ? client_1.Prisma.sql `AND quiz_categories::text LIKE ${`%${filters.category}%`}` : client_1.Prisma.sql ``}
      ORDER BY RANDOM()
      LIMIT ${count}
    `;
        return tracks;
    }
    async generateQuizQuestions(tracks, gameMode, preloadedData) {
        const data = preloadedData || await this.preloadData();
        const questions = tracks.map(track => {
            const wrongArtists = data.artists
                .filter(a => a !== track.artist)
                .sort(() => Math.random() - 0.5)
                .slice(0, 3);
            const wrongAlbums = data.albums
                .filter(a => a !== track.album)
                .sort(() => Math.random() - 0.5)
                .slice(0, 3);
            const wrongTitles = data.titles
                .filter(t => t !== track.title)
                .sort(() => Math.random() - 0.5)
                .slice(0, 3);
            const wrongYears = data.years
                .filter(y => y !== track.year)
                .sort(() => Math.random() - 0.5)
                .slice(0, 3);
            return {
                track,
                wrongArtists,
                wrongAlbums,
                wrongTitles,
                wrongYears
            };
        });
        return questions;
    }
    clearCache() {
        this.preloadedData = null;
        this.lastPreloadTime = 0;
    }
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
}
exports.OptimizedQuizData = OptimizedQuizData;
//# sourceMappingURL=quiz-data-optimized.js.map