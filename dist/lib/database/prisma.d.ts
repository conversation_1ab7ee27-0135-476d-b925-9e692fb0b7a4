import { PrismaClient, Prisma } from '@prisma/client';
export declare class ArrayUtils {
    static toJsonString(array: string[]): string;
    static fromJsonString(jsonString: string): string[];
    static addToJsonArray(jsonString: string, item: string): string;
    static removeFromJsonArray(jsonString: string, item: string): string;
    static objectToJsonString(obj: any): string;
    static objectFromJsonString(jsonString: string): any;
}
declare const prisma: PrismaClient<Prisma.PrismaClientOptions, never, import("@prisma/client/runtime/library").DefaultArgs>;
export declare const AuthUtils: {
    hashPassword(password: string): Promise<string>;
    verifyPassword(password: string, hashedPassword: string): Promise<boolean>;
    createUser(data: {
        email: string;
        username: string;
        displayName?: string;
        password: string;
        role?: string;
    }): Promise<{
        id: string;
        createdAt: Date;
        role: string;
        password: string | null;
        email: string;
        username: string;
        displayName: string | null;
        avatarUrl: string | null;
        updatedAt: Date;
        lastLogin: Date | null;
        preferences: string;
        lastActive: Date | null;
        autoQueuePreferences: string;
    }>;
    findUserByCredentials(email: string, password: string): Promise<{
        id: string;
        createdAt: Date;
        role: string;
        password: string | null;
        email: string;
        username: string;
        displayName: string | null;
        avatarUrl: string | null;
        updatedAt: Date;
        lastLogin: Date | null;
        preferences: string;
        lastActive: Date | null;
        autoQueuePreferences: string;
    } | null>;
};
export declare function checkDatabaseConnection(): Promise<boolean>;
export declare function disconnectDatabase(): Promise<void>;
export { prisma };
export default prisma;
export declare function testDatabaseConnection(): Promise<boolean>;
export declare class DatabaseError extends Error {
    readonly operation: string;
    readonly originalError?: any | undefined;
    constructor(message: string, operation: string, originalError?: any | undefined);
}
export declare class QuizUtils {
    static updateTrackStats(trackId: string, wasCorrect: boolean): Promise<void>;
    static updateUserStats(userId: string, sessionId: string): Promise<void>;
    private static updateModeSpecificStats;
    static getRandomTracks(count: number, filters?: {
        genre?: string;
        decade?: string;
        difficulty?: number;
        excludeIds?: string[];
        minPopularity?: number;
        minLastPlayedMinutes?: number;
        category?: string;
        thematicTag?: string;
        specialList?: string;
    }): Promise<QuizTrack[]>;
    static getAvailableGenres(): Promise<string[]>;
    static getAvailableDecades(): Promise<string[]>;
    static getLibraryStats(): Promise<{
        totalTracks: number;
        byGenre: Record<string, number>;
        byDecade: Record<string, number>;
        byDifficulty: Record<number, number>;
        lastSyncDate: Date | null;
    }>;
}
export type { User, QuizTrack, GameSession, GameAnswer, UserStats, Achievement, UserAchievement, Artist, Album } from '@prisma/client';
export { Prisma } from '@prisma/client';
