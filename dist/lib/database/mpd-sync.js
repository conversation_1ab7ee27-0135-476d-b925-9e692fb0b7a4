"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MPDLibrarySync = void 0;
const prisma_1 = __importStar(require("./prisma"));
class MPDLibrarySync {
    constructor(mpdClient) {
        this.mpdClient = mpdClient;
        this.isRunning = false;
    }
    onProgress(callback) {
        this.progressCallback = callback;
    }
    async syncMPDLibrary(options = {}) {
        if (this.isRunning) {
            throw new Error('Sync operation already in progress');
        }
        const startTime = Date.now();
        this.isRunning = true;
        const result = {
            success: false,
            tracksDiscovered: 0,
            tracksProcessed: 0,
            tracksUpdated: 0,
            tracksCreated: 0,
            errors: [],
            duration: 0
        };
        try {
            console.log('[MPD-Sync] Starting library synchronization...');
            this.reportProgress('discovering', 0, 0, 'Discovering tracks from MPD library...');
            const mpdTracks = await this.mpdClient.listAllTracks();
            result.tracksDiscovered = mpdTracks.length;
            console.log(`[MPD-Sync] Found ${mpdTracks.length} tracks in MPD library`);
            if (mpdTracks.length === 0) {
                throw new Error('No tracks found in MPD library');
            }
            const batchSize = options.batchSize || 50;
            let processed = 0;
            for (let i = 0; i < mpdTracks.length; i += batchSize) {
                const batch = mpdTracks.slice(i, i + batchSize);
                const batchEnd = Math.min(i + batchSize, mpdTracks.length);
                this.reportProgress('processing', processed, mpdTracks.length, `Processing tracks ${i + 1}-${batchEnd} of ${mpdTracks.length}...`);
                try {
                    const batchResult = await this.processBatch(batch, options.forceUpdate || false, options.preview || false);
                    result.tracksProcessed += batchResult.processed;
                    result.tracksCreated += batchResult.created;
                    result.tracksUpdated += batchResult.updated;
                    processed += batch.length;
                }
                catch (error) {
                    const errorMsg = `Batch ${i + 1}-${batchEnd} failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
                    console.error('[MPD-Sync]', errorMsg);
                    result.errors.push(errorMsg);
                }
            }
            this.reportProgress('updating', processed, mpdTracks.length, 'Updating statistics...');
            result.success = result.errors.length === 0;
            result.duration = Date.now() - startTime;
            this.reportProgress('complete', processed, mpdTracks.length, `Sync complete: ${result.tracksCreated} created, ${result.tracksUpdated} updated`);
            console.log('[MPD-Sync] Library synchronization complete:', result);
        }
        catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error during sync';
            console.error('[MPD-Sync] Synchronization failed:', error);
            result.errors.push(errorMsg);
            result.duration = Date.now() - startTime;
            this.reportProgress('error', 0, 0, `Sync failed: ${errorMsg}`);
        }
        finally {
            this.isRunning = false;
        }
        return result;
    }
    async getEnhancedTrack(mpdFilePath) {
        try {
            const mpdTracks = await this.mpdClient.search('file', mpdFilePath);
            if (mpdTracks.length === 0) {
                console.warn(`[MPD-Sync] Track not found in MPD: ${mpdFilePath}`);
                return null;
            }
            const mpdTrack = mpdTracks[0];
            const quizTrack = await prisma_1.default.quizTrack.findUnique({
                where: { mpdFilePath }
            });
            const enhancedTrack = {
                ...mpdTrack,
                quizTrackId: quizTrack?.id,
                difficultyRating: quizTrack?.difficultyRating || 3,
                popularityScore: quizTrack?.popularityScore || 50,
                triviaFacts: prisma_1.ArrayUtils.fromJsonString(quizTrack?.triviaFacts || '[]'),
                albumArtUrl: quizTrack?.albumArtUrl || undefined,
                artistImageUrl: quizTrack?.artistImageUrl || undefined,
                chartPosition: quizTrack?.chartPosition || undefined,
                chartCountry: quizTrack?.chartCountry || 'US',
                interestingFacts: prisma_1.ArrayUtils.objectFromJsonString(quizTrack?.interestingFacts || '{}'),
                similarArtists: prisma_1.ArrayUtils.fromJsonString(quizTrack?.similarArtists || '[]'),
                timesPlayed: quizTrack?.timesPlayed || 0,
                correctAnswers: quizTrack?.correctAnswers || 0,
                lastPlayed: quizTrack?.lastPlayed || undefined
            };
            return enhancedTrack;
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get enhanced track:', error);
            return null;
        }
    }
    async searchEnhancedTracks(query, filters = {}) {
        try {
            const whereClause = {};
            if (query.trim()) {
                whereClause.OR = [
                    { title: { contains: query } },
                    { artist: { contains: query } },
                    { album: { contains: query } }
                ];
            }
            if (filters.genre) {
                whereClause.genre = filters.genre;
            }
            if (filters.artist) {
                whereClause.artist = filters.artist;
            }
            if (filters.decade) {
                const startYear = parseInt(filters.decade);
                const endYear = startYear + 9;
                whereClause.year = {
                    gte: startYear,
                    lte: endYear
                };
            }
            if (filters.difficulty) {
                whereClause.difficultyRating = filters.difficulty;
            }
            if (filters.minPopularity || filters.maxPopularity) {
                whereClause.popularityScore = {};
                if (filters.minPopularity) {
                    ;
                    whereClause.popularityScore.gte = filters.minPopularity;
                }
                if (filters.maxPopularity) {
                    ;
                    whereClause.popularityScore.lte = filters.maxPopularity;
                }
            }
            const quizTracks = await prisma_1.default.quizTrack.findMany({
                where: whereClause,
                take: filters.limit || undefined,
                orderBy: [
                    { popularityScore: 'desc' },
                    { year: 'desc' }
                ]
            });
            const enhancedTracks = quizTracks.map((track) => ({
                file: track.mpdFilePath,
                title: track.title || 'Unknown Title',
                artist: track.artist || 'Unknown Artist',
                album: track.album || 'Unknown Album',
                date: track.year?.toString() || '',
                genre: track.genre || 'Unknown',
                time: track.duration || 0,
                quizTrackId: track.id,
                difficultyRating: track.difficultyRating,
                popularityScore: track.popularityScore,
                triviaFacts: prisma_1.ArrayUtils.fromJsonString(track.triviaFacts),
                albumArtUrl: track.albumArtUrl || undefined,
                artistImageUrl: track.artistImageUrl || undefined,
                chartPosition: track.chartPosition || undefined,
                chartCountry: track.chartCountry,
                interestingFacts: prisma_1.ArrayUtils.objectFromJsonString(track.interestingFacts),
                similarArtists: prisma_1.ArrayUtils.fromJsonString(track.similarArtists),
                timesPlayed: track.timesPlayed,
                correctAnswers: track.correctAnswers,
                lastPlayed: track.lastPlayed || undefined
            }));
            return enhancedTracks;
        }
        catch (error) {
            console.error('[MPD-Sync] Search failed:', error);
            throw new prisma_1.DatabaseError('Failed to search enhanced tracks', 'searchEnhancedTracks', error instanceof Error ? error : new Error('Unknown error'));
        }
    }
    async getRandomTracks(count, filters = {}) {
        try {
            const perfStart = Date.now();
            if (typeof window !== 'undefined' && typeof document !== 'undefined') {
                console.time('[MPD-Sync] getRandomTracks-browser');
                const params = new URLSearchParams({ count: String(count) });
                for (const [key, value] of Object.entries(filters)) {
                    if (value !== undefined) {
                        params.append(key, String(value));
                    }
                }
                const res = await fetch(`/api/quiz/random-tracks?${params.toString()}`);
                if (!res.ok) {
                    throw new Error(`API error ${res.status}`);
                }
                const data = await res.json();
                console.timeEnd('[MPD-Sync] getRandomTracks-browser');
                return data;
            }
            const tracks = await prisma_1.QuizUtils.getRandomTracks(count, filters);
            const duration = Date.now() - perfStart;
            console.log(`[MPD-Sync] getRandomTracks db fetch ${tracks.length} tracks in ${duration} ms`);
            return tracks.map((track) => ({
                file: track.mpd_file_path || track.mpdFilePath,
                title: track.title || 'Unknown Title',
                artist: track.artist || 'Unknown Artist',
                album: track.album || 'Unknown Album',
                date: track.year?.toString() || '',
                genre: track.genre || 'Unknown',
                time: track.duration || 0,
                quizTrackId: track.id,
                difficultyRating: track.difficultyRating,
                popularityScore: track.popularityScore,
                triviaFacts: prisma_1.ArrayUtils.fromJsonString(track.triviaFacts),
                albumArtUrl: track.albumArtUrl || undefined,
                artistImageUrl: track.artistImageUrl || undefined,
                chartPosition: track.chartPosition || undefined,
                chartCountry: track.chartCountry,
                interestingFacts: prisma_1.ArrayUtils.objectFromJsonString(track.interestingFacts),
                similarArtists: prisma_1.ArrayUtils.fromJsonString(track.similarArtists),
                timesPlayed: track.timesPlayed,
                correctAnswers: track.correctAnswers,
                lastPlayed: track.lastPlayed || undefined,
                lufsVolume: track.lufsVolume || undefined,
                calculatedGain: track.calculatedGain || 0
            }));
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get random tracks:', error);
            throw error;
        }
    }
    async updateTrackStats(trackId, wasCorrect) {
        try {
            await prisma_1.QuizUtils.updateTrackStats(trackId, wasCorrect);
        }
        catch (error) {
            console.error('[MPD-Sync] Track stats update failed:', error);
            throw error;
        }
    }
    async getLibraryStats() {
        try {
            return await prisma_1.QuizUtils.getLibraryStats();
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get library stats:', error);
            throw error;
        }
    }
    async processBatch(tracks, forceUpdate = false, preview = false) {
        try {
            let created = 0;
            let updated = 0;
            for (const track of tracks) {
                try {
                    const fileFingerprint = `mpd-${Buffer.from(track.file).toString('base64').slice(0, 32)}`;
                    const fileSize = track.time ? track.time * 44100 * 2 * 2 : 0;
                    const fileMtime = new Date();
                    const trackData = {
                        mpdFilePath: track.file,
                        fileFingerprint,
                        fileSize,
                        fileMtime,
                        title: track.title || this.extractTitleFromPath(track.file),
                        artist: track.artist || 'Unknown Artist',
                        album: track.album || 'Unknown Album',
                        year: track.date ? parseInt(track.date) || null : null,
                        genre: track.genre || 'Unknown',
                        duration: track.time || 0,
                        quizCategories: prisma_1.ArrayUtils.toJsonString([]),
                        thematicTags: prisma_1.ArrayUtils.toJsonString([]),
                        specialLists: prisma_1.ArrayUtils.toJsonString([]),
                        similarArtists: prisma_1.ArrayUtils.toJsonString([]),
                        triviaFacts: prisma_1.ArrayUtils.toJsonString([]),
                        chartData: prisma_1.ArrayUtils.objectToJsonString({}),
                        culturalContext: prisma_1.ArrayUtils.objectToJsonString({}),
                        interestingFacts: prisma_1.ArrayUtils.objectToJsonString({}),
                        syncedAt: new Date()
                    };
                    if (preview) {
                        const existing = await prisma_1.default.quizTrack.findUnique({ where: { mpdFilePath: track.file } });
                        if (!existing) {
                            console.log(`(preview) CREATE -> ${track.file}`);
                            console.table([{ field: 'new', ...trackData }]);
                            created++;
                        }
                        else {
                            const diff = [];
                            for (const [k, v] of Object.entries(trackData)) {
                                const cur = existing[k];
                                if (JSON.stringify(cur) !== JSON.stringify(v)) {
                                    diff.push({ field: k, current: cur, new: v });
                                }
                            }
                            if (diff.length > 0) {
                                console.log(`(preview) UPDATE -> ${track.file}`);
                                console.table(diff);
                                updated++;
                            }
                        }
                        continue;
                    }
                    if (forceUpdate) {
                        const result = await prisma_1.default.quizTrack.upsert({
                            where: { mpdFilePath: track.file },
                            update: {
                                ...trackData,
                                updatedAt: new Date()
                            },
                            create: {
                                ...trackData,
                                difficultyRating: 3,
                                popularityScore: 50
                            }
                        });
                        updated++;
                    }
                    else {
                        try {
                            if (!preview) {
                                await prisma_1.default.quizTrack.create({
                                    data: {
                                        ...trackData,
                                        difficultyRating: 3,
                                        popularityScore: 50
                                    }
                                });
                            }
                            created++;
                        }
                        catch (error) {
                            if (error && error.code === 'P2002') {
                                continue;
                            }
                            throw error;
                        }
                    }
                }
                catch (error) {
                    console.error(`[MPD-Sync] Failed to process track ${track.file}:`, error);
                }
            }
            return {
                processed: tracks.length,
                created,
                updated
            };
        }
        catch (error) {
            console.error('[MPD-Sync] Batch processing failed:', error);
            throw new prisma_1.DatabaseError('Batch processing failed', 'processBatch', error instanceof Error ? error : new Error('Unknown error'));
        }
    }
    extractTitleFromPath(filePath) {
        const fileName = filePath.split('/').pop() || filePath;
        return fileName.replace(/\.[^/.]+$/, '').replace(/[-_]/g, ' ');
    }
    reportProgress(phase, current, total, message) {
        if (this.progressCallback) {
            this.progressCallback({ phase, current, total, message });
        }
    }
    isSyncRunning() {
        return this.isRunning;
    }
    async getAvailableGenres() {
        try {
            return await prisma_1.QuizUtils.getAvailableGenres();
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get genres:', error);
            return [];
        }
    }
    async getAvailableDecades() {
        try {
            return await prisma_1.QuizUtils.getAvailableDecades();
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get decades:', error);
            return [];
        }
    }
    async getAvailableArtists(limit = 100) {
        try {
            const artists = await prisma_1.default.quizTrack.findMany({
                where: { artist: { not: null } },
                select: { artist: true },
                distinct: ['artist'],
                take: limit
            });
            return artists
                .map((a) => a.artist)
                .filter(Boolean)
                .sort();
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get artists:', error);
            return [];
        }
    }
    async cleanupOrphanedTracks() {
        try {
            console.log('[MPD-Sync] Starting cleanup of orphaned tracks...');
            const mpdTracks = await this.mpdClient.listAllTracks();
            const mpdFilePaths = new Set(mpdTracks.map((track) => track.file));
            const dbTracks = await prisma_1.default.quizTrack.findMany({
                select: { id: true, mpdFilePath: true }
            });
            const orphanedIds = dbTracks
                .filter((track) => !mpdFilePaths.has(track.mpdFilePath))
                .map((track) => track.id);
            if (orphanedIds.length > 0) {
                console.log(`[MPD-Sync] Found ${orphanedIds.length} orphaned tracks, removing...`);
                const deleted = await prisma_1.default.quizTrack.deleteMany({
                    where: { id: { in: orphanedIds } }
                });
                console.log(`[MPD-Sync] Removed ${deleted.count} orphaned tracks`);
                return deleted.count;
            }
            console.log('[MPD-Sync] No orphaned tracks found');
            return 0;
        }
        catch (error) {
            console.error('[MPD-Sync] Cleanup failed:', error);
            throw new prisma_1.DatabaseError('Cleanup failed', 'cleanupOrphanedTracks', error);
        }
    }
}
exports.MPDLibrarySync = MPDLibrarySync;
//# sourceMappingURL=mpd-sync.js.map