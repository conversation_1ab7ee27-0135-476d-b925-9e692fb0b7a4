"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Prisma = exports.QuizUtils = exports.DatabaseError = exports.prisma = exports.AuthUtils = exports.ArrayUtils = void 0;
exports.checkDatabaseConnection = checkDatabaseConnection;
exports.disconnectDatabase = disconnectDatabase;
exports.testDatabaseConnection = testDatabaseConnection;
const client_1 = require("@prisma/client");
const query_optimizations_1 = require("./query-optimizations");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const path = __importStar(require("path"));
const POSTGRES_URL = "***********************************************************************/music_quiz?schema=public";
function resolveDatabaseUrl() {
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
        return undefined;
    }
    if (databaseUrl.startsWith('file:./')) {
        const relativePath = databaseUrl.substring(5);
        const absolutePath = path.resolve(process.cwd(), relativePath);
        const resolvedUrl = `file:${absolutePath}`;
        console.log(`[Database] Resolved relative path: ${databaseUrl} -> ${resolvedUrl}`);
        return resolvedUrl;
    }
    return databaseUrl;
}
const resolvedDatabaseUrl = resolveDatabaseUrl();
if (resolvedDatabaseUrl) {
    process.env.DATABASE_URL = resolvedDatabaseUrl;
}
class ArrayUtils {
    static toJsonString(array) {
        return JSON.stringify(array || []);
    }
    static fromJsonString(jsonString) {
        try {
            const parsed = JSON.parse(jsonString || '[]');
            return Array.isArray(parsed) ? parsed : [];
        }
        catch {
            return [];
        }
    }
    static addToJsonArray(jsonString, item) {
        const array = this.fromJsonString(jsonString);
        if (!array.includes(item)) {
            array.push(item);
        }
        return this.toJsonString(array);
    }
    static removeFromJsonArray(jsonString, item) {
        const array = this.fromJsonString(jsonString);
        const filtered = array.filter(i => i !== item);
        return this.toJsonString(filtered);
    }
    static objectToJsonString(obj) {
        return JSON.stringify(obj || {});
    }
    static objectFromJsonString(jsonString) {
        try {
            return JSON.parse(jsonString || '{}');
        }
        catch {
            return {};
        }
    }
}
exports.ArrayUtils = ArrayUtils;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ??
    new client_1.PrismaClient({
        log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
        errorFormat: 'minimal',
        datasources: {
            db: {
                url: process.env.DATABASE_URL + (process.env.DATABASE_URL?.includes('?') ? '&' : '?') +
                    'connection_limit=5&' +
                    'pool_timeout=10&' +
                    'connect_timeout=10&' +
                    'socket_timeout=10&' +
                    'statement_cache_size=100'
            }
        }
    });
exports.prisma = prisma;
if (process.env.NODE_ENV !== 'production')
    globalForPrisma.prisma = prisma;
exports.AuthUtils = {
    async hashPassword(password) {
        return await bcryptjs_1.default.hash(password, 12);
    },
    async verifyPassword(password, hashedPassword) {
        return await bcryptjs_1.default.compare(password, hashedPassword);
    },
    async createUser(data) {
        const hashedPassword = await this.hashPassword(data.password);
        return await prisma.user.create({
            data: {
                ...data,
                email: data.email.toLowerCase(),
                password: hashedPassword,
                role: data.role || 'user'
            }
        });
    },
    async findUserByCredentials(email, password) {
        const user = await prisma.user.findUnique({
            where: { email: email.toLowerCase() }
        });
        if (!user || !user.password) {
            return null;
        }
        const isValid = await this.verifyPassword(password, user.password);
        if (!isValid) {
            return null;
        }
        return user;
    }
};
async function checkDatabaseConnection() {
    try {
        await prisma.$connect();
        await prisma.$queryRaw `SELECT 1`;
        return true;
    }
    catch (error) {
        console.error('Database connection failed:', error);
        return false;
    }
}
async function disconnectDatabase() {
    await prisma.$disconnect();
}
exports.default = prisma;
async function testDatabaseConnection() {
    try {
        await prisma.$connect();
        await prisma.quizTrack.findFirst();
        return true;
    }
    catch (error) {
        console.error('[Database] Connection test failed:', error);
        return false;
    }
}
class DatabaseError extends Error {
    constructor(message, operation, originalError) {
        super(`Database ${operation} failed: ${message}`);
        this.operation = operation;
        this.originalError = originalError;
        this.name = 'DatabaseError';
    }
}
exports.DatabaseError = DatabaseError;
class QuizUtils {
    static async updateTrackStats(trackId, wasCorrect) {
        try {
            await prisma.quizTrack.update({
                where: { id: trackId },
                data: {
                    timesPlayed: { increment: 1 },
                    correctAnswers: wasCorrect ? { increment: 1 } : undefined,
                    lastPlayed: new Date()
                }
            });
        }
        catch (error) {
            throw new DatabaseError(`Failed to update track statistics for ${trackId}`, 'updateTrackStats', error);
        }
    }
    static async updateUserStats(userId, sessionId) {
        try {
            const session = await prisma.gameSession.findUnique({
                where: { id: sessionId },
                include: {
                    gameAnswers: true
                }
            });
            if (!session || session.userId !== userId) {
                throw new Error('Session not found or unauthorized');
            }
            const totalSessions = await prisma.gameSession.count({
                where: {
                    userId,
                    completedAt: { not: null }
                }
            });
            const allAnswers = await prisma.gameAnswer.findMany({
                where: {
                    gameSession: {
                        userId,
                        completedAt: { not: null }
                    }
                }
            });
            const totalQuestions = allAnswers.length;
            const totalCorrect = allAnswers.filter(a => a.isCorrect).length;
            const averageAccuracy = totalQuestions > 0 ? (totalCorrect / totalQuestions) * 100 : 0;
            const totalResponseTime = allAnswers
                .filter(a => a.timeSpent)
                .reduce((sum, a) => sum + (a.timeSpent || 0), 0);
            const averageResponseTime = allAnswers.length > 0 ? totalResponseTime / allAnswers.length : 0;
            await prisma.userStats.upsert({
                where: { userId },
                update: {
                    totalGamesPlayed: totalSessions,
                    totalQuestionsAnswered: totalQuestions,
                    totalCorrectAnswers: totalCorrect,
                    averageAccuracy,
                    averageResponseTime,
                    bestScore: {
                        set: Math.max(session.totalScore, (await prisma.userStats.findUnique({ where: { userId } }))?.bestScore || 0)
                    },
                    longestStreak: {
                        set: Math.max(session.maxStreak, (await prisma.userStats.findUnique({ where: { userId } }))?.longestStreak || 0)
                    },
                    lastGamePlayed: new Date()
                },
                create: {
                    userId,
                    totalGamesPlayed: totalSessions,
                    totalQuestionsAnswered: totalQuestions,
                    totalCorrectAnswers: totalCorrect,
                    averageAccuracy,
                    averageResponseTime,
                    bestScore: session.totalScore,
                    longestStreak: session.maxStreak,
                    lastGamePlayed: new Date()
                }
            });
            await this.updateModeSpecificStats(userId, session.gameMode, session.totalScore);
        }
        catch (error) {
            throw new DatabaseError(`Failed to update user statistics for ${userId}`, 'updateUserStats', error);
        }
    }
    static async updateModeSpecificStats(userId, gameMode, score) {
        const updateData = {};
        switch (gameMode) {
            case 'classic':
                updateData.classicGamesPlayed = { increment: 1 };
                updateData.classicBestScore = { set: Math.max(score, 0) };
                break;
            case 'chart-position':
                updateData.chartPositionGamesPlayed = { increment: 1 };
                updateData.chartPositionBestScore = { set: Math.max(score, 0) };
                break;
            case 'decade-challenge':
                updateData.decadeChallengeGamesPlayed = { increment: 1 };
                updateData.decadeChallengeBestScore = { set: Math.max(score, 0) };
                break;
            case 'genre-specialist':
                updateData.genreSpecialistGamesPlayed = { increment: 1 };
                updateData.genreSpecialistBestScore = { set: Math.max(score, 0) };
                break;
        }
        if (Object.keys(updateData).length > 0) {
            await prisma.userStats.upsert({
                where: { userId },
                update: updateData,
                create: {
                    userId,
                    ...updateData
                }
            });
        }
    }
    static async getRandomTracks(count, filters = {}) {
        let whereClause = {};
        try {
            if (filters.genre) {
                whereClause.genre = filters.genre;
            }
            if (filters.decade) {
                const startYear = parseInt(filters.decade);
                const endYear = startYear + 9;
                whereClause.year = {
                    gte: startYear,
                    lte: endYear
                };
            }
            if (filters.difficulty) {
                whereClause.difficultyRating = { lte: filters.difficulty };
            }
            if (filters.minPopularity) {
                whereClause.popularityScore = { gte: filters.minPopularity };
            }
            if (filters.excludeIds && filters.excludeIds.length > 0) {
                whereClause.id = { notIn: filters.excludeIds };
            }
            if (filters.minLastPlayedMinutes !== undefined) {
                const cutoffMs = Date.now() - filters.minLastPlayedMinutes * 60 * 1000;
                whereClause.OR = [
                    { lastPlayed: null },
                    { lastPlayed: { lt: new Date(cutoffMs) } }
                ];
            }
            if (filters.category) {
                whereClause.quizCategories = { contains: `"${filters.category}"` };
            }
            if (filters.thematicTag) {
                whereClause.thematicTags = { contains: `"${filters.thematicTag}"` };
            }
            if (filters.specialList) {
                whereClause.specialLists = { contains: `"${filters.specialList}"` };
            }
            try {
                const isPostgreSQL = process.env.DATABASE_URL?.includes('postgresql');
                const tableName = isPostgreSQL ? '"quiz_tracks"' : 'QuizTrack';
                const rawWhere = [];
                const params = [];
                let paramCount = 0;
                if (filters.genre) {
                    paramCount++;
                    rawWhere.push(isPostgreSQL ? `genre = $${paramCount}` : `genre = ?`);
                    params.push(filters.genre);
                }
                if (filters.decade) {
                    const startYear = parseInt(filters.decade);
                    const endYear = startYear + 9;
                    if (isPostgreSQL) {
                        rawWhere.push(`year BETWEEN $${++paramCount} AND $${++paramCount}`);
                    }
                    else {
                        rawWhere.push(`year BETWEEN ? AND ?`);
                    }
                    params.push(startYear, endYear);
                }
                if (filters.difficulty) {
                    paramCount++;
                    rawWhere.push(isPostgreSQL ? `"difficulty_rating" = $${paramCount}` : `difficultyRating = ?`);
                    params.push(filters.difficulty);
                }
                if (filters.minPopularity) {
                    paramCount++;
                    rawWhere.push(isPostgreSQL ? `"popularity_score" >= $${paramCount}` : `popularityScore >= ?`);
                    params.push(filters.minPopularity);
                }
                if (filters.excludeIds && filters.excludeIds.length > 0) {
                    if (isPostgreSQL) {
                        const placeholders = filters.excludeIds.map((_, i) => `$${++paramCount}`).join(',');
                        rawWhere.push(`id NOT IN (${placeholders})`);
                    }
                    else {
                        const placeholders = filters.excludeIds.map(() => '?').join(',');
                        rawWhere.push(`id NOT IN (${placeholders})`);
                    }
                    params.push(...filters.excludeIds);
                }
                if (filters.minLastPlayedMinutes !== undefined) {
                    const cutoffMs = Date.now() - filters.minLastPlayedMinutes * 60 * 1000;
                    paramCount++;
                    rawWhere.push(isPostgreSQL ? `("last_played" IS NULL OR "last_played" < $${paramCount}::timestamp)` : `(lastPlayed IS NULL OR lastPlayed < ?)`);
                    params.push(new Date(cutoffMs).toISOString());
                }
                if (filters.category) {
                    paramCount++;
                    rawWhere.push(isPostgreSQL ? `"quiz_categories"::text LIKE $${paramCount}` : `quiz_categories LIKE ?`);
                    params.push(`%"${filters.category}"%`);
                }
                if (filters.thematicTag) {
                    paramCount++;
                    rawWhere.push(isPostgreSQL ? `"thematic_tags"::text LIKE $${paramCount}` : `thematic_tags LIKE ?`);
                    params.push(`%"${filters.thematicTag}"%`);
                }
                if (filters.specialList) {
                    paramCount++;
                    rawWhere.push(isPostgreSQL ? `"special_lists"::text LIKE $${paramCount}` : `special_lists LIKE ?`);
                    params.push(`%"${filters.specialList}"%`);
                }
                const whereSql = rawWhere.length > 0 ? `WHERE ${rawWhere.join(' AND ')}` : '';
                paramCount++;
                const sql = `SELECT * FROM ${tableName} ${whereSql} ORDER BY RANDOM() LIMIT ${isPostgreSQL ? `$${paramCount}` : '?'}`;
                params.push(count);
                const tracks = await prisma.$queryRawUnsafe(sql, ...params);
                if (tracks && tracks.length >= count) {
                    return tracks;
                }
            }
            catch (rawErr) {
                console.warn('[QuizUtils] RAW random query failed, falling back to Prisma shuffle.', rawErr);
            }
            const fetchMultiplier = Math.max(20, Math.min(50, 2000 / count));
            const tracks = await prisma.quizTrack.findMany({
                where: whereClause,
                select: query_optimizations_1.optimizedSelects.quizTrackQuestion,
                take: Math.min(count * fetchMultiplier, 2000)
            });
            const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
            const shuffled = RandomUtils.shuffle(tracks);
            return shuffled.slice(0, count);
        }
        catch (error) {
            console.error('[getRandomTracks] whereClause:', whereClause);
            console.error('[getRandomTracks] raw error →', error);
            throw new DatabaseError('Failed to get random tracks', 'getRandomTracks', error);
        }
    }
    static async getAvailableGenres() {
        try {
            const result = await prisma.quizTrack.findMany({
                where: { genre: { not: null } },
                select: { genre: true },
                distinct: ['genre']
            });
            return result
                .map(r => r.genre)
                .filter(Boolean)
                .sort();
        }
        catch (error) {
            throw new DatabaseError('Failed to get available genres', 'getAvailableGenres', error);
        }
    }
    static async getAvailableDecades() {
        try {
            const result = await prisma.quizTrack.findMany({
                where: { year: { not: null } },
                select: { year: true },
                distinct: ['year']
            });
            const decades = [...new Set(result
                    .map(r => Math.floor(r.year / 10) * 10)
                    .map(decade => `${decade}s`))].sort();
            return decades;
        }
        catch (error) {
            throw new DatabaseError('Failed to get available decades', 'getAvailableDecades', error);
        }
    }
    static async getLibraryStats() {
        try {
            const totalTracks = await prisma.quizTrack.count();
            const genreStats = await prisma.quizTrack.groupBy({
                by: ['genre'],
                _count: true,
                where: { genre: { not: null } }
            });
            const yearStats = await prisma.quizTrack.groupBy({
                by: ['year'],
                _count: true,
                where: { year: { not: null } }
            });
            const difficultyStats = await prisma.quizTrack.groupBy({
                by: ['difficultyRating'],
                _count: true
            });
            const lastSync = await prisma.quizTrack.findFirst({
                select: { syncedAt: true },
                orderBy: { syncedAt: 'desc' }
            });
            const byGenre = {};
            genreStats.forEach(stat => {
                if (stat.genre) {
                    byGenre[stat.genre] = stat._count;
                }
            });
            const byDecade = {};
            yearStats.forEach(stat => {
                if (stat.year) {
                    const decade = `${Math.floor(stat.year / 10) * 10}s`;
                    byDecade[decade] = (byDecade[decade] || 0) + stat._count;
                }
            });
            const byDifficulty = {};
            difficultyStats.forEach(stat => {
                byDifficulty[stat.difficultyRating] = stat._count;
            });
            return {
                totalTracks,
                byGenre,
                byDecade,
                byDifficulty,
                lastSyncDate: lastSync?.syncedAt || null
            };
        }
        catch (error) {
            throw new DatabaseError('Failed to get library statistics', 'getLibraryStats', error);
        }
    }
}
exports.QuizUtils = QuizUtils;
var client_2 = require("@prisma/client");
Object.defineProperty(exports, "Prisma", { enumerable: true, get: function () { return client_2.Prisma; } });
//# sourceMappingURL=prisma.js.map