export interface ConnectionHealth {
    isConnected: boolean;
    lastCheck: Date;
    consecutiveFailures: number;
    totalFailures: number;
    uptime: number;
    responseTime?: number;
}
export declare class DatabaseConnectionRecovery {
    private static instance;
    private health;
    private healthCheckInterval;
    private readonly maxRetries;
    private readonly baseDelay;
    private readonly maxDelay;
    private readonly healthCheckIntervalMs;
    private constructor();
    private isBuildTime;
    static getInstance(): DatabaseConnectionRecovery;
    executeWithRecovery<T>(operation: () => Promise<T>, operationName?: string): Promise<T>;
    checkHealth(): Promise<ConnectionHealth>;
    private quickHealthCheck;
    private attemptRecovery;
    private updateHealthOnSuccess;
    private updateHealthOnFailure;
    private isFatalError;
    private startHealthCheck;
    stopHealthCheck(): void;
    getHealth(): ConnectionHealth;
    private sleep;
    shutdown(): Promise<void>;
}
export declare const dbRecovery: DatabaseConnectionRecovery;
