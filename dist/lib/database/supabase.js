"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testDatabaseConnection = exports.DatabaseError = exports.isSupabaseConfigured = exports.createSupabaseAdmin = exports.supabase = void 0;
const supabase_js_1 = require("@supabase/supabase-js");
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables. Please check your .env.local file.');
}
exports.supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseAnonKey, {
    auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
    }
});
const createSupabaseAdmin = () => {
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!serviceRoleKey) {
        throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY for admin operations');
    }
    return (0, supabase_js_1.createClient)(supabaseUrl, serviceRoleKey, {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    });
};
exports.createSupabaseAdmin = createSupabaseAdmin;
const isSupabaseConfigured = () => {
    try {
        return !!(process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
    }
    catch {
        return false;
    }
};
exports.isSupabaseConfigured = isSupabaseConfigured;
class DatabaseError extends Error {
    constructor(message, operation, originalError) {
        super(`Database ${operation} failed: ${message}`);
        this.operation = operation;
        this.originalError = originalError;
        this.name = 'DatabaseError';
    }
}
exports.DatabaseError = DatabaseError;
const testDatabaseConnection = async () => {
    try {
        const { error } = await exports.supabase.from('quiz_tracks').select('id').limit(1);
        return !error;
    }
    catch {
        return false;
    }
};
exports.testDatabaseConnection = testDatabaseConnection;
//# sourceMappingURL=supabase.js.map