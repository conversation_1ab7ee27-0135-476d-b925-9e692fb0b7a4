{"version": 3, "file": "connection-recovery.js", "sourceRoot": "", "sources": ["../../../lib/database/connection-recovery.ts"], "names": [], "mappings": ";;;;;;AAKA,mEAA0C;AAC1C,yCAA6C;AAW7C,MAAa,0BAA0B;IAerC;QAbQ,WAAM,GAAqB;YACjC,WAAW,EAAE,KAAK;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,mBAAmB,EAAE,CAAC;YACtB,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,CAAC;SACV,CAAA;QACO,wBAAmB,GAA0B,IAAI,CAAA;QACxC,eAAU,GAAG,CAAC,CAAA;QACd,cAAS,GAAG,IAAI,CAAA;QAChB,aAAQ,GAAG,KAAK,CAAA;QAChB,0BAAqB,GAAG,KAAK,CAAA;QAI5C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACzB,CAAC;IACH,CAAC;IAKO,WAAW;QACjB,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YACxE,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,wBAAwB;YACnD,OAAO,CAAC,GAAG,CAAC,EAAE,KAAK,MAAM;YACzB,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,MAAM,CAAA;IAC1C,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,CAAC;YACzC,0BAA0B,CAAC,QAAQ,GAAG,IAAI,0BAA0B,EAAE,CAAA;QACxE,CAAC;QACD,OAAO,0BAA0B,CAAC,QAAQ,CAAA;IAC5C,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,SAA2B,EAC3B,gBAAwB,oBAAoB;QAE5C,IAAI,SAAS,GAAiB,IAAI,CAAA;QAElC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YAC5D,IAAI,CAAC;gBAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;gBAC/C,IAAI,CAAC,SAAS,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;oBAChC,uBAAc,CAAC,IAAI,CAAC,6BAA6B,aAAa,uBAAuB,CAAC,CAAA;oBACtF,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;gBAC9B,CAAC;gBAGD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBAC5B,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAA;gBAChC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;gBAG3C,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAA;gBAExC,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;oBAChB,uBAAc,CAAC,IAAI,CAAC,GAAG,aAAa,yBAAyB,OAAO,EAAE,CAAC,CAAA;gBACzE,CAAC;gBAED,OAAO,MAAM,CAAA;YAEf,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAc,CAAA;gBAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAA;gBAE5B,uBAAc,CAAC,IAAI,CAAC,GAAG,aAAa,sBAAsB,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,KAAc,CAAC,CAAA;gBAGvG,IAAI,IAAI,CAAC,YAAY,CAAC,KAAc,CAAC,EAAE,CAAC;oBACtC,uBAAc,CAAC,KAAK,CAAC,kBAAkB,aAAa,gBAAgB,EAAE,KAAc,CAAC,CAAA;oBACrF,MAAK;gBACP,CAAC;gBAGD,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;oBAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;oBAChF,uBAAc,CAAC,KAAK,CAAC,WAAW,KAAK,mBAAmB,OAAO,GAAG,CAAC,EAAE,CAAC,CAAA;oBACtE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;oBAGvB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;QAGD,uBAAc,CAAC,KAAK,CAAC,GAAG,aAAa,iBAAiB,IAAI,CAAC,UAAU,WAAW,EAAE,SAAU,CAAC,CAAA;QAC7F,MAAM,SAAU,CAAA;IAClB,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAE5B,IAAI,CAAC;YACH,MAAM,gBAAM,CAAC,SAAS,CAAA,UAAU,CAAA;YAChC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YAE3C,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAA;YAC9B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAA;YAClC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,YAAY,CAAA;YACvC,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,CAAC,CAAA;YACnC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,qBAAqB,CAAA;YAEhD,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;QAE3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YAE3C,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAA;YAC/B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAA;YAClC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,YAAY,CAAA;YACvC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA;YACjC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAA;YAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA;YAEtB,uBAAc,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAc,CAAC,CAAA;YACpE,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;QAC3B,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,MAAM,gBAAM,CAAC,SAAS,CAAA,UAAU,CAAA;YAChC,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,uBAAc,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAA;YAGjE,MAAM,gBAAM,CAAC,WAAW,EAAE,CAAA;YAC1B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACtB,MAAM,gBAAM,CAAC,QAAQ,EAAE,CAAA;YAGvB,MAAM,gBAAM,CAAC,SAAS,CAAA,UAAU,CAAA;YAEhC,uBAAc,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAA;YAC9D,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,CAAC,CAAA;QAErC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAc,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAc,CAAC,CAAA;YAC3E,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKO,qBAAqB,CAAC,YAAoB;QAChD,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAA;QAC9B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAA;QAClC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,YAAY,CAAA;QACvC,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,CAAC,CAAA;IACrC,CAAC;IAKO,qBAAqB;QAC3B,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAA;QAC/B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAA;QAClC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA;QACjC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAA;IAC7B,CAAC;IAKO,YAAY,CAAC,KAAY;QAC/B,MAAM,aAAa,GAAG;YACpB,WAAW;YACX,OAAO;YACP,OAAO;YACP,uBAAuB;YACvB,mBAAmB;SACpB,CAAA;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAA;QAChD,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAA;IACpF,CAAC;IAKO,gBAAgB;QACtB,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACvB,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;QACzC,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAChD,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YAGxB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC7B,uBAAc,CAAC,IAAI,CAAC,kCAAkC,EAAE;oBACtD,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB;oBACpD,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;oBACxC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;iBACjC,CAAC,CAAA;YACJ,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAA;QAE9B,uBAAc,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAA;IAC3D,CAAC;IAKD,eAAe;QACb,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;YACvC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;YAC/B,uBAAc,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAA;QAC3D,CAAC;IACH,CAAC;IAKD,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;IAC3B,CAAC;IAKO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;IACxD,CAAC;IAKD,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC;YACH,MAAM,gBAAM,CAAC,WAAW,EAAE,CAAA;YAC1B,uBAAc,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAc,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAc,CAAC,CAAA;QACxE,CAAC;IACH,CAAC;;AA7QH,gEA8QC;AA7QgB,mCAAQ,GAAsC,IAAI,AAA1C,CAA0C;AAgRtD,QAAA,UAAU,GAAG,0BAA0B,CAAC,WAAW,EAAE,CAAA;AAGlE,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,wBAAwB,EAAE,CAAC;IAC1F,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;QAC9B,MAAM,kBAAU,CAAC,QAAQ,EAAE,CAAA;QAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC,CAAC,CAAA;IAEF,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;QAC/B,MAAM,kBAAU,CAAC,QAAQ,EAAE,CAAA;QAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC,CAAC,CAAA;AACJ,CAAC"}