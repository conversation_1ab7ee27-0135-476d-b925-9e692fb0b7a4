{"version": 3, "file": "general-quiz-data.js", "sourceRoot": "", "sources": ["../../../lib/database/general-quiz-data.ts"], "names": [], "mappings": ";;;AAMA,2CAA6C;AAG7C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAA;AAsCjC,MAAa,kBAAkB;IAGtB,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACjC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAA;QACxD,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAA;IACpC,CAAC;IAKD,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;gBACxC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACzB,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;aAC9B,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;YACvD,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,EAAU;QAC9B,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;gBACzC,KAAK,EAAE;oBACL,EAAE,EAAE;wBACF,EAAE,EAAE,EAAE,EAAE,EAAE;wBACV,EAAE,IAAI,EAAE,EAAE,EAAE;qBACb;oBACD,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;YAChD,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,UAAkB,EAClB,QAAgB,EAAE,EAClB,UAAmB;QAEnB,IAAI,CAAC;YACH,MAAM,WAAW,GAAQ;gBACvB,QAAQ,EAAE;oBACR,EAAE,EAAE;wBACF,EAAE,EAAE,EAAE,UAAU,EAAE;wBAClB,EAAE,IAAI,EAAE,UAAU,EAAE;qBACrB;iBACF;gBACD,QAAQ,EAAE,IAAI;aACf,CAAA;YAED,IAAI,UAAU,EAAE,CAAC;gBACf,WAAW,CAAC,UAAU,GAAG,UAAU,CAAA;YACrC,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC;gBAC1D,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI;iBACf;gBACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;aAC/B,CAAC,CAAA;YAGF,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAA;YAC1D,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;YAExC,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAA;YAC7D,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,EAAE,UAAmB;QAC7D,IAAI,CAAC;YACH,MAAM,WAAW,GAAQ;gBACvB,QAAQ,EAAE,IAAI;aACf,CAAA;YAED,IAAI,UAAU,EAAE,CAAC;gBACf,WAAW,CAAC,UAAU,GAAG,UAAU,CAAA;YACrC,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC;gBAC1D,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI;iBACf;gBACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;aAC/B,CAAC,CAAA;YAGF,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAA;YAC1D,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;YAExC,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;YACvD,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAC5B,WAAqB,EACrB,QAAgB,EAAE,EAClB,UAAmB;QAEnB,IAAI,CAAC;YACH,MAAM,WAAW,GAAQ;gBACvB,QAAQ,EAAE;oBACR,IAAI,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;iBAC1B;gBACD,QAAQ,EAAE,IAAI;aACf,CAAA;YAED,IAAI,UAAU,EAAE,CAAC;gBACf,WAAW,CAAC,UAAU,GAAG,UAAU,CAAA;YACrC,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC;gBAC1D,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI;iBACf;gBACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;aAC/B,CAAC,CAAA;YAGF,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAA;YAC1D,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;YAExC,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAA;YAC/D,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,UAAmB,EAAE,UAAmB;QAC9D,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,UAAU;gBAC1B,CAAC,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,CAAC,EAAE,UAAU,CAAC;gBAC9D,CAAC,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;YAE/C,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;YACvD,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,UAAkB,EAAE,MAAuB;QAC9D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;aAC1B,CAAC,CAAA;YAEF,IAAI,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAA;YAE3B,OAAO,QAAQ,CAAC,aAAa,KAAK,MAAM,CAAC,MAAM,CAAC,CAAA;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;YAChD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,UAAkB,EAAE,SAAkB,EAAE,SAAiB;QACjF,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC;gBACtC,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;gBACzB,IAAI,EAAE;oBACJ,WAAW,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;oBAC7B,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;oBACxD,WAAW,EAAE;wBAEX,GAAG,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,SAAS,CAAC;qBAC/D;iBACF;aACF,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;QACzD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,UAAmB;QACxC,IAAI,CAAC;YACH,MAAM,WAAW,GAAQ;gBACvB,QAAQ,EAAE,IAAI;aACf,CAAA;YAED,IAAI,UAAU,EAAE,CAAC;gBACf,WAAW,CAAC,QAAQ,GAAG;oBACrB,EAAE,EAAE;wBACF,EAAE,EAAE,EAAE,UAAU,EAAE;wBAClB,EAAE,IAAI,EAAE,UAAU,EAAE;qBACrB;iBACF,CAAA;YACH,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC;gBAC1D,KAAK,EAAE,WAAW;gBAClB,MAAM,EAAE;oBACN,UAAU,EAAE,IAAI;oBAChB,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE;wBACR,MAAM,EAAE;4BACN,IAAI,EAAE,IAAI;4BACV,IAAI,EAAE,IAAI;yBACX;qBACF;iBACF;aACF,CAAC,CAAA;YAEF,MAAM,KAAK,GAAG;gBACZ,KAAK,EAAE,SAAS,CAAC,MAAM;gBACvB,YAAY,EAAE;oBACZ,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,MAAM;oBACnD,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,MAAM;oBACnD,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,MAAM;oBACnD,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,MAAM;oBACnD,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,MAAM;iBACpD;gBACD,MAAM,EAAE;oBACN,iBAAiB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,CAAC,MAAM;oBAC7E,YAAY,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC,MAAM;oBACnE,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,MAAM;iBAC5D;gBACD,UAAU,EAAE,EAA4B;aACzC,CAAA;YAGD,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBACpB,MAAM,YAAY,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAA;gBACpC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YAC5E,CAAC,CAAC,CAAA;YAEF,OAAO,KAAK,CAAA;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;YACtD,OAAO;gBACL,KAAK,EAAE,CAAC;gBACR,YAAY,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBAC9C,MAAM,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;gBAC9D,UAAU,EAAE,EAAE;aACf,CAAA;QACH,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,uBAAuB,CAAC,UAAkB,EAAE,OAAe;QACvE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC;YAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;YACzB,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE;SACjD,CAAC,CAAA;QAEF,IAAI,CAAC,QAAQ;YAAE,OAAO,OAAO,CAAA;QAE7B,MAAM,cAAc,GAAG,QAAQ,CAAC,WAAW,CAAA;QAC3C,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAA;QAGxC,OAAO,CAAC,cAAc,GAAG,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,CAAA;IACrE,CAAC;IAKO,iBAAiB,CAAC,UAAe;QACvC,OAAO;YACL,EAAE,EAAE,UAAU,CAAC,EAAE;YACjB,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC,IAAI;YAClC,UAAU,EAAE,UAAU,CAAC,UAAU;YACjC,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,OAAO,EAAE,UAAU,CAAC,OAAO,IAAI,SAAS;YACxC,aAAa,EAAE,UAAU,CAAC,aAAa;YACvC,WAAW,EAAE,UAAU,CAAC,WAAW,IAAI,SAAS;YAChD,SAAS,EAAE,UAAU,CAAC,SAAS;YAC/B,MAAM,EAAE,UAAU,CAAC,MAAM;YACzB,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,EAAE;SAC9B,CAAA;IACH,CAAC;CACF;AA5TD,gDA4TC;AAED,kBAAe,kBAAkB,CAAC,WAAW,EAAE,CAAA"}