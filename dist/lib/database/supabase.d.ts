import { createClient } from '@supabase/supabase-js';
export declare const supabase: import("@supabase/supabase-js").SupabaseClient<any, "public", any>;
export declare const createSupabaseAdmin: () => import("@supabase/supabase-js").SupabaseClient<any, "public", any>;
export interface Database {
    public: {
        Tables: {
            users: {
                Row: {
                    id: string;
                    email: string;
                    username: string;
                    display_name: string | null;
                    avatar_url: string | null;
                    created_at: string;
                    updated_at: string;
                    last_login: string | null;
                    preferences: UserPreferences;
                };
                Insert: {
                    id?: string;
                    email: string;
                    username: string;
                    display_name?: string | null;
                    avatar_url?: string | null;
                    created_at?: string;
                    updated_at?: string;
                    last_login?: string | null;
                    preferences?: UserPreferences;
                };
                Update: {
                    id?: string;
                    email?: string;
                    username?: string;
                    display_name?: string | null;
                    avatar_url?: string | null;
                    created_at?: string;
                    updated_at?: string;
                    last_login?: string | null;
                    preferences?: UserPreferences;
                };
            };
            quiz_tracks: {
                Row: {
                    id: string;
                    mpd_file_path: string;
                    title: string | null;
                    artist: string | null;
                    album: string | null;
                    year: number | null;
                    genre: string | null;
                    duration: number | null;
                    difficulty_rating: number | null;
                    popularity_score: number | null;
                    chart_position: number | null;
                    chart_country: string | null;
                    chart_date: string | null;
                    release_date: string | null;
                    album_art_url: string | null;
                    artist_image_url: string | null;
                    spotify_preview_url: string | null;
                    youtube_url: string | null;
                    spotify_id: string | null;
                    musicbrainz_id: string | null;
                    discogs_id: number | null;
                    last_fm_id: string | null;
                    trivia_facts: string[] | null;
                    interesting_facts: InterestingFacts | null;
                    times_played: number | null;
                    correct_answers: number | null;
                    average_guess_time: number | null;
                    last_played: string | null;
                    created_at: string;
                    updated_at: string;
                    synced_at: string | null;
                };
                Insert: {
                    id?: string;
                    mpd_file_path: string;
                    title?: string | null;
                    artist?: string | null;
                    album?: string | null;
                    year?: number | null;
                    genre?: string | null;
                    duration?: number | null;
                    difficulty_rating?: number | null;
                    popularity_score?: number | null;
                    chart_position?: number | null;
                    chart_country?: string | null;
                    chart_date?: string | null;
                    release_date?: string | null;
                    album_art_url?: string | null;
                    artist_image_url?: string | null;
                    spotify_preview_url?: string | null;
                    youtube_url?: string | null;
                    spotify_id?: string | null;
                    musicbrainz_id?: string | null;
                    discogs_id?: number | null;
                    last_fm_id?: string | null;
                    trivia_facts?: string[] | null;
                    interesting_facts?: InterestingFacts | null;
                    times_played?: number | null;
                    correct_answers?: number | null;
                    average_guess_time?: number | null;
                    last_played?: string | null;
                    created_at?: string;
                    updated_at?: string;
                    synced_at?: string | null;
                };
                Update: {
                    id?: string;
                    mpd_file_path?: string;
                    title?: string | null;
                    artist?: string | null;
                    album?: string | null;
                    year?: number | null;
                    genre?: string | null;
                    duration?: number | null;
                    difficulty_rating?: number | null;
                    popularity_score?: number | null;
                    chart_position?: number | null;
                    chart_country?: string | null;
                    chart_date?: string | null;
                    release_date?: string | null;
                    album_art_url?: string | null;
                    artist_image_url?: string | null;
                    spotify_preview_url?: string | null;
                    youtube_url?: string | null;
                    spotify_id?: string | null;
                    musicbrainz_id?: string | null;
                    discogs_id?: number | null;
                    last_fm_id?: string | null;
                    trivia_facts?: string[] | null;
                    interesting_facts?: InterestingFacts | null;
                    times_played?: number | null;
                    correct_answers?: number | null;
                    average_guess_time?: number | null;
                    last_played?: string | null;
                    created_at?: string;
                    updated_at?: string;
                    synced_at?: string | null;
                };
            };
            game_sessions: {
                Row: {
                    id: string;
                    user_id: string | null;
                    game_mode: string;
                    total_questions: number;
                    difficulty_level: number | null;
                    time_limit_per_question: number | null;
                    genre_filter: string | null;
                    decade_filter: string | null;
                    artist_filter: string | null;
                    correct_answers: number | null;
                    total_score: number | null;
                    max_streak: number | null;
                    average_response_time: number | null;
                    completion_percentage: number | null;
                    duration_seconds: number | null;
                    started_at: string | null;
                    completed_at: string | null;
                    is_multiplayer: boolean | null;
                    room_code: string | null;
                    multiplayer_data: any | null;
                    session_data: any | null;
                    created_at: string;
                };
                Insert: {
                    id?: string;
                    user_id?: string | null;
                    game_mode: string;
                    total_questions: number;
                    difficulty_level?: number | null;
                    time_limit_per_question?: number | null;
                    genre_filter?: string | null;
                    decade_filter?: string | null;
                    artist_filter?: string | null;
                    correct_answers?: number | null;
                    total_score?: number | null;
                    max_streak?: number | null;
                    average_response_time?: number | null;
                    completion_percentage?: number | null;
                    duration_seconds?: number | null;
                    started_at?: string | null;
                    completed_at?: string | null;
                    is_multiplayer?: boolean | null;
                    room_code?: string | null;
                    multiplayer_data?: any | null;
                    session_data?: any | null;
                    created_at?: string;
                };
                Update: {
                    id?: string;
                    user_id?: string | null;
                    game_mode?: string;
                    total_questions?: number;
                    difficulty_level?: number | null;
                    time_limit_per_question?: number | null;
                    genre_filter?: string | null;
                    decade_filter?: string | null;
                    artist_filter?: string | null;
                    correct_answers?: number | null;
                    total_score?: number | null;
                    max_streak?: number | null;
                    average_response_time?: number | null;
                    completion_percentage?: number | null;
                    duration_seconds?: number | null;
                    started_at?: string | null;
                    completed_at?: string | null;
                    is_multiplayer?: boolean | null;
                    room_code?: string | null;
                    multiplayer_data?: any | null;
                    session_data?: any | null;
                    created_at?: string;
                };
            };
            game_answers: {
                Row: {
                    id: string;
                    session_id: string;
                    quiz_track_id: string | null;
                    question_number: number;
                    question_type: string;
                    question_text: string;
                    question_data: any | null;
                    correct_answer: string;
                    provided_options: string[] | null;
                    user_answer: string | null;
                    is_correct: boolean;
                    time_spent: number | null;
                    points_earned: number | null;
                    streak_at_time: number | null;
                    bonus_points: number | null;
                    audio_start_time: number | null;
                    audio_duration: number | null;
                    audio_volume: number | null;
                    hints_used: number | null;
                    hint_data: any | null;
                    question_started_at: string | null;
                    answered_at: string | null;
                };
                Insert: {
                    id?: string;
                    session_id: string;
                    quiz_track_id?: string | null;
                    question_number: number;
                    question_type: string;
                    question_text: string;
                    question_data?: any | null;
                    correct_answer: string;
                    provided_options?: string[] | null;
                    user_answer?: string | null;
                    is_correct: boolean;
                    time_spent?: number | null;
                    points_earned?: number | null;
                    streak_at_time?: number | null;
                    bonus_points?: number | null;
                    audio_start_time?: number | null;
                    audio_duration?: number | null;
                    audio_volume?: number | null;
                    hints_used?: number | null;
                    hint_data?: any | null;
                    question_started_at?: string | null;
                    answered_at?: string | null;
                };
                Update: {
                    id?: string;
                    session_id?: string;
                    quiz_track_id?: string | null;
                    question_number?: number;
                    question_type?: string;
                    question_text?: string;
                    question_data?: any | null;
                    correct_answer?: string;
                    provided_options?: string[] | null;
                    user_answer?: string | null;
                    is_correct?: boolean;
                    time_spent?: number | null;
                    points_earned?: number | null;
                    streak_at_time?: number | null;
                    bonus_points?: number | null;
                    audio_start_time?: number | null;
                    audio_duration?: number | null;
                    audio_volume?: number | null;
                    hints_used?: number | null;
                    hint_data?: any | null;
                    question_started_at?: string | null;
                    answered_at?: string | null;
                };
            };
            user_stats: {
                Row: {
                    user_id: string;
                    total_games_played: number | null;
                    total_questions_answered: number | null;
                    total_correct_answers: number | null;
                    total_points_earned: number | null;
                    best_score: number | null;
                    longest_streak: number | null;
                    average_score: number | null;
                    average_accuracy: number | null;
                    total_playtime_seconds: number | null;
                    average_response_time: number | null;
                    fastest_correct_answer: number | null;
                    strongest_genre: string | null;
                    strongest_decade: string | null;
                    weakest_genre: string | null;
                    weakest_decade: string | null;
                    classic_games_played: number | null;
                    classic_best_score: number | null;
                    classic_avg_score: number | null;
                    chart_position_games_played: number | null;
                    chart_position_best_score: number | null;
                    chart_position_avg_score: number | null;
                    decade_challenge_games_played: number | null;
                    decade_challenge_best_score: number | null;
                    decade_challenge_avg_score: number | null;
                    genre_specialist_games_played: number | null;
                    genre_specialist_best_score: number | null;
                    genre_specialist_avg_score: number | null;
                    longest_streak_mode: string | null;
                    longest_streak_date: string | null;
                    current_daily_streak: number | null;
                    longest_daily_streak: number | null;
                    last_game_played: string | null;
                    updated_at: string | null;
                };
            };
            achievements: {
                Row: {
                    id: string;
                    name: string;
                    description: string;
                    category: string;
                    icon: string;
                    color: string | null;
                    rarity: string | null;
                    points_required: number | null;
                    games_required: number | null;
                    streak_required: number | null;
                    accuracy_required: number | null;
                    specific_mode: string | null;
                    specific_genre: string | null;
                    specific_decade: string | null;
                    difficulty_level: number | null;
                    is_active: boolean | null;
                    is_hidden: boolean | null;
                    reward_points: number | null;
                    custom_criteria: any | null;
                    created_at: string;
                    created_by: string | null;
                };
            };
            user_achievements: {
                Row: {
                    user_id: string;
                    achievement_id: string;
                    earned_at: string | null;
                    earned_in_session: string | null;
                    progress_data: any | null;
                };
            };
            artists: {
                Row: {
                    id: string;
                    name: string;
                    image_url: string | null;
                    banner_url: string | null;
                    biography: string | null;
                    formed_year: number | null;
                    disbanded_year: number | null;
                    origin_country: string | null;
                    origin_city: string | null;
                    primary_genre: string | null;
                    genres: string[] | null;
                    decades_active: string[] | null;
                    trivia_facts: string[] | null;
                    notable_achievements: string[] | null;
                    spotify_id: string | null;
                    musicbrainz_id: string | null;
                    discogs_id: number | null;
                    last_fm_id: string | null;
                    website_url: string | null;
                    wikipedia_url: string | null;
                    created_at: string;
                    updated_at: string;
                };
            };
            albums: {
                Row: {
                    id: string;
                    title: string;
                    artist_name: string;
                    release_date: string | null;
                    release_type: string | null;
                    cover_art_url: string | null;
                    cover_art_colors: any | null;
                    total_tracks: number | null;
                    total_duration: number | null;
                    primary_genre: string | null;
                    genres: string[] | null;
                    record_label: string | null;
                    producer: string | null;
                    chart_positions: any | null;
                    certifications: any | null;
                    trivia_facts: string[] | null;
                    recording_info: any | null;
                    spotify_id: string | null;
                    musicbrainz_id: string | null;
                    discogs_id: number | null;
                    created_at: string;
                    updated_at: string;
                };
            };
        };
        Functions: {
            update_track_stats: {
                Args: {
                    track_id: string;
                    was_correct: boolean;
                };
                Returns: void;
            };
            update_user_stats: {
                Args: {
                    p_user_id: string;
                    p_session_id: string;
                };
                Returns: void;
            };
        };
    };
}
export interface UserPreferences {
    theme: 'light' | 'dark' | 'system';
    quiz_difficulty: number;
    audio_volume: number;
    auto_play: boolean;
    show_hints: boolean;
    favorite_genres: string[];
    favorite_decades: string[];
}
export interface InterestingFacts {
    recording_location?: string | null;
    producer?: string | null;
    featured_instruments?: string[];
    chart_achievements?: Record<string, any>;
    awards?: string[];
    cover_versions?: string[];
    sampling_info?: Record<string, any>;
    cultural_impact?: string | null;
}
export type TypedSupabaseClient = ReturnType<typeof createClient<Database>>;
export type User = Database['public']['Tables']['users']['Row'];
export type QuizTrack = Database['public']['Tables']['quiz_tracks']['Row'];
export type GameSession = Database['public']['Tables']['game_sessions']['Row'];
export type GameAnswer = Database['public']['Tables']['game_answers']['Row'];
export type UserStats = Database['public']['Tables']['user_stats']['Row'];
export type Achievement = Database['public']['Tables']['achievements']['Row'];
export type UserAchievement = Database['public']['Tables']['user_achievements']['Row'];
export type Artist = Database['public']['Tables']['artists']['Row'];
export type Album = Database['public']['Tables']['albums']['Row'];
export type UserInsert = Database['public']['Tables']['users']['Insert'];
export type QuizTrackInsert = Database['public']['Tables']['quiz_tracks']['Insert'];
export type GameSessionInsert = Database['public']['Tables']['game_sessions']['Insert'];
export type GameAnswerInsert = Database['public']['Tables']['game_answers']['Insert'];
export type UserUpdate = Database['public']['Tables']['users']['Update'];
export type QuizTrackUpdate = Database['public']['Tables']['quiz_tracks']['Update'];
export type GameSessionUpdate = Database['public']['Tables']['game_sessions']['Update'];
export type GameAnswerUpdate = Database['public']['Tables']['game_answers']['Update'];
export declare const isSupabaseConfigured: () => boolean;
export declare class DatabaseError extends Error {
    readonly operation: string;
    readonly originalError?: any | undefined;
    constructor(message: string, operation: string, originalError?: any | undefined);
}
export declare const testDatabaseConnection: () => Promise<boolean>;
