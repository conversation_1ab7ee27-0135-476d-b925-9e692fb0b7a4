"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dbRecovery = exports.DatabaseConnectionRecovery = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
const logger_1 = require("@/lib/logger");
class DatabaseConnectionRecovery {
    constructor() {
        this.health = {
            isConnected: false,
            lastCheck: new Date(),
            consecutiveFailures: 0,
            totalFailures: 0,
            uptime: 0
        };
        this.healthCheckInterval = null;
        this.maxRetries = 3;
        this.baseDelay = 1000;
        this.maxDelay = 30000;
        this.healthCheckIntervalMs = 30000;
        if (!this.isBuildTime()) {
            this.startHealthCheck();
        }
    }
    isBuildTime() {
        return process.env.NODE_ENV === 'development' && process.argv.includes('build') ||
            process.env.NEXT_PHASE === 'phase-production-build' ||
            process.env.CI === 'true' ||
            process.env.BUILD_MODE === 'true';
    }
    static getInstance() {
        if (!DatabaseConnectionRecovery.instance) {
            DatabaseConnectionRecovery.instance = new DatabaseConnectionRecovery();
        }
        return DatabaseConnectionRecovery.instance;
    }
    async executeWithRecovery(operation, operationName = 'database-operation') {
        let lastError = null;
        for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
            try {
                const isHealthy = await this.quickHealthCheck();
                if (!isHealthy && attempt === 1) {
                    logger_1.databaseLogger.warn(`Database unhealthy before ${operationName}, attempting recovery`);
                    await this.attemptRecovery();
                }
                const startTime = Date.now();
                const result = await operation();
                const responseTime = Date.now() - startTime;
                this.updateHealthOnSuccess(responseTime);
                if (attempt > 1) {
                    logger_1.databaseLogger.info(`${operationName} succeeded on attempt ${attempt}`);
                }
                return result;
            }
            catch (error) {
                lastError = error;
                this.updateHealthOnFailure();
                logger_1.databaseLogger.warn(`${operationName} failed on attempt ${attempt}/${this.maxRetries}`, error);
                if (this.isFatalError(error)) {
                    logger_1.databaseLogger.error(`Fatal error in ${operationName}, not retrying`, error);
                    break;
                }
                if (attempt < this.maxRetries) {
                    const delay = Math.min(this.baseDelay * Math.pow(2, attempt - 1), this.maxDelay);
                    logger_1.databaseLogger.debug(`Waiting ${delay}ms before retry ${attempt + 1}`);
                    await this.sleep(delay);
                    await this.attemptRecovery();
                }
            }
        }
        logger_1.databaseLogger.error(`${operationName} failed after ${this.maxRetries} attempts`, lastError);
        throw lastError;
    }
    async checkHealth() {
        const startTime = Date.now();
        try {
            await prisma_1.default.$queryRaw `SELECT 1`;
            const responseTime = Date.now() - startTime;
            this.health.isConnected = true;
            this.health.lastCheck = new Date();
            this.health.responseTime = responseTime;
            this.health.consecutiveFailures = 0;
            this.health.uptime += this.healthCheckIntervalMs;
            return { ...this.health };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.health.isConnected = false;
            this.health.lastCheck = new Date();
            this.health.responseTime = responseTime;
            this.health.consecutiveFailures++;
            this.health.totalFailures++;
            this.health.uptime = 0;
            logger_1.databaseLogger.error('Database health check failed', error);
            return { ...this.health };
        }
    }
    async quickHealthCheck() {
        try {
            await prisma_1.default.$queryRaw `SELECT 1`;
            return true;
        }
        catch {
            return false;
        }
    }
    async attemptRecovery() {
        try {
            logger_1.databaseLogger.info('Attempting database connection recovery...');
            await prisma_1.default.$disconnect();
            await this.sleep(1000);
            await prisma_1.default.$connect();
            await prisma_1.default.$queryRaw `SELECT 1`;
            logger_1.databaseLogger.info('Database connection recovery successful');
            this.health.consecutiveFailures = 0;
        }
        catch (error) {
            logger_1.databaseLogger.error('Database connection recovery failed', error);
            throw error;
        }
    }
    updateHealthOnSuccess(responseTime) {
        this.health.isConnected = true;
        this.health.lastCheck = new Date();
        this.health.responseTime = responseTime;
        this.health.consecutiveFailures = 0;
    }
    updateHealthOnFailure() {
        this.health.isConnected = false;
        this.health.lastCheck = new Date();
        this.health.consecutiveFailures++;
        this.health.totalFailures++;
    }
    isFatalError(error) {
        const fatalPatterns = [
            'ENOTFOUND',
            'P1001',
            'P1017',
            'authentication failed',
            'permission denied'
        ];
        const errorMessage = error.message.toLowerCase();
        return fatalPatterns.some(pattern => errorMessage.includes(pattern.toLowerCase()));
    }
    startHealthCheck() {
        if (this.isBuildTime()) {
            return;
        }
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }
        this.healthCheckInterval = setInterval(async () => {
            await this.checkHealth();
            if (!this.health.isConnected) {
                logger_1.databaseLogger.warn('Database health check: UNHEALTHY', {
                    consecutiveFailures: this.health.consecutiveFailures,
                    totalFailures: this.health.totalFailures,
                    lastCheck: this.health.lastCheck
                });
            }
        }, this.healthCheckIntervalMs);
        logger_1.databaseLogger.info('Database health monitoring started');
    }
    stopHealthCheck() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
            logger_1.databaseLogger.info('Database health monitoring stopped');
        }
    }
    getHealth() {
        return { ...this.health };
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    async shutdown() {
        this.stopHealthCheck();
        try {
            await prisma_1.default.$disconnect();
            logger_1.databaseLogger.info('Database connection closed gracefully');
        }
        catch (error) {
            logger_1.databaseLogger.error('Error during database shutdown', error);
        }
    }
}
exports.DatabaseConnectionRecovery = DatabaseConnectionRecovery;
DatabaseConnectionRecovery.instance = null;
exports.dbRecovery = DatabaseConnectionRecovery.getInstance();
if (typeof process !== 'undefined' && process.env.NEXT_PHASE !== 'phase-production-build') {
    process.on('SIGINT', async () => {
        await exports.dbRecovery.shutdown();
        process.exit(0);
    });
    process.on('SIGTERM', async () => {
        await exports.dbRecovery.shutdown();
        process.exit(0);
    });
}
//# sourceMappingURL=connection-recovery.js.map