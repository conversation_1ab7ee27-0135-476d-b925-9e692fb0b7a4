{"version": 3, "file": "quiz-data-optimized.js", "sourceRoot": "", "sources": ["../../../lib/database/quiz-data-optimized.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAA6B;AAC7B,2CAAuC;AAUvC,MAAa,iBAAiB;IAA9B;QACU,kBAAa,GAAyB,IAAI,CAAA;QAC1C,oBAAe,GAAG,CAAC,CAAA;QACV,2BAAsB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAA;IAoSzD,CAAC;IA/RC,KAAK,CAAC,WAAW;QACf,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAGtB,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACrF,OAAO,IAAI,CAAC,aAAa,CAAA;QAC3B,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAA;QAGrD,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACjE,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,gBAAgB,EAAE;SACxB,CAAC,CAAA;QAEF,IAAI,CAAC,aAAa,GAAG;YACnB,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,KAAK;SACN,CAAA;QACD,IAAI,CAAC,eAAe,GAAG,GAAG,CAAA;QAE1B,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE;YACjD,OAAO,EAAE,OAAO,CAAC,MAAM;YACvB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,KAAK,EAAE,KAAK,CAAC,MAAM;SACpB,CAAC,CAAA;QAEF,OAAO,IAAI,CAAC,aAAa,CAAA;IAC3B,CAAC;IAKO,KAAK,CAAC,kBAAkB;QAC9B,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAsB;;;;;;;;KAQ1D,CAAA;QACD,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;IAClC,CAAC;IAKO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAqB;;;;;;;;KAQzD,CAAA;QACD,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IACjC,CAAC;IAKO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAqB;;;;;;;;KAQzD,CAAA;QACD,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IACjC,CAAC;IAKO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAqB;;;;;;;;KAQzD,CAAA;QACD,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IACjC,CAAC;IAKO,KAAK,CAAC,gBAAgB;QAC5B,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAoB;;;;;;;;KAQxD,CAAA;QACD,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,OAAgB;QACpD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QACrC,MAAM,QAAQ,GAAG,OAAO;YACtB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC;YACzC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAA;QAEhB,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACpD,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAa,EAAE,OAAgB;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QACrC,MAAM,QAAQ,GAAG,OAAO;YACtB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC;YACxC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAA;QAEf,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACpD,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAa,EAAE,OAAgB;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QACrC,MAAM,QAAQ,GAAG,OAAO;YACtB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC;YACxC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAA;QAEf,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACpD,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAa,EAAE,OAAgB;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QACrC,MAAM,QAAQ,GAAG,OAAO;YACtB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC;YACxC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAA;QAEf,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACpD,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,KAAa,EAAE,OAAgB;QAClD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QACrC,MAAM,QAAQ,GAAG,OAAO;YACtB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC;YACvC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;QAEd,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACpD,CAAC;IAKD,KAAK,CAAC,aAAa,CACjB,KAAa,EACb,OAKC;QAED,MAAM,WAAW,GAA+B;YAC9C,GAAG,EAAE;gBACH,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;gBACzB,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;gBACxB,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;aAC/B;SACF,CAAA;QAED,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC;YACnB,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QACnC,CAAC;QAED,IAAI,OAAO,EAAE,QAAQ,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;YACzC,WAAW,CAAC,IAAI,GAAG;gBACjB,GAAG,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI;gBAC7B,GAAG,EAAE,OAAO,CAAC,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aAChD,CAAA;QACH,CAAC;QAED,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC;YACtB,WAAW,CAAC,cAAc,GAAG;gBAC3B,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAA;QACH,CAAC;QAGD,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAA;;;;;UAK/B,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,eAAM,CAAC,GAAG,CAAA,eAAe,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,eAAM,CAAC,GAAG,CAAA,EAAE;UACxE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,eAAM,CAAC,GAAG,CAAA,eAAe,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,eAAM,CAAC,GAAG,CAAA,EAAE;UAC9E,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,eAAM,CAAC,GAAG,CAAA,eAAe,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,eAAM,CAAC,GAAG,CAAA,EAAE;UAC1E,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,eAAM,CAAC,GAAG,CAAA,kCAAkC,IAAI,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,eAAM,CAAC,GAAG,CAAA,EAAE;;cAEpG,KAAK;KACd,CAAA;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,MAAa,EACb,QAAgB,EAChB,aAA6B;QAG7B,MAAM,IAAI,GAAG,aAAa,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QAGtD,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACnC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO;iBAC9B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,MAAM,CAAC;iBAC/B,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;iBAC/B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YAEd,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM;iBAC5B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC;iBAC9B,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;iBAC/B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YAEd,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM;iBAC5B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC;iBAC9B,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;iBAC/B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YAEd,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK;iBAC1B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC;iBAC7B,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;iBAC/B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YAId,OAAO;gBACL,KAAK;gBACL,YAAY;gBACZ,WAAW;gBACX,WAAW;gBACX,UAAU;aACX,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,SAAS,CAAA;IAClB,CAAC;IAKD,UAAU;QACR,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAA;IAC1B,CAAC;IAKO,YAAY,CAAI,KAAU;QAChC,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;QAC3B,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;QACzD,CAAC;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;CACF;AAvSD,8CAuSC"}