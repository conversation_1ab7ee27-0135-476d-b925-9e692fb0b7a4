"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuizDataManager = void 0;
const prisma_1 = __importDefault(require("./prisma"));
function simpleShuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}
class QuizDataManager {
    constructor(librarySync) {
        this.librarySync = librarySync;
    }
    async generateQuizQuestions(gameMode, settings) {
        try {
            const perfStart = Date.now();
            const questionCount = settings.totalQuestions || settings.questionCount || 10;
            console.log(`[Quiz-Data] Generating ${questionCount} questions for ${gameMode} mode`);
            let tracks = [];
            try {
                const where = {};
                if (settings.genre && settings.genre !== 'all') {
                    where.genre = settings.genre;
                }
                if (settings.decade) {
                    const startYear = parseInt(settings.decade);
                    const endYear = startYear + 9;
                    where.year = {
                        gte: startYear,
                        lte: endYear
                    };
                }
                const category = settings.category;
                if (category && category !== 'all') {
                    where.quizCategories = { contains: `"${category}"` };
                }
                if (gameMode === 'guess-the-year' || gameMode === 'decade-challenge') {
                    where.year = {
                        not: null,
                        gte: 1900,
                        lte: new Date().getFullYear() + 1
                    };
                }
                if (gameMode === 'album-art') {
                    where.OR = [
                        { albumArtUrl: { not: null } },
                        { localAlbumArtThumbnail: { not: null } },
                        { localAlbumArtCover: { not: null } },
                        { localAlbumArtOriginal: { not: null } }
                    ];
                }
                const dbTracks = await prisma_1.default.quizTrack.findMany({
                    where,
                    take: Math.min(questionCount * 50, 2000),
                    orderBy: [
                        { updatedAt: 'desc' },
                        { createdAt: 'desc' }
                    ]
                });
                if (dbTracks.length > 0) {
                    tracks = dbTracks.map((track) => ({
                        file: track.mpdFilePath,
                        title: track.title,
                        artist: track.artist,
                        album: track.album,
                        date: track.year?.toString() || '',
                        genre: track.genre,
                        time: track.duration,
                        quizTrackId: track.id,
                        albumArtUrl: track.albumArtUrl,
                        localAlbumArtCover: track.localAlbumArtCover,
                        localAlbumArtThumbnail: track.localAlbumArtThumbnail,
                        localAlbumArtOriginal: track.localAlbumArtOriginal,
                        difficultyRating: track.difficultyRating,
                        popularityScore: track.popularityScore,
                        chartPosition: track.chartPosition,
                        chartCountry: track.chartCountry,
                        similarArtists: track.similarArtists ? JSON.parse(track.similarArtists) : []
                    }));
                    const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
                    tracks = RandomUtils.shuffle(tracks);
                    console.log(`[Quiz-Data] Got ${tracks.length} tracks from database`);
                }
            }
            catch (error) {
                console.warn('[Quiz-Data] Database query failed:', error);
            }
            if (tracks.length === 0) {
                console.log('[Quiz-Data] No tracks from database, trying MPD library sync');
                try {
                    tracks = await this.librarySync.getRandomTracks(questionCount * 20, {
                        genre: settings.genre,
                        decade: settings.decade,
                        difficulty: settings.difficultyLevel,
                        minPopularity: this.getMinPopularityForMode(gameMode),
                        minLastPlayedMinutes: 180,
                        category: settings.category,
                        thematicTag: settings.thematicTag,
                        specialList: settings.specialList
                    });
                }
                catch (error) {
                    console.warn('[Quiz-Data] MPD library sync also failed:', error);
                }
            }
            if (tracks.length === 0) {
                console.warn('[Quiz-Data] No tracks found from database, falling back to mock questions');
                return this.generateMockQuestions(gameMode, questionCount);
            }
            console.log(`[Quiz-Data] Filtering ${tracks.length} tracks for ${gameMode} mode`);
            const usableTracks = tracks.filter((t, index) => {
                const track = t;
                const hasFile = !!(track.file);
                const hasArtist = track.artist && track.artist !== 'Unknown Artist' && track.artist !== '';
                const hasTitle = track.title && track.title !== 'Unknown Title' && track.title !== '';
                const hasBasicData = hasFile && hasArtist && hasTitle;
                if (index < 5) {
                    console.log(`[Quiz-Data] Track ${index}: file="${track.file}", artist="${track.artist}", title="${track.title}", album="${track.album}"`);
                    console.log(`[Quiz-Data] Track ${index}: hasFile=${hasFile}, hasArtist=${hasArtist}, hasTitle=${hasTitle}, hasBasicData=${hasBasicData}`);
                }
                if (gameMode === 'album-art') {
                    const hasAlbumArt = track.albumArtUrl ||
                        track.localAlbumArtThumbnail ||
                        track.localAlbumArtCover ||
                        track.localAlbumArtOriginal;
                    if (index < 3) {
                        console.log(`[Quiz-Data] Track ${index}: albumArtUrl=${!!track.albumArtUrl}, hasAlbumArt=${hasAlbumArt}`);
                    }
                    return hasBasicData && hasAlbumArt;
                }
                if (gameMode === 'audio-manipulation') {
                    const hasAlbum = track.album && track.album !== 'Unknown Album';
                    return hasBasicData && hasAlbum;
                }
                if (gameMode === 'hitster-timeline') {
                    const hasYear = !!(track.year || (track.date && parseInt(track.date.split('-')[0])));
                    if (index < 5) {
                        console.log(`[Quiz-Data] Track ${index} year check: year=${track.year}, date=${track.date}, hasYear=${hasYear}`);
                    }
                    return hasBasicData && hasYear;
                }
                if (gameMode === 'guess-the-year' || gameMode === 'decade-challenge') {
                    const yearStr = track.date ? track.date.toString() : '';
                    const year = parseInt(yearStr);
                    const hasValidYear = !isNaN(year) && year >= 1900 && year <= new Date().getFullYear() + 1;
                    if (index < 5) {
                        console.log(`[Quiz-Data] Track ${index} year validation: year=${year}, valid=${hasValidYear}`);
                    }
                    return hasBasicData && hasValidYear;
                }
                return hasBasicData;
            });
            if (usableTracks.length === 0) {
                if (gameMode === 'album-art') {
                    console.warn('[Quiz-Data] No tracks with album art found for album art quiz, falling back to mock questions');
                    console.warn('[Quiz-Data] Consider running sync with --update-cover-art to fetch missing album artwork');
                    console.warn(`[Quiz-Data] Album art validation: out of ${tracks.length} tracks, 0 passed album art filter`);
                }
                else if (gameMode === 'audio-manipulation') {
                    console.warn('[Quiz-Data] No tracks with proper album metadata found for audio tricks quiz, falling back to mock questions');
                    console.warn('[Quiz-Data] Audio tricks requires tracks with valid album names to generate album questions');
                }
                else if (gameMode === 'hitster-timeline') {
                    console.warn('[Quiz-Data] No tracks with year information found for Hitster timeline quiz');
                    console.warn('[Quiz-Data] Hitster timeline requires tracks with year metadata');
                    console.warn(`[Quiz-Data] Year validation: out of ${tracks.length} tracks, 0 had year information`);
                }
                else if (gameMode === 'guess-the-year' || gameMode === 'decade-challenge') {
                    console.warn('[Quiz-Data] No tracks with valid year data found for year-based quiz');
                    console.warn('[Quiz-Data] Year-based modes require tracks with valid year between 1900 and current year');
                    console.warn(`[Quiz-Data] Year validation: out of ${tracks.length} tracks, 0 passed year validation`);
                }
                else {
                    console.warn('[Quiz-Data] No tracks with proper metadata found, falling back to mock questions');
                    console.warn(`[Quiz-Data] Basic validation: out of ${tracks.length} tracks, 0 passed basic validation`);
                }
                return this.generateMockQuestions(gameMode, questionCount);
            }
            if (gameMode === 'album-art') {
                const totalOriginalTracks = tracks.length;
                const tracksWithArt = usableTracks.length;
                console.log(`[Quiz-Data] Album Art Quiz: ${tracksWithArt}/${totalOriginalTracks} tracks have album artwork (${((tracksWithArt / totalOriginalTracks) * 100).toFixed(1)}%)`);
            }
            let finalQuestions = [];
            if (usableTracks.length < questionCount) {
                console.warn(`[Quiz-Data] Only ${usableTracks.length} tracks with file paths found (need ${questionCount}). Using mix of real tracks and mock questions.`);
                const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
                const selectedTracks = RandomUtils.shuffle(usableTracks);
                for (let i = 0; i < selectedTracks.length; i++) {
                    const track = selectedTracks[i];
                    try {
                        const question = await this.generateQuestionForTrack(track, gameMode, settings);
                        finalQuestions.push(question);
                    }
                    catch (error) {
                        console.warn(`[Quiz-Data] Failed to generate question for track "${track.title}":`, error.message);
                    }
                }
                const remainingCount = questionCount - selectedTracks.length;
                const mockQuestions = this.generateMockQuestions(gameMode, remainingCount);
                finalQuestions.push(...mockQuestions);
                finalQuestions = RandomUtils.shuffle(finalQuestions);
            }
            else {
                const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
                const selectedTracks = RandomUtils.sample(usableTracks, questionCount);
                for (let i = 0; i < selectedTracks.length; i++) {
                    const track = selectedTracks[i];
                    try {
                        const question = await this.generateQuestionForTrack(track, gameMode, settings);
                        finalQuestions.push(question);
                    }
                    catch (error) {
                        console.warn(`[Quiz-Data] Failed to generate question for track "${track.title}":`, error.message);
                    }
                }
                if (finalQuestions.length < questionCount) {
                    const additionalNeeded = questionCount - finalQuestions.length;
                    console.warn(`[Quiz-Data] Only generated ${finalQuestions.length} questions, need ${additionalNeeded} more`);
                    const mockQuestions = this.generateMockQuestions(gameMode, additionalNeeded);
                    finalQuestions.push(...mockQuestions);
                }
            }
            const duration = Date.now() - perfStart;
            console.log(`[Quiz-Data] Generated ${finalQuestions.length} questions successfully in ${duration} ms`);
            return finalQuestions;
        }
        catch (error) {
            console.error('[Quiz-Data] Question generation failed:', error);
            console.warn('[Quiz-Data] Falling back to mock questions due to error');
            return this.generateMockQuestions(gameMode, questionCount);
        }
    }
    async generateQuestionForTrack(track, gameMode, settings) {
        const questionId = `${track.quizTrackId || track.file}_${Date.now()}_${Math.random()}`;
        if (gameMode === 'custom' && settings.questionTypes) {
            const enabledTypes = Object.entries(settings.questionTypes)
                .filter(([_, enabled]) => enabled)
                .map(([type, _]) => type);
            if (enabledTypes.length === 0) {
                return this.generateArtistQuestion(questionId, track, settings);
            }
            const randomType = enabledTypes[Math.floor(Math.random() * enabledTypes.length)];
            switch (randomType) {
                case 'artist':
                    return this.generateArtistQuestion(questionId, track, settings);
                case 'title':
                    return this.generateTitleQuestion(questionId, track, settings);
                case 'album':
                    return this.generateAlbumQuestion(questionId, track, settings);
                case 'year':
                    return this.generateYearQuestion(questionId, track, settings);
                case 'genre':
                    return this.generateGenreQuestion(questionId, track, settings);
                case 'chartPosition':
                    return this.generateChartPositionQuestion(questionId, track, settings);
                default:
                    return this.generateArtistQuestion(questionId, track, settings);
            }
        }
        switch (gameMode) {
            case 'classic':
                return this.generateArtistQuestion(questionId, track, settings);
            case 'chart-position':
                return this.generateChartPositionQuestion(questionId, track, settings);
            case 'decade-challenge':
                return this.generateDecadeQuestion(questionId, track, settings);
            case 'genre-specialist':
                return this.generateGenreQuestion(questionId, track, settings);
            case 'guess-the-year':
                return this.generateYearQuestion(questionId, track, settings);
            case 'quick-fire':
                return this.generateQuickFireQuestion(questionId, track, settings);
            case 'audio-manipulation':
                return this.generateAudioTricksQuestion(questionId, track, settings);
            case 'album-art':
                return this.generateAlbumArtQuestion(questionId, track, settings);
            case 'audio-fingerprint':
                return this.generateAudioFingerprintQuestion(questionId, track, settings);
            case 'hitster-timeline':
                return this.generateHitsterQuestion(questionId, track, settings);
            case 'ultimote':
                return this.generateArtistQuestion(questionId, track, settings);
            default:
                return this.generateArtistQuestion(questionId, track, settings);
        }
    }
    async generateArtistQuestion(questionId, track, settings) {
        const wrongOptions = [];
        if (track.similarArtists && Array.isArray(track.similarArtists)) {
            for (const artist of track.similarArtists) {
                if (wrongOptions.length >= 3)
                    break;
                if (artist && artist !== (track.artist || '') && !wrongOptions.includes(artist)) {
                    wrongOptions.push(artist);
                }
            }
        }
        if (wrongOptions.length < 3) {
            const additionalArtists = await this.getRandomArtists(3 - wrongOptions.length, [(track.artist || ''), ...wrongOptions]);
            wrongOptions.push(...additionalArtists);
        }
        const finalWrongOptions = wrongOptions.slice(0, 3);
        const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
        const artistName = track.artist || 'Unknown Artist';
        const options = RandomUtils.shuffle([artistName, ...finalWrongOptions]);
        return {
            id: questionId,
            type: 'artist',
            question: 'Who is the artist of this song?',
            correctAnswer: artistName,
            options,
            track,
            difficulty: track.difficultyRating || 3,
            trackPreviewStart: this.getRandomPreviewStart(track.time || 0)
        };
    }
    async generateChartPositionQuestion(questionId, track, settings) {
        const chartPosition = track.chartPosition || Math.floor(Math.random() * 100) + 1;
        const yearStr = track.date ? ` in ${track.date}` : '';
        return {
            id: questionId,
            type: 'chart-position',
            question: `What was the highest chart position of "${track.title || 'this song'}" by ${track.artist || 'the artist'}${yearStr}?`,
            correctAnswer: chartPosition.toString(),
            options: [],
            track,
            difficulty: track.difficultyRating || 3,
            trackPreviewStart: this.getRandomPreviewStart(track.time || 0)
        };
    }
    async generateDecadeQuestion(questionId, track, settings) {
        let year = null;
        if (track.date) {
            const parsedYear = parseInt(track.date.toString());
            if (!isNaN(parsedYear) && parsedYear >= 1900 && parsedYear <= new Date().getFullYear() + 1) {
                year = parsedYear;
            }
        }
        if (!year) {
            throw new Error(`Track "${track.title}" has no valid year data for year-based question`);
        }
        return {
            id: questionId,
            type: 'year',
            question: `What year was "${track.title || 'this song'}" by ${track.artist || 'the artist'} released?`,
            correctAnswer: year.toString(),
            options: [],
            track,
            difficulty: track.difficultyRating || 3,
            trackPreviewStart: this.getRandomPreviewStart(track.time || 0),
            metadata: {
                actualYear: year
            }
        };
    }
    async generateGenreQuestion(questionId, track, settings) {
        const wrongOptions = await this.getRandomGenres(3, [track.genre || '']);
        const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
        const options = RandomUtils.shuffle([(track.genre || 'Unknown'), ...wrongOptions]);
        return {
            id: questionId,
            type: 'genre',
            question: `What genre is "${track.title || 'this song'}" by ${track.artist || 'the artist'}?`,
            correctAnswer: track.genre || 'Unknown',
            options,
            track,
            difficulty: track.difficultyRating || 3,
            trackPreviewStart: this.getRandomPreviewStart(track.time || 0)
        };
    }
    async generateTitleQuestion(questionId, track, settings) {
        const wrongOptions = await this.getRandomTitles(3, [track.title || '']);
        const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
        const titleName = track.title || 'Unknown Title';
        const artistName = track.artist || 'Unknown Artist';
        const options = RandomUtils.shuffle([titleName, ...wrongOptions]);
        return {
            id: questionId,
            type: 'title',
            question: `What is the title of this song by ${artistName}?`,
            correctAnswer: titleName,
            options,
            track,
            difficulty: track.difficultyRating || 3,
            trackPreviewStart: this.getRandomPreviewStart(track.time || 0)
        };
    }
    async generateAlbumQuestion(questionId, track, settings) {
        if (!track.album || track.album === 'Unknown Album') {
            console.warn(`[Quiz-Data] Track "${track.title}" has no album metadata, generating artist question instead`);
            return this.generateArtistQuestion(questionId, track, settings);
        }
        const wrongOptions = await this.getRandomAlbums(3, [track.album]);
        const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
        const albumName = track.album;
        const titleName = track.title || 'Unknown Title';
        const artistName = track.artist || 'Unknown Artist';
        const options = RandomUtils.shuffle([albumName, ...wrongOptions]);
        return {
            id: questionId,
            type: 'album',
            question: `Which album is "${titleName}" by ${artistName} from?`,
            correctAnswer: albumName,
            options,
            track,
            difficulty: track.difficultyRating || 3,
            trackPreviewStart: this.getRandomPreviewStart(track.time || 0)
        };
    }
    async getRandomArtists(count, exclude = []) {
        const artists = await this.librarySync.getAvailableArtists(100);
        const fallbackArtists = [
            'Adele', 'The Beatles', 'Queen', 'Michael Jackson', 'Madonna', 'Elvis Presley',
            'Bob Dylan', 'David Bowie', 'Pink Floyd', 'Led Zeppelin', 'The Rolling Stones',
            'U2', 'Coldplay', 'Ed Sheeran', 'Taylor Swift', 'Beyoncé', 'Eminem', 'Drake',
            'Rihanna', 'Lady Gaga', 'Kanye West', 'Bruno Mars', 'John Lennon', 'Paul McCartney',
            'Prince', 'Whitney Houston', 'Mariah Carey', 'Celine Dion', 'Elton John', 'Sting'
        ];
        const availableArtists = artists.length > 0 ? artists : fallbackArtists;
        const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
        const filtered = availableArtists.filter(artist => !exclude.includes(artist));
        const shuffled = RandomUtils.shuffle(filtered);
        const filteredArtists = shuffled.slice(0, count);
        return filteredArtists;
    }
    async generateQuickFireQuestion(questionId, track, settings) {
        const quickFireTypes = ['artist', 'title', 'album', 'genre'];
        const randomType = quickFireTypes[Math.floor(Math.random() * quickFireTypes.length)];
        switch (randomType) {
            case 'artist':
                return this.generateArtistQuestion(questionId, track, settings);
            case 'title':
                return this.generateTitleQuestion(questionId, track, settings);
            case 'album':
                return this.generateAlbumQuestion(questionId, track, settings);
            case 'genre':
                return this.generateGenreQuestion(questionId, track, settings);
            default:
                return this.generateArtistQuestion(questionId, track, settings);
        }
    }
    async generateAudioTricksQuestion(questionId, track, settings) {
        const audioTricksTypes = ['artist', 'title', 'album'];
        const randomType = audioTricksTypes[Math.floor(Math.random() * audioTricksTypes.length)];
        let question;
        switch (randomType) {
            case 'artist':
                question = await this.generateArtistQuestion(questionId, track, settings);
                break;
            case 'title':
                question = await this.generateTitleQuestion(questionId, track, settings);
                break;
            case 'album':
                question = await this.generateAlbumQuestion(questionId, track, settings);
                break;
            default:
                question = await this.generateArtistQuestion(questionId, track, settings);
        }
        const audioEffects = ['rewind', 'fast', 'slow', null];
        const randomEffect = audioEffects[Math.floor(Math.random() * audioEffects.length)];
        return {
            ...question,
            audioEffect: randomEffect
        };
    }
    async generateAlbumArtQuestion(questionId, track, settings) {
        const albumArtTypes = ['artist', 'album', 'year'];
        const randomType = albumArtTypes[Math.floor(Math.random() * albumArtTypes.length)];
        let baseQuestion;
        switch (randomType) {
            case 'artist':
                baseQuestion = await this.generateArtistQuestion(questionId, track, settings);
                baseQuestion.question = `Who is the artist of this album?`;
                break;
            case 'album':
                baseQuestion = await this.generateAlbumQuestion(questionId, track, settings);
                baseQuestion.question = `What is the name of this album?`;
                break;
            case 'year':
                baseQuestion = await this.generateYearQuestion(questionId, track, settings);
                baseQuestion.question = `When was this album released?`;
                break;
            default:
                baseQuestion = await this.generateArtistQuestion(questionId, track, settings);
                baseQuestion.question = `Who is the artist of this album?`;
                break;
        }
        return {
            ...baseQuestion,
            metadata: {
                ...baseQuestion.metadata,
                isAlbumArtQuiz: true,
                albumArtUrl: track.albumArtUrl || track.localAlbumArtCover || null,
                showAlbumArt: true,
                hideAudioPlayer: true
            }
        };
    }
    async generateAudioFingerprintQuestion(questionId, track, settings) {
        const fingerprintTypes = ['artist', 'artist', 'artist', 'title'];
        const randomType = fingerprintTypes[Math.floor(Math.random() * fingerprintTypes.length)];
        switch (randomType) {
            case 'artist':
                return this.generateArtistQuestion(questionId, track, settings);
            case 'title':
                return this.generateTitleQuestion(questionId, track, settings);
            default:
                return this.generateArtistQuestion(questionId, track, settings);
        }
    }
    async generateHitsterQuestion(questionId, track, settings) {
        let year;
        if (track.year) {
            year = track.year;
        }
        else if (track.date) {
            const yearMatch = track.date.match(/^(\d{4})/);
            if (yearMatch) {
                year = parseInt(yearMatch[1]);
            }
        }
        if (!year) {
            console.warn(`[Quiz-Data] Track missing year for Hitster: ${track.title} by ${track.artist}`);
            return this.generateArtistQuestion(questionId, track, settings);
        }
        return {
            id: questionId,
            type: 'timeline-placement',
            question: `Place this song in the timeline: "${track.title}" by ${track.artist}`,
            correctAnswer: year.toString(),
            options: [],
            track: {
                ...track,
                year
            },
            difficulty: track.difficultyRating || 3,
            trackPreviewStart: this.getRandomPreviewStart(track.time || 0),
            metadata: {
                year,
                decade: Math.floor(year / 10) * 10,
                isHitsterQuestion: true,
                albumArt: track.albumArtUrl || track.localAlbumArtCover || null
            }
        };
    }
    async getRandomGenres(count, exclude = []) {
        const genres = await this.librarySync.getAvailableGenres();
        const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
        const filtered = genres.filter(genre => !exclude.includes(genre));
        const shuffled = RandomUtils.shuffle(filtered);
        return shuffled.slice(0, count);
    }
    async getRandomTitles(count, exclude = []) {
        try {
            const titles = await this.librarySync.getAvailableTitles?.(100) || [];
            if (titles.length > 0) {
                const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
                const filtered = titles.filter(title => !exclude.includes(title));
                const shuffled = RandomUtils.shuffle(filtered);
                return shuffled.slice(0, count);
            }
        }
        catch (error) {
            console.warn('[Quiz-Data] Could not get titles from database, using fallback');
        }
        const fallbackTitles = [
            'Yesterday', 'Bohemian Rhapsody', 'Stairway to Heaven', 'Imagine', 'Hotel California',
            'Sweet Child O\' Mine', 'Billie Jean', 'Like a Rolling Stone', 'Smells Like Teen Spirit',
            'Purple Haze', 'What\'s Going On', 'Respect', 'Good Vibrations', 'Johnny B. Goode',
            'I Want to Hold Your Hand', 'Satisfaction', 'My Generation', 'Born to Run'
        ];
        const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
        const filtered = fallbackTitles.filter(title => !exclude.includes(title));
        const shuffled = RandomUtils.shuffle(filtered);
        return shuffled.slice(0, count);
    }
    async getRandomAlbums(count, exclude = []) {
        try {
            const albums = await this.librarySync.getAvailableAlbums?.(100) || [];
            if (albums.length > 0) {
                const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
                const filtered = albums.filter(album => !exclude.includes(album));
                const shuffled = RandomUtils.shuffle(filtered);
                return shuffled.slice(0, count);
            }
        }
        catch (error) {
            console.warn('[Quiz-Data] Could not get albums from database, using fallback');
        }
        const fallbackAlbums = [
            'Abbey Road', 'A Night at the Opera', 'Led Zeppelin IV', 'The Dark Side of the Moon',
            'Thriller', 'Nevermind', 'Sgt. Pepper\'s Lonely Hearts Club Band', 'Pet Sounds',
            'Revolver', 'Highway 61 Revisited', 'Are You Experienced', 'What\'s Going On',
            'Born to Run', 'London Calling', 'OK Computer', 'The Velvet Underground & Nico'
        ];
        const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
        const filtered = fallbackAlbums.filter(album => !exclude.includes(album));
        const shuffled = RandomUtils.shuffle(filtered);
        return shuffled.slice(0, count);
    }
    generateChartPositionOptions(correctPosition) {
        const options = [];
        const usedPositions = new Set([correctPosition]);
        while (options.length < 3) {
            let position;
            if (correctPosition <= 10) {
                position = Math.floor(Math.random() * 20) + 1;
            }
            else if (correctPosition <= 50) {
                position = Math.floor(Math.random() * 80) + 1;
            }
            else {
                position = Math.floor(Math.random() * 100) + 1;
            }
            if (!usedPositions.has(position)) {
                options.push(position.toString());
                usedPositions.add(position);
            }
        }
        return options;
    }
    generateDecadeOptions(correctDecade) {
        const decades = ['1960s', '1970s', '1980s', '1990s', '2000s', '2010s', '2020s'];
        const filtered = decades.filter(decade => decade !== correctDecade);
        for (let i = filtered.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [filtered[i], filtered[j]] = [filtered[j], filtered[i]];
        }
        return filtered.slice(0, 3);
    }
    getRandomPreviewStart(duration) {
        if (duration <= 30)
            return 0;
        const maxStart = Math.max(0, duration - 30);
        return Math.floor(Math.random() * maxStart);
    }
    getMinPopularityForMode(gameMode) {
        switch (gameMode) {
            case 'chart-position':
                return 0;
            case 'classic':
                return 0;
            case 'decade-challenge':
                return 0;
            case 'genre-specialist':
                return 0;
            case 'ultimote':
                return 0;
            default:
                return 0;
        }
    }
    generateMockQuestions(gameMode, count) {
        console.log(`[Quiz-Data] Generating ${count} mock questions for ${gameMode} mode`);
        const mockTracks = [
            { id: '1', title: 'Bohemian Rhapsody', artist: 'Queen', album: 'A Night at the Opera', year: 1975, genre: 'Rock', file: '/test1.mp3', chartPosition: 1 },
            { id: '2', title: 'Billie Jean', artist: 'Michael Jackson', album: 'Thriller', year: 1982, genre: 'Pop', file: '/test2.mp3', chartPosition: 1 },
            { id: '3', title: 'Imagine', artist: 'John Lennon', album: 'Imagine', year: 1971, genre: 'Rock', file: '/test3.mp3', chartPosition: 3 },
            { id: '4', title: 'Like a Rolling Stone', artist: 'Bob Dylan', album: 'Highway 61 Revisited', year: 1965, genre: 'Rock', file: '/test4.mp3', chartPosition: 2 },
            { id: '5', title: 'What\'s Going On', artist: 'Marvin Gaye', album: 'What\'s Going On', year: 1971, genre: 'Soul', file: '/test5.mp3', chartPosition: 4 },
            { id: '6', title: 'Hotel California', artist: 'Eagles', album: 'Hotel California', year: 1976, genre: 'Rock', file: '/test6.mp3', chartPosition: 1 },
            { id: '7', title: 'Stairway to Heaven', artist: 'Led Zeppelin', album: 'Led Zeppelin IV', year: 1971, genre: 'Rock', file: '/test7.mp3', chartPosition: 37 },
            { id: '8', title: 'Yesterday', artist: 'The Beatles', album: 'Help!', year: 1965, genre: 'Pop', file: '/test8.mp3', chartPosition: 1 },
            { id: '9', title: 'Purple Haze', artist: 'Jimi Hendrix', album: 'Are You Experienced', year: 1967, genre: 'Rock', file: '/test9.mp3', chartPosition: 65 },
            { id: '10', title: 'Respect', artist: 'Aretha Franklin', album: 'I Never Loved a Man', year: 1967, genre: 'Soul', file: '/test10.mp3', chartPosition: 1 },
        ];
        const fallbackArtists = [
            'Adele', 'The Beatles', 'Queen', 'Michael Jackson', 'Madonna', 'Elvis Presley',
            'Bob Dylan', 'David Bowie', 'Pink Floyd', 'Led Zeppelin', 'The Rolling Stones',
            'U2', 'Coldplay', 'Ed Sheeran', 'Taylor Swift', 'Beyoncé', 'Eminem', 'Drake',
            'Rihanna', 'Lady Gaga', 'Kanye West', 'Bruno Mars', 'John Lennon', 'Paul McCartney',
            'Prince', 'Whitney Houston', 'Mariah Carey', 'Celine Dion', 'Elton John', 'Sting'
        ];
        return mockTracks.slice(0, count).map((track, index) => {
            const questionId = `mock_${index}_${Date.now()}`;
            if (gameMode === 'decade-challenge') {
                return {
                    id: questionId,
                    type: 'year',
                    question: `What year was "${track.title}" by ${track.artist} released?`,
                    correctAnswer: track.year.toString(),
                    options: [],
                    track: {
                        ...track,
                        artist: track.artist,
                        title: track.title,
                        album: track.album,
                        file: track.file
                    },
                    difficulty: 3,
                    trackPreviewStart: 0
                };
            }
            else if (gameMode === 'chart-position') {
                return {
                    id: questionId,
                    type: 'chart-position',
                    question: `What was the highest chart position of "${track.title}" by ${track.artist}?`,
                    correctAnswer: track.chartPosition.toString(),
                    options: [],
                    track: {
                        ...track,
                        artist: track.artist,
                        title: track.title,
                        album: track.album,
                        file: track.file
                    },
                    difficulty: 3,
                    trackPreviewStart: 0
                };
            }
            else if (gameMode === 'quick-fire') {
                const quickFireTypes = ['artist', 'title', 'album'];
                const randomType = quickFireTypes[index % quickFireTypes.length];
                const filtered = fallbackArtists.filter(artist => artist !== track.artist);
                for (let i = filtered.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [filtered[i], filtered[j]] = [filtered[j], filtered[i]];
                }
                const wrongOptions = filtered.slice(0, 3);
                if (randomType === 'title') {
                    const wrongTitles = mockTracks
                        .filter(t => t.title !== track.title)
                        .map(t => t.title);
                    wrongTitles = simpleShuffleArray(wrongTitles).slice(0, 3);
                    const options = simpleShuffleArray([track.title, ...wrongTitles]);
                    return {
                        id: questionId,
                        type: 'title',
                        question: `What is the title of this song by ${track.artist}?`,
                        correctAnswer: track.title,
                        options,
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 3,
                        trackPreviewStart: 0
                    };
                }
                else if (randomType === 'album') {
                    const wrongAlbums = mockTracks
                        .filter(t => t.album !== track.album)
                        .map(t => t.album);
                    wrongAlbums = simpleShuffleArray(wrongAlbums).slice(0, 3);
                    const options = simpleShuffleArray([track.album, ...wrongAlbums]);
                    return {
                        id: questionId,
                        type: 'album',
                        question: `Which album is "${track.title}" by ${track.artist} from?`,
                        correctAnswer: track.album,
                        options,
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 3,
                        trackPreviewStart: 0
                    };
                }
                else {
                    const options = simpleShuffleArray([track.artist, ...wrongOptions]);
                    return {
                        id: questionId,
                        type: 'artist',
                        question: `Who is the artist of this song?`,
                        correctAnswer: track.artist,
                        options,
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 3,
                        trackPreviewStart: 0
                    };
                }
            }
            else if (gameMode === 'audio-manipulation') {
                const audioTricksTypes = ['artist', 'title', 'album'];
                const randomType = audioTricksTypes[index % audioTricksTypes.length];
                if (randomType === 'title') {
                    const wrongTitles = mockTracks
                        .filter(t => t.title !== track.title)
                        .map(t => t.title);
                    wrongTitles = simpleShuffleArray(wrongTitles).slice(0, 3);
                    const options = simpleShuffleArray([track.title, ...wrongTitles]);
                    const audioEffects = ['rewind', 'fast', 'slow', null];
                    const randomEffect = audioEffects[index % audioEffects.length];
                    return {
                        id: questionId,
                        type: 'title',
                        question: `What is the title of this song? (Audio effects applied)`,
                        correctAnswer: track.title,
                        options,
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 4,
                        trackPreviewStart: 0,
                        audioEffect: randomEffect
                    };
                }
                else if (randomType === 'album') {
                    const wrongAlbums = mockTracks
                        .filter(t => t.album !== track.album)
                        .map(t => t.album);
                    wrongAlbums = simpleShuffleArray(wrongAlbums).slice(0, 3);
                    const options = simpleShuffleArray([track.album, ...wrongAlbums]);
                    const audioEffects = ['rewind', 'fast', 'slow', null];
                    const randomEffect = audioEffects[index % audioEffects.length];
                    return {
                        id: questionId,
                        type: 'album',
                        question: `Which album is this song from? (Audio effects applied)`,
                        correctAnswer: track.album,
                        options,
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 4,
                        trackPreviewStart: 0,
                        audioEffect: randomEffect
                    };
                }
                else {
                    const wrongOptions = fallbackArtists
                        .filter(artist => artist !== track.artist)
                        .slice(0, 3);
                    const options = simpleShuffleArray([track.artist, ...wrongOptions]);
                    const audioEffects = ['rewind', 'fast', 'slow', null];
                    const randomEffect = audioEffects[index % audioEffects.length];
                    return {
                        id: questionId,
                        type: 'artist',
                        question: `Who is the artist of this song? (Audio effects applied)`,
                        correctAnswer: track.artist,
                        options,
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 4,
                        trackPreviewStart: 0,
                        audioEffect: randomEffect
                    };
                }
            }
            else if (gameMode === 'album-art') {
                const albumArtTypes = ['artist', 'album', 'year'];
                const randomType = albumArtTypes[index % albumArtTypes.length];
                if (randomType === 'album') {
                    const wrongAlbums = mockTracks
                        .filter(t => t.album !== track.album)
                        .map(t => t.album);
                    wrongAlbums = simpleShuffleArray(wrongAlbums).slice(0, 3);
                    const options = simpleShuffleArray([track.album, ...wrongAlbums]);
                    return {
                        id: questionId,
                        type: 'album',
                        question: `Which album cover is shown?`,
                        correctAnswer: track.album,
                        options,
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 3,
                        trackPreviewStart: 0
                    };
                }
                else if (randomType === 'year') {
                    return {
                        id: questionId,
                        type: 'year',
                        question: `What year was this album released?`,
                        correctAnswer: track.year.toString(),
                        options: [],
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 3,
                        trackPreviewStart: 0
                    };
                }
                else {
                    const wrongOptions = fallbackArtists
                        .filter(artist => artist !== track.artist)
                        .slice(0, 3);
                    const options = simpleShuffleArray([track.artist, ...wrongOptions]);
                    return {
                        id: questionId,
                        type: 'artist',
                        question: `Who is the artist of this album?`,
                        correctAnswer: track.artist,
                        options,
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 3,
                        trackPreviewStart: 0
                    };
                }
            }
            else if (gameMode === 'audio-fingerprint') {
                const fingerprintTypes = ['artist', 'title'];
                const randomType = fingerprintTypes[index % fingerprintTypes.length];
                if (randomType === 'title') {
                    const wrongTitles = mockTracks
                        .filter(t => t.title !== track.title)
                        .map(t => t.title);
                    wrongTitles = simpleShuffleArray(wrongTitles).slice(0, 3);
                    const options = simpleShuffleArray([track.title, ...wrongTitles]);
                    return {
                        id: questionId,
                        type: 'title',
                        question: `Identify this song from the micro-clip`,
                        correctAnswer: track.title,
                        options,
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 5,
                        trackPreviewStart: 0
                    };
                }
                else {
                    const wrongOptions = fallbackArtists
                        .filter(artist => artist !== track.artist)
                        .slice(0, 3);
                    const options = simpleShuffleArray([track.artist, ...wrongOptions]);
                    return {
                        id: questionId,
                        type: 'artist',
                        question: `Who is the artist? (Expert audio fingerprint)`,
                        correctAnswer: track.artist,
                        options,
                        track: {
                            ...track,
                            artist: track.artist,
                            title: track.title,
                            album: track.album,
                            file: track.file
                        },
                        difficulty: 5,
                        trackPreviewStart: 0
                    };
                }
            }
            else if (gameMode === 'hitster-timeline') {
                return {
                    id: questionId,
                    type: 'timeline-placement',
                    question: `Place this song in the timeline: "${track.title}" by ${track.artist}`,
                    correctAnswer: track.year.toString(),
                    options: [],
                    track: {
                        ...track,
                        artist: track.artist,
                        title: track.title,
                        album: track.album,
                        file: track.file,
                        year: track.year
                    },
                    difficulty: 3,
                    trackPreviewStart: 0,
                    metadata: {
                        year: track.year,
                        decade: Math.floor(track.year / 10) * 10,
                        isHitsterQuestion: true,
                        albumArt: null
                    }
                };
            }
            else {
                const filtered = fallbackArtists.filter(artist => artist !== track.artist);
                for (let i = filtered.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [filtered[i], filtered[j]] = [filtered[j], filtered[i]];
                }
                const wrongOptions = filtered.slice(0, 3);
                const options = simpleShuffleArray([track.artist, ...wrongOptions]);
                return {
                    id: questionId,
                    type: 'artist',
                    question: `Who is the artist of this song?`,
                    correctAnswer: track.artist,
                    options,
                    track: {
                        ...track,
                        artist: track.artist,
                        title: track.title,
                        album: track.album,
                        file: track.file
                    },
                    difficulty: 3,
                    trackPreviewStart: 0
                };
            }
        });
    }
    async generateYearQuestion(questionId, track, settings) {
        return this.generateDecadeQuestion(questionId, track, settings);
    }
    async generateHitsterQuestion(questionId, track, settings) {
        const trackData = track;
        const year = trackData.year || (trackData.date ? parseInt(trackData.date.split('-')[0]) : null);
        if (!year) {
            console.warn('[Quiz-Data] Track has no year for Hitster mode, falling back to artist question');
            return this.generateArtistQuestion(questionId, track, settings);
        }
        return {
            id: questionId,
            type: 'timeline-placement',
            question: 'Place this song in the correct position on the timeline',
            correctAnswer: year.toString(),
            options: [],
            track: {
                ...trackData,
                year
            },
            trackPreviewStart: track.preview_start || this.getRandomPreviewStart(track.duration || 180),
            difficulty: settings.difficultyLevel || 3,
            hints: [
                track.genre ? `Genre: ${track.genre}` : '',
                `Decade: ${Math.floor(year / 10) * 10}s`
            ].filter(h => h),
            audioEffect: null,
            metadata: {
                year,
                decade: `${Math.floor(year / 10) * 10}s`,
                exactYear: year,
                artist: trackData.artist,
                title: trackData.title,
                album: trackData.album
            }
        };
    }
}
exports.QuizDataManager = QuizDataManager;
//# sourceMappingURL=quiz-data.js.map