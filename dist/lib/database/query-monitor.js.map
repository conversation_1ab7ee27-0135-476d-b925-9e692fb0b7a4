{"version": 3, "file": "query-monitor.js", "sourceRoot": "", "sources": ["../../../lib/database/query-monitor.ts"], "names": [], "mappings": ";;;AAqNA,oCAiBC;AAjOD,yCAA6C;AAmB7C,MAAa,YAAY;IAOvB;QALQ,YAAO,GAAmB,EAAE,CAAA;QACnB,eAAU,GAAG,IAAI,CAAA;QACjB,uBAAkB,GAAG,IAAI,CAAA;QACzB,mBAAc,GAAG,KAAK,CAAA;QAGrC,IAAI,CAAC,sBAAsB,EAAE,CAAA;IAC/B,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAA;QAC5C,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAA;IAC9B,CAAC;IAKD,QAAQ,CACN,KAAa,EACb,QAAgB,EAChB,UAAkB,EAClB,KAAa;QAEb,MAAM,MAAM,GAAiB;YAC3B,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;YAChC,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;YACxE,KAAK,EAAE,KAAK,EAAE,OAAO;YACrB,UAAU,EAAE,KAAK,EAAE,KAAK;SACzB,CAAA;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAGzB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1C,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;QACtB,CAAC;QAGD,IAAI,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACvC,uBAAc,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBACzC,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,GAAG,QAAQ,IAAI;gBACzB,UAAU,EAAE,MAAM,CAAC,UAAU;aAC9B,CAAC,CAAA;QACJ,CAAC;QAGD,IAAI,KAAK,EAAE,CAAC;YACV,uBAAc,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,EAAE;gBAC1C,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,GAAG,QAAQ,IAAI;gBACzB,UAAU,EAAE,MAAM,CAAC,UAAU;aAC9B,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKD,mBAAmB;QACjB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO;gBACL,YAAY,EAAE,CAAC;gBACf,mBAAmB,EAAE,CAAC;gBACtB,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC;gBACZ,cAAc,EAAE,EAAE;aACnB,CAAA;QACH,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;QACtE,MAAM,mBAAmB,GAAG,SAAS,GAAG,YAAY,CAAA;QACpD,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAA;QACzF,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAA;QAC7D,MAAM,SAAS,GAAG,CAAC,YAAY,GAAG,YAAY,CAAC,GAAG,GAAG,CAAA;QAGrD,MAAM,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;aACrC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;aACvC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QAEf,OAAO;YACL,YAAY;YACZ,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,GAAG,CAAC,GAAG,GAAG;YAChE,WAAW;YACX,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG;YAC5C,cAAc;SACf,CAAA;IACH,CAAC;IAKD,cAAc,CAAC,QAAgB,EAAE;QAC/B,OAAO,IAAI,CAAC,OAAO;aAChB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC;aACjD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;aAC7D,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACpB,CAAC;IAKD,eAAe,CAAC,QAAgB,EAAE;QAChC,OAAO,IAAI,CAAC,OAAO;aAChB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;aACpB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;aAC7D,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACpB,CAAC;IAKD,KAAK;QACH,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;QACjB,uBAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAA;IAC9C,CAAC;IAKO,aAAa,CAAC,KAAa;QAEjC,OAAO,KAAK;aACT,OAAO,CAAC,0BAA0B,EAAE,gBAAgB,CAAC;aACrD,OAAO,CAAC,uBAAuB,EAAE,aAAa,CAAC;aAC/C,IAAI,EAAE,CAAA;IACX,CAAC;IAKO,kBAAkB,CAAC,MAAa;QACtC,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACxB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAE9B,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,IAAI,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzD,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;IACJ,CAAC;IAKO,sBAAsB;QAC5B,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAA;YAExC,IAAI,KAAK,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;gBAC3B,uBAAc,CAAC,IAAI,CAAC,0BAA0B,EAAE;oBAC9C,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,mBAAmB,EAAE,GAAG,KAAK,CAAC,mBAAmB,IAAI;oBACrD,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,SAAS,EAAE,GAAG,KAAK,CAAC,SAAS,GAAG;iBACjC,CAAC,CAAA;gBAGF,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;gBAChD,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACjC,uBAAc,CAAC,IAAI,CAAC,qBAAqB,EAAE;wBACzC,KAAK,EAAE,iBAAiB,CAAC,MAAM;wBAC/B,OAAO,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;4BACnC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;4BAChC,QAAQ,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI;4BAC3B,SAAS,EAAE,CAAC,CAAC,SAAS;yBACvB,CAAC,CAAC;qBACJ,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;IACzB,CAAC;;AApLH,oCAqLC;AApLgB,qBAAQ,GAAwB,IAAI,AAA5B,CAA4B;AAuLxC,QAAA,YAAY,GAAG,YAAY,CAAC,WAAW,EAAE,CAAA;AAKtD,SAAgB,YAAY,CAC1B,SAAiB,EACjB,aAA+B;IAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IAE5B,OAAO,aAAa,EAAE;SACnB,IAAI,CAAC,MAAM,CAAC,EAAE;QACb,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;QACvC,oBAAY,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;QAC1C,OAAO,MAAM,CAAA;IACf,CAAC,CAAC;SACD,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;QACvC,oBAAY,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;QAC5D,MAAM,KAAK,CAAA;IACb,CAAC,CAAC,CAAA;AACN,CAAC"}