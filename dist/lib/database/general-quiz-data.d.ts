import type { QuizQuestion } from '@/components/enhanced-quiz-interface';
export interface GeneralQuizQuestion {
    id: string;
    categoryId: string;
    type: string;
    difficulty: number;
    question: string;
    options?: string[] | null;
    correctAnswer: string;
    explanation?: string | null;
    timeLimit: number;
    points: number;
    hints: string[];
    mediaUrl?: string | null;
    mediaType?: string | null;
    tags: string[];
    isActive: boolean;
    timesPlayed: number;
    correctAnswers: number;
    averageTime: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface GeneralQuizCategory {
    id: string;
    slug: string;
    name: string;
    description?: string | null;
    icon: string;
    color: string;
    isActive: boolean;
    sortOrder: number;
    createdAt: Date;
    updatedAt: Date;
}
export declare class GeneralQuizService {
    private static instance;
    static getInstance(): GeneralQuizService;
    getCategories(): Promise<GeneralQuizCategory[]>;
    getCategoryById(id: string): Promise<GeneralQuizCategory | null>;
    getQuestionsByCategory(categoryId: string, count?: number, difficulty?: number): Promise<QuizQuestion[]>;
    getMixedQuestions(count?: number, difficulty?: number): Promise<QuizQuestion[]>;
    getQuestionsByCategories(categoryIds: string[], count?: number, difficulty?: number): Promise<QuizQuestion[]>;
    getRandomQuestion(categoryId?: string, difficulty?: number): Promise<QuizQuestion | null>;
    validateAnswer(questionId: string, answer: string | number): Promise<boolean>;
    recordQuestionUsage(questionId: string, isCorrect: boolean, timeSpent: number): Promise<void>;
    getQuestionStats(categoryId?: string): Promise<{
        total: number;
        byDifficulty: {
            1: number;
            2: number;
            3: number;
            4: number;
            5: number;
        };
        byType: {
            'multiple-choice': number;
            'true-false': number;
            slider: number;
        };
        byCategory: Record<string, number>;
    }>;
    private calculateNewAverageTime;
    private mapToQuizQuestion;
}
declare const _default: GeneralQuizService;
export default _default;
