{"version": 3, "file": "postgres-optimizations.js", "sourceRoot": "", "sources": ["../../../lib/database/postgres-optimizations.ts"], "names": [], "mappings": ";;;;;;AAIA,mEAA0C;AAC1C,yCAA6C;AAE7C,MAAa,uBAAuB;IAIlC,MAAM,CAAC,KAAK,CAAC,aAAa;QACxB,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAA;QACrE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,uBAAc,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAA;YAC/E,OAAM;QACR,CAAC;QAED,IAAI,CAAC;YACH,uBAAc,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;YAGrD,MAAM,OAAO,GAAG;gBAEd,4GAA4G;gBAC5G,yGAAyG;gBACzG,0GAA0G;gBAC1G,iHAAiH;gBACjH,+HAA+H;gBAG/H,+FAA+F;gBAC/F,yIAAyI;gBAGzI,4FAA4F;gBAC5F,mGAAmG;gBAGnG,mFAAmF;gBACnF,+GAA+G;gBAC/G,+GAA+G;gBAG/G,sGAAsG;gBACtG,gHAAgH;gBAGhH,0FAA0F;gBAC1F,qGAAqG;gBACrG,4FAA4F;gBAG5F,gHAAgH;gBAChH,kHAAkH;aACnH,CAAA;YAGD,MAAM,gBAAM,CAAC,iBAAiB,CAAC,wCAAwC,CAAC,CAAA;YAGxE,KAAK,MAAM,QAAQ,IAAI,OAAO,EAAE,CAAC;gBAC/B,IAAI,CAAC;oBACH,MAAM,gBAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;oBACxC,uBAAc,CAAC,KAAK,CAAC,kBAAkB,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;gBAC1E,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;wBAC/C,uBAAc,CAAC,IAAI,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;oBACjE,CAAC;gBACH,CAAC;YACH,CAAC;YAED,uBAAc,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAA;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAc,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAc,CAAC,CAAA;QAC7E,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe;QAC1B,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAA;QACrE,IAAI,CAAC,YAAY;YAAE,OAAM;QAEzB,IAAI,CAAC;YACH,uBAAc,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAA;YAEpD,MAAM,MAAM,GAAG;gBACb,WAAW;gBACX,cAAc;gBACd,UAAU;gBACV,eAAe;gBACf,aAAa;gBACb,YAAY;gBACZ,WAAW;aACZ,CAAA;YAED,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,gBAAM,CAAC,iBAAiB,CAAC,YAAY,KAAK,GAAG,CAAC,CAAA;YACtD,CAAC;YAED,uBAAc,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAA;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAc,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAc,CAAC,CAAA;QACpE,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,4BAA4B,CAAC,OAAe;QACjD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,OAAO,CAAA;QAGnD,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAA;QAG5B,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAG9C,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;QACpC,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QAGhC,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAA;QAGzC,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,CAAA;QAC7B,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;QAEnC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAA;QAC9B,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAA;IACvB,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,uBAAuB;QAClC,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAA;QACrE,IAAI,CAAC,YAAY;YAAE,OAAM;QAEzB,IAAI,CAAC;YACH,uBAAc,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;YAGrD,MAAM,gBAAM,CAAC,iBAAiB,CAAC;;;;;;;;;;;OAW9B,CAAC,CAAA;YAGF,MAAM,gBAAM,CAAC,iBAAiB,CAAC;;;OAG9B,CAAC,CAAA;YAGF,MAAM,gBAAM,CAAC,iBAAiB,CAAC;;;;;;;;;;;;;OAa9B,CAAC,CAAA;YAEF,uBAAc,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAA;QAChE,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC/C,uBAAc,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAA;YACpE,CAAC;QACH,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,wBAAwB;QACnC,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAA;QACrE,IAAI,CAAC,YAAY;YAAE,OAAM;QAEzB,IAAI,CAAC;YACH,MAAM,gBAAM,CAAC,iBAAiB,CAAC,uDAAuD,CAAC,CAAA;YACvF,MAAM,gBAAM,CAAC,iBAAiB,CAAC,4DAA4D,CAAC,CAAA;YAC5F,uBAAc,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAA;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAc,CAAC,IAAI,CAAC,sCAAsC,EAAE,KAAc,CAAC,CAAA;QAC7E,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,mBAAmB;QAC9B,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAA;QACrE,IAAI,CAAC,YAAY;YAAE,OAAM;QAEzB,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG;gBACd,oEAAoE;gBACpE,uEAAuE;gBACvE,wEAAwE;gBACxE,uEAAuE;aACxE,CAAA;YAED,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,IAAI,CAAC;oBACH,MAAM,gBAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;gBACxC,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,uBAAc,CAAC,KAAK,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;gBAC7D,CAAC;YACH,CAAC;YAED,uBAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,uBAAc,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAc,CAAC,CAAA;QACxE,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,mBAAmB;QAC9B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAA;QAC1B,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;QAC5B,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACpC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;IAClC,CAAC;CACF;AA7OD,0DA6OC"}