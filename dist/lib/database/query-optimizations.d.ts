import { Prisma } from '@prisma/client';
export declare const optimizedSelects: {
    queueTrack: {
        readonly id: true;
        readonly fileFingerprint: true;
        readonly mpdFilePath: true;
        readonly title: true;
        readonly artist: true;
        readonly album: true;
        readonly albumArtUrl: true;
        readonly localAlbumArtThumbnail: true;
        readonly localAlbumArtCover: true;
        readonly localAlbumArtOriginal: true;
        readonly duration: true;
        readonly calculatedGain: true;
    };
    suggestionWithVotes: {
        readonly id: true;
        readonly title: true;
        readonly artist: true;
        readonly album: true;
        readonly duration: true;
        readonly genre: true;
        readonly year: true;
        readonly filePath: true;
        readonly albumArtUrl: true;
        readonly suggestedById: true;
        readonly suggestedBy: true;
        readonly status: true;
        readonly createdAt: true;
        readonly _count: {
            readonly select: {
                readonly votes: true;
            };
        };
    };
    userMinimal: {
        readonly id: true;
        readonly email: true;
        readonly username: true;
        readonly displayName: true;
        readonly role: true;
    };
    mpdTrack: {
        readonly id: true;
        readonly mpdFilePath: true;
        readonly title: true;
        readonly artist: true;
        readonly albumArtUrl: true;
        readonly genre: true;
        readonly year: true;
    };
    quizTrackMinimal: {
        readonly id: true;
        readonly mpdFilePath: true;
        readonly title: true;
        readonly artist: true;
        readonly album: true;
        readonly year: true;
        readonly genre: true;
        readonly duration: true;
        readonly albumArtUrl: true;
    };
    quizTrackQuestion: {
        readonly id: true;
        readonly mpdFilePath: true;
        readonly title: true;
        readonly artist: true;
        readonly album: true;
        readonly year: true;
        readonly genre: true;
        readonly duration: true;
        readonly albumArtUrl: true;
        readonly triviaFacts: true;
        readonly interestingFacts: true;
        readonly chartData: true;
    };
    gameSessionMinimal: {
        readonly id: true;
        readonly userId: true;
        readonly gameMode: true;
        readonly totalQuestions: true;
        readonly correctAnswers: true;
        readonly totalScore: true;
        readonly startedAt: true;
        readonly completedAt: true;
    };
};
export declare function buildOptimizedQuery<T extends Record<string, any>>(baseQuery: T, selectFields?: Prisma.JsonObject): T;
