"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioOutputManager = void 0;
class AudioOutputManager {
    constructor(mpdClient, initialConfig = {}) {
        this.availableOutputs = [];
        this.streamElement = null;
        this.mpdClient = mpdClient;
        this.config = {
            mode: 'host',
            selectedOutputs: [],
            hostVolume: 70,
            streamVolume: 70,
            ...initialConfig
        };
    }
    async initialize() {
        await this.discoverOutputs();
        await this.configureOutputs();
    }
    async discoverOutputs() {
        try {
            const outputsResponse = await this.mpdClient.sendCommand('outputs');
            this.availableOutputs = this.parseOutputs(outputsResponse);
        }
        catch (error) {
            console.error('Failed to discover MPD outputs:', error);
            throw error;
        }
    }
    parseOutputs(response) {
        const outputs = [];
        const lines = response.split('\n');
        let currentOutput = {};
        for (const line of lines) {
            if (line.startsWith('outputid: ')) {
                if (currentOutput.id) {
                    outputs.push(currentOutput);
                }
                currentOutput = { id: line.split(': ')[1] };
            }
            else if (line.startsWith('outputname: ')) {
                currentOutput.name = line.split(': ')[1];
            }
            else if (line.startsWith('outputenabled: ')) {
                currentOutput.enabled = line.split(': ')[1] === '1';
            }
            else if (line.startsWith('plugin: ')) {
                currentOutput.type = line.split(': ')[1];
            }
        }
        if (currentOutput.id) {
            outputs.push(currentOutput);
        }
        return outputs;
    }
    async configureOutputs() {
        switch (this.config.mode) {
            case 'host':
                await this.enableHostOutput();
                await this.disableStreamOutput();
                break;
            case 'stream':
                await this.disableHostOutput();
                await this.enableStreamOutput();
                break;
            case 'both':
                await this.enableHostOutput();
                await this.enableStreamOutput();
                break;
        }
    }
    async enableHostOutput() {
        const hostOutputs = this.availableOutputs.filter(o => o.type === 'pulse' || o.type === 'alsa');
        for (const output of hostOutputs) {
            try {
                await this.mpdClient.sendCommand(`enableoutput ${output.id}`);
                console.log(`Enabled host output: ${output.name}`);
            }
            catch (error) {
                console.warn(`Failed to enable host output ${output.name}:`, error);
            }
        }
    }
    async disableHostOutput() {
        const hostOutputs = this.availableOutputs.filter(o => o.type === 'pulse' || o.type === 'alsa');
        for (const output of hostOutputs) {
            try {
                await this.mpdClient.sendCommand(`disableoutput ${output.id}`);
                console.log(`Disabled host output: ${output.name}`);
            }
            catch (error) {
                console.warn(`Failed to disable host output ${output.name}:`, error);
            }
        }
    }
    async enableStreamOutput() {
        const streamOutputs = this.availableOutputs.filter(o => o.type === 'httpd');
        for (const output of streamOutputs) {
            try {
                await this.mpdClient.sendCommand(`enableoutput ${output.id}`);
                console.log(`Enabled stream output: ${output.name}`);
                await this.setupClientStream(output);
            }
            catch (error) {
                console.warn(`Failed to enable stream output ${output.name}:`, error);
            }
        }
    }
    async disableStreamOutput() {
        const streamOutputs = this.availableOutputs.filter(o => o.type === 'httpd');
        for (const output of streamOutputs) {
            try {
                await this.mpdClient.sendCommand(`disableoutput ${output.id}`);
                console.log(`Disabled stream output: ${output.name}`);
            }
            catch (error) {
                console.warn(`Failed to disable stream output ${output.name}:`, error);
            }
        }
        if (this.streamElement) {
            this.streamElement.pause();
            this.streamElement = null;
        }
    }
    async setupClientStream(output) {
        const streamUrl = this.config.streamUrl ||
            `http://${process.env.NEXT_PUBLIC_MPD_HOST || 'localhost'}:8000/`;
        if (typeof window !== 'undefined') {
            this.streamElement = new Audio(streamUrl);
            this.streamElement.crossOrigin = 'anonymous';
            this.streamElement.volume = this.config.streamVolume / 100;
            this.streamElement.autoplay = true;
            this.streamElement.addEventListener('error', (e) => {
                console.error('Stream playback error:', e);
            });
            this.streamElement.addEventListener('loadstart', () => {
                console.log('Stream loading started');
            });
        }
    }
    async setOutputMode(mode) {
        this.config.mode = mode;
        await this.configureOutputs();
    }
    async setVolume(type, volume) {
        const clampedVolume = Math.max(0, Math.min(100, volume));
        if (type === 'host') {
            this.config.hostVolume = clampedVolume;
            await this.mpdClient.setVolume(clampedVolume);
        }
        else if (type === 'stream') {
            this.config.streamVolume = clampedVolume;
            if (this.streamElement) {
                this.streamElement.volume = clampedVolume / 100;
            }
        }
    }
    getAvailableOutputs() {
        return [...this.availableOutputs];
    }
    getConfig() {
        return { ...this.config };
    }
    isStreamingAvailable() {
        return this.availableOutputs.some(o => o.type === 'httpd');
    }
    async startStream() {
        if (this.streamElement && (this.config.mode === 'stream' || this.config.mode === 'both')) {
            try {
                await this.streamElement.play();
            }
            catch (error) {
                console.error('Failed to start stream:', error);
                throw error;
            }
        }
    }
    stopStream() {
        if (this.streamElement) {
            this.streamElement.pause();
            this.streamElement.currentTime = 0;
        }
    }
    getStreamStatus() {
        if (!this.streamElement) {
            return { connected: false, playing: false };
        }
        return {
            connected: this.streamElement.readyState >= 2,
            playing: !this.streamElement.paused,
            url: this.streamElement.src
        };
    }
}
exports.AudioOutputManager = AudioOutputManager;
//# sourceMappingURL=audio-output-manager.js.map