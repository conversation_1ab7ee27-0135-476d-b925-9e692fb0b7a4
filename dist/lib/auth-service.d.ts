import type { UserRole } from '@/lib/user-context';
export interface AuthUser {
    id: string;
    email: string;
    username: string;
    displayName?: string;
    role: UserRole;
    avatarUrl?: string;
    preferences: string;
    lastLogin?: Date;
    createdAt: Date;
}
export declare class AuthService {
    private static readonly TOKEN_EXPIRY;
    private static readonly REFRESH_THRESHOLD;
    static authenticate(loginId: string, password: string): Promise<{
        user: AuthUser;
        token: string;
    } | null>;
    static validateSession(token: string): Promise<{
        user: AuthUser;
        newToken?: string;
    } | null>;
    static verifyToken(token: string): Promise<AuthUser | null>;
    static refreshUserData(userId: string): Promise<AuthUser | null>;
    static generateToken(user: AuthUser): Promise<string>;
    private static hasUserDataChanged;
}
export declare class TokenManager {
    private static readonly TOKEN_KEY;
    private static readonly USER_KEY;
    static getToken(): string | null;
    static setToken(token: string): void;
    static removeToken(): void;
    static getStoredUser(): AuthUser | null;
    static setStoredUser(user: AuthUser): void;
}
export declare function validateToken(request: Request): Promise<{
    success: boolean;
    user?: AuthUser;
    error?: string;
}>;
