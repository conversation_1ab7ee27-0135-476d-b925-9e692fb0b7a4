export interface AudioConfig {
    mpdHost: string;
    mpdPort: number;
    mpdPassword?: string;
    mpdHttpPort: number;
    statusUpdateInterval: number;
    enableLogging: boolean;
    autoReconnect: boolean;
    maxReconnectAttempts: number;
    quizTimeLimit: number;
    fadeEffects: boolean;
    defaultVolume: number;
}
export interface MpdConnectionConfigParams {
    host: string;
    port: number;
    password?: string;
    httpProxyPort?: number;
    timeout?: number;
}
export declare function getAudioConfig(): AudioConfig;
export declare function getMpdConnectionConfig(): MpdConnectionConfigParams;
export declare function validateAudioConfig(config: AudioConfig): string[];
export declare function isDevelopmentMode(): boolean;
export declare function getAppUrl(): string;
