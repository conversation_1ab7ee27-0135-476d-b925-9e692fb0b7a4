export interface StreamConfig {
    mpdHost: string;
    mpdPort: number;
    streamPort: number;
    fallbackStreamPort?: number;
    enableLowLatency?: boolean;
    bufferSize?: number;
    maxRetries?: number;
}
export interface StreamStatus {
    isConnected: boolean;
    isStreaming: boolean;
    streamUrl: string;
    latency: number;
    bitrate: number;
    clients: number;
    error?: string;
}
export declare class MPDStreamManager {
    private config;
    private streamUrl;
    private audioElement;
    private isInitialized;
    private reconnectAttempts;
    private statusCallbacks;
    constructor(config: StreamConfig);
    initialize(): Promise<void>;
    private checkStreamAvailability;
    private setupAudioElement;
    startStream(): Promise<void>;
    stopStream(): void;
    setVolume(volume: number): void;
    getStatus(): StreamStatus;
    private handleStreamError;
    private handleStreamStall;
    private estimateLatency;
    private getLastError;
    onStatusChange(callback: (status: StreamStatus) => void): void;
    offStatusChange(callback: (status: StreamStatus) => void): void;
    private notifyStatusChange;
    dispose(): void;
}
export declare function createMPDStreamManager(config: Partial<StreamConfig>): MPDStreamManager;
