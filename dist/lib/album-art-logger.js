"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logAlbumArtChange = logAlbumArtChange;
let logBuffer = [];
let logTimer = null;
function logAlbumArtChange(message) {
    if (process.env.NODE_ENV !== 'development')
        return;
    logBuffer.push(message);
    if (logTimer)
        clearTimeout(logTimer);
    logTimer = setTimeout(() => {
        if (logBuffer.length > 0) {
            console.log(`[AlbumArt] Batch update: ${logBuffer.length} components changed`);
            if (logBuffer.length <= 5) {
                logBuffer.forEach(msg => console.log(`  - ${msg}`));
            }
            logBuffer = [];
        }
    }, 100);
}
//# sourceMappingURL=album-art-logger.js.map