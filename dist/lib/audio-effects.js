"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AUDIO_TRICKS_PRESETS = exports.AudioEffectsProcessor = void 0;
class AudioEffectsProcessor {
    constructor() {
        this.effects = new Map();
        this.audioContext = new AudioContext();
        this.initializeEffects();
    }
    initializeEffects() {
        this.effects.set('backwards', {
            id: 'backwards',
            name: 'Backwards',
            description: 'Audio played in reverse',
            difficulty: 4,
            apply: (ctx, source) => {
                const gainNode = ctx.createGain();
                source.connect(gainNode);
                return gainNode;
            }
        });
        this.effects.set('speed-up', {
            id: 'speed-up',
            name: 'Speed Up',
            description: 'Faster playback (1.5x-2x)',
            difficulty: 2,
            apply: (ctx, source) => {
                const gainNode = ctx.createGain();
                source.playbackRate.value = 1.5 + Math.random() * 0.5;
                source.connect(gainNode);
                return gainNode;
            }
        });
        this.effects.set('slow-down', {
            id: 'slow-down',
            name: 'Slow Down',
            description: 'Slower playback (0.5x-0.75x)',
            difficulty: 3,
            apply: (ctx, source) => {
                const gainNode = ctx.createGain();
                source.playbackRate.value = 0.5 + Math.random() * 0.25;
                source.connect(gainNode);
                return gainNode;
            }
        });
        this.effects.set('low-pass', {
            id: 'low-pass',
            name: 'Muffled',
            description: 'Low-pass filtered (underwater effect)',
            difficulty: 3,
            apply: (ctx, source) => {
                const filter = ctx.createBiquadFilter();
                filter.type = 'lowpass';
                filter.frequency.value = 800 + Math.random() * 1200;
                filter.Q.value = 5;
                source.connect(filter);
                return filter;
            }
        });
        this.effects.set('high-pass', {
            id: 'high-pass',
            name: 'Tinny',
            description: 'High-pass filtered (telephone effect)',
            difficulty: 3,
            apply: (ctx, source) => {
                const filter = ctx.createBiquadFilter();
                filter.type = 'highpass';
                filter.frequency.value = 1000 + Math.random() * 1500;
                filter.Q.value = 3;
                source.connect(filter);
                return filter;
            }
        });
        this.effects.set('pitch-shift', {
            id: 'pitch-shift',
            name: 'Pitch Shift',
            description: 'Higher or lower pitch',
            difficulty: 4,
            apply: (ctx, source) => {
                const gainNode = ctx.createGain();
                const semitones = (Math.random() - 0.5) * 8;
                source.playbackRate.value = Math.pow(2, semitones / 12);
                source.connect(gainNode);
                return gainNode;
            }
        });
        this.effects.set('echo', {
            id: 'echo',
            name: 'Echo',
            description: 'Added echo and reverb',
            difficulty: 2,
            apply: (ctx, source) => {
                const delay = ctx.createDelay(1.0);
                const feedback = ctx.createGain();
                const wetGain = ctx.createGain();
                const dryGain = ctx.createGain();
                const output = ctx.createGain();
                delay.delayTime.value = 0.2 + Math.random() * 0.3;
                feedback.gain.value = 0.3 + Math.random() * 0.2;
                wetGain.gain.value = 0.4;
                dryGain.gain.value = 0.6;
                source.connect(dryGain);
                source.connect(delay);
                delay.connect(feedback);
                feedback.connect(delay);
                delay.connect(wetGain);
                dryGain.connect(output);
                wetGain.connect(output);
                return output;
            }
        });
        this.effects.set('distortion', {
            id: 'distortion',
            name: 'Distortion',
            description: 'Heavily distorted audio',
            difficulty: 5,
            apply: (ctx, source) => {
                const waveshaper = ctx.createWaveShaper();
                const makeDistortionCurve = (amount) => {
                    const samples = 44100;
                    const curve = new Float32Array(samples);
                    const deg = Math.PI / 180;
                    for (let i = 0; i < samples; i++) {
                        const x = (i * 2) / samples - 1;
                        curve[i] = ((3 + amount) * x * 20 * deg) / (Math.PI + amount * Math.abs(x));
                    }
                    return curve;
                };
                waveshaper.curve = makeDistortionCurve(20 + Math.random() * 80);
                waveshaper.oversample = '4x';
                source.connect(waveshaper);
                return waveshaper;
            }
        });
    }
    getAvailableEffects() {
        return Array.from(this.effects.values());
    }
    getRandomEffect() {
        const effects = this.getAvailableEffects();
        return effects[Math.floor(Math.random() * effects.length)];
    }
    getRandomEffectCombination(count = 2) {
        const effects = this.getAvailableEffects();
        const selected = [];
        while (selected.length < count && selected.length < effects.length) {
            const effect = effects[Math.floor(Math.random() * effects.length)];
            if (!selected.includes(effect)) {
                selected.push(effect);
            }
        }
        return selected;
    }
    async applyEffects(audioBuffer, effects) {
        const offlineContext = new OfflineAudioContext(audioBuffer.numberOfChannels, audioBuffer.length, audioBuffer.sampleRate);
        const source = offlineContext.createBufferSource();
        source.buffer = audioBuffer;
        let currentNode = source;
        for (const effect of effects) {
            currentNode = effect.apply(offlineContext, source);
        }
        currentNode.connect(offlineContext.destination);
        source.start(0);
        try {
            return await offlineContext.startRendering();
        }
        catch (error) {
            console.error('Audio processing failed:', error);
            return audioBuffer;
        }
    }
    async processAudioForBackwards(audioBuffer) {
        const reversedBuffer = this.audioContext.createBuffer(audioBuffer.numberOfChannels, audioBuffer.length, audioBuffer.sampleRate);
        for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
            const originalData = audioBuffer.getChannelData(channel);
            const reversedData = reversedBuffer.getChannelData(channel);
            for (let i = 0; i < originalData.length; i++) {
                reversedData[i] = originalData[originalData.length - 1 - i];
            }
        }
        return reversedBuffer;
    }
    static getEffectDifficultyLevel(effects) {
        if (effects.length === 0)
            return 1;
        const avgDifficulty = effects.reduce((sum, effect) => sum + effect.difficulty, 0) / effects.length;
        return Math.round(avgDifficulty);
    }
    static getEffectDescription(effects) {
        if (effects.length === 0)
            return 'No effects';
        if (effects.length === 1)
            return effects[0].description;
        return `${effects.map(e => e.name).join(' + ')}`;
    }
    cleanup() {
        if (this.audioContext.state !== 'closed') {
            this.audioContext.close();
        }
    }
}
exports.AudioEffectsProcessor = AudioEffectsProcessor;
exports.AUDIO_TRICKS_PRESETS = {
    level1: [
        ['speed-up'],
        ['slow-down'],
        ['low-pass'],
        ['high-pass']
    ],
    level2: [
        ['echo'],
        ['pitch-shift'],
        ['speed-up', 'low-pass'],
        ['slow-down', 'high-pass']
    ],
    level3: [
        ['backwards'],
        ['distortion'],
        ['speed-up', 'echo'],
        ['pitch-shift', 'low-pass']
    ],
    level4: [
        ['backwards', 'low-pass'],
        ['speed-up', 'pitch-shift'],
        ['distortion', 'echo'],
        ['slow-down', 'backwards']
    ],
    level5: [
        ['backwards', 'pitch-shift', 'low-pass'],
        ['distortion', 'echo', 'speed-up'],
        ['slow-down', 'backwards', 'high-pass']
    ]
};
//# sourceMappingURL=audio-effects.js.map