{"version": 3, "file": "performance-monitor.js", "sourceRoot": "", "sources": ["../../lib/performance-monitor.ts"], "names": [], "mappings": ";;;AAsRA,4CASC;AA1RD,iCAAiC;AAgBjC,MAAM,kBAAkB;IAQtB;QANQ,YAAO,GAAqC,IAAI,GAAG,EAAE,CAAA;QACrD,kBAAa,GAAmB,EAAE,CAAA;QAClC,cAAS,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAA;QAClD,eAAU,GAAG,IAAI,CAAA;QACjB,qBAAgB,GAAG,GAAG,CAAA;QAI5B,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,qBAAqB,IAAI,MAAM,EAAE,CAAC;YACrE,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC5B,CAAC;IACH,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACjC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAA;QACxD,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAA;IACpC,CAAC;IAEO,mBAAmB;QAEzB,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,IAAI,mBAAmB,CAAC,CAAC,IAAI,EAAE,EAAE;gBACxD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;oBACtC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;gBACrD,CAAC;YACH,CAAC,CAAC,CAAA;YACF,gBAAgB,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;QACxD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;QAEb,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC3D,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;oBACtC,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;wBACrB,IAAI,CAAC,YAAY,CAAC,aAAa,EAAG,KAAa,CAAC,KAAK,CAAC,CAAA;oBACxD,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAA;YACF,mBAAmB,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;QAC/D,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;QAEb,CAAC;IACH,CAAC;IAKD,YAAY,CAAC,IAAY,EAAE,KAAa,EAAE,IAAa;QACrD,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAM;QAE3B,MAAM,MAAM,GAAsB;YAChC,IAAI;YACJ,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI;SACL,CAAA;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;QAC5B,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAE,CAAA;QAC3C,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAGxB,IAAI,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACzC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,CAAA;QAC7D,CAAC;IACH,CAAC;IAKD,YAAY,CAAC,aAAqB,EAAE,UAAkB,EAAE,KAA2B;QACjF,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAM;QAE3B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACtB,aAAa;YACb,UAAU;YACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,KAAK;SACN,CAAC,CAAA;QAGF,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACjF,CAAC;QAGD,IAAI,CAAC,YAAY,CAAC,UAAU,aAAa,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;IAChE,CAAC;IAKD,KAAK,CAAC,YAAY,CAAI,IAAY,EAAE,EAAoB;QACtD,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,EAAE,EAAE,CAAA;QAEhC,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAA;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,EAAE,EAAE,CAAA;YACzB,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;YAC1C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACvC,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;YAC1C,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YAClD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,OAAO,CAAI,IAAY,EAAE,EAAW;QAClC,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,EAAE,EAAE,CAAA;QAEhC,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAA;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,EAAE,EAAE,CAAA;YACnB,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;YAC1C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACvC,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;YAC1C,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YAClD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,iBAAiB,CAAC,UAAmB;QACnC,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAA;YAClD,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;QACvD,CAAC;QAED,MAAM,OAAO,GAAwB,EAAE,CAAA;QACvC,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;QAChE,CAAC;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAKD,gBAAgB,CAAC,aAAsB;QACrC,IAAI,WAAW,GAAG,IAAI,CAAC,aAAa,CAAA;QAEpC,IAAI,aAAa,EAAE,CAAC;YAClB,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,KAAK,aAAa,CAAC,CAAA;QAC1E,CAAC;QAED,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACjD,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC/B,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,EAAE,CAAA;YAChC,CAAC;YACD,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;YACjD,OAAO,GAAG,CAAA;QACZ,CAAC,EAAE,EAA8B,CAAC,CAAA;QAElC,MAAM,OAAO,GAAwB,EAAE,CAAA;QACvC,KAAK,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACzD,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAKO,cAAc,CAAC,MAAgB;QACrC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAA;QACtE,CAAC;QAED,MAAM,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;QAChD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QAE7C,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM;YACpB,IAAI,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM;YACzB,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;YACd,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YAC9B,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;YAC5C,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;YAC5C,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;SAC9C,CAAA;IACH,CAAC;IAKD,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAM;QAE3B,OAAO,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAA;QAEjD,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;QACnC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAA;QACvC,OAAO,CAAC,QAAQ,EAAE,CAAA;QAElB,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAA;QACtC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QACtC,OAAO,CAAC,QAAQ,EAAE,CAAA;QAElB,OAAO,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAC1C,IAAI,OAAO,MAAM,KAAK,WAAW,IAAK,MAAc,CAAC,cAAc,EAAE,CAAC;YACpE,OAAO,CAAC,GAAG,CAAE,MAAc,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAA;QACxD,CAAC;QACD,OAAO,CAAC,QAAQ,EAAE,CAAA;QAElB,OAAO,CAAC,QAAQ,EAAE,CAAA;IACpB,CAAC;IAKD,KAAK;QACH,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;QACpB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAA;IACzB,CAAC;IAKD,UAAU,CAAC,OAAgB;QACzB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAA;IAC1B,CAAC;CACF;AAGY,QAAA,kBAAkB,GAAG,kBAAkB,CAAC,WAAW,EAAE,CAAA;AAG3D,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,KAAa,EAAE,IAAa,EAAE,EAAE,CACzE,0BAAkB,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;AADvC,QAAA,YAAY,gBAC2B;AAE7C,MAAM,YAAY,GAAG,CAAC,aAAqB,EAAE,UAAkB,EAAE,KAA2B,EAAE,EAAE,CACrG,0BAAkB,CAAC,YAAY,CAAC,aAAa,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;AADtD,QAAA,YAAY,gBAC0C;AAE5D,MAAM,YAAY,GAAG,CAAI,IAAY,EAAE,EAAoB,EAAE,EAAE,CACpE,0BAAkB,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;AAD9B,QAAA,YAAY,gBACkB;AAEpC,MAAM,OAAO,GAAG,CAAI,IAAY,EAAE,EAAW,EAAE,EAAE,CACtD,0BAAkB,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;AADzB,QAAA,OAAO,WACkB;AAGtC,SAAgB,gBAAgB,CAAC,aAAqB,EAAE,KAA2B;IACjF,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE,CAAA;QAEpC,OAAO,GAAG,EAAE;YACV,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,UAAU,CAAA;YAC/C,IAAA,oBAAY,EAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAA;QAC9C,CAAC,CAAA;IACH,CAAC,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC,CAAA;AAC5B,CAAC;AAGD,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;IAC3E,MAAc,CAAC,kBAAkB,GAAG,0BAAkB,CAAA;AACzD,CAAC"}