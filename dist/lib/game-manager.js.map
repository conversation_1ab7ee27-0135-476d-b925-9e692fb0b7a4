{"version": 3, "file": "game-manager.js", "sourceRoot": "", "sources": ["../../lib/game-manager.ts"], "names": [], "mappings": ";;;AAOA,mEAA6D;AAC7D,6EAAuE;AAuBvE,MAAa,WAAW;IAOtB,YAAY,MAAkB;QAJtB,uBAAkB,GAAuB,IAAI,CAAA;QAKnD,IAAI,CAAC,aAAa,GAAG,IAAI,qDAAwB,EAAE,CAAA;QACnD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY,IAAI,KAAK,CAAA;QACjD,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAA;QAC3C,IAAI,CAAC,SAAS,GAAG;YACf,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,OAAO,EAAE,EAAE;YACX,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;YACvC,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,KAAK;YAClC,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,SAAS,EAAE,EAAE;YACb,oBAAoB,EAAE,CAAC,CAAC;YACxB,eAAe,EAAE,IAAI;YACrB,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,EAAE;YAC3C,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,EAAE;YAC7C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE;gBACR,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC;gBAClC,eAAe,EAAE,KAAK;gBACtB,gBAAgB,EAAE,IAAI;aACvB;SACF,CAAA;IACH,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,IAAY;QAC3B,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,CAAA;QAG/B,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;QAE9E,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;gBAC3C,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAA;gBAC7D,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAA;gBACnK,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAA;gBAC9E,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAA;gBAC5E,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;YACpF,CAAC;iBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACjD,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAA;gBACtE,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,iCAAiC,CACrF,IAAI,CAAC,SAAS,CAAC,cAAc,EAC7B,CAAC,SAAS,CAAC,EACX,CAAC,EACD,IAAI,CAAC,SAAS,CAAC,eAAe,CAC/B,CAAA;gBACD,OAAO,CAAC,GAAG,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAA;gBACtF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAA;YAC9E,CAAC;iBAAM,CAAC;gBAEN,OAAO,CAAC,GAAG,CAAC,yDAAyD,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;gBAC/F,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;oBACjE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;oBACjC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc;oBAC7C,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;oBAC/C,OAAO,EAAE,SAAS;iBACnB,CAAC,CAAA;gBACF,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAA;gBACpF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAA;YAC9E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uDAAuD,IAAI,CAAC,SAAS,CAAC,QAAQ,yBAAyB,EAAE,KAAK,CAAC,CAAA;YAC7H,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACzD,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAA;QAC/D,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,SAAS,CAAC,MAAc;QACtB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;QAC1D,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACnC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,YAAY,CAAC,QAAgB;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAA;QACtE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QACzC,CAAC;QACD,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,SAAS;QACP,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,CAAA;QACjC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACrC,IAAI,CAAC,SAAS,CAAC,oBAAoB,GAAG,CAAC,CAAA;QACvC,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QAC5D,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,YAAY,CAAC,QAAgB,EAAE,WAAmB,EAAE,SAAiB;QACnE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAA;QAClE,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,WAAW,GAAG,IAAI,CAAA;YACzB,MAAM,CAAC,UAAU,GAAG,WAAW,CAAA;YAC/B,MAAM,CAAC,cAAc,GAAG,SAAS,CAAA;QACnC,CAAC;QACD,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,sBAAsB;QACpB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,kBAAkB,CAAA;QAG1C,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC5B,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC3B,CAAC;aAAM,CAAC;QAGR,CAAC;QAED,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,YAAY;QACV,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAA;QACrC,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAA;QAC9F,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,CAAA;QACjC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,CAAA;QAG1D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAClC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;gBACxB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;gBAC3B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAA;YACjC,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,OAAO;QACL,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,CAAA;QAClC,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,YAAY;QACV,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,cAAc,CAAC,OAAgB,EAAE,QAA2B;QAC1D,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAA;QACjC,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAA;QAC5D,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACzE,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,OAAO,CAAC,IAAU;QAChB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAC7C,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE,CAAA;QAC3B,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/B,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,UAAU,CAAC,MAAc;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC1B,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;QAC9B,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAA;QAClE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QACvC,CAAC;QACD,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,eAAe,CAAC,QAAgB,EAAE,MAAc;QAC9C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;QACvC,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAA;QAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAA;QAElE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAC7C,CAAC;QAGD,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,WAAW;YACxC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;QACjC,CAAC;QAGD,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAA;QAGvC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAEzB,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,oBAAoB,CAAC,QAAgB;QACnC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAA;QACvC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,gBAAgB,CAAC,MAAc,EAAE,WAAmB,EAAE,SAAiB,EAAE,WAAmB;QAC1F,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAC7C,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAA;QAC5D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;QACnC,CAAC;QAGD,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,CAAA;QACnE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC/C,CAAC;QAGD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,UAAU,GAAG,WAAW,CAAA;QAC7B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAA;QAG/B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,CAAA;YACvE,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,WAAW,GAAG,IAAI,CAAA;gBAC7B,UAAU,CAAC,UAAU,GAAG,WAAW,CAAA;gBACnC,UAAU,CAAC,cAAc,GAAG,SAAS,CAAA;YACvC,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,kBAAkB;QAChB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;YACzF,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;QAC9B,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,CAAA;QAClE,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,IAAI,SAAS,CAAA;QAE7E,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAClC,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,KAAK,aAAa,EAAE,CAAC;gBAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,CAAC,CAAA;gBACtE,IAAI,UAAU,GAAG,CAAC,CAAA;gBAElB,QAAQ,WAAW,EAAE,CAAC;oBACpB,KAAK,SAAS;wBACZ,UAAU,GAAG,UAAU,CAAA;wBACvB,MAAK;oBACP,KAAK,KAAK;wBACR,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;wBAC7C,MAAK;oBACP,KAAK,MAAM;wBACT,UAAU,GAAG,UAAU,GAAG,GAAG,CAAA;wBAC7B,MAAK;oBACP,KAAK,SAAS;wBACZ,UAAU,GAAG,UAAU,CAAA;wBACvB,MAAK;gBACT,CAAC;gBAGD,IAAI,CAAC,KAAK,IAAI,UAAU,CAAA;gBAGxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,CAAA;oBACvE,IAAI,UAAU,EAAE,CAAC;wBACf,UAAU,CAAC,KAAK,IAAI,UAAU,CAAA;oBAChC,CAAC;gBACH,CAAC,CAAC,CAAA;YACJ,CAAC;YAGD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;YACxB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;YAC3B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAA;QACjC,CAAC,CAAC,CAAA;QAEF,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC9B,CAAC;IAKD,mBAAmB;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC1B,OAAO,EAAE,CAAA;QACX,CAAC;QACD,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;IACpE,CAAC;IAKO,wBAAwB,CAAC,QAAgB;QAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC1B,OAAM;QACR,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAClC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAA;YAClE,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE,CAAC;gBACvB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAA;gBAGnC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,CAAA;oBACxE,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;wBACrB,IAAI,CAAC,SAAS,CAAC,KAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;oBAC5C,CAAC;gBACH,CAAC;qBAEI,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;gBACrC,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAKO,mBAAmB,CAAC,SAAiB;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAA;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,OAAO,CAAC,CAAA;QAC9D,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC,CAAA;IAC3C,CAAC;IAEO,qBAAqB;QAC3B,MAAM,SAAS,GAAe,EAAE,CAAA;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,SAAS,CAAC,IAAI,CAAC;gBACb,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;gBACf,IAAI,EAAE,iBAAiB;gBACvB,SAAS,EAAE,iBAAiB,CAAC,GAAG,CAAC,MAAM;gBACvC,QAAQ,EAAE,oBAAoB;gBAC9B,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;gBACjD,aAAa,EAAE,CAAC;gBAChB,MAAM,EAAE,GAAG;gBACX,SAAS,EAAE,EAAE;aACd,CAAC,CAAA;QACJ,CAAC;QACD,OAAO,SAAS,CAAA;IAClB,CAAC;IAEO,sBAAsB,CAAC,oBAA2B;QACxD,OAAO,oBAAoB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAC9C,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE;YAC5B,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,iBAAiB;YAClC,SAAS,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,IAAI,iBAAiB,KAAK,GAAG,CAAC,MAAM;YAC7D,QAAQ,EAAE,EAAE,CAAC,QAAQ;YACrB,OAAO,EAAE,EAAE,CAAC,OAAO,IAAI,EAAE;YACzB,aAAa,EAAE,OAAO,EAAE,CAAC,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAC1E,MAAM,EAAE,GAAG;YACX,SAAS,EAAE,EAAE,CAAC,SAAS,IAAI,EAAE;YAC7B,KAAK,EAAE,EAAE,CAAC,KAAK;SAChB,CAAC,CAAC,CAAA;IACL,CAAC;IAKD,kBAAkB;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;IACnE,CAAC;IAKD,cAAc;QACZ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;IACtE,CAAC;IASD,gBAAgB,CACd,IAAyB,EACzB,MAAW,EACX,aAAwD;QAExD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAA;QAClD,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,SAAiB,CAAA;QACrB,MAAM,WAAW,GAAgB;YAC/B,IAAI;YACJ,KAAK,EAAE,aAAa,EAAE,KAAK,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YAC/D,WAAW,EAAE,aAAa,EAAE,WAAW,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC;YACjF,QAAQ,EAAE,IAAI;SACf,CAAA;QAGD,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,UAAU;gBACb,SAAS,GAAG,2CAAmB,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,YAAY,EAAE,MAAM,CAAC,CAAA;gBACjG,MAAK;YACP,KAAK,QAAQ;gBACX,SAAS,GAAG,2CAAmB,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,YAAY,EAAE,MAAM,CAAC,CAAA;gBAC/F,MAAK;YACP,KAAK,WAAW;gBACd,SAAS,GAAG,2CAAmB,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,YAAY,EAAE,MAAM,CAAC,CAAA;gBACjG,MAAK;YACP;gBACE,OAAO,IAAI,CAAA;QACf,CAAC;QAED,WAAW,CAAC,SAAS,GAAG,SAAS,CAAA;QACjC,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAA;QAErC,OAAO,WAAW,CAAA;IACpB,CAAC;IAKD,mBAAmB;QACjB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;YACnE,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,aAAa,GAAG,2CAAmB,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QACxF,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAA;QACnC,IAAI,UAAU,GAAG,EAAE,CAAA;QAGnB,QAAQ,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;YACrC,KAAK,UAAU;gBAEb,UAAU,GAAG,2BAA2B,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;gBACnE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;gBAC7C,MAAK;YAEP,KAAK,QAAQ;gBAEX,UAAU,GAAG,sCAAsC,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;gBAC9E,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;gBAC3C,MAAK;YAEP,KAAK,WAAW;gBAEd,UAAU,GAAG,wBAAwB,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;gBAChE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;gBAC7C,MAAK;YAEP;gBACE,UAAU,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,eAAe,CAAA;QAC5D,CAAC;QAGD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAA;QAE9B,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,CAAA;IAC/B,CAAC;IAKD,uBAAuB;QACrB,OAAO,2CAAmB,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;IAC3E,CAAC;IAKD,cAAc,CAAC,QAAgB;QAC7B,OAAO,2CAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;IAC5E,CAAC;IAKD,aAAa,CAAC,QAAgB;QAC5B,OAAO,2CAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;IAC3E,CAAC;IAKD,mBAAmB,CAAC,aAAqB;QACvC,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO,KAAK,CAAA;QAGrC,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,GAAG,CAAC,CAAA;IAC3D,CAAC;IAKD,aAAa,CAAC,aAAqB;QACjC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;QAE/C,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,CAAC,CAAC,CAAC,OAAO,UAAU,CAAA;YACzB,KAAK,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAA;YACvB,KAAK,CAAC,CAAC,CAAC,OAAO,WAAW,CAAA;YAC1B,OAAO,CAAC,CAAC,OAAO,UAAU,CAAA;QAC5B,CAAC;IACH,CAAC;IAKD,qBAAqB;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAA;IAChC,CAAC;IAKD,gBAAgB,CAAC,OAAgB;QAC/B,IAAI,CAAC,aAAa,GAAG,OAAO,CAAA;IAC9B,CAAC;IAKO,qBAAqB,CAAC,IAAyB;QACrD,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,UAAU,CAAC,CAAC,OAAO,sBAAsB,CAAA;YAC9C,KAAK,QAAQ,CAAC,CAAC,OAAO,oBAAoB,CAAA;YAC1C,KAAK,WAAW,CAAC,CAAC,OAAO,kBAAkB,CAAA;YAC3C,KAAK,OAAO,CAAC,CAAC,OAAO,cAAc,CAAA;YACnC,OAAO,CAAC,CAAC,OAAO,UAAU,CAAA;QAC5B,CAAC;IACH,CAAC;IAEO,2BAA2B,CAAC,IAAyB;QAC3D,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,UAAU,CAAC,CAAC,OAAO,gDAAgD,CAAA;YACxE,KAAK,QAAQ,CAAC,CAAC,OAAO,8CAA8C,CAAA;YACpE,KAAK,WAAW,CAAC,CAAC,OAAO,2CAA2C,CAAA;YACpE,KAAK,OAAO,CAAC,CAAC,OAAO,uCAAuC,CAAA;YAC5D,OAAO,CAAC,CAAC,OAAO,4CAA4C,CAAA;QAC9D,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,QAAgB;QAGpC,OAAO,CAAC,GAAG,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAA;IACtD,CAAC;IAEO,WAAW,CAAC,MAAc;QAEhC,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,EAAE,CAAC,CAAA;IAClD,CAAC;IAEO,aAAa,CAAC,QAAgB;QAEpC,OAAO,CAAC,GAAG,CAAC,uBAAuB,QAAQ,EAAE,CAAC,CAAA;QAG9C,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,OAAO;gBACV,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,EAAE,CAAA;gBACnC,MAAK;YACP,KAAK,WAAW;gBACd,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,EAAE,CAAA;gBACnC,MAAK;YACP;gBACE,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,EAAE,CAAA;QACvC,CAAC;IACH,CAAC;IAKD,aAAa;QACX,2CAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QACtD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAA;IAChC,CAAC;CACF;AA3oBD,kCA2oBC"}