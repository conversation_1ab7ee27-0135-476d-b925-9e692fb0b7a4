"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioOutputSelector = void 0;
class AudioOutputSelector {
    constructor(mpdClient) {
        this.streamAudio = null;
        this.isInitialized = false;
        this.mpdClient = mpdClient;
        this.config = {
            mode: 'host',
            streamUrl: `http://${process.env.NEXT_PUBLIC_MPD_HOST || 'localhost'}:8000/`,
            volume: 70
        };
    }
    async initialize() {
        try {
            await this.checkAvailableOutputs();
            this.isInitialized = true;
        }
        catch (error) {
            console.error('Failed to initialize audio outputs:', error);
            throw error;
        }
    }
    async checkAvailableOutputs() {
        try {
            const outputs = await this.mpdClient.sendRawCommand('outputs');
        }
        catch (error) {
            console.warn('Could not check MPD outputs:', error);
        }
    }
    async setOutputMode(mode) {
        if (!this.isInitialized) {
            throw new Error('Output selector not initialized');
        }
        this.config.mode = mode;
        switch (mode) {
            case 'host':
                await this.enableHostPlayback();
                await this.disableStreamPlayback();
                break;
            case 'stream':
                await this.disableHostPlayback();
                await this.enableStreamPlayback();
                break;
            case 'both':
                await this.enableHostPlayback();
                await this.enableStreamPlayback();
                break;
        }
    }
    async enableHostPlayback() {
        try {
            await this.mpdClient.sendRawCommand('enableoutput 0');
            console.log('✅ Host audio output enabled');
        }
        catch (error) {
            console.warn('Could not enable host output:', error);
        }
    }
    async disableHostPlayback() {
        try {
            await this.mpdClient.sendRawCommand('disableoutput 0');
            console.log('🔇 Host audio output disabled');
        }
        catch (error) {
            console.warn('Could not disable host output:', error);
        }
    }
    async enableStreamPlayback() {
        try {
            await this.mpdClient.sendRawCommand('enableoutput 1');
            if (typeof window !== 'undefined') {
                this.streamAudio = new Audio(this.config.streamUrl);
                this.streamAudio.crossOrigin = 'anonymous';
                this.streamAudio.volume = this.config.volume / 100;
                this.streamAudio.addEventListener('loadstart', () => {
                    console.log('📡 Stream loading...');
                });
                this.streamAudio.addEventListener('canplay', () => {
                    console.log('✅ Stream ready');
                });
                this.streamAudio.addEventListener('error', (e) => {
                    console.error('❌ Stream error:', e);
                });
                this.streamAudio.load();
            }
            console.log('📡 Client streaming enabled');
        }
        catch (error) {
            console.warn('Could not enable streaming:', error);
        }
    }
    async disableStreamPlayback() {
        try {
            await this.mpdClient.sendRawCommand('disableoutput 1');
            if (this.streamAudio) {
                this.streamAudio.pause();
                this.streamAudio.remove();
                this.streamAudio = null;
            }
            console.log('🔇 Client streaming disabled');
        }
        catch (error) {
            console.warn('Could not disable streaming:', error);
        }
    }
    async startClientStream() {
        if (this.streamAudio && (this.config.mode === 'stream' || this.config.mode === 'both')) {
            try {
                await this.streamAudio.play();
                console.log('▶️ Client stream started');
            }
            catch (error) {
                console.error('Failed to start client stream:', error);
            }
        }
    }
    stopClientStream() {
        if (this.streamAudio) {
            this.streamAudio.pause();
            console.log('⏸️ Client stream stopped');
        }
    }
    setClientVolume(volume) {
        this.config.volume = Math.max(0, Math.min(100, volume));
        if (this.streamAudio) {
            this.streamAudio.volume = this.config.volume / 100;
            console.log(`[AudioOutputSelector] Client volume set to ${volume}% (HTML Audio: ${this.streamAudio.volume})`);
        }
        else {
            console.log(`[AudioOutputSelector] No stream audio element available`);
        }
    }
    setClientMuted(muted) {
        if (this.streamAudio) {
            this.streamAudio.muted = muted;
            console.log(`[AudioOutputSelector] Client ${muted ? 'muted' : 'unmuted'}`);
        }
    }
    getClientVolume() {
        return this.config.volume;
    }
    isClientMuted() {
        return this.streamAudio ? this.streamAudio.muted : false;
    }
    getConfig() {
        return { ...this.config };
    }
    getStreamStatus() {
        return {
            available: this.streamAudio !== null,
            playing: this.streamAudio ? !this.streamAudio.paused : false,
            connected: this.streamAudio ? this.streamAudio.readyState >= 2 : false
        };
    }
}
exports.AudioOutputSelector = AudioOutputSelector;
//# sourceMappingURL=audio-output-selector.js.map