"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSocketInstance = getSocketInstance;
exports.disconnectSocket = disconnectSocket;
let socketInstance = null;
let socketPromise = null;
async function getSocketInstance() {
    if (socketInstance && socketInstance.connected) {
        return socketInstance;
    }
    if (socketPromise) {
        return socketPromise;
    }
    socketPromise = createSocket();
    const socket = await socketPromise;
    socketPromise = null;
    return socket;
}
async function createSocket() {
    if (typeof window === 'undefined') {
        return null;
    }
    try {
        const socketIOClient = await Promise.resolve().then(() => __importStar(require('socket.io-client')));
        const io = socketIOClient.io || socketIOClient.default?.io;
        if (!io) {
            console.error('Failed to import Socket.IO');
            return null;
        }
        const currentHost = window.location.hostname;
        const configuredHost = process.env.NEXT_PUBLIC_SOCKET_HOST || '************';
        const port = process.env.NEXT_PUBLIC_WEBSOCKET_PORT || '3001';
        const socketHost = configuredHost !== 'localhost' ? configuredHost : currentHost;
        const SOCKET_URL = `http://${socketHost}:${port}`;
        console.log('🔌 Creating singleton socket connection to:', SOCKET_URL);
        socketInstance = io(SOCKET_URL, {
            transports: ['polling', 'websocket'],
            reconnectionAttempts: 3,
            timeout: 15000,
            forceNew: false,
            multiplex: true,
        });
        socketInstance.on('connect', () => {
            console.log('🔌 Singleton socket connected');
        });
        socketInstance.on('disconnect', (reason) => {
            console.log('🔌 Singleton socket disconnected:', reason);
        });
        socketInstance.on('error', (error) => {
            console.error('🔌 Singleton socket error:', error);
        });
        return socketInstance;
    }
    catch (error) {
        console.error('🔌 Failed to create socket:', error);
        return null;
    }
}
function disconnectSocket() {
    if (socketInstance) {
        socketInstance.disconnect();
        socketInstance = null;
    }
}
//# sourceMappingURL=socket-singleton.js.map