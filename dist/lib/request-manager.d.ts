declare class RequestManagerClass {
    private pendingRequests;
    private requestCounts;
    private readonly MAX_CONCURRENT_PER_ENDPOINT;
    private readonly RATE_LIMIT_WINDOW;
    private readonly MAX_REQUESTS_PER_WINDOW;
    fetch(url: string, options?: RequestInit): Promise<Response>;
    private cleanOldCounts;
    clear(): void;
}
export declare const requestManager: RequestManagerClass;
export {};
