import { Team, TeamGameMode, TeamGameSettings, TeamAnswer } from './types';
export interface TeamGameState {
    currentMode: TeamGameMode;
    currentTeamTurn?: string;
    currentSpecialty?: string;
    collaborationPhase: boolean;
    relaySequence: string[];
    specialistAssignments: Map<string, string>;
    teamReadyStates: Map<string, boolean>;
}
export interface TeamTurnInfo {
    teamId: string;
    activePlayerId?: string;
    timeRemaining: number;
    canAnswer: boolean;
    waitingFor: string[];
}
export declare class TeamGameModeManager {
    private gameStates;
    initializeTeamGame(gameId: string, teams: Team[], settings: TeamGameSettings): TeamGameState;
    private initializeCollaborativeMode;
    private initializeRelayMode;
    private initializeSpecialistMode;
    getCurrentTurnInfo(gameId: string, questionCategory?: string): TeamTurnInfo[];
    handleTeamAnswer(gameId: string, teamAnswer: TeamAnswer, teams: Team[]): {
        accepted: boolean;
        reason: string;
        nextTurn?: string;
        phaseComplete: boolean;
    };
    private handleCollaborativeAnswer;
    private handleRelayAnswer;
    private handleSpecialistAnswer;
    startCollaborationPhase(gameId: string, duration: number): boolean;
    getSpecialistAssignments(gameId: string): Map<string, string>;
    getRelaySequence(gameId: string): string[];
    canPlayerAnswer(gameId: string, playerId: string, questionCategory?: string): {
        canAnswer: boolean;
        reason: string;
    };
    resetForNewQuestion(gameId: string): void;
    getModeDescription(mode: TeamGameMode): {
        name: string;
        description: string;
        icon: string;
        features: string[];
    };
    cleanupGame(gameId: string): void;
}
export declare const teamGameModeManager: TeamGameModeManager;
