import React, { ReactNode } from 'react';
import type { UserProfile } from '@/components/onboarding-flow';
import type { Playlist, PlaylistTrack } from '@/components/playlist-manager';
export type UserRole = 'user' | 'dj' | 'superuser';
interface User extends UserProfile {
    id: string;
    role: UserRole;
    level: number;
    xp: number;
    xpToNext: number;
    joinDate: string;
    stats: {
        totalGames: number;
        winRate: number;
        averageScore: number;
        bestStreak: number;
        hoursPlayed: number;
        achievements: number;
        rank: number;
        favoriteMode: string;
    };
    achievements: Achievement[];
    playlists: Playlist[];
    settings: {
        emailNotifications: boolean;
        soundEffects: boolean;
        autoPlay: boolean;
        showHints: boolean;
        publicProfile: boolean;
        shareStats: boolean;
    };
}
interface Achievement {
    id: string;
    name: string;
    description: string;
    icon: string;
    rarity: 'Common' | 'Rare' | 'Epic' | 'Legendary';
    earned: boolean;
    earnedAt?: Date;
    progress?: number;
    maxProgress?: number;
}
interface UserContextType {
    user: User | null;
    isAuthenticated: boolean;
    isFirstTime: boolean;
    isLoading: boolean;
    login: (email: string, password: string) => Promise<boolean>;
    register: (userData: Partial<UserProfile>) => Promise<boolean>;
    logout: () => Promise<void>;
    updateProfile: (updates: Partial<UserProfile>) => Promise<boolean>;
    completeOnboarding: (profile: UserProfile) => Promise<boolean>;
    skipOnboarding: () => Promise<void>;
    resetOnboarding: () => void;
    addToPlaylist: (playlistId: string, track: PlaylistTrack) => Promise<boolean>;
    createPlaylist: (playlist: Omit<Playlist, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>) => Promise<boolean>;
    updateXP: (points: number) => void;
    unlockAchievement: (achievementId: string) => void;
    favoriteTrack: (track: PlaylistTrack) => Promise<boolean>;
    isAdmin: () => boolean;
    isDJ: () => boolean;
    isDJOrAdmin: () => boolean;
    hasRole: (role: UserRole) => boolean;
    refreshUser: () => Promise<void>;
    lastRoleCheck: Date | null;
}
interface UserProviderProps {
    children: ReactNode;
}
export declare function UserProvider({ children }: UserProviderProps): React.JSX.Element;
export declare function useUser(): UserContextType;
export {};
