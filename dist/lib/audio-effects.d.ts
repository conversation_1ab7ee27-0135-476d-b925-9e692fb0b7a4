export interface AudioEffect {
    id: string;
    name: string;
    description: string;
    difficulty: number;
    apply: (audioContext: AudioContext, source: AudioBufferSourceNode) => AudioNode;
}
export interface AudioEffectConfig {
    backwards?: boolean;
    speed?: number;
    pitch?: number;
    lowPass?: number;
    highPass?: number;
    reverb?: number;
    echo?: {
        delay: number;
        feedback: number;
    };
}
export declare class AudioEffectsProcessor {
    private audioContext;
    private effects;
    constructor();
    private initializeEffects;
    getAvailableEffects(): AudioEffect[];
    getRandomEffect(): AudioEffect;
    getRandomEffectCombination(count?: number): AudioEffect[];
    applyEffects(audioBuffer: AudioBuffer, effects: AudioEffect[]): Promise<AudioBuffer>;
    processAudioForBackwards(audioBuffer: AudioBuffer): Promise<AudioBuffer>;
    static getEffectDifficultyLevel(effects: AudioEffect[]): number;
    static getEffectDescription(effects: AudioEffect[]): string;
    cleanup(): void;
}
export declare const AUDIO_TRICKS_PRESETS: {
    level1: string[][];
    level2: string[][];
    level3: string[][];
    level4: string[][];
    level5: string[][];
};
