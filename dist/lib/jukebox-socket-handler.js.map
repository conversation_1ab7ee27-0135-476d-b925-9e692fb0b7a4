{"version": 3, "file": "jukebox-socket-handler.js", "sourceRoot": "", "sources": ["../../lib/jukebox-socket-handler.ts"], "names": [], "mappings": ";;;AACA,2DAAqD;AACrD,6CAAwC;AACxC,+BAAsC;AACtC,iDAA4C;AAC5C,yDAAmD;AAQnD,MAAa,oBAAoB;IAO/B,YAAY,EAAkB;QAHtB,0BAAqB,GAAwB,IAAI,CAAA;QACjD,iBAAY,GAAyD,IAAI,CAAA;QAG/E,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,eAAe,GAAG,IAAI,mCAAe,CAAC,EAAE,CAAC,CAAA;QAE9C,MAAM,MAAM,GAAG,IAAA,oBAAc,GAAE,CAAA;QAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,sBAAS,CAAC;YAC7B,IAAI,EAAE,MAAM,CAAC,OAAO;YACpB,IAAI,EAAE,MAAM,CAAC,OAAO;YACpB,QAAQ,EAAE,MAAM,CAAC,WAAW;YAC5B,aAAa,EAAE,MAAM,CAAC,WAAW;SAClC,CAAC,CAAA;QAEF,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC3B,CAAC;IAEO,cAAc;QACpB,MAAM,gBAAgB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,CAAA;QAE/C,gBAAgB,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAc,EAAE,EAAE;YACnD,OAAO,CAAC,GAAG,CAAC,gCAAgC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;YAGxD,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,IAAwD,EAAE,EAAE;gBAC3F,IAAI,CAAC;oBAEH,IAAI,QAAQ,GAAG,IAAI,CAAA;oBAEnB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;wBACf,QAAQ,GAAG,MAAM,0BAAW,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBACtD,CAAC;yBAAM,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;wBAEvB,QAAQ,GAAG;4BACT,EAAE,EAAE,IAAI,CAAC,MAAM;4BACf,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,MAAM;yBAC1B,CAAA;oBACH,CAAC;oBAED,IAAI,QAAQ,EAAE,CAAC;wBACb,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAA;wBAChC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,EAAE,CAAA;wBACvD,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;wBAGhC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;wBAGxD,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;wBAEpC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAA;oBACjE,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAA;oBAC1E,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAA;oBAC7C,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAA;gBAClF,CAAC;YACH,CAAC,CAAC,CAAA;YAGF,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,KAAK,EAAE,IAA4D,EAAE,EAAE;gBAC5F,IAAI,CAAC;oBACH,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;oBAExC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;wBACxB,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAA;wBAChE,OAAM;oBACR,CAAC;oBAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC/C,MAAM,CAAC,IAAI,CAAC,MAAM,EAClB,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,WAAW,CACpC,CAAA;oBAED,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE;wBAC9C,KAAK,EAAE,MAAM,CAAC,UAAU;wBACxB,MAAM,EAAE,MAAM,CAAC,WAAW;wBAC1B,UAAU,EAAE,MAAM,CAAC,UAAU;qBAC9B,CAAC,CAAA;gBAGJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAA;oBACxC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAA;gBAC9D,CAAC;YACH,CAAC,CAAC,CAAA;YAGF,MAAM,CAAC,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;gBACrC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAA;gBACnD,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAA;YACzC,CAAC,CAAC,CAAA;YAGF,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC3B,OAAO,CAAC,GAAG,CAAC,mCAAmC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;gBAC3D,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACvB,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;gBAClE,CAAC;YACH,CAAC,CAAC,CAAA;YAGF,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,KAAK,EAAE,IAAsC,EAAE,EAAE;gBACxE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACzE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAA;oBAC7D,OAAM;gBACR,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;oBAErD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;gBAC9B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAA;oBAC1C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,qBAAqB,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;gBACvE,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,MAAY;QACzD,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAA;QAE9B,IAAI,CAAC;YACH,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,MAAM;oBACT,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;oBAC3B,MAAK;gBACP,KAAK,OAAO;oBACV,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;oBAC5B,MAAK;gBACP,KAAK,MAAM;oBACT,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAA;oBAChC,MAAK;gBACP,KAAK,UAAU;oBACb,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAA;oBACpC,MAAK;gBACP,KAAK,QAAQ;oBACX,IAAI,MAAM,EAAE,MAAM,KAAK,SAAS,EAAE,CAAC;wBACjC,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;oBAC/C,CAAC;oBACD,MAAK;gBACP,KAAK,MAAM;oBACT,IAAI,MAAM,EAAE,QAAQ,KAAK,SAAS,EAAE,CAAC;wBACnC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;oBAC5C,CAAC;oBACD,MAAK;gBACP;oBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAA;YAChD,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAA;QACnC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAE9B,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAClD,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;YAC9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAA;YAC/C,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAA;IACV,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,iCAAc,CAAC,WAAW,EAAE,CAAA;YAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,CAAA;YAE1B,IAAI,MAAM,EAAE,WAAW,EAAE,KAAK,CAAA;YAE9B,IAAI,MAAM,EAAE,CAAC;gBAEX,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;gBACtB,WAAW,GAAG,MAAM,CAAC,WAAW,CAAA;gBAGhC,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAA;gBAC9B,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAA;gBACjD,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAA;YACnC,CAAC;iBAAM,CAAC;gBAEN,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAA;gBAE9B,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAA;gBACzC,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAA;gBACnD,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAA;gBAEjD,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAA;gBAGjC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;YAChC,CAAC;YAGD,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,OAAO,GAAG,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,WAAW,CAAC,IAAI,CAAA;gBAC9D,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK,OAAO,EAAE,CAAC;oBAC3D,IAAI,CAAC,YAAY,GAAG;wBAClB,EAAE,EAAE,OAAO;wBACX,KAAK,EAAE,WAAW,CAAC,KAAK,IAAI,eAAe;wBAC3C,MAAM,EAAE,WAAW,CAAC,MAAM,IAAI,gBAAgB;qBAC/C,CAAA;oBAGD,IAAI,CAAC,eAAe,CAAC,eAAe,CAClC,OAAO,EACP,IAAI,CAAC,YAAY,CAAC,KAAK,EACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CACzB,CAAA;gBACH,CAAC;YACH,CAAC;YAGD,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE;gBACxC,MAAM;gBACN,WAAW;gBACX,KAAK;gBACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;QACzD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAAc;QAC5C,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,iCAAc,CAAC,WAAW,EAAE,CAAA;YAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,CAAA;YAE1B,IAAI,MAAM,EAAE,WAAW,EAAE,KAAK,CAAA;YAE9B,IAAI,MAAM,EAAE,CAAC;gBAEX,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;gBACtB,WAAW,GAAG,MAAM,CAAC,WAAW,CAAA;gBAGhC,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAA;gBAC9B,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAA;gBACjD,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAA;YACnC,CAAC;iBAAM,CAAC;gBAEN,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAA;gBAE9B,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAA;gBACzC,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAA;gBACnD,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAA;gBAEjD,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAA;gBAGjC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;YAChC,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE;gBACxB,MAAM;gBACN,WAAW;gBACX,KAAK;gBACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAA;YAGF,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAA;YAC3D,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAA;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;YACtD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC,CAAA;QACnE,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;YACzC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA;QACnC,CAAC;IACH,CAAC;CACF;AAhSD,oDAgSC"}