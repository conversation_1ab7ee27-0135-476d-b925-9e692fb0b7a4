"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketTestClient = exports.MusicQuizAutomation = void 0;
exports.runBatchTests = runBatchTests;
const test_1 = require("@playwright/test");
const socket_io_client_1 = require("socket.io-client");
class MusicQuizAutomation {
    constructor(config = {}) {
        this.browser = null;
        this.context = null;
        this.config = {
            headless: true,
            timeout: 30000,
            viewport: { width: 1280, height: 720 },
            ...config
        };
    }
    async initialize() {
        this.browser = await test_1.chromium.launch({
            headless: this.config.headless
        });
        this.context = await this.browser.newContext({
            viewport: this.config.viewport
        });
    }
    async close() {
        if (this.context)
            await this.context.close();
        if (this.browser)
            await this.browser.close();
    }
    async createPage() {
        if (!this.context)
            throw new Error('Browser not initialized');
        return await this.context.newPage();
    }
    async scrapeAlbumArt(artist, album) {
        const page = await this.createPage();
        const images = [];
        try {
            const sources = [
                `https://www.google.com/search?q=${encodeURIComponent(artist + ' ' + album + ' album cover')}&tbm=isch`,
                `https://musicbrainz.org/search?query=${encodeURIComponent(artist + ' ' + album)}&type=release`
            ];
            for (const url of sources) {
                await page.goto(url, { waitUntil: 'networkidle' });
                const imgUrls = await page.evaluate(() => {
                    const imgs = Array.from(document.querySelectorAll('img'));
                    return imgs
                        .map(img => img.src)
                        .filter(src => src && !src.includes('data:image'))
                        .slice(0, 5);
                });
                images.push(...imgUrls);
            }
            return [...new Set(images)];
        }
        finally {
            await page.close();
        }
    }
    async simulateMultiplayerGame(config) {
        const players = [];
        for (let i = 0; i < config.playerCount; i++) {
            const page = await this.createPage();
            await page.goto(config.gameUrl);
            await page.fill('[data-testid="player-name"]', `Player${i + 1}`);
            if (config.roomCode) {
                await page.fill('[data-testid="room-code"]', config.roomCode);
                await page.click('[data-testid="join-room"]');
            }
            else if (i === 0) {
                await page.click('[data-testid="create-room"]');
                const roomCode = await page.textContent('[data-testid="room-code-display"]');
                config.roomCode = roomCode || undefined;
            }
            players.push(page);
        }
        return players;
    }
    async monitorWebSocket(page) {
        const messages = [];
        let connections = 0;
        await page.evaluateOnNewDocument(() => {
            window.__wsMessages = [];
            window.__wsConnections = 0;
            const originalWebSocket = window.WebSocket;
            window.WebSocket = new Proxy(originalWebSocket, {
                construct(target, args) {
                    const ws = new target(...args);
                    window.__wsConnections++;
                    const originalSend = ws.send.bind(ws);
                    ws.send = function (data) {
                        window.__wsMessages.push({
                            type: 'sent',
                            data,
                            timestamp: Date.now()
                        });
                        return originalSend(data);
                    };
                    ws.addEventListener('message', (event) => {
                        window.__wsMessages.push({
                            type: 'received',
                            data: event.data,
                            timestamp: Date.now()
                        });
                    });
                    return ws;
                }
            });
        });
        const interval = setInterval(async () => {
            const data = await page.evaluate(() => ({
                messages: window.__wsMessages || [],
                connections: window.__wsConnections || 0
            }));
            messages.push(...data.messages);
            connections = data.connections;
        }, 1000);
        setTimeout(() => clearInterval(interval), 30000);
        return { messages, connections };
    }
    async captureGameState(page, stateName) {
        const screenshotPath = `./screenshots/game-state-${stateName}-${Date.now()}.png`;
        return await page.screenshot({
            path: screenshotPath,
            fullPage: true
        });
    }
    async measureGamePerformance(page) {
        const metrics = await page.evaluate(() => {
            return new Promise((resolve) => {
                let fps = 0;
                let frames = 0;
                let lastTime = performance.now();
                const measureFPS = () => {
                    frames++;
                    const currentTime = performance.now();
                    if (currentTime >= lastTime + 1000) {
                        fps = Math.round((frames * 1000) / (currentTime - lastTime));
                        frames = 0;
                        lastTime = currentTime;
                    }
                    requestAnimationFrame(measureFPS);
                };
                measureFPS();
                setTimeout(async () => {
                    const memory = performance.memory;
                    const memoryUsage = memory ? memory.usedJSHeapSize / 1048576 : 0;
                    resolve({
                        fps,
                        memoryUsage,
                        cpuUsage: 0
                    });
                }, 5000);
            });
        });
        return metrics;
    }
    async verifyQuizQuestion(page, expectedQuestion) {
        try {
            const questionText = await page.textContent('[data-testid="question-text"]');
            if (questionText !== expectedQuestion.text)
                return false;
            if (expectedQuestion.options) {
                const options = await page.$$eval('[data-testid="answer-option"]', els => els.map(el => el.textContent?.trim() || ''));
                if (JSON.stringify(options.sort()) !== JSON.stringify(expectedQuestion.options.sort())) {
                    return false;
                }
            }
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.MusicQuizAutomation = MusicQuizAutomation;
class SocketTestClient {
    constructor() {
        this.socket = null;
        this.messages = [];
    }
    async connect(url, options) {
        this.socket = (0, socket_io_client_1.io)(url, options);
        this.socket.on('connect', () => {
            this.messages.push({ event: 'connect', timestamp: Date.now() });
        });
        this.socket.onAny((event, ...args) => {
            this.messages.push({ event, data: args, timestamp: Date.now() });
        });
        await new Promise((resolve) => {
            this.socket.once('connect', resolve);
        });
    }
    async emit(event, data) {
        if (!this.socket)
            throw new Error('Socket not connected');
        this.socket.emit(event, data);
    }
    async waitForEvent(event, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(`Timeout waiting for event: ${event}`));
            }, timeout);
            this.socket.once(event, (data) => {
                clearTimeout(timer);
                resolve(data);
            });
        });
    }
    getMessages() {
        return this.messages;
    }
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
        }
    }
}
exports.SocketTestClient = SocketTestClient;
async function runBatchTests(tests) {
    const results = [];
    let passed = 0;
    let failed = 0;
    for (const { name, test } of tests) {
        try {
            const start = Date.now();
            await test();
            const duration = Date.now() - start;
            results.push({ name, status: 'passed', duration });
            passed++;
        }
        catch (error) {
            results.push({ name, status: 'failed', error: error.message });
            failed++;
        }
    }
    return { passed, failed, results };
}
//# sourceMappingURL=playwright-utils.js.map