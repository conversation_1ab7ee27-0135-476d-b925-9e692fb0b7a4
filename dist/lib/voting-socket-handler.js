"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.votingSocketHandler = exports.VotingSocketHandler = void 0;
const voting_manager_1 = require("./voting-manager");
class VotingSocketHandler {
    constructor() {
        this.gameVotingSessions = new Map();
        this.sessionTimers = new Map();
    }
    handleConnection(socket, playerId, playerName, gameId) {
        socket.on('create-voting', (data) => {
            try {
                const { type, title, description, options, timeLimit = 30 } = data;
                const sessionId = `voting_${gameId}_${Date.now()}`;
                const totalPlayers = 4;
                const session = voting_manager_1.votingManager.createVotingSession(sessionId, type, title, description, options, timeLimit, totalPlayers, playerId);
                this.gameVotingSessions.set(gameId, sessionId);
                socket.to(gameId).emit('voting-session-created', { session });
                socket.emit('voting-session-created', { session });
                this.startTimeUpdateTimer(sessionId, gameId, socket);
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to create voting session' });
            }
        });
        socket.on('submit-vote', (data) => {
            try {
                const { sessionId, optionIndex } = data;
                const success = voting_manager_1.votingManager.submitVote(sessionId, playerId, playerName, optionIndex);
                if (success) {
                    const session = voting_manager_1.votingManager.getVotingSession(sessionId);
                    if (session) {
                        socket.to(gameId).emit('vote-submitted', {
                            sessionId,
                            playerId,
                            optionIndex,
                            totalVotes: session.votes.length
                        });
                        socket.emit('vote-submitted', {
                            sessionId,
                            playerId,
                            optionIndex,
                            totalVotes: session.votes.length
                        });
                        socket.to(gameId).emit('voting-session-updated', { session });
                        socket.emit('voting-session-updated', { session });
                        this.broadcastVotingStats(sessionId, gameId, socket);
                        if (session.status === 'completed' && session.result) {
                            socket.to(gameId).emit('voting-completed', {
                                sessionId,
                                result: session.result
                            });
                            socket.emit('voting-completed', {
                                sessionId,
                                result: session.result
                            });
                            this.clearTimer(sessionId);
                        }
                    }
                }
                else {
                    socket.emit('error', { message: 'Failed to submit vote' });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to submit vote' });
            }
        });
        socket.on('skip-voting', (data) => {
            try {
                const { sessionId } = data;
                const result = voting_manager_1.votingManager.skipVoting(sessionId, playerId);
                if (result) {
                    socket.to(gameId).emit('voting-completed', { sessionId, result });
                    socket.emit('voting-completed', { sessionId, result });
                    this.clearTimer(sessionId);
                }
                else {
                    socket.emit('error', { message: 'Cannot skip voting' });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to skip voting' });
            }
        });
        socket.on('get-voting-session', (data) => {
            try {
                const { sessionId } = data;
                const session = voting_manager_1.votingManager.getVotingSession(sessionId);
                if (session) {
                    socket.emit('voting-session-updated', { session });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to get voting session' });
            }
        });
    }
    handleDisconnection(playerId, gameId) {
    }
    startAutomaticVoting(gameId, type, title, description, options, totalPlayers, timeLimit = 30, socket) {
        const sessionId = `auto_voting_${gameId}_${Date.now()}`;
        const session = voting_manager_1.votingManager.createVotingSession(sessionId, type, title, description, options, timeLimit, totalPlayers);
        this.gameVotingSessions.set(gameId, sessionId);
        socket.to(gameId).emit('voting-session-created', { session });
        socket.emit('voting-session-created', { session });
        this.startTimeUpdateTimer(sessionId, gameId, socket);
        return sessionId;
    }
    getCurrentVotingSession(gameId) {
        const sessionId = this.gameVotingSessions.get(gameId);
        if (!sessionId)
            return null;
        return voting_manager_1.votingManager.getVotingSession(sessionId);
    }
    hasPlayerVoted(gameId, playerId) {
        const sessionId = this.gameVotingSessions.get(gameId);
        if (!sessionId)
            return false;
        return voting_manager_1.votingManager.hasPlayerVoted(sessionId, playerId);
    }
    getPlayerVote(gameId, playerId) {
        const sessionId = this.gameVotingSessions.get(gameId);
        if (!sessionId)
            return null;
        return voting_manager_1.votingManager.getPlayerVote(sessionId, playerId);
    }
    startTimeUpdateTimer(sessionId, gameId, socket) {
        const timer = setInterval(() => {
            const timeRemaining = voting_manager_1.votingManager.getTimeRemaining(sessionId);
            if (timeRemaining <= 0) {
                const result = voting_manager_1.votingManager.completeVoting(sessionId);
                if (result) {
                    socket.to(gameId).emit('voting-completed', { sessionId, result });
                    socket.emit('voting-completed', { sessionId, result });
                }
                clearInterval(timer);
                this.sessionTimers.delete(sessionId);
            }
            else {
                socket.to(gameId).emit('voting-time-update', { sessionId, timeRemaining });
                socket.emit('voting-time-update', { sessionId, timeRemaining });
                this.broadcastVotingStats(sessionId, gameId, socket);
            }
        }, 1000);
        this.sessionTimers.set(sessionId, timer);
    }
    clearTimer(sessionId) {
        const timer = this.sessionTimers.get(sessionId);
        if (timer) {
            clearInterval(timer);
            this.sessionTimers.delete(sessionId);
        }
    }
    broadcastVotingStats(sessionId, gameId, socket) {
        const stats = voting_manager_1.votingManager.getVotingStats(sessionId);
        if (stats) {
            const participationRate = voting_manager_1.votingManager.getParticipationRate(sessionId);
            socket.to(gameId).emit('voting-stats', {
                sessionId,
                totalVotes: stats.totalVotes,
                votePercentages: stats.votePercentages,
                participationRate
            });
            socket.emit('voting-stats', {
                sessionId,
                totalVotes: stats.totalVotes,
                votePercentages: stats.votePercentages,
                participationRate
            });
        }
    }
    cleanupGame(gameId) {
        const sessionId = this.gameVotingSessions.get(gameId);
        if (sessionId) {
            this.clearTimer(sessionId);
            voting_manager_1.votingManager.removeSession(sessionId);
            this.gameVotingSessions.delete(gameId);
        }
    }
    createCategoryVoting(gameId, totalPlayers, socket) {
        return this.startAutomaticVoting(gameId, 'category', 'Choose Next Category', 'Vote for the music category for the next round', [
            { label: 'Rock & Metal', value: 'rock', description: 'Hard rock, metal, punk', emoji: '🎸' },
            { label: 'Pop & Dance', value: 'pop', description: 'Pop hits, dance, electronic', emoji: '🎵' },
            { label: 'Hip Hop & R&B', value: 'hiphop', description: 'Rap, hip hop, R&B', emoji: '🎤' },
            { label: 'Classic & Jazz', value: 'classic', description: 'Classical, jazz, blues', emoji: '🎼' }
        ], totalPlayers, 30, socket);
    }
    createDecadeVoting(gameId, totalPlayers, socket) {
        return this.startAutomaticVoting(gameId, 'decade', 'Choose Time Period', 'Vote for the era of music for the next round', [
            { label: '80s Hits', value: '80s', description: 'Music from the 1980s', emoji: '📻' },
            { label: '90s Classics', value: '90s', description: 'Music from the 1990s', emoji: '💿' },
            { label: '2000s Pop', value: '2000s', description: 'Music from the 2000s', emoji: '💽' },
            { label: '2010s & Beyond', value: '2010s', description: 'Modern hits', emoji: '📱' }
        ], totalPlayers, 30, socket);
    }
    createGameModeVoting(gameId, totalPlayers, socket) {
        return this.startAutomaticVoting(gameId, 'game-mode', 'Choose Game Mode', 'Vote for the game mode for the next round', [
            { label: 'Classic Quiz', value: 'classic', description: 'Traditional music quiz', emoji: '🎯' },
            { label: 'Speed Round', value: 'speed', description: 'Fast-paced questions', emoji: '⚡' },
            { label: 'Challenge Mode', value: 'challenge', description: 'Harder questions', emoji: '🏆' },
            { label: 'Guess the Year', value: 'year', description: 'Identify release years', emoji: '📅' }
        ], totalPlayers, 30, socket);
    }
}
exports.VotingSocketHandler = VotingSocketHandler;
exports.votingSocketHandler = new VotingSocketHandler();
//# sourceMappingURL=voting-socket-handler.js.map