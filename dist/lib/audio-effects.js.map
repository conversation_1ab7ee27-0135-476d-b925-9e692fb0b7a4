{"version": 3, "file": "audio-effects.js", "sourceRoot": "", "sources": ["../../lib/audio-effects.ts"], "names": [], "mappings": ";;;AAuBA,MAAa,qBAAqB;IAIhC;QAFQ,YAAO,GAA6B,IAAI,GAAG,EAAE,CAAA;QAGnD,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAA;QACtC,IAAI,CAAC,iBAAiB,EAAE,CAAA;IAC1B,CAAC;IAEO,iBAAiB;QAEvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;YAC5B,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,yBAAyB;YACtC,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBAGrB,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,EAAE,CAAA;gBACjC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;gBACxB,OAAO,QAAQ,CAAA;YACjB,CAAC;SACF,CAAC,CAAA;QAGF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;YAC3B,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,2BAA2B;YACxC,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACrB,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,EAAE,CAAA;gBACjC,MAAM,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAA;gBACrD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;gBACxB,OAAO,QAAQ,CAAA;YACjB,CAAC;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;YAC5B,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,8BAA8B;YAC3C,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACrB,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,EAAE,CAAA;gBACjC,MAAM,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAA;gBACtD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;gBACxB,OAAO,QAAQ,CAAA;YACjB,CAAC;SACF,CAAC,CAAA;QAGF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;YAC3B,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,uCAAuC;YACpD,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACrB,MAAM,MAAM,GAAG,GAAG,CAAC,kBAAkB,EAAE,CAAA;gBACvC,MAAM,CAAC,IAAI,GAAG,SAAS,CAAA;gBACvB,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAA;gBACnD,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAA;gBAElB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;gBACtB,OAAO,MAAM,CAAA;YACf,CAAC;SACF,CAAC,CAAA;QAGF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;YAC5B,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,uCAAuC;YACpD,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACrB,MAAM,MAAM,GAAG,GAAG,CAAC,kBAAkB,EAAE,CAAA;gBACvC,MAAM,CAAC,IAAI,GAAG,UAAU,CAAA;gBACxB,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAA;gBACpD,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAA;gBAElB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;gBACtB,OAAO,MAAM,CAAA;YACf,CAAC;SACF,CAAC,CAAA;QAGF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE;YAC9B,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE,uBAAuB;YACpC,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACrB,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,EAAE,CAAA;gBAEjC,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;gBAC3C,MAAM,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,GAAG,EAAE,CAAC,CAAA;gBACvD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;gBACxB,OAAO,QAAQ,CAAA;YACjB,CAAC;SACF,CAAC,CAAA;QAGF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE;YACvB,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,uBAAuB;YACpC,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACrB,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gBAClC,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,EAAE,CAAA;gBACjC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAE,CAAA;gBAChC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAE,CAAA;gBAChC,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU,EAAE,CAAA;gBAG/B,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAA;gBACjD,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAA;gBAC/C,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;gBACxB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;gBAGxB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;gBACvB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;gBACrB,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;gBACvB,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;gBACvB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;gBAEtB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;gBACvB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;gBAEvB,OAAO,MAAM,CAAA;YACf,CAAC;SACF,CAAC,CAAA;QAGF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;YAC7B,EAAE,EAAE,YAAY;YAChB,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,yBAAyB;YACtC,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACrB,MAAM,UAAU,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAA;gBACzC,MAAM,mBAAmB,GAAG,CAAC,MAAc,EAAE,EAAE;oBAC7C,MAAM,OAAO,GAAG,KAAK,CAAA;oBACrB,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAA;oBACvC,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAA;oBAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;wBACjC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,CAAA;wBAC/B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC7E,CAAC;oBACD,OAAO,KAAK,CAAA;gBACd,CAAC,CAAA;gBAED,UAAU,CAAC,KAAK,GAAG,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAA;gBAC/D,UAAU,CAAC,UAAU,GAAG,IAAI,CAAA;gBAE5B,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;gBAC1B,OAAO,UAAU,CAAA;YACnB,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED,mBAAmB;QACjB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;IAC1C,CAAC;IAED,eAAe;QACb,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC1C,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAA;IAC5D,CAAC;IAED,0BAA0B,CAAC,QAAgB,CAAC;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC1C,MAAM,QAAQ,GAAkB,EAAE,CAAA;QAElC,OAAO,QAAQ,CAAC,MAAM,GAAG,KAAK,IAAI,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YACnE,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAA;YAClE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/B,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACvB,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,WAAwB,EACxB,OAAsB;QAGtB,MAAM,cAAc,GAAG,IAAI,mBAAmB,CAC5C,WAAW,CAAC,gBAAgB,EAC5B,WAAW,CAAC,MAAM,EAClB,WAAW,CAAC,UAAU,CACvB,CAAA;QAED,MAAM,MAAM,GAAG,cAAc,CAAC,kBAAkB,EAAE,CAAA;QAClD,MAAM,CAAC,MAAM,GAAG,WAAW,CAAA;QAG3B,IAAI,WAAW,GAAc,MAAM,CAAA;QACnC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,MAAM,CAAC,CAAA;QACpD,CAAC;QAGD,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAG/C,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAEf,IAAI,CAAC;YACH,OAAO,MAAM,cAAc,CAAC,cAAc,EAAE,CAAA;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;YAChD,OAAO,WAAW,CAAA;QACpB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,WAAwB;QAErD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CACnD,WAAW,CAAC,gBAAgB,EAC5B,WAAW,CAAC,MAAM,EAClB,WAAW,CAAC,UAAU,CACvB,CAAA;QAED,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,WAAW,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE,CAAC;YACxE,MAAM,YAAY,GAAG,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;YACxD,MAAM,YAAY,GAAG,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;YAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC7C,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;YAC7D,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAA;IACvB,CAAC;IAGD,MAAM,CAAC,wBAAwB,CAAC,OAAsB;QACpD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAA;QAClC,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAA;QAClG,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;IAClC,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,OAAsB;QAChD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,YAAY,CAAA;QAC7C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,CAAA;QACvD,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;IAClD,CAAC;IAED,OAAO;QACL,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YACzC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;QAC3B,CAAC;IACH,CAAC;CACF;AAnQD,sDAmQC;AAGY,QAAA,oBAAoB,GAAG;IAClC,MAAM,EAAE;QACN,CAAC,UAAU,CAAC;QACZ,CAAC,WAAW,CAAC;QACb,CAAC,UAAU,CAAC;QACZ,CAAC,WAAW,CAAC;KACd;IACD,MAAM,EAAE;QACN,CAAC,MAAM,CAAC;QACR,CAAC,aAAa,CAAC;QACf,CAAC,UAAU,EAAE,UAAU,CAAC;QACxB,CAAC,WAAW,EAAE,WAAW,CAAC;KAC3B;IACD,MAAM,EAAE;QACN,CAAC,WAAW,CAAC;QACb,CAAC,YAAY,CAAC;QACd,CAAC,UAAU,EAAE,MAAM,CAAC;QACpB,CAAC,aAAa,EAAE,UAAU,CAAC;KAC5B;IACD,MAAM,EAAE;QACN,CAAC,WAAW,EAAE,UAAU,CAAC;QACzB,CAAC,UAAU,EAAE,aAAa,CAAC;QAC3B,CAAC,YAAY,EAAE,MAAM,CAAC;QACtB,CAAC,WAAW,EAAE,WAAW,CAAC;KAC3B;IACD,MAAM,EAAE;QACN,CAAC,WAAW,EAAE,aAAa,EAAE,UAAU,CAAC;QACxC,CAAC,YAAY,EAAE,MAAM,EAAE,UAAU,CAAC;QAClC,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC;KACxC;CACF,CAAA"}