export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    FATAL = 4
}
export interface LogEntry {
    timestamp: string;
    level: LogLevel;
    message: string;
    context?: string;
    data?: any;
    error?: Error;
    userId?: string;
    sessionId?: string;
}
export interface LoggerConfig {
    level: LogLevel;
    enableConsole: boolean;
    enableStorage: boolean;
    maxStorageEntries: number;
    context?: string;
}
export declare class Logger {
    private config;
    private storageKey;
    private sessionId;
    constructor(config?: Partial<LoggerConfig>);
    private generateSessionId;
    private shouldLog;
    private formatMessage;
    private log;
    private logToConsole;
    private logToStorage;
    private getStoredLogs;
    debug(message: string, data?: any): void;
    info(message: string, data?: any): void;
    warn(message: string, data?: any): void;
    error(message: string, error?: Error, data?: any): void;
    fatal(message: string, error?: Error, data?: any): void;
    createChild(context: string): Logger;
    getLogs(): LogEntry[];
    clearLogs(): void;
    exportLogs(): string;
}
export declare const logger: Logger;
export declare const audioLogger: Logger;
export declare const databaseLogger: Logger;
export declare const quizLogger: Logger;
export declare const multiplayerLogger: Logger;
export declare const apiLogger: Logger;
export declare class PerformanceLogger {
    private static timers;
    static start(operation: string): void;
    static end(operation: string, data?: any): number;
    static measure<T>(operation: string, fn: () => T): T;
    static measureAsync<T>(operation: string, fn: () => Promise<T>): Promise<T>;
}
export declare function logError(error: Error, context?: string, data?: any): void;
export declare function logUnhandledError(event: ErrorEvent): void;
export declare function logUnhandledRejection(event: PromiseRejectionEvent): void;
