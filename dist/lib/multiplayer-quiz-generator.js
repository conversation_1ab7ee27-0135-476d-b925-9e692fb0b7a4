"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MultiplayerQuizGenerator = void 0;
const client_1 = require("@prisma/client");
class MultiplayerQuizGenerator {
    constructor() {
        this.prisma = new client_1.PrismaClient();
    }
    createTrackObject(track) {
        return {
            id: parseInt(track.id) || 0,
            file: track.mpdFilePath,
            title: track.title,
            artist: track.artist,
            album: track.album || undefined,
            year: track.year || undefined,
            albumArtUrl: track.albumArtUrl || undefined,
            genre: track.genre || undefined,
            duration: track.duration || undefined,
            popularityScore: track.popularityScore || undefined,
            chartPosition: track.chartPosition || undefined,
            releaseDate: track.releaseDate ? track.releaseDate.toISOString().split('T')[0] : undefined,
            triviaFacts: track.triviaFacts ? (typeof track.triviaFacts === 'string' ? JSON.parse(track.triviaFacts) : track.triviaFacts) : undefined,
            interestingFacts: track.interestingFacts ? (typeof track.interestingFacts === 'string' ? JSON.parse(track.interestingFacts) : track.interestingFacts) : undefined,
            calculatedGain: track.calculatedGain,
            lufsVolume: track.lufsVolume
        };
    }
    async generateQuiz(settings) {
        console.log('[MultiplayerQuizGenerator] generateQuiz called with:', {
            gameMode: settings.gameMode,
            totalQuestions: settings.totalQuestions,
            timePerQuestion: settings.timePerQuestion,
            hasFilters: !!settings.filters,
            hasUltimoteConfig: !!settings.ultimoteConfig
        });
        if (settings.ultimoteConfig) {
            console.log('[MultiplayerQuizGenerator] ultimoteConfig:', JSON.stringify(settings.ultimoteConfig, null, 2));
        }
        try {
            const trackCount = await this.prisma.quizTrack.count();
            if (trackCount === 0) {
                console.error('[MultiplayerQuizGenerator] No tracks in database');
                throw new Error('No tracks found in database. Please ensure your music library is imported.');
            }
            return await this.generateDatabaseQuestions(settings);
        }
        catch (error) {
            console.error('[MultiplayerQuizGenerator] Error generating quiz:', error);
            throw error;
        }
    }
    async generateDatabaseQuestions(settings) {
        console.log(`[MultiplayerQuizGenerator] Generating ${settings.totalQuestions} questions for ${settings.gameMode}`);
        console.log('[MultiplayerQuizGenerator] Filters:', settings.filters ? 'Applied' : 'None');
        const where = this.buildWhereClause(settings.filters);
        console.log('[MultiplayerQuizGenerator] Final where clause:', JSON.stringify(where, null, 2));
        const totalMatchingTracks = await this.prisma.quizTrack.count({ where });
        console.log(`[MultiplayerQuizGenerator] Total matching tracks in database: ${totalMatchingTracks}`);
        if (totalMatchingTracks === 0) {
            const totalTracks = await this.prisma.quizTrack.count();
            const tracksWithArtist = await this.prisma.quizTrack.count({ where: { artist: { not: null } } });
            const tracksWithFile = await this.prisma.quizTrack.count({ where: { mpdFilePath: { not: '' } } });
            console.error('[MultiplayerQuizGenerator] No tracks match the filter criteria!');
            console.error(`[MultiplayerQuizGenerator] Debug info:`);
            console.error(`  - Total tracks in DB: ${totalTracks}`);
            console.error(`  - Tracks with artist: ${tracksWithArtist}`);
            console.error(`  - Tracks with file path: ${tracksWithFile}`);
            console.error(`  - Applied filters:`, JSON.stringify(settings.filters, null, 2));
            throw new Error(`No tracks match the current filter criteria. Total tracks: ${totalTracks}, but 0 match your filters.`);
        }
        console.log('[MultiplayerQuizGenerator] Executing findMany with take:', Math.max(200, settings.totalQuestions * 20));
        const allTracks = await this.prisma.quizTrack.findMany({
            take: Math.max(500, settings.totalQuestions * 50),
            orderBy: [
                { popularityScore: 'desc' },
                { artist: 'asc' }
            ],
            where
        }).catch(error => {
            console.error('[MultiplayerQuizGenerator] FindMany query error:', error);
            throw new Error(`Database query failed: ${error.message}`);
        });
        const tracksWithGain = allTracks;
        const tracks = this.shuffleArray(tracksWithGain);
        console.log(`[MultiplayerQuizGenerator] Fetched ${tracks.length} tracks with ${new Set(tracks.map(t => t.artist)).size} unique artists`);
        if (tracks.length < 4) {
            console.error(`[MultiplayerQuizGenerator] Not enough tracks found: ${tracks.length} < 4. Need more tracks in database.`);
            console.error(`[MultiplayerQuizGenerator] Total matching in DB: ${totalMatchingTracks}, but only fetched: ${tracks.length}`);
            throw new Error(`Not enough tracks in database. Found ${tracks.length} but need at least 4.`);
        }
        if (tracks.length < settings.totalQuestions) {
            console.warn(`[MultiplayerQuizGenerator] Limited tracks: ${tracks.length} tracks for ${settings.totalQuestions} questions`);
        }
        const questions = [];
        const usedTrackIds = new Set();
        for (let i = 0; i < settings.totalQuestions; i++) {
            const track = tracks.find(t => !usedTrackIds.has(t.id));
            if (!track) {
                console.error(`[MultiplayerQuizGenerator] No more unused tracks at question ${i + 1}/${settings.totalQuestions}`);
                break;
            }
            usedTrackIds.add(track.id);
            let question;
            switch (settings.gameMode) {
                case 'ultimote':
                    return await this.generateUltimoteQuestionsInternal(settings, tracks);
                case 'classic':
                    let wrongArtists = [];
                    try {
                        const similarArtistsJson = track.similarArtists || '[]';
                        const similarArtists = JSON.parse(similarArtistsJson);
                        if (similarArtists.length > 0) {
                            console.log(`[MultiplayerQuizGenerator] Using similar artists for ${track.artist}: ${similarArtists.join(', ')}`);
                            wrongArtists = this.shuffleArray(similarArtists).slice(0, 3);
                        }
                    }
                    catch (e) {
                        console.warn('[MultiplayerQuizGenerator] Failed to parse similar artists:', e);
                    }
                    if (wrongArtists.length < 3) {
                        const allArtists = tracks
                            .filter(t => t.artist && t.artist !== track.artist && !wrongArtists.includes(t.artist))
                            .map(t => t.artist);
                        const uniqueArtists = Array.from(new Set(allArtists));
                        const additionalArtists = this.shuffleArray(uniqueArtists).slice(0, 3 - wrongArtists.length);
                        wrongArtists = [...wrongArtists, ...additionalArtists];
                    }
                    while (wrongArtists.length < 3) {
                        console.warn(`[MultiplayerQuizGenerator] Only found ${wrongArtists.length} artists for question, using fallback`);
                        const fakeArtist = `Unknown Artist ${wrongArtists.length + 1}`;
                        wrongArtists.push(fakeArtist);
                    }
                    question = {
                        id: `mp_${Date.now()}_${i}`,
                        type: 'multiple-choice',
                        question: `Who is the artist?`,
                        options: this.shuffleArray([track.artist, ...wrongArtists.slice(0, 3)]),
                        correctAnswer: track.artist,
                        category: 'Classic',
                        timeLimit: settings.timePerQuestion || 30,
                        track: this.createTrackObject(track)
                    };
                    break;
                case 'guess_the_year':
                    const correctYear = track.year || 2000;
                    const yearOptions = [
                        correctYear,
                        correctYear - 2,
                        correctYear + 3,
                        correctYear - 5
                    ].sort();
                    question = {
                        id: `mp_${Date.now()}_${i}`,
                        type: 'multiple-choice',
                        question: `What year was this song released?`,
                        options: yearOptions.map(y => y.toString()),
                        correctAnswer: correctYear.toString(),
                        category: 'Guess the Year',
                        timeLimit: settings.timePerQuestion || 30,
                        track: this.createTrackObject(track)
                    };
                    break;
                case 'chart_position':
                    const chartPos = track.chartPosition || Math.floor(Math.random() * 100) + 1;
                    const chartOptions = [
                        chartPos,
                        Math.max(1, chartPos - 10),
                        Math.min(100, chartPos + 15),
                        Math.max(1, chartPos - 25)
                    ].sort((a, b) => a - b);
                    question = {
                        id: `mp_${Date.now()}_${i}`,
                        type: 'multiple-choice',
                        question: `What was the highest chart position of this song?`,
                        options: chartOptions.map(p => `#${p}`),
                        correctAnswer: `#${chartPos}`,
                        category: 'Chart Position',
                        timeLimit: settings.timePerQuestion || 30,
                        track: this.createTrackObject(track)
                    };
                    break;
                case 'genre_specialist':
                    const genres = ['Rock', 'Pop', 'Hip-Hop', 'R&B', 'Electronic', 'Country', 'Jazz', 'Blues', 'Folk', 'Alternative'];
                    const correctGenre = track.genre || genres[Math.floor(Math.random() * genres.length)];
                    const wrongGenres = genres.filter(g => g !== correctGenre);
                    const genreOptions = [correctGenre, ...this.shuffleArray(wrongGenres).slice(0, 3)];
                    question = {
                        id: `mp_${Date.now()}_${i}`,
                        type: 'multiple-choice',
                        question: `What genre is this song?`,
                        options: this.shuffleArray(genreOptions),
                        correctAnswer: correctGenre,
                        category: 'Genre Specialist',
                        timeLimit: settings.timePerQuestion || 30,
                        track: this.createTrackObject(track)
                    };
                    break;
                case 'decade_challenge':
                    const year = track.year || 2000;
                    const correctDecade = Math.floor(year / 10) * 10;
                    const decades = ['1960s', '1970s', '1980s', '1990s', '2000s', '2010s', '2020s'];
                    const correctDecadeStr = `${correctDecade}s`;
                    const wrongDecades = decades.filter(d => d !== correctDecadeStr);
                    const decadeOptions = [correctDecadeStr, ...this.shuffleArray(wrongDecades).slice(0, 3)];
                    question = {
                        id: `mp_${Date.now()}_${i}`,
                        type: 'multiple-choice',
                        question: `What decade is this song from?`,
                        options: this.shuffleArray(decadeOptions),
                        correctAnswer: correctDecadeStr,
                        category: 'Decade Challenge',
                        timeLimit: settings.timePerQuestion || 30,
                        track: this.createTrackObject(track)
                    };
                    break;
                case 'audio_manipulation':
                    let audioWrongArtists = [];
                    try {
                        const similarArtistsJson = track.similarArtists || '[]';
                        const similarArtists = JSON.parse(similarArtistsJson);
                        if (similarArtists.length > 0) {
                            audioWrongArtists = this.shuffleArray(similarArtists).slice(0, 3);
                        }
                    }
                    catch (e) {
                        console.warn('[MultiplayerQuizGenerator] Failed to parse similar artists:', e);
                    }
                    if (audioWrongArtists.length < 3) {
                        const audioAllArtists = tracks
                            .filter(t => t.artist && t.artist !== track.artist && !audioWrongArtists.includes(t.artist))
                            .map(t => t.artist);
                        const audioUniqueArtists = Array.from(new Set(audioAllArtists));
                        const additionalArtists = this.shuffleArray(audioUniqueArtists).slice(0, 3 - audioWrongArtists.length);
                        audioWrongArtists = [...audioWrongArtists, ...additionalArtists];
                    }
                    while (audioWrongArtists.length < 3) {
                        const fakeArtist = `Unknown Artist ${audioWrongArtists.length + 1}`;
                        audioWrongArtists.push(fakeArtist);
                    }
                    question = {
                        id: `mp_${Date.now()}_${i}`,
                        type: 'multiple-choice',
                        question: `Who is the artist? (Audio effects applied)`,
                        options: this.shuffleArray([track.artist, ...audioWrongArtists.slice(0, 3)]),
                        correctAnswer: track.artist,
                        category: 'Audio Manipulation',
                        timeLimit: settings.timePerQuestion || 30,
                        track: this.createTrackObject(track)
                    };
                    break;
                default:
                    let defaultWrongArtists = [];
                    try {
                        const similarArtistsJson = track.similarArtists || '[]';
                        const similarArtists = JSON.parse(similarArtistsJson);
                        if (similarArtists.length > 0) {
                            console.log(`[MultiplayerQuizGenerator] Using similar artists for ${track.artist}: ${similarArtists.join(', ')}`);
                            defaultWrongArtists = this.shuffleArray(similarArtists).slice(0, 3);
                        }
                    }
                    catch (e) {
                        console.warn('[MultiplayerQuizGenerator] Failed to parse similar artists:', e);
                    }
                    if (defaultWrongArtists.length < 3) {
                        const defaultAllArtists = tracks
                            .filter(t => t.artist && t.artist !== track.artist && !defaultWrongArtists.includes(t.artist))
                            .map(t => t.artist);
                        const defaultUniqueArtists = Array.from(new Set(defaultAllArtists));
                        const additionalArtists = this.shuffleArray(defaultUniqueArtists).slice(0, 3 - defaultWrongArtists.length);
                        defaultWrongArtists = [...defaultWrongArtists, ...additionalArtists];
                    }
                    while (defaultWrongArtists.length < 3) {
                        console.warn(`[MultiplayerQuizGenerator] Only found ${defaultWrongArtists.length} artists for default question, using fallback`);
                        const fakeArtist = `Unknown Artist ${defaultWrongArtists.length + 1}`;
                        defaultWrongArtists.push(fakeArtist);
                    }
                    question = {
                        id: `mp_${Date.now()}_${i}`,
                        type: 'multiple-choice',
                        question: `Who is the artist?`,
                        options: this.shuffleArray([track.artist, ...defaultWrongArtists.slice(0, 3)]),
                        correctAnswer: track.artist,
                        category: 'Classic',
                        timeLimit: settings.timePerQuestion || 30,
                        track: this.createTrackObject(track)
                    };
            }
            questions.push(question);
        }
        console.log(`[MultiplayerQuizGenerator] Generated ${questions.length} questions`);
        if (questions.length === 0) {
            console.error('[MultiplayerQuizGenerator] Failed to generate any questions');
            throw new Error('Failed to generate quiz questions. Please check your music library.');
        }
        return questions;
    }
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    buildWhereClause(filters) {
        const where = {
            artist: { not: null },
            title: { not: null },
            mpdFilePath: { not: '' }
        };
        console.log('[MultiplayerQuizGenerator] Building where clause with filters:', filters ? 'YES' : 'NO');
        if (!filters) {
            console.log('[MultiplayerQuizGenerator] No filters, using basic where clause');
            return where;
        }
        console.log('[MultiplayerQuizGenerator] Filter details:', JSON.stringify({
            genres: filters.genres,
            yearRange: filters.yearRange,
            charts: filters.charts,
            quality: filters.quality,
            metadata: filters.metadata,
            folders: filters.folders,
            playlists: filters.playlists
        }, null, 2));
        if (filters.genres.values.length > 0) {
            if (filters.genres.mode === 'include') {
                where.genre = { in: filters.genres.values };
            }
            else {
                where.genre = { notIn: filters.genres.values };
            }
        }
        if (filters.yearRange.enabled) {
            const yearConditions = {};
            if (filters.yearRange.min) {
                yearConditions.gte = filters.yearRange.min;
            }
            if (filters.yearRange.max) {
                yearConditions.lte = filters.yearRange.max;
            }
            if (Object.keys(yearConditions).length > 0) {
                where.year = yearConditions;
            }
        }
        console.log('[MultiplayerQuizGenerator] Chart filter settings:', {
            includeChartMusic: filters.charts.includeChartMusic,
            includeNonChartMusic: filters.charts.includeNonChartMusic
        });
        if (!filters.charts.includeChartMusic && filters.charts.includeNonChartMusic) {
            console.log('[MultiplayerQuizGenerator] Filtering: Non-chart music only');
            where.chartPosition = null;
        }
        else if (filters.charts.includeChartMusic && !filters.charts.includeNonChartMusic) {
            console.log('[MultiplayerQuizGenerator] Filtering: Chart music only');
            where.chartPosition = { not: null };
        }
        else {
            console.log('[MultiplayerQuizGenerator] Filtering: Both chart and non-chart music');
        }
        if (filters.charts.countries && filters.charts.countries.length > 0) {
            where.chartCountry = { in: filters.charts.countries };
        }
        if (filters.quality.minDifficulty !== undefined) {
            where.difficultyRating = { gte: filters.quality.minDifficulty };
        }
        if (filters.quality.maxDifficulty !== undefined) {
            where.difficultyRating = {
                ...(where.difficultyRating || {}),
                lte: filters.quality.maxDifficulty
            };
        }
        if (filters.quality.minPopularity !== undefined) {
            where.popularityScore = { gte: filters.quality.minPopularity };
        }
        if (filters.quality.requireAlbumArt) {
            where.albumArtUrl = { not: null };
        }
        if (!filters.sources.includeMyItunes) {
            where.mpdFilePath = { not: { startsWith: 'MyItunes/' } };
        }
        if (filters.metadata.requireYear) {
            where.year = { ...(where.year || {}), not: null };
        }
        if (filters.metadata.requireGenre) {
            where.genre = { ...(where.genre || {}), not: null };
        }
        if (filters.metadata.requireAlbum) {
            where.album = { not: null };
        }
        if (filters.folders.values.length > 0) {
            const folderConditions = filters.folders.values.map(folder => ({
                mpdFilePath: { contains: `${folder}/` }
            }));
            if (filters.folders.mode === 'include') {
                where.OR = folderConditions;
            }
            else {
                where.NOT = folderConditions;
            }
        }
        if (filters.playlists.values.length > 0) {
            const categoryConditions = filters.playlists.values.map(category => ({
                quizCategories: { contains: category }
            }));
            if (filters.playlists.mode === 'include') {
                if (where.OR) {
                    where.AND = [{ OR: where.OR }, { OR: categoryConditions }];
                    delete where.OR;
                }
                else {
                    where.OR = categoryConditions;
                }
            }
            else {
                if (where.NOT) {
                    where.NOT = Array.isArray(where.NOT) ? [...where.NOT, ...categoryConditions] : [where.NOT, ...categoryConditions];
                }
                else {
                    where.NOT = categoryConditions;
                }
            }
        }
        console.log('[MultiplayerQuizGenerator] Final built where clause:', JSON.stringify(where, null, 2));
        return where;
    }
    async generateUltimoteQuestions(count, ultimoteConfig, timePerQuestion = 30) {
        const settings = {
            gameMode: 'ultimote',
            totalQuestions: count,
            timePerQuestion: timePerQuestion,
            ultimoteConfig: ultimoteConfig || {
                questionsPerRound: 1,
                categories: {
                    classic: { enabled: true, rounds: Math.ceil(count / 3) },
                    quickFire: { enabled: true, rounds: Math.floor(count / 3) },
                    generalKnowledge: {
                        enabled: true,
                        rounds: Math.ceil(count / 3),
                        categories: ['film', 'sport', 'geography', 'history', 'science']
                    }
                }
            }
        };
        const tracks = await this.prisma.quizTrack.findMany({
            take: 1000,
            orderBy: { id: 'asc' }
        });
        if (tracks.length === 0) {
            console.error('[MultiplayerQuizGenerator] No tracks found in database');
            throw new Error('No tracks available for quiz generation');
        }
        console.log(`[MultiplayerQuizGenerator] Fetched ${tracks.length} tracks for question generation`);
        return this.generateUltimoteQuestionsInternal(settings, tracks);
    }
    async generateUltimoteQuestionsInternal(settings, tracks) {
        console.log('[MultiplayerQuizGenerator] Generating ulTimote questions');
        console.log('[MultiplayerQuizGenerator] ulTimote config received:', JSON.stringify(settings.ultimoteConfig, null, 2));
        console.log('[MultiplayerQuizGenerator] General knowledge config specifically:', JSON.stringify(settings.ultimoteConfig?.categories?.generalKnowledge, null, 2));
        if (!settings.ultimoteConfig) {
            console.warn('[MultiplayerQuizGenerator] No ulTimote config provided, falling back to classic mode');
            settings.gameMode = 'classic';
            return this.generateDatabaseQuestions(settings);
        }
        const config = settings.ultimoteConfig;
        const questions = [];
        const categoryRounds = [];
        console.log('[MultiplayerQuizGenerator] Processing categories:', Object.keys(config.categories || {}));
        for (const [key, cat] of Object.entries(config.categories || {})) {
            const category = cat;
            console.log(`[MultiplayerQuizGenerator] Category ${key}:`, { enabled: category?.enabled, rounds: category?.rounds });
            if (category?.enabled === true && category?.rounds && category.rounds > 0 && key !== 'generalKnowledge') {
                console.log(`[MultiplayerQuizGenerator] Adding ${category.rounds} rounds for music category: ${key}`);
                for (let i = 0; i < category.rounds; i++) {
                    categoryRounds.push({ category: key, rounds: 1, type: 'music' });
                }
            }
            else if (key !== 'generalKnowledge') {
                console.log(`[MultiplayerQuizGenerator] Skipping music category ${key} (enabled: ${category?.enabled}, rounds: ${category?.rounds})`);
            }
        }
        const generalKnowledge = config.categories?.generalKnowledge;
        console.log('[MultiplayerQuizGenerator] General Knowledge config:', JSON.stringify(generalKnowledge, null, 2));
        if (generalKnowledge?.enabled && generalKnowledge.rounds > 0) {
            if (!Array.isArray(generalKnowledge.categories)) {
                console.error('[MultiplayerQuizGenerator] General knowledge categories is not an array:', generalKnowledge.categories);
                generalKnowledge.categories = [];
            }
            console.log(`[MultiplayerQuizGenerator] General knowledge categories (${generalKnowledge.categories.length}):`, generalKnowledge.categories);
            for (let i = 0; i < generalKnowledge.rounds; i++) {
                categoryRounds.push({ category: 'generalKnowledge', rounds: 1, type: 'general' });
            }
        }
        const shuffledRounds = this.shuffleArray(categoryRounds);
        console.log(`[MultiplayerQuizGenerator] Total rounds: ${shuffledRounds.length}`);
        console.log('[MultiplayerQuizGenerator] Round distribution:', shuffledRounds.map(r => r.category));
        console.log('[MultiplayerQuizGenerator] Music rounds:', shuffledRounds.filter(r => r.type === 'music').length);
        console.log('[MultiplayerQuizGenerator] General rounds:', shuffledRounds.filter(r => r.type === 'general').length);
        if (shuffledRounds.length === 0) {
            console.error('[MultiplayerQuizGenerator] No rounds configured for ulTimote mode');
            console.error('[MultiplayerQuizGenerator] Config categories:', JSON.stringify(config.categories, null, 2));
            throw new Error('No categories enabled for ulTimote mode. Please enable at least one category with rounds > 0.');
        }
        for (const round of shuffledRounds) {
            console.log(`[MultiplayerQuizGenerator] Processing round - category: ${round.category}, type: ${round.type}`);
            if (round.type === 'music' && (!config.categories[round.category] || !config.categories[round.category].enabled || config.categories[round.category].rounds === 0)) {
                console.error(`[MultiplayerQuizGenerator] ERROR: Attempting to generate questions for disabled category ${round.category}`);
                continue;
            }
            if (round.type === 'general') {
                console.log(`[MultiplayerQuizGenerator] Generating general knowledge questions with:`);
                console.log(`  - Count: ${config.questionsPerRound || 5}`);
                console.log(`  - Categories: ${JSON.stringify(generalKnowledge.categories || [])}`);
                console.log(`  - Difficulty: ${generalKnowledge.difficulty || 3}`);
                console.log(`  - Time per question: ${settings.timePerQuestion || 30}`);
                const generalQuestions = await this.generateGeneralKnowledgeQuestions(config.questionsPerRound || 5, generalKnowledge.categories || [], generalKnowledge.difficulty || 3, settings.timePerQuestion || 30);
                console.log(`[MultiplayerQuizGenerator] Generated ${generalQuestions.length} general knowledge questions`);
                questions.push(...generalQuestions);
            }
            else {
                console.log(`[MultiplayerQuizGenerator] Generating music questions for category: ${round.category}`);
                console.log(`  - Questions per round: ${config.questionsPerRound || 5}`);
                console.log(`  - Time per question: ${settings.timePerQuestion || 30}`);
                console.log(`  - Category config:`, JSON.stringify(config.categories[round.category]));
                const requestedCount = config.questionsPerRound || 1;
                console.log(`[MultiplayerQuizGenerator] Requesting ${requestedCount} questions for ${round.category}`);
                const categoryQuestions = await this.generateCategoryQuestions(round.category, requestedCount, settings.timePerQuestion || 30, tracks, config.categories[round.category]);
                console.log(`[MultiplayerQuizGenerator] Generated ${categoryQuestions.length} questions for ${round.category} (requested: ${requestedCount})`);
                if (categoryQuestions.length !== requestedCount) {
                    console.warn(`[MultiplayerQuizGenerator] WARNING: Generated ${categoryQuestions.length} questions but requested ${requestedCount}`);
                }
                questions.push(...categoryQuestions);
            }
        }
        console.log(`[MultiplayerQuizGenerator] Generated ${questions.length} total ulTimote questions`);
        console.log(`[MultiplayerQuizGenerator] Expected ${settings.totalQuestions} questions`);
        if (questions.length === 0) {
            console.error('[MultiplayerQuizGenerator] No questions generated in ultimote mode!');
            console.error('[MultiplayerQuizGenerator] This can happen when only General Knowledge is enabled but no GK categories are selected');
            throw new Error('No questions could be generated. Please select at least one General Knowledge category or enable a music quiz category.');
        }
        if (questions.length < settings.totalQuestions) {
            console.warn(`[MultiplayerQuizGenerator] Generated fewer questions (${questions.length}) than requested (${settings.totalQuestions})`);
            console.warn('[MultiplayerQuizGenerator] This happens when the enabled categories cannot provide enough questions');
        }
        return questions;
    }
    async generateGeneralKnowledgeQuestions(count, categories, difficulty, timeLimit) {
        console.log(`[MultiplayerQuizGenerator] Generating ${count} general knowledge questions with timeLimit: ${timeLimit}s`);
        console.log(`[MultiplayerQuizGenerator] Using categories:`, categories);
        try {
            const whereClause = { isActive: true };
            if (categories.length > 0) {
                whereClause.category = {
                    slug: { in: categories }
                };
            }
            else {
                console.log('[MultiplayerQuizGenerator] No general knowledge categories selected, returning empty array');
                return [];
            }
            console.log('[MultiplayerQuizGenerator] DIFFICULTY FILTERING DISABLED FOR DEBUGGING');
            console.log('[MultiplayerQuizGenerator] General Knowledge query whereClause:', JSON.stringify(whereClause, null, 2));
            console.log('[MultiplayerQuizGenerator] About to query database with whereClause:', JSON.stringify(whereClause, null, 2));
            const startTime = Date.now();
            console.log('[MultiplayerQuizGenerator] Executing query...');
            const generalQuestions = await this.prisma.generalQuizQuestion.findMany({
                where: whereClause,
                include: { category: true }
            }).catch(error => {
                console.error('[MultiplayerQuizGenerator] Query error:', error);
                console.error('[MultiplayerQuizGenerator] Query details:', {
                    whereClause: JSON.stringify(whereClause, null, 2),
                    model: 'generalQuizQuestion'
                });
                throw error;
            });
            const queryTime = Date.now() - startTime;
            console.log(`[MultiplayerQuizGenerator] Database query took ${queryTime}ms`);
            console.log(`[MultiplayerQuizGenerator] Found ${generalQuestions.length} general questions`);
            console.log(`[MultiplayerQuizGenerator] Query result:`, {
                totalFound: generalQuestions.length,
                requestedCategories: categories,
                requestedDifficulty: difficulty
            });
            if (generalQuestions.length > 0) {
                const categoryCounts = generalQuestions.reduce((acc, q) => {
                    acc[q.category.slug] = (acc[q.category.slug] || 0) + 1;
                    return acc;
                }, {});
                console.log('[MultiplayerQuizGenerator] Questions by category:', categoryCounts);
                const difficultyDistribution = generalQuestions.reduce((acc, q) => {
                    acc[q.difficulty] = (acc[q.difficulty] || 0) + 1;
                    return acc;
                }, {});
                console.log('[MultiplayerQuizGenerator] Difficulty distribution:', difficultyDistribution);
                console.log('[MultiplayerQuizGenerator] Sample questions (first 3):', generalQuestions.slice(0, 3).map(q => ({
                    category: q.category.slug,
                    difficulty: q.difficulty,
                    question: q.question.substring(0, 50) + '...'
                })));
            }
            if (generalQuestions.length === 0) {
                console.error(`[MultiplayerQuizGenerator] No general knowledge questions found for categories: ${categories.join(', ')}`);
                const categoryDetails = await this.prisma.quizCategory.findMany({
                    where: { slug: { in: categories } },
                    include: {
                        _count: {
                            select: { questions: true }
                        }
                    }
                });
                const detailInfo = categoryDetails.map(cat => `${cat.name} (${cat.slug}): ${cat._count.questions} total questions`).join(', ');
                throw new Error(`No general knowledge questions found.\n` +
                    `Selected categories: ${categories.join(', ')}\n` +
                    `Category details: ${detailInfo}\n` +
                    `Query filters: isActive=true, difficulty=${difficulty} (DISABLED FOR DEBUGGING)`);
            }
            const questionsByCategory = {};
            generalQuestions.forEach(q => {
                const slug = q.category.slug;
                if (!questionsByCategory[slug]) {
                    questionsByCategory[slug] = [];
                }
                questionsByCategory[slug].push(q);
            });
            const categoryCount = Object.keys(questionsByCategory).length;
            const questionsPerCategory = Math.ceil(count / categoryCount);
            console.log(`[MultiplayerQuizGenerator] Distributing ${count} questions across ${categoryCount} categories (${questionsPerCategory} per category)`);
            const selectedQuestions = [];
            Object.entries(questionsByCategory).forEach(([slug, questions]) => {
                const shuffledCategory = this.shuffleArray(questions);
                const selected = shuffledCategory.slice(0, questionsPerCategory);
                selectedQuestions.push(...selected);
                console.log(`[MultiplayerQuizGenerator] Selected ${selected.length} questions from ${slug}`);
            });
            const shuffled = this.shuffleArray(selectedQuestions).slice(0, count);
            return shuffled.map((q, index) => ({
                id: `mp_gk_${Date.now()}_${index}`,
                type: 'multiple-choice',
                question: q.question,
                options: q.options ? (typeof q.options === 'string' ? JSON.parse(q.options) : q.options) : [],
                correctAnswer: q.correctAnswer,
                category: q.category.name,
                timeLimit: timeLimit,
                explanation: q.explanation || undefined
            }));
        }
        catch (error) {
            console.error('[MultiplayerQuizGenerator] Error generating general knowledge questions:', error);
            console.error('[MultiplayerQuizGenerator] Error details:', {
                count,
                categories,
                difficulty,
                timeLimit,
                errorMessage: error instanceof Error ? error.message : String(error),
                errorStack: error instanceof Error ? error.stack : undefined
            });
            return [];
        }
    }
    async generateCategoryQuestions(category, count, timeLimit, tracks, categoryConfig) {
        console.log(`[MultiplayerQuizGenerator] Generating ${count} questions for category: ${category}`);
        const categoryMap = {
            classic: 'classic',
            quickFire: 'classic',
            audioTricks: 'audio_manipulation',
            albumArt: 'classic',
            audioFingerprint: 'classic',
            chartPosition: 'chart_position',
            decadeChallenge: 'guess_the_year',
            genreSpecialist: 'genre_specialist'
        };
        const mappedGameMode = categoryMap[category] || 'classic';
        const tempSettings = {
            gameMode: mappedGameMode,
            totalQuestions: count,
            timePerQuestion: timeLimit
        };
        console.log(`[MultiplayerQuizGenerator] Generating ${mappedGameMode} questions for ulTimote category ${category}`);
        const generatedQuestions = await this.generateDatabaseQuestions(tempSettings);
        const questions = generatedQuestions.slice(0, count).map(q => ({
            ...q,
            category: this.getCategoryDisplayName(category)
        }));
        return questions;
    }
    async generateSingleQuestion(track, settings, tracks, index) {
        switch (settings.gameMode) {
            case 'classic':
                let wrongArtists = [];
                try {
                    const similarArtistsJson = track.similarArtists || '[]';
                    const similarArtists = JSON.parse(similarArtistsJson);
                    if (similarArtists.length > 0) {
                        wrongArtists = this.shuffleArray(similarArtists).slice(0, 3);
                    }
                }
                catch (e) { }
                if (wrongArtists.length < 3) {
                    const allArtists = tracks
                        .filter(t => t.artist && t.artist !== track.artist && !wrongArtists.includes(t.artist))
                        .map(t => t.artist);
                    const uniqueArtists = Array.from(new Set(allArtists));
                    const additionalArtists = this.shuffleArray(uniqueArtists).slice(0, 3 - wrongArtists.length);
                    wrongArtists = [...wrongArtists, ...additionalArtists];
                }
                while (wrongArtists.length < 3) {
                    wrongArtists.push(`Unknown Artist ${wrongArtists.length + 1}`);
                }
                return {
                    id: `mp_${Date.now()}_${index}`,
                    type: 'multiple-choice',
                    question: `Who is the artist?`,
                    options: this.shuffleArray([track.artist, ...wrongArtists.slice(0, 3)]),
                    correctAnswer: track.artist,
                    category: 'Classic',
                    timeLimit: settings.timePerQuestion || 30,
                    track: {
                        id: parseInt(track.id),
                        file: track.mpdFilePath,
                        title: track.title,
                        artist: track.artist,
                        album: track.album || undefined,
                        year: track.year || undefined,
                        albumArtUrl: track.albumArtUrl || undefined,
                        genre: track.genre || undefined,
                        duration: track.duration || undefined,
                        popularityScore: track.popularityScore || undefined,
                        chartPosition: track.chartPosition || undefined,
                        releaseDate: track.releaseDate || undefined,
                        triviaFacts: track.triviaFacts ? (typeof track.triviaFacts === 'string' ? JSON.parse(track.triviaFacts) : track.triviaFacts) : undefined,
                        interestingFacts: track.interestingFacts ? (typeof track.interestingFacts === 'string' ? JSON.parse(track.interestingFacts) : track.interestingFacts) : undefined
                    }
                };
            case 'guess_the_year':
                const correctYear = track.year || 2000;
                const yearOptions = [
                    correctYear,
                    correctYear - 2,
                    correctYear + 3,
                    correctYear - 5
                ].sort();
                return {
                    id: `mp_${Date.now()}_${index}`,
                    type: 'multiple-choice',
                    question: `What year was this song released?`,
                    options: yearOptions.map(y => y.toString()),
                    correctAnswer: correctYear.toString(),
                    category: 'Guess the Year',
                    timeLimit: settings.timePerQuestion || 30,
                    track: {
                        id: parseInt(track.id),
                        file: track.mpdFilePath,
                        title: track.title,
                        artist: track.artist,
                        album: track.album || undefined,
                        year: track.year || undefined,
                        albumArtUrl: track.albumArtUrl || undefined,
                        genre: track.genre || undefined,
                        duration: track.duration || undefined,
                        popularityScore: track.popularityScore || undefined,
                        chartPosition: track.chartPosition || undefined,
                        releaseDate: track.releaseDate || undefined,
                        triviaFacts: track.triviaFacts ? (typeof track.triviaFacts === 'string' ? JSON.parse(track.triviaFacts) : track.triviaFacts) : undefined,
                        interestingFacts: track.interestingFacts ? (typeof track.interestingFacts === 'string' ? JSON.parse(track.interestingFacts) : track.interestingFacts) : undefined
                    }
                };
            default:
                return null;
        }
    }
    getCategoryDisplayName(category) {
        const displayNames = {
            classic: 'Classic Quiz',
            quickFire: 'Quick Fire',
            audioTricks: 'Audio Tricks',
            albumArt: 'Album Art',
            audioFingerprint: 'Audio Fingerprint',
            chartPosition: 'Chart Position',
            decadeChallenge: 'Guess the Year',
            genreSpecialist: 'Genre Specialist',
            generalKnowledge: 'General Knowledge'
        };
        return displayNames[category] || category;
    }
    async cleanup() {
        await this.prisma.$disconnect();
    }
}
exports.MultiplayerQuizGenerator = MultiplayerQuizGenerator;
//# sourceMappingURL=multiplayer-quiz-generator.js.map