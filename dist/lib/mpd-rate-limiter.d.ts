export declare class MPDRateLimiter {
    private static instance;
    private clients;
    private readonly SERVER_MIN_INTERVAL_MS;
    private readonly SERVER_MAX_REQUESTS_PER_MINUTE;
    private readonly CLIENT_MIN_INTERVAL_MS;
    private readonly CLIENT_MAX_REQUESTS_PER_MINUTE;
    private readonly WINDOW_SIZE_MS;
    private constructor();
    static getInstance(): MPDRateLimiter;
    private isServerRequest;
    canMakeRequest(clientId: string): boolean;
    getTimeUntilNextRequest(clientId: string): number;
    cleanup(): void;
}
