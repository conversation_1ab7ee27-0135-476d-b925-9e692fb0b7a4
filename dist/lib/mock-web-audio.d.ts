declare class MockAudioParam {
    value: number;
    private _defaultValue;
    minValue?: number;
    maxValue?: number;
    constructor(defaultValue: number, minValue?: number, maxValue?: number);
    setValueAtTime(value: number, startTime: number): void;
    linearRampToValueAtTime(value: number, endTime: number): void;
    exponentialRampToValueAtTime(value: number, endTime: number): void;
    setTargetAtTime(target: number, startTime: number, timeConstant: number): void;
    get defaultValue(): number;
}
declare class MockAudioNode {
    context: MockAudioContext;
    protected connectedTo: MockAudioNode[];
    numberOfInputs: number;
    numberOfOutputs: number;
    constructor(context: MockAudioContext);
    connect(destinationNode: MockAudioNode | MockAudioParam, outputIndex?: number, inputIndex?: number): MockAudioNode | void;
    disconnect(destination?: MockAudioNode | number | MockAudioParam, output?: number, input?: number): void;
}
declare class MockAudioBufferSourceNode extends MockAudioNode {
    buffer: AudioBuffer | null;
    loop: boolean;
    loopStart: number;
    loopEnd: number;
    onended: (() => void) | null;
    private _isPlaying;
    constructor(context: MockAudioContext);
    start(when?: number, offset?: number, duration?: number): void;
    stop(when?: number): void;
}
declare class MockGainNode extends MockAudioNode {
    readonly gain: MockAudioParam;
    constructor(context: MockAudioContext);
}
declare class MockBiquadFilterNode extends MockAudioNode {
    type: BiquadFilterType;
    readonly frequency: MockAudioParam;
    readonly Q: MockAudioParam;
    readonly gain: MockAudioParam;
    constructor(context: MockAudioContext);
    getFrequencyResponse(frequencyHz: Float32Array, magResponse: Float32Array, phaseResponse: Float32Array): void;
}
declare class MockPannerNode extends MockAudioNode {
    panningModel: PanningModelType;
    distanceModel: DistanceModelType;
    refDistance: number;
    maxDistance: number;
    rolloffFactor: number;
    coneInnerAngle: number;
    coneOuterAngle: number;
    coneOuterGain: number;
    readonly pan: MockAudioParam;
    constructor(context: MockAudioContext);
    setPosition(x: number, y: number, z: number): void;
    setOrientation(x: number, y: number, z: number): void;
}
declare class MockDelayNode extends MockAudioNode {
    readonly delayTime: MockAudioParam;
    constructor(context: MockAudioContext, maxDelayTime?: number);
}
declare class MockConvolverNode extends MockAudioNode {
    buffer: AudioBuffer | null;
    normalize: boolean;
    constructor(context: MockAudioContext);
}
declare class MockAnalyserNode extends MockAudioNode {
    fftSize: number;
    frequencyBinCount: number;
    minDecibels: number;
    maxDecibels: number;
    smoothingTimeConstant: number;
    constructor(context: MockAudioContext);
    getByteFrequencyData(array: Uint8Array): void;
    getFloatFrequencyData(array: Float32Array): void;
    getByteTimeDomainData(array: Uint8Array): void;
}
export declare class MockAudioContext {
    readonly destination: MockAudioNode;
    readonly sampleRate: number;
    static currentTime: number;
    private _state;
    constructor();
    get state(): AudioContextState;
    resume(): Promise<void>;
    suspend(): Promise<void>;
    close(): Promise<void>;
    createBufferSource(): MockAudioBufferSourceNode;
    createGain(): MockGainNode;
    createBiquadFilter(): MockBiquadFilterNode;
    createPanner(): MockPannerNode;
    createDelay(maxDelayTime?: number): MockDelayNode;
    createConvolver(): MockConvolverNode;
    createAnalyser(): MockAnalyserNode;
    decodeAudioData(audioData: ArrayBuffer, successCallback: (decodedData: AudioBuffer) => void, errorCallback?: (error: DOMException) => void): Promise<AudioBuffer>;
}
interface MockMPDTrack {
    id: string;
    url: string;
    title: string;
    artist: string;
}
interface EffectsConfig {
    gain?: number;
    filter?: {
        type: BiquadFilterType;
        frequency: number;
        Q?: number;
    };
    pan?: number;
    delay?: {
        delayTime: number;
        feedback?: number;
    };
    reverb?: {
        impulseResponseUrl: string;
    };
}
export declare function loadTrackAndApplyEffects(track: MockMPDTrack, effects: EffectsConfig): Promise<void>;
export declare function updateEffectParameter(paramName: string, value: any): void;
export {};
