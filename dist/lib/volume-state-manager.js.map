{"version": 3, "file": "volume-state-manager.js", "sourceRoot": "", "sources": ["../../lib/volume-state-manager.ts"], "names": [], "mappings": ";;;;;;AAwMA,wCAeC;AAvND,kDAAyB;AAOzB,MAAM,kBAAkB,GAAG,8BAA8B,CAAA;AACzD,MAAM,cAAc,GAAG,EAAE,CAAA;AAQzB,MAAM,kBAAkB;IAItB;QAFQ,cAAS,GAAsC,IAAI,GAAG,EAAE,CAAA;QAG9D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;IAC5C,CAAC;IAKO,eAAe;QAErB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;YACzE,OAAO;gBACL,UAAU,EAAE,cAAc;gBAC1B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,IAAI,EAAE,SAAS;aAChB,CAAA;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAA;YACvD,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAgB,CAAA;gBAEhD,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,IAAI,MAAM,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;oBACvD,OAAO;wBACL,GAAG,MAAM;wBACT,IAAI,EAAE,SAAS;qBAChB,CAAA;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAA;QACnE,CAAC;QAED,OAAO;YACL,UAAU,EAAE,cAAc;YAC1B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI,EAAE,SAAS;SAChB,CAAA;IACH,CAAC;IAKO,eAAe;QAErB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;YACzE,OAAM;QACR,CAAC;QAED,IAAI,CAAC;YACH,YAAY,CAAC,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAA;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAA;QACjE,CAAC;IACH,CAAC;IAKD,cAAc;QACZ,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;IACjC,CAAC;IAKD,aAAa;QACX,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAA;IACrC,CAAC;IAKD,aAAa,CAAC,MAAc,EAAE,OAA2B,SAAS;QAEhE,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAA;QAExD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,aAAa,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,IAAI,CAAA;QAEpG,IAAI,CAAC,YAAY,GAAG;YAClB,UAAU,EAAE,aAAa;YACzB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI;SACL,CAAA;QAED,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,EAAE,CAAA;YACtB,IAAI,CAAC,eAAe,EAAE,CAAA;YACtB,OAAO,CAAC,GAAG,CAAC,+BAA+B,aAAa,QAAQ,IAAI,OAAO,CAAC,CAAA;QAC9E,CAAC;IACH,CAAC;IAKD,OAAO,CAAC,IAAwB;QAC9B,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAA;YAC7B,IAAI,CAAC,eAAe,EAAE,CAAA;YACtB,OAAO,CAAC,GAAG,CAAC,iCAAiC,IAAI,aAAa,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,CAAC,CAAA;QAChG,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,QAAsC;QAChD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC5B,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IAC9C,CAAC;IAKO,eAAe;QACrB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAChC,IAAI,CAAC;gBACH,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAA;YACjC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;YACvD,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAMD,iBAAiB,CAAC,IAAwB;QACxC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAGlB,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAA;IACrC,CAAC;IAMD,gBAAgB,CAAC,QAA4B,EAAE,MAA0B;QACvE,OAAO,CAAC,GAAG,CAAC,oCAAoC,QAAQ,OAAO,MAAM,EAAE,CAAC,CAAA;QAGxE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAGpB,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAA;IACrC,CAAC;IAKD,cAAc;QACZ,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAA;QAClC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;IACtD,CAAC;IAKD,YAAY;QAKV,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE;YAC5B,OAAO,EAAE,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,YAAY,KAAK,WAAW;gBAC3E,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC;gBAC1C,CAAC,CAAC,IAAI;YACR,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;SAC/B,CAAA;IACH,CAAC;CACF;AAGY,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAA;AAI1D,SAAgB,cAAc;IAC5B,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,eAAK,CAAC,QAAQ,CAAC,0BAAkB,CAAC,cAAc,EAAE,CAAC,CAAA;IAEzF,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,OAAO,0BAAkB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAA;IACvD,CAAC,EAAE,EAAE,CAAC,CAAA;IAEN,OAAO;QACL,GAAG,WAAW;QACd,aAAa,EAAE,0BAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,0BAAkB,CAAC;QACxE,OAAO,EAAE,0BAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,0BAAkB,CAAC;QAC5D,aAAa,EAAE,0BAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,0BAAkB,CAAC;QACxE,iBAAiB,EAAE,0BAAkB,CAAC,iBAAiB,CAAC,IAAI,CAAC,0BAAkB,CAAC;QAChF,gBAAgB,EAAE,0BAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,0BAAkB,CAAC;KAC/E,CAAA;AACH,CAAC"}