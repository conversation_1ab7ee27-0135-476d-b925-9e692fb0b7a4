"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bluetoothManager = exports.BluetoothManager = void 0;
const child_process_1 = require("child_process");
const util_1 = require("util");
const events_1 = require("events");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class BluetoothManager extends events_1.EventEmitter {
    constructor() {
        super();
        this.scanning = false;
        this.currentDevice = null;
    }
    async isAvailable() {
        try {
            if (typeof window !== 'undefined') {
                return false;
            }
            const { stdout } = await execAsync('which bluetoothctl 2>/dev/null || echo "not found"');
            return !stdout.includes('not found');
        }
        catch {
            return false;
        }
    }
    async getAdapterStatus() {
        try {
            const { stdout: listOutput } = await execAsync('bluetoothctl list 2>&1');
            if (listOutput.includes('No default controller') || listOutput.trim() === '') {
                console.log('No Bluetooth controller found');
                return { powered: false, discovering: false };
            }
            const { stdout } = await execAsync('bluetoothctl show');
            const powered = stdout.includes('Powered: yes');
            const discovering = stdout.includes('Discovering: yes');
            return { powered, discovering };
        }
        catch (error) {
            console.error('Failed to get adapter status:', error);
            return { powered: false, discovering: false };
        }
    }
    async setPower(enable) {
        try {
            const { stdout: listOutput } = await execAsync('bluetoothctl list 2>&1');
            if (listOutput.includes('No default controller') || listOutput.trim() === '') {
                console.error('Cannot set power: No Bluetooth controller found');
                throw new Error('No Bluetooth adapter found. Please ensure a Bluetooth adapter is connected.');
            }
            await execAsync(`bluetoothctl power ${enable ? 'on' : 'off'}`);
            return true;
        }
        catch (error) {
            console.error('Failed to set Bluetooth power:', error);
            if (error instanceof Error && error.message.includes('No Bluetooth adapter')) {
                throw error;
            }
            return false;
        }
    }
    async startScan(duration = 15) {
        if (this.scanning) {
            throw new Error('Already scanning');
        }
        this.scanning = true;
        this.emit('scan:start');
        try {
            await execAsync('bluetoothctl scan on');
            await new Promise(resolve => setTimeout(resolve, duration * 1000));
            await execAsync('bluetoothctl scan off');
            const devices = await this.getDevices();
            this.scanning = false;
            this.emit('scan:complete', devices);
            return devices;
        }
        catch (error) {
            this.scanning = false;
            this.emit('scan:error', error);
            throw error;
        }
    }
    async getDevices() {
        try {
            const { stdout } = await execAsync('bluetoothctl devices');
            const devices = [];
            for (const line of stdout.split('\n').filter(Boolean)) {
                const match = line.match(/Device\s+([0-9A-F:]+)\s+(.+)/);
                if (match) {
                    const [, mac, name] = match;
                    const device = await this.getDeviceInfo(mac);
                    devices.push(device);
                }
            }
            return devices;
        }
        catch (error) {
            console.error('Failed to get devices:', error);
            return [];
        }
    }
    async getDeviceInfo(mac) {
        try {
            const { stdout } = await execAsync(`bluetoothctl info ${mac}`);
            return {
                mac,
                name: this.extractValue(stdout, 'Name') || 'Unknown Device',
                connected: stdout.includes('Connected: yes'),
                paired: stdout.includes('Paired: yes'),
                trusted: stdout.includes('Trusted: yes'),
                audioDevice: stdout.includes('0x00110b') || stdout.includes('Audio Sink')
            };
        }
        catch {
            return {
                mac,
                name: 'Unknown Device',
                connected: false,
                paired: false,
                trusted: false
            };
        }
    }
    async connectDevice(mac) {
        try {
            this.emit('device:connecting', mac);
            await execAsync(`bluetoothctl trust ${mac}`);
            try {
                await execAsync(`bluetoothctl pair ${mac}`);
            }
            catch {
            }
            await execAsync(`bluetoothctl connect ${mac}`);
            await new Promise(resolve => setTimeout(resolve, 3000));
            const device = await this.getDeviceInfo(mac);
            if (device.connected) {
                this.currentDevice = device;
                this.emit('device:connected', device);
                if (device.audioDevice) {
                    await this.configureAudioOutput(device);
                }
                return true;
            }
            this.emit('device:error', 'Failed to connect');
            return false;
        }
        catch (error) {
            this.emit('device:error', error);
            return false;
        }
    }
    async disconnectDevice(mac) {
        try {
            if (!mac && this.currentDevice) {
                mac = this.currentDevice.mac;
            }
            if (!mac) {
                return false;
            }
            await execAsync(`bluetoothctl disconnect ${mac}`);
            this.currentDevice = null;
            this.emit('device:disconnected', mac);
            return true;
        }
        catch (error) {
            console.error('Failed to disconnect:', error);
            return false;
        }
    }
    async configureAudioOutput(device) {
        try {
            const { stdout } = await execAsync('pactl list short sinks');
            const bluetoothSink = stdout
                .split('\n')
                .find(line => line.includes('bluez'))
                ?.split('\t')[1];
            if (bluetoothSink) {
                await execAsync(`pactl set-default-sink ${bluetoothSink}`);
                await this.updateMPDConfig(bluetoothSink);
                this.emit('audio:configured', { device, sink: bluetoothSink });
            }
        }
        catch (error) {
            console.error('Failed to configure audio output:', error);
            this.emit('audio:error', error);
        }
    }
    async updateMPDConfig(sink) {
        try {
            await execAsync('sudo systemctl restart mpd');
        }
        catch (error) {
            console.error('Failed to restart MPD:', error);
        }
    }
    async getStatus() {
        const adapter = await this.getAdapterStatus();
        const devices = await this.getDevices();
        const connectedDevice = devices.find(d => d.connected);
        return {
            enabled: adapter.powered,
            discovering: adapter.discovering,
            devices,
            connectedDevice
        };
    }
    extractValue(output, key) {
        const match = output.match(new RegExp(`${key}:\\s+(.+)`));
        return match ? match[1].trim() : null;
    }
    async removeDevice(mac) {
        try {
            await execAsync(`bluetoothctl remove ${mac}`);
            this.emit('device:removed', mac);
            return true;
        }
        catch (error) {
            console.error('Failed to remove device:', error);
            return false;
        }
    }
    async autoConnect() {
        try {
            const devices = await this.getDevices();
            const pairedAudioDevice = devices.find(d => d.paired && d.audioDevice);
            if (pairedAudioDevice) {
                return await this.connectDevice(pairedAudioDevice.mac);
            }
            return false;
        }
        catch (error) {
            console.error('Auto-connect failed:', error);
            return false;
        }
    }
}
exports.BluetoothManager = BluetoothManager;
exports.bluetoothManager = new BluetoothManager();
//# sourceMappingURL=bluetooth-manager.js.map