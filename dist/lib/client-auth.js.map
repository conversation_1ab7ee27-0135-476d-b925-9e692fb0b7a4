{"version": 3, "file": "client-auth.js", "sourceRoot": "", "sources": ["../../lib/client-auth.ts"], "names": [], "mappings": ";;;AAMA,MAAa,kBAAkB;IAI7B,MAAM,CAAC,QAAQ;QACb,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO,IAAI,CAAA;QAC9C,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IAC7C,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,KAAa;QAC3B,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAM;QACzC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;IAC7C,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAM;QACzC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACvC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IACxC,CAAC;IAED,MAAM,CAAC,aAAa;QAClB,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO,IAAI,CAAA;QAC9C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACpD,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QAC/C,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,IAAc;QACjC,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAM;QACzC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;IAC3D,CAAC;;AAjCH,gDAkCC;AAjCyB,4BAAS,GAAG,YAAY,CAAA;AACxB,2BAAQ,GAAG,cAAc,CAAA"}