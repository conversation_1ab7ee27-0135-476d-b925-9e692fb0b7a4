{"version": 3, "file": "image-preloader.js", "sourceRoot": "", "sources": ["../../lib/image-preloader.ts"], "names": [], "mappings": ";;;AAWA,MAAM,cAAc;IAQlB;QANQ,iBAAY,GAA6B,IAAI,GAAG,EAAE,CAAA;QAClD,iBAAY,GAAgB,IAAI,GAAG,EAAE,CAAA;QACrC,iBAAY,GAAG,KAAK,CAAA;QACpB,kBAAa,GAAG,CAAC,CAAA;QACjB,gBAAW,GAAG,CAAC,CAAA;QAIrB,IAAI,CAAC,YAAY,EAAE,CAAA;IACrB,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAA;QAChD,CAAC;QACD,OAAO,cAAc,CAAC,QAAQ,CAAA;IAChC,CAAC;IAKD,OAAO,CAAC,GAA8B,EAAE,WAAsC,QAAQ;QACpF,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACvC,OAAM;QACR,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE;YACzB,GAAG;YACH,QAAQ;YACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAA;QAEF,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,EAAE,CAAA;QACrB,CAAC;IACH,CAAC;IAKD,YAAY,CAAC,IAAmC,EAAE,WAAsC,QAAQ;QAC9F,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAA;IAClD,CAAC;IAKD,oBAAoB,CAAC,UAInB;QACA,MAAM,IAAI,GAAa,EAAE,CAAA;QAEzB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAEjC,MAAM,QAAQ,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAA;YAGnE,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC7B,CAAC;YAGD,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;gBACZ,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,EAAE,iBAAiB,CAAC,CAAA;gBACrD,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,EAAE,aAAa,CAAC,CAAA;YACnD,CAAC;YAGD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW,iBAAiB,CAAC,CAAA;gBACrE,IAAI,CAAC,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW,aAAa,CAAC,CAAA;YACnE,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5C,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YAChC,MAAM,QAAQ,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAA;YACnE,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;QAC7B,CAAC,CAAC,CAAA;IACJ,CAAC;IAKD,KAAK;QACH,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;QACzB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;IAC3B,CAAC;IAKO,KAAK,CAAC,YAAY;QACxB,IAAI,IAAI,CAAC,YAAY;YAAE,OAAM;QAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QAExB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3E,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;YACnC,IAAI,CAAC,QAAQ;gBAAE,MAAK;YAEpB,IAAI,CAAC,WAAW,EAAE,CAAA;YAClB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;gBACpC,IAAI,CAAC,WAAW,EAAE,CAAA;YACpB,CAAC,CAAC,CAAA;QACJ,CAAC;QAGD,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC/B,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;gBACzB,IAAI,CAAC,YAAY,EAAE,CAAA;YACrB,CAAC,EAAE,GAAG,CAAC,CAAA;QACT,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;QAC3B,CAAC;IACH,CAAC;IAKO,WAAW;QACjB,IAAI,gBAAgB,GAAuB,IAAI,CAAA;QAC/C,IAAI,kBAAkB,GAAuB,IAAI,CAAA;QACjD,IAAI,eAAe,GAAuB,IAAI,CAAA;QAE9C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAClD,gBAAgB,GAAG,IAAI,CAAA;gBACvB,MAAK;YACP,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC7D,kBAAkB,GAAG,IAAI,CAAA;YAC3B,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvD,eAAe,GAAG,IAAI,CAAA;YACxB,CAAC;QACH,CAAC;QAED,MAAM,YAAY,GAAG,gBAAgB,IAAI,kBAAkB,IAAI,eAAe,CAAA;QAC9E,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QAC5C,CAAC;QACD,OAAO,YAAY,CAAA;IACrB,CAAC;IAKO,KAAK,CAAC,SAAS,CAAC,IAAiB;QACvC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAA;YAEvB,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC1C,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;oBAChB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;oBAC/B,OAAO,EAAE,CAAA;gBACX,CAAC,CAAA;gBACD,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE;oBACjB,OAAO,CAAC,IAAI,CAAC,4BAA4B,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;oBAEpD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;oBAC/B,OAAO,EAAE,CAAA;gBACX,CAAC,CAAA;gBAGD,GAAG,CAAC,WAAW,GAAG,WAAW,CAAA;gBAC7B,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;YACpB,CAAC,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAKD,QAAQ,CAAC,GAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IACnC,CAAC;IAKD,QAAQ;QAKN,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YACjC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAA;IACH,CAAC;CACF;AAGY,QAAA,cAAc,GAAG,cAAc,CAAC,WAAW,EAAE,CAAA;AAGnD,MAAM,YAAY,GAAG,CAAC,GAA8B,EAAE,QAAoC,EAAE,EAAE,CACnG,sBAAc,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;AAD1B,QAAA,YAAY,gBACc;AAEhC,MAAM,aAAa,GAAG,CAAC,IAAmC,EAAE,QAAoC,EAAE,EAAE,CACzG,sBAAc,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;AADhC,QAAA,aAAa,iBACmB;AAEtC,MAAM,oBAAoB,GAAG,CAAC,UAInC,EAAE,EAAE,CAAC,sBAAc,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAA;AAJzC,QAAA,oBAAoB,wBAIqB"}