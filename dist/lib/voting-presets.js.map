{"version": 3, "file": "voting-presets.js", "sourceRoot": "", "sources": ["../../lib/voting-presets.ts"], "names": [], "mappings": ";;;AAoBa,QAAA,gBAAgB,GAAmB;IAC9C;QACE,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,UAAU;QAChB,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,6CAA6C;QAC1D,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC;QACpC,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,qCAAqC,EAAE,KAAK,EAAE,IAAI,EAAE;YACzG,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,oCAAoC,EAAE,KAAK,EAAE,IAAI,EAAE;YACtG,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,yBAAyB,EAAE,KAAK,EAAE,IAAI,EAAE;YAChG,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,8BAA8B,EAAE,KAAK,EAAE,IAAI,EAAE;SACxG;KACF;IACD;QACE,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,UAAU;QAChB,KAAK,EAAE,0BAA0B;QACjC,WAAW,EAAE,gCAAgC;QAC7C,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;QACvC,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,gCAAgC,EAAE,KAAK,EAAE,IAAI,EAAE;YAC9G,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,qCAAqC,EAAE,KAAK,EAAE,IAAI,EAAE;YAC9G,EAAE,KAAK,EAAE,qBAAqB,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,yBAAyB,EAAE,KAAK,EAAE,IAAI,EAAE;YAC3G,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,6BAA6B,EAAE,KAAK,EAAE,IAAI,EAAE;SAClG;KACF;IACD;QACE,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,UAAU;QAChB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,oCAAoC;QACjD,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;QAChC,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,kCAAkC,EAAE,KAAK,EAAE,GAAG,EAAE;YAC3G,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,2BAA2B,EAAE,KAAK,EAAE,IAAI,EAAE;YACnG,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,uCAAuC,EAAE,KAAK,EAAE,IAAI,EAAE;YAC1G,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,uCAAuC,EAAE,KAAK,EAAE,IAAI,EAAE;SAC9G;KACF;CACF,CAAA;AAKY,QAAA,cAAc,GAAmB;IAC5C;QACE,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,8CAA8C;QAC3D,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC;QAC/B,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,sBAAsB,EAAE,KAAK,EAAE,IAAI,EAAE;YACrF,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,sBAAsB,EAAE,KAAK,EAAE,IAAI,EAAE;YACzF,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,sBAAsB,EAAE,KAAK,EAAE,IAAI,EAAE;YACxF,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,gCAAgC,EAAE,KAAK,EAAE,IAAI,EAAE;SACxG;KACF;IACD;QACE,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,sBAAsB;QAC7B,WAAW,EAAE,+BAA+B;QAC5C,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;QAC/B,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,+BAA+B,EAAE,KAAK,EAAE,IAAI,EAAE;YACzG,EAAE,KAAK,EAAE,sBAAsB,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,yBAAyB,EAAE,KAAK,EAAE,IAAI,EAAE;YACvG,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE;YAC/F,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAE;SAC7F;KACF;IACD;QACE,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,qBAAqB;QAC5B,WAAW,EAAE,kCAAkC;QAC/C,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC;QACrC,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,0CAA0C,EAAE,KAAK,EAAE,IAAI,EAAE;YACtG,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,kCAAkC,EAAE,KAAK,EAAE,IAAI,EAAE;YAC9F,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,wBAAwB,EAAE,KAAK,EAAE,IAAI,EAAE;YACpF,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,wBAAwB,EAAE,KAAK,EAAE,IAAI,EAAE;SACrF;KACF;CACF,CAAA;AAKY,QAAA,iBAAiB,GAAmB;IAC/C;QACE,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,WAAW;QACjB,KAAK,EAAE,kBAAkB;QACzB,WAAW,EAAE,2CAA2C;QACxD,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC;QACvC,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,+BAA+B,EAAE,KAAK,EAAE,IAAI,EAAE;YACtG,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,iCAAiC,EAAE,KAAK,EAAE,GAAG,EAAE;YACpG,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,+BAA+B,EAAE,KAAK,EAAE,IAAI,EAAE;YAC1G,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,wBAAwB,EAAE,KAAK,EAAE,IAAI,EAAE;SAC/F;KACF;IACD;QACE,EAAE,EAAE,qBAAqB;QACzB,IAAI,EAAE,WAAW;QACjB,KAAK,EAAE,qBAAqB;QAC5B,WAAW,EAAE,gCAAgC;QAC7C,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,SAAS,CAAC;QAC5C,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,oBAAoB,EAAE,WAAW,EAAE,sCAAsC,EAAE,KAAK,EAAE,IAAI,EAAE;YAC9H,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,6BAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;YAC1G,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,wBAAwB,EAAE,KAAK,EAAE,IAAI,EAAE;YACpG,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,6BAA6B,EAAE,KAAK,EAAE,IAAI,EAAE;SACnG;KACF;IACD;QACE,EAAE,EAAE,YAAY;QAChB,IAAI,EAAE,WAAW;QACjB,KAAK,EAAE,iBAAiB;QACxB,WAAW,EAAE,gCAAgC;QAC7C,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,MAAM,EAAE,eAAe,EAAE,aAAa,CAAC;QAC9C,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,oBAAoB,EAAE,WAAW,EAAE,mCAAmC,EAAE,KAAK,EAAE,IAAI,EAAE;YAC3H,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,yBAAyB,EAAE,KAAK,EAAE,OAAO,EAAE;YACpG,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,+BAA+B,EAAE,KAAK,EAAE,IAAI,EAAE;YACzG,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,oCAAoC,EAAE,KAAK,EAAE,IAAI,EAAE;SAC/G;KACF;CACF,CAAA;AAKY,QAAA,aAAa,GAAmB;IAC3C;QACE,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,OAAO;QACb,KAAK,EAAE,iBAAiB;QACxB,WAAW,EAAE,4CAA4C;QACzD,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;QACpC,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,kCAAkC,EAAE,KAAK,EAAE,IAAI,EAAE;YACxG,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,iCAAiC,EAAE,KAAK,EAAE,IAAI,EAAE;YAC5G,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,8BAA8B,EAAE,KAAK,EAAE,IAAI,EAAE;YAC3G,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,+BAA+B,EAAE,KAAK,EAAE,IAAI,EAAE;SAClG;KACF;IACD;QACE,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,OAAO;QACb,KAAK,EAAE,mBAAmB;QAC1B,WAAW,EAAE,iCAAiC;QAC9C,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC;QACnC,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,mBAAmB,EAAE,WAAW,EAAE,+BAA+B,EAAE,KAAK,EAAE,IAAI,EAAE;YACrH,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,6BAA6B,EAAE,KAAK,EAAE,IAAI,EAAE;YACxG,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,4BAA4B,EAAE,KAAK,EAAE,IAAI,EAAE;YACxG,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,yBAAyB,EAAE,KAAK,EAAE,IAAI,EAAE;SACrG;KACF;IACD;QACE,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,OAAO;QACb,KAAK,EAAE,iBAAiB;QACxB,WAAW,EAAE,mDAAmD;QAChE,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,eAAe,CAAC;QAC5C,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,0BAA0B,EAAE,KAAK,EAAE,MAAM,EAAE;YACvG,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,0BAA0B,EAAE,KAAK,EAAE,MAAM,EAAE;YACzG,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,6BAA6B,EAAE,KAAK,EAAE,IAAI,EAAE;YACnG,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,iCAAiC,EAAE,KAAK,EAAE,IAAI,EAAE;SACtG;KACF;CACF,CAAA;AAKY,QAAA,cAAc,GAAmB;IAC5C;QACE,EAAE,EAAE,oBAAoB;QACxB,IAAI,EAAE,YAAY;QAClB,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,yCAAyC;QACtD,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;QAClC,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,gCAAgC,EAAE,KAAK,EAAE,KAAK,EAAE;YAC/G,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,8BAA8B,EAAE,KAAK,EAAE,IAAI,EAAE;YAClG,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,wBAAwB,EAAE,KAAK,EAAE,GAAG,EAAE;YACnG,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,2BAA2B,EAAE,KAAK,EAAE,GAAG,EAAE;SACnG;KACF;IACD;QACE,EAAE,EAAE,mBAAmB;QACvB,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,kBAAkB;QACzB,WAAW,EAAE,mDAAmD;QAChE,gBAAgB,EAAE,EAAE;QACpB,IAAI,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,OAAO,CAAC;QAC1C,OAAO,EAAE;YACP,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,2BAA2B,EAAE,KAAK,EAAE,IAAI,EAAE;YAC5F,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,gCAAgC,EAAE,KAAK,EAAE,IAAI,EAAE;YAC1G,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,4BAA4B,EAAE,KAAK,EAAE,IAAI,EAAE;YAC7F,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,4BAA4B,EAAE,KAAK,EAAE,IAAI,EAAE;SACnG;KACF;CACF,CAAA;AAKY,QAAA,WAAW,GAAG;IACzB,GAAG,wBAAgB;IACnB,GAAG,sBAAc;IACjB,GAAG,yBAAiB;IACpB,GAAG,qBAAa;IAChB,GAAG,sBAAc;CAClB,CAAA;AAKM,MAAM,gBAAgB,GAAG,CAAC,IAAgB,EAAkB,EAAE;IACnE,OAAO,mBAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;AAC3D,CAAC,CAAA;AAFY,QAAA,gBAAgB,oBAE5B;AAEM,MAAM,aAAa,GAAG,CAAC,EAAU,EAA4B,EAAE;IACpE,OAAO,mBAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAA;AACrD,CAAC,CAAA;AAFY,QAAA,aAAa,iBAEzB;AAEM,MAAM,eAAe,GAAG,CAAC,GAAW,EAAkB,EAAE;IAC7D,OAAO,mBAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;AAChE,CAAC,CAAA;AAFY,QAAA,eAAe,mBAE3B;AAEM,MAAM,eAAe,GAAG,CAAC,IAAiB,EAAgB,EAAE;IACjE,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,IAAA,wBAAgB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,mBAAW,CAAA;IAC3D,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAA;AAC5D,CAAC,CAAA;AAHY,QAAA,eAAe,mBAG3B;AAKM,MAAM,kBAAkB,GAAG,CAChC,IAAgB,EAChB,KAAa,EACb,WAAmB,EACnB,OAAuB,EACvB,YAAoB,EAAE,EACR,EAAE;IAChB,OAAO;QACL,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;QAC1B,IAAI;QACJ,KAAK;QACL,WAAW;QACX,OAAO;QACP,gBAAgB,EAAE,SAAS;QAC3B,IAAI,EAAE,CAAC,QAAQ,CAAC;KACjB,CAAA;AACH,CAAC,CAAA;AAhBY,QAAA,kBAAkB,sBAgB9B"}