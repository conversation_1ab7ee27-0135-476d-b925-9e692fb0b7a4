"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MPDStreamManager = void 0;
exports.createMPDStreamManager = createMPDStreamManager;
class MPDStreamManager {
    constructor(config) {
        this.audioElement = null;
        this.isInitialized = false;
        this.reconnectAttempts = 0;
        this.statusCallbacks = [];
        this.config = {
            mpdHost: config.mpdHost,
            mpdPort: config.mpdPort,
            streamPort: config.streamPort,
            fallbackStreamPort: config.fallbackStreamPort || config.streamPort + 1,
            enableLowLatency: config.enableLowLatency ?? true,
            bufferSize: config.bufferSize || 2048,
            maxRetries: config.maxRetries || 5
        };
        this.streamUrl = `http://${this.config.mpdHost}:${this.config.streamPort}/`;
    }
    async initialize() {
        try {
            await this.checkStreamAvailability();
            await this.setupAudioElement();
            this.isInitialized = true;
            this.notifyStatusChange();
        }
        catch (error) {
            throw new Error(`Failed to initialize MPD stream: ${error}`);
        }
    }
    async checkStreamAvailability() {
        const urls = [
            this.streamUrl,
            ...(this.config.fallbackStreamPort ?
                [`http://${this.config.mpdHost}:${this.config.fallbackStreamPort}/`] : [])
        ];
        let lastError = null;
        for (const url of urls) {
            try {
                const response = await fetch(url, {
                    method: 'HEAD',
                    signal: AbortSignal.timeout(5000)
                });
                if (response.ok || response.status === 200) {
                    this.streamUrl = url;
                    return;
                }
            }
            catch (error) {
                lastError = error;
                continue;
            }
        }
        throw new Error(`MPD stream not available: ${lastError?.message}`);
    }
    async setupAudioElement() {
        if (this.audioElement) {
            this.audioElement.remove();
        }
        this.audioElement = new Audio();
        this.audioElement.crossOrigin = 'anonymous';
        this.audioElement.preload = 'auto';
        if (this.config.enableLowLatency) {
            this.audioElement.mozAudioChannels = 'content';
            this.audioElement.webkitAudioDecodedByteCount = this.config.bufferSize;
        }
        this.audioElement.addEventListener('loadstart', () => {
            console.log('[MPD Stream] Loading started');
        });
        this.audioElement.addEventListener('canplay', () => {
            console.log('[MPD Stream] Ready to play');
            this.notifyStatusChange();
        });
        this.audioElement.addEventListener('playing', () => {
            console.log('[MPD Stream] Playing');
            this.notifyStatusChange();
        });
        this.audioElement.addEventListener('error', (e) => {
            console.error('[MPD Stream] Error:', e);
            this.handleStreamError(e);
        });
        this.audioElement.addEventListener('stalled', () => {
            console.warn('[MPD Stream] Stream stalled, attempting recovery');
            this.handleStreamStall();
        });
        this.audioElement.src = this.streamUrl;
        this.audioElement.load();
    }
    async startStream() {
        if (!this.audioElement || !this.isInitialized) {
            throw new Error('Stream not initialized');
        }
        try {
            await this.audioElement.play();
            this.notifyStatusChange();
        }
        catch (error) {
            throw new Error(`Failed to start stream: ${error}`);
        }
    }
    stopStream() {
        if (this.audioElement) {
            this.audioElement.pause();
            this.audioElement.currentTime = 0;
            this.notifyStatusChange();
        }
    }
    setVolume(volume) {
        if (this.audioElement) {
            this.audioElement.volume = Math.max(0, Math.min(1, volume));
            this.notifyStatusChange();
        }
    }
    getStatus() {
        const isConnected = this.isInitialized && this.audioElement !== null;
        const isStreaming = this.audioElement?.readyState === 4 && !this.audioElement.paused;
        return {
            isConnected,
            isStreaming,
            streamUrl: this.streamUrl,
            latency: this.estimateLatency(),
            bitrate: 128,
            clients: 1,
            error: this.getLastError()
        };
    }
    async handleStreamError(error) {
        if (this.reconnectAttempts >= this.config.maxRetries) {
            console.error('[MPD Stream] Max reconnection attempts reached');
            return;
        }
        this.reconnectAttempts++;
        console.log(`[MPD Stream] Attempting reconnection ${this.reconnectAttempts}/${this.config.maxRetries}`);
        await new Promise(resolve => setTimeout(resolve, 2000 * this.reconnectAttempts));
        try {
            await this.checkStreamAvailability();
            if (this.audioElement) {
                this.audioElement.src = this.streamUrl;
                this.audioElement.load();
            }
        }
        catch (error) {
            console.error('[MPD Stream] Reconnection failed:', error);
        }
    }
    async handleStreamStall() {
        if (this.audioElement) {
            const currentTime = this.audioElement.currentTime;
            this.audioElement.load();
            this.audioElement.addEventListener('canplay', () => {
                if (this.audioElement) {
                    this.audioElement.currentTime = currentTime;
                    this.audioElement.play();
                }
            }, { once: true });
        }
    }
    estimateLatency() {
        return this.config.enableLowLatency ? 100 : 500;
    }
    getLastError() {
        if (this.audioElement?.error) {
            switch (this.audioElement.error.code) {
                case MediaError.MEDIA_ERR_NETWORK:
                    return 'Network error loading stream';
                case MediaError.MEDIA_ERR_DECODE:
                    return 'Stream decode error';
                case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
                    return 'Stream format not supported';
                default:
                    return 'Unknown stream error';
            }
        }
        return undefined;
    }
    onStatusChange(callback) {
        this.statusCallbacks.push(callback);
    }
    offStatusChange(callback) {
        const index = this.statusCallbacks.indexOf(callback);
        if (index > -1) {
            this.statusCallbacks.splice(index, 1);
        }
    }
    notifyStatusChange() {
        const status = this.getStatus();
        this.statusCallbacks.forEach(callback => callback(status));
    }
    dispose() {
        if (this.audioElement) {
            this.audioElement.pause();
            this.audioElement.remove();
            this.audioElement = null;
        }
        this.statusCallbacks = [];
        this.isInitialized = false;
    }
}
exports.MPDStreamManager = MPDStreamManager;
function createMPDStreamManager(config) {
    const defaultConfig = {
        mpdHost: process.env.NEXT_PUBLIC_MPD_HOST || 'localhost',
        mpdPort: parseInt(process.env.NEXT_PUBLIC_MPD_PORT || '6600'),
        streamPort: parseInt(process.env.NEXT_PUBLIC_MPD_STREAM_PORT || '8000'),
        enableLowLatency: true,
        bufferSize: 2048,
        maxRetries: 5
    };
    return new MPDStreamManager({ ...defaultConfig, ...config });
}
//# sourceMappingURL=mpd-stream-manager.js.map