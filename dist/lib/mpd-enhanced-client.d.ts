import { MPDClient } from './mpd-client';
import type { MPDConnectionConfig } from './types';
export interface CrossfadeSettings {
    enabled: boolean;
    duration: number;
}
export interface ReplayGainSettings {
    mode: 'off' | 'track' | 'album' | 'auto';
    preventClipping: boolean;
    missingPreamp: number;
}
export declare class MPDEnhancedClient extends MPDClient {
    constructor(config: MPDConnectionConfig);
    setCrossfade(seconds: number): Promise<void>;
    getCrossfade(): Promise<number>;
    setReplayGainMode(mode: 'off' | 'track' | 'album' | 'auto'): Promise<void>;
    getReplayGainMode(): Promise<string>;
    setReplayGainPreamp(db: number): Promise<void>;
    configureAudioEnhancements(config: {
        crossfade?: number;
        replayGain?: ReplayGainSettings;
    }): Promise<void>;
    getAudioEnhancements(): Promise<{
        crossfade: number;
        replayGain: {
            mode: string;
            status: string;
        };
    }>;
    applyRecommendedSettings(): Promise<void>;
}
