"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = exports.LogLevel = void 0;
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    constructor(minLevel = LogLevel.INFO) {
        this.minLevel = LogLevel.INFO;
        this.minLevel = minLevel;
    }
    static getInstance() {
        if (!Logger.instance) {
            const level = process.env.LOG_LEVEL?.toUpperCase();
            const minLevel = level ? LogLevel[level] : LogLevel.INFO;
            Logger.instance = new Logger(minLevel);
        }
        return Logger.instance;
    }
    debug(message, context = {}) {
        this.log(LogLevel.DEBUG, message, context);
    }
    info(message, context = {}) {
        this.log(LogLevel.INFO, message, context);
    }
    warn(message, context = {}) {
        this.log(LogLevel.WARN, message, context);
    }
    error(message, error, context = {}) {
        this.log(LogLevel.ERROR, message, { ...context, error });
    }
    log(level, message, context) {
        if (level < this.minLevel)
            return;
        const entry = {
            timestamp: new Date().toISOString(),
            level: LogLevel[level],
            message,
            context
        };
        if (context.error) {
            entry.error = this.serializeError(context.error);
        }
        const output = JSON.stringify(entry);
        if (level >= LogLevel.ERROR) {
            console.error(output);
        }
        else if (level >= LogLevel.WARN) {
            console.warn(output);
        }
        else {
            console.log(output);
        }
    }
    serializeError(error) {
        if (error instanceof Error) {
            return {
                name: error.name,
                message: error.message,
                stack: error.stack
            };
        }
        return error;
    }
}
exports.Logger = Logger;
exports.logger = Logger.getInstance();
//# sourceMappingURL=logger.js.map