{"version": 3, "file": "backup-manager.js", "sourceRoot": "", "sources": ["../../../lib/utils/backup-manager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,mEAA0C;AAC1C,uCAAwB;AACxB,2CAA4B;AAW5B,MAAa,aAAa;IAGxB,YAAY,YAAoB,WAAW;QACzC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,eAAe,EAAE,CAAA;IACxB,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,cAAsB,mBAAmB;QAC1D,MAAM,QAAQ,GAAG,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA;QAClF,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;QAE1C,OAAO,CAAC,GAAG,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAA;QAE3D,IAAI,CAAC;YAEH,MAAM,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC9D,gBAAM,CAAC,SAAS,CAAC,KAAK,EAAE;gBACxB,gBAAM,CAAC,MAAM,CAAC,KAAK,EAAE;gBACrB,gBAAM,CAAC,KAAK,CAAC,KAAK,EAAE;aACrB,CAAC,CAAA;YAGF,MAAM,SAAS,GAAG,IAAI,CAAA;YACtB,MAAM,MAAM,GAAU,EAAE,CAAA;YACxB,MAAM,OAAO,GAAU,EAAE,CAAA;YACzB,MAAM,MAAM,GAAU,EAAE,CAAA;YAGxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3D,MAAM,KAAK,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;oBAC5C,IAAI,EAAE,CAAC,GAAG,SAAS;oBACnB,IAAI,EAAE,SAAS;iBAChB,CAAC,CAAA;gBACF,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA;YACvB,CAAC;YAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5D,MAAM,KAAK,GAAG,MAAM,gBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;oBACzC,IAAI,EAAE,CAAC,GAAG,SAAS;oBACnB,IAAI,EAAE,SAAS;iBAChB,CAAC,CAAA;gBACF,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA;YACxB,CAAC;YAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3D,MAAM,KAAK,GAAG,MAAM,gBAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;oBACxC,IAAI,EAAE,CAAC,GAAG,SAAS;oBACnB,IAAI,EAAE,SAAS;iBAChB,CAAC,CAAA;gBACF,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA;YACvB,CAAC;YAED,MAAM,UAAU,GAAG;gBACjB,QAAQ,EAAE;oBACR,EAAE,EAAE,QAAQ;oBACZ,SAAS;oBACT,WAAW;oBACX,aAAa,EAAE;wBACb,UAAU,EAAE,UAAU;wBACtB,OAAO,EAAE,WAAW;wBACpB,MAAM,EAAE,UAAU;qBACnB;iBACF;gBACD,IAAI,EAAE;oBACJ,UAAU,EAAE,MAAM;oBAClB,OAAO,EAAE,OAAO;oBAChB,MAAM,EAAE,MAAM;iBACf;aACF,CAAA;YAGD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAA;YAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;YACpD,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;YAGtC,MAAM,QAAQ,GAAmB;gBAC/B,EAAE,EAAE,QAAQ;gBACZ,SAAS;gBACT,WAAW;gBACX,aAAa,EAAE,UAAU,CAAC,QAAQ,CAAC,aAAa;gBAChD,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC;gBACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC7E,CAAA;YAED,EAAE,CAAC,aAAa,CACd,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,YAAY,CAAC,EAClD,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAClC,CAAA;YAED,OAAO,CAAC,GAAG,CAAC,mCAAmC,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;YAClG,OAAO,QAAQ,CAAA;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAA;YAChE,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,WAAW;QACT,MAAM,OAAO,GAAqB,EAAE,CAAA;QAEpC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,OAAO,CAAA;QAElD,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAE5C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;oBAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAA;oBAC/D,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBACxB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,mDAAmD,IAAI,EAAE,CAAC,CAAA;gBACzE,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;IAClG,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,QAAgB;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAA;QAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,YAAY,CAAC,CAAA;QAEnE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,EAAE,CAAC,CAAA;QAClD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,qCAAqC,QAAQ,EAAE,CAAC,CAAA;QAE5D,IAAI,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;YACnE,MAAM,QAAQ,GAAmB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAA;YAG/E,MAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC;iBACtC,UAAU,CAAC,KAAK,CAAC;iBACjB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;iBAC3C,MAAM,CAAC,KAAK,CAAC,CAAA;YAEhB,IAAI,eAAe,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAA;YAC9D,CAAC;YAGD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAA;YACxD,MAAM,gBAAM,CAAC,YAAY,CAAC;gBACxB,gBAAM,CAAC,UAAU,CAAC,UAAU,EAAE;gBAC9B,gBAAM,CAAC,SAAS,CAAC,UAAU,EAAE;gBAC7B,gBAAM,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC1B,gBAAM,CAAC,KAAK,CAAC,UAAU,EAAE;aAC1B,CAAC,CAAA;YAGF,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,IAAI,CAAA;YAEvD,IAAI,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,gBAAM,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAA;YACnD,CAAC;YAED,IAAI,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvB,MAAM,gBAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAA;YACjD,CAAC;YAED,IAAI,UAAU,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,gBAAM,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAA;YACzD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,iDAAiD,QAAQ,EAAE,CAAC,CAAA;YACxE,OAAO,CAAC,GAAG,CAAC,oBAAoB,UAAU,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAA;YAC1D,OAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAA;YACnD,OAAO,CAAC,GAAG,CAAC,eAAe,MAAM,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAA;QAEnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAA;YACjE,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,YAAY,CAAC,QAAgB;QAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAA;QAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,YAAY,CAAC,CAAA;QAEnE,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;QAC3B,CAAC;QAED,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;QACzB,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAA;IAC5D,CAAC;IAKD,aAAa,CAAC,QAAgB;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,YAAY,CAAC,CAAA;QAEnE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAA;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKD,iBAAiB,CAAC,YAAoB,CAAC;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QAElC,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS;YAAE,OAAM;QAEvC,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QAEzC,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACH,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YAC9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,+CAA+C,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAA;YAClF,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,8BAA8B,QAAQ,CAAC,MAAM,cAAc,CAAC,CAAA;IAC1E,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACnC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QACnD,CAAC;IACH,CAAC;CACF;AA9PD,sCA8PC"}