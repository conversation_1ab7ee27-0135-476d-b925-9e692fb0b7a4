interface NetworkStatus {
    isOnline: boolean;
    isLanConnected: boolean;
    canReachServer: boolean;
    canReachMpd: boolean;
    fallbackMode: boolean;
}
export declare function checkServerReachability(): Promise<boolean>;
export declare function checkMpdReachability(): Promise<boolean>;
export declare function isLocalhostMode(): boolean;
export declare function getFallbackUrls(): {
    api: string;
    socket: string;
    mpd: string;
};
export declare function initNetworkMonitoring(): void;
export declare function getNetworkStatus(): NetworkStatus;
export declare function fetchWithFallback(url: string, options?: RequestInit): Promise<Response>;
export declare function getAdaptiveSocketUrl(): string;
export declare function getAdaptiveMpdUrl(): string;
export {};
