export interface BatchProcessorOptions {
    batchSize: number;
    concurrency: number;
    retryAttempts: number;
    retryDelay: number;
    onProgress?: (processed: number, total: number, errors: number) => void;
    onError?: (error: any, item: any, attempt: number) => void;
    onBatchComplete?: (batchIndex: number, totalBatches: number, results: any[]) => void;
}
export interface ProcessingResult<T> {
    success: T[];
    errors: Array<{
        item: any;
        error: any;
        attempts: number;
    }>;
    stats: {
        total: number;
        successful: number;
        failed: number;
        duration: number;
        avgProcessingTime: number;
    };
}
export declare class BatchProcessor {
    private static defaultOptions;
    static process<TInput, TOutput>(items: TInput[], processor: (item: TInput) => Promise<TOutput>, options?: Partial<BatchProcessorOptions>): Promise<ProcessingResult<TOutput>>;
    private static processBatchWithConcurrency;
    private static processItemWithRetry;
    private static createBatches;
    private static sleep;
}
export declare class ProgressTracker {
    private startTime;
    private processed;
    private total;
    private errors;
    private lastReportTime;
    private reportInterval;
    constructor(total: number);
    update(processed: number, errors?: number): void;
    report(): void;
    complete(): void;
    private formatDuration;
}
