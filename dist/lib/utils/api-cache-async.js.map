{"version": 3, "file": "api-cache-async.js", "sourceRoot": "", "sources": ["../../../lib/utils/api-cache-async.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8VA,8BAuBC;AA9WD,gDAAiC;AACjC,2CAA4B;AA4B5B,MAAa,QAAQ;IASnB,YAAoB,OAAqB;QAArB,YAAO,GAAP,OAAO,CAAc;QARjC,UAAK,GAAG,IAAI,GAAG,EAAyB,CAAA;QACxC,UAAK,GAAe;YAC1B,IAAI,EAAE,CAAC;YACP,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;YACP,SAAS,EAAE,CAAC;SACb,CAAA;QAIC,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAChC,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAA;QAC9D,CAAC,CAAC,CAAA;IACJ,CAAC;IAKD,KAAK,CAAC,GAAG,CAAC,GAAW;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QACjC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAErC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAA;YACnB,OAAO,IAAI,CAAA;QACb,CAAC;QAGD,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAA;YACnB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;QACjB,OAAO,KAAK,CAAC,IAAI,CAAA;IACnB,CAAC;IAKD,KAAK,CAAC,GAAG,CAAC,GAAW,EAAE,KAAQ,EAAE,OAA2C;QAC1E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QACjC,MAAM,KAAK,GAAkB;YAC3B,IAAI,EAAE,KAAK;YACX,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,GAAG,EAAE,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG;YACrC,IAAI,EAAE,OAAO,EAAE,IAAI;SACpB,CAAA;QAGD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAC5C,IAAI,CAAC,WAAW,EAAE,CAAA;QACpB,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;QAGjC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAC5C,OAAO,CAAC,IAAI,CAAC,yCAAyC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAA;QACtE,CAAC,CAAC,CAAA;IACJ,CAAC;IAKD,KAAK,CAAC,MAAM,CAAC,GAAW;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;QAGjC,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1C,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,GAAG,OAAO,OAAO,CAAA;gBAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;gBAChE,MAAM,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA;gBAGzC,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;YAC9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,8CAA8C,GAAG,GAAG,EAAE,KAAK,CAAC,CAAA;YAC3E,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAKD,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;QAClB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAA;QAGnB,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;gBAC1D,MAAM,OAAO,CAAC,GAAG,CACf,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACf,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAc,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CACxE,CACF,CAAA;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAA;YAC/D,CAAC;QACH,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,GAAW;QAC1B,IAAI,OAAO,GAAG,CAAC,CAAA;QACf,MAAM,YAAY,GAAa,EAAE,CAAA;QAEjC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAChD,IAAI,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACtB,OAAO,EAAE,CAAA;YACX,CAAC;QACH,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QACxB,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;QAGjC,IAAI,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAC9C,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;QAC9B,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAKD,QAAQ;QACN,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAA;IAC1B,CAAC;IAKO,OAAO,CAAC,GAAW;QACzB,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,GAAG,EAAE,CAAA;IAC3C,CAAC;IAKO,WAAW;QACjB,IAAI,SAAS,GAAkB,IAAI,CAAA;QACnC,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAE3B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAChD,IAAI,KAAK,CAAC,SAAS,GAAG,UAAU,EAAE,CAAC;gBACjC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAA;gBAC5B,SAAS,GAAG,GAAG,CAAA;YACjB,CAAC;QACH,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;YAC5B,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAA;QACxB,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,YAAY;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;YAAE,OAAM;QAEvC,IAAI,CAAC;YAEH,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;YAE/D,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAA;YAErE,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;gBAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;gBACtC,IAAI,MAAM,GAAG,CAAC,CAAA;gBAEd,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,EAAE;oBAClD,IAAI,CAAC;wBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAc,EAAE,QAAkB,CAAC,CAAA;wBAC3E,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;wBACpD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;wBAGjC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC;4BAC9C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;4BAC1B,MAAM,EAAE,CAAA;wBACV,CAAC;6BAAM,CAAC;4BAEN,MAAM,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA;wBAC3C,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,IAAI,CAAC,yCAAyC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAA;oBACtE,CAAC;gBACH,CAAC,CAAC,CACH,CAAA;gBAED,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,0BAA0B,CAAC,CAAA;gBACpE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;YAEjB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAA;QACpE,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,UAAU,CAAC,GAAW,EAAE,KAAoB;QACxD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;YAAE,OAAM;QAEvC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,CAAC,OAAO,CAAA;YAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;YAEhE,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;YAGnD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAA;YACrE,IAAI,KAAK,GAA2B,EAAE,CAAA;YAEtC,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;gBAC1D,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;YAClC,CAAC;YAAC,MAAM,CAAC;YAET,CAAC;YAED,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAA;YACrB,MAAM,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,yCAAyC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAA;QACtE,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;YAAE,OAAM;QAEvC,IAAI,CAAC;YACH,MAAM,KAAK,GAA2B,EAAE,CAAA;YAExC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;gBACpC,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,CAAC,OAAO,CAAA;gBAC9D,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAA;YACvB,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAA;YACrE,MAAM,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAA;QAChE,CAAC;IACH,CAAC;CACF;AAjRD,4BAiRC;AAKD,MAAa,YAAY;IAGvB,MAAM,CAAC,QAAQ,CAAC,OAAe,EAAE,UAAiC,EAAE;QAClE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,QAAQ,CAAC;gBACpC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,OAAO;gBAClB,GAAG,OAAO;aACX,CAAC,CAAC,CAAA;QACL,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAE,CAAA;IAClC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,QAAQ;QACnB,MAAM,OAAO,CAAC,GAAG,CACf,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAC7D,CAAA;IACH,CAAC;IAED,MAAM,CAAC,QAAQ;QACb,MAAM,KAAK,GAA+B,EAAE,CAAA;QAE5C,KAAK,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YACrD,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAA;QACnC,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;;AA9BH,oCA+BC;AA9BgB,mBAAM,GAAG,IAAI,GAAG,EAAoB,CAAA;AAmCrD,SAAgB,SAAS,CAAC,OAAqF;IAC7G,OAAO,UAAU,MAAW,EAAE,YAAoB,EAAE,UAA8B;QAChF,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAA;QAEvC,UAAU,CAAC,KAAK,GAAG,KAAK,WAAW,GAAG,IAAW;YAC/C,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YACpD,MAAM,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAA;YAG5G,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YACnC,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBACpB,OAAO,MAAM,CAAA;YACf,CAAC;YAGD,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YACrD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAA;YAElD,OAAO,MAAM,CAAA;QACf,CAAC,CAAA;QAED,OAAO,UAAU,CAAA;IACnB,CAAC,CAAA;AACH,CAAC"}