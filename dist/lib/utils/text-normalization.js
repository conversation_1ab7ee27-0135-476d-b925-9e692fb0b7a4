"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.normalizeText = normalizeText;
exports.normalizeArtistName = normalizeArtistName;
exports.normalizeSongTitle = normalizeSongTitle;
exports.levenshteinDistance = levenshteinDistance;
exports.calculateSimilarity = calculateSimilarity;
exports.calculateArtistSimilarity = calculateArtistSimilarity;
exports.calculateTitleSimilarity = calculateTitleSimilarity;
exports.areTracksDuplicates = areTracksDuplicates;
exports.findBestTrackMatch = findBestTrackMatch;
exports.groupSimilarTracks = groupSimilarTracks;
exports.generateTextVariations = generateTextVariations;
function normalizeText(text) {
    if (!text)
        return '';
    return text
        .toLowerCase()
        .trim()
        .replace(/[.,!?;:"'`~@#$%^&*()_+=\[\]{}|\\/<>]/g, ' ')
        .replace(/\s+/g, ' ')
        .replace(/^(the|a|an)\s+/i, '')
        .replace(/\s+(the|a|an)$/i, '')
        .replace(/\s+(feat|featuring|ft|with)\s+.*/i, '')
        .replace(/\([^)]*\)/g, ' ')
        .replace(/\[[^\]]*\]/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();
}
function normalizeArtistName(artist) {
    if (!artist)
        return '';
    let normalized = normalizeText(artist);
    normalized = normalized
        .replace(/\s+(&|and|&amp;|\+)\s+/gi, ' ')
        .replace(/\bdr\b/gi, 'doctor')
        .replace(/\bmr\b/gi, 'mister')
        .replace(/\bst\b/gi, 'saint')
        .replace(/\s+(jr|sr|ii|iii|iv)$/gi, '');
    return normalized.trim();
}
function normalizeSongTitle(title) {
    if (!title)
        return '';
    let normalized = normalizeText(title);
    normalized = normalized
        .replace(/\s+(remix|edit|version|mix|radio|extended|original|remastered|remaster).*$/gi, '')
        .replace(/\s+\(explicit\)/gi, '');
    return normalized.trim();
}
function levenshteinDistance(a, b) {
    if (a.length === 0)
        return b.length;
    if (b.length === 0)
        return a.length;
    const matrix = Array(b.length + 1).fill(null).map(() => Array(a.length + 1).fill(null));
    for (let i = 0; i <= a.length; i++)
        matrix[0][i] = i;
    for (let j = 0; j <= b.length; j++)
        matrix[j][0] = j;
    for (let j = 1; j <= b.length; j++) {
        for (let i = 1; i <= a.length; i++) {
            const substitutionCost = a[i - 1] === b[j - 1] ? 0 : 1;
            matrix[j][i] = Math.min(matrix[j][i - 1] + 1, matrix[j - 1][i] + 1, matrix[j - 1][i - 1] + substitutionCost);
        }
    }
    return matrix[b.length][a.length];
}
function calculateSimilarity(a, b) {
    if (!a || !b)
        return 0;
    if (a === b)
        return 1;
    const normalizedA = normalizeText(a);
    const normalizedB = normalizeText(b);
    if (normalizedA === normalizedB)
        return 1;
    const maxLength = Math.max(normalizedA.length, normalizedB.length);
    if (maxLength === 0)
        return 1;
    const distance = levenshteinDistance(normalizedA, normalizedB);
    return 1 - (distance / maxLength);
}
function calculateArtistSimilarity(a, b) {
    if (!a || !b)
        return 0;
    const normalizedA = normalizeArtistName(a);
    const normalizedB = normalizeArtistName(b);
    if (normalizedA === normalizedB)
        return 1;
    if (normalizedA.includes(normalizedB) || normalizedB.includes(normalizedA)) {
        return 0.9;
    }
    const wordsA = normalizedA.split(' ').sort().join(' ');
    const wordsB = normalizedB.split(' ').sort().join(' ');
    if (wordsA === wordsB)
        return 0.95;
    return calculateSimilarity(normalizedA, normalizedB);
}
function calculateTitleSimilarity(a, b) {
    if (!a || !b)
        return 0;
    const normalizedA = normalizeSongTitle(a);
    const normalizedB = normalizeSongTitle(b);
    if (normalizedA === normalizedB)
        return 1;
    if (normalizedA.includes(normalizedB) || normalizedB.includes(normalizedA)) {
        return 0.85;
    }
    return calculateSimilarity(normalizedA, normalizedB);
}
function areTracksDuplicates(track1, track2, threshold = 0.85) {
    const artistSimilarity = calculateArtistSimilarity(track1.artist, track2.artist);
    const titleSimilarity = calculateTitleSimilarity(track1.title, track2.title);
    return artistSimilarity >= threshold && titleSimilarity >= threshold;
}
function findBestTrackMatch(target, candidates, threshold = 0.8) {
    let bestMatch = null;
    let bestScore = 0;
    for (const candidate of candidates) {
        const artistScore = calculateArtistSimilarity(target.artist, candidate.artist);
        const titleScore = calculateTitleSimilarity(target.title, candidate.title);
        const combinedScore = (artistScore * 0.6) + (titleScore * 0.4);
        if (combinedScore > bestScore && combinedScore >= threshold) {
            bestScore = combinedScore;
            bestMatch = candidate;
        }
    }
    return bestMatch ? { match: bestMatch, score: bestScore } : null;
}
function groupSimilarTracks(tracks, threshold = 0.85) {
    const groups = [];
    const processed = new Set();
    for (let i = 0; i < tracks.length; i++) {
        if (processed.has(i))
            continue;
        const group = [tracks[i]];
        processed.add(i);
        for (let j = i + 1; j < tracks.length; j++) {
            if (processed.has(j))
                continue;
            if (areTracksDuplicates(tracks[i], tracks[j], threshold)) {
                group.push(tracks[j]);
                processed.add(j);
            }
        }
        groups.push(group);
    }
    return groups;
}
function generateTextVariations(text) {
    if (!text)
        return [];
    const variations = new Set();
    variations.add(text);
    variations.add(normalizeText(text));
    const withoutArticles = text.replace(/^(the|a|an)\s+/i, '').replace(/\s+(the|a|an)$/i, '');
    variations.add(withoutArticles);
    variations.add(normalizeText(withoutArticles));
    const withoutParens = text.replace(/\([^)]*\)/g, '').trim();
    if (withoutParens !== text) {
        variations.add(withoutParens);
        variations.add(normalizeText(withoutParens));
    }
    const withoutFeat = text.replace(/\s+(feat|featuring|ft|with)\s+.*/i, '').trim();
    if (withoutFeat !== text) {
        variations.add(withoutFeat);
        variations.add(normalizeText(withoutFeat));
    }
    return Array.from(variations).filter(v => v.length > 0);
}
//# sourceMappingURL=text-normalization.js.map