export interface CacheEntry<T> {
    data: T;
    timestamp: number;
    ttl: number;
    hits: number;
}
export interface CacheOptions {
    ttl: number;
    maxSize: number;
    persistToDisk: boolean;
    diskCachePath?: string;
}
export declare class ApiCache<T = any> {
    private cache;
    private accessOrder;
    private accessCounter;
    private options;
    constructor(options?: Partial<CacheOptions>);
    private generateKey;
    get(service: string, endpoint: string, params?: Record<string, any>): T | null;
    set(service: string, endpoint: string, data: T, params?: Record<string, any>, ttl?: number): void;
    has(service: string, endpoint: string, params?: Record<string, any>): boolean;
    cleanup(): number;
    private evictLRU;
    getStats(): {
        size: number;
        maxSize: number;
        hitRate: number;
        totalHits: number;
        totalEntries: number;
    };
    clear(): void;
    private loadFromDisk;
    private saveToDisk;
}
export declare class CacheManager {
    private static caches;
    static getCache(service: string, options?: Partial<CacheOptions>): ApiCache;
    private static getDefaultTTL;
    static cleanup(): void;
    static getGlobalStats(): Record<string, any>;
    static clearAll(): void;
}
