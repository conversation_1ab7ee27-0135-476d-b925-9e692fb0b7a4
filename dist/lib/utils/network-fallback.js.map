{"version": 3, "file": "network-fallback.js", "sourceRoot": "", "sources": ["../../../lib/utils/network-fallback.ts"], "names": [], "mappings": ";;AAyBA,0DAgBC;AAKD,oDAoBC;AAKD,0CAIC;AAKD,0CAQC;AAKD,sDAYC;AA8DD,4CAYC;AAKD,8CA4BC;AAKD,oDAoBC;AAKD,8CAaC;AA3PD,mCAA8B;AAU9B,IAAI,aAAa,GAAkB;IACjC,QAAQ,EAAE,IAAI;IACd,cAAc,EAAE,IAAI;IACpB,cAAc,EAAE,IAAI;IACpB,WAAW,EAAE,IAAI;IACjB,YAAY,EAAE,KAAK;CACpB,CAAA;AAKM,KAAK,UAAU,uBAAuB;IAC3C,IAAI,CAAC;QAEH,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAA;QACxC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,CAAA;QAE5D,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,aAAa,EAAE;YAC1C,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,UAAU,CAAC,MAAM;SAC1B,CAAC,CAAA;QAEF,YAAY,CAAC,SAAS,CAAC,CAAA;QACvB,OAAO,QAAQ,CAAC,EAAE,CAAA;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AAKM,KAAK,UAAU,oBAAoB;IACxC,IAAI,CAAC;QAEH,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAA;QAC5E,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,MAAM,CAAA;QAC/D,MAAM,WAAW,GAAG,UAAU,OAAO,IAAI,OAAO,EAAE,CAAA;QAElD,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAA;QACxC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,CAAA;QAE5D,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,WAAW,SAAS,EAAE;YACpD,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,UAAU,CAAC,MAAM;SAC1B,CAAC,CAAA;QAEF,YAAY,CAAC,SAAS,CAAC,CAAA;QACvB,OAAO,QAAQ,CAAC,EAAE,CAAA;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AAKD,SAAgB,eAAe;IAC7B,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,OAAO,IAAI,CAAA;IAC9C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAA;IACzC,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,WAAW,CAAA;AAC7D,CAAC;AAKD,SAAgB,eAAe;IAC7B,MAAM,WAAW,GAAG,eAAe,EAAE,CAAA;IAErC,OAAO;QACL,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM;QACnE,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,QAAQ,OAAO;QAC/G,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,QAAQ,OAAO;KAC7G,CAAA;AACH,CAAC;AAKD,SAAgB,qBAAqB;IACnC,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,OAAM;IAGzC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;IAC/C,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;IAGjD,kBAAkB,EAAE,CAAA;IAGpB,WAAW,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAA;AACxC,CAAC;AAED,KAAK,UAAU,YAAY;IACzB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;IAChD,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAA;IAC7B,MAAM,kBAAkB,EAAE,CAAA;AAC5B,CAAC;AAED,SAAS,aAAa;IACpB,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;IACjD,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAA;IAC9B,aAAa,CAAC,cAAc,GAAG,KAAK,CAAA;IACpC,aAAa,CAAC,WAAW,GAAG,KAAK,CAAA;IACjC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAA;IAEjC,cAAK,CAAC,OAAO,CAAC,sBAAsB,EAAE;QACpC,WAAW,EAAE,wDAAwD;QACrE,QAAQ,EAAE,IAAI;KACf,CAAC,CAAA;AACJ,CAAC;AAED,KAAK,UAAU,kBAAkB;IAC/B,MAAM,YAAY,GAAG,aAAa,CAAC,cAAc,CAAA;IACjD,MAAM,eAAe,GAAG,aAAa,CAAC,WAAW,CAAA;IAGjD,aAAa,CAAC,cAAc,GAAG,MAAM,uBAAuB,EAAE,CAAA;IAC9D,aAAa,CAAC,WAAW,GAAG,MAAM,oBAAoB,EAAE,CAAA;IAGxD,MAAM,cAAc,GAAG,CAAC,aAAa,CAAC,cAAc,IAAI,CAAC,aAAa,CAAC,WAAW,CAAA;IAElF,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,cAAc,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC;QAEnG,aAAa,CAAC,YAAY,GAAG,KAAK,CAAA;QAClC,cAAK,CAAC,OAAO,CAAC,uBAAuB,EAAE;YACrC,WAAW,EAAE,+CAA+C;YAC5D,QAAQ,EAAE,IAAI;SACf,CAAC,CAAA;IACJ,CAAC;SAAM,IAAI,YAAY,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;QAEzD,aAAa,CAAC,YAAY,GAAG,IAAI,CAAA;QACjC,cAAK,CAAC,OAAO,CAAC,oBAAoB,EAAE;YAClC,WAAW,EAAE,8DAA8D;YAC3E,QAAQ,EAAE,IAAI;SACf,CAAC,CAAA;IACJ,CAAC;SAAM,IAAI,eAAe,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;QAEzD,aAAa,CAAC,YAAY,GAAG,IAAI,CAAA;QACjC,cAAK,CAAC,OAAO,CAAC,yBAAyB,EAAE;YACvC,WAAW,EAAE,sDAAsD;YACnE,QAAQ,EAAE,IAAI;SACf,CAAC,CAAA;IACJ,CAAC;SAAM,IAAI,cAAc,KAAK,aAAa,CAAC,YAAY,EAAE,CAAC;QAEzD,aAAa,CAAC,YAAY,GAAG,cAAc,CAAA;IAC7C,CAAC;AACH,CAAC;AAKD,SAAgB,gBAAgB;IAE9B,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,oCAAoC,KAAK,MAAM,EAAE,CAAC;QACjG,OAAO;YACL,QAAQ,EAAE,IAAI;YACd,cAAc,EAAE,IAAI;YACpB,cAAc,EAAE,IAAI;YACpB,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,KAAK;SACpB,CAAA;IACH,CAAC;IACD,OAAO,EAAE,GAAG,aAAa,EAAE,CAAA;AAC7B,CAAC;AAKM,KAAK,UAAU,iBAAiB,CAAC,GAAW,EAAE,OAAqB;IAIxE,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;YAChC,GAAG,OAAO;YAEV,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;YAErD,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,SAAS;SAC/C,CAAC,CAAA;QAEF,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;YAE3C,kBAAkB,EAAE,CAAA;QACtB,CAAC;QAED,OAAO,QAAQ,CAAA;IACjB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAEf,kBAAkB,EAAE,CAAA;QAKpB,MAAM,KAAK,CAAA;IACb,CAAC;AACH,CAAC;AAKD,SAAgB,oBAAoB;IAClC,MAAM,EAAE,YAAY,EAAE,GAAG,aAAa,CAAA;IAEtC,IAAI,YAAY,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;QAElD,OAAO,uBAAuB,CAAA;IAChC,CAAC;IAGD,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAA;IACrC,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAA;IACzC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,MAAM,CAAA;IAGhE,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAAE,CAAC;QACvC,OAAO,GAAG,QAAQ,KAAK,IAAI,IAAI,UAAU,EAAE,CAAA;IAC7C,CAAC;IAGD,OAAO,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,oBAAoB,UAAU,EAAE,CAAA;AAC/E,CAAC;AAKD,SAAgB,iBAAiB;IAC/B,MAAM,EAAE,YAAY,EAAE,GAAG,aAAa,CAAA;IAEtC,IAAI,YAAY,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;QAElD,OAAO,uBAAuB,CAAA;IAChC,CAAC;IAGD,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAA;IAC5E,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,MAAM,CAAA;IAE/D,OAAO,UAAU,OAAO,IAAI,OAAO,EAAE,CAAA;AACvC,CAAC"}