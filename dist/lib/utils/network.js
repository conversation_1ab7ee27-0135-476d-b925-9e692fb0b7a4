"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentHost = getCurrentHost;
exports.isRestrictedNetwork = isRestrictedNetwork;
exports.isMobileNetwork = isMobileNetwork;
exports.getBaseUrl = getBaseUrl;
exports.getSocketUrl = getSocketUrl;
exports.getMpdProxyUrl = getMpdProxyUrl;
function getCurrentHost() {
    if (typeof window === 'undefined') {
        return process.env.NEXT_PUBLIC_HOST || 'localhost';
    }
    return window.location.hostname;
}
function isRestrictedNetwork() {
    if (typeof window === 'undefined') {
        return false;
    }
    const hostname = window.location.hostname;
    const hotspotPatterns = [
        /^192\.168\.43\.\d+$/,
        /^172\.20\.10\.\d+$/,
        /^192\.168\.137\.\d+$/,
        /^192\.168\.\d+\.1$/,
        /^10\.42\.0\.\d+$/,
        /^192\.168\.42\.\d+$/,
        /^192\.168\.2\.\d+$/,
        /^10\.\d+\.\d+\.\d+$/,
    ];
    const isHotspotIP = hotspotPatterns.some(pattern => pattern.test(hostname));
    const connection = navigator.connection ||
        navigator.mozConnection ||
        navigator.webkitConnection;
    if (connection) {
        const connectionType = connection.type;
        const effectiveType = connection.effectiveType;
        if (effectiveType && ['slow-2g', '2g', '3g'].includes(effectiveType)) {
            return true;
        }
        if (connectionType && ['cellular', 'wimax'].includes(connectionType)) {
            return true;
        }
    }
    if (isHotspotIP) {
        return true;
    }
    const isIP = /^\d+\.\d+\.\d+\.\d+$/.test(hostname);
    const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
    if (isIP && !isLocalhost) {
        return true;
    }
    return false;
}
function isMobileNetwork() {
    return isRestrictedNetwork();
}
function getBaseUrl() {
    if (typeof window === 'undefined') {
        return process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    }
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}`;
}
function getSocketUrl() {
    const host = getCurrentHost();
    const socketPort = process.env.NEXT_PUBLIC_SOCKET_PORT || '3001';
    if (host === 'localhost' || host === '127.0.0.1') {
        return process.env.NEXT_PUBLIC_SOCKET_URL || `http://${host}:${socketPort}`;
    }
    const protocol = typeof window !== 'undefined' ? window.location.protocol : 'http:';
    const hostWithoutPort = host.split(':')[0];
    return `${protocol}//${hostWithoutPort}:${socketPort}`;
}
function getMpdProxyUrl() {
    const host = getCurrentHost();
    const mpdProxyPort = process.env.NEXT_PUBLIC_MPD_HTTP_PORT || '8001';
    const mpdHost = process.env.NEXT_PUBLIC_MPD_HOST || host;
    if (host !== 'localhost' && host !== '127.0.0.1') {
        const protocol = typeof window !== 'undefined' ? window.location.protocol : 'http:';
        const hostWithoutPort = mpdHost.split(':')[0];
        return `${protocol}//${hostWithoutPort}:${mpdProxyPort}`;
    }
    return `http://${mpdHost}:${mpdProxyPort}`;
}
//# sourceMappingURL=network.js.map