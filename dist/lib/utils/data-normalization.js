"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataQualityService = exports.GenreNormalizer = exports.ArtistNormalizer = void 0;
const text_normalization_1 = require("./text-normalization");
const ARTIST_ALIASES = {
    'Eminem': ['<PERSON> Shady', '<PERSON> Math<PERSON>', '<PERSON><PERSON><PERSON> feat', 'Eminem ft'],
    '<PERSON><PERSON><PERSON>': ['<PERSON> Z', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],
    '<PERSON>': ['<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'],
    '<PERSON> Gaga': ['<PERSON> GaGa', '<PERSON><PERSON>'],
    'The Beatles': ['Beatles', 'The Beetles'],
    'Led Zeppelin': ['<PERSON> Zepelin', '<PERSON> Zepplin'],
    'AC/DC': ['ACDC', 'AC DC', 'Ac/Dc'],
    'Guns N\' Roses': ['Guns N Roses', 'Guns and Roses', 'GNR'],
    'Red Hot Chili Peppers': ['Red Hot Chilli Peppers', '<PERSON>HC<PERSON>'],
    'Foo Fighters': ['Foo Fighter'],
    'Green Day': ['Greenday'],
    'Linkin Park': ['Lincoln Park'],
    'System of a Down': ['System Of A Down', 'SOAD'],
    '<PERSON>lica': ['<PERSON>ica'],
    '<PERSON><PERSON>na': ['Ni<PERSON>'],
    '<PERSON> Jam': ['Pear<PERSON>am'],
    'Radiohead': ['Radio Head'],
    'Coldplay': ['Cold Play'],
    '<PERSON>': ['U 2', 'U-2'],
    'R.E.M.': ['R<PERSON>', 'R E M'],
    'N.W.A': ['<PERSON>', 'N W A'],
    'Wu-<PERSON> Clan': ['Wu Tang Clan', 'WuTang Clan'],
    'Beastie Boys': ['Beasty Boys'],
    'Black Sabbath': ['Black Sabath'],
    'Deep Purple': ['Deep Purpel'],
    'Pink Floyd': ['Pink Flyod'],
    'The Rolling Stones': ['Rolling Stones', 'The Rolling Stone'],
    'The Who': ['Who'],
    'The Doors': ['Doors'],
    'The Kinks': ['Kinks'],
    'The Clash': ['Clash'],
    'The Cure': ['Cure'],
    'The Police': ['Police'],
    'The Smiths': ['Smiths'],
    'Depeche Mode': ['Depeche Mode', 'Depech Mode'],
    'Duran Duran': ['Duran-Duran'],
    'Tears for Fears': ['Tears For Fears'],
    'Simple Minds': ['Simple Mind'],
    'Spandau Ballet': ['Spandau Balet'],
    'Culture Club': ['Culture-Club'],
    'Wham!': ['Wham', 'WHAM!'],
    'George Michael': ['George Micheal'],
    'Madonna': ['Madona'],
    'Prince': ['The Artist Formerly Known as Prince', 'TAFKAP'],
    'Michael Jackson': ['Micheal Jackson', 'M. Jackson'],
    'Whitney Houston': ['Whitey Houston'],
    'Mariah Carey': ['Maria Carey'],
    'Celine Dion': ['Celin Dion'],
    'Alanis Morissette': ['Alanis Morisette'],
    'Britney Spears': ['Britny Spears'],
    'Christina Aguilera': ['Christina Aquilera'],
    'Justin Timberlake': ['Justin Timberlaek'],
    'Usher': ['Usher Raymond'],
    'Alicia Keys': ['Alisha Keys'],
    'John Mayer': ['Jon Mayer'],
    'Ed Sheeran': ['Ed Sheran', 'Edward Sheeran'],
    'Adele': ['Adel'],
    'Taylor Swift': ['Tailor Swift'],
    'Katy Perry': ['Katie Perry'],
    'Rihanna': ['Rhianna', 'Rianna'],
    'Bruno Mars': ['Bruno Mar'],
    'The Weeknd': ['Weeknd'],
    'Drake': ['Drizzy'],
    'Kanye West': ['Ye', 'Kayne West'],
    'Kendrick Lamar': ['Kendrik Lamar'],
    'Post Malone': ['Post Malon'],
    'Billie Eilish': ['Billy Eilish'],
    'Ariana Grande': ['Arianna Grande'],
    'Dua Lipa': ['Dua Lippa'],
    'The Chainsmokers': ['Chainsmokers'],
    'Twenty One Pilots': ['21 Pilots', 'Twenty-One Pilots', '21pilots'],
    'Imagine Dragons': ['Imagine Dragon'],
    'OneRepublic': ['One Republic'],
    'Maroon 5': ['Maroon5', 'Maroon Five'],
    'Fall Out Boy': ['Fallout Boy'],
    'Panic! at the Disco': ['Panic at the Disco', 'Panic! At The Disco'],
    'My Chemical Romance': ['My Chemical Romance'],
    'Paramore': ['Paramour'],
    'All Time Low': ['All-Time Low'],
    'Blink-182': ['Blink 182', 'blink-182'],
    'Sum 41': ['Sum41'],
    'Good Charlotte': ['Good Charlot'],
    'Simple Plan': ['Simple Plans'],
    'New Found Glory': ['New Found Glory'],
    'Yellowcard': ['Yellow Card'],
    'The All-American Rejects': ['All American Rejects'],
    'Dashboard Confessional': ['Dashboard Confesional']
};
const GENRE_MAPPING = {
    'Hip-Hop': ['Hip Hop', 'HipHop', 'Rap', 'Rap/Hip-Hop', 'Rap - Hip-Hop', 'Hip-Hop/Rap', 'Rap/HipHop', 'Hip Hop/Rap'],
    'Electronic': ['Electronic Music', 'Electronica', 'EDM', 'Dance', 'Dance Music', 'Techno', 'House', 'Trance', 'Dubstep'],
    'Rock': ['Rock Music', 'Rock & Roll', 'Rock and Roll', 'Rock\'n\'Roll', 'RnR'],
    'Alternative Rock': ['Alternative', 'Alt Rock', 'Alt-Rock', 'Indie Rock', 'Indie', 'Alternative/Indie'],
    'Pop': ['Pop Music', 'Popular Music', 'Pop/Rock', 'Pop Rock'],
    'Pop/Chart': ['Chart Pop', 'Chart Music', 'Chart Hits', 'Hit Music', 'Top 40'],
    'R&B': ['RnB', 'R and B', 'Rhythm and Blues', 'R&B/Soul', 'Soul/R&B'],
    'Country': ['Country Music', 'Country & Western', 'Country/Folk'],
    'Folk': ['Folk Music', 'Folk Rock', 'Acoustic Folk'],
    'Jazz': ['Jazz Music', 'Traditional Jazz', 'Contemporary Jazz'],
    'Blues': ['Blues Music', 'Electric Blues', 'Acoustic Blues'],
    'Classical': ['Classical Music', 'Orchestral', 'Symphony'],
    'Metal': ['Heavy Metal', 'Death Metal', 'Black Metal', 'Thrash Metal', 'Metal Music'],
    'Punk': ['Punk Rock', 'Punk Music', 'Pop Punk', 'Pop-Punk'],
    'Reggae': ['Reggae Music', 'Ska', 'Dancehall'],
    'Latin': ['Latin Music', 'Latino', 'Hispanic Music'],
    'World Music': ['World', 'International', 'Ethnic Music'],
    'Soundtrack': ['Movie Soundtrack', 'Film Score', 'OST', 'Original Soundtrack'],
    'Christmas': ['Holiday Music', 'Xmas', 'Christmas Music', 'Holiday']
};
class ArtistNormalizer {
    static normalize(artistName) {
        if (!artistName || artistName.trim() === '')
            return artistName;
        const cleaned = this.cleanArtistName(artistName);
        const aliasMatch = this.findAliasMatch(cleaned);
        if (aliasMatch) {
            return aliasMatch;
        }
        return cleaned;
    }
    static findPotentialDuplicates(artistName, existingArtists) {
        const normalized = this.normalize(artistName);
        const duplicates = [];
        for (const existing of existingArtists) {
            const existingNormalized = this.normalize(existing);
            if (normalized === existingNormalized)
                continue;
            if (this.areAliases(normalized, existingNormalized)) {
                duplicates.push({
                    artist: existing,
                    similarity: 1.0,
                    confidence: 'high',
                    reason: 'Known alias relationship'
                });
                continue;
            }
            const similarity = (0, text_normalization_1.calculateSimilarity)(normalized, existingNormalized);
            if (similarity >= this.EXACT_MATCH_THRESHOLD) {
                duplicates.push({
                    artist: existing,
                    similarity,
                    confidence: 'high',
                    reason: 'Very high text similarity (likely typo)'
                });
            }
            else if (similarity >= this.SIMILARITY_THRESHOLD) {
                if (this.passesAdditionalChecks(normalized, existingNormalized)) {
                    duplicates.push({
                        artist: existing,
                        similarity,
                        confidence: 'medium',
                        reason: 'High text similarity with validation checks passed'
                    });
                }
            }
        }
        return duplicates.sort((a, b) => b.similarity - a.similarity);
    }
    static cleanArtistName(name) {
        return name
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[""'']/g, '"')
            .replace(/[–—]/g, '-')
            .replace(/\s*feat\.?\s*/gi, ' feat ')
            .replace(/\s*ft\.?\s*/gi, ' ft ')
            .replace(/\s*&\s*/g, ' & ');
    }
    static findAliasMatch(artistName) {
        const lowerName = artistName.toLowerCase();
        for (const canonical in ARTIST_ALIASES) {
            if (canonical.toLowerCase() === lowerName) {
                return canonical;
            }
        }
        for (const canonical in ARTIST_ALIASES) {
            const aliases = ARTIST_ALIASES[canonical];
            for (const alias of aliases) {
                if (alias.toLowerCase() === lowerName) {
                    return canonical;
                }
            }
        }
        return null;
    }
    static areAliases(name1, name2) {
        const lower1 = name1.toLowerCase();
        const lower2 = name2.toLowerCase();
        for (const canonical in ARTIST_ALIASES) {
            const allNames = [canonical, ...ARTIST_ALIASES[canonical]].map(n => n.toLowerCase());
            if (allNames.includes(lower1) && allNames.includes(lower2)) {
                return true;
            }
        }
        return false;
    }
    static passesAdditionalChecks(name1, name2) {
        const lower1 = name1.toLowerCase();
        const lower2 = name2.toLowerCase();
        const words1 = lower1.split(/\s+/).filter(w => w.length > 2);
        const words2 = lower2.split(/\s+/).filter(w => w.length > 2);
        const commonWords = words1.filter(w => words2.includes(w));
        const minWords = Math.min(words1.length, words2.length);
        if (minWords > 1) {
            const wordOverlap = commonWords.length / minWords;
            if (wordOverlap < 0.5)
                return false;
        }
        const falsePositivePatterns = [
            /\b(the|and|of|in|with|for|to|a|an)\b/,
            Math.abs(name1.length - name2.length) > Math.max(name1.length, name2.length) * 0.5
        ];
        return true;
    }
}
exports.ArtistNormalizer = ArtistNormalizer;
ArtistNormalizer.SIMILARITY_THRESHOLD = 0.85;
ArtistNormalizer.EXACT_MATCH_THRESHOLD = 0.95;
class GenreNormalizer {
    static normalize(genre) {
        if (!genre || genre.trim() === '')
            return genre;
        const cleaned = this.cleanGenre(genre);
        for (const canonical in GENRE_MAPPING) {
            const variations = GENRE_MAPPING[canonical];
            if (canonical.toLowerCase() === cleaned.toLowerCase()) {
                return canonical;
            }
            for (const variation of variations) {
                if (variation.toLowerCase() === cleaned.toLowerCase()) {
                    return canonical;
                }
            }
        }
        return cleaned;
    }
    static findPotentialDuplicates(genre, existingGenres) {
        const normalized = this.normalize(genre);
        const duplicates = [];
        for (const existing of existingGenres) {
            const existingNormalized = this.normalize(existing);
            if (normalized === existingNormalized)
                continue;
            if (this.mapToSameCanonical(normalized, existingNormalized)) {
                duplicates.push({
                    genre: existing,
                    confidence: 'high',
                    reason: 'Map to same canonical genre'
                });
            }
        }
        return duplicates;
    }
    static getGenreGroup(genre) {
        const canonical = this.normalize(genre);
        const group = [canonical];
        for (const canonicalGenre in GENRE_MAPPING) {
            if (canonicalGenre === canonical) {
                group.push(...GENRE_MAPPING[canonicalGenre]);
                break;
            }
        }
        return Array.from(new Set(group));
    }
    static cleanGenre(genre) {
        return genre
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[""'']/g, '"')
            .replace(/[–—]/g, '-')
            .replace(/\s*\/\s*/g, '/')
            .replace(/\s*-\s*/g, '-')
            .replace(/\s*&\s*/g, ' & ');
    }
    static mapToSameCanonical(genre1, genre2) {
        return this.normalize(genre1) === this.normalize(genre2);
    }
}
exports.GenreNormalizer = GenreNormalizer;
class DataQualityService {
    static async analyzeArtistDuplicates(artists) {
        const results = [];
        const processed = new Set();
        for (const artist of artists) {
            if (processed.has(artist.name))
                continue;
            const normalized = ArtistNormalizer.normalize(artist.name);
            const otherArtists = artists
                .filter(a => a.name !== artist.name && !processed.has(a.name))
                .map(a => a.name);
            const duplicates = ArtistNormalizer.findPotentialDuplicates(artist.name, otherArtists);
            if (duplicates.length > 0) {
                const duplicateDetails = duplicates.map(dup => {
                    const original = artists.find(a => a.name === dup.artist);
                    return {
                        name: dup.artist,
                        count: original.count,
                        similarity: dup.similarity,
                        confidence: dup.confidence,
                        reason: dup.reason
                    };
                });
                const totalTracks = artist.count + duplicateDetails.reduce((sum, d) => sum + d.count, 0);
                let suggestedAction = 'ignore';
                const highConfidenceCount = duplicateDetails.filter(d => d.confidence === 'high').length;
                const mediumConfidenceCount = duplicateDetails.filter(d => d.confidence === 'medium').length;
                if (highConfidenceCount > 0) {
                    suggestedAction = 'merge';
                }
                else if (mediumConfidenceCount > 0) {
                    suggestedAction = 'review';
                }
                results.push({
                    canonical: normalized,
                    duplicates: duplicateDetails,
                    totalTracks,
                    suggestedAction
                });
                processed.add(artist.name);
                duplicates.forEach(dup => processed.add(dup.artist));
            }
        }
        return results.sort((a, b) => b.totalTracks - a.totalTracks);
    }
    static analyzeGenreDuplicates(genres) {
        const results = [];
        const processed = new Set();
        for (const genre of genres) {
            if (processed.has(genre.name))
                continue;
            const normalized = GenreNormalizer.normalize(genre.name);
            const otherGenres = genres
                .filter(g => g.name !== genre.name && !processed.has(g.name))
                .map(g => g.name);
            const duplicates = GenreNormalizer.findPotentialDuplicates(genre.name, otherGenres);
            if (duplicates.length > 0) {
                const duplicateDetails = duplicates.map(dup => {
                    const original = genres.find(g => g.name === dup.genre);
                    return {
                        name: dup.genre,
                        count: original.count,
                        confidence: dup.confidence,
                        reason: dup.reason
                    };
                });
                const totalTracks = genre.count + duplicateDetails.reduce((sum, d) => sum + d.count, 0);
                results.push({
                    canonical: normalized,
                    duplicates: duplicateDetails,
                    totalTracks
                });
                processed.add(genre.name);
                duplicates.forEach(dup => processed.add(dup.genre));
            }
        }
        return results.sort((a, b) => b.totalTracks - a.totalTracks);
    }
}
exports.DataQualityService = DataQualityService;
//# sourceMappingURL=data-normalization.js.map