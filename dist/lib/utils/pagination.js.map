{"version": 3, "file": "pagination.js", "sourceRoot": "", "sources": ["../../../lib/utils/pagination.ts"], "names": [], "mappings": ";;;AA4BA,sDAWC;AAKD,4DAmBC;AAKD,sEAQC;AAKD,oCAiBC;AAKD,kDAYC;AAKD,sDAwCC;AA3IY,QAAA,YAAY,GAAG,CAAC,CAAA;AAChB,QAAA,aAAa,GAAG,EAAE,CAAA;AAClB,QAAA,SAAS,GAAG,GAAG,CAAA;AAK5B,SAAgB,qBAAqB,CACnC,MAAwB;IAExB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,IAAI,oBAAY,CAAC,CAAA;IACrD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CACpB,MAAM,CAAC,QAAQ,IAAI,iBAAS,EAC5B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,qBAAa,CAAC,CAC3C,CAAA;IACD,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAA;IAE/B,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;AAC9B,CAAC;AAKD,SAAgB,wBAAwB,CACtC,IAAY,EACZ,KAAa,EACb,KAAa,EACb,YAAoB;IAEpB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAA;IAC3C,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAA;IAE/B,OAAO;QACL,IAAI;QACJ,KAAK;QACL,KAAK;QACL,UAAU;QACV,OAAO,EAAE,IAAI,GAAG,UAAU;QAC1B,WAAW,EAAE,IAAI,GAAG,CAAC;QACrB,IAAI;QACJ,IAAI,EAAE,KAAK;KACZ,CAAA;AACH,CAAC;AAKD,SAAgB,6BAA6B,CAC3C,YAA6B,EAC7B,QAAoC;IAEpC,MAAM,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,QAAQ,EAAE,IAAI,IAAI,oBAAY,CAAA;IACvF,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,IAAI,QAAQ,EAAE,KAAK,IAAI,qBAAa,CAAA;IAE3F,OAAO,qBAAqB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;AAC7E,CAAC;AAKM,KAAK,UAAU,YAAY,CAChC,UAAkB,EAClB,SAAiB,EACjB,SAAuD,EACvD,OAAqC;IAErC,MAAM,OAAO,GAAQ,EAAE,CAAA;IACvB,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,CAAA;IAEtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,IAAI,GAAG,CAAC,GAAG,SAAS,CAAA;QAC1B,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QAC9C,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,CAAA;QACtC,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAA;IAC5B,CAAC;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;AAKD,SAAgB,mBAAmB,CACjC,IAAY,EACZ,KAAa,EACb,OAAa;IAEb,MAAM,EAAE,IAAI,EAAE,GAAG,qBAAqB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAA;IAEvD,OAAO;QACL,IAAI;QACJ,IAAI,EAAE,KAAK;QACX,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;KAC5B,CAAA;AACH,CAAC;AAKD,SAAgB,qBAAqB,CACnC,OAAe,EACf,IAAY,EACZ,UAAkB,EAClB,WAAoC;IAOpC,MAAM,SAAS,GAAG,CAAC,OAAe,EAAE,EAAE;QACpC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAA;QAC5B,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAA;QAEhD,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;gBACnD,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;YAClC,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAA;IACvB,CAAC,CAAA;IAED,MAAM,KAAK,GAAQ,EAAE,CAAA;IAErB,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;QACnB,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;QAC1B,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,CAAA;IACpC,CAAC;IAED,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;QACb,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;IACtC,CAAC;IAED,IAAI,IAAI,GAAG,UAAU,EAAE,CAAC;QACtB,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;IAClC,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC"}