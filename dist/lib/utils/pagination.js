"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MAX_LIMIT = exports.DEFAULT_LIMIT = exports.DEFAULT_PAGE = void 0;
exports.parsePaginationParams = parsePaginationParams;
exports.createPaginationResponse = createPaginationResponse;
exports.getPaginationFromSearchParams = getPaginationFromSearchParams;
exports.batchProcess = batchProcess;
exports.createPrismaOptions = createPrismaOptions;
exports.createPaginationLinks = createPaginationLinks;
exports.DEFAULT_PAGE = 1;
exports.DEFAULT_LIMIT = 20;
exports.MAX_LIMIT = 100;
function parsePaginationParams(params) {
    const page = Math.max(1, params.page || exports.DEFAULT_PAGE);
    const limit = Math.min(params.maxLimit || exports.MAX_LIMIT, Math.max(1, params.limit || exports.DEFAULT_LIMIT));
    const skip = (page - 1) * limit;
    return { page, limit, skip };
}
function createPaginationResponse(page, limit, total, currentCount) {
    const totalPages = Math.ceil(total / limit);
    const skip = (page - 1) * limit;
    return {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrevious: page > 1,
        skip,
        take: limit
    };
}
function getPaginationFromSearchParams(searchParams, defaults) {
    const page = parseInt(searchParams.get('page') || '') || defaults?.page || exports.DEFAULT_PAGE;
    const limit = parseInt(searchParams.get('limit') || '') || defaults?.limit || exports.DEFAULT_LIMIT;
    return parsePaginationParams({ page, limit, maxLimit: defaults?.maxLimit });
}
async function batchProcess(totalCount, batchSize, processor, handler) {
    const results = [];
    const totalBatches = Math.ceil(totalCount / batchSize);
    for (let i = 0; i < totalBatches; i++) {
        const skip = i * batchSize;
        const items = await processor(skip, batchSize);
        const processed = await handler(items);
        results.push(...processed);
    }
    return results;
}
function createPrismaOptions(page, limit, orderBy) {
    const { skip } = parsePaginationParams({ page, limit });
    return {
        skip,
        take: limit,
        ...(orderBy && { orderBy })
    };
}
function createPaginationLinks(baseUrl, page, totalPages, queryParams) {
    const createUrl = (pageNum) => {
        const url = new URL(baseUrl);
        url.searchParams.set('page', pageNum.toString());
        if (queryParams) {
            Object.entries(queryParams).forEach(([key, value]) => {
                url.searchParams.set(key, value);
            });
        }
        return url.toString();
    };
    const links = {};
    if (totalPages > 0) {
        links.first = createUrl(1);
        links.last = createUrl(totalPages);
    }
    if (page > 1) {
        links.previous = createUrl(page - 1);
    }
    if (page < totalPages) {
        links.next = createUrl(page + 1);
    }
    return links;
}
//# sourceMappingURL=pagination.js.map