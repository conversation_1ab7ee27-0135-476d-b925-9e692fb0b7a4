"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDefaultContentFilters = getDefaultContentFilters;
exports.getAllTimeFavoritesFilter = getAllTimeFavoritesFilter;
exports.getMyItunesOnlyFilter = getMyItunesOnlyFilter;
function getDefaultContentFilters() {
    return {
        genres: {
            mode: 'include',
            values: []
        },
        playlists: {
            mode: 'include',
            values: []
        },
        yearRange: {
            enabled: false
        },
        charts: {
            includeChartMusic: true,
            includeNonChartMusic: true
        },
        quality: {
            minPopularity: 0
        },
        sources: {
            includeMyItunes: true,
            includeSharedLibrary: true,
            includePlaylists: []
        },
        metadata: {},
        folders: {
            mode: 'include',
            values: []
        }
    };
}
function getAllTimeFavoritesFilter() {
    const filters = getDefaultContentFilters();
    filters.playlists = {
        mode: 'include',
        values: ['all-time-favorites']
    };
    return filters;
}
function getMyItunesOnlyFilter() {
    const filters = getDefaultContentFilters();
    filters.sources.includeMyItunes = true;
    filters.sources.includeSharedLibrary = false;
    filters.folders = {
        mode: 'include',
        values: ['MyItunes']
    };
    return filters;
}
//# sourceMappingURL=default-filters.js.map