export interface CacheEntry<T = any> {
    data: T;
    timestamp: number;
    ttl: number;
    tags?: string[];
}
export interface CacheOptions {
    ttl: number;
    maxSize: number;
    namespace: string;
    diskCachePath?: string;
    redisUrl?: string;
}
export interface CacheStats {
    hits: number;
    misses: number;
    size: number;
    evictions: number;
}
export declare class ApiCache<T = any> {
    private options;
    private cache;
    private stats;
    constructor(options: CacheOptions);
    get(key: string): Promise<T | null>;
    set(key: string, value: T, options?: {
        ttl?: number;
        tags?: string[];
    }): Promise<void>;
    delete(key: string): Promise<boolean>;
    clear(): Promise<void>;
    clearByTag(tag: string): Promise<number>;
    getStats(): CacheStats;
    private makeKey;
    private evictOldest;
    private loadFromDisk;
    private saveToDisk;
    private updateDiskIndex;
}
export declare class CacheManager {
    private static caches;
    static getCache(service: string, options?: Partial<CacheOptions>): ApiCache;
    static clearAll(): Promise<void>;
    static getStats(): Record<string, CacheStats>;
}
export declare function Cacheable(options: {
    service: string;
    ttl?: number;
    keyGenerator?: (...args: any[]) => string;
}): (target: any, propertyName: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
