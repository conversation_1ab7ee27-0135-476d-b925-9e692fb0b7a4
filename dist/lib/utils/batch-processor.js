"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProgressTracker = exports.BatchProcessor = void 0;
class BatchProcessor {
    static async process(items, processor, options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const startTime = Date.now();
        const results = [];
        const errors = [];
        const batches = this.createBatches(items, config.batchSize);
        let processedCount = 0;
        console.log(`[BatchProcessor] Processing ${items.length} items in ${batches.length} batches (concurrency: ${config.concurrency})`);
        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            const batchStartTime = Date.now();
            console.log(`[BatchProcessor] Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} items)`);
            const batchResults = await this.processBatchWithConcurrency(batch, processor, config.concurrency, config.retryAttempts, config.retryDelay, config.onError);
            results.push(...batchResults.successful);
            errors.push(...batchResults.failed);
            processedCount += batch.length;
            const batchDuration = Date.now() - batchStartTime;
            console.log(`[BatchProcessor] Batch ${batchIndex + 1} completed in ${batchDuration}ms (${batchResults.successful.length} success, ${batchResults.failed.length} errors)`);
            if (config.onProgress) {
                config.onProgress(processedCount, items.length, errors.length);
            }
            if (config.onBatchComplete) {
                config.onBatchComplete(batchIndex, batches.length, batchResults.successful);
            }
            if (batchIndex < batches.length - 1) {
                await this.sleep(200);
            }
        }
        const totalDuration = Date.now() - startTime;
        const avgProcessingTime = results.length > 0 ? totalDuration / results.length : 0;
        const stats = {
            total: items.length,
            successful: results.length,
            failed: errors.length,
            duration: totalDuration,
            avgProcessingTime
        };
        console.log(`[BatchProcessor] Completed processing: ${stats.successful}/${stats.total} successful (${Math.round(stats.successful / stats.total * 100)}%) in ${stats.duration}ms`);
        return { success: results, errors, stats };
    }
    static async processBatchWithConcurrency(items, processor, concurrency, retryAttempts, retryDelay, onError) {
        const successful = [];
        const failed = [];
        const semaphore = new Semaphore(concurrency);
        const promises = items.map(async (item) => {
            await semaphore.acquire();
            try {
                const result = await this.processItemWithRetry(item, processor, retryAttempts, retryDelay, onError);
                successful.push(result);
            }
            catch (error) {
                failed.push({ item, error, attempts: retryAttempts + 1 });
            }
            finally {
                semaphore.release();
            }
        });
        await Promise.all(promises);
        return { successful, failed };
    }
    static async processItemWithRetry(item, processor, retryAttempts, retryDelay, onError) {
        let lastError;
        for (let attempt = 0; attempt <= retryAttempts; attempt++) {
            try {
                return await processor(item);
            }
            catch (error) {
                lastError = error;
                if (onError) {
                    onError(error, item, attempt + 1);
                }
                if (attempt < retryAttempts) {
                    await this.sleep(retryDelay * Math.pow(2, attempt));
                }
            }
        }
        throw lastError;
    }
    static createBatches(items, batchSize) {
        const batches = [];
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }
        return batches;
    }
    static sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.BatchProcessor = BatchProcessor;
BatchProcessor.defaultOptions = {
    batchSize: 10,
    concurrency: 3,
    retryAttempts: 2,
    retryDelay: 1000
};
class Semaphore {
    constructor(permits) {
        this.waiting = [];
        this.permits = permits;
    }
    async acquire() {
        if (this.permits > 0) {
            this.permits--;
            return Promise.resolve();
        }
        return new Promise((resolve) => {
            this.waiting.push(resolve);
        });
    }
    release() {
        this.permits++;
        if (this.waiting.length > 0) {
            const next = this.waiting.shift();
            this.permits--;
            next();
        }
    }
}
class ProgressTracker {
    constructor(total) {
        this.processed = 0;
        this.errors = 0;
        this.lastReportTime = 0;
        this.reportInterval = 5000;
        this.total = total;
        this.startTime = Date.now();
    }
    update(processed, errors = 0) {
        this.processed = processed;
        this.errors = errors;
        const now = Date.now();
        if (now - this.lastReportTime >= this.reportInterval) {
            this.report();
            this.lastReportTime = now;
        }
    }
    report() {
        const elapsed = Date.now() - this.startTime;
        const rate = this.processed / (elapsed / 1000);
        const percentage = (this.processed / this.total) * 100;
        const remaining = this.total - this.processed;
        const eta = remaining / rate;
        console.log(`[Progress] ${this.processed}/${this.total} (${percentage.toFixed(1)}%) | ${rate.toFixed(1)}/s | ETA: ${this.formatDuration(eta * 1000)} | Errors: ${this.errors}`);
    }
    complete() {
        const elapsed = Date.now() - this.startTime;
        const rate = this.processed / (elapsed / 1000);
        console.log(`[Progress] Completed: ${this.processed}/${this.total} in ${this.formatDuration(elapsed)} | Avg: ${rate.toFixed(1)}/s | Errors: ${this.errors}`);
    }
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        }
        else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        }
        else {
            return `${seconds}s`;
        }
    }
}
exports.ProgressTracker = ProgressTracker;
//# sourceMappingURL=batch-processor.js.map