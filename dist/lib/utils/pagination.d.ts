export interface PaginationParams {
    page?: number;
    limit?: number;
    maxLimit?: number;
}
export interface PaginationResult {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
    skip: number;
    take: number;
}
export declare const DEFAULT_PAGE = 1;
export declare const DEFAULT_LIMIT = 20;
export declare const MAX_LIMIT = 100;
export declare function parsePaginationParams(params: PaginationParams): {
    page: number;
    limit: number;
    skip: number;
};
export declare function createPaginationResponse(page: number, limit: number, total: number, currentCount: number): PaginationResult;
export declare function getPaginationFromSearchParams(searchParams: URLSearchParams, defaults?: Partial<PaginationParams>): {
    page: number;
    limit: number;
    skip: number;
};
export declare function batchProcess<T, R>(totalCount: number, batchSize: number, processor: (skip: number, take: number) => Promise<T[]>, handler: (items: T[]) => Promise<R[]>): Promise<R[]>;
export declare function createPrismaOptions(page: number, limit: number, orderBy?: any): any;
export declare function createPaginationLinks(baseUrl: string, page: number, totalPages: number, queryParams?: Record<string, string>): {
    first?: string;
    previous?: string;
    next?: string;
    last?: string;
};
