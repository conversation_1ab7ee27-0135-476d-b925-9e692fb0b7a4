{"version": 3, "file": "text-normalization.js", "sourceRoot": "", "sources": ["../../../lib/utils/text-normalization.ts"], "names": [], "mappings": ";;AAQA,sCAsBC;AAKD,kDAiBC;AAKD,gDAaC;AAKD,kDAqBC;AAKD,kDAcC;AAKD,8DAoBC;AAKD,4DAeC;AAUD,kDAUC;AAKD,gDAsBC;AAKD,gDA0BC;AAKD,wDA+BC;AA1QD,SAAgB,aAAa,CAAC,IAAY;IACxC,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,CAAA;IAEpB,OAAO,IAAI;SACR,WAAW,EAAE;SACb,IAAI,EAAE;SAEN,OAAO,CAAC,uCAAuC,EAAE,GAAG,CAAC;SAErD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;SAEpB,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;SAC9B,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;SAE9B,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC;SAEhD,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC;SAE1B,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC;SAE3B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;SACpB,IAAI,EAAE,CAAA;AACX,CAAC;AAKD,SAAgB,mBAAmB,CAAC,MAAc;IAChD,IAAI,CAAC,MAAM;QAAE,OAAO,EAAE,CAAA;IAEtB,IAAI,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAA;IAGtC,UAAU,GAAG,UAAU;SAEpB,OAAO,CAAC,0BAA0B,EAAE,GAAG,CAAC;SAExC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC;SAC7B,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC;SAC7B,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC;SAE5B,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAA;IAEzC,OAAO,UAAU,CAAC,IAAI,EAAE,CAAA;AAC1B,CAAC;AAKD,SAAgB,kBAAkB,CAAC,KAAa;IAC9C,IAAI,CAAC,KAAK;QAAE,OAAO,EAAE,CAAA;IAErB,IAAI,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,CAAA;IAGrC,UAAU,GAAG,UAAU;SAEpB,OAAO,CAAC,8EAA8E,EAAE,EAAE,CAAC;SAE3F,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAA;IAEnC,OAAO,UAAU,CAAC,IAAI,EAAE,CAAA;AAC1B,CAAC;AAKD,SAAgB,mBAAmB,CAAC,CAAS,EAAE,CAAS;IACtD,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,CAAC,CAAC,MAAM,CAAA;IACnC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,CAAC,CAAC,MAAM,CAAA;IAEnC,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IAEvF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE;QAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;IACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE;QAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;IAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,gBAAgB,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACtD,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CACrB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EACpB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EACpB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB,CACxC,CAAA;QACH,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;AACnC,CAAC;AAKD,SAAgB,mBAAmB,CAAC,CAAS,EAAE,CAAS;IACtD,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QAAE,OAAO,CAAC,CAAA;IACtB,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,CAAC,CAAA;IAErB,MAAM,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;IACpC,MAAM,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;IAEpC,IAAI,WAAW,KAAK,WAAW;QAAE,OAAO,CAAC,CAAA;IAEzC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAA;IAClE,IAAI,SAAS,KAAK,CAAC;QAAE,OAAO,CAAC,CAAA;IAE7B,MAAM,QAAQ,GAAG,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;IAC9D,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAA;AACnC,CAAC;AAKD,SAAgB,yBAAyB,CAAC,CAAS,EAAE,CAAS;IAC5D,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QAAE,OAAO,CAAC,CAAA;IAEtB,MAAM,WAAW,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;IAC1C,MAAM,WAAW,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;IAG1C,IAAI,WAAW,KAAK,WAAW;QAAE,OAAO,CAAC,CAAA;IAGzC,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QAC3E,OAAO,GAAG,CAAA;IACZ,CAAC;IAGD,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACtD,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACtD,IAAI,MAAM,KAAK,MAAM;QAAE,OAAO,IAAI,CAAA;IAElC,OAAO,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;AACtD,CAAC;AAKD,SAAgB,wBAAwB,CAAC,CAAS,EAAE,CAAS;IAC3D,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QAAE,OAAO,CAAC,CAAA;IAEtB,MAAM,WAAW,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAA;IACzC,MAAM,WAAW,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAA;IAGzC,IAAI,WAAW,KAAK,WAAW;QAAE,OAAO,CAAC,CAAA;IAGzC,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QAC3E,OAAO,IAAI,CAAA;IACb,CAAC;IAED,OAAO,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;AACtD,CAAC;AAUD,SAAgB,mBAAmB,CACjC,MAAkB,EAClB,MAAkB,EAClB,YAAoB,IAAI;IAExB,MAAM,gBAAgB,GAAG,yBAAyB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;IAChF,MAAM,eAAe,GAAG,wBAAwB,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA;IAG5E,OAAO,gBAAgB,IAAI,SAAS,IAAI,eAAe,IAAI,SAAS,CAAA;AACtE,CAAC;AAKD,SAAgB,kBAAkB,CAChC,MAAkB,EAClB,UAAwB,EACxB,YAAoB,GAAG;IAEvB,IAAI,SAAS,GAAsB,IAAI,CAAA;IACvC,IAAI,SAAS,GAAG,CAAC,CAAA;IAEjB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACnC,MAAM,WAAW,GAAG,yBAAyB,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,CAAA;QAC9E,MAAM,UAAU,GAAG,wBAAwB,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,CAAA;QAG1E,MAAM,aAAa,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAA;QAE9D,IAAI,aAAa,GAAG,SAAS,IAAI,aAAa,IAAI,SAAS,EAAE,CAAC;YAC5D,SAAS,GAAG,aAAa,CAAA;YACzB,SAAS,GAAG,SAAS,CAAA;QACvB,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;AAClE,CAAC;AAKD,SAAgB,kBAAkB,CAChC,MAAoB,EACpB,YAAoB,IAAI;IAExB,MAAM,MAAM,GAAmB,EAAE,CAAA;IACjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAA;IAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAAE,SAAQ;QAE9B,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QACzB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;QAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;gBAAE,SAAQ;YAE9B,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC;gBACzD,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;gBACrB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;YAClB,CAAC;QACH,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACpB,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAKD,SAAgB,sBAAsB,CAAC,IAAY;IACjD,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,CAAA;IAEpB,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAA;IAGpC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAGpB,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;IAGnC,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAA;IAC1F,UAAU,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IAC/B,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAA;IAG9C,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;IAC3D,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QAC3B,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;QAC7B,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAA;IAC9C,CAAC;IAGD,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;IAChF,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;QACzB,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;QAC3B,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAA;IAC5C,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;AACzD,CAAC"}