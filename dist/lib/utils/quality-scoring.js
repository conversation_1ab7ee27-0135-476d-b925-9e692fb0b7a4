"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QualityScorer = void 0;
const data_normalization_1 = require("./data-normalization");
const text_normalization_1 = require("./text-normalization");
class QualityScorer {
    static calculateScore(track) {
        const breakdown = this.analyzeTrack(track);
        const weights = {
            core: 0.4,
            extended: 0.15,
            enrichment: 0.25,
            accuracy: 0.15,
            audio: 0.05
        };
        const overall = Math.round(breakdown.core.score * weights.core +
            breakdown.extended.score * weights.extended +
            breakdown.enrichment.score * weights.enrichment +
            breakdown.accuracy.overallAccuracy * weights.accuracy +
            breakdown.audio.score * weights.audio);
        return {
            overall: Math.max(0, Math.min(100, overall)),
            metadataCompleteness: Math.round((breakdown.core.score + breakdown.extended.score) / 2),
            metadataAccuracy: Math.round(breakdown.accuracy.overallAccuracy),
            enrichmentLevel: Math.round(breakdown.enrichment.score),
            audioQuality: Math.round(breakdown.audio.score),
            breakdown
        };
    }
    static analyzeTrack(track) {
        const issues = [];
        const core = this.analyzeCoreMetadata(track, issues);
        const extended = this.analyzeExtendedMetadata(track, issues);
        const enrichment = this.analyzeEnrichment(track, issues);
        const accuracy = this.analyzeAccuracy(track, issues);
        const audio = this.analyzeAudioQuality(track, issues);
        return {
            core,
            extended,
            enrichment,
            accuracy,
            audio,
            issues
        };
    }
    static analyzeCoreMetadata(track, issues) {
        const hasTitle = this.hasValidValue(track.title);
        const hasArtist = this.hasValidValue(track.artist);
        const hasAlbum = this.hasValidValue(track.album);
        const hasYear = track.year !== null && track.year > 1800 && track.year <= new Date().getFullYear();
        const hasGenre = this.hasValidValue(track.genre) && track.genre !== 'Unknown';
        const hasDuration = track.duration > 0;
        if (!hasTitle) {
            issues.push({
                type: 'missing',
                field: 'title',
                severity: 'high',
                description: 'Track title is missing or empty'
            });
        }
        if (!hasArtist) {
            issues.push({
                type: 'missing',
                field: 'artist',
                severity: 'high',
                description: 'Artist name is missing or empty'
            });
        }
        if (!hasAlbum) {
            issues.push({
                type: 'missing',
                field: 'album',
                severity: 'medium',
                description: 'Album name is missing',
                suggestion: 'Consider extracting album from folder structure'
            });
        }
        if (!hasYear) {
            issues.push({
                type: 'missing',
                field: 'year',
                severity: 'medium',
                description: 'Release year is missing or invalid',
                suggestion: 'Check chart data or external APIs for year information'
            });
        }
        if (!hasGenre) {
            issues.push({
                type: 'missing',
                field: 'genre',
                severity: 'low',
                description: 'Genre information is missing',
                suggestion: 'Use folder structure or external APIs for genre detection'
            });
        }
        if (!hasDuration) {
            issues.push({
                type: 'missing',
                field: 'duration',
                severity: 'medium',
                description: 'Track duration is missing or zero'
            });
        }
        const coreFields = [hasTitle, hasArtist, hasAlbum, hasYear, hasGenre, hasDuration];
        const score = (coreFields.filter(Boolean).length / coreFields.length) * 100;
        return {
            hasTitle,
            hasArtist,
            hasAlbum,
            hasYear,
            hasGenre,
            hasDuration,
            score
        };
    }
    static analyzeExtendedMetadata(track, issues) {
        const hasChartData = track.chartPosition !== null || this.hasValidValue(track.chartCountry);
        const hasCategories = this.hasValidJsonArray(track.quizCategories);
        const hasReleaseDate = track.releaseDate !== null;
        const extendedFields = [hasChartData, hasCategories, hasReleaseDate];
        const score = extendedFields.length > 0 ? (extendedFields.filter(Boolean).length / extendedFields.length) * 100 : 0;
        return {
            hasChartData,
            hasCategories,
            hasReleaseDate,
            score
        };
    }
    static analyzeEnrichment(track, issues) {
        const hasSpotifyId = this.hasValidValue(track.spotifyId);
        const hasMusicBrainzId = this.hasValidValue(track.musicbrainzId);
        const hasAlbumArt = this.hasValidValue(track.albumArtUrl);
        const hasArtistImage = this.hasValidValue(track.artistImageUrl);
        const hasTriviaFacts = this.hasValidJsonArray(track.triviaFacts);
        const hasSimilarArtists = this.hasValidJsonArray(track.similarArtists);
        const hasThematicTags = this.hasValidJsonArray(track.thematicTags);
        if (!hasSpotifyId && !hasMusicBrainzId) {
            issues.push({
                type: 'missing',
                field: 'external_ids',
                severity: 'low',
                description: 'No external service IDs found',
                suggestion: 'Run enrichment process to add Spotify/MusicBrainz IDs'
            });
        }
        if (!hasAlbumArt && !hasArtistImage) {
            issues.push({
                type: 'missing',
                field: 'images',
                severity: 'low',
                description: 'No album art or artist images found',
                suggestion: 'External APIs can provide artwork'
            });
        }
        const enrichmentFields = [
            hasSpotifyId,
            hasMusicBrainzId,
            hasAlbumArt,
            hasArtistImage,
            hasTriviaFacts,
            hasSimilarArtists,
            hasThematicTags
        ];
        const score = (enrichmentFields.filter(Boolean).length / enrichmentFields.length) * 100;
        return {
            hasSpotifyId,
            hasMusicBrainzId,
            hasAlbumArt,
            hasArtistImage,
            hasTriviaFacts,
            hasSimilarArtists,
            hasThematicTags,
            score
        };
    }
    static analyzeAccuracy(track, issues) {
        const titleQuality = this.assessTextQuality(track.title, 'title');
        const artistQuality = this.assessTextQuality(track.artist, 'artist');
        const genreQuality = this.assessGenreQuality(track.genre);
        const yearQuality = this.assessYearQuality(track.year);
        if (track.title && this.isSuspiciousText(track.title)) {
            issues.push({
                type: 'suspicious',
                field: 'title',
                severity: 'medium',
                description: 'Title contains suspicious patterns (numbers, underscores, etc.)',
                suggestion: 'Consider parsing filename or using external APIs'
            });
        }
        if (track.artist && this.isSuspiciousText(track.artist)) {
            issues.push({
                type: 'suspicious',
                field: 'artist',
                severity: 'medium',
                description: 'Artist name contains suspicious patterns',
                suggestion: 'Check for filename parsing artifacts'
            });
        }
        if (track.title && track.mpdFilePath) {
            const filenameSimilarity = this.calculateFilenameSimilarity(track.title, track.mpdFilePath);
            if (filenameSimilarity < 0.3) {
                issues.push({
                    type: 'inconsistent',
                    field: 'title',
                    severity: 'low',
                    description: 'Title doesn\'t match filename pattern',
                    suggestion: 'Verify title accuracy'
                });
            }
        }
        const overallAccuracy = (titleQuality + artistQuality + genreQuality + yearQuality) / 4;
        return {
            titleQuality,
            artistQuality,
            genreQuality,
            yearQuality,
            overallAccuracy
        };
    }
    static analyzeAudioQuality(track, issues) {
        const hasFingerprint = this.hasValidValue(track.fileFingerprint);
        const bitrate = null;
        const format = track.mpdFilePath ? track.mpdFilePath.split('.').pop()?.toLowerCase() || 'unknown' : 'unknown';
        const fileSize = track.fileSize || 0;
        let score = 50;
        if (hasFingerprint)
            score += 20;
        if (format === 'flac')
            score += 30;
        else if (format === 'mp3')
            score += 10;
        else if (format === 'wav')
            score += 25;
        if (fileSize > 5 * 1024 * 1024)
            score += 10;
        if (fileSize < 1 * 1024 * 1024) {
            score -= 20;
            issues.push({
                type: 'poor_quality',
                field: 'file_size',
                severity: 'medium',
                description: 'File size is unusually small, may indicate low quality audio'
            });
        }
        return {
            hasFingerprint,
            bitrate,
            format,
            fileSize,
            score: Math.max(0, Math.min(100, score))
        };
    }
    static assessTextQuality(text, field) {
        if (!text || text.trim() === '')
            return 0;
        let score = 70;
        if (this.hasProperCapitalization(text))
            score += 15;
        if (this.isSuspiciousText(text))
            score -= 30;
        if (text.length > 3 && text.length < 100)
            score += 10;
        if (text.includes('_') || text.includes('.mp3'))
            score -= 20;
        return Math.max(0, Math.min(100, score));
    }
    static assessGenreQuality(genre) {
        if (!genre || genre === 'Unknown')
            return 0;
        let score = 60;
        const normalizedGenre = data_normalization_1.GenreNormalizer.normalize(genre);
        if (normalizedGenre !== genre)
            score += 20;
        if (genre.includes('/') || genre.includes('-'))
            score += 10;
        return Math.min(100, score);
    }
    static assessYearQuality(year) {
        if (!year)
            return 0;
        const currentYear = new Date().getFullYear();
        if (year < 1900 || year > currentYear)
            return 0;
        let score = 80;
        if (year > 1950)
            score += 10;
        if (year > 1980)
            score += 10;
        return score;
    }
    static hasProperCapitalization(text) {
        const words = text.split(' ');
        const capitalizedWords = words.filter(word => word.length > 0 &&
            (word[0] === word[0].toUpperCase() ||
                ['and', 'or', 'the', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'of', 'with'].includes(word.toLowerCase())));
        return capitalizedWords.length / words.length > 0.7;
    }
    static isSuspiciousText(text) {
        const suspiciousPatterns = [
            /^\d+[-_\s]/,
            /_/g,
            /\.(mp3|flac|wav)$/i,
            /^track\s*\d+/i,
            /^untitled/i,
            /^unknown/i,
            /[{}[\]]/,
            /\s{2,}/
        ];
        return suspiciousPatterns.some(pattern => pattern.test(text));
    }
    static calculateFilenameSimilarity(title, filePath) {
        const filename = filePath.split('/').pop()?.replace(/\.[^.]+$/, '') || '';
        const cleanFilename = filename.replace(/^\d+[-_\s]*/, '').replace(/_/g, ' ');
        return (0, text_normalization_1.calculateSimilarity)(title.toLowerCase(), cleanFilename.toLowerCase());
    }
    static hasValidValue(value) {
        if (!value)
            return false;
        const trimmed = value.trim().toLowerCase();
        const invalidValues = [
            '', 'unknown', 'unknown artist', 'unknown album', 'unknown title',
            'untitled', 'no title', 'no artist', 'no album', 'n/a', 'na', 'null', 'undefined'
        ];
        return !invalidValues.includes(trimmed);
    }
    static hasValidJsonArray(jsonString) {
        if (!jsonString)
            return false;
        try {
            const parsed = JSON.parse(jsonString);
            return Array.isArray(parsed) && parsed.length > 0;
        }
        catch {
            return false;
        }
    }
    static analyzeCollection(tracks) {
        const scores = tracks.map(track => this.calculateScore(track));
        const totalTracks = tracks.length;
        const averageScore = scores.reduce((sum, score) => sum + score.overall, 0) / totalTracks;
        const distribution = {
            excellent: scores.filter(s => s.overall >= 90).length,
            good: scores.filter(s => s.overall >= 70 && s.overall < 90).length,
            fair: scores.filter(s => s.overall >= 50 && s.overall < 70).length,
            poor: scores.filter(s => s.overall >= 30 && s.overall < 50).length,
            critical: scores.filter(s => s.overall < 30).length
        };
        const issueCount = new Map();
        scores.forEach(score => {
            score.breakdown.issues.forEach(issue => {
                const key = `${issue.type}_${issue.field}`;
                issueCount.set(key, (issueCount.get(key) || 0) + 1);
            });
        });
        const commonIssues = Array.from(issueCount.entries())
            .map(([issue, count]) => ({
            issue: issue.replace('_', ' in '),
            count,
            percentage: (count / totalTracks) * 100
        }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
        const recommendations = [];
        if (averageScore < 60) {
            recommendations.push('Consider running the enrichment process to improve metadata quality');
        }
        if (distribution.critical > totalTracks * 0.1) {
            recommendations.push('Many tracks have critical quality issues - prioritize metadata cleanup');
        }
        const missingDataIssues = commonIssues.filter(i => i.issue.includes('missing'));
        if (missingDataIssues.length > 0) {
            recommendations.push('Focus on filling missing core metadata (title, artist, album, year)');
        }
        if (commonIssues.some(i => i.issue.includes('suspicious'))) {
            recommendations.push('Review tracks with suspicious patterns - they may need filename parsing fixes');
        }
        return {
            totalTracks,
            averageScore: Math.round(averageScore),
            distribution,
            commonIssues,
            recommendations
        };
    }
}
exports.QualityScorer = QualityScorer;
//# sourceMappingURL=quality-scoring.js.map