export interface ReleaseDateSource {
    date: string | Date | null;
    year: number | null;
    source: 'id3_tags' | 'musicbrainz' | 'spotify' | 'lastfm' | 'chart_data' | 'folder_structure' | 'filename';
    confidence: 'high' | 'medium' | 'low';
    metadata?: any;
}
export interface ReleaseDateResult {
    finalDate: Date | null;
    finalYear: number | null;
    confidence: 'high' | 'medium' | 'low';
    sources: ReleaseDateSource[];
    reasoning: string[];
    warnings: string[];
}
export declare class ReleaseDateValidator {
    static validateReleaseDate(sources: ReleaseDateSource[]): ReleaseDateResult;
    private static selectBestYear;
    private static extractYear;
    static createMusicBrainzSource(data: any): ReleaseDateSource;
    static createSpotifySource(data: any): ReleaseDateSource;
    static createLastFmSource(data: any): ReleaseDateSource;
    static createChartDataSource(chartInfo: any): ReleaseDateSource;
    static createId3TagsSource(data: any): ReleaseDateSource;
    static createFolderSource(folderMetadata: any): ReleaseDateSource;
    static validateYear(year: number | null): {
        isValid: boolean;
        warnings: string[];
        confidence: 'high' | 'medium' | 'low';
    };
}
