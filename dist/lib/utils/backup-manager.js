"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackupManager = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class BackupManager {
    constructor(backupDir = './backups') {
        this.backupDir = backupDir;
        this.ensureBackupDir();
    }
    async createBackup(description = 'Pre-import backup') {
        const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const timestamp = new Date().toISOString();
        console.log(`[BackupManager] Creating backup: ${backupId}`);
        try {
            const [trackCount, artistCount, albumCount] = await Promise.all([
                prisma_1.default.quizTrack.count(),
                prisma_1.default.artist.count(),
                prisma_1.default.album.count()
            ]);
            const batchSize = 1000;
            const tracks = [];
            const artists = [];
            const albums = [];
            for (let i = 0; i < Math.ceil(trackCount / batchSize); i++) {
                const batch = await prisma_1.default.quizTrack.findMany({
                    skip: i * batchSize,
                    take: batchSize
                });
                tracks.push(...batch);
            }
            for (let i = 0; i < Math.ceil(artistCount / batchSize); i++) {
                const batch = await prisma_1.default.artist.findMany({
                    skip: i * batchSize,
                    take: batchSize
                });
                artists.push(...batch);
            }
            for (let i = 0; i < Math.ceil(albumCount / batchSize); i++) {
                const batch = await prisma_1.default.album.findMany({
                    skip: i * batchSize,
                    take: batchSize
                });
                albums.push(...batch);
            }
            const backupData = {
                metadata: {
                    id: backupId,
                    timestamp,
                    description,
                    tablesCounted: {
                        quizTracks: trackCount,
                        artists: artistCount,
                        albums: albumCount
                    }
                },
                data: {
                    quizTracks: tracks,
                    artists: artists,
                    albums: albums
                }
            };
            const backupPath = path.join(this.backupDir, `${backupId}.json`);
            const jsonData = JSON.stringify(backupData, null, 2);
            fs.writeFileSync(backupPath, jsonData);
            const metadata = {
                id: backupId,
                timestamp,
                description,
                tablesCounted: backupData.metadata.tablesCounted,
                size: Buffer.byteLength(jsonData, 'utf8'),
                checksum: require('crypto').createHash('md5').update(jsonData).digest('hex')
            };
            fs.writeFileSync(path.join(this.backupDir, `${backupId}.meta.json`), JSON.stringify(metadata, null, 2));
            console.log(`[BackupManager] Backup created: ${backupId} (${Math.round(metadata.size / 1024)}KB)`);
            return backupId;
        }
        catch (error) {
            console.error(`[BackupManager] Failed to create backup:`, error);
            throw error;
        }
    }
    listBackups() {
        const backups = [];
        if (!fs.existsSync(this.backupDir))
            return backups;
        const files = fs.readdirSync(this.backupDir);
        for (const file of files) {
            if (file.endsWith('.meta.json')) {
                try {
                    const metaPath = path.join(this.backupDir, file);
                    const metadata = JSON.parse(fs.readFileSync(metaPath, 'utf-8'));
                    backups.push(metadata);
                }
                catch (error) {
                    console.warn(`[BackupManager] Failed to read backup metadata: ${file}`);
                }
            }
        }
        return backups.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    }
    async restoreBackup(backupId) {
        const backupPath = path.join(this.backupDir, `${backupId}.json`);
        const metaPath = path.join(this.backupDir, `${backupId}.meta.json`);
        if (!fs.existsSync(backupPath) || !fs.existsSync(metaPath)) {
            throw new Error(`Backup not found: ${backupId}`);
        }
        console.log(`[BackupManager] Restoring backup: ${backupId}`);
        try {
            const backupData = JSON.parse(fs.readFileSync(backupPath, 'utf-8'));
            const metadata = JSON.parse(fs.readFileSync(metaPath, 'utf-8'));
            const currentChecksum = require('crypto')
                .createHash('md5')
                .update(JSON.stringify(backupData, null, 2))
                .digest('hex');
            if (currentChecksum !== metadata.checksum) {
                throw new Error('Backup file corrupted - checksum mismatch');
            }
            console.log('[BackupManager] Clearing existing data...');
            await prisma_1.default.$transaction([
                prisma_1.default.gameAnswer.deleteMany(),
                prisma_1.default.quizTrack.deleteMany(),
                prisma_1.default.artist.deleteMany(),
                prisma_1.default.album.deleteMany()
            ]);
            console.log('[BackupManager] Restoring data...');
            const { quizTracks, artists, albums } = backupData.data;
            if (artists?.length > 0) {
                await prisma_1.default.artist.createMany({ data: artists });
            }
            if (albums?.length > 0) {
                await prisma_1.default.album.createMany({ data: albums });
            }
            if (quizTracks?.length > 0) {
                await prisma_1.default.quizTrack.createMany({ data: quizTracks });
            }
            console.log(`[BackupManager] Successfully restored backup: ${backupId}`);
            console.log(`  - Quiz Tracks: ${quizTracks?.length || 0}`);
            console.log(`  - Artists: ${artists?.length || 0}`);
            console.log(`  - Albums: ${albums?.length || 0}`);
        }
        catch (error) {
            console.error(`[BackupManager] Failed to restore backup:`, error);
            throw error;
        }
    }
    deleteBackup(backupId) {
        const backupPath = path.join(this.backupDir, `${backupId}.json`);
        const metaPath = path.join(this.backupDir, `${backupId}.meta.json`);
        if (fs.existsSync(backupPath)) {
            fs.unlinkSync(backupPath);
        }
        if (fs.existsSync(metaPath)) {
            fs.unlinkSync(metaPath);
        }
        console.log(`[BackupManager] Deleted backup: ${backupId}`);
    }
    getBackupInfo(backupId) {
        const metaPath = path.join(this.backupDir, `${backupId}.meta.json`);
        if (!fs.existsSync(metaPath)) {
            return null;
        }
        try {
            return JSON.parse(fs.readFileSync(metaPath, 'utf-8'));
        }
        catch (error) {
            return null;
        }
    }
    cleanupOldBackups(keepCount = 5) {
        const backups = this.listBackups();
        if (backups.length <= keepCount)
            return;
        const toDelete = backups.slice(keepCount);
        for (const backup of toDelete) {
            try {
                this.deleteBackup(backup.id);
            }
            catch (error) {
                console.warn(`[BackupManager] Failed to delete old backup ${backup.id}:`, error);
            }
        }
        console.log(`[BackupManager] Cleaned up ${toDelete.length} old backups`);
    }
    ensureBackupDir() {
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
        }
    }
}
exports.BackupManager = BackupManager;
//# sourceMappingURL=backup-manager.js.map