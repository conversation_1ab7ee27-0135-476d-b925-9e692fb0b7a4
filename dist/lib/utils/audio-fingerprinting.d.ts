export interface AudioFingerprint {
    acousticId?: string;
    spectralHash: string;
    durationMs: number;
    fileHash: string;
    bitrate?: number;
    sampleRate?: number;
    channels?: number;
    format: string;
    confidence: number;
}
export interface AudioDuplicate {
    originalFile: string;
    duplicateFile: string;
    similarity: number;
    matchType: 'identical' | 'very_similar' | 'similar' | 'possible';
    reasons: string[];
    fileComparison: {
        sizeDifference: number;
        bitrateComparison: string;
        formatComparison: string;
    };
}
export interface AudioAnalysis {
    bpm?: number;
    key?: string;
    energy: number;
    danceability: number;
    loudness: number;
    speechiness: number;
    acousticness: number;
    instrumentalness: number;
    liveness: number;
    valence: number;
}
export declare class AudioFingerprintService {
    private chromaprintPath;
    private ffmpegPath;
    constructor();
    private findExecutable;
    generateFingerprint(filePath: string): Promise<AudioFingerprint | null>;
    private generateAcoustIDFingerprint;
    private generateSpectralHash;
    private extractSpectralFeatures;
    private generateFileHash;
    private getAudioInfo;
    static compareFingerprints(fp1: AudioFingerprint, fp2: AudioFingerprint, file1: string, file2: string): AudioDuplicate | null;
    private static compareAcoustIDs;
    static findDuplicates(fingerprints: Array<{
        filePath: string;
        fingerprint: AudioFingerprint;
    }>): AudioDuplicate[];
    static checkRequirements(): Promise<{
        chromaprint: boolean;
        ffmpeg: boolean;
        ffprobe: boolean;
        recommendations: string[];
    }>;
    private static checkCommand;
}
