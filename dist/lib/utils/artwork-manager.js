"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArtworkManager = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const crypto = __importStar(require("crypto"));
const sharp = require('sharp');
class ArtworkManager {
    constructor(baseDir = '/media/images') {
        this.baseDir = baseDir;
        this.sizes = {
            thumbnail: { width: 150, height: 150 },
            medium: { width: 400, height: 400 },
            large: { width: 800, height: 800 }
        };
        this.ensureDirectories();
    }
    ensureDirectories() {
        try {
            const dirs = [
                this.baseDir,
                path.join(this.baseDir, 'album'),
                path.join(this.baseDir, 'artist'),
                path.join(this.baseDir, 'album', 'thumbnails'),
                path.join(this.baseDir, 'album', 'medium'),
                path.join(this.baseDir, 'album', 'large'),
                path.join(this.baseDir, 'album', 'original'),
                path.join(this.baseDir, 'artist', 'thumbnails'),
                path.join(this.baseDir, 'artist', 'medium'),
                path.join(this.baseDir, 'artist', 'large'),
                path.join(this.baseDir, 'artist', 'original')
            ];
            for (const dir of dirs) {
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
            }
        }
        catch (error) {
            console.warn(`[ArtworkManager] Could not create directories: ${error instanceof Error ? error.message : 'Unknown error'}`);
            console.warn(`[ArtworkManager] Artwork features will be disabled. Ensure ${this.baseDir} directory exists and is writable.`);
        }
    }
    async processAlbumArt(albumArtUrl, artist, album, trackId) {
        try {
            if (!albumArtUrl) {
                return {
                    success: false,
                    paths: { thumbnail: null, medium: null, large: null, original: null },
                    hash: null,
                    error: 'No album art URL provided'
                };
            }
            const filename = this.generateFilename(artist, album, trackId);
            const hash = this.generateHash(albumArtUrl + artist + album);
            const existingPaths = this.getArtworkPaths('album', filename);
            if (this.allPathsExist(existingPaths)) {
                return {
                    success: true,
                    paths: existingPaths,
                    hash,
                    metadata: {
                        originalUrl: albumArtUrl,
                        format: 'cached',
                        fileSize: 0
                    }
                };
            }
            const originalBuffer = await this.downloadImage(albumArtUrl);
            const originalMetadata = await this.getImageMetadata(originalBuffer);
            const originalPath = path.join(this.baseDir, 'album', 'original', `${filename}.${originalMetadata.format}`);
            fs.writeFileSync(originalPath, originalBuffer);
            const paths = await this.generateScaledVersions(originalBuffer, 'album', filename, originalMetadata.format);
            return {
                success: true,
                paths: {
                    ...paths,
                    original: `album/original/${filename}.${originalMetadata.format}`
                },
                hash,
                metadata: {
                    originalUrl: albumArtUrl,
                    originalSize: { width: originalMetadata.width, height: originalMetadata.height },
                    format: originalMetadata.format,
                    fileSize: originalBuffer.length
                }
            };
        }
        catch (error) {
            console.error(`[ArtworkManager] Failed to process album art for ${artist} - ${album}:`, error);
            return {
                success: false,
                paths: { thumbnail: null, medium: null, large: null, original: null },
                hash: null,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async processArtistImage(artistImageUrl, artist) {
        try {
            if (!artistImageUrl) {
                return {
                    success: false,
                    paths: { thumbnail: null, medium: null, large: null, original: null },
                    hash: null,
                    error: 'No artist image URL provided'
                };
            }
            const filename = this.generateFilename(artist);
            const hash = this.generateHash(artistImageUrl + artist);
            const existingPaths = this.getArtworkPaths('artist', filename);
            if (this.allPathsExist(existingPaths)) {
                return {
                    success: true,
                    paths: existingPaths,
                    hash,
                    metadata: {
                        originalUrl: artistImageUrl,
                        format: 'cached',
                        fileSize: 0
                    }
                };
            }
            const originalBuffer = await this.downloadImage(artistImageUrl);
            const originalMetadata = await this.getImageMetadata(originalBuffer);
            const originalPath = path.join(this.baseDir, 'artist', 'original', `${filename}.${originalMetadata.format}`);
            fs.writeFileSync(originalPath, originalBuffer);
            const paths = await this.generateScaledVersions(originalBuffer, 'artist', filename, originalMetadata.format);
            return {
                success: true,
                paths: {
                    ...paths,
                    original: `artist/original/${filename}.${originalMetadata.format}`
                },
                hash,
                metadata: {
                    originalUrl: artistImageUrl,
                    originalSize: { width: originalMetadata.width, height: originalMetadata.height },
                    format: originalMetadata.format,
                    fileSize: originalBuffer.length
                }
            };
        }
        catch (error) {
            console.error(`[ArtworkManager] Failed to process artist image for ${artist}:`, error);
            return {
                success: false,
                paths: { thumbnail: null, medium: null, large: null, original: null },
                hash: null,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async downloadImage(url) {
        const response = await fetch(url, {
            headers: {
                'User-Agent': 'MusicQuiz/1.0 (+https://github.com/your-repo/music-quiz)'
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const contentType = response.headers.get('content-type') || '';
        if (!contentType.startsWith('image/')) {
            throw new Error(`Invalid content type: ${contentType}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        return Buffer.from(arrayBuffer);
    }
    async getImageMetadata(buffer) {
        try {
            if (sharp) {
                const metadata = await sharp(buffer).metadata();
                return {
                    width: metadata.width || 0,
                    height: metadata.height || 0,
                    format: metadata.format || 'jpg'
                };
            }
            else {
                return {
                    width: 0,
                    height: 0,
                    format: 'jpg'
                };
            }
        }
        catch (error) {
            console.warn('[ArtworkManager] Failed to get image metadata, using defaults:', error);
            return {
                width: 0,
                height: 0,
                format: 'jpg'
            };
        }
    }
    async generateScaledVersions(originalBuffer, type, filename, format) {
        const paths = {
            thumbnail: null,
            medium: null,
            large: null
        };
        try {
            if (!sharp) {
                console.warn('[ArtworkManager] Sharp not available, skipping image scaling');
                return paths;
            }
            try {
                const thumbnailBuffer = await sharp(originalBuffer)
                    .resize(this.sizes.thumbnail.width, this.sizes.thumbnail.height, {
                    fit: 'cover',
                    position: 'center'
                })
                    .jpeg({ quality: 85 })
                    .toBuffer();
                const thumbnailPath = path.join(this.baseDir, type, 'thumbnails', `${filename}.jpg`);
                fs.writeFileSync(thumbnailPath, thumbnailBuffer);
                paths.thumbnail = `${type}/thumbnails/${filename}.jpg`;
            }
            catch (error) {
                console.warn(`[ArtworkManager] Failed to generate thumbnail for ${filename}:`, error);
            }
            try {
                const mediumBuffer = await sharp(originalBuffer)
                    .resize(this.sizes.medium.width, this.sizes.medium.height, {
                    fit: 'inside',
                    withoutEnlargement: true
                })
                    .jpeg({ quality: 90 })
                    .toBuffer();
                const mediumPath = path.join(this.baseDir, type, 'medium', `${filename}.jpg`);
                fs.writeFileSync(mediumPath, mediumBuffer);
                paths.medium = `${type}/medium/${filename}.jpg`;
            }
            catch (error) {
                console.warn(`[ArtworkManager] Failed to generate medium size for ${filename}:`, error);
            }
            try {
                const largeBuffer = await sharp(originalBuffer)
                    .resize(this.sizes.large.width, this.sizes.large.height, {
                    fit: 'inside',
                    withoutEnlargement: true
                })
                    .jpeg({ quality: 95 })
                    .toBuffer();
                const largePath = path.join(this.baseDir, type, 'large', `${filename}.jpg`);
                fs.writeFileSync(largePath, largeBuffer);
                paths.large = `${type}/large/${filename}.jpg`;
            }
            catch (error) {
                console.warn(`[ArtworkManager] Failed to generate large size for ${filename}:`, error);
            }
        }
        catch (error) {
            console.error(`[ArtworkManager] Failed to process image scaling for ${filename}:`, error);
        }
        return paths;
    }
    generateFilename(artist, album, trackId) {
        let parts = [artist];
        if (album)
            parts.push(album);
        if (trackId)
            parts.push(trackId);
        const combined = parts.join(' - ');
        return combined
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '_')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '')
            .substring(0, 100);
    }
    generateHash(input) {
        return crypto.createHash('md5').update(input).digest('hex');
    }
    getArtworkPaths(type, filename) {
        return {
            thumbnail: `${type}/thumbnails/${filename}.jpg`,
            medium: `${type}/medium/${filename}.jpg`,
            large: `${type}/large/${filename}.jpg`,
            original: null
        };
    }
    allPathsExist(paths) {
        const pathsToCheck = [paths.thumbnail, paths.medium, paths.large].filter(Boolean);
        return pathsToCheck.length > 0 && pathsToCheck.every(relativePath => {
            const fullPath = path.join(this.baseDir, relativePath);
            return fs.existsSync(fullPath);
        });
    }
    getFullPath(relativePath) {
        return path.join(this.baseDir, relativePath);
    }
    async cleanupOrphanedArtwork(validTracks) {
        const validPaths = new Set();
        const errors = [];
        let deletedFiles = 0;
        for (const track of validTracks) {
            if (track.albumArtPath)
                validPaths.add(track.albumArtPath);
            if (track.artistImagePath)
                validPaths.add(track.artistImagePath);
        }
        for (const subdir of ['thumbnails', 'medium', 'large', 'original']) {
            const albumDir = path.join(this.baseDir, 'album', subdir);
            if (fs.existsSync(albumDir)) {
                try {
                    const files = fs.readdirSync(albumDir);
                    for (const file of files) {
                        const relativePath = `album/${subdir}/${file}`;
                        if (!validPaths.has(relativePath)) {
                            fs.unlinkSync(path.join(albumDir, file));
                            deletedFiles++;
                        }
                    }
                }
                catch (error) {
                    errors.push(`Failed to clean album/${subdir}: ${error}`);
                }
            }
        }
        for (const subdir of ['thumbnails', 'medium', 'large', 'original']) {
            const artistDir = path.join(this.baseDir, 'artist', subdir);
            if (fs.existsSync(artistDir)) {
                try {
                    const files = fs.readdirSync(artistDir);
                    for (const file of files) {
                        const relativePath = `artist/${subdir}/${file}`;
                        if (!validPaths.has(relativePath)) {
                            fs.unlinkSync(path.join(artistDir, file));
                            deletedFiles++;
                        }
                    }
                }
                catch (error) {
                    errors.push(`Failed to clean artist/${subdir}: ${error}`);
                }
            }
        }
        return { deletedFiles, errors };
    }
    getStorageStats() {
        const stats = {
            totalFiles: 0,
            totalSize: 0,
            byType: {}
        };
        const types = ['album', 'artist'];
        const sizes = ['thumbnails', 'medium', 'large', 'original'];
        for (const type of types) {
            for (const size of sizes) {
                const dir = path.join(this.baseDir, type, size);
                const key = `${type}_${size}`;
                stats.byType[key] = { files: 0, size: 0 };
                if (fs.existsSync(dir)) {
                    try {
                        const files = fs.readdirSync(dir);
                        for (const file of files) {
                            const filePath = path.join(dir, file);
                            const fileStat = fs.statSync(filePath);
                            stats.byType[key].files++;
                            stats.byType[key].size += fileStat.size;
                            stats.totalFiles++;
                            stats.totalSize += fileStat.size;
                        }
                    }
                    catch (error) {
                        console.warn(`[ArtworkManager] Failed to get stats for ${dir}:`, error);
                    }
                }
            }
        }
        return stats;
    }
    static checkSharpAvailability() {
        try {
            require('sharp');
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.ArtworkManager = ArtworkManager;
//# sourceMappingURL=artwork-manager.js.map