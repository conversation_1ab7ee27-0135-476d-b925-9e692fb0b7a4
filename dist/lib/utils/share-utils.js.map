{"version": 3, "file": "share-utils.js", "sourceRoot": "", "sources": ["../../../lib/utils/share-utils.ts"], "names": [], "mappings": ";;;;;AASA,0CAEC;AAKD,8CAgBC;AAYD,kDAoCC;AAKD,4CAGC;AAKD,sCAeC;AAYD,gEA4CC;AAKD,4CAUC;AAKD,sDAiBC;AArMD,oDAA2B;AAK3B,SAAgB,eAAe;IAC7B,OAAO,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;AACrD,CAAC;AAKD,SAAgB,iBAAiB;IAC/B,MAAM,UAAU,GAAG;QACjB,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;QACrE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;KACrE,CAAA;IAED,MAAM,KAAK,GAAG;QACZ,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM;QAClE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM;KACxE,CAAA;IAED,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAA;IAC3E,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAA;IAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IAE3E,OAAO,GAAG,SAAS,IAAI,IAAI,IAAI,MAAM,EAAE,CAAA;AACzC,CAAC;AAYD,SAAgB,mBAAmB,CACjC,KAAU,EACV,eAAmD;IAGnD,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,iCAAiC,EAAE,CAAA;IACtE,CAAC;IAGD,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;QAC9D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,wBAAwB,EAAE,CAAA;IAC7D,CAAC;IAGD,QAAQ,eAAe,EAAE,CAAC;QACxB,KAAK,MAAM;YACT,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;QAC1B,KAAK,MAAM;YACT,OAAO;gBACL,OAAO,EAAE,KAAK,CAAC,SAAS;gBACxB,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,6BAA6B;aACpE,CAAA;QACH,KAAK,MAAM;YACT,OAAO;gBACL,OAAO,EAAE,KAAK,CAAC,SAAS;gBACxB,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,6BAA6B;aACpE,CAAA;QACH,KAAK,OAAO;YACV,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,mCAAmC;aAC5C,CAAA;QACH;YACE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAA;IACvD,CAAC;AACH,CAAC;AAKD,SAAgB,gBAAgB,CAAC,OAAe,EAAE,OAAgB;IAChE,MAAM,IAAI,GAAG,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,uBAAuB,CAAA;IAClF,OAAO,GAAG,IAAI,oBAAoB,OAAO,EAAE,CAAA;AAC7C,CAAC;AAKD,SAAgB,aAAa,CAAC,GAAW;IACvC,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAA;QAC3B,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAG5C,MAAM,WAAW,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAA;QAClE,IAAI,WAAW,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC;YACrD,OAAO,SAAS,CAAC,WAAW,GAAG,CAAC,CAAC,CAAA;QACnC,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,IAAI,CAAA;IACb,CAAC;AACH,CAAC;AAYD,SAAgB,0BAA0B,CACxC,QAAa,EACb,QAAgB,EAChB,WAA6D,SAAS;IAEtE,MAAM,WAAW,GAAG;QAClB,KAAK,EAAE,cAAc,QAAQ,CAAC,IAAI,YAAY;QAC9C,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,2BAA2B,QAAQ,CAAC,UAAU,IAAI,CAAC,iBAAiB;QACzG,GAAG,EAAE,QAAQ;QACb,QAAQ,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC;KAC3C,CAAA;IAED,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,SAAS;YACZ,OAAO;gBACL,GAAG,WAAW;gBACd,KAAK,EAAE,iBAAiB,QAAQ,CAAC,IAAI,eAAe,QAAQ,CAAC,UAAU,IAAI,CAAC,kCAAkC;gBAC9G,QAAQ,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,CAAC;aACzD,CAAA;QAEH,KAAK,UAAU;YACb,OAAO;gBACL,GAAG,WAAW;gBACd,KAAK,EAAE,cAAc,QAAQ,CAAC,IAAI,GAAG;gBACrC,WAAW,EAAE,wCAAwC,QAAQ,CAAC,UAAU,IAAI,CAAC,wBAAwB;aACtG,CAAA;QAEH,KAAK,UAAU;YACb,OAAO;gBACL,GAAG,WAAW;gBACd,KAAK,EAAE,oBAAoB,QAAQ,CAAC,IAAI,GAAG;gBAC3C,WAAW,EAAE,8CAA8C,QAAQ,CAAC,UAAU,IAAI,CAAC,UAAU;aAC9F,CAAA;QAEH,KAAK,UAAU;YACb,OAAO;gBACL,GAAG,WAAW;gBACd,KAAK,EAAE,oBAAoB,QAAQ,CAAC,IAAI,YAAY;gBACpD,WAAW,EAAE,GAAG,QAAQ,CAAC,UAAU,IAAI,CAAC,kCAAkC;aAC3E,CAAA;QAEH;YACE,OAAO,WAAW,CAAA;IACtB,CAAC;AACH,CAAC;AAKM,KAAK,UAAU,gBAAgB,CAAC,OAAe;IACpD,IAAI,CAAC;QACH,MAAM,KAAK,CAAC,kCAAkC,EAAE;YAC9C,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC;SAClC,CAAC,CAAA;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;IACtD,CAAC;AACH,CAAC;AAKD,SAAgB,qBAAqB,CAAC,SAAsB;IAC1D,IAAI,CAAC,SAAS;QAAE,OAAO,eAAe,CAAA;IAEtC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;IACtB,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAA;IAEhD,IAAI,IAAI,IAAI,CAAC;QAAE,OAAO,SAAS,CAAA;IAE/B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAA;IACrD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAA;IAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAA;IAEnE,IAAI,IAAI,GAAG,CAAC;QAAE,OAAO,cAAc,IAAI,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;IACnE,IAAI,KAAK,GAAG,CAAC;QAAE,OAAO,cAAc,KAAK,QAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;IACvE,IAAI,OAAO,GAAG,CAAC;QAAE,OAAO,cAAc,OAAO,UAAU,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;IAE/E,OAAO,cAAc,CAAA;AACvB,CAAC"}