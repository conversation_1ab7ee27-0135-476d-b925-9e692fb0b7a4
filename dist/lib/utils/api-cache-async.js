"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManager = exports.ApiCache = void 0;
exports.Cacheable = Cacheable;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
class ApiCache {
    constructor(options) {
        this.options = options;
        this.cache = new Map();
        this.stats = {
            hits: 0,
            misses: 0,
            size: 0,
            evictions: 0
        };
        this.loadFromDisk().catch(error => {
            console.warn('[ApiCache] Failed to load disk cache:', error);
        });
    }
    async get(key) {
        const fullKey = this.makeKey(key);
        const entry = this.cache.get(fullKey);
        if (!entry) {
            this.stats.misses++;
            return null;
        }
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(fullKey);
            this.stats.misses++;
            return null;
        }
        this.stats.hits++;
        return entry.data;
    }
    async set(key, value, options) {
        const fullKey = this.makeKey(key);
        const entry = {
            data: value,
            timestamp: Date.now(),
            ttl: options?.ttl || this.options.ttl,
            tags: options?.tags
        };
        if (this.cache.size >= this.options.maxSize) {
            this.evictOldest();
        }
        this.cache.set(fullKey, entry);
        this.stats.size = this.cache.size;
        this.saveToDisk(fullKey, entry).catch(error => {
            console.warn(`[ApiCache] Failed to save cache entry ${key}:`, error);
        });
    }
    async delete(key) {
        const fullKey = this.makeKey(key);
        const deleted = this.cache.delete(fullKey);
        this.stats.size = this.cache.size;
        if (deleted && this.options.diskCachePath) {
            try {
                const filename = `${fullKey}.json`;
                const filePath = path.join(this.options.diskCachePath, filename);
                await fs.unlink(filePath).catch(() => { });
                await this.updateDiskIndex();
            }
            catch (error) {
                console.warn(`[ApiCache] Failed to delete cache file for ${key}:`, error);
            }
        }
        return deleted;
    }
    async clear() {
        this.cache.clear();
        this.stats.size = 0;
        if (this.options.diskCachePath) {
            try {
                const files = await fs.readdir(this.options.diskCachePath);
                await Promise.all(files.map(file => fs.unlink(path.join(this.options.diskCachePath, file)).catch(() => { })));
            }
            catch (error) {
                console.warn('[ApiCache] Failed to clear disk cache:', error);
            }
        }
    }
    async clearByTag(tag) {
        let cleared = 0;
        const keysToDelete = [];
        for (const [key, entry] of this.cache.entries()) {
            if (entry.tags?.includes(tag)) {
                keysToDelete.push(key);
                cleared++;
            }
        }
        for (const key of keysToDelete) {
            this.cache.delete(key);
        }
        this.stats.size = this.cache.size;
        if (cleared > 0 && this.options.diskCachePath) {
            await this.updateDiskIndex();
        }
        return cleared;
    }
    getStats() {
        return { ...this.stats };
    }
    makeKey(key) {
        return `${this.options.namespace}:${key}`;
    }
    evictOldest() {
        let oldestKey = null;
        let oldestTime = Date.now();
        for (const [key, entry] of this.cache.entries()) {
            if (entry.timestamp < oldestTime) {
                oldestTime = entry.timestamp;
                oldestKey = key;
            }
        }
        if (oldestKey) {
            this.cache.delete(oldestKey);
            this.stats.evictions++;
        }
    }
    async loadFromDisk() {
        if (!this.options.diskCachePath)
            return;
        try {
            await fs.mkdir(this.options.diskCachePath, { recursive: true });
            const indexPath = path.join(this.options.diskCachePath, 'index.json');
            try {
                const indexContent = await fs.readFile(indexPath, 'utf-8');
                const index = JSON.parse(indexContent);
                let loaded = 0;
                await Promise.all(Object.entries(index).map(async ([key, filename]) => {
                    try {
                        const filePath = path.join(this.options.diskCachePath, filename);
                        const content = await fs.readFile(filePath, 'utf-8');
                        const entry = JSON.parse(content);
                        if (Date.now() - entry.timestamp <= entry.ttl) {
                            this.cache.set(key, entry);
                            loaded++;
                        }
                        else {
                            await fs.unlink(filePath).catch(() => { });
                        }
                    }
                    catch (error) {
                        console.warn(`[ApiCache] Failed to load cache entry ${key}:`, error);
                    }
                }));
                if (loaded > 0) {
                    console.log(`[ApiCache] Loaded ${loaded} entries from disk cache`);
                }
            }
            catch (error) {
            }
        }
        catch (error) {
            console.warn('[ApiCache] Failed to initialize disk cache:', error);
        }
    }
    async saveToDisk(key, entry) {
        if (!this.options.diskCachePath)
            return;
        try {
            const filename = `${key.replace(/[^a-zA-Z0-9-_]/g, '_')}.json`;
            const filePath = path.join(this.options.diskCachePath, filename);
            await fs.writeFile(filePath, JSON.stringify(entry));
            const indexPath = path.join(this.options.diskCachePath, 'index.json');
            let index = {};
            try {
                const indexContent = await fs.readFile(indexPath, 'utf-8');
                index = JSON.parse(indexContent);
            }
            catch {
            }
            index[key] = filename;
            await fs.writeFile(indexPath, JSON.stringify(index));
        }
        catch (error) {
            console.warn(`[ApiCache] Failed to save cache entry ${key}:`, error);
        }
    }
    async updateDiskIndex() {
        if (!this.options.diskCachePath)
            return;
        try {
            const index = {};
            for (const key of this.cache.keys()) {
                const filename = `${key.replace(/[^a-zA-Z0-9-_]/g, '_')}.json`;
                index[key] = filename;
            }
            const indexPath = path.join(this.options.diskCachePath, 'index.json');
            await fs.writeFile(indexPath, JSON.stringify(index));
        }
        catch (error) {
            console.warn('[ApiCache] Failed to update disk index:', error);
        }
    }
}
exports.ApiCache = ApiCache;
class CacheManager {
    static getCache(service, options = {}) {
        if (!this.caches.has(service)) {
            this.caches.set(service, new ApiCache({
                ttl: 5 * 60 * 1000,
                maxSize: 1000,
                namespace: service,
                ...options
            }));
        }
        return this.caches.get(service);
    }
    static async clearAll() {
        await Promise.all(Array.from(this.caches.values()).map(cache => cache.clear()));
    }
    static getStats() {
        const stats = {};
        for (const [service, cache] of this.caches.entries()) {
            stats[service] = cache.getStats();
        }
        return stats;
    }
}
exports.CacheManager = CacheManager;
CacheManager.caches = new Map();
function Cacheable(options) {
    return function (target, propertyName, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = async function (...args) {
            const cache = CacheManager.getCache(options.service);
            const key = options.keyGenerator ? options.keyGenerator(...args) : `${propertyName}:${JSON.stringify(args)}`;
            const cached = await cache.get(key);
            if (cached !== null) {
                return cached;
            }
            const result = await originalMethod.apply(this, args);
            await cache.set(key, result, { ttl: options.ttl });
            return result;
        };
        return descriptor;
    };
}
//# sourceMappingURL=api-cache-async.js.map