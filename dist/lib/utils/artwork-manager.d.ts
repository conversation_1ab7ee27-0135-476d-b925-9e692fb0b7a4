export interface ArtworkSizes {
    thumbnail: {
        width: number;
        height: number;
    };
    medium: {
        width: number;
        height: number;
    };
    large: {
        width: number;
        height: number;
    };
}
export interface ArtworkPaths {
    thumbnail: string | null;
    medium: string | null;
    large: string | null;
    original: string | null;
}
export interface ArtworkResult {
    success: boolean;
    paths: ArtworkPaths;
    hash: string | null;
    error?: string;
    metadata?: {
        originalUrl: string;
        originalSize?: {
            width: number;
            height: number;
        };
        format: string;
        fileSize: number;
    };
}
export declare class ArtworkManager {
    private baseDir;
    private sizes;
    constructor(baseDir?: string);
    private ensureDirectories;
    processAlbumArt(albumArtUrl: string, artist: string, album: string, trackId?: string): Promise<ArtworkResult>;
    processArtistImage(artistImageUrl: string, artist: string): Promise<ArtworkResult>;
    private downloadImage;
    private getImageMetadata;
    private generateScaledVersions;
    private generateFilename;
    private generateHash;
    private getArtworkPaths;
    private allPathsExist;
    getFullPath(relativePath: string): string;
    cleanupOrphanedArtwork(validTracks: Array<{
        albumArtPath?: string;
        artistImagePath?: string;
    }>): Promise<{
        deletedFiles: number;
        errors: string[];
    }>;
    getStorageStats(): {
        totalFiles: number;
        totalSize: number;
        byType: Record<string, {
            files: number;
            size: number;
        }>;
    };
    static checkSharpAvailability(): boolean;
}
