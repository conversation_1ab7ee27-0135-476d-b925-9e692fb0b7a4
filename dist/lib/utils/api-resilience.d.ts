export interface RetryOptions {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
    backoffMultiplier: number;
    retryCondition?: (error: any) => boolean;
}
export interface RateLimitConfig {
    requestsPerSecond: number;
    burstLimit: number;
}
export interface CircuitBreakerConfig {
    failureThreshold: number;
    resetTimeout: number;
    monitoringPeriod: number;
}
export declare class RetryHandler {
    private static defaultOptions;
    static execute<T>(operation: () => Promise<T>, options?: Partial<RetryOptions>): Promise<T>;
    private static sleep;
}
export declare class RateLimiter {
    private requests;
    private readonly config;
    constructor(config: RateLimitConfig);
    acquire(): Promise<void>;
}
export declare class CircuitBreaker {
    private failures;
    private lastFailureTime;
    private state;
    private readonly config;
    constructor(config: CircuitBreakerConfig);
    execute<T>(operation: () => Promise<T>): Promise<T>;
    private recordFailure;
    private reset;
    getState(): string;
}
export declare class ResilientApiClient {
    private rateLimiters;
    private circuitBreakers;
    constructor();
    fetch(service: string, url: string, options?: RequestInit, retryOptions?: Partial<RetryOptions>, useCache?: boolean): Promise<Response>;
    private generateCacheKey;
    getCircuitBreakerState(service: string): string;
}
