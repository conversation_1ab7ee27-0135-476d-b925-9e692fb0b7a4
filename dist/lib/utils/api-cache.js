"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManager = exports.ApiCache = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const crypto = __importStar(require("crypto"));
class ApiCache {
    constructor(options = {}) {
        this.cache = new Map();
        this.accessOrder = new Map();
        this.accessCounter = 0;
        this.options = {
            ttl: 60 * 60 * 1000,
            maxSize: 1000,
            persistToDisk: false,
            diskCachePath: './cache',
            ...options
        };
        if (this.options.persistToDisk) {
            this.loadFromDisk();
        }
    }
    generateKey(service, endpoint, params = {}) {
        const keyData = {
            service,
            endpoint,
            params: Object.keys(params).sort().reduce((sorted, key) => {
                sorted[key] = params[key];
                return sorted;
            }, {})
        };
        return crypto.createHash('md5').update(JSON.stringify(keyData)).digest('hex');
    }
    get(service, endpoint, params = {}) {
        const key = this.generateKey(service, endpoint, params);
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            this.accessOrder.delete(key);
            return null;
        }
        entry.hits++;
        this.accessOrder.set(key, ++this.accessCounter);
        console.log(`[ApiCache] Cache HIT for ${service}:${endpoint} (hits: ${entry.hits})`);
        return entry.data;
    }
    set(service, endpoint, data, params = {}, ttl) {
        const key = this.generateKey(service, endpoint, params);
        const useTtl = ttl || this.options.ttl;
        const entry = {
            data,
            timestamp: Date.now(),
            ttl: useTtl,
            hits: 0
        };
        if (this.cache.size >= this.options.maxSize) {
            this.evictLRU();
        }
        this.cache.set(key, entry);
        this.accessOrder.set(key, ++this.accessCounter);
        console.log(`[ApiCache] Cache SET for ${service}:${endpoint} (TTL: ${Math.round(useTtl / 1000)}s)`);
        if (this.options.persistToDisk) {
            this.saveToDisk(key, entry);
        }
    }
    has(service, endpoint, params = {}) {
        return this.get(service, endpoint, params) !== null;
    }
    cleanup() {
        const now = Date.now();
        let cleaned = 0;
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
                this.cache.delete(key);
                this.accessOrder.delete(key);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            console.log(`[ApiCache] Cleaned ${cleaned} expired entries`);
        }
        return cleaned;
    }
    evictLRU() {
        let oldestKey = null;
        let oldestAccess = Infinity;
        for (const [key, accessTime] of this.accessOrder.entries()) {
            if (accessTime < oldestAccess) {
                oldestAccess = accessTime;
                oldestKey = key;
            }
        }
        if (oldestKey) {
            this.cache.delete(oldestKey);
            this.accessOrder.delete(oldestKey);
            console.log(`[ApiCache] Evicted LRU entry: ${oldestKey}`);
        }
    }
    getStats() {
        let totalHits = 0;
        let totalEntries = this.cache.size;
        for (const entry of this.cache.values()) {
            totalHits += entry.hits;
        }
        const hitRate = totalEntries > 0 ? totalHits / totalEntries : 0;
        return {
            size: this.cache.size,
            maxSize: this.options.maxSize,
            hitRate,
            totalHits,
            totalEntries
        };
    }
    clear() {
        this.cache.clear();
        this.accessOrder.clear();
        this.accessCounter = 0;
        console.log('[ApiCache] Cache cleared');
    }
    loadFromDisk() {
        if (!this.options.diskCachePath)
            return;
        try {
            if (!fs.existsSync(this.options.diskCachePath)) {
                fs.mkdirSync(this.options.diskCachePath, { recursive: true });
                return;
            }
            const indexPath = path.join(this.options.diskCachePath, 'index.json');
            if (!fs.existsSync(indexPath))
                return;
            const index = JSON.parse(fs.readFileSync(indexPath, 'utf-8'));
            let loaded = 0;
            for (const [key, filename] of Object.entries(index)) {
                try {
                    const filePath = path.join(this.options.diskCachePath, filename);
                    if (fs.existsSync(filePath)) {
                        const entry = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
                        if (Date.now() - entry.timestamp <= entry.ttl) {
                            this.cache.set(key, entry);
                            loaded++;
                        }
                    }
                }
                catch (error) {
                    console.warn(`[ApiCache] Failed to load cache entry ${key}:`, error);
                }
            }
            if (loaded > 0) {
                console.log(`[ApiCache] Loaded ${loaded} entries from disk cache`);
            }
        }
        catch (error) {
            console.warn('[ApiCache] Failed to load disk cache:', error);
        }
    }
    saveToDisk(key, entry) {
        if (!this.options.diskCachePath)
            return;
        try {
            const filename = `${key}.json`;
            const filePath = path.join(this.options.diskCachePath, filename);
            fs.writeFileSync(filePath, JSON.stringify(entry));
            const indexPath = path.join(this.options.diskCachePath, 'index.json');
            let index = {};
            if (fs.existsSync(indexPath)) {
                index = JSON.parse(fs.readFileSync(indexPath, 'utf-8'));
            }
            index[key] = filename;
            fs.writeFileSync(indexPath, JSON.stringify(index));
        }
        catch (error) {
            console.warn(`[ApiCache] Failed to save cache entry ${key}:`, error);
        }
    }
}
exports.ApiCache = ApiCache;
class CacheManager {
    static getCache(service, options = {}) {
        if (!this.caches.has(service)) {
            const defaultOptions = {
                ttl: this.getDefaultTTL(service),
                maxSize: 500,
                persistToDisk: process.env.NODE_ENV === 'production',
                diskCachePath: `./cache/${service}`
            };
            this.caches.set(service, new ApiCache({ ...defaultOptions, ...options }));
        }
        return this.caches.get(service);
    }
    static getDefaultTTL(service) {
        const ttls = {
            'spotify': 24 * 60 * 60 * 1000,
            'lastfm': 7 * 24 * 60 * 60 * 1000,
            'musicbrainz': 30 * 24 * 60 * 60 * 1000,
        };
        return ttls[service] || 60 * 60 * 1000;
    }
    static cleanup() {
        for (const cache of this.caches.values()) {
            cache.cleanup();
        }
    }
    static getGlobalStats() {
        const stats = {};
        for (const [service, cache] of this.caches.entries()) {
            stats[service] = cache.getStats();
        }
        return stats;
    }
    static clearAll() {
        for (const cache of this.caches.values()) {
            cache.clear();
        }
    }
}
exports.CacheManager = CacheManager;
CacheManager.caches = new Map();
if (typeof global !== 'undefined') {
    setInterval(() => {
        CacheManager.cleanup();
    }, 5 * 60 * 1000);
}
//# sourceMappingURL=api-cache.js.map