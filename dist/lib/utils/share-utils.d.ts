export declare function generateShareId(): string;
export declare function generateShareCode(): string;
export interface SharePermissions {
    canView: boolean;
    canEdit: boolean;
    canCopy: boolean;
    canShare: boolean;
}
export declare function validateShareAccess(share: any, requestedAction: 'view' | 'edit' | 'copy' | 'share'): {
    allowed: boolean;
    reason?: string;
};
export declare function generateShareUrl(shareId: string, baseUrl?: string): string;
export declare function parseShareUrl(url: string): string | null;
export interface SocialShareContent {
    title: string;
    description: string;
    url: string;
    hashtags?: string[];
}
export declare function generateSocialShareContent(playlist: any, shareUrl: string, platform?: 'twitter' | 'facebook' | 'linkedin' | 'whatsapp'): SocialShareContent;
export declare function trackShareAccess(shareId: string): Promise<void>;
export declare function formatShareExpiration(expiresAt: Date | null): string;
