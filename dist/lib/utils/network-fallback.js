"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkServerReachability = checkServerReachability;
exports.checkMpdReachability = checkMpdReachability;
exports.isLocalhostMode = isLocalhostMode;
exports.getFallbackUrls = getFallbackUrls;
exports.initNetworkMonitoring = initNetworkMonitoring;
exports.getNetworkStatus = getNetworkStatus;
exports.fetchWithFallback = fetchWithFallback;
exports.getAdaptiveSocketUrl = getAdaptiveSocketUrl;
exports.getAdaptiveMpdUrl = getAdaptiveMpdUrl;
const sonner_1 = require("sonner");
let networkStatus = {
    isOnline: true,
    isLanConnected: true,
    canReachServer: true,
    canReachMpd: true,
    fallbackMode: false
};
async function checkServerReachability() {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000);
        const response = await fetch('/api/health', {
            method: 'HEAD',
            signal: controller.signal
        });
        clearTimeout(timeoutId);
        return response.ok;
    }
    catch (error) {
        return false;
    }
}
async function checkMpdReachability() {
    try {
        const mpdHost = process.env.NEXT_PUBLIC_MPD_HOST || window.location.hostname;
        const mpdPort = process.env.NEXT_PUBLIC_MPD_HTTP_PORT || '8001';
        const mpdProxyUrl = `http://${mpdHost}:${mpdPort}`;
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000);
        const response = await fetch(`${mpdProxyUrl}/status`, {
            method: 'GET',
            signal: controller.signal
        });
        clearTimeout(timeoutId);
        return response.ok;
    }
    catch (error) {
        return false;
    }
}
function isLocalhostMode() {
    if (typeof window === 'undefined')
        return true;
    const hostname = window.location.hostname;
    return hostname === 'localhost' || hostname === '127.0.0.1';
}
function getFallbackUrls() {
    const isLocalhost = isLocalhostMode();
    return {
        api: isLocalhost ? 'http://localhost:3000' : window.location.origin,
        socket: isLocalhost ? 'http://localhost:3001' : `${window.location.protocol}//${window.location.hostname}:3001`,
        mpd: isLocalhost ? 'http://localhost:8001' : `${window.location.protocol}//${window.location.hostname}:8001`
    };
}
function initNetworkMonitoring() {
    if (typeof window === 'undefined')
        return;
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    checkNetworkStatus();
    setInterval(checkNetworkStatus, 30000);
}
async function handleOnline() {
    console.log('🌐 Network: Online event detected');
    networkStatus.isOnline = true;
    await checkNetworkStatus();
}
function handleOffline() {
    console.log('🌐 Network: Offline event detected');
    networkStatus.isOnline = false;
    networkStatus.canReachServer = false;
    networkStatus.canReachMpd = false;
    networkStatus.fallbackMode = true;
    sonner_1.toast.warning('Network disconnected', {
        description: 'Running in offline mode. Some features may be limited.',
        duration: 5000
    });
}
async function checkNetworkStatus() {
    const wasReachable = networkStatus.canReachServer;
    const wasMpdReachable = networkStatus.canReachMpd;
    networkStatus.canReachServer = await checkServerReachability();
    networkStatus.canReachMpd = await checkMpdReachability();
    const shouldFallback = !networkStatus.canReachServer || !networkStatus.canReachMpd;
    if (!wasReachable && networkStatus.canReachServer && !wasMpdReachable && networkStatus.canReachMpd) {
        networkStatus.fallbackMode = false;
        sonner_1.toast.success('All services restored', {
            description: 'Server and MPD connections are now available.',
            duration: 3000
        });
    }
    else if (wasReachable && !networkStatus.canReachServer) {
        networkStatus.fallbackMode = true;
        sonner_1.toast.warning('Server unreachable', {
            description: 'Some features may be limited. Check your network connection.',
            duration: 5000
        });
    }
    else if (wasMpdReachable && !networkStatus.canReachMpd) {
        networkStatus.fallbackMode = true;
        sonner_1.toast.warning('MPD service unreachable', {
            description: 'Music playback may be limited. Check MPD connection.',
            duration: 5000
        });
    }
    else if (shouldFallback !== networkStatus.fallbackMode) {
        networkStatus.fallbackMode = shouldFallback;
    }
}
function getNetworkStatus() {
    if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_DISABLE_NETWORK_FALLBACK === 'true') {
        return {
            isOnline: true,
            isLanConnected: true,
            canReachServer: true,
            canReachMpd: true,
            fallbackMode: false
        };
    }
    return { ...networkStatus };
}
async function fetchWithFallback(url, options) {
    try {
        const response = await fetch(url, {
            ...options,
            signal: options?.signal || AbortSignal.timeout(10000),
            credentials: options?.credentials || 'include'
        });
        if (!response.ok && response.status >= 500) {
            checkNetworkStatus();
        }
        return response;
    }
    catch (error) {
        checkNetworkStatus();
        throw error;
    }
}
function getAdaptiveSocketUrl() {
    const { fallbackMode } = networkStatus;
    if (fallbackMode || !networkStatus.canReachServer) {
        return 'http://localhost:3001';
    }
    const host = window.location.hostname;
    const protocol = window.location.protocol;
    const socketPort = process.env.NEXT_PUBLIC_SOCKET_PORT || '3001';
    if (host.match(/^\d+\.\d+\.\d+\.\d+$/)) {
        return `${protocol}//${host}:${socketPort}`;
    }
    return process.env.NEXT_PUBLIC_SOCKET_URL || `http://localhost:${socketPort}`;
}
function getAdaptiveMpdUrl() {
    const { fallbackMode } = networkStatus;
    if (fallbackMode || !networkStatus.canReachServer) {
        return 'http://localhost:8001';
    }
    const mpdHost = process.env.NEXT_PUBLIC_MPD_HOST || window.location.hostname;
    const mpdPort = process.env.NEXT_PUBLIC_MPD_HTTP_PORT || '8001';
    return `http://${mpdHost}:${mpdPort}`;
}
//# sourceMappingURL=network-fallback.js.map