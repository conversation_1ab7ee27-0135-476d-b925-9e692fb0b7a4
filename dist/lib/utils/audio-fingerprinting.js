"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioFingerprintService = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const crypto = __importStar(require("crypto"));
const child_process_1 = require("child_process");
class AudioFingerprintService {
    constructor() {
        this.chromaprintPath = this.findExecutable('fpcalc', [
            '/usr/bin/fpcalc',
            '/usr/local/bin/fpcalc',
            '/opt/homebrew/bin/fpcalc',
            'fpcalc'
        ]);
        this.ffmpegPath = this.findExecutable('ffmpeg', [
            '/usr/bin/ffmpeg',
            '/usr/local/bin/ffmpeg',
            '/opt/homebrew/bin/ffmpeg',
            'ffmpeg'
        ]);
    }
    findExecutable(name, paths) {
        for (const testPath of paths) {
            try {
                if (fs.existsSync(testPath)) {
                    return testPath;
                }
            }
            catch (error) {
                continue;
            }
        }
        return name;
    }
    async generateFingerprint(filePath) {
        try {
            const absolutePath = path.resolve(filePath);
            if (!fs.existsSync(absolutePath)) {
                console.warn(`[AudioFingerprint] File not found: ${absolutePath}`);
                return null;
            }
            const fileStats = fs.statSync(absolutePath);
            const format = path.extname(filePath).toLowerCase().replace('.', '');
            const audioInfo = await this.getAudioInfo(absolutePath);
            const spectralHash = await this.generateSpectralHash(absolutePath);
            const fileHash = await this.generateFileHash(absolutePath);
            let acousticId;
            try {
                acousticId = await this.generateAcoustIDFingerprint(absolutePath);
            }
            catch (error) {
                console.warn(`[AudioFingerprint] AcoustID generation failed for ${filePath}:`, error);
            }
            const fingerprint = {
                acousticId,
                spectralHash,
                durationMs: audioInfo.duration * 1000,
                fileHash,
                bitrate: audioInfo.bitrate,
                sampleRate: audioInfo.sampleRate,
                channels: audioInfo.channels,
                format,
                confidence: acousticId ? 0.9 : 0.7
            };
            return fingerprint;
        }
        catch (error) {
            console.error(`[AudioFingerprint] Failed to generate fingerprint for ${filePath}:`, error);
            return null;
        }
    }
    async generateAcoustIDFingerprint(filePath) {
        return new Promise((resolve, reject) => {
            const process = (0, child_process_1.spawn)(this.chromaprintPath, ['-json', filePath]);
            let stdout = '';
            let stderr = '';
            process.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            process.on('close', (code) => {
                if (code !== 0) {
                    reject(new Error(`chromaprint failed with code ${code}: ${stderr}`));
                    return;
                }
                try {
                    const result = JSON.parse(stdout);
                    if (result.fingerprint) {
                        resolve(result.fingerprint);
                    }
                    else {
                        reject(new Error('No fingerprint in chromaprint output'));
                    }
                }
                catch (error) {
                    reject(new Error(`Failed to parse chromaprint output: ${error}`));
                }
            });
            process.on('error', (error) => {
                reject(new Error(`Failed to spawn chromaprint: ${error}`));
            });
        });
    }
    async generateSpectralHash(filePath) {
        return new Promise((resolve, reject) => {
            const args = [
                '-i', filePath,
                '-af', 'showfreqs=mode=line:fscale=log',
                '-f', 'null',
                '-'
            ];
            const process = (0, child_process_1.spawn)(this.ffmpegPath, args);
            let stderr = '';
            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            process.on('close', (code) => {
                try {
                    const spectralData = this.extractSpectralFeatures(stderr);
                    const hash = crypto.createHash('sha256').update(spectralData).digest('hex');
                    resolve(hash.substring(0, 32));
                }
                catch (error) {
                    const fallbackData = `${filePath}_${Date.now()}`;
                    const hash = crypto.createHash('md5').update(fallbackData).digest('hex');
                    resolve(hash);
                }
            });
            process.on('error', (error) => {
                reject(error);
            });
        });
    }
    extractSpectralFeatures(ffmpegOutput) {
        const lines = ffmpegOutput.split('\n');
        const features = [];
        for (const line of lines) {
            if (line.includes('Hz')) {
                const match = line.match(/(\d+)\s*Hz/);
                if (match)
                    features.push(`hz_${match[1]}`);
            }
            if (line.includes('kb/s')) {
                const match = line.match(/(\d+)\s*kb\/s/);
                if (match)
                    features.push(`kbps_${match[1]}`);
            }
            if (line.includes('Duration:')) {
                const match = line.match(/Duration:\s*(\d+):(\d+):(\d+\.\d+)/);
                if (match) {
                    const totalSeconds = parseInt(match[1]) * 3600 + parseInt(match[2]) * 60 + parseFloat(match[3]);
                    features.push(`dur_${Math.round(totalSeconds)}`);
                }
            }
        }
        return features.join('_') || 'unknown_spectral';
    }
    async generateFileHash(filePath) {
        return new Promise((resolve, reject) => {
            try {
                const fileStats = fs.statSync(filePath);
                const fileSize = fileStats.size;
                const sampleSize = Math.min(65536, Math.floor(fileSize / 4));
                const firstSample = Buffer.alloc(sampleSize);
                const lastSample = Buffer.alloc(sampleSize);
                const fd = fs.openSync(filePath, 'r');
                try {
                    fs.readSync(fd, firstSample, 0, sampleSize, 0);
                    if (fileSize > sampleSize * 2) {
                        fs.readSync(fd, lastSample, 0, sampleSize, fileSize - sampleSize);
                    }
                    fs.closeSync(fd);
                    const combinedData = Buffer.concat([
                        firstSample,
                        lastSample,
                        Buffer.from(fileSize.toString())
                    ]);
                    const hash = crypto.createHash('md5').update(combinedData).digest('hex');
                    resolve(hash);
                }
                catch (error) {
                    fs.closeSync(fd);
                    throw error;
                }
            }
            catch (error) {
                reject(error);
            }
        });
    }
    async getAudioInfo(filePath) {
        return new Promise((resolve, reject) => {
            const process = (0, child_process_1.spawn)('ffprobe', [
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                filePath
            ]);
            let stdout = '';
            let stderr = '';
            process.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            process.on('close', (code) => {
                if (code !== 0) {
                    reject(new Error(`ffprobe failed: ${stderr}`));
                    return;
                }
                try {
                    const data = JSON.parse(stdout);
                    const audioStream = data.streams?.find((s) => s.codec_type === 'audio');
                    resolve({
                        duration: parseFloat(data.format?.duration || '0'),
                        bitrate: parseInt(data.format?.bit_rate || '0'),
                        sampleRate: parseInt(audioStream?.sample_rate || '0'),
                        channels: parseInt(audioStream?.channels || '0')
                    });
                }
                catch (error) {
                    reject(new Error(`Failed to parse ffprobe output: ${error}`));
                }
            });
            process.on('error', (error) => {
                reject(error);
            });
        });
    }
    static compareFingerprints(fp1, fp2, file1, file2) {
        const reasons = [];
        let similarity = 0;
        let matchType = 'possible';
        if (fp1.fileHash === fp2.fileHash) {
            similarity = 1.0;
            matchType = 'identical';
            reasons.push('Identical file hash');
        }
        else {
            if (fp1.acousticId && fp2.acousticId) {
                const acousticSimilarity = this.compareAcoustIDs(fp1.acousticId, fp2.acousticId);
                similarity = Math.max(similarity, acousticSimilarity);
                if (acousticSimilarity > 0.95) {
                    matchType = 'very_similar';
                    reasons.push(`AcoustID match: ${(acousticSimilarity * 100).toFixed(1)}%`);
                }
                else if (acousticSimilarity > 0.8) {
                    matchType = 'similar';
                    reasons.push(`AcoustID similarity: ${(acousticSimilarity * 100).toFixed(1)}%`);
                }
            }
            if (fp1.spectralHash === fp2.spectralHash) {
                similarity = Math.max(similarity, 0.85);
                if (matchType === 'possible')
                    matchType = 'similar';
                reasons.push('Identical spectral fingerprint');
            }
            const durationDiff = Math.abs(fp1.durationMs - fp2.durationMs);
            if (durationDiff < 2000) {
                similarity += 0.1;
                reasons.push(`Similar duration (${durationDiff}ms difference)`);
            }
            const formatMatch = fp1.format === fp2.format;
            const bitrateMatch = fp1.bitrate && fp2.bitrate && Math.abs(fp1.bitrate - fp2.bitrate) < 32000;
            if (formatMatch && bitrateMatch) {
                similarity += 0.05;
                reasons.push('Same format and similar bitrate');
            }
        }
        if (similarity < 0.7) {
            return null;
        }
        const file1Stats = fs.existsSync(file1) ? fs.statSync(file1) : null;
        const file2Stats = fs.existsSync(file2) ? fs.statSync(file2) : null;
        const sizeDifference = file1Stats && file2Stats
            ? Math.abs(file1Stats.size - file2Stats.size) / Math.max(file1Stats.size, file2Stats.size) * 100
            : 0;
        const bitrateComparison = fp1.bitrate && fp2.bitrate
            ? `${fp1.bitrate} vs ${fp2.bitrate} kbps`
            : 'Unknown';
        const formatComparison = `${fp1.format} vs ${fp2.format}`;
        return {
            originalFile: file1,
            duplicateFile: file2,
            similarity,
            matchType,
            reasons,
            fileComparison: {
                sizeDifference,
                bitrateComparison,
                formatComparison
            }
        };
    }
    static compareAcoustIDs(id1, id2) {
        if (id1 === id2)
            return 1.0;
        const minLength = Math.min(id1.length, id2.length);
        let matches = 0;
        for (let i = 0; i < minLength; i++) {
            if (id1[i] === id2[i])
                matches++;
        }
        return matches / Math.max(id1.length, id2.length);
    }
    static findDuplicates(fingerprints) {
        const duplicates = [];
        for (let i = 0; i < fingerprints.length; i++) {
            for (let j = i + 1; j < fingerprints.length; j++) {
                const fp1 = fingerprints[i];
                const fp2 = fingerprints[j];
                const duplicate = this.compareFingerprints(fp1.fingerprint, fp2.fingerprint, fp1.filePath, fp2.filePath);
                if (duplicate) {
                    duplicates.push(duplicate);
                }
            }
        }
        return duplicates.sort((a, b) => b.similarity - a.similarity);
    }
    static async checkRequirements() {
        const recommendations = [];
        const chromaprint = await this.checkCommand('fpcalc', '--version');
        const ffmpeg = await this.checkCommand('ffmpeg', '-version');
        const ffprobe = await this.checkCommand('ffprobe', '-version');
        if (!chromaprint) {
            recommendations.push('Install chromaprint for AcoustID fingerprinting: brew install chromaprint (macOS) or apt-get install libchromaprint-tools (Ubuntu)');
        }
        if (!ffmpeg) {
            recommendations.push('Install FFmpeg for audio analysis: brew install ffmpeg (macOS) or apt-get install ffmpeg (Ubuntu)');
        }
        if (!ffprobe) {
            recommendations.push('FFprobe not found (usually comes with FFmpeg)');
        }
        return {
            chromaprint,
            ffmpeg,
            ffprobe,
            recommendations
        };
    }
    static async checkCommand(command, args) {
        return new Promise((resolve) => {
            const process = (0, child_process_1.spawn)(command, [args]);
            process.on('close', () => {
                resolve(true);
            });
            process.on('error', () => {
                resolve(false);
            });
        });
    }
}
exports.AudioFingerprintService = AudioFingerprintService;
//# sourceMappingURL=audio-fingerprinting.js.map