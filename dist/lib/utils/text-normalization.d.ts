export declare function normalizeText(text: string): string;
export declare function normalizeArtistName(artist: string): string;
export declare function normalizeSongTitle(title: string): string;
export declare function levenshteinDistance(a: string, b: string): number;
export declare function calculateSimilarity(a: string, b: string): number;
export declare function calculateArtistSimilarity(a: string, b: string): number;
export declare function calculateTitleSimilarity(a: string, b: string): number;
export interface TrackMatch {
    artist: string;
    title: string;
}
export declare function areTracksDuplicates(track1: TrackMatch, track2: TrackMatch, threshold?: number): boolean;
export declare function findBestTrackMatch(target: TrackMatch, candidates: TrackMatch[], threshold?: number): {
    match: TrackMatch;
    score: number;
} | null;
export declare function groupSimilarTracks(tracks: TrackMatch[], threshold?: number): TrackMatch[][];
export declare function generateTextVariations(text: string): string[];
