export interface BackupMetadata {
    id: string;
    timestamp: string;
    description: string;
    tablesCounted: Record<string, number>;
    size: number;
    checksum: string;
}
export declare class BackupManager {
    private backupDir;
    constructor(backupDir?: string);
    createBackup(description?: string): Promise<string>;
    listBackups(): BackupMetadata[];
    restoreBackup(backupId: string): Promise<void>;
    deleteBackup(backupId: string): void;
    getBackupInfo(backupId: string): BackupMetadata | null;
    cleanupOldBackups(keepCount?: number): void;
    private ensureBackupDir;
}
