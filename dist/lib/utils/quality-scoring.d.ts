import type { QuizTrack } from '@prisma/client';
export interface QualityScore {
    overall: number;
    metadataCompleteness: number;
    metadataAccuracy: number;
    enrichmentLevel: number;
    audioQuality: number;
    breakdown: QualityBreakdown;
}
export interface QualityBreakdown {
    core: {
        hasTitle: boolean;
        hasArtist: boolean;
        hasAlbum: boolean;
        hasYear: boolean;
        hasGenre: boolean;
        hasDuration: boolean;
        score: number;
    };
    extended: {
        hasChartData: boolean;
        hasCategories: boolean;
        hasReleaseDate: boolean;
        score: number;
    };
    enrichment: {
        hasSpotifyId: boolean;
        hasMusicBrainzId: boolean;
        hasAlbumArt: boolean;
        hasArtistImage: boolean;
        hasTriviaFacts: boolean;
        hasSimilarArtists: boolean;
        hasThematicTags: boolean;
        score: number;
    };
    accuracy: {
        titleQuality: number;
        artistQuality: number;
        genreQuality: number;
        yearQuality: number;
        overallAccuracy: number;
    };
    audio: {
        hasFingerprint: boolean;
        bitrate: number | null;
        format: string;
        fileSize: number;
        score: number;
    };
    issues: QualityIssue[];
}
export interface QualityIssue {
    type: 'missing' | 'suspicious' | 'inconsistent' | 'poor_quality';
    field: string;
    severity: 'low' | 'medium' | 'high';
    description: string;
    suggestion?: string;
}
export interface QualityAnalysis {
    totalTracks: number;
    averageScore: number;
    distribution: {
        excellent: number;
        good: number;
        fair: number;
        poor: number;
        critical: number;
    };
    commonIssues: Array<{
        issue: string;
        count: number;
        percentage: number;
    }>;
    recommendations: string[];
}
export declare class QualityScorer {
    static calculateScore(track: QuizTrack): QualityScore;
    private static analyzeTrack;
    private static analyzeCoreMetadata;
    private static analyzeExtendedMetadata;
    private static analyzeEnrichment;
    private static analyzeAccuracy;
    private static analyzeAudioQuality;
    private static assessTextQuality;
    private static assessGenreQuality;
    private static assessYearQuality;
    private static hasProperCapitalization;
    private static isSuspiciousText;
    private static calculateFilenameSimilarity;
    private static hasValidValue;
    private static hasValidJsonArray;
    static analyzeCollection(tracks: QuizTrack[]): QualityAnalysis;
}
