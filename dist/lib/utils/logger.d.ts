export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}
export interface LogContext {
    service?: string;
    operation?: string;
    trackId?: string;
    sessionId?: string;
    duration?: number;
    [key: string]: any;
}
export interface LogEntry {
    timestamp: string;
    level: string;
    message: string;
    context: LogContext;
    error?: any;
}
export declare class Logger {
    private static instance;
    private minLevel;
    constructor(minLevel?: LogLevel);
    static getInstance(): Logger;
    debug(message: string, context?: LogContext): void;
    info(message: string, context?: LogContext): void;
    warn(message: string, context?: LogContext): void;
    error(message: string, error?: any, context?: LogContext): void;
    private log;
    private serializeError;
}
export declare const logger: Logger;
