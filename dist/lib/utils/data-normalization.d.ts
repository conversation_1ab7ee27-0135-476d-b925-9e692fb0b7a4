export declare class ArtistNormalizer {
    private static readonly SIMILARITY_THRESHOLD;
    private static readonly EXACT_MATCH_THRESHOLD;
    static normalize(artistName: string): string;
    static findPotentialDuplicates(artistName: string, existingArtists: string[]): Array<{
        artist: string;
        similarity: number;
        confidence: 'high' | 'medium' | 'low';
        reason: string;
    }>;
    private static cleanArtistName;
    private static findAliasMatch;
    private static areAliases;
    private static passesAdditionalChecks;
}
export declare class GenreNormalizer {
    static normalize(genre: string): string;
    static findPotentialDuplicates(genre: string, existingGenres: string[]): Array<{
        genre: string;
        confidence: 'high' | 'medium' | 'low';
        reason: string;
    }>;
    static getGenreGroup(genre: string): string[];
    private static cleanGenre;
    private static mapToSameCanonical;
}
export declare class DataQualityService {
    static analyzeArtistDuplicates(artists: Array<{
        name: string;
        count: number;
    }>): Promise<Array<{
        canonical: string;
        duplicates: Array<{
            name: string;
            count: number;
            similarity: number;
            confidence: 'high' | 'medium' | 'low';
            reason: string;
        }>;
        totalTracks: number;
        suggestedAction: 'merge' | 'review' | 'ignore';
    }>>;
    static analyzeGenreDuplicates(genres: Array<{
        name: string;
        count: number;
    }>): Array<{
        canonical: string;
        duplicates: Array<{
            name: string;
            count: number;
            confidence: 'high' | 'medium' | 'low';
            reason: string;
        }>;
        totalTracks: number;
    }>;
}
