{"version": 3, "file": "folder-metadata-parser.js", "sourceRoot": "", "sources": ["../../../lib/utils/folder-metadata-parser.ts"], "names": [], "mappings": ";;;AAKA,6DAAuD;AAevD,MAAM,eAAe,GAAG;IAEtB;QACE,OAAO,EAAE,6BAA6B;QACtC,KAAK,EAAE,iCAAiC;QACxC,OAAO,EAAE,CAAC,KAAuB,EAAE,QAAgB,EAAE,EAAE,CAAC,CAAC;YACvD,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YACf,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC;SAC1C,CAAC;QACF,UAAU,EAAE,MAAe;KAC5B;IAGD;QACE,OAAO,EAAE,uBAAuB;QAChC,KAAK,EAAE,uBAAuB;QAC9B,OAAO,EAAE,CAAC,KAAuB,EAAE,QAAgB,EAAE,EAAE;YACrD,MAAM,MAAM,GAAG,4BAA4B,CAAC,QAAQ,CAAC,CAAA;YAGrD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACpD,OAAO;oBACL,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,wBAAwB,CAAC,QAAQ,CAAC;oBACzD,KAAK,EAAE,aAAa;iBACrB,CAAA;YACH,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,wBAAwB,CAAC,QAAQ,CAAC;aAC1D,CAAA;QACH,CAAC;QACD,UAAU,EAAE,QAAiB;KAC9B;IAGD;QACE,OAAO,EAAE,oBAAoB;QAC7B,KAAK,EAAE,4BAA4B;QACnC,OAAO,EAAE,CAAC,KAAuB,EAAE,QAAgB,EAAE,EAAE,CAAC,CAAC;YACvD,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YACf,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC;SAC1C,CAAC;QACF,UAAU,EAAE,MAAe;KAC5B;IAGD;QACE,OAAO,EAAE,2BAA2B;QACpC,KAAK,EAAE,8CAA8C;QACrD,OAAO,EAAE,CAAC,KAAuB,EAAE,QAAgB,EAAE,EAAE,CAAC,CAAC;YACvD,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YACf,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC;YACzC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACzB,CAAC;QACF,UAAU,EAAE,MAAe;KAC5B;IAGD;QACE,OAAO,EAAE,2BAA2B;QACpC,KAAK,EAAE,0CAA0C;QACjD,OAAO,EAAE,CAAC,KAAuB,EAAE,QAAgB,EAAE,EAAE,CAAC,CAAC;YACvD,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YACf,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC;YACzC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACzB,CAAC;QACF,UAAU,EAAE,MAAe;KAC5B;IAGD;QACE,OAAO,EAAE,2BAA2B;QACpC,KAAK,EAAE,2CAA2C;QAClD,OAAO,EAAE,CAAC,KAAuB,EAAE,QAAgB,EAAE,EAAE,CAAC,CAAC;YACvD,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YACf,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC;YACzC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACzB,CAAC;QACF,UAAU,EAAE,MAAe;KAC5B;IAGD;QACE,OAAO,EAAE,sBAAsB;QAC/B,KAAK,EAAE,mDAAmD;QAC1D,OAAO,EAAE,CAAC,KAAuB,EAAE,QAAgB,EAAE,EAAE;YACrD,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YAC3C,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;YAG3D,MAAM,MAAM,GAAG,4BAA4B,CAAC,QAAQ,CAAC,CAAA;YACrD,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,KAAK,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;gBACxC,IAAI;aACL,CAAA;QACH,CAAC;QACD,UAAU,EAAE,QAAiB;KAC9B;IAGD;QACE,OAAO,EAAE,6BAA6B;QACtC,KAAK,EAAE,qDAAqD;QAC5D,OAAO,EAAE,CAAC,KAAuB,EAAE,QAAgB,EAAE,EAAE;YACrD,MAAM,MAAM,GAAG,4BAA4B,CAAC,QAAQ,CAAC,CAAA;YACrD,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;gBACf,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,wBAAwB,CAAC,QAAQ,CAAC;aAC1D,CAAA;QACH,CAAC;QACD,UAAU,EAAE,QAAiB;KAC9B;IAGD;QACE,OAAO,EAAE,0BAA0B;QACnC,KAAK,EAAE,sCAAsC;QAC7C,OAAO,EAAE,CAAC,KAAuB,EAAE,QAAgB,EAAE,EAAE,CAAC,CAAC;YACvD,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YACf,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC;SAC1C,CAAC;QACF,UAAU,EAAE,QAAiB;KAC9B;IAGD;QACE,OAAO,EAAE,cAAc;QACvB,KAAK,EAAE,kBAAkB;QACzB,OAAO,EAAE,CAAC,KAAuB,EAAE,QAAgB,EAAE,EAAE,CAAC,CAAC;YACvD,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAChB,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC;SAC1C,CAAC;QACF,UAAU,EAAE,KAAc;KAC3B;CACF,CAAA;AAKD,SAAS,wBAAwB,CAAC,QAAgB;IAChD,OAAO,QAAQ;SACZ,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;SACxB,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;SAC1B,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC;SACpC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;SAClB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;SACpB,IAAI,EAAE,CAAA;AACX,CAAC;AAKD,SAAS,4BAA4B,CAAC,QAAgB;IACpD,MAAM,OAAO,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAA;IAGlD,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAA;IACnD,IAAI,KAAK,EAAE,CAAC;QACV,OAAO;YACL,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YACvB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;SACvB,CAAA;IACH,CAAC;IAGD,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;IACvC,IAAI,KAAK,EAAE,CAAC;QACV,OAAO;YACL,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE;YAC1C,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE;SAC1C,CAAA;IACH,CAAC;IAGD,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAA;IAC5D,IAAI,KAAK,EAAE,CAAC;QACV,OAAO;YACL,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YACvB,KAAK,EAAE,OAAO;SACf,CAAA;IACH,CAAC;IAED,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAA;AAC3B,CAAC;AAKD,SAAS,iBAAiB,CAAC,WAAmB,EAAE,IAAa;IAC3D,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,CAAC,CAAC,mBAAmB,IAAI,EAAE,CAAC,CAAC,CAAC,iBAAiB,CAAA;IAC7D,CAAC;SAAM,IAAI,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;QAC/C,OAAO,IAAI,CAAC,CAAC,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAA;IACjE,CAAC;SAAM,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC,CAAC,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC,CAAC,WAAW,CAAA;IACjD,CAAC;SAAM,IAAI,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACzC,OAAO,IAAI,CAAC,CAAC,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC,CAAC,YAAY,CAAA;IACnD,CAAC;IACD,OAAO,WAAW,CAAA;AACpB,CAAC;AAKD,SAAS,eAAe,CAAC,IAAY;IACnC,OAAO,IAAI;SACR,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;SACpB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;SACpB,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;SACvB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;SACrB,IAAI,EAAE,CAAA;AACX,CAAC;AAKD,MAAa,oBAAoB;IAI/B,MAAM,CAAC,KAAK,CAAC,QAAgB;QAC3B,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;YAAE,OAAO,IAAI,CAAA;QAEpD,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACrC,MAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAA;QACtD,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAGnD,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YAC7C,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;oBAGlD,MAAM,MAAM,GAAmB;wBAC7B,UAAU,EAAE,OAAO,CAAC,UAAU;wBAC9B,MAAM,EAAE,kBAAkB;wBAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;qBACzB,CAAA;oBAED,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;wBACrB,MAAM,CAAC,MAAM,GAAG,qCAAgB,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAA;oBAC/E,CAAC;oBAED,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;wBACpB,MAAM,CAAC,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;oBACjD,CAAC;oBAED,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;wBACpB,MAAM,CAAC,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;oBACjD,CAAC;oBAED,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;wBACnB,MAAM,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;oBAC9B,CAAC;oBAGD,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;wBAClD,OAAO,MAAM,CAAA;oBACf,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBAEf,SAAQ;gBACV,CAAC;YACH,CAAC;QACH,CAAC;QAGD,MAAM,YAAY,GAAG,4BAA4B,CAAC,QAAQ,CAAC,CAAA;QAC3D,IAAI,YAAY,CAAC,MAAM,IAAI,YAAY,CAAC,KAAK,EAAE,CAAC;YAC9C,OAAO;gBACL,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,qCAAgB,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;gBACzF,KAAK,EAAE,YAAY,CAAC,KAAK;gBACzB,UAAU,EAAE,KAAK;gBACjB,MAAM,EAAE,eAAe;gBACvB,OAAO,EAAE,kBAAkB;aAC5B,CAAA;QACH,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAMD,MAAM,CAAC,kBAAkB,CACvB,UAA0B,EAC1B,SAKC;QAQD,MAAM,MAAM,GAAG;YACb,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,SAAS;YACxE,OAAO,EAAE,EAAc;SACxB,CAAA;QAGD,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAA;YACjC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,UAAU,CAAC,MAAM,EAAE,CAAC,CAAA;QACzD,CAAC;aAAM,IAAI,UAAU,CAAC,UAAU,KAAK,MAAM,IAAI,UAAU,CAAC,MAAM;YACrD,UAAU,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;YAE/C,MAAM,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,qCAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YAC5F,MAAM,sBAAsB,GAAG,qCAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;YAE5E,IAAI,qBAAqB,KAAK,sBAAsB,EAAE,CAAC;gBACrD,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAA;gBACjC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,UAAU,CAAC,MAAM,EAAE,CAAC,CAAA;YACnE,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YACtC,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAA;YAC/B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,UAAU,CAAC,MAAM,EAAE,CAAC,CAAA;QACxD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YACtC,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAA;YAC/B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,UAAU,CAAC,MAAM,EAAE,CAAC,CAAA;QACxD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAA;YAC7B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,UAAU,CAAC,MAAM,EAAE,CAAC,CAAA;QACvD,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,SAAmB;QAKxC,MAAM,aAAa,GAA2B,EAAE,CAAA;QAChD,IAAI,cAAc,GAAG,CAAC,CAAA;QACtB,MAAM,WAAW,GAAa,EAAE,CAAA;QAEhC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YACnC,IAAI,MAAM,EAAE,CAAC;gBACX,cAAc,EAAE,CAAA;gBAChB,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YAC1E,CAAC;QACH,CAAC;QAGD,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAA;QACnC,MAAM,mBAAmB,GAAG,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,GAAG,CAAA;QAE/D,IAAI,mBAAmB,GAAG,EAAE,EAAE,CAAC;YAC7B,WAAW,CAAC,IAAI,CAAC,iGAAiG,CAAC,CAAA;QACrH,CAAC;QAED,IAAI,aAAa,CAAC,kBAAkB,CAAC,GAAG,cAAc,GAAG,GAAG,EAAE,CAAC;YAC7D,WAAW,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAA;QACnG,CAAC;QAED,MAAM,mBAAmB,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;aACtD,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,MAAM,CAAC;aAC9F,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAA;QAE7C,IAAI,mBAAmB,GAAG,cAAc,GAAG,GAAG,EAAE,CAAC;YAC/C,WAAW,CAAC,IAAI,CAAC,8FAA8F,CAAC,CAAA;QAClH,CAAC;QAED,OAAO;YACL,mBAAmB,EAAE,aAAa;YAClC,cAAc;YACd,WAAW;SACZ,CAAA;IACH,CAAC;IAKD,MAAM,CAAC,iBAAiB,CAAC,QAAgB;QAKvC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAEnC,IAAI,WAAW,GAAG,EAAE,CAAA;QACpB,IAAI,MAAM,EAAE,CAAC;YACX,WAAW,GAAG,qBAAqB,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,UAAU,gBAAgB,CAAA;YACvF,WAAW,IAAI,WAAW,MAAM,CAAC,MAAM,IAAI,CAAA;YAE3C,IAAI,MAAM,CAAC,MAAM;gBAAE,WAAW,IAAI,YAAY,MAAM,CAAC,MAAM,KAAK,CAAA;YAChE,IAAI,MAAM,CAAC,KAAK;gBAAE,WAAW,IAAI,WAAW,MAAM,CAAC,KAAK,KAAK,CAAA;YAC7D,IAAI,MAAM,CAAC,KAAK;gBAAE,WAAW,IAAI,WAAW,MAAM,CAAC,KAAK,KAAK,CAAA;YAC7D,IAAI,MAAM,CAAC,IAAI;gBAAE,WAAW,IAAI,SAAS,MAAM,CAAC,IAAI,IAAI,CAAA;QAC1D,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,6FAA6F,CAAA;QAC7G,CAAC;QAED,OAAO;YACL,YAAY,EAAE,QAAQ;YACtB,UAAU,EAAE,MAAM;YAClB,WAAW;SACZ,CAAA;IACH,CAAC;CACF;AA5MD,oDA4MC"}