"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateShareId = generateShareId;
exports.generateShareCode = generateShareCode;
exports.validateShareAccess = validateShareAccess;
exports.generateShareUrl = generateShareUrl;
exports.parseShareUrl = parseShareUrl;
exports.generateSocialShareContent = generateSocialShareContent;
exports.trackShareAccess = trackShareAccess;
exports.formatShareExpiration = formatShareExpiration;
const crypto_1 = __importDefault(require("crypto"));
function generateShareId() {
    return crypto_1.default.randomBytes(16).toString('base64url');
}
function generateShareCode() {
    const adjectives = [
        'happy', 'bright', 'smooth', 'swift', 'brave', 'calm', 'cool', 'epic',
        'fresh', 'jolly', 'keen', 'lively', 'merry', 'neat', 'quick', 'wise'
    ];
    const nouns = [
        'melody', 'rhythm', 'beat', 'tune', 'song', 'track', 'mix', 'vibe',
        'sound', 'music', 'note', 'chord', 'harmony', 'tempo', 'groove', 'flow'
    ];
    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    const number = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
    return `${adjective}-${noun}-${number}`;
}
function validateShareAccess(share, requestedAction) {
    if (!share.isActive) {
        return { allowed: false, reason: 'Share link has been deactivated' };
    }
    if (share.expiresAt && new Date() > new Date(share.expiresAt)) {
        return { allowed: false, reason: 'Share link has expired' };
    }
    switch (requestedAction) {
        case 'view':
            return { allowed: true };
        case 'edit':
            return {
                allowed: share.allowEdit,
                reason: share.allowEdit ? undefined : 'Edit permission not granted'
            };
        case 'copy':
            return {
                allowed: share.allowCopy,
                reason: share.allowCopy ? undefined : 'Copy permission not granted'
            };
        case 'share':
            return {
                allowed: false,
                reason: 'Cannot re-share a shared playlist'
            };
        default:
            return { allowed: false, reason: 'Unknown action' };
    }
}
function generateShareUrl(shareId, baseUrl) {
    const base = baseUrl || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    return `${base}/playlist/shared/${shareId}`;
}
function parseShareUrl(url) {
    try {
        const parsed = new URL(url);
        const pathParts = parsed.pathname.split('/');
        const sharedIndex = pathParts.findIndex(part => part === 'shared');
        if (sharedIndex !== -1 && pathParts[sharedIndex + 1]) {
            return pathParts[sharedIndex + 1];
        }
        return null;
    }
    catch {
        return null;
    }
}
function generateSocialShareContent(playlist, shareUrl, platform = 'twitter') {
    const baseContent = {
        title: `Check out "${playlist.name}" playlist`,
        description: playlist.description || `A curated playlist with ${playlist.trackCount || 0} amazing tracks`,
        url: shareUrl,
        hashtags: ['music', 'playlist', 'jukebox']
    };
    switch (platform) {
        case 'twitter':
            return {
                ...baseContent,
                title: `🎵 Check out "${playlist.name}" playlist! ${playlist.trackCount || 0} tracks of pure musical goodness`,
                hashtags: ['music', 'playlist', 'jukebox', 'nowplaying']
            };
        case 'facebook':
            return {
                ...baseContent,
                title: `Listen to "${playlist.name}"`,
                description: `I created this awesome playlist with ${playlist.trackCount || 0} tracks. Check it out!`
            };
        case 'linkedin':
            return {
                ...baseContent,
                title: `Music Playlist: "${playlist.name}"`,
                description: `Sharing a curated music playlist featuring ${playlist.trackCount || 0} tracks.`
            };
        case 'whatsapp':
            return {
                ...baseContent,
                title: `🎶 Listen to my "${playlist.name}" playlist`,
                description: `${playlist.trackCount || 0} handpicked tracks just for you!`
            };
        default:
            return baseContent;
    }
}
async function trackShareAccess(shareId) {
    try {
        await fetch('/api/internal/track-share-access', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ shareId })
        });
    }
    catch (error) {
        console.warn('Failed to track share access:', error);
    }
}
function formatShareExpiration(expiresAt) {
    if (!expiresAt)
        return 'Never expires';
    const now = new Date();
    const diff = expiresAt.getTime() - now.getTime();
    if (diff <= 0)
        return 'Expired';
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    if (days > 0)
        return `Expires in ${days} day${days > 1 ? 's' : ''}`;
    if (hours > 0)
        return `Expires in ${hours} hour${hours > 1 ? 's' : ''}`;
    if (minutes > 0)
        return `Expires in ${minutes} minute${minutes > 1 ? 's' : ''}`;
    return 'Expires soon';
}
//# sourceMappingURL=share-utils.js.map