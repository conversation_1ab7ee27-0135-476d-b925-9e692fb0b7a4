"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReleaseDateValidator = void 0;
class ReleaseDateValidator {
    static validateReleaseDate(sources) {
        const reasoning = [];
        const warnings = [];
        const validSources = sources.filter(s => s.date || s.year);
        if (validSources.length === 0) {
            return {
                finalDate: null,
                finalYear: null,
                confidence: 'low',
                sources,
                reasoning: ['No valid release date sources found'],
                warnings: ['Release date missing - this will affect "guess the year" quiz accuracy']
            };
        }
        const yearSources = validSources.map(source => ({
            ...source,
            normalizedYear: this.extractYear(source.date, source.year)
        })).filter(s => s.normalizedYear);
        if (yearSources.length === 0) {
            return {
                finalDate: null,
                finalYear: null,
                confidence: 'low',
                sources,
                reasoning: ['No valid years could be extracted from sources'],
                warnings: ['Unable to determine release year']
            };
        }
        const yearGroups = new Map();
        for (const source of yearSources) {
            const year = source.normalizedYear;
            if (!yearGroups.has(year)) {
                yearGroups.set(year, []);
            }
            yearGroups.get(year).push(source);
        }
        const result = this.selectBestYear(yearGroups, reasoning, warnings);
        return {
            finalDate: result.date,
            finalYear: result.year,
            confidence: result.confidence,
            sources,
            reasoning,
            warnings
        };
    }
    static selectBestYear(yearGroups, reasoning, warnings) {
        const sourcePriority = {
            'musicbrainz': 100,
            'spotify': 90,
            'id3_tags': 80,
            'lastfm': 70,
            'chart_data': 60,
            'folder_structure': 40,
            'filename': 20
        };
        const confidencePriority = {
            'high': 100,
            'medium': 50,
            'low': 20
        };
        const scoredYears = Array.from(yearGroups.entries()).map(([year, sources]) => {
            let score = 0;
            let maxConfidence = 'low';
            let bestSource = sources[0];
            let sourceCount = sources.length;
            for (const source of sources) {
                score += sourcePriority[source.source] || 10;
                score += confidencePriority[source.confidence] || 10;
                if (confidencePriority[source.confidence] > confidencePriority[maxConfidence]) {
                    maxConfidence = source.confidence;
                    bestSource = source;
                }
            }
            if (sourceCount > 1) {
                score += sourceCount * 25;
                reasoning.push(`Multiple sources (${sourceCount}) agree on year ${year}`);
            }
            const currentYear = new Date().getFullYear();
            if (year < 1900) {
                score -= 1000;
                warnings.push(`Year ${year} seems too old, may be incorrect`);
            }
            else if (year > currentYear) {
                score -= 500;
                warnings.push(`Year ${year} is in the future, may be incorrect`);
            }
            return {
                year,
                score,
                confidence: maxConfidence,
                sources,
                bestSource,
                sourceCount
            };
        });
        scoredYears.sort((a, b) => b.score - a.score);
        const winner = scoredYears[0];
        if (!winner) {
            return { date: null, year: null, confidence: 'low' };
        }
        if (scoredYears.length > 1) {
            const runner_up = scoredYears[1];
            const scoreDifference = winner.score - runner_up.score;
            if (scoreDifference < 50) {
                warnings.push(`Conflicting release years: ${winner.year} vs ${runner_up.year} - chose ${winner.year} with ${winner.confidence} confidence`);
            }
        }
        reasoning.push(`Selected year ${winner.year} from ${winner.bestSource.source} (${winner.confidence} confidence)`);
        if (winner.sourceCount > 1) {
            const sourceNames = winner.sources.map(s => s.source).join(', ');
            reasoning.push(`Confirmed by multiple sources: ${sourceNames}`);
        }
        let finalDate = null;
        if (winner.bestSource.date) {
            try {
                finalDate = new Date(winner.bestSource.date);
                if (isNaN(finalDate.getTime())) {
                    finalDate = new Date(winner.year, 0, 1);
                }
            }
            catch {
                finalDate = new Date(winner.year, 0, 1);
            }
        }
        else {
            finalDate = new Date(winner.year, 0, 1);
        }
        return {
            date: finalDate,
            year: winner.year,
            confidence: winner.confidence
        };
    }
    static extractYear(date, year) {
        if (year && year > 1800 && year <= new Date().getFullYear() + 1) {
            return year;
        }
        if (!date)
            return null;
        try {
            let dateObj;
            if (date instanceof Date) {
                dateObj = date;
            }
            else if (typeof date === 'string') {
                if (/^\d{4}$/.test(date)) {
                    return parseInt(date);
                }
                else if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
                    dateObj = new Date(date);
                }
                else if (/^\d{4}-\d{2}$/.test(date)) {
                    dateObj = new Date(date + '-01');
                }
                else {
                    dateObj = new Date(date);
                }
            }
            else {
                return null;
            }
            if (isNaN(dateObj.getTime())) {
                return null;
            }
            const extractedYear = dateObj.getFullYear();
            if (extractedYear < 1800 || extractedYear > new Date().getFullYear() + 1) {
                return null;
            }
            return extractedYear;
        }
        catch {
            return null;
        }
    }
    static createMusicBrainzSource(data) {
        const releaseDate = data?.releaseDate || data?.date || data?.first_release_date;
        const year = data?.year || (releaseDate ? this.extractYear(releaseDate, null) : null);
        return {
            date: releaseDate,
            year,
            source: 'musicbrainz',
            confidence: releaseDate ? 'high' : (year ? 'medium' : 'low'),
            metadata: {
                mbid: data?.id || data?.mbid,
                release_group: data?.release_group,
                country: data?.country
            }
        };
    }
    static createSpotifySource(data) {
        const releaseDate = data?.album?.release_date || data?.release_date;
        const precision = data?.album?.release_date_precision || data?.release_date_precision;
        const year = releaseDate ? this.extractYear(releaseDate, null) : null;
        let confidence = 'medium';
        if (precision === 'day')
            confidence = 'high';
        else if (precision === 'month')
            confidence = 'medium';
        else if (precision === 'year')
            confidence = 'medium';
        else
            confidence = 'low';
        return {
            date: releaseDate,
            year,
            source: 'spotify',
            confidence,
            metadata: {
                album_id: data?.album?.id,
                precision,
                album_type: data?.album?.album_type
            }
        };
    }
    static createLastFmSource(data) {
        const releaseDate = data?.album?.releasedate || data?.wiki?.published;
        const year = releaseDate ? this.extractYear(releaseDate, null) : null;
        return {
            date: releaseDate,
            year,
            source: 'lastfm',
            confidence: releaseDate ? 'medium' : 'low',
            metadata: {
                album_mbid: data?.album?.mbid,
                wiki_summary: data?.wiki?.summary
            }
        };
    }
    static createChartDataSource(chartInfo) {
        const chartDate = chartInfo?.chartDate;
        const chartYear = chartInfo?.chartYear;
        const year = chartYear || (chartDate ? this.extractYear(chartDate, null) : null);
        return {
            date: chartDate,
            year,
            source: 'chart_data',
            confidence: chartYear ? 'medium' : 'low',
            metadata: {
                chart_type: chartInfo?.chartType,
                chart_position: chartInfo?.chartPosition,
                chart_country: chartInfo?.chartCountry
            }
        };
    }
    static createId3TagsSource(data) {
        const date = data?.date || data?.year || data?.originalDate || data?.recordingDate;
        const year = data?.year || this.extractYear(date, null);
        return {
            date,
            year,
            source: 'id3_tags',
            confidence: date && date.length > 4 ? 'high' : (year ? 'medium' : 'low'),
            metadata: {
                format: 'id3',
                has_original_date: !!data?.originalDate,
                has_recording_date: !!data?.recordingDate
            }
        };
    }
    static createFolderSource(folderMetadata) {
        const year = folderMetadata?.year;
        return {
            date: year ? new Date(year, 0, 1) : null,
            year,
            source: 'folder_structure',
            confidence: year ? 'medium' : 'low',
            metadata: {
                pattern: folderMetadata?.pattern,
                folder_path: folderMetadata?.source
            }
        };
    }
    static validateYear(year) {
        const warnings = [];
        const currentYear = new Date().getFullYear();
        if (!year || isNaN(year)) {
            return {
                isValid: false,
                warnings: ['Year is missing or invalid'],
                confidence: 'low'
            };
        }
        if (year < 1900) {
            warnings.push(`Year ${year} is very old, may be incorrect`);
            return {
                isValid: false,
                warnings,
                confidence: 'low'
            };
        }
        if (year > currentYear) {
            warnings.push(`Year ${year} is in the future`);
            return {
                isValid: false,
                warnings,
                confidence: 'low'
            };
        }
        let confidence = 'medium';
        if (year >= 1950 && year <= currentYear) {
            confidence = 'high';
        }
        else if (year >= 1900 && year < 1950) {
            confidence = 'medium';
            warnings.push(`Year ${year} is quite old, please verify accuracy`);
        }
        return {
            isValid: true,
            warnings,
            confidence
        };
    }
}
exports.ReleaseDateValidator = ReleaseDateValidator;
//# sourceMappingURL=release-date-validator.js.map