"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResilientApiClient = exports.CircuitBreaker = exports.RateLimiter = exports.RetryHandler = void 0;
const api_cache_async_1 = require("./api-cache-async");
class RetryHandler {
    static async execute(operation, options = {}) {
        const config = { ...this.defaultOptions, ...options };
        let lastError;
        for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error;
                if (attempt === config.maxRetries || !config.retryCondition(error)) {
                    break;
                }
                const delay = Math.min(config.baseDelay * Math.pow(config.backoffMultiplier, attempt), config.maxDelay);
                const jitteredDelay = delay + Math.random() * 1000;
                console.log(`[RetryHand<PERSON>] Attempt ${attempt + 1}/${config.maxRetries + 1} failed, retrying in ${Math.round(jitteredDelay)}ms...`);
                await this.sleep(jitteredDelay);
            }
        }
        throw lastError;
    }
    static sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.RetryHandler = RetryHandler;
RetryHandler.defaultOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
    retryCondition: (error) => {
        return error.code === 'ECONNRESET' ||
            error.code === 'ETIMEDOUT' ||
            (error.status >= 500 && error.status < 600) ||
            error.status === 429;
    }
};
class RateLimiter {
    constructor(config) {
        this.requests = [];
        this.config = config;
    }
    async acquire() {
        const now = Date.now();
        this.requests = this.requests.filter(time => now - time < 1000);
        if (this.requests.length >= this.config.requestsPerSecond) {
            const oldestRequest = Math.min(...this.requests);
            const waitTime = 1000 - (now - oldestRequest) + 10;
            if (waitTime > 0) {
                console.log(`[RateLimiter] Rate limit reached, waiting ${waitTime}ms...`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
        this.requests.push(Date.now());
    }
}
exports.RateLimiter = RateLimiter;
class CircuitBreaker {
    constructor(config) {
        this.failures = 0;
        this.lastFailureTime = 0;
        this.state = 'closed';
        this.config = config;
    }
    async execute(operation) {
        if (this.state === 'open') {
            if (Date.now() - this.lastFailureTime < this.config.resetTimeout) {
                throw new Error(`Circuit breaker is OPEN - service unavailable`);
            }
            else {
                this.state = 'half-open';
                console.log('[CircuitBreaker] Transitioning to HALF-OPEN state');
            }
        }
        try {
            const result = await operation();
            if (this.state === 'half-open') {
                this.reset();
            }
            return result;
        }
        catch (error) {
            this.recordFailure();
            throw error;
        }
    }
    recordFailure() {
        this.failures++;
        this.lastFailureTime = Date.now();
        if (this.failures >= this.config.failureThreshold) {
            this.state = 'open';
            console.log(`[CircuitBreaker] Circuit breaker is now OPEN after ${this.failures} failures`);
        }
    }
    reset() {
        this.failures = 0;
        this.state = 'closed';
        console.log('[CircuitBreaker] Circuit breaker reset to CLOSED state');
    }
    getState() {
        return this.state;
    }
}
exports.CircuitBreaker = CircuitBreaker;
class ResilientApiClient {
    constructor() {
        this.rateLimiters = new Map();
        this.circuitBreakers = new Map();
        this.rateLimiters.set('spotify', new RateLimiter({ requestsPerSecond: 10, burstLimit: 100 }));
        this.rateLimiters.set('lastfm', new RateLimiter({ requestsPerSecond: 5, burstLimit: 50 }));
        this.rateLimiters.set('musicbrainz', new RateLimiter({ requestsPerSecond: 1, burstLimit: 10 }));
        this.circuitBreakers.set('spotify', new CircuitBreaker({
            failureThreshold: 5,
            resetTimeout: 60000,
            monitoringPeriod: 300000
        }));
        this.circuitBreakers.set('lastfm', new CircuitBreaker({
            failureThreshold: 3,
            resetTimeout: 30000,
            monitoringPeriod: 180000
        }));
        this.circuitBreakers.set('musicbrainz', new CircuitBreaker({
            failureThreshold: 3,
            resetTimeout: 120000,
            monitoringPeriod: 600000
        }));
    }
    async fetch(service, url, options = {}, retryOptions = {}, useCache = true) {
        const cache = api_cache_async_1.CacheManager.getCache(service);
        const cacheKey = this.generateCacheKey(url, options);
        if (useCache && (!options.method || options.method.toUpperCase() === 'GET')) {
            const cachedResponse = cache.get(service, url, { headers: options.headers });
            if (cachedResponse) {
                return new Response(JSON.stringify(cachedResponse), {
                    status: 200,
                    statusText: 'OK (Cached)',
                    headers: { 'Content-Type': 'application/json' }
                });
            }
        }
        const rateLimiter = this.rateLimiters.get(service);
        const circuitBreaker = this.circuitBreakers.get(service);
        if (rateLimiter) {
            await rateLimiter.acquire();
        }
        const operation = async () => {
            const response = await fetch(url, options);
            if (!response.ok) {
                const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
                error.status = response.status;
                throw error;
            }
            if (useCache && (!options.method || options.method.toUpperCase() === 'GET')) {
                try {
                    const clonedResponse = response.clone();
                    const data = await clonedResponse.json();
                    cache.set(service, url, data, { headers: options.headers });
                }
                catch (error) {
                }
            }
            return response;
        };
        if (circuitBreaker) {
            return await circuitBreaker.execute(() => RetryHandler.execute(operation, retryOptions));
        }
        else {
            return await RetryHandler.execute(operation, retryOptions);
        }
    }
    generateCacheKey(url, options) {
        return `${options.method || 'GET'}:${url}:${JSON.stringify(options.headers || {})}`;
    }
    getCircuitBreakerState(service) {
        return this.circuitBreakers.get(service)?.getState() || 'unknown';
    }
}
exports.ResilientApiClient = ResilientApiClient;
//# sourceMappingURL=api-resilience.js.map