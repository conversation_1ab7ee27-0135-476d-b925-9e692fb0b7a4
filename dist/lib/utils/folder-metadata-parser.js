"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FolderMetadataParser = void 0;
const data_normalization_1 = require("./data-normalization");
const FOLDER_PATTERNS = [
    {
        pattern: 'MyItunes/Artist/Album/Track',
        regex: /^MyItunes\/([^\/]+)\/([^\/]+)$/i,
        extract: (match, filename) => ({
            artist: match[1],
            album: match[2],
            title: extractTitleFromFilename(filename)
        }),
        confidence: 'high'
    },
    {
        pattern: 'MyItunes/Artist/Track',
        regex: /^MyItunes\/([^\/]+)$/i,
        extract: (match, filename) => {
            const parsed = parseArtistTitleFromFilename(filename);
            if (match[1] === '-Neu' || match[1].startsWith('-')) {
                return {
                    artist: parsed.artist,
                    title: parsed.title || extractTitleFromFilename(filename),
                    album: 'New Singles'
                };
            }
            return {
                artist: match[1],
                title: parsed.title || extractTitleFromFilename(filename)
            };
        },
        confidence: 'medium'
    },
    {
        pattern: 'Artist/Album/Track',
        regex: /^([^\/]+)\/([^\/]+)\/(.+)$/,
        extract: (match, filename) => ({
            artist: match[1],
            album: match[2],
            title: extractTitleFromFilename(filename)
        }),
        confidence: 'high'
    },
    {
        pattern: 'Artist/Year - Album/Track',
        regex: /^([^\/]+)\/(\d{4})\s*[-–—]\s*([^\/]+)\/(.+)$/,
        extract: (match, filename) => ({
            artist: match[1],
            album: match[3],
            title: extractTitleFromFilename(filename),
            year: parseInt(match[2])
        }),
        confidence: 'high'
    },
    {
        pattern: 'Artist/(Year) Album/Track',
        regex: /^([^\/]+)\/\((\d{4})\)\s*([^\/]+)\/(.+)$/,
        extract: (match, filename) => ({
            artist: match[1],
            album: match[3],
            title: extractTitleFromFilename(filename),
            year: parseInt(match[2])
        }),
        confidence: 'high'
    },
    {
        pattern: 'Artist/Album (Year)/Track',
        regex: /^([^\/]+)\/([^\/]+?)\s*\((\d{4})\)\/(.+)$/,
        extract: (match, filename) => ({
            artist: match[1],
            album: match[2],
            title: extractTitleFromFilename(filename),
            year: parseInt(match[3])
        }),
        confidence: 'high'
    },
    {
        pattern: 'Chart/Artist - Title',
        regex: /^(DC\d{4}|Billboard\d{4}|UK\d{4}|Top\d{4})\/(.+)$/,
        extract: (match, filename) => {
            const yearMatch = match[1].match(/(\d{4})/);
            const year = yearMatch ? parseInt(yearMatch[1]) : undefined;
            const parsed = parseArtistTitleFromFilename(filename);
            return {
                artist: parsed.artist,
                title: parsed.title,
                album: getChartAlbumName(match[1], year),
                year
            };
        },
        confidence: 'medium'
    },
    {
        pattern: 'Various Artists/Album/Track',
        regex: /^(Various Artists?|VA|Compilation)\/([^\/]+)\/(.+)$/,
        extract: (match, filename) => {
            const parsed = parseArtistTitleFromFilename(filename);
            return {
                artist: parsed.artist,
                album: match[2],
                title: parsed.title || extractTitleFromFilename(filename)
            };
        },
        confidence: 'medium'
    },
    {
        pattern: 'Genre/Artist/Album/Track',
        regex: /^([^\/]+)\/([^\/]+)\/([^\/]+)\/(.+)$/,
        extract: (match, filename) => ({
            artist: match[2],
            album: match[3],
            title: extractTitleFromFilename(filename)
        }),
        confidence: 'medium'
    },
    {
        pattern: 'Artist/Track',
        regex: /^([^\/]+)\/(.+)$/,
        extract: (match, filename) => ({
            artist: match[1],
            title: extractTitleFromFilename(filename)
        }),
        confidence: 'low'
    }
];
function extractTitleFromFilename(filename) {
    return filename
        .replace(/\.[^/.]+$/, '')
        .replace(/^\d+[-.\s]*/, '')
        .replace(/^\d+\s*-\s*\d+\s*-\s*/, '')
        .replace(/_/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();
}
function parseArtistTitleFromFilename(filename) {
    const cleaned = extractTitleFromFilename(filename);
    let match = cleaned.match(/^(.+?)\s*[-–—]\s*(.+)$/);
    if (match) {
        return {
            artist: match[1].trim(),
            title: match[2].trim()
        };
    }
    match = cleaned.match(/^(.+?)_-_(.+)$/);
    if (match) {
        return {
            artist: match[1].replace(/_/g, ' ').trim(),
            title: match[2].replace(/_/g, ' ').trim()
        };
    }
    match = cleaned.match(/^(.+?)\s*(?:feat\.?|ft\.?)\s*(.+)$/i);
    if (match) {
        return {
            artist: match[1].trim(),
            title: cleaned
        };
    }
    return { title: cleaned };
}
function getChartAlbumName(chartFolder, year) {
    if (chartFolder.startsWith('DC')) {
        return year ? `Deutsche Charts ${year}` : 'Deutsche Charts';
    }
    else if (chartFolder.startsWith('Billboard')) {
        return year ? `Billboard Hot 100 ${year}` : 'Billboard Hot 100';
    }
    else if (chartFolder.startsWith('UK')) {
        return year ? `UK Charts ${year}` : 'UK Charts';
    }
    else if (chartFolder.startsWith('Top')) {
        return year ? `Top Charts ${year}` : 'Top Charts';
    }
    return chartFolder;
}
function cleanFolderText(text) {
    return text
        .replace(/[_]/g, ' ')
        .replace(/\s+/g, ' ')
        .replace(/[""'']/g, '"')
        .replace(/[–—]/g, '-')
        .trim();
}
class FolderMetadataParser {
    static parse(filePath) {
        if (!filePath || filePath.trim() === '')
            return null;
        const pathParts = filePath.split('/');
        const filename = pathParts[pathParts.length - 1] || '';
        const folderPath = pathParts.slice(0, -1).join('/');
        for (const pattern of FOLDER_PATTERNS) {
            const match = folderPath.match(pattern.regex);
            if (match) {
                try {
                    const extracted = pattern.extract(match, filename);
                    const result = {
                        confidence: pattern.confidence,
                        source: 'folder_structure',
                        pattern: pattern.pattern
                    };
                    if (extracted.artist) {
                        result.artist = data_normalization_1.ArtistNormalizer.normalize(cleanFolderText(extracted.artist));
                    }
                    if (extracted.album) {
                        result.album = cleanFolderText(extracted.album);
                    }
                    if (extracted.title) {
                        result.title = cleanFolderText(extracted.title);
                    }
                    if (extracted.year) {
                        result.year = extracted.year;
                    }
                    if (result.artist || result.album || result.title) {
                        return result;
                    }
                }
                catch (error) {
                    continue;
                }
            }
        }
        const filenameData = parseArtistTitleFromFilename(filename);
        if (filenameData.artist || filenameData.title) {
            return {
                artist: filenameData.artist ? data_normalization_1.ArtistNormalizer.normalize(filenameData.artist) : undefined,
                title: filenameData.title,
                confidence: 'low',
                source: 'filename_only',
                pattern: 'filename_parsing'
            };
        }
        return null;
    }
    static mergeWithTrackData(folderData, trackData) {
        const result = {
            artist: trackData.artist,
            title: trackData.title,
            album: trackData.album,
            year: trackData.date ? parseInt(trackData.date) || undefined : undefined,
            sources: []
        };
        if (!result.artist && folderData.artist) {
            result.artist = folderData.artist;
            result.sources.push(`artist_from_${folderData.source}`);
        }
        else if (folderData.confidence === 'high' && folderData.artist &&
            folderData.artist !== result.artist) {
            const normalizedTrackArtist = result.artist ? data_normalization_1.ArtistNormalizer.normalize(result.artist) : '';
            const normalizedFolderArtist = data_normalization_1.ArtistNormalizer.normalize(folderData.artist);
            if (normalizedTrackArtist !== normalizedFolderArtist) {
                result.artist = folderData.artist;
                result.sources.push(`artist_corrected_from_${folderData.source}`);
            }
        }
        if (!result.title && folderData.title) {
            result.title = folderData.title;
            result.sources.push(`title_from_${folderData.source}`);
        }
        if (!result.album && folderData.album) {
            result.album = folderData.album;
            result.sources.push(`album_from_${folderData.source}`);
        }
        if (!result.year && folderData.year) {
            result.year = folderData.year;
            result.sources.push(`year_from_${folderData.source}`);
        }
        return result;
    }
    static analyzePatterns(filePaths) {
        const patternCounts = {};
        let totalParseable = 0;
        const suggestions = [];
        for (const filePath of filePaths) {
            const parsed = this.parse(filePath);
            if (parsed) {
                totalParseable++;
                patternCounts[parsed.pattern] = (patternCounts[parsed.pattern] || 0) + 1;
            }
        }
        const totalFiles = filePaths.length;
        const parseablePercentage = (totalParseable / totalFiles) * 100;
        if (parseablePercentage < 70) {
            suggestions.push('Consider organizing files in Artist/Album/Track folder structure for better metadata extraction');
        }
        if (patternCounts['filename_parsing'] > totalParseable * 0.5) {
            suggestions.push('Many files rely on filename parsing. Consider using folder-based organization');
        }
        const highConfidenceCount = Object.entries(patternCounts)
            .filter(([pattern]) => FOLDER_PATTERNS.find(p => p.pattern === pattern)?.confidence === 'high')
            .reduce((sum, [, count]) => sum + count, 0);
        if (highConfidenceCount < totalParseable * 0.5) {
            suggestions.push('Consider using more structured folder patterns like "Artist/Album/Track" for better accuracy');
        }
        return {
            patternDistribution: patternCounts,
            totalParseable,
            suggestions
        };
    }
    static getParsingExample(filePath) {
        const parsed = this.parse(filePath);
        let explanation = '';
        if (parsed) {
            explanation = `Detected pattern: ${parsed.pattern} (${parsed.confidence} confidence)\n`;
            explanation += `Source: ${parsed.source}\n`;
            if (parsed.artist)
                explanation += `Artist: "${parsed.artist}"\n`;
            if (parsed.album)
                explanation += `Album: "${parsed.album}"\n`;
            if (parsed.title)
                explanation += `Title: "${parsed.title}"\n`;
            if (parsed.year)
                explanation += `Year: ${parsed.year}\n`;
        }
        else {
            explanation = 'No recognizable folder pattern found. Consider reorganizing for better metadata extraction.';
        }
        return {
            originalPath: filePath,
            parsedData: parsed,
            explanation
        };
    }
}
exports.FolderMetadataParser = FolderMetadataParser;
//# sourceMappingURL=folder-metadata-parser.js.map