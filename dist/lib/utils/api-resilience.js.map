{"version": 3, "file": "api-resilience.js", "sourceRoot": "", "sources": ["../../../lib/utils/api-resilience.ts"], "names": [], "mappings": ";;;AAKA,uDAAgD;AAwBhD,MAAa,YAAY;IAevB,MAAM,CAAC,KAAK,CAAC,OAAO,CAClB,SAA2B,EAC3B,UAAiC,EAAE;QAEnC,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAA;QACrD,IAAI,SAAc,CAAA;QAElB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YAC9D,IAAI,CAAC;gBACH,OAAO,MAAM,SAAS,EAAE,CAAA;YAC1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,CAAA;gBAGjB,IAAI,OAAO,KAAK,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,cAAe,CAAC,KAAK,CAAC,EAAE,CAAC;oBACpE,MAAK;gBACP,CAAC;gBAGD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CACpB,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,iBAAiB,EAAE,OAAO,CAAC,EAC9D,MAAM,CAAC,QAAQ,CAChB,CAAA;gBACD,MAAM,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAA;gBAElD,OAAO,CAAC,GAAG,CAAC,0BAA0B,OAAO,GAAG,CAAC,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC,wBAAwB,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;gBACnI,MAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;YACjC,CAAC;QACH,CAAC;QAED,MAAM,SAAS,CAAA;IACjB,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,EAAU;QAC7B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;IACxD,CAAC;;AAlDH,oCAmDC;AAlDgB,2BAAc,GAAiB;IAC5C,UAAU,EAAE,CAAC;IACb,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,KAAK;IACf,iBAAiB,EAAE,CAAC;IACpB,cAAc,EAAE,CAAC,KAAK,EAAE,EAAE;QAExB,OAAO,KAAK,CAAC,IAAI,KAAK,YAAY;YAC3B,KAAK,CAAC,IAAI,KAAK,WAAW;YAC1B,CAAC,KAAK,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;YAC3C,KAAK,CAAC,MAAM,KAAK,GAAG,CAAA;IAC7B,CAAC;CACF,CAAA;AA2CH,MAAa,WAAW;IAItB,YAAY,MAAuB;QAH3B,aAAQ,GAAa,EAAE,CAAA;QAI7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAGtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAA;QAG/D,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC1D,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;YAChD,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,CAAA;YAElD,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACjB,OAAO,CAAC,GAAG,CAAC,6CAA6C,QAAQ,OAAO,CAAC,CAAA;gBACzE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAA;YAC7D,CAAC;QACH,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;IAChC,CAAC;CACF;AA3BD,kCA2BC;AAKD,MAAa,cAAc;IAMzB,YAAY,MAA4B;QALhC,aAAQ,GAAG,CAAC,CAAA;QACZ,oBAAe,GAAG,CAAC,CAAA;QACnB,UAAK,GAAoC,QAAQ,CAAA;QAIvD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;IAED,KAAK,CAAC,OAAO,CAAI,SAA2B;QAC1C,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBACjE,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAA;YAClE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,GAAG,WAAW,CAAA;gBACxB,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAA;YAClE,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAA;YAEhC,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;gBAC/B,IAAI,CAAC,KAAK,EAAE,CAAA;YACd,CAAC;YAED,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,EAAE,CAAA;YACpB,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,QAAQ,EAAE,CAAA;QACf,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEjC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAClD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAA;YACnB,OAAO,CAAC,GAAG,CAAC,sDAAsD,IAAI,CAAC,QAAQ,WAAW,CAAC,CAAA;QAC7F,CAAC;IACH,CAAC;IAEO,KAAK;QACX,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA;QACjB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAA;QACrB,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAA;IACvE,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;CACF;AArDD,wCAqDC;AAKD,MAAa,kBAAkB;IAI7B;QAHQ,iBAAY,GAAG,IAAI,GAAG,EAAuB,CAAA;QAC7C,oBAAe,GAAG,IAAI,GAAG,EAA0B,CAAA;QAIzD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,WAAW,CAAC,EAAE,iBAAiB,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC,CAAA;QAC7F,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,WAAW,CAAC,EAAE,iBAAiB,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC,CAAA;QAC1F,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,WAAW,CAAC,EAAE,iBAAiB,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC,CAAA;QAG/F,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,cAAc,CAAC;YACrD,gBAAgB,EAAE,CAAC;YACnB,YAAY,EAAE,KAAK;YACnB,gBAAgB,EAAE,MAAM;SACzB,CAAC,CAAC,CAAA;QACH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,cAAc,CAAC;YACpD,gBAAgB,EAAE,CAAC;YACnB,YAAY,EAAE,KAAK;YACnB,gBAAgB,EAAE,MAAM;SACzB,CAAC,CAAC,CAAA;QACH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,cAAc,CAAC;YACzD,gBAAgB,EAAE,CAAC;YACnB,YAAY,EAAE,MAAM;YACpB,gBAAgB,EAAE,MAAM;SACzB,CAAC,CAAC,CAAA;IACL,CAAC;IAED,KAAK,CAAC,KAAK,CACT,OAAe,EACf,GAAW,EACX,UAAuB,EAAE,EACzB,eAAsC,EAAE,EACxC,WAAoB,IAAI;QAExB,MAAM,KAAK,GAAG,8BAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;QAGpD,IAAI,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;YAC5E,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;YAC5E,IAAI,cAAc,EAAE,CAAC;gBAEnB,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;oBAClD,MAAM,EAAE,GAAG;oBACX,UAAU,EAAE,aAAa;oBACzB,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;iBAChD,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAExD,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QAC7B,CAAC;QAED,MAAM,SAAS,GAAG,KAAK,IAAI,EAAE;YAC3B,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;YAE1C,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CACzE;gBAAC,KAAa,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAA;gBACxC,MAAM,KAAK,CAAA;YACb,CAAC;YAGD,IAAI,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;gBAC5E,IAAI,CAAC;oBACH,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAA;oBACvC,MAAM,IAAI,GAAG,MAAM,cAAc,CAAC,IAAI,EAAE,CAAA;oBACxC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;gBAC7D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;gBAEjB,CAAC;YACH,CAAC;YAED,OAAO,QAAQ,CAAA;QACjB,CAAC,CAAA;QAED,IAAI,cAAc,EAAE,CAAC;YACnB,OAAO,MAAM,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,CACvC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC,CAC9C,CAAA;QACH,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC,CAAA;QAC5D,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,GAAW,EAAE,OAAoB;QACxD,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,EAAE,CAAA;IACrF,CAAC;IAED,sBAAsB,CAAC,OAAe;QACpC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,SAAS,CAAA;IACnE,CAAC;CACF;AAjGD,gDAiGC"}