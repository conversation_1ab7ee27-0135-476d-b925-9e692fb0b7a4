export interface FolderMetadata {
    artist?: string;
    album?: string;
    title?: string;
    year?: number;
    confidence: 'high' | 'medium' | 'low';
    source: string;
    pattern: string;
}
export declare class FolderMetadataParser {
    static parse(filePath: string): FolderMetadata | null;
    static mergeWithTrackData(folderData: FolderMetadata, trackData: {
        artist?: string;
        title?: string;
        album?: string;
        date?: string;
    }): {
        artist?: string;
        title?: string;
        album?: string;
        year?: number;
        sources: string[];
    };
    static analyzePatterns(filePaths: string[]): {
        patternDistribution: Record<string, number>;
        totalParseable: number;
        suggestions: string[];
    };
    static getParsingExample(filePath: string): {
        originalPath: string;
        parsedData: FolderMetadata | null;
        explanation: string;
    };
}
