import type { MP<PERSON><PERSON><PERSON>, MPDStatus, MPDConnectionConfig, MPDPlaylist } from "@/lib/types";
export type { MPD<PERSON>rack, MPDStatus, MPDConnectionConfig, MPDPlaylist };
export declare class MPDClient {
    private config;
    private isConnected;
    private baseUrl;
    private lastErrorToastTime;
    private errorToastCooldown;
    constructor(config: MPDConnectionConfig);
    connect(): Promise<void>;
    getConnectionStatus(): boolean;
    disconnect(): Promise<void>;
    play(songPos?: number): Promise<void>;
    pause(): Promise<void>;
    resume(): Promise<void>;
    stop(): Promise<void>;
    next(): Promise<void>;
    addTrackGetId(file: string): Promise<number>;
    previous(): Promise<void>;
    seek(time: number): Promise<void>;
    seekId(songId: number, time: number): Promise<void>;
    setVolume(volume: number): Promise<void>;
    clearPlaylist(): Promise<void>;
    addTrack(file: string): Promise<void>;
    addAndPlay(file: string): Promise<void>;
    commandList(commands: string[]): Promise<string>;
    removeTrack(pos: number): Promise<void>;
    moveTrack(from: number, to: number): Promise<void>;
    loadPlaylist(name: string): Promise<void>;
    getPlaylistTracks(name: string): Promise<MPDTrack[]>;
    addPlaylistToQueue(name: string, clearFirst?: boolean): Promise<void>;
    savePlaylist(name: string): Promise<void>;
    getCurrentPlaylist(): Promise<MPDTrack[]>;
    getStatus(): Promise<MPDStatus>;
    getCurrentSong(): Promise<MPDTrack | null>;
    getStats(): Promise<any>;
    search(type: string, query: string): Promise<MPDTrack[]>;
    find(type: string, query: string): Promise<MPDTrack[]>;
    listAllTracks(): Promise<MPDTrack[]>;
    private listAllTracksWithRetries;
    private listAllTracksByDirectory;
    listArtists(): Promise<string[]>;
    listAlbums(): Promise<string[]>;
    listGenres(): Promise<string[]>;
    listPlaylists(): Promise<MPDPlaylist[]>;
    setRepeat(enabled: boolean): Promise<void>;
    setRandom(enabled: boolean): Promise<void>;
    setSingle(enabled: boolean): Promise<void>;
    setConsume(enabled: boolean): Promise<void>;
    sendRawCommand(command: string): Promise<string>;
    private sendCommand;
    private parseStatus;
    private parseTracks;
    private parseKeyValue;
}
