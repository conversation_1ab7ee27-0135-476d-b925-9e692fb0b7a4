interface AlbumArtUrls {
    thumbnail: string;
    cover: string;
    original: string;
}
interface AlbumArtProcessingResult {
    success: boolean;
    paths?: AlbumArtUrls;
    hash?: string;
    error?: string;
}
export declare class AlbumArtProcessor {
    private static readonly BASE_DIR;
    private static readonly THUMBNAILS_DIR;
    private static readonly COVERS_DIR;
    private static readonly ORIGINALS_DIR;
    private static readonly THUMBNAIL_SIZE;
    private static readonly COVER_SIZE;
    private static readonly QUALITY;
    static initialize(): Promise<void>;
    static generateHash(artist: string, album: string): string;
    static processAlbumArt(trackId: string, artist: string, album: string, sourceUrl?: string): Promise<AlbumArtProcessingResult>;
    private static downloadImage;
    private static extractAlbumArtFromFile;
    private static getPlaceholderImage;
    private static processAndSaveImages;
    private static updateTrackWithAlbumArt;
    static batchProcessAlbumArt(limit?: number, onProgress?: (processed: number, total: number) => void): Promise<void>;
    static cleanup(): Promise<void>;
    private static extractHashFromFilename;
    static getAlbumArt(trackId: string): Promise<AlbumArtUrls | null>;
}
export {};
