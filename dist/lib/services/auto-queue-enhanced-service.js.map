{"version": 3, "file": "auto-queue-enhanced-service.js", "sourceRoot": "", "sources": ["../../../lib/services/auto-queue-enhanced-service.ts"], "names": [], "mappings": ";;;AAKA,+EAAwE;AACxE,6EAAuE;AACvE,qEAAgE;AAChE,uDAAsD;AAoBtD,MAAa,wBAAwB;IAYnC,MAAM,CAAC,uBAAuB,CAAC,QAAuC;QACpE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC3C,CAAC;IAKD,MAAM,CAAC,0BAA0B,CAAC,QAAuC;QACvE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAA;IACrF,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,cAA2B,SAAS;QACrD,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAA;YAG9C,MAAM,MAAM,GAAG,MAAM,sDAAwB,CAAC,eAAe,EAAE,CAAA;YAE/D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,IAAI,CAAC,gBAAgB,CAAC;oBACpB,IAAI,EAAE,OAAO;oBACb,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,+BAA+B;oBACxC,OAAO,EAAE,EAAE,MAAM,EAAE,qDAAqD,EAAE;iBAC3E,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAED,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,EAAE,CAAA;YAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;YAGrB,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW,CAAC,CAAA;YAE1D,IAAI,CAAC,gBAAgB,CAAC;gBACpB,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,KAAK,EAAE,oBAAoB;gBAC3B,OAAO,EAAE,UAAU,MAAM,CAAC,IAAI,iBAAiB;gBAC/C,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,MAAM,EAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,SAAS,CAAC;iBACvD;aACF,CAAC,CAAA;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAA;YAC5D,IAAI,CAAC,gBAAgB,CAAC;gBACpB,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,KAAK,EAAE,kBAAkB;gBACzB,OAAO,EAAE,mCAAmC;gBAC5C,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAC9E,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,QAAgB,EAAE,WAAwB;QACrF,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,KAAK,QAAQ;gBAAE,OAAM;YAEhE,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,sDAAwB,CAAC,eAAe,EAAE,CAAA;gBAC/D,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,QAAQ,EAAE,CAAC;oBACtC,IAAI,CAAC,IAAI,EAAE,CAAA;oBACX,OAAM;gBACR,CAAC;gBAGD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAA;gBAC7D,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,GAAG,EAAE,CAAA;gBAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,GAAG,kBAAkB,CAAA;gBAE5D,IAAI,SAAS,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;oBACpC,IAAI,CAAC,oBAAoB,GAAG,UAAU,GAAG,SAAS,CAAA;gBACpD,CAAC;gBAED,IAAI,CAAC,eAAe,GAAG,kBAAkB,CAAA;gBACzC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBAG/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;gBAGhD,MAAM,YAAY,GAAG,MAAM,sDAAwB,CAAC,mBAAmB,CACrE,QAAQ,EACR,IAAI,CAAC,oBAAoB,CAC1B,CAAA;gBAGD,IAAI,IAAI,CAAC,aAAa;oBAAE,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;gBACxD,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,YAAY,EAAE,YAAY,GAAG,IAAI,CAAC,CAAA;YAEpE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAA;YAC3D,CAAC;QACH,CAAC,CAAA;QAGD,MAAM,YAAY,EAAE,CAAA;IACtB,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAW,EAAE,WAAwB;QACzE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAGxD,MAAM,WAAW,GAAG,MAAM,oCAAkB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QACxE,IAAI,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAClD,OAAM;QACR,CAAC;QAGD,IAAI,SAAS,GAAG,KAAK,CAAA;QACrB,IAAI,MAAM,GAAG,EAAE,CAAA;QACf,IAAI,UAAU,GAAG,CAAC,CAAA;QAElB,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;YACxB,SAAS,GAAG,IAAI,CAAA;YAChB,MAAM,GAAG,gBAAgB,CAAA;YACzB,UAAU,GAAG,MAAM,CAAC,kBAAkB,CAAA;QACxC,CAAC;aAAM,IAAI,aAAa,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YAClD,SAAS,GAAG,IAAI,CAAA;YAChB,MAAM,GAAG,0BAA0B,aAAa,IAAI,MAAM,CAAC,cAAc,GAAG,CAAA;YAC5E,UAAU,GAAG,IAAI,CAAC,GAAG,CACnB,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,cAAc,GAAG,aAAa,GAAG,CAAC,CAC1C,CAAA;QACH,CAAC;aAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAErC,MAAM,uBAAuB,GAAG,MAAM,sDAAwB,CAAC,uBAAuB,CACpF,MAAM,CAAC,EAAE,EACT,aAAa,EACb,IAAI,CAAC,oBAAoB,CAC1B,CAAA;YAED,IAAI,uBAAuB,EAAE,CAAC;gBAC5B,SAAS,GAAG,IAAI,CAAA;gBAChB,MAAM,GAAG,gDAAgD,CAAA;gBACzD,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAA;YACtD,CAAC;QACH,CAAC;QAED,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,CAAA;QACrE,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,eAAe,CAClC,MAAW,EACX,WAAwB,EACxB,UAAkB,EAClB,MAAc;QAEd,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,sDAAwB,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YAG3E,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,gBAAgB,CAAC,CAAA;YACnD,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE,CAAA;YAC5C,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YACrE,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAA;YAGvF,MAAM,WAAW,GAAG,MAAM,oCAAkB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;YACxE,MAAM,OAAO,GAA0B;gBACrC,WAAW;gBACX,gBAAgB,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5C,YAAY;gBACZ,cAAc,EAAE,EAAE;gBAClB,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;gBACzF,gBAAgB;aACjB,CAAA;YAED,IAAI,KAAK,GAAU,EAAE,CAAA;YACrB,IAAI,MAAM,GAAG,EAAE,CAAA;YAGf,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAE7D,MAAM,OAAO,GAAG,MAAM,CAAC,gBAAgB,CAAA;gBACvC,MAAM,UAAU,GAAG,UAAU,CAAA;gBAE7B,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;oBACrD,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;wBACf,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAI,MAAiB,CAAC,CAAA;wBAC/D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAC9C,IAAI,EACJ,YAAY,EACZ,OAAO,EACP,gBAAgB,EAChB,OAAO,CACR,CAAA;wBACD,KAAK,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAA;oBAC1B,CAAC;gBACH,CAAC;gBAED,MAAM,GAAG,eAAe,CAAA;YAC1B,CAAC;iBAAM,CAAC;gBAEN,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,CACpC,MAAM,CAAC,SAAS,EAChB,UAAU,EACV,OAAO,EACP,gBAAgB,EAChB,OAAO,CACR,CAAA;gBACD,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;YACzD,CAAC;YAGD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;gBACjD,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAA;gBAC3E,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,CACpC,MAAM,CAAC,iBAAiB,EACxB,UAAU,EACV,OAAO,EACP,gBAAgB,EAChB,OAAO,CACR,CAAA;gBACD,MAAM,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAA;YACjF,CAAC;YAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,IAAI,CAAC,gBAAgB,CAAC;oBACpB,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,KAAK,EAAE,oBAAoB;oBAC3B,OAAO,EAAE,yBAAyB;oBAClC,OAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE;iBACjD,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAGD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;YAGlC,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACnC,KAAK,CAAC,oBAAoB,EAAE;gBAC1B,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI;oBACpC,OAAO,EAAE,eAAe,MAAM,GAAG;iBAClC,CAAC;aACH,CAAC,CACH,CAAA;YAED,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;YACjD,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAA;YAEpE,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;gBAEvB,MAAM,sDAAwB,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE;oBACxD,UAAU,EAAE,cAAc;oBAC1B,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB;iBACnE,CAAC,CAAA;gBAGF,IAAI,CAAC,gBAAgB,CAAC;oBACpB,IAAI,EAAE,aAAa;oBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,KAAK,EAAE,wBAAwB;oBAC/B,OAAO,EAAE,SAAS,cAAc,gBAAgB,MAAM,EAAE;oBACxD,OAAO,EAAE;wBACP,UAAU,EAAE,cAAc;wBAC1B,MAAM;wBACN,SAAS,EAAE,MAAM,CAAC,SAAS;wBAC3B,MAAM;wBACN,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;4BACjC,KAAK,EAAE,CAAC,CAAC,KAAK,IAAI,SAAS;4BAC3B,MAAM,EAAE,CAAC,CAAC,MAAM,IAAI,SAAS;yBAC9B,CAAC,CAAC;qBACJ;iBACF,CAAC,CAAA;YACJ,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAA;YAChE,IAAI,CAAC,gBAAgB,CAAC;gBACpB,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,KAAK,EAAE,kBAAkB;gBACzB,OAAO,EAAE,8BAA8B;gBACvC,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aAC9E,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,mBAAmB,CACtC,SAAiB,EACjB,KAAa,EACb,OAA8B,EAC9B,gBAA0B,EAC1B,OAAY;QAEZ,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,WAAW,CAAC;YACjB,KAAK,gBAAgB;gBACnB,MAAM,OAAO,GAAG,MAAM,qDAAwB,CAAC,2BAA2B,CACxE,OAAO,CAAC,WAAW,EACnB,KAAK,EACL,gBAAgB,CACjB,CAAA;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YAEtC,KAAK,aAAa;gBAChB,MAAM,OAAO,GAAG,MAAM,8CAAqB,CAAC,6BAA6B,CACvE,OAAO,EACP,KAAK,CACN,CAAA;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAErC,KAAK,YAAY;gBAGf,MAAM,OAAO,GAAG,MAAM,qDAAwB,CAAC,2BAA2B,CACxE,OAAO,CAAC,WAAW,EACnB,KAAK,EACL,gBAAgB,CACjB,CAAA;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YAEtC;gBACE,OAAO,EAAE,CAAA;QACb,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,IAAI;QACT,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;QACtB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YAChC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QAC3B,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC;YACpB,IAAI,EAAE,aAAa;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,KAAK,EAAE,oBAAoB;YAC3B,OAAO,EAAE,oCAAoC;YAC7C,OAAO,EAAE,EAAE;SACZ,CAAC,CAAA;IACJ,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,qBAAqB;QACxC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,gBAAgB,CAAC,CAAA;YAC9C,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;YAClC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACrD,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,CAAC,CAAA;QACV,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,YAAY;QACzB,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAA;QAClC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE;YAAE,OAAO,SAAS,CAAA;QAC5C,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,EAAE;YAAE,OAAO,WAAW,CAAA;QAC/C,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,EAAE;YAAE,OAAO,SAAS,CAAA;QAC7C,OAAO,OAAO,CAAA;IAChB,CAAC;IAKO,MAAM,CAAC,uBAAuB,CAAC,SAAiB;QACtD,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,WAAW,CAAC;YACjB,KAAK,gBAAgB;gBACnB,OAAO,WAAW,CAAA;YACpB,KAAK,aAAa;gBAChB,OAAO,gBAAgB,CAAA;YACzB,KAAK,QAAQ;gBACX,OAAO,OAAO,CAAA;YAChB,KAAK,YAAY;gBACf,OAAO,gBAAgB,CAAA;YACzB;gBACE,OAAO,SAAS,CAAA;QACpB,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,gBAAgB,CAAC,YAAmC;QACjE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC5C,IAAI,CAAC;gBACH,QAAQ,CAAC,YAAY,CAAC,CAAA;YACxB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAA;YAC1E,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;;AAhbH,4DAibC;AAhbgB,8CAAqB,GAAoC,EAAE,CAAA;AAC3D,kCAAS,GAAG,KAAK,CAAA;AACjB,sCAAa,GAA0B,IAAI,CAAA;AAC3C,wCAAe,GAAkB,IAAI,CAAA;AACrC,6CAAoB,GAAG,CAAC,CAAA;AACxB,wCAAe,GAAG,CAAC,CAAA;AACnB,sCAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA"}