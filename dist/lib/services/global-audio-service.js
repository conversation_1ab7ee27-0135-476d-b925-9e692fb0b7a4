"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalAudioService = void 0;
exports.useGlobalAudio = useGlobalAudio;
const react_1 = __importDefault(require("react"));
const audio_manager_1 = require("@/lib/audio-manager");
const env_1 = require("@/lib/env");
class GlobalAudioServiceClass {
    constructor() {
        this.audioManager = null;
        this.currentState = {
            isPlaying: false,
            currentTrack: null,
            volume: 70,
            mode: 'idle',
            isInitialized: false,
            audioManager: null
        };
        this.listeners = [];
        this.initializationPromise = null;
        this.transitionInProgress = false;
    }
    static getInstance() {
        if (!GlobalAudioServiceClass.instance) {
            GlobalAudioServiceClass.instance = new GlobalAudioServiceClass();
        }
        return GlobalAudioServiceClass.instance;
    }
    async initialize() {
        if (this.initializationPromise) {
            return this.initializationPromise;
        }
        this.initializationPromise = this._initialize();
        return this.initializationPromise;
    }
    async _initialize() {
        if (this.audioManager && this.currentState.isInitialized) {
            return;
        }
        try {
            console.log('[GlobalAudio] Initializing global audio service...');
            const config = (0, env_1.getMpdConnectionConfig)();
            if (!config || !config.host) {
                throw new Error('Invalid MPD configuration');
            }
            this.audioManager = new audio_manager_1.AudioManager(config);
            await this.audioManager.initialize();
            this.currentState = {
                ...this.currentState,
                isInitialized: true,
                audioManager: this.audioManager
            };
            console.log('[GlobalAudio] Global audio service initialized successfully');
            this.notifyListeners();
        }
        catch (error) {
            console.error('[GlobalAudio] Failed to initialize:', error);
            throw error;
        }
    }
    async transitionToMode(transition) {
        if (this.transitionInProgress) {
            console.log('[GlobalAudio] Transition already in progress, skipping');
            return;
        }
        this.transitionInProgress = true;
        console.log(`[GlobalAudio] Transitioning from ${transition.from} to ${transition.to}`);
        try {
            await this.initialize();
            if (!this.audioManager) {
                throw new Error('Audio manager not initialized');
            }
            const { from, to, fadeOutDuration = 1.5, fadeInDuration = 1.0, pauseInsteadOfStop = true } = transition;
            if (from === 'jukebox' && to === 'quiz') {
                await this.jukeboxToQuizTransition(fadeOutDuration, pauseInsteadOfStop);
            }
            else if (from === 'quiz' && to === 'jukebox') {
                await this.quizToJukeboxTransition(fadeInDuration);
            }
            else if (from === 'jukebox' && to === 'idle') {
                await this.fadeOutAndPause(fadeOutDuration);
            }
            else if (from === 'idle' && to === 'jukebox') {
                await this.resumeFromIdle(fadeInDuration);
            }
            this.currentState.mode = to;
            this.notifyListeners();
        }
        finally {
            this.transitionInProgress = false;
        }
    }
    async jukeboxToQuizTransition(fadeOutDuration, pauseInsteadOfStop) {
        if (!this.audioManager)
            return;
        try {
            const status = await this.audioManager.getAudioStatus();
            if (status.isPlaying) {
                console.log('[GlobalAudio] Fading out jukebox music for quiz transition');
                if (pauseInsteadOfStop) {
                    await this.audioManager.setVolume(10, true);
                    this.currentState.isPlaying = true;
                }
                else {
                    await this.audioManager.fadeOut(fadeOutDuration);
                    this.currentState.isPlaying = false;
                }
            }
            this.currentState.currentTrack = status.currentTrack;
        }
        catch (error) {
            console.error('[GlobalAudio] Error in jukebox to quiz transition:', error);
        }
    }
    async quizToJukeboxTransition(fadeInDuration) {
        if (!this.audioManager)
            return;
        try {
            console.log('[GlobalAudio] Resuming jukebox music from quiz');
            if (this.currentState.currentTrack && !this.currentState.isPlaying) {
                await this.audioManager.resume();
            }
            await this.audioManager.setVolume(this.currentState.volume, false);
            this.currentState.isPlaying = true;
        }
        catch (error) {
            console.error('[GlobalAudio] Error in quiz to jukebox transition:', error);
        }
    }
    async fadeOutAndPause(fadeOutDuration) {
        if (!this.audioManager)
            return;
        try {
            const status = await this.audioManager.getAudioStatus();
            if (status.isPlaying) {
                await this.audioManager.fadeOut(fadeOutDuration);
                this.currentState.isPlaying = false;
            }
        }
        catch (error) {
            console.error('[GlobalAudio] Error fading out:', error);
        }
    }
    async resumeFromIdle(fadeInDuration) {
        if (!this.audioManager)
            return;
        try {
            await this.audioManager.resume();
            await this.audioManager.setVolume(this.currentState.volume, false);
            this.currentState.isPlaying = true;
        }
        catch (error) {
            console.error('[GlobalAudio] Error resuming from idle:', error);
        }
    }
    getState() {
        return { ...this.currentState };
    }
    async setVolume(volume) {
        this.currentState.volume = volume;
        if (this.audioManager) {
            await this.audioManager.setVolume(volume, false);
        }
        this.notifyListeners();
    }
    subscribe(listener) {
        this.listeners.push(listener);
        return () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }
    getAudioManager() {
        return this.audioManager;
    }
    async refreshState() {
        if (!this.audioManager)
            return;
        try {
            const status = await this.audioManager.getAudioStatus();
            this.currentState = {
                ...this.currentState,
                isPlaying: status.isPlaying,
                currentTrack: status.currentTrack,
                volume: status.volume
            };
            this.notifyListeners();
        }
        catch (error) {
            console.error('[GlobalAudio] Error refreshing state:', error);
        }
    }
    async cleanup() {
        if (this.audioManager) {
            await this.audioManager.cleanup();
            this.audioManager = null;
        }
        this.currentState = {
            isPlaying: false,
            currentTrack: null,
            volume: 70,
            mode: 'idle',
            isInitialized: false,
            audioManager: null
        };
        this.listeners = [];
        this.initializationPromise = null;
    }
    notifyListeners() {
        this.listeners.forEach(listener => {
            try {
                listener(this.currentState);
            }
            catch (error) {
                console.error('[GlobalAudio] Error in listener:', error);
            }
        });
    }
}
GlobalAudioServiceClass.instance = null;
exports.GlobalAudioService = GlobalAudioServiceClass.getInstance();
function useGlobalAudio() {
    const [state, setState] = react_1.default.useState(exports.GlobalAudioService.getState());
    react_1.default.useEffect(() => {
        const unsubscribe = exports.GlobalAudioService.subscribe(setState);
        return unsubscribe;
    }, []);
    return {
        ...state,
        transitionToMode: exports.GlobalAudioService.transitionToMode.bind(exports.GlobalAudioService),
        setVolume: exports.GlobalAudioService.setVolume.bind(exports.GlobalAudioService),
        getAudioManager: exports.GlobalAudioService.getAudioManager.bind(exports.GlobalAudioService),
        refreshState: exports.GlobalAudioService.refreshState.bind(exports.GlobalAudioService)
    };
}
//# sourceMappingURL=global-audio-service.js.map