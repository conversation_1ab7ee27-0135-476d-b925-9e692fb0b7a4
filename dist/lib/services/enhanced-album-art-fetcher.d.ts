import { ArtworkManager, ArtworkResult } from '../utils/artwork-manager';
export interface AlbumArtSearchResult {
    url: string;
    source: 'spotify' | 'lastfm' | 'musicbrainz' | 'coverartarchive' | 'itunes' | 'deezer';
    confidence: number;
    metadata?: {
        width?: number;
        height?: number;
        format?: string;
    };
}
export interface AlbumArtSearchOptions {
    artist: string;
    album?: string;
    title?: string;
    year?: number;
    enableFuzzyMatching?: boolean;
    maxRetries?: number;
    timeout?: number;
}
export declare class EnhancedAlbumArtFetcher {
    private apiClient;
    private spotifyToken;
    private lastFmApiKey;
    private musicBrainzUserAgent;
    private useDirectFetch;
    searchAlbumArt(options: AlbumArtSearchOptions): Promise<AlbumArtSearchResult | null>;
    private searchSpotify;
    private searchMusicBrainzCoverArtArchive;
    private searchLastFm;
    private searchItunes;
    private searchDeezer;
    private searchWithVariations;
    private generateSearchVariations;
    private cleanArtistName;
    private cleanAlbumName;
    private buildMusicBrainzQuery;
    private findBestMatch;
    private findBestItunesMatch;
    private calculateMatchScore;
    private calculateItunesMatchScore;
    private fuzzyMatch;
    private levenshteinDistance;
    private calculateConfidence;
    private calculateMusicBrainzConfidence;
    private calculateItunesConfidence;
    private ensureSpotifyToken;
}
export declare function downloadAlbumArtWithRetry(searchResult: AlbumArtSearchResult, artworkManager: ArtworkManager, artist: string, album: string, trackId?: string, maxRetries?: number): Promise<ArtworkResult>;
