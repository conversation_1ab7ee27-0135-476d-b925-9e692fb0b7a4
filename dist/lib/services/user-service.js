"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
class UserService {
    static getInstance() {
        if (!UserService.instance) {
            UserService.instance = new UserService();
        }
        return UserService.instance;
    }
    async syncProfileToDatabase(profile) {
        try {
            const existingUser = await prisma_1.default.user.findFirst({
                where: {
                    OR: [
                        { email: profile.email || `${profile.id}@local.user` },
                        { username: profile.username || profile.name || `user_${profile.id}` }
                    ]
                }
            });
            if (existingUser) {
                const updatedUser = await prisma_1.default.user.update({
                    where: { id: existingUser.id },
                    data: {
                        displayName: profile.displayName || profile.name,
                        avatarUrl: profile.avatar,
                        lastActive: new Date(),
                        preferences: JSON.stringify({
                            soundPreference: profile.soundPreference || 'default',
                            isGuest: profile.isGuest || false,
                            theme: 'system',
                            quiz_difficulty: 3,
                            audio_volume: 80,
                            auto_play: true,
                            show_hints: true,
                            favorite_genres: [],
                            favorite_decades: []
                        })
                    }
                });
                return updatedUser;
            }
            else {
                const newUser = await prisma_1.default.user.create({
                    data: {
                        email: profile.email || `${profile.id}@local.user`,
                        username: profile.username || profile.name || `user_${profile.id}`,
                        displayName: profile.displayName || profile.name,
                        avatarUrl: profile.avatar,
                        role: profile.role || 'user',
                        lastActive: new Date(),
                        preferences: JSON.stringify({
                            soundPreference: profile.soundPreference || 'default',
                            isGuest: profile.isGuest || false,
                            theme: 'system',
                            quiz_difficulty: 3,
                            audio_volume: 80,
                            auto_play: true,
                            show_hints: true,
                            favorite_genres: [],
                            favorite_decades: []
                        })
                    }
                });
                return newUser;
            }
        }
        catch (error) {
            console.error('Failed to sync profile to database:', error);
            return null;
        }
    }
    async getAllUsers() {
        try {
            const users = await prisma_1.default.user.findMany({
                orderBy: { createdAt: 'desc' }
            });
            return users;
        }
        catch (error) {
            console.error('Failed to get users from database:', error);
            return [];
        }
    }
    async updateUserRole(userId, role) {
        try {
            await prisma_1.default.user.update({
                where: { id: userId },
                data: {
                    role,
                    updatedAt: new Date()
                }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to update user role:', error);
            return false;
        }
    }
    async deleteUser(userId) {
        try {
            await prisma_1.default.user.delete({
                where: { id: userId }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to delete user:', error);
            return false;
        }
    }
    async getUserById(userId) {
        try {
            const user = await prisma_1.default.user.findUnique({
                where: { id: userId }
            });
            return user;
        }
        catch (error) {
            console.error('Failed to get user by ID:', error);
            return null;
        }
    }
    convertDatabaseUserToProfile(dbUser) {
        let preferences = {};
        try {
            preferences = JSON.parse(dbUser.preferences);
        }
        catch (error) {
            console.error('Failed to parse user preferences:', error);
        }
        return {
            id: dbUser.id,
            name: dbUser.username,
            displayName: dbUser.displayName || dbUser.username,
            avatar: dbUser.avatarUrl || '👤',
            soundPreference: preferences.soundPreference || 'default',
            isGuest: preferences.isGuest || false,
            role: dbUser.role,
            email: dbUser.email,
            username: dbUser.username
        };
    }
    async migrateLocalStorageUsers() {
        try {
            const profileManagerProfile = this.getProfileManagerProfile();
            if (profileManagerProfile) {
                await this.syncProfileToDatabase(profileManagerProfile);
            }
            const userContextProfile = this.getUserContextProfile();
            if (userContextProfile) {
                await this.syncProfileToDatabase(userContextProfile);
            }
            console.log('Successfully migrated localStorage users to database');
        }
        catch (error) {
            console.error('Failed to migrate localStorage users:', error);
        }
    }
    getProfileManagerProfile() {
        if (typeof window === 'undefined')
            return null;
        try {
            const profileJson = localStorage.getItem('jukebox-profile');
            if (profileJson) {
                return JSON.parse(profileJson);
            }
        }
        catch (error) {
            console.error('Failed to get ProfileManager profile:', error);
        }
        return null;
    }
    getUserContextProfile() {
        if (typeof window === 'undefined')
            return null;
        try {
            const userJson = localStorage.getItem('user');
            if (userJson) {
                const user = JSON.parse(userJson);
                return {
                    id: user.id,
                    name: user.username || user.displayName,
                    displayName: user.displayName,
                    avatar: user.avatar || '👤',
                    role: user.role,
                    email: user.email,
                    username: user.username
                };
            }
        }
        catch (error) {
            console.error('Failed to get UserContext profile:', error);
        }
        return null;
    }
    async getCurrentUnifiedProfile() {
        let profile = this.getProfileManagerProfile();
        if (!profile) {
            profile = this.getUserContextProfile();
        }
        if (profile) {
            const dbUser = await this.syncProfileToDatabase(profile);
            if (dbUser) {
                return this.convertDatabaseUserToProfile(dbUser);
            }
        }
        return profile;
    }
}
exports.UserService = UserService;
//# sourceMappingURL=user-service.js.map