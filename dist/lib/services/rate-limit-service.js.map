{"version": 3, "file": "rate-limit-service.js", "sourceRoot": "", "sources": ["../../../lib/services/rate-limit-service.ts"], "names": [], "mappings": ";;;AAgFA,sCA6CC;AAvHD,MAAa,gBAAgB;IAK3B;QAHQ,kBAAa,GAAkD,IAAI,GAAG,EAAE,CAAA;QACxE,oBAAe,GAA0B,IAAI,CAAA;QAInD,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAC9B,CAAC,EAAE,KAAK,CAAC,CAAA;IACX,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YAC/B,gBAAgB,CAAC,QAAQ,GAAG,IAAI,gBAAgB,EAAE,CAAA;QACpD,CAAC;QACD,OAAO,gBAAgB,CAAC,QAAQ,CAAA;IAClC,CAAC;IAEO,qBAAqB;QAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YACxD,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;gBACtB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,UAAU,CACrB,GAAW,EACX,KAAa,EACb,aAAqB;QAErB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,MAAM,GAAG,aAAa,GAAG,IAAI,CAAA;QACnC,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAA;QAG1B,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAEzC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;YAEhC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAA;YAChD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,KAAK,GAAG,CAAC;gBACpB,KAAK;aACN,CAAA;QACH,CAAC;QAGD,KAAK,CAAC,KAAK,EAAE,CAAA;QAEb,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,KAAK,IAAI,KAAK;YAC7B,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YAC3C,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAA;IACH,CAAC;IAEM,KAAK,CAAC,KAAK,CAAC,GAAW;QAC5B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;IAChC,CAAC;IAEM,OAAO;QACZ,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YACnC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;QAC7B,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;IAC5B,CAAC;CACF;AAvED,4CAuEC;AAGD,SAAgB,aAAa,CAC3B,OAAiB,EACjB,OAIC;IAED,OAAO,KAAK,EAAE,GAAY,EAAE,GAAG,IAAW,EAAE,EAAE;QAC5C,MAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW,EAAE,CAAA;QAClD,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,UAAU,CACzC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAChB,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,CACf,CAAA;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,IAAI,QAAQ,CACjB,IAAI,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,qBAAqB;gBAC5B,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,KAAK,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE;aAC5C,CAAC,EACF;gBACE,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE;oBACP,mBAAmB,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE;oBAC7C,uBAAuB,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;oBACpD,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;oBAC5C,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE;iBACxE;aACF,CACF,CAAA;QACH,CAAC;QAGD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;QAC5C,IAAI,QAAQ,YAAY,QAAQ,EAAE,CAAC;YACjC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAA;YACnE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAA;YAC1E,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAA;QACpE,CAAC;QAED,OAAO,QAAQ,CAAA;IACjB,CAAC,CAAA;AACH,CAAC"}