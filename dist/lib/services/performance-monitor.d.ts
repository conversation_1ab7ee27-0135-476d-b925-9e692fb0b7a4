interface PerformanceMetric {
    name: string;
    value: number;
    timestamp: number;
    tags?: Record<string, string>;
    type: 'timing' | 'counter' | 'gauge';
}
interface PerformanceReport {
    metrics: PerformanceMetric[];
    summary: {
        totalMetrics: number;
        averageResponseTime: number;
        slowestOperations: Array<{
            name: string;
            duration: number;
        }>;
        errorRate: number;
    };
    timeRange: {
        start: number;
        end: number;
    };
}
export declare class PerformanceMonitor {
    private static instance;
    private metrics;
    private readonly maxMetrics;
    private errorCount;
    private totalOperations;
    private constructor();
    static getInstance(): PerformanceMonitor;
    recordTiming(name: string, duration: number, tags?: Record<string, string>): void;
    recordCounter(name: string, value?: number, tags?: Record<string, string>): void;
    recordGauge(name: string, value: number, tags?: Record<string, string>): void;
    recordError(operation: string, error: Error, tags?: Record<string, string>): void;
    measure<T>(name: string, fn: () => T, tags?: Record<string, string>): T;
    measureAsync<T>(name: string, fn: () => Promise<T>, tags?: Record<string, string>): Promise<T>;
    getReport(timeRangeMs?: number): PerformanceReport;
    getMetricsByName(name: string, timeRangeMs?: number): PerformanceMetric[];
    getSystemInfo(): Record<string, any>;
    cleanup(): void;
    exportMetrics(): string;
    private addMetric;
    private setupPerformanceObservers;
}
export declare const performanceMonitor: PerformanceMonitor;
export {};
