import type { AutoQueueConfig as PrismaAutoQueueConfig } from '@prisma/client';
export interface AutoQueueDatabaseConfig extends PrismaAutoQueueConfig {
    algorithmWeights: Record<string, number>;
    genreFilter: string[];
}
export declare class AutoQueueDatabaseService {
    static getActiveConfig(): Promise<AutoQueueDatabaseConfig | null>;
    static getConfig(id: string): Promise<AutoQueueDatabaseConfig | null>;
    static getAllConfigs(): Promise<AutoQueueDatabaseConfig[]>;
    static updateAnalytics(configId: string, updates: {
        songsAdded?: number;
        triggerCount?: number;
        averageQueueTime?: number;
    }): Promise<void>;
    static getAdaptiveInterval(configId: string, queueConsumptionRate: number): Promise<number>;
    static shouldTriggerPredictive(configId: string, currentQueueLength: number, consumptionRate: number): Promise<boolean>;
    static getContentFilters(configId: string): Promise<{
        genres?: string[];
        yearRange?: {
            min?: number;
            max?: number;
        };
        durationRange?: {
            min?: number;
            max?: number;
        };
        excludeExplicit: boolean;
    }>;
    private static createDefaultConfig;
    private static parseConfig;
}
