{"version": 3, "file": "auto-queue-database-service.js", "sourceRoot": "", "sources": ["../../../lib/services/auto-queue-database-service.ts"], "names": [], "mappings": ";;;;;;AAKA,mEAA0C;AAQ1C,MAAa,wBAAwB;IAInC,MAAM,CAAC,KAAK,CAAC,eAAe;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,eAAe,CAAC,SAAS,CAAC;gBACpD,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAA;YAEF,IAAI,CAAC,MAAM,EAAE,CAAC;gBAEZ,MAAM,aAAa,GAAG,MAAM,gBAAM,CAAC,eAAe,CAAC,SAAS,CAAC;oBAC3D,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;iBAC3B,CAAC,CAAA;gBAEF,IAAI,CAAC,aAAa,EAAE,CAAC;oBAEnB,OAAO,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;gBACzC,CAAC;gBAED,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAA;YACxC,CAAC;YAED,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAA;YAClE,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAU;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,eAAe,CAAC,UAAU,CAAC;gBACrD,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAA;YAEF,IAAI,CAAC,MAAM;gBAAE,OAAO,IAAI,CAAA;YAExB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAA;YAC3D,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,aAAa;QACxB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,gBAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;gBACpD,OAAO,EAAE;oBACP,EAAE,QAAQ,EAAE,MAAM,EAAE;oBACpB,EAAE,SAAS,EAAE,MAAM,EAAE;oBACrB,EAAE,IAAI,EAAE,KAAK,EAAE;iBAChB;aACF,CAAC,CAAA;YAEF,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAA;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAA;YAChE,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe,CAC1B,QAAgB,EAChB,OAIC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,eAAe,CAAC,UAAU,CAAC;gBACrD,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;aACxB,CAAC,CAAA;YAEF,IAAI,CAAC,MAAM;gBAAE,OAAM;YAEnB,MAAM,gBAAM,CAAC,eAAe,CAAC,MAAM,CAAC;gBAClC,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;gBACvB,IAAI,EAAE;oBACJ,eAAe,EAAE,MAAM,CAAC,eAAe,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC;oBACnE,aAAa,EAAE,MAAM,CAAC,aAAa,GAAG,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;oBACjE,aAAa,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa;oBACvE,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,KAAK,SAAS;wBACtD,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,aAAa,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC;wBAC1G,CAAC,CAAC,MAAM,CAAC,gBAAgB;iBAC5B;aACF,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAA;QACnE,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAgB,EAAE,oBAA4B;QAC7E,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YAC7C,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBAC1C,OAAO,MAAM,EAAE,aAAa,IAAI,EAAE,CAAA;YACpC,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,CAAA;YACzC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,YAAY,GAAG,GAAG,CAAC,CAAA;YACpD,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,GAAG,CAAC,CAAC,CAAA;YAInD,IAAI,oBAAoB,GAAG,GAAG,EAAE,CAAC;gBAC/B,OAAO,WAAW,CAAA;YACpB,CAAC;iBAAM,IAAI,oBAAoB,GAAG,GAAG,EAAE,CAAC;gBACtC,OAAO,WAAW,CAAA;YACpB,CAAC;iBAAM,CAAC;gBAEN,MAAM,MAAM,GAAG,CAAC,GAAG,oBAAoB,CAAA;gBACvC,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,MAAM,CAAC,CAAA;YACxE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAA;YAC5E,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAClC,QAAgB,EAChB,kBAA0B,EAC1B,eAAuB;QAEvB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YAC7C,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBAC1C,OAAO,KAAK,CAAA;YACd,CAAC;YAGD,MAAM,WAAW,GAAG,kBAAkB,GAAG,eAAe,CAAA;YAGxD,OAAO,WAAW,IAAI,CAAC,CAAA;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAA;YACzE,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAM7C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YAC7C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,CAAA;YACnC,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBACtE,SAAS,EAAE,CAAC,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBACxD,GAAG,EAAE,MAAM,CAAC,YAAY,IAAI,SAAS;oBACrC,GAAG,EAAE,MAAM,CAAC,YAAY,IAAI,SAAS;iBACtC,CAAC,CAAC,CAAC,SAAS;gBACb,aAAa,EAAE,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;oBAC1D,GAAG,EAAE,MAAM,CAAC,WAAW,IAAI,SAAS;oBACpC,GAAG,EAAE,MAAM,CAAC,WAAW,IAAI,SAAS;iBACrC,CAAC,CAAC,CAAC,SAAS;gBACb,eAAe,EAAE,MAAM,CAAC,eAAe;aACxC,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAA;YACpE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,CAAA;QACnC,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,mBAAmB;QACtC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,eAAe,CAAC,MAAM,CAAC;gBACjD,IAAI,EAAE;oBACJ,IAAI,EAAE,uBAAuB;oBAC7B,WAAW,EAAE,6CAA6C;oBAC1D,QAAQ,EAAE,IAAI;oBACd,SAAS,EAAE,IAAI;oBACf,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,EAAE;oBACjB,iBAAiB,EAAE,CAAC;oBACpB,kBAAkB,EAAE,KAAK;oBACzB,kBAAkB,EAAE,KAAK;oBACzB,SAAS,EAAE,QAAQ;oBACnB,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC;wBAC/B,SAAS,EAAE,GAAG;wBACd,WAAW,EAAE,GAAG;wBAChB,UAAU,EAAE,GAAG;qBAChB,CAAC;oBACF,eAAe,EAAE,IAAI;oBACrB,iBAAiB,EAAE,YAAY;oBAC/B,WAAW,EAAE,IAAI;oBACjB,eAAe,EAAE,KAAK;oBACtB,cAAc,EAAE,QAAQ;oBACxB,gBAAgB,EAAE,IAAI;oBACtB,mBAAmB,EAAE,IAAI;oBACzB,oBAAoB,EAAE,GAAG;oBACzB,kBAAkB,EAAE,EAAE;oBACtB,cAAc,EAAE,EAAE;oBAClB,cAAc,EAAE,IAAI;oBACpB,UAAU,EAAE,EAAE;oBACd,kBAAkB,EAAE,CAAC;iBACtB;aACF,CAAC,CAAA;YAEF,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAA;YACtE,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,WAAW,CAAC,MAA6B;QACtD,OAAO;YACL,GAAG,MAAM;YACT,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC;YACrD,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;SAC5C,CAAA;IACH,CAAC;CACF;AAxPD,4DAwPC"}