{"version": 3, "file": "session-service.js", "sourceRoot": "", "sources": ["../../../lib/services/session-service.ts"], "names": [], "mappings": ";;;;;;AAKA,mEAA0C;AAkB1C,MAAa,kBAAkB;IAO7B,MAAM,CAAC,KAAK,CAAC,YAAY,CACvB,MAAc,EACd,cAA2B,SAAS,EACpC,SAAkB,EAClB,SAAkB,EAClB,WAA4B,EAAE;QAE9B,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;YAGjD,MAAM,OAAO,GAAG,MAAM,gBAAM,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC9C,IAAI,EAAE;oBACJ,MAAM;oBACN,WAAW;oBACX,SAAS;oBACT,SAAS;oBACT,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;oBAClC,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,UAAU,EAAE,IAAI,IAAI,EAAE;iBACvB;aACF,CAAC,CAAA;YAGF,MAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACvB,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACrB,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE;aACjC,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,4BAA4B,WAAW,qBAAqB,MAAM,EAAE,CAAC,CAAA;YACjF,OAAO,OAAO,CAAA;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAA;YACjE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,cAAc,CACzB,SAAiB,EACjB,WAAqC,EAAE;QAEvC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,gBAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gBAClD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;aACzB,CAAC,CAAA;YAEF,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAA;YAClD,CAAC;YAGD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAA;YAC7D,MAAM,eAAe,GAAG,EAAE,GAAG,gBAAgB,EAAE,GAAG,QAAQ,EAAE,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAA;YAErG,MAAM,gBAAM,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC9B,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;gBACxB,IAAI,EAAE;oBACJ,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;iBAC1C;aACF,CAAC,CAAA;YAGF,MAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACvB,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;gBAC7B,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE;aACjC,CAAC,CAAA;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAA;YACnE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAA;QACtD,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,SAAiB;QACvC,IAAI,CAAC;YACH,MAAM,gBAAM,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC9B,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;gBACxB,IAAI,EAAE;oBACJ,QAAQ,EAAE,KAAK;oBACf,cAAc,EAAE,IAAI,IAAI,EAAE;iBAC3B;aACF,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,kCAAkC,SAAS,EAAE,CAAC,CAAA;QAE5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAA;YAC/D,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;QAC1C,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAC5B,MAAc,EACd,WAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,WAAW,GAAQ;gBACvB,MAAM;gBACN,QAAQ,EAAE,IAAI;aACf,CAAA;YAED,IAAI,WAAW,EAAE,CAAC;gBAChB,WAAW,CAAC,WAAW,GAAG,WAAW,CAAA;YACvC,CAAC;YAED,MAAM,gBAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gBAClC,KAAK,EAAE,WAAW;gBAClB,IAAI,EAAE;oBACJ,QAAQ,EAAE,KAAK;oBACf,cAAc,EAAE,IAAI,IAAI,EAAE;iBAC3B;aACF,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,mDAAmD,MAAM,EAAE,CAAC,CAAA;QAE1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAA;YACvE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAA;QAClD,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,cAAc,CACzB,cAA2B,SAAS,EACpC,2BAA2B,GAAG,KAAK;QAEnC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAA;YAC7B,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAA;YAG7E,MAAM,YAAY,GAAQ;gBACxB,WAAW;gBACX,UAAU,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE;aAChC,CAAA;YAED,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBACjC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAA;YAC9B,CAAC;YAGD,MAAM,KAAK,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACvC,KAAK,EAAE;oBACL,YAAY,EAAE;wBACZ,IAAI,EAAE,YAAY;qBACnB;iBACF;gBACD,OAAO,EAAE;oBACP,YAAY,EAAE;wBACZ,KAAK,EAAE,YAAY;wBACnB,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;wBAC/B,IAAI,EAAE,CAAC;qBACR;iBACF;aACF,CAAC,CAAA;YAGF,MAAM,WAAW,GAAiB,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACjD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;gBAC3C,MAAM,kBAAkB,GAAG,cAAc;oBACvC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;oBAC9E,CAAC,CAAC,GAAG,CAAA;gBAEP,OAAO;oBACL,GAAG,IAAI;oBACP,cAAc;oBACd,kBAAkB;iBACnB,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,kBAAkB,CAAC,CAAA;QAEhF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAA;YACpE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC/C,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,WAAyB;QAMpD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAA;YAC7B,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAA;YAE7E,MAAM,WAAW,GAAQ,EAAE,CAAA;YAC3B,IAAI,WAAW,EAAE,CAAC;gBAChB,WAAW,CAAC,WAAW,GAAG,WAAW,CAAA;YACvC,CAAC;YAED,MAAM,CAAC,WAAW,EAAE,aAAa,EAAE,cAAc,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAErE,gBAAM,CAAC,WAAW,CAAC,KAAK,CAAC;oBACvB,KAAK,EAAE;wBACL,GAAG,WAAW;wBACd,QAAQ,EAAE,IAAI;wBACd,UAAU,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE;qBAChC;iBACF,CAAC;gBAGF,gBAAM,CAAC,WAAW,CAAC,KAAK,CAAC;oBACvB,KAAK,EAAE,WAAW;iBACnB,CAAC;gBAGF,gBAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;oBAC1B,KAAK,EAAE;wBACL,GAAG,WAAW;wBACd,cAAc,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE;wBAC7B,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE;qBACjE;oBACD,MAAM,EAAE;wBACN,WAAW,EAAE,IAAI;wBACjB,cAAc,EAAE,IAAI;qBACrB;iBACF,CAAC;aACH,CAAC,CAAA;YAGF,MAAM,SAAS,GAAG,cAAc;iBAC7B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC;iBAC7B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAe,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAA;YAElE,MAAM,sBAAsB,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC;gBACjD,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC;gBACvE,CAAC,CAAC,CAAC,CAAA;YAGL,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;YAC/B,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAA;YAElD,MAAM,cAAc,GAAG,MAAM,gBAAM,CAAC,WAAW,CAAC,KAAK,CAAC;gBACpD,KAAK,EAAE;oBACL,GAAG,WAAW;oBACd,UAAU,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE;iBAClC;aACF,CAAC,CAAA;YAEF,OAAO;gBACL,WAAW;gBACX,aAAa;gBACb,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC;gBAC1D,cAAc;aACf,CAAA;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAA;YACrE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA;QACrD,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,IAAI,EAAE,CAAA;YAChC,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAA;YAErD,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gBACjD,KAAK,EAAE;oBACL,QAAQ,EAAE,KAAK;oBACf,cAAc,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;iBACtC;aACF,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,+BAA+B,MAAM,CAAC,KAAK,eAAe,CAAC,CAAA;YACvE,OAAO,MAAM,CAAC,KAAK,CAAA;QAErB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAA;YACpE,OAAO,CAAC,CAAA;QACV,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,mBAAmB;QAC9B,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,IAAI,EAAE,CAAA;YAChC,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAA;YAEnF,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gBACjD,KAAK,EAAE;oBACL,QAAQ,EAAE,IAAI;oBACd,UAAU,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;iBAClC;gBACD,IAAI,EAAE;oBACJ,QAAQ,EAAE,KAAK;oBACf,cAAc,EAAE,IAAI,IAAI,EAAE;iBAC3B;aACF,CAAC,CAAA;YAEF,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,KAAK,wBAAwB,CAAC,CAAA;YAC9E,CAAC;YAED,OAAO,MAAM,CAAC,KAAK,CAAA;QAErB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAA;YACzE,OAAO,CAAC,CAAA;QACV,CAAC;IACH,CAAC;;AA7UH,gDA8UC;AA7UyB,0CAAuB,GAAG,EAAE,CAAA;AAC5B,6CAA0B,GAAG,CAAC,CAAA"}