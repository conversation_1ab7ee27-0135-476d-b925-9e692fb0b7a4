"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedAutoQueueService = void 0;
const auto_queue_database_service_1 = require("./auto-queue-database-service");
const favorites_analysis_service_1 = require("./favorites-analysis-service");
const recommendation_service_1 = require("./recommendation-service");
const session_service_1 = require("./session-service");
class EnhancedAutoQueueService {
    static addNotificationListener(listener) {
        this.notificationListeners.push(listener);
    }
    static removeNotificationListener(listener) {
        this.notificationListeners = this.notificationListeners.filter(l => l !== listener);
    }
    static async start(sessionType = 'jukebox') {
        try {
            console.log('[EnhancedAutoQueue] Starting...');
            const config = await auto_queue_database_service_1.AutoQueueDatabaseService.getActiveConfig();
            if (!config) {
                this.emitNotification({
                    type: 'error',
                    timestamp: new Date(),
                    title: 'Auto Queue Error',
                    message: 'No active configuration found',
                    details: { reason: 'Please configure auto queue settings in the manager' }
                });
                return;
            }
            this.currentConfigId = config.id;
            this.isRunning = true;
            await this.startAdaptiveMonitoring(config.id, sessionType);
            this.emitNotification({
                type: 'songs-added',
                timestamp: new Date(),
                title: 'Auto Queue Started',
                message: `Using "${config.name}" configuration`,
                details: {
                    algorithm: config.algorithm,
                    source: this.getAlgorithmDisplayName(config.algorithm)
                }
            });
        }
        catch (error) {
            console.error('[EnhancedAutoQueue] Failed to start:', error);
            this.emitNotification({
                type: 'error',
                timestamp: new Date(),
                title: 'Auto Queue Error',
                message: 'Failed to start auto queue system',
                details: { reason: error instanceof Error ? error.message : 'Unknown error' }
            });
        }
    }
    static async startAdaptiveMonitoring(configId, sessionType) {
        const performCheck = async () => {
            if (!this.isRunning || this.currentConfigId !== configId)
                return;
            try {
                const config = await auto_queue_database_service_1.AutoQueueDatabaseService.getActiveConfig();
                if (!config || config.id !== configId) {
                    this.stop();
                    return;
                }
                const currentQueueLength = await this.getCurrentQueueLength();
                const timeDelta = (Date.now() - this.lastCheckTime) / 1000 / 60;
                const queueDelta = this.lastQueueLength - currentQueueLength;
                if (timeDelta > 0 && queueDelta > 0) {
                    this.queueConsumptionRate = queueDelta / timeDelta;
                }
                this.lastQueueLength = currentQueueLength;
                this.lastCheckTime = Date.now();
                await this.checkAndAddSongs(config, sessionType);
                const nextInterval = await auto_queue_database_service_1.AutoQueueDatabaseService.getAdaptiveInterval(configId, this.queueConsumptionRate);
                if (this.checkInterval)
                    clearTimeout(this.checkInterval);
                this.checkInterval = setTimeout(performCheck, nextInterval * 1000);
            }
            catch (error) {
                console.error('[EnhancedAutoQueue] Check failed:', error);
            }
        };
        await performCheck();
    }
    static async checkAndAddSongs(config, sessionType) {
        const currentLength = await this.getCurrentQueueLength();
        const activeUsers = await session_service_1.UserSessionService.getActiveUsers(sessionType);
        if (activeUsers.length < config.minConnectedUsers) {
            return;
        }
        let shouldAdd = false;
        let reason = '';
        let songsToAdd = 0;
        if (currentLength === 0) {
            shouldAdd = true;
            reason = 'Queue is empty';
            songsToAdd = config.maxSongsPerTrigger;
        }
        else if (currentLength <= config.queueThreshold) {
            shouldAdd = true;
            reason = `Queue below threshold (${currentLength}/${config.queueThreshold})`;
            songsToAdd = Math.min(config.maxSongsPerTrigger, config.queueThreshold - currentLength + 5);
        }
        else if (config.predictiveQueueing) {
            const shouldTriggerPredictive = await auto_queue_database_service_1.AutoQueueDatabaseService.shouldTriggerPredictive(config.id, currentLength, this.queueConsumptionRate);
            if (shouldTriggerPredictive) {
                shouldAdd = true;
                reason = 'Predictive queueing (queue will be empty soon)';
                songsToAdd = Math.min(config.maxSongsPerTrigger, 10);
            }
        }
        if (shouldAdd && songsToAdd > 0) {
            await this.addSongsToQueue(config, sessionType, songsToAdd, reason);
        }
    }
    static async addSongsToQueue(config, sessionType, songsToAdd, reason) {
        try {
            const filters = await auto_queue_database_service_1.AutoQueueDatabaseService.getContentFilters(config.id);
            const queueResponse = await fetch('/api/mpd/queue');
            const queueData = await queueResponse.json();
            const currentQueue = queueData.success ? (queueData.queue || []) : [];
            const excludeFilePaths = currentQueue.map((track) => track.file || track.filePath);
            const activeUsers = await session_service_1.UserSessionService.getActiveUsers(sessionType);
            const context = {
                sessionType,
                connectedUserIds: activeUsers.map(u => u.id),
                currentQueue,
                recentlyPlayed: [],
                timeOfDay: this.getTimeOfDay(),
                dayOfWeek: new Date().getDay() === 0 || new Date().getDay() === 6 ? 'weekend' : 'weekday',
                excludeFilePaths
            };
            let songs = [];
            let source = '';
            if (config.algorithm === 'hybrid' && config.algorithmWeights) {
                const weights = config.algorithmWeights;
                const totalSongs = songsToAdd;
                for (const [algo, weight] of Object.entries(weights)) {
                    if (weight > 0) {
                        const songsForAlgo = Math.ceil(totalSongs * weight);
                        const algoSongs = await this.getSongsByAlgorithm(algo, songsForAlgo, context, excludeFilePaths, filters);
                        songs.push(...algoSongs);
                    }
                }
                source = 'Mixed Sources';
            }
            else {
                songs = await this.getSongsByAlgorithm(config.algorithm, songsToAdd, context, excludeFilePaths, filters);
                source = this.getAlgorithmDisplayName(config.algorithm);
            }
            if (songs.length === 0 && config.fallbackEnabled) {
                console.log('[EnhancedAutoQueue] Primary algorithm failed, using fallback');
                songs = await this.getSongsByAlgorithm(config.fallbackAlgorithm, songsToAdd, context, excludeFilePaths, filters);
                source = `${this.getAlgorithmDisplayName(config.fallbackAlgorithm)} (Fallback)`;
            }
            if (songs.length === 0) {
                this.emitNotification({
                    type: 'warning',
                    timestamp: new Date(),
                    title: 'Auto Queue Warning',
                    message: 'No suitable songs found',
                    details: { reason, algorithm: config.algorithm }
                });
                return;
            }
            songs = songs.slice(0, songsToAdd);
            const addPromises = songs.map(song => fetch('/api/mpd/queue/add', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    filePath: song.filePath || song.file,
                    addedBy: `Auto Queue (${source})`
                })
            }));
            const addResults = await Promise.all(addPromises);
            const successfulAdds = addResults.filter(result => result.ok).length;
            if (successfulAdds > 0) {
                await auto_queue_database_service_1.AutoQueueDatabaseService.updateAnalytics(config.id, {
                    songsAdded: successfulAdds,
                    triggerCount: 1,
                    averageQueueTime: this.lastQueueLength / this.queueConsumptionRate
                });
                this.emitNotification({
                    type: 'songs-added',
                    timestamp: new Date(),
                    title: 'Auto Queue Added Songs',
                    message: `Added ${successfulAdds} tracks from ${source}`,
                    details: {
                        songsAdded: successfulAdds,
                        source,
                        algorithm: config.algorithm,
                        reason,
                        songs: songs.slice(0, 5).map(s => ({
                            title: s.title || 'Unknown',
                            artist: s.artist || 'Unknown'
                        }))
                    }
                });
            }
        }
        catch (error) {
            console.error('[EnhancedAutoQueue] Failed to add songs:', error);
            this.emitNotification({
                type: 'error',
                timestamp: new Date(),
                title: 'Auto Queue Error',
                message: 'Failed to add songs to queue',
                details: { reason: error instanceof Error ? error.message : 'Unknown error' }
            });
        }
    }
    static async getSongsByAlgorithm(algorithm, count, context, excludeFilePaths, filters) {
        switch (algorithm) {
            case 'favorites':
            case 'favorites-only':
                const favRecs = await favorites_analysis_service_1.FavoritesAnalysisService.getAutoQueueRecommendations(context.sessionType, count, excludeFilePaths);
                return favRecs.map(rec => rec.track);
            case 'intelligent':
                const intRecs = await recommendation_service_1.RecommendationService.getIntelligentRecommendations(context, count);
                return intRecs.map(rec => rec.song);
            case 'popularity':
                const popRecs = await favorites_analysis_service_1.FavoritesAnalysisService.getAutoQueueRecommendations(context.sessionType, count, excludeFilePaths);
                return popRecs.map(rec => rec.track);
            default:
                return [];
        }
    }
    static stop() {
        this.isRunning = false;
        if (this.checkInterval) {
            clearTimeout(this.checkInterval);
            this.checkInterval = null;
        }
        this.emitNotification({
            type: 'songs-added',
            timestamp: new Date(),
            title: 'Auto Queue Stopped',
            message: 'Auto queue system has been stopped',
            details: {}
        });
    }
    static async getCurrentQueueLength() {
        try {
            const response = await fetch('/api/mpd/queue');
            const data = await response.json();
            return data.success ? (data.queue?.length || 0) : 0;
        }
        catch {
            return 0;
        }
    }
    static getTimeOfDay() {
        const hour = new Date().getHours();
        if (hour >= 6 && hour < 12)
            return 'morning';
        if (hour >= 12 && hour < 17)
            return 'afternoon';
        if (hour >= 17 && hour < 22)
            return 'evening';
        return 'night';
    }
    static getAlgorithmDisplayName(algorithm) {
        switch (algorithm) {
            case 'favorites':
            case 'favorites-only':
                return 'Favorites';
            case 'intelligent':
                return 'AI Suggestions';
            case 'hybrid':
                return 'Mixed';
            case 'popularity':
                return 'Popular Tracks';
            default:
                return algorithm;
        }
    }
    static emitNotification(notification) {
        this.notificationListeners.forEach(listener => {
            try {
                listener(notification);
            }
            catch (error) {
                console.error('[EnhancedAutoQueue] Notification listener error:', error);
            }
        });
    }
}
exports.EnhancedAutoQueueService = EnhancedAutoQueueService;
EnhancedAutoQueueService.notificationListeners = [];
EnhancedAutoQueueService.isRunning = false;
EnhancedAutoQueueService.checkInterval = null;
EnhancedAutoQueueService.currentConfigId = null;
EnhancedAutoQueueService.queueConsumptionRate = 0;
EnhancedAutoQueueService.lastQueueLength = 0;
EnhancedAutoQueueService.lastCheckTime = Date.now();
//# sourceMappingURL=auto-queue-enhanced-service.js.map