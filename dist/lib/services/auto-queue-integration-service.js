"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoQueueIntegrationService = void 0;
const favorites_analysis_service_1 = require("./favorites-analysis-service");
const recommendation_service_1 = require("./recommendation-service");
const auto_queue_database_service_1 = require("./auto-queue-database-service");
const session_service_1 = require("./session-service");
class AutoQueueIntegrationService {
    static async start(userId, sessionType = 'jukebox') {
        try {
            console.log(`[AutoQueueIntegration] Starting for user ${userId}`);
            this.currentUserId = userId;
            this.isRunning = true;
            const config = await auto_queue_database_service_1.AutoQueueDatabaseService.getActiveConfig();
            if (!config) {
                console.log('[AutoQueueIntegration] No active configuration found');
                return;
            }
            this.lastStatus = {
                isActive: true,
                isRunning: true,
                songsAddedCount: 0,
                configurationId: config.id,
                configurationName: config.name
            };
            this.startMonitoringLoop(config, sessionType);
            this.emitEvent({
                type: 'config-changed',
                timestamp: new Date(),
                data: { config, status: 'started' },
                userId,
                configId: config.id
            });
            console.log(`[AutoQueueIntegration] Started with configuration: ${config.name}`);
        }
        catch (error) {
            console.error('[AutoQueueIntegration] Failed to start:', error);
            this.emitEvent({
                type: 'error',
                timestamp: new Date(),
                data: { error: error instanceof Error ? error.message : 'Unknown error' },
                userId
            });
        }
    }
    static stop() {
        console.log('[AutoQueueIntegration] Stopping');
        this.isRunning = false;
        this.currentUserId = null;
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
        if (this.lastStatus) {
            this.lastStatus.isRunning = false;
        }
        this.emitEvent({
            type: 'config-changed',
            timestamp: new Date(),
            data: { status: 'stopped' }
        });
    }
    static getStatus() {
        return this.lastStatus;
    }
    static async trigger(userId, sessionType = 'jukebox', reason = 'manual') {
        try {
            console.log(`[AutoQueueIntegration] Manual trigger by ${userId}, reason: ${reason}`);
            const config = await AutoQueueConfigService.getActiveConfiguration(userId);
            const result = await this.analyzeAndAct(config, sessionType, reason);
            if (this.lastStatus) {
                this.lastStatus.lastTriggerTime = new Date();
                this.lastStatus.triggerReason = reason;
            }
            return result;
        }
        catch (error) {
            console.error('[AutoQueueIntegration] Manual trigger failed:', error);
            return {
                success: false,
                songsAdded: 0,
                message: error instanceof Error ? error.message : 'Trigger failed'
            };
        }
    }
    static addEventListener(listener) {
        this.eventListeners.push(listener);
    }
    static removeEventListener(listener) {
        const index = this.eventListeners.indexOf(listener);
        if (index > -1) {
            this.eventListeners.splice(index, 1);
        }
    }
    static startMonitoringLoop(config, sessionType) {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
        }
        const checkIntervalMs = 30 * 1000;
        this.checkInterval = setInterval(async () => {
            if (!this.isRunning || !this.currentUserId) {
                return;
            }
            try {
                await this.performPeriodicCheck(config, sessionType);
            }
            catch (error) {
                console.error('[AutoQueueIntegration] Periodic check failed:', error);
                this.emitEvent({
                    type: 'error',
                    timestamp: new Date(),
                    data: { error: error instanceof Error ? error.message : 'Periodic check failed' },
                    userId: this.currentUserId
                });
            }
        }, checkIntervalMs);
        if (this.lastStatus) {
            this.lastStatus.nextCheckTime = new Date(Date.now() + checkIntervalMs);
        }
    }
    static async performPeriodicCheck(config, sessionType) {
        const analysis = await this.analyzeCurrentQueue(config, sessionType);
        this.emitEvent({
            type: 'queue-analyzed',
            timestamp: new Date(),
            data: { analysis },
            userId: this.currentUserId,
            configId: config.id
        });
        if (analysis.needsRefill) {
            console.log(`[AutoQueueIntegration] Queue needs refill: ${analysis.triggerCondition}`);
            const result = await this.analyzeAndAct(config, sessionType, analysis.triggerCondition);
            if (result.success && result.songsAdded > 0) {
                console.log(`[AutoQueueIntegration] Successfully added ${result.songsAdded} songs`);
            }
        }
        if (this.lastStatus) {
            this.lastStatus.nextCheckTime = new Date(Date.now() + 30000);
        }
    }
    static async analyzeCurrentQueue(config, sessionType) {
        try {
            const queueResponse = await fetch('/api/mpd/queue');
            const queueData = await queueResponse.json();
            const currentLength = queueData.success ? (queueData.queue?.length || 0) : 0;
            const isEmpty = currentLength === 0;
            const belowThreshold = currentLength <= config.triggerSettings.queueEmptyThreshold;
            const activeUsers = await session_service_1.UserSessionService.getActiveUsers(sessionType);
            const hasEnoughUsers = activeUsers.length >= config.triggerSettings.minimumConnectedUsers;
            let needsRefill = false;
            let triggerCondition = '';
            let recommendedSongsToAdd = 0;
            if (isEmpty) {
                needsRefill = hasEnoughUsers;
                triggerCondition = 'Queue is empty';
                recommendedSongsToAdd = config.recommendationSettings.maxSongsToAdd;
            }
            else if (belowThreshold && hasEnoughUsers) {
                needsRefill = true;
                triggerCondition = `Queue below threshold (${currentLength} <= ${config.triggerSettings.queueEmptyThreshold})`;
                recommendedSongsToAdd = Math.min(config.recommendationSettings.maxSongsToAdd, config.triggerSettings.queueEmptyThreshold - currentLength + 2);
            }
            else if (!hasEnoughUsers) {
                triggerCondition = `Not enough connected users (${activeUsers.length} < ${config.triggerSettings.minimumConnectedUsers})`;
            }
            return {
                currentLength,
                isEmpty,
                needsRefill,
                recommendedSongsToAdd,
                triggerCondition,
                lastAnalysisTime: new Date()
            };
        }
        catch (error) {
            console.error('[AutoQueueIntegration] Queue analysis failed:', error);
            return {
                currentLength: 0,
                isEmpty: true,
                needsRefill: false,
                recommendedSongsToAdd: 0,
                triggerCondition: 'Analysis failed',
                lastAnalysisTime: new Date()
            };
        }
    }
    static async analyzeAndAct(config, sessionType, reason) {
        try {
            const activeUsers = await session_service_1.UserSessionService.getActiveUsers(sessionType);
            const connectedUserIds = activeUsers.map(user => user.id);
            if (connectedUserIds.length < config.triggerSettings.minimumConnectedUsers) {
                return {
                    success: false,
                    songsAdded: 0,
                    message: `Not enough connected users (${connectedUserIds.length} < ${config.triggerSettings.minimumConnectedUsers})`
                };
            }
            const queueResponse = await fetch('/api/mpd/queue');
            const queueData = await queueResponse.json();
            const currentQueue = queueData.success ? (queueData.queue || []) : [];
            const excludeFilePaths = currentQueue.map((track) => track.file || track.filePath);
            const now = new Date();
            const hour = now.getHours();
            const timeOfDay = hour >= 6 && hour < 12 ? 'morning' :
                hour >= 12 && hour < 17 ? 'afternoon' :
                    hour >= 17 && hour < 22 ? 'evening' : 'night';
            const context = {
                sessionType,
                connectedUserIds,
                currentQueue,
                recentlyPlayed: [],
                timeOfDay,
                dayOfWeek: now.getDay() === 0 || now.getDay() === 6 ? 'weekend' : 'weekday',
                excludeFilePaths
            };
            let songs = [];
            switch (config.recommendationSettings.algorithm) {
                case 'favorites-only':
                    const favoritesRecs = await favorites_analysis_service_1.FavoritesAnalysisService.getAutoQueueRecommendations(sessionType, config.recommendationSettings.maxSongsToAdd, excludeFilePaths);
                    songs = favoritesRecs.map(rec => rec.track);
                    break;
                case 'intelligent':
                case 'hybrid':
                    const intelligentRecs = await recommendation_service_1.RecommendationService.getIntelligentRecommendations(context, config.recommendationSettings.maxSongsToAdd);
                    songs = intelligentRecs.map(rec => rec.song);
                    break;
                case 'popularity':
                    const popularRecs = await favorites_analysis_service_1.FavoritesAnalysisService.getAutoQueueRecommendations(sessionType, config.recommendationSettings.maxSongsToAdd, excludeFilePaths);
                    songs = popularRecs.map(rec => rec.track);
                    break;
                default:
                    throw new Error(`Unknown algorithm: ${config.recommendationSettings.algorithm}`);
            }
            if (songs.length === 0) {
                return {
                    success: false,
                    songsAdded: 0,
                    message: 'No suitable recommendations found'
                };
            }
            const addPromises = songs.map(song => fetch('/api/mpd/queue/add', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    filePath: song.filePath,
                    addedBy: 'Intelligent Auto-Queue'
                })
            }));
            const addResults = await Promise.all(addPromises);
            const successfulAdds = addResults.filter(result => result.ok).length;
            if (successfulAdds > 0) {
                if (this.lastStatus) {
                    this.lastStatus.songsAddedCount += successfulAdds;
                    this.lastStatus.lastAddedSongs = new Date();
                }
                await AutoQueueConfigService.recordConfigurationUsage(config.id);
                this.emitEvent({
                    type: 'songs-added',
                    timestamp: new Date(),
                    data: {
                        songs: songs.slice(0, successfulAdds),
                        algorithm: config.recommendationSettings.algorithm,
                        reason,
                        totalRequested: songs.length,
                        successfulAdds
                    },
                    userId: this.currentUserId,
                    configId: config.id
                });
                return {
                    success: true,
                    songsAdded: successfulAdds,
                    message: `Added ${successfulAdds} songs using ${config.recommendationSettings.algorithm} algorithm`
                };
            }
            else {
                return {
                    success: false,
                    songsAdded: 0,
                    message: 'Failed to add songs to queue'
                };
            }
        }
        catch (error) {
            console.error('[AutoQueueIntegration] Analyze and act failed:', error);
            return {
                success: false,
                songsAdded: 0,
                message: error instanceof Error ? error.message : 'Action failed'
            };
        }
    }
    static emitEvent(event) {
        this.eventListeners.forEach(listener => {
            try {
                listener(event);
            }
            catch (error) {
                console.error('[AutoQueueIntegration] Event listener error:', error);
            }
        });
    }
    static getAnalytics() {
        return {
            totalSongsAdded: this.lastStatus?.songsAddedCount || 0,
            lastActivity: this.lastStatus?.lastAddedSongs,
            activeConfiguration: this.lastStatus?.configurationName,
            isCurrentlyRunning: this.isRunning
        };
    }
}
exports.AutoQueueIntegrationService = AutoQueueIntegrationService;
AutoQueueIntegrationService.eventListeners = [];
AutoQueueIntegrationService.isRunning = false;
AutoQueueIntegrationService.currentUserId = null;
AutoQueueIntegrationService.lastStatus = null;
AutoQueueIntegrationService.checkInterval = null;
//# sourceMappingURL=auto-queue-integration-service.js.map