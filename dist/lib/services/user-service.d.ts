import type { UserRole } from '@/lib/user-context';
import type { User } from '@prisma/client';
export type DatabaseUser = User;
export interface UserProfile {
    id: string;
    name: string;
    displayName?: string;
    avatar: string;
    soundPreference?: string;
    isGuest?: boolean;
    role?: UserRole;
    email?: string;
    username?: string;
}
export declare class UserService {
    private static instance;
    static getInstance(): UserService;
    syncProfileToDatabase(profile: UserProfile): Promise<DatabaseUser | null>;
    getAllUsers(): Promise<DatabaseUser[]>;
    updateUserRole(userId: string, role: UserRole): Promise<boolean>;
    deleteUser(userId: string): Promise<boolean>;
    getUserById(userId: string): Promise<DatabaseUser | null>;
    convertDatabaseUserToProfile(dbUser: DatabaseUser): UserProfile;
    migrateLocalStorageUsers(): Promise<void>;
    private getProfileManagerProfile;
    private getUserContextProfile;
    getCurrentUnifiedProfile(): Promise<UserProfile | null>;
}
