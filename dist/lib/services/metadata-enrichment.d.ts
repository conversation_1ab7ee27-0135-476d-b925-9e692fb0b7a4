import { MPDTrack } from '../mpd-client';
import { type ReleaseDateSource } from '../utils/release-date-validator';
export interface EnrichedTrackData {
    albumArtUrl: string | null;
    artistImageUrl: string | null;
    triviaFacts: string[];
    chartPosition: number | null;
    chartCountry?: string | null;
    releaseDate?: string | null;
    spotifyId?: string | null;
    musicbrainzId?: string | null;
    releaseDateValidation?: {
        finalYear: number | null;
        confidence: 'high' | 'medium' | 'low';
        sources: ReleaseDateSource[];
        warnings: string[];
    };
    artworkPaths?: {
        albumArt?: {
            thumbnail: string | null;
            medium: string | null;
            large: string | null;
            original: string | null;
        };
        artistImage?: {
            thumbnail: string | null;
            medium: string | null;
            large: string | null;
            original: string | null;
        };
    };
    quizCategories: string[];
    chartData: Record<string, any>;
    thematicTags: string[];
    specialLists: string[];
    culturalContext: Record<string, any>;
    similarArtists: string[];
    detailedMetadata: Record<string, any>;
}
export declare class MetadataEnrichmentService {
    private spotifyToken;
    private lastFmApiKey;
    private musicRootPath;
    private musicBrainzUserAgent;
    private _configCheckDone;
    private apiClient;
    private artworkManager;
    private albumArtFetcher;
    enrich(track: MPDTrack): Promise<EnrichedTrackData | null>;
    private parseFilenameMetadata;
    private cleanText;
    private extractFileMetadata;
    private enrichFromExternalAPIs;
    private categorizeForQuiz;
    private analyzeThematicContent;
    private isSportsRelated;
    private isSoccerRelated;
    private isSoundtrackRelated;
    private enrichChartData;
    private fetchSpotifyData;
    private ensureSpotifyToken;
    private fetchLastFmData;
    private extractMeaningfulSentence;
    private fetchMusicBrainzData;
    private normalizeForProcessing;
    checkForDuplicates(artist: string, title: string): Promise<any[]>;
    private getStoredSpotifyData;
    private getStoredLastFmData;
    private getStoredMusicBrainzData;
    private validateAndEnhanceReleaseDate;
    private processArtwork;
    private _checkConfiguration;
}
