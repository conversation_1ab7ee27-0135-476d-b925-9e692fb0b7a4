{"version": 3, "file": "content-filter-service.js", "sourceRoot": "", "sources": ["../../../lib/services/content-filter-service.ts"], "names": [], "mappings": ";;;AAOA,iDAAqE;AAGrE,MAAa,oBAAoB;IAW/B;QATQ,mBAAc,GAAmB,yBAAe,CAAA;QAChD,mBAAc,GAAuB;YAC3C,OAAO,EAAE,KAAK;YACd,eAAe,EAAE,IAAI;SACtB,CAAA;QAGgB,uBAAkB,GAAG,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAA;IAEjD,CAAC;IAExB,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;YACnC,oBAAoB,CAAC,QAAQ,GAAG,IAAI,oBAAoB,EAAE,CAAA;QAC5D,CAAC;QACD,OAAO,oBAAoB,CAAC,QAAQ,CAAA;IACtC,CAAC;IAKD,YAAY;QACV,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,QAAQ,KAAK,WAAW;YAAE,OAAO,IAAI,CAAC,cAAc,CAAA;QAEhG,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,CAAC,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;YACzH,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBACxC,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;oBACtC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAA;gBACzD,CAAC;qBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;oBAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;oBACjE,IAAI,MAAM,EAAE,CAAC;wBACX,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,CAAA;oBACtC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;QACzD,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAA;IAC5B,CAAC;IAKD,YAAY,CAAC,QAA4B;QACvC,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAA;QAC9B,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE,CAAC;YACrE,YAAY,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAA;QACtE,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,QAAgB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;QACvC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,CAAA;YACpC,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,QAAQ,CAAA;YAC7C,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,SAAS,CAAA;YAC7C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAKD,kBAAkB,CAAC,OAAuB;QACxC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAA;QAC7B,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,OAAO,CAAA;QAC3C,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,SAAS,CAAA;QAC9C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;IACxC,CAAC;IAKD,SAAS,CAAC,EAAU;QAClB,OAAO,wBAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAA;IACxD,CAAC;IAKD,UAAU;QACR,OAAO,wBAAc,CAAA;IACvB,CAAC;IAKD,kBAAkB;QAChB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAA;IACpC,CAAC;IAKD,mBAAmB,CAAC,OAAgB;QAClC,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,OAAO,CAAA;QACrC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;IACxC,CAAC;IAKD,iBAAiB;QACf,OAAO,IAAI,CAAC,cAAc,CAAA;IAC5B,CAAC;IAKD,iBAAiB,CAAC,QAAgB;QAChC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,IAAI,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChG,OAAO,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAA;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC,cAAc,CAAA;IAC5B,CAAC;IAKD,YAAY,CAAC,MAAmB,EAAE,QAAiB;QACjD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;YACjC,OAAO,MAAM,CAAA;QACf,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAA;QAEjF,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAA;IACzE,CAAC;IAKD,mBAAmB,CAAC,KAAgB,EAAE,OAAuB;QAE3D,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAErC,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YAChG,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YAE7F,IAAI,OAAO,GAAG,KAAK,CAAA;YAGnB,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC5C,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACnC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CACzD,CAAA;YACH,CAAC;YAGD,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAEjC,IAAI,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;oBACzF,OAAO,GAAG,IAAI,CAAA;gBAChB,CAAC;gBAGD,IAAI,iBAAiB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;oBAC7E,IAAI,CAAC;wBACH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,cAAwB,CAAC,CAAA;wBAC7D,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;4BAC3E,OAAO,GAAG,IAAI,CAAA;wBAChB,CAAC;oBACH,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;oBAEb,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO;gBAAE,OAAO,KAAK,CAAA;YAC/D,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO;gBAAE,OAAO,KAAK,CAAA;QAChE,CAAC;QAGD,IAAI,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,IAAI;gBAAE,OAAO,KAAK,CAAA;YAC7B,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG;gBAAE,OAAO,KAAK,CAAA;YAC7E,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG;gBAAE,OAAO,KAAK,CAAA;QAC/E,CAAC;QAGD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,IAAI,KAAK,CAAC,aAAa;YAAE,OAAO,KAAK,CAAA;QAC1E,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,IAAI,CAAC,KAAK,CAAC,aAAa;YAAE,OAAO,KAAK,CAAA;QAC9E,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;YAC5D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC;gBAAE,OAAO,KAAK,CAAA;QAC1E,CAAC;QAGD,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,IAAI,KAAK,CAAC,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa;YAAE,OAAO,KAAK,CAAA;QACzG,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,IAAI,KAAK,CAAC,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa;YAAE,OAAO,KAAK,CAAA;QACzG,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,IAAI,KAAK,CAAC,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa;YAAE,OAAO,KAAK,CAAA;QAExG,IAAI,OAAO,CAAC,OAAO,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,WAAW;YAAE,OAAO,KAAK,CAAA;QAGvE,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,OAAO,KAAK,CAAA;QAC7D,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK;YAAE,OAAO,KAAK,CAAA;QAC/D,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK;YAAE,OAAO,KAAK,CAAA;QAG/D,IAAI,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;YACzC,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,CAAA;YACnD,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,CAAA;YACnD,IAAI,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC;gBAC1E,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC;gBAAE,OAAO,KAAK,CAAA;QACxF,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,CAAC,qBAAqB,IAAI,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,KAAK,CAAA;QACvG,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,IAAI,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,KAAK,CAAA;QAGjG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,IAAI,KAAK,CAAC,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC;YAAE,OAAO,KAAK,CAAA;QAChG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC;YAAE,OAAO,KAAK,CAAA;QAGtG,IAAI,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,IAAI,YAAY,GAAa,EAAE,CAAA;YAG/B,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,gBAAgB,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;gBACxD,IAAI,gBAAgB,EAAE,CAAC;oBACrB,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;gBACrC,CAAC;YACH,CAAC;YAGD,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAa,CAAA;oBAE/D,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;wBACvB,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;4BACvC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;wBACxB,CAAC;oBACH,CAAC,CAAC,CAAA;gBACJ,CAAC;gBAAC,MAAM,CAAC;gBAET,CAAC;YACH,CAAC;YAGD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CACzD,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAC9B,WAAW,CAAC,WAAW,EAAE,KAAK,YAAY,CAAC,WAAW,EAAE,CACzD,CACF,CAAA;YAED,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO;gBAAE,OAAO,KAAK,CAAA;YAChE,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO;gBAAE,OAAO,KAAK,CAAA;QACjE,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,WAAmB;QAc3C,OAAO;YACL,WAAW;YACX,cAAc,EAAE,WAAW;YAC3B,mBAAmB,EAAE,GAAG;YACxB,eAAe,EAAE,EAAE;SACpB,CAAA;IACH,CAAC;IAKD,aAAa;QACX,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ,EAAE,IAAI,CAAC,cAAc;YAC7B,OAAO,EAAE,IAAI,CAAC,cAAc;SAC7B,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;IACb,CAAC;IAKD,aAAa,CAAC,UAAkB;QAC9B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YACnC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAA;YACrC,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAA;YACpC,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACtC,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAA;YACjD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;CACF;AA5TD,oDA4TC"}