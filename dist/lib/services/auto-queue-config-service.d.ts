export interface AutoQueueConfig {
    id: string;
    name: string;
    description: string;
    isActive: boolean;
    isDefault: boolean;
    triggerSettings: {
        enabled: boolean;
        queueEmptyThreshold: number;
        noSuggestionsTimeout: number;
        minimumConnectedUsers: number;
        timeBasedTriggers: {
            enabled: boolean;
            schedules: Array<{
                dayOfWeek: number[];
                startTime: string;
                endTime: string;
                active: boolean;
            }>;
        };
    };
    recommendationSettings: {
        algorithm: 'favorites-only' | 'intelligent' | 'hybrid' | 'popularity';
        maxSongsToAdd: number;
        preventRecentRepeats: boolean;
        recentRepeatWindowHours: number;
        algorithmWeights: {
            collaborativeFiltering: number;
            contentBased: number;
            popularityBased: number;
            contextAware: number;
            favoritesAnalysis: number;
        };
        contentFilters: {
            allowedGenres: string[];
            blockedGenres: string[];
            allowedYearRange: {
                min?: number;
                max?: number;
            };
            maxDurationSeconds?: number;
            minDurationSeconds?: number;
            requireAlbumArt: boolean;
        };
        userPreferences: {
            respectUserAutoQueuePrefs: boolean;
            userVetoWeight: number;
            consensusThreshold: number;
        };
    };
    behaviorSettings: {
        diversityMode: 'high' | 'medium' | 'low' | 'adaptive';
        energyLevelConsideration: boolean;
        timeOfDayAdaptation: boolean;
        fallbackBehavior: {
            enabled: boolean;
            mode: 'random' | 'popular' | 'recent-favorites' | 'genre-based';
            fallbackGenres: string[];
        };
        adaptiveLearning: {
            enabled: boolean;
            trackUserSkips: boolean;
            trackVotes: boolean;
            learningRate: number;
        };
    };
    notificationSettings: {
        notifyOnAutoAdd: boolean;
        notifyUsers: boolean;
        showRecommendationReasons: boolean;
        logAnalytics: boolean;
    };
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    lastUsedAt?: Date;
    usageCount: number;
}
export interface AutoQueueConfigSummary {
    id: string;
    name: string;
    description: string;
    isActive: boolean;
    isDefault: boolean;
    algorithm: string;
    maxSongsToAdd: number;
    createdAt: Date;
    lastUsedAt?: Date;
    usageCount: number;
}
export declare class AutoQueueConfigService {
    static getAllConfigurations(): Promise<AutoQueueConfigSummary[]>;
    static getConfiguration(configId: string): Promise<AutoQueueConfig | null>;
    static getActiveConfiguration(userId?: string): Promise<AutoQueueConfig>;
    static saveConfiguration(config: AutoQueueConfig, userId: string): Promise<string>;
    static setActiveConfiguration(configId: string, userId: string): Promise<void>;
    static recordConfigurationUsage(configId: string): Promise<void>;
    private static getDefaultConfigurations;
    private static getDefaultConfiguration;
    static validateConfiguration(config: Partial<AutoQueueConfig>): {
        isValid: boolean;
        errors: string[];
    };
}
