import type { SessionType } from './session-service';
export interface AutoQueueNotification {
    type: 'songs-added' | 'queue-analyzed' | 'error' | 'warning';
    timestamp: Date;
    title: string;
    message: string;
    details?: {
        songsAdded?: number;
        source?: string;
        algorithm?: string;
        reason?: string;
        songs?: Array<{
            title: string;
            artist: string;
        }>;
    };
}
export type AutoQueueNotificationListener = (notification: AutoQueueNotification) => void;
export declare class EnhancedAutoQueueService {
    private static notificationListeners;
    private static isRunning;
    private static checkInterval;
    private static currentConfigId;
    private static queueConsumptionRate;
    private static lastQueueLength;
    private static lastCheckTime;
    static addNotificationListener(listener: AutoQueueNotificationListener): void;
    static removeNotificationListener(listener: AutoQueueNotificationListener): void;
    static start(sessionType?: SessionType): Promise<void>;
    private static startAdaptiveMonitoring;
    private static checkAndAddSongs;
    private static addSongsToQueue;
    private static getSongsByAlgorithm;
    static stop(): void;
    private static getCurrentQueueLength;
    private static getTimeOfDay;
    private static getAlgorithmDisplayName;
    private static emitNotification;
}
