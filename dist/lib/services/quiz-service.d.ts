import type { GameMode, QuizSettings } from '@/lib/types';
import type { QuizQuestion } from '@/lib/database/quiz-data';
interface QuizServiceConfig {
    enableCaching: boolean;
    cacheSize: number;
    cacheTTL: number;
}
export declare class QuizService {
    private static instance;
    private cache;
    private config;
    private _quizDataManager;
    private _mpdClient;
    private _librarySync;
    private _isInitialized;
    private constructor();
    static getInstance(config?: Partial<QuizServiceConfig>): QuizService;
    private ensureInitialized;
    generateQuestions(gameMode: GameMode, settings: QuizSettings): Promise<QuizQuestion[]>;
    getRandomTracks(count: number, filters?: any): Promise<any[]>;
    preloadCommonData(): Promise<void>;
    getAvailableArtists(limit?: number): Promise<string[]>;
    getAvailableGenres(limit?: number): Promise<string[]>;
    private generateCacheKey;
    private getFromCache;
    private setCache;
    clearCache(): void;
    getCacheStats(): {
        size: number;
        maxSize: number;
        hitRate?: number;
    };
    cleanup(): Promise<void>;
}
export declare const quizService: QuizService;
export {};
