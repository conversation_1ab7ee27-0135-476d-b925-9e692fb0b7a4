{"version": 3, "file": "global-audio-service.js", "sourceRoot": "", "sources": ["../../../lib/services/global-audio-service.ts"], "names": [], "mappings": ";;;;;;AA0UA,wCAeC;AAnVD,kDAAyB;AACzB,uDAAkD;AAClD,mCAAkD;AAwBlD,MAAM,uBAAuB;IAgB3B;QAdQ,iBAAY,GAAwB,IAAI,CAAA;QACxC,iBAAY,GAAqB;YACvC,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,KAAK;YACpB,YAAY,EAAE,IAAI;SACnB,CAAA;QAEO,cAAS,GAA6C,EAAE,CAAA;QACxD,0BAAqB,GAAyB,IAAI,CAAA;QAClD,yBAAoB,GAAG,KAAK,CAAA;IAIpC,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC;YACtC,uBAAuB,CAAC,QAAQ,GAAG,IAAI,uBAAuB,EAAE,CAAA;QAClE,CAAC;QACD,OAAO,uBAAuB,CAAC,QAAQ,CAAA;IACzC,CAAC;IAKM,KAAK,CAAC,UAAU;QACrB,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,qBAAqB,CAAA;QACnC,CAAC;QAED,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QAC/C,OAAO,IAAI,CAAC,qBAAqB,CAAA;IACnC,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YACzD,OAAM;QACR,CAAC;QAED,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAA;YAEjE,MAAM,MAAM,GAAG,IAAA,4BAAsB,GAAE,CAAA;YACvC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;YAC9C,CAAC;YAED,IAAI,CAAC,YAAY,GAAG,IAAI,4BAAY,CAAC,MAAM,CAAC,CAAA;YAC5C,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAA;YAEpC,IAAI,CAAC,YAAY,GAAG;gBAClB,GAAG,IAAI,CAAC,YAAY;gBACpB,aAAa,EAAE,IAAI;gBACnB,YAAY,EAAE,IAAI,CAAC,YAAY;aAChC,CAAA;YAED,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAA;YAC1E,IAAI,CAAC,eAAe,EAAE,CAAA;QAExB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAA;YAC3D,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKM,KAAK,CAAC,gBAAgB,CAAC,UAA+B;QAC3D,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAA;YACrE,OAAM;QACR,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA;QAChC,OAAO,CAAC,GAAG,CAAC,oCAAoC,UAAU,CAAC,IAAI,OAAO,UAAU,CAAC,EAAE,EAAE,CAAC,CAAA;QAEtF,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;YAEvB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAA;YAClD,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,eAAe,GAAG,GAAG,EAAE,cAAc,GAAG,GAAG,EAAE,kBAAkB,GAAG,IAAI,EAAE,GAAG,UAAU,CAAA;YAGvG,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,KAAK,MAAM,EAAE,CAAC;gBACxC,MAAM,IAAI,CAAC,uBAAuB,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAA;YACzE,CAAC;iBAAM,IAAI,IAAI,KAAK,MAAM,IAAI,EAAE,KAAK,SAAS,EAAE,CAAC;gBAC/C,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAA;YACpD,CAAC;iBAAM,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,KAAK,MAAM,EAAE,CAAC;gBAC/C,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAA;YAC7C,CAAC;iBAAM,IAAI,IAAI,KAAK,MAAM,IAAI,EAAE,KAAK,SAAS,EAAE,CAAC;gBAC/C,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;YAC3C,CAAC;YAGD,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,EAAE,CAAA;YAC3B,IAAI,CAAC,eAAe,EAAE,CAAA;QAExB,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAA;QACnC,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,uBAAuB,CAAC,eAAuB,EAAE,kBAA2B;QACxF,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAM;QAE9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAA;YAEvD,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAA;gBAEzE,IAAI,kBAAkB,EAAE,CAAC;oBAEvB,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;oBAC3C,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAA;gBACpC,CAAC;qBAAM,CAAC;oBAEN,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;oBAChD,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAA;gBACrC,CAAC;YACH,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAA;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAA;QAC5E,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,uBAAuB,CAAC,cAAsB;QAC1D,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAM;QAE9B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAA;YAG7D,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;gBACnE,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAA;YAClC,CAAC;YAGD,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAClE,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAA;QAEpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAA;QAC5E,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,eAAuB;QACnD,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAM;QAE9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAA;YAEvD,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACrB,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;gBAChD,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAA;YACrC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;QACzD,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,cAAc,CAAC,cAAsB;QACjD,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAM;QAE9B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAA;YAChC,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAClE,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAA;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAA;QACjE,CAAC;IACH,CAAC;IAKM,QAAQ;QACb,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;IACjC,CAAC;IAKM,KAAK,CAAC,SAAS,CAAC,MAAc;QACnC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAA;QAEjC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;QAClD,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAA;IACxB,CAAC;IAKM,SAAS,CAAC,QAA2C;QAC1D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAG7B,OAAO,GAAG,EAAE;YACV,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;YAC9C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACf,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;YACjC,CAAC;QACH,CAAC,CAAA;IACH,CAAC;IAKM,eAAe;QACpB,OAAO,IAAI,CAAC,YAAY,CAAA;IAC1B,CAAC;IAKM,KAAK,CAAC,YAAY;QACvB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAM;QAE9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAA;YACvD,IAAI,CAAC,YAAY,GAAG;gBAClB,GAAG,IAAI,CAAC,YAAY;gBACpB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAA;YACD,IAAI,CAAC,eAAe,EAAE,CAAA;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAA;QAC/D,CAAC;IACH,CAAC;IAKM,KAAK,CAAC,OAAO;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QAC1B,CAAC;QAED,IAAI,CAAC,YAAY,GAAG;YAClB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,KAAK;YACpB,YAAY,EAAE,IAAI;SACnB,CAAA;QAED,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;QACnB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA;IACnC,CAAC;IAKO,eAAe;QACrB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAChC,IAAI,CAAC;gBACH,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAA;YAC1D,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;;AAlSc,gCAAQ,GAAmC,IAAI,AAAvC,CAAuC;AAsSnD,QAAA,kBAAkB,GAAG,uBAAuB,CAAC,WAAW,EAAE,CAAA;AAGvE,SAAgB,cAAc;IAC5B,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,eAAK,CAAC,QAAQ,CAAmB,0BAAkB,CAAC,QAAQ,EAAE,CAAC,CAAA;IAEzF,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,MAAM,WAAW,GAAG,0BAAkB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;QAC1D,OAAO,WAAW,CAAA;IACpB,CAAC,EAAE,EAAE,CAAC,CAAA;IAEN,OAAO;QACL,GAAG,KAAK;QACR,gBAAgB,EAAE,0BAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,0BAAkB,CAAC;QAC9E,SAAS,EAAE,0BAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,0BAAkB,CAAC;QAChE,eAAe,EAAE,0BAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,0BAAkB,CAAC;QAC5E,YAAY,EAAE,0BAAkB,CAAC,YAAY,CAAC,IAAI,CAAC,0BAAkB,CAAC;KACvE,CAAA;AACH,CAAC"}