import { type QuizTrack } from '@/lib/database/prisma';
interface TrackFilters {
    genre?: string;
    decade?: string;
    difficulty?: number;
    excludeIds?: string[];
    minPopularity?: number;
    maxPopularity?: number;
    minLastPlayedMinutes?: number;
    hasFile?: boolean;
}
interface DatabaseStats {
    totalTracks: number;
    tracksWithFiles: number;
    uniqueArtists: number;
    uniqueGenres: number;
}
export declare class DatabaseService {
    private static instance;
    private statsCache;
    private readonly STATS_CACHE_TTL;
    private constructor();
    static getInstance(): DatabaseService;
    getRandomTracks(count: number, filters?: TrackFilters): Promise<QuizTrack[]>;
    private getRandomTracksOptimized;
    private getRandomTracksFallback;
    private buildWhereClause;
    getStats(): Promise<DatabaseStats>;
    getAvailableArtists(limit?: number): Promise<string[]>;
    getAvailableGenres(limit?: number): Promise<string[]>;
    updateTrackStats(trackId: string, stats: {
        played?: boolean;
        correct?: boolean;
    }): Promise<void>;
    clearStatsCache(): void;
}
export declare const databaseService: DatabaseService;
export {};
