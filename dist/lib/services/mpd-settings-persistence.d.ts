export interface PersistedMPDSettings {
    crossfade: number;
    replayGain: {
        mode: 'off' | 'track' | 'album' | 'auto';
        preventClipping: boolean;
        missingPreamp: number;
    };
    lastUpdated: Date;
}
export declare class MPDSettingsPersistence {
    private static instance;
    private storageKey;
    private constructor();
    static getInstance(): MPDSettingsPersistence;
    saveSettings(settings: PersistedMPDSettings): void;
    saveSettingsToDatabase(userId: string, settings: PersistedMPDSettings): Promise<void>;
    loadSettings(): PersistedMPDSettings | null;
    loadSettingsFromDatabase(userId: string): Promise<PersistedMPDSettings | null>;
    clearSettings(): void;
    areSettingsRecent(settings: PersistedMPDSettings): boolean;
    getDefaultSettings(): PersistedMPDSettings;
}
