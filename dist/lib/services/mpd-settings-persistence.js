"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MPDSettingsPersistence = void 0;
const prisma_1 = require("@/lib/database/prisma");
class MPDSettingsPersistence {
    constructor() {
        this.storageKey = 'mpd_audio_settings';
    }
    static getInstance() {
        if (!MPDSettingsPersistence.instance) {
            MPDSettingsPersistence.instance = new MPDSettingsPersistence();
        }
        return MPDSettingsPersistence.instance;
    }
    saveSettings(settings) {
        if (typeof window !== 'undefined') {
            try {
                const settingsWithTimestamp = {
                    ...settings,
                    lastUpdated: new Date()
                };
                localStorage.setItem(this.storageKey, JSON.stringify(settingsWithTimestamp));
                console.log('MPD settings saved to localStorage:', settingsWithTimestamp);
            }
            catch (error) {
                console.error('Failed to save MPD settings to localStorage:', error);
            }
        }
        else {
            console.log('Server-side save not available in synchronous method');
        }
    }
    async saveSettingsToDatabase(userId, settings) {
        try {
            const user = await prisma_1.prisma.user.findUnique({
                where: { id: userId },
                select: { preferences: true }
            });
            if (!user) {
                throw new Error('User not found');
            }
            const currentPrefs = JSON.parse(user.preferences || '{}');
            const updatedPrefs = {
                ...currentPrefs,
                audioEnhancements: {
                    ...settings,
                    lastUpdated: new Date()
                }
            };
            await prisma_1.prisma.user.update({
                where: { id: userId },
                data: { preferences: JSON.stringify(updatedPrefs) }
            });
            console.log('MPD settings saved to database for user:', userId);
        }
        catch (error) {
            console.error('Failed to save MPD settings to database:', error);
            throw error;
        }
    }
    loadSettings() {
        if (typeof window !== 'undefined') {
            try {
                const stored = localStorage.getItem(this.storageKey);
                if (stored) {
                    const settings = JSON.parse(stored);
                    return {
                        ...settings,
                        lastUpdated: new Date(settings.lastUpdated)
                    };
                }
            }
            catch (error) {
                console.error('Failed to load MPD settings from localStorage:', error);
            }
        }
        return null;
    }
    async loadSettingsFromDatabase(userId) {
        try {
            const user = await prisma_1.prisma.user.findUnique({
                where: { id: userId },
                select: { preferences: true }
            });
            if (!user) {
                return null;
            }
            const prefs = JSON.parse(user.preferences || '{}');
            if (prefs.audioEnhancements) {
                return {
                    ...prefs.audioEnhancements,
                    lastUpdated: new Date(prefs.audioEnhancements.lastUpdated)
                };
            }
        }
        catch (error) {
            console.error('Failed to load MPD settings from database:', error);
        }
        return null;
    }
    clearSettings() {
        if (typeof window !== 'undefined') {
            localStorage.removeItem(this.storageKey);
        }
    }
    areSettingsRecent(settings) {
        const now = new Date();
        const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        return settings.lastUpdated > dayAgo;
    }
    getDefaultSettings() {
        return {
            crossfade: 0,
            replayGain: {
                mode: 'off',
                preventClipping: true,
                missingPreamp: 0
            },
            lastUpdated: new Date()
        };
    }
}
exports.MPDSettingsPersistence = MPDSettingsPersistence;
//# sourceMappingURL=mpd-settings-persistence.js.map