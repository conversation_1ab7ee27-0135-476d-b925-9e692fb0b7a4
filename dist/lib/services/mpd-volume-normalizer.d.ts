import { MPDClient } from '@/lib/mpd-client';
export declare class MPDVolumeNormalizer {
    private mpdClient;
    private userVolume;
    private isEnabled;
    private currentTrackFile;
    private pollingInterval;
    private lastStatus;
    constructor(mpdClient: MPDClient);
    start(intervalMs?: number): Promise<void>;
    stop(): void;
    setEnabled(enabled: boolean): void;
    setUserVolume(volume: number): void;
    private checkAndAdjustVolume;
    private getTrackGain;
}
export declare function getMPDVolumeNormalizer(mpdClient: MPDClient): MPDVolumeNormalizer;
export declare function stopMPDVolumeNormalizer(): void;
