"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitService = void 0;
exports.withRateLimit = withRateLimit;
class RateLimitService {
    constructor() {
        this.inMemoryStore = new Map();
        this.cleanupInterval = null;
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpiredEntries();
        }, 60000);
    }
    static getInstance() {
        if (!RateLimitService.instance) {
            RateLimitService.instance = new RateLimitService();
        }
        return RateLimitService.instance;
    }
    cleanupExpiredEntries() {
        const now = Date.now();
        for (const [key, value] of this.inMemoryStore.entries()) {
            if (value.reset < now) {
                this.inMemoryStore.delete(key);
            }
        }
    }
    async checkLimit(key, limit, windowSeconds) {
        const now = Date.now();
        const window = windowSeconds * 1000;
        const reset = now + window;
        const entry = this.inMemoryStore.get(key);
        if (!entry || entry.reset < now) {
            this.inMemoryStore.set(key, { count: 1, reset });
            return {
                allowed: true,
                remaining: limit - 1,
                reset
            };
        }
        entry.count++;
        return {
            allowed: entry.count <= limit,
            remaining: Math.max(0, limit - entry.count),
            reset: entry.reset
        };
    }
    async reset(key) {
        this.inMemoryStore.delete(key);
    }
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        this.inMemoryStore.clear();
    }
}
exports.RateLimitService = RateLimitService;
function withRateLimit(handler, options) {
    return async (req, ...args) => {
        const rateLimiter = RateLimitService.getInstance();
        const result = await rateLimiter.checkLimit(options.key(req), options.limit, options.window);
        if (!result.allowed) {
            return new Response(JSON.stringify({
                error: 'Rate limit exceeded',
                remaining: result.remaining,
                reset: new Date(result.reset).toISOString()
            }), {
                status: 429,
                headers: {
                    'X-RateLimit-Limit': options.limit.toString(),
                    'X-RateLimit-Remaining': result.remaining.toString(),
                    'X-RateLimit-Reset': result.reset.toString(),
                    'Retry-After': Math.ceil((result.reset - Date.now()) / 1000).toString()
                }
            });
        }
        const response = await handler(req, ...args);
        if (response instanceof Response) {
            response.headers.set('X-RateLimit-Limit', options.limit.toString());
            response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
            response.headers.set('X-RateLimit-Reset', result.reset.toString());
        }
        return response;
    };
}
//# sourceMappingURL=rate-limit-service.js.map