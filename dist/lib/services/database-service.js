"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseService = exports.DatabaseService = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
const logger_1 = require("@/lib/logger");
const utils_1 = require("@/lib/utils");
class DatabaseService {
    constructor() {
        this.statsCache = null;
        this.STATS_CACHE_TTL = 10 * 60 * 1000;
        logger_1.databaseLogger.info('DatabaseService initialized');
    }
    static getInstance() {
        if (!DatabaseService.instance) {
            DatabaseService.instance = new DatabaseService();
        }
        return DatabaseService.instance;
    }
    async getRandomTracks(count, filters = {}) {
        return await logger_1.PerformanceLogger.measureAsync('db-get-random-tracks', async () => {
            logger_1.databaseLogger.debug('Getting random tracks', { count, filters });
            const whereClause = this.buildWhereClause(filters);
            try {
                const tracks = await this.getRandomTracksOptimized(count, whereClause);
                if (tracks.length >= count) {
                    logger_1.databaseLogger.debug('Database random query successful', {
                        requested: count,
                        returned: tracks.length
                    });
                    return tracks.slice(0, count);
                }
                logger_1.databaseLogger.debug('Falling back to Prisma query with client shuffle');
                return await this.getRandomTracksFallback(count, whereClause);
            }
            catch (error) {
                logger_1.databaseLogger.error('Random tracks query failed', error, { count, filters });
                throw error;
            }
        });
    }
    async getRandomTracksOptimized(count, whereClause) {
        const isPostgreSQL = process.env.DATABASE_URL?.includes('postgresql');
        const randomFunction = isPostgreSQL ? 'RANDOM()' : 'RANDOM()';
        const tableName = isPostgreSQL ? '"QuizTrack"' : 'QuizTrack';
        const conditions = [];
        const params = [];
        if (whereClause.genre) {
            conditions.push('genre = $' + (params.length + 1));
            params.push(whereClause.genre);
        }
        if (whereClause.year) {
            if (whereClause.year.gte && whereClause.year.lte) {
                conditions.push('year BETWEEN $' + (params.length + 1) + ' AND $' + (params.length + 2));
                params.push(whereClause.year.gte, whereClause.year.lte);
            }
        }
        if (whereClause.difficultyRating) {
            conditions.push('"difficultyRating" = $' + (params.length + 1));
            params.push(whereClause.difficultyRating);
        }
        if (whereClause.popularityScore) {
            if (whereClause.popularityScore.gte) {
                conditions.push('"popularityScore" >= $' + (params.length + 1));
                params.push(whereClause.popularityScore.gte);
            }
            if (whereClause.popularityScore.lte) {
                conditions.push('"popularityScore" <= $' + (params.length + 1));
                params.push(whereClause.popularityScore.lte);
            }
        }
        if (whereClause.mpdFilePath) {
            conditions.push('"mpdFilePath" IS NOT NULL AND "mpdFilePath" != \'\'');
        }
        if (whereClause.OR) {
            const cutoffDate = new Date(Date.now() - (180 * 60 * 1000)).toISOString();
            conditions.push('("lastPlayed" IS NULL OR "lastPlayed" < $' + (params.length + 1) + ')');
            params.push(cutoffDate);
        }
        const whereSQL = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        const sql = `SELECT * FROM ${tableName} ${whereSQL} ORDER BY ${randomFunction} LIMIT $${params.length + 1}`;
        params.push(count);
        const tracks = await prisma_1.default.$queryRawUnsafe(sql, ...params);
        return tracks || [];
    }
    async getRandomTracksFallback(count, whereClause) {
        const fetchCount = Math.min(count * 2, 500);
        const tracks = await prisma_1.default.quizTrack.findMany({
            where: whereClause,
            take: fetchCount
        });
        const shuffled = utils_1.RandomUtils.shuffle(tracks);
        return shuffled.slice(0, count);
    }
    buildWhereClause(filters) {
        const whereClause = {};
        if (filters.genre) {
            whereClause.genre = filters.genre;
        }
        if (filters.decade) {
            const startYear = parseInt(filters.decade);
            const endYear = startYear + 9;
            whereClause.year = {
                gte: startYear,
                lte: endYear
            };
        }
        if (filters.difficulty) {
            whereClause.difficultyRating = filters.difficulty;
        }
        if (filters.minPopularity || filters.maxPopularity) {
            whereClause.popularityScore = {};
            if (filters.minPopularity) {
                whereClause.popularityScore.gte = filters.minPopularity;
            }
            if (filters.maxPopularity) {
                whereClause.popularityScore.lte = filters.maxPopularity;
            }
        }
        if (filters.hasFile !== false) {
            whereClause.mpdFilePath = {
                not: null
            };
        }
        if (filters.excludeIds && filters.excludeIds.length > 0) {
            whereClause.id = {
                notIn: filters.excludeIds
            };
        }
        if (filters.minLastPlayedMinutes) {
            const cutoffMs = Date.now() - filters.minLastPlayedMinutes * 60 * 1000;
            whereClause.OR = [
                { lastPlayed: null },
                { lastPlayed: { lt: new Date(cutoffMs) } }
            ];
        }
        return whereClause;
    }
    async getStats() {
        if (this.statsCache && Date.now() - this.statsCache.timestamp < this.STATS_CACHE_TTL) {
            return this.statsCache.data;
        }
        const stats = await logger_1.PerformanceLogger.measureAsync('db-get-stats', async () => {
            const [totalTracks, tracksWithFiles, uniqueArtists, uniqueGenres] = await Promise.all([
                prisma_1.default.quizTrack.count(),
                prisma_1.default.quizTrack.count({
                    where: {
                        mpdFilePath: { not: null }
                    }
                }),
                prisma_1.default.quizTrack.findMany({
                    select: { artist: true },
                    distinct: ['artist'],
                    where: { artist: { not: null } }
                }).then(results => results.length),
                prisma_1.default.quizTrack.findMany({
                    select: { genre: true },
                    distinct: ['genre'],
                    where: { genre: { not: null } }
                }).then(results => results.length)
            ]);
            return {
                totalTracks,
                tracksWithFiles,
                uniqueArtists,
                uniqueGenres
            };
        });
        this.statsCache = {
            data: stats,
            timestamp: Date.now()
        };
        logger_1.databaseLogger.info('Database stats retrieved', stats);
        return stats;
    }
    async getAvailableArtists(limit = 100) {
        return await logger_1.PerformanceLogger.measureAsync('db-get-artists', async () => {
            const artists = await prisma_1.default.quizTrack.findMany({
                where: {
                    artist: { not: null },
                    mpdFilePath: { not: null }
                },
                select: { artist: true },
                distinct: ['artist'],
                take: limit
            });
            return artists
                .map(a => a.artist)
                .filter(Boolean)
                .sort();
        });
    }
    async getAvailableGenres(limit = 50) {
        return await logger_1.PerformanceLogger.measureAsync('db-get-genres', async () => {
            const genres = await prisma_1.default.quizTrack.findMany({
                where: {
                    genre: { not: null },
                    mpdFilePath: { not: null }
                },
                select: { genre: true },
                distinct: ['genre'],
                take: limit
            });
            return genres
                .map(g => g.genre)
                .filter(Boolean)
                .sort();
        });
    }
    async updateTrackStats(trackId, stats) {
        const updateData = {};
        if (stats.played) {
            updateData.timesPlayed = { increment: 1 };
            updateData.lastPlayed = new Date();
        }
        if (stats.correct) {
            updateData.correctAnswers = { increment: 1 };
        }
        if (Object.keys(updateData).length > 0) {
            await prisma_1.default.quizTrack.update({
                where: { id: trackId },
                data: updateData
            });
        }
    }
    clearStatsCache() {
        this.statsCache = null;
        logger_1.databaseLogger.debug('Database stats cache cleared');
    }
}
exports.DatabaseService = DatabaseService;
DatabaseService.instance = null;
exports.databaseService = DatabaseService.getInstance();
//# sourceMappingURL=database-service.js.map