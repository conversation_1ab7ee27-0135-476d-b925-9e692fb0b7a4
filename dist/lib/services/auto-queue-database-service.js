"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoQueueDatabaseService = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
class AutoQueueDatabaseService {
    static async getActiveConfig() {
        try {
            const config = await prisma_1.default.autoQueueConfig.findFirst({
                where: { isActive: true }
            });
            if (!config) {
                const defaultConfig = await prisma_1.default.autoQueueConfig.findFirst({
                    where: { isDefault: true }
                });
                if (!defaultConfig) {
                    return await this.createDefaultConfig();
                }
                return this.parseConfig(defaultConfig);
            }
            return this.parseConfig(config);
        }
        catch (error) {
            console.error('[AutoQueueDB] Failed to get active config:', error);
            return null;
        }
    }
    static async getConfig(id) {
        try {
            const config = await prisma_1.default.autoQueueConfig.findUnique({
                where: { id }
            });
            if (!config)
                return null;
            return this.parseConfig(config);
        }
        catch (error) {
            console.error('[AutoQueueDB] Failed to get config:', error);
            return null;
        }
    }
    static async getAllConfigs() {
        try {
            const configs = await prisma_1.default.autoQueueConfig.findMany({
                orderBy: [
                    { isActive: 'desc' },
                    { isDefault: 'desc' },
                    { name: 'asc' }
                ]
            });
            return configs.map(config => this.parseConfig(config));
        }
        catch (error) {
            console.error('[AutoQueueDB] Failed to get all configs:', error);
            return [];
        }
    }
    static async updateAnalytics(configId, updates) {
        try {
            const config = await prisma_1.default.autoQueueConfig.findUnique({
                where: { id: configId }
            });
            if (!config)
                return;
            await prisma_1.default.autoQueueConfig.update({
                where: { id: configId },
                data: {
                    totalSongsAdded: config.totalSongsAdded + (updates.songsAdded || 0),
                    totalTriggers: config.totalTriggers + (updates.triggerCount || 0),
                    lastTriggered: updates.triggerCount ? new Date() : config.lastTriggered,
                    averageQueueTime: updates.averageQueueTime !== undefined
                        ? (config.averageQueueTime * config.totalTriggers + updates.averageQueueTime) / (config.totalTriggers + 1)
                        : config.averageQueueTime
                }
            });
        }
        catch (error) {
            console.error('[AutoQueueDB] Failed to update analytics:', error);
        }
    }
    static async getAdaptiveInterval(configId, queueConsumptionRate) {
        try {
            const config = await this.getConfig(configId);
            if (!config || !config.adaptiveMonitoring) {
                return config?.checkInterval || 30;
            }
            const baseInterval = config.checkInterval;
            const minInterval = Math.max(10, baseInterval * 0.5);
            const maxInterval = Math.min(300, baseInterval * 2);
            if (queueConsumptionRate > 0.8) {
                return minInterval;
            }
            else if (queueConsumptionRate < 0.2) {
                return maxInterval;
            }
            else {
                const factor = 1 - queueConsumptionRate;
                return Math.round(minInterval + (baseInterval - minInterval) * factor);
            }
        }
        catch (error) {
            console.error('[AutoQueueDB] Failed to calculate adaptive interval:', error);
            return 30;
        }
    }
    static async shouldTriggerPredictive(configId, currentQueueLength, consumptionRate) {
        try {
            const config = await this.getConfig(configId);
            if (!config || !config.predictiveQueueing) {
                return false;
            }
            const timeToEmpty = currentQueueLength / consumptionRate;
            return timeToEmpty <= 2;
        }
        catch (error) {
            console.error('[AutoQueueDB] Failed to check predictive trigger:', error);
            return false;
        }
    }
    static async getContentFilters(configId) {
        try {
            const config = await this.getConfig(configId);
            if (!config) {
                return { excludeExplicit: false };
            }
            return {
                genres: config.genreFilter.length > 0 ? config.genreFilter : undefined,
                yearRange: (config.yearRangeMin || config.yearRangeMax) ? {
                    min: config.yearRangeMin || undefined,
                    max: config.yearRangeMax || undefined
                } : undefined,
                durationRange: (config.minDuration || config.maxDuration) ? {
                    min: config.minDuration || undefined,
                    max: config.maxDuration || undefined
                } : undefined,
                excludeExplicit: config.excludeExplicit
            };
        }
        catch (error) {
            console.error('[AutoQueueDB] Failed to get content filters:', error);
            return { excludeExplicit: false };
        }
    }
    static async createDefaultConfig() {
        try {
            const config = await prisma_1.default.autoQueueConfig.create({
                data: {
                    name: 'Default Configuration',
                    description: 'Automatically created default configuration',
                    isActive: true,
                    isDefault: true,
                    queueThreshold: 5,
                    checkInterval: 30,
                    minConnectedUsers: 1,
                    adaptiveMonitoring: false,
                    predictiveQueueing: false,
                    algorithm: 'hybrid',
                    algorithmWeights: JSON.stringify({
                        favorites: 0.4,
                        intelligent: 0.3,
                        popularity: 0.3
                    }),
                    fallbackEnabled: true,
                    fallbackAlgorithm: 'popularity',
                    genreFilter: '[]',
                    excludeExplicit: false,
                    diversityLevel: 'medium',
                    energyAdaptation: true,
                    timeOfDayAdaptation: true,
                    userPreferenceWeight: 0.7,
                    maxSongsPerTrigger: 10,
                    preventRepeats: 50,
                    maintainBuffer: true,
                    bufferSize: 20,
                    emergencyQueueSize: 5
                }
            });
            return this.parseConfig(config);
        }
        catch (error) {
            console.error('[AutoQueueDB] Failed to create default config:', error);
            throw error;
        }
    }
    static parseConfig(config) {
        return {
            ...config,
            algorithmWeights: JSON.parse(config.algorithmWeights),
            genreFilter: JSON.parse(config.genreFilter)
        };
    }
}
exports.AutoQueueDatabaseService = AutoQueueDatabaseService;
//# sourceMappingURL=auto-queue-database-service.js.map