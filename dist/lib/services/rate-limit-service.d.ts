interface RateLimitResult {
    allowed: boolean;
    remaining: number;
    reset: number;
}
export declare class RateLimitService {
    private static instance;
    private inMemoryStore;
    private cleanupInterval;
    private constructor();
    static getInstance(): RateLimitService;
    private cleanupExpiredEntries;
    checkLimit(key: string, limit: number, windowSeconds: number): Promise<RateLimitResult>;
    reset(key: string): Promise<void>;
    destroy(): void;
}
export declare function withRateLimit(handler: Function, options: {
    key: (req: Request) => string;
    limit: number;
    window: number;
}): (req: Request, ...args: any[]) => Promise<any>;
export {};
