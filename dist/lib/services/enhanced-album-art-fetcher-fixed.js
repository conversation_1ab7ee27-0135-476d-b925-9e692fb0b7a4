"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedAlbumArtFetcher = void 0;
exports.downloadAlbumArtWithRetry = downloadAlbumArtWithRetry;
class EnhancedAlbumArtFetcher {
    constructor() {
        this.spotifyToken = null;
        this.lastFmApiKey = process.env.LASTFM_API_KEY || null;
        this.musicBrainzUserAgent = process.env.MUSICBRAINZ_USER_AGENT || 'MusicQuiz/1.0';
    }
    async fetchWithTimeout(url, options = {}, timeoutMs = 10000) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeoutMs);
        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        }
        catch (error) {
            clearTimeout(timeoutId);
            if (error instanceof Error && error.name === 'AbortError') {
                throw new Error(`Request timeout after ${timeoutMs}ms`);
            }
            throw error;
        }
    }
    async searchAlbumArt(options) {
        const strategies = [
            { name: 'Spotify', fn: () => this.searchSpotify(options) },
            { name: 'MusicBrainz/CoverArtArchive', fn: () => this.searchMusicBrainzCoverArtArchive(options) },
            { name: 'Last.fm', fn: () => this.searchLastFm(options) },
            { name: 'iTunes', fn: () => this.searchItunes(options) },
            { name: 'Deezer', fn: () => this.searchDeezer(options) },
        ];
        console.log(`[AlbumArtFetcher] Searching for: ${options.artist} - ${options.album || options.title}`);
        for (const strategy of strategies) {
            try {
                console.log(`[AlbumArtFetcher] Trying ${strategy.name}...`);
                const result = await strategy.fn();
                if (result && result.url) {
                    console.log(`[AlbumArtFetcher] ✅ Found album art via ${result.source} (confidence: ${result.confidence})`);
                    return result;
                }
                else {
                    console.log(`[AlbumArtFetcher] ❌ ${strategy.name} returned no results`);
                }
            }
            catch (error) {
                console.error(`[AlbumArtFetcher] ❌ ${strategy.name} failed:`, error instanceof Error ? error.message : error);
            }
        }
        if (options.enableFuzzyMatching !== false) {
            console.log(`[AlbumArtFetcher] Trying fuzzy matching variants...`);
            return this.searchWithVariations(options);
        }
        return null;
    }
    async searchSpotify(options) {
        if (!process.env.SPOTIFY_CLIENT_ID || !process.env.SPOTIFY_CLIENT_SECRET) {
            console.log('[Spotify] No credentials configured');
            return null;
        }
        try {
            await this.ensureSpotifyToken();
            const queryParts = [];
            if (options.artist)
                queryParts.push(`artist:"${options.artist}"`);
            if (options.album)
                queryParts.push(`album:"${options.album}"`);
            else if (options.title)
                queryParts.push(`track:"${options.title}"`);
            const query = queryParts.join(' ');
            const url = `https://api.spotify.com/v1/search?q=${encodeURIComponent(query)}&type=${options.album ? 'album' : 'track'}&limit=5`;
            console.log(`[Spotify] Query: ${query}`);
            const response = await this.fetchWithTimeout(url, {
                headers: { Authorization: `Bearer ${this.spotifyToken}` }
            });
            if (!response.ok) {
                console.log(`[Spotify] Response: ${response.status} ${response.statusText}`);
                return null;
            }
            const data = await response.json();
            const items = options.album ? data.albums?.items : data.tracks?.items;
            console.log(`[Spotify] Found ${items?.length || 0} results`);
            if (!items || items.length === 0)
                return null;
            const bestMatch = this.findBestMatch(items, options);
            if (!bestMatch)
                return null;
            const images = options.album ? bestMatch.images : bestMatch.album?.images;
            const largestImage = images?.reduce((prev, current) => (!prev || current.width > prev.width) ? current : prev, null);
            if (!largestImage?.url)
                return null;
            return {
                url: largestImage.url,
                source: 'spotify',
                confidence: this.calculateConfidence(bestMatch, options),
                metadata: {
                    width: largestImage.width,
                    height: largestImage.height
                }
            };
        }
        catch (error) {
            console.error('[Spotify] Error:', error);
            return null;
        }
    }
    async searchMusicBrainzCoverArtArchive(options) {
        try {
            const query = this.buildMusicBrainzQuery(options);
            const searchUrl = `https://musicbrainz.org/ws/2/release?query=${encodeURIComponent(query)}&limit=10&fmt=json`;
            console.log(`[MusicBrainz] Query: ${query}`);
            const searchResponse = await this.fetchWithTimeout(searchUrl, {
                headers: {
                    'User-Agent': this.musicBrainzUserAgent,
                    'Accept': 'application/json'
                }
            });
            if (!searchResponse.ok) {
                console.log(`[MusicBrainz] Response: ${searchResponse.status} ${searchResponse.statusText}`);
                return null;
            }
            const searchData = await searchResponse.json();
            const releases = searchData.releases || [];
            console.log(`[MusicBrainz] Found ${releases.length} releases`);
            for (const release of releases.slice(0, 5)) {
                try {
                    console.log(`[CoverArtArchive] Checking release: ${release.title} (${release.id})`);
                    const coverArtUrl = `https://coverartarchive.org/release/${release.id}`;
                    const coverArtResponse = await this.fetchWithTimeout(coverArtUrl, {
                        headers: { 'Accept': 'application/json' }
                    }, 5000);
                    if (coverArtResponse.ok) {
                        const coverArtData = await coverArtResponse.json();
                        const images = coverArtData.images || [];
                        console.log(`[CoverArtArchive] Found ${images.length} images`);
                        const frontCover = images.find((img) => img.types?.includes('Front')) || images[0];
                        if (frontCover?.image) {
                            return {
                                url: frontCover.image,
                                source: 'coverartarchive',
                                confidence: this.calculateMusicBrainzConfidence(release, options),
                                metadata: {
                                    width: frontCover.width,
                                    height: frontCover.height,
                                    format: frontCover.image.split('.').pop()
                                }
                            };
                        }
                    }
                    else if (coverArtResponse.status !== 404) {
                        console.log(`[CoverArtArchive] Response: ${coverArtResponse.status}`);
                    }
                }
                catch (error) {
                    console.log(`[CoverArtArchive] Error checking release:`, error instanceof Error ? error.message : error);
                }
            }
            return null;
        }
        catch (error) {
            console.error('[MusicBrainz] Error:', error);
            return null;
        }
    }
    async searchLastFm(options) {
        if (!this.lastFmApiKey) {
            console.log('[Last.fm] No API key configured');
            return null;
        }
        try {
            let url;
            if (options.album && options.artist) {
                url = `https://ws.audioscrobbler.com/2.0/?method=album.getinfo` +
                    `&artist=${encodeURIComponent(options.artist)}` +
                    `&album=${encodeURIComponent(options.album)}` +
                    `&api_key=${this.lastFmApiKey}&format=json`;
            }
            else if (options.title && options.artist) {
                url = `https://ws.audioscrobbler.com/2.0/?method=track.getinfo` +
                    `&artist=${encodeURIComponent(options.artist)}` +
                    `&track=${encodeURIComponent(options.title)}` +
                    `&api_key=${this.lastFmApiKey}&format=json`;
            }
            else {
                return null;
            }
            console.log(`[Last.fm] URL: ${url.replace(this.lastFmApiKey, 'REDACTED')}`);
            const response = await this.fetchWithTimeout(url);
            if (!response.ok) {
                console.log(`[Last.fm] Response: ${response.status} ${response.statusText}`);
                return null;
            }
            const data = await response.json();
            const images = data.album?.image || data.track?.album?.image || [];
            console.log(`[Last.fm] Found ${images.length} images`);
            const largeImage = images.find((img) => img.size === 'extralarge') ||
                images.find((img) => img.size === 'large') ||
                images[images.length - 1];
            if (!largeImage?.['#text']) {
                console.log(`[Last.fm] No valid image URL found`);
                return null;
            }
            const imageUrl = largeImage['#text'].trim();
            if (!imageUrl || imageUrl === '') {
                console.log(`[Last.fm] Empty image URL`);
                return null;
            }
            return {
                url: imageUrl,
                source: 'lastfm',
                confidence: 0.8,
                metadata: {
                    format: 'jpg'
                }
            };
        }
        catch (error) {
            console.error('[Last.fm] Error:', error);
            return null;
        }
    }
    async searchItunes(options) {
        try {
            let term;
            if (options.album) {
                term = options.artist === '' ? options.album : `${options.artist} ${options.album}`;
            }
            else {
                term = `${options.artist} ${options.title}`;
            }
            const entity = options.album ? 'album' : 'song';
            const url = `https://itunes.apple.com/search?term=${encodeURIComponent(term)}&entity=${entity}&limit=10`;
            console.log(`[iTunes] Query: ${term}`);
            const response = await this.fetchWithTimeout(url);
            if (!response.ok) {
                console.log(`[iTunes] Response: ${response.status} ${response.statusText}`);
                return null;
            }
            const data = await response.json();
            const results = data.results || [];
            console.log(`[iTunes] Found ${results.length} results`);
            if (results.length === 0)
                return null;
            const bestMatch = this.findBestItunesMatch(results, options);
            if (!bestMatch)
                return null;
            const artworkUrl = bestMatch.artworkUrl100 || bestMatch.artworkUrl60;
            if (!artworkUrl)
                return null;
            const highResUrl = artworkUrl.replace(/100x100|60x60/, '3000x3000');
            return {
                url: highResUrl,
                source: 'itunes',
                confidence: this.calculateItunesConfidence(bestMatch, options),
                metadata: {
                    width: 3000,
                    height: 3000,
                    format: 'jpg'
                }
            };
        }
        catch (error) {
            console.error('[iTunes] Error:', error);
            return null;
        }
    }
    async searchDeezer(options) {
        try {
            let query;
            if (options.album) {
                query = options.artist === '' ? `album:"${options.album}"` : `artist:"${options.artist}" album:"${options.album}"`;
            }
            else {
                query = `artist:"${options.artist}" track:"${options.title}"`;
            }
            const url = `https://api.deezer.com/search?q=${encodeURIComponent(query)}&limit=10`;
            console.log(`[Deezer] Query: ${query}`);
            const response = await this.fetchWithTimeout(url);
            if (!response.ok) {
                console.log(`[Deezer] Response: ${response.status} ${response.statusText}`);
                return null;
            }
            const data = await response.json();
            const results = data.data || [];
            console.log(`[Deezer] Found ${results.length} results`);
            if (results.length === 0)
                return null;
            const firstResult = results[0];
            const coverUrl = firstResult.album?.cover_xl || firstResult.album?.cover_big;
            if (!coverUrl)
                return null;
            return {
                url: coverUrl,
                source: 'deezer',
                confidence: 0.7,
                metadata: {
                    width: coverUrl.includes('xl') ? 1000 : 500,
                    height: coverUrl.includes('xl') ? 1000 : 500,
                    format: 'jpg'
                }
            };
        }
        catch (error) {
            console.error('[Deezer] Error:', error);
            return null;
        }
    }
    async searchWithVariations(options) {
        const variations = this.generateSearchVariations(options);
        console.log(`[AlbumArtFetcher] Generated ${variations.length} variations`);
        for (const variation of variations) {
            const result = await this.searchAlbumArt({
                ...variation,
                enableFuzzyMatching: false
            });
            if (result) {
                result.confidence *= 0.8;
                return result;
            }
        }
        return null;
    }
    generateSearchVariations(options) {
        const variations = [];
        const typoFixed = this.fixCommonTypos(options.artist);
        if (typoFixed !== options.artist) {
            variations.push({
                ...options,
                artist: typoFixed
            });
        }
        const cleanArtist = this.cleanArtistName(options.artist);
        const cleanAlbum = options.album ? this.cleanAlbumName(options.album) : undefined;
        if (cleanArtist !== options.artist || cleanAlbum !== options.album) {
            variations.push({
                ...options,
                artist: cleanArtist,
                album: cleanAlbum
            });
        }
        if (options.artist.toUpperCase() === 'VA') {
            variations.push({
                ...options,
                artist: 'Various Artists'
            });
        }
        else if (options.artist.toLowerCase() === 'various artists') {
            variations.push({
                ...options,
                artist: 'VA'
            });
        }
        if (options.artist.toLowerCase().includes('feat')) {
            const mainArtist = options.artist.split(/feat\.?|featuring/i)[0].trim();
            variations.push({
                ...options,
                artist: mainArtist
            });
        }
        if (options.album) {
            const simpleAlbum = options.album.replace(/\([^)]*edition[^)]*\)/gi, '').trim();
            if (simpleAlbum !== options.album) {
                variations.push({
                    ...options,
                    album: simpleAlbum
                });
            }
            const albumWithoutYear = options.album.replace(/\(\d{4}\)/, '').trim();
            if (albumWithoutYear !== options.album) {
                variations.push({
                    ...options,
                    album: albumWithoutYear
                });
            }
        }
        if (options.album && options.title) {
            variations.push({
                ...options,
                album: undefined
            });
        }
        if (options.album && (options.artist.toUpperCase() === 'VA' ||
            options.artist.toLowerCase() === 'various artists' ||
            options.artist.toLowerCase() === 'various')) {
            variations.push({
                ...options,
                artist: ''
            });
        }
        return variations;
    }
    fixCommonTypos(text) {
        return text
            .replace(/\b(\w+)\s+(\w{1,2})\b/g, (match, p1, p2) => {
            const combined = p1 + p2;
            if (combined.length <= 10) {
                return combined;
            }
            return match;
        })
            .replace(/\s+/g, ' ')
            .trim();
    }
    cleanArtistName(artist) {
        return artist
            .replace(/\s*\([^)]*\)\s*/g, '')
            .replace(/\s*\[[^\]]*\]\s*/g, '')
            .replace(/\s+/g, ' ')
            .trim();
    }
    cleanAlbumName(album) {
        return album
            .replace(/\s*\([^)]*deluxe[^)]*\)/gi, '')
            .replace(/\s*\([^)]*remaster[^)]*\)/gi, '')
            .replace(/\s*\([^)]*anniversary[^)]*\)/gi, '')
            .replace(/\s*\([^)]*edition[^)]*\)/gi, '')
            .replace(/\s+/g, ' ')
            .trim();
    }
    buildMusicBrainzQuery(options) {
        const parts = [];
        if (options.artist) {
            parts.push(`artist:"${options.artist}"`);
        }
        if (options.album) {
            parts.push(`release:"${options.album}"`);
        }
        else if (options.title) {
            parts.push(`recording:"${options.title}"`);
        }
        if (options.year) {
            parts.push(`date:${options.year}`);
        }
        return parts.join(' AND ');
    }
    findBestMatch(items, options) {
        return items.reduce((best, current) => {
            const currentScore = this.calculateMatchScore(current, options);
            const bestScore = best ? this.calculateMatchScore(best, options) : -1;
            return currentScore > bestScore ? current : best;
        }, null);
    }
    findBestItunesMatch(results, options) {
        return results.reduce((best, current) => {
            const currentScore = this.calculateItunesMatchScore(current, options);
            const bestScore = best ? this.calculateItunesMatchScore(best, options) : -1;
            return currentScore > bestScore ? current : best;
        }, null);
    }
    calculateMatchScore(item, options) {
        let score = 0;
        const itemArtist = item.artists?.[0]?.name || '';
        if (this.fuzzyMatch(itemArtist, options.artist)) {
            score += 0.4;
        }
        if (options.album) {
            const itemAlbum = item.name || item.album?.name || '';
            if (this.fuzzyMatch(itemAlbum, options.album)) {
                score += 0.4;
            }
        }
        if (options.title && !options.album) {
            const itemTitle = item.name || '';
            if (this.fuzzyMatch(itemTitle, options.title)) {
                score += 0.4;
            }
        }
        if (item.popularity) {
            score += (item.popularity / 100) * 0.2;
        }
        return score;
    }
    calculateItunesMatchScore(item, options) {
        let score = 0;
        if (this.fuzzyMatch(item.artistName, options.artist)) {
            score += 0.4;
        }
        if (options.album && this.fuzzyMatch(item.collectionName, options.album)) {
            score += 0.4;
        }
        if (options.title && this.fuzzyMatch(item.trackName, options.title)) {
            score += 0.2;
        }
        return score;
    }
    fuzzyMatch(str1, str2) {
        if (!str1 || !str2)
            return false;
        const clean1 = str1.toLowerCase().replace(/[^a-z0-9]/g, '');
        const clean2 = str2.toLowerCase().replace(/[^a-z0-9]/g, '');
        if (clean1 === clean2)
            return true;
        if (clean1.includes(clean2) || clean2.includes(clean1))
            return true;
        const distance = this.levenshteinDistance(clean1, clean2);
        const maxLength = Math.max(clean1.length, clean2.length);
        const similarity = 1 - (distance / maxLength);
        return similarity > 0.8;
    }
    levenshteinDistance(str1, str2) {
        const matrix = [];
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                }
                else {
                    matrix[i][j] = Math.min(matrix[i - 1][j - 1] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j] + 1);
                }
            }
        }
        return matrix[str2.length][str1.length];
    }
    calculateConfidence(item, options) {
        const matchScore = this.calculateMatchScore(item, options);
        const popularity = (item.popularity || 50) / 100;
        return Math.min(matchScore * 0.8 + popularity * 0.2, 1);
    }
    calculateMusicBrainzConfidence(release, options) {
        let confidence = 0.5;
        const artistCredit = release['artist-credit']?.[0]?.name;
        if (artistCredit && this.fuzzyMatch(artistCredit, options.artist)) {
            confidence += 0.3;
        }
        if (options.album && this.fuzzyMatch(release.title, options.album)) {
            confidence += 0.2;
        }
        return Math.min(confidence, 1);
    }
    calculateItunesConfidence(item, options) {
        return this.calculateItunesMatchScore(item, options);
    }
    async ensureSpotifyToken() {
        if (this.spotifyToken)
            return;
        const basic = Buffer.from(`${process.env.SPOTIFY_CLIENT_ID}:${process.env.SPOTIFY_CLIENT_SECRET}`).toString('base64');
        const response = await fetch('https://accounts.spotify.com/api/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': `Basic ${basic}`
            },
            body: 'grant_type=client_credentials'
        });
        if (!response.ok) {
            throw new Error('Failed to get Spotify token');
        }
        const data = await response.json();
        this.spotifyToken = data.access_token;
    }
}
exports.EnhancedAlbumArtFetcher = EnhancedAlbumArtFetcher;
async function downloadAlbumArtWithRetry(searchResult, artworkManager, artist, album, trackId, maxRetries = 3) {
    let lastError = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const result = await artworkManager.processAlbumArt(searchResult.url, artist, album, trackId);
            if (result.success) {
                return result;
            }
            lastError = new Error(result.error || 'Unknown error');
        }
        catch (error) {
            lastError = error instanceof Error ? error : new Error('Unknown error');
            console.warn(`[AlbumArtFetcher] Download attempt ${attempt} failed:`, lastError.message);
            if (attempt < maxRetries) {
                const delay = Math.pow(2, attempt) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    return {
        success: false,
        paths: { thumbnail: null, medium: null, large: null, original: null },
        hash: null,
        error: lastError?.message || 'All retry attempts failed'
    };
}
//# sourceMappingURL=enhanced-album-art-fetcher-fixed.js.map