{"version": 3, "file": "auto-queue-config-service.js", "sourceRoot": "", "sources": ["../../../lib/services/auto-queue-config-service.ts"], "names": [], "mappings": ";;;;;;AAMA,mEAA0C;AAkH1C,MAAa,sBAAsB;IAKjC,MAAM,CAAC,KAAK,CAAC,oBAAoB;QAC/B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACzC,KAAK,EAAE;oBACL,oBAAoB,EAAE;wBACpB,GAAG,EAAE,IAAI;qBACV;iBACF;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,oBAAoB,EAAE,IAAI;oBAC1B,QAAQ,EAAE,IAAI;oBACd,SAAS,EAAE,IAAI;iBAChB;aACF,CAAC,CAAA;YAKF,MAAM,SAAS,GAA6B,EAAE,CAAA;YAG9C,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAA;YAElD,OAAO,SAAS,CAAA;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAA;YACvE,OAAO,IAAI,CAAC,wBAAwB,EAAE,CAAA;QACxC,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC5C,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAA;YAC5D,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,aAAa,CAAA;YACtB,CAAC;YAID,OAAO,IAAI,CAAA;QAEb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAA;YACtE,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,MAAe;QACjD,IAAI,CAAC;YACH,IAAI,MAAM,EAAE,CAAC;gBAEX,MAAM,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;oBACrB,MAAM,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE;iBACvC,CAAC,CAAA;gBAEF,IAAI,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE,CAAC;oBACrE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;oBACnD,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;wBACzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;wBAChE,IAAI,MAAM;4BAAE,OAAO,MAAM,CAAA;oBAC3B,CAAC;gBACH,CAAC;YACH,CAAC;YAGD,OAAO,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,CAAE,CAAA;QAE9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAA;YAC7E,OAAO,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,CAAE,CAAA;QAC9D,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAuB,EAAE,MAAc;QACpE,IAAI,CAAC;YAIH,MAAM,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACrB,MAAM,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE;aACvC,CAAC,CAAA;YAEF,MAAM,YAAY,GAAG,IAAI,EAAE,oBAAoB;gBAC7C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC;gBACvC,CAAC,CAAC,EAAE,CAAA;YAGN,YAAY,CAAC,aAAa,GAAG,YAAY,CAAC,aAAa,IAAI,EAAE,CAAA;YAC7D,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAA;YAE9C,MAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACvB,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACrB,IAAI,EAAE;oBACJ,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;iBACnD;aACF,CAAC,CAAA;YAEF,OAAO,MAAM,CAAC,EAAE,CAAA;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAA;YACvE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,QAAgB,EAAE,MAAc;QAClE,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACrB,MAAM,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE;aACvC,CAAC,CAAA;YAEF,MAAM,YAAY,GAAG,IAAI,EAAE,oBAAoB;gBAC7C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC;gBACvC,CAAC,CAAC,EAAE,CAAA;YAEN,YAAY,CAAC,cAAc,GAAG,QAAQ,CAAA;YACtC,YAAY,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;YAEvD,MAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACvB,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACrB,IAAI,EAAE;oBACJ,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;iBACnD;aACF,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,iDAAiD,QAAQ,aAAa,MAAM,EAAE,CAAC,CAAA;QAE7F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAA;YAC7E,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,QAAgB;QACpD,IAAI,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,wDAAwD,QAAQ,EAAE,CAAC,CAAA;QAIjF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAA;QACnE,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,wBAAwB;QACrC,OAAO;YACL;gBACE,EAAE,EAAE,sBAAsB;gBAC1B,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,mEAAmE;gBAChF,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,aAAa;gBACxB,aAAa,EAAE,CAAC;gBAChB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,UAAU,EAAE,CAAC;aACd;YACD;gBACE,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,oDAAoD;gBACjE,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,gBAAgB;gBAC3B,aAAa,EAAE,CAAC;gBAChB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,UAAU,EAAE,CAAC;aACd;YACD;gBACE,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,oDAAoD;gBACjE,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,QAAQ;gBACnB,aAAa,EAAE,CAAC;gBAChB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,UAAU,EAAE,CAAC;aACd;YACD;gBACE,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,uCAAuC;gBACpD,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,YAAY;gBACvB,aAAa,EAAE,CAAC;gBAChB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,UAAU,EAAE,CAAC;aACd;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,mDAAmD;gBAChE,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,gBAAgB;gBAC3B,aAAa,EAAE,CAAC;gBAChB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,UAAU,EAAE,CAAC;aACd;SACF,CAAA;IACH,CAAC;IAKO,MAAM,CAAC,uBAAuB,CAAC,QAAgB;QACrD,MAAM,cAAc,GAAoC;YACtD,sBAAsB,EAAE;gBACtB,EAAE,EAAE,sBAAsB;gBAC1B,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,mEAAmE;gBAChF,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;gBAEf,eAAe,EAAE;oBACf,OAAO,EAAE,IAAI;oBACb,mBAAmB,EAAE,CAAC;oBACtB,oBAAoB,EAAE,EAAE;oBACxB,qBAAqB,EAAE,CAAC;oBACxB,iBAAiB,EAAE;wBACjB,OAAO,EAAE,KAAK;wBACd,SAAS,EAAE,EAAE;qBACd;iBACF;gBAED,sBAAsB,EAAE;oBACtB,SAAS,EAAE,aAAa;oBACxB,aAAa,EAAE,CAAC;oBAChB,oBAAoB,EAAE,IAAI;oBAC1B,uBAAuB,EAAE,CAAC;oBAE1B,gBAAgB,EAAE;wBAChB,sBAAsB,EAAE,GAAG;wBAC3B,YAAY,EAAE,IAAI;wBAClB,eAAe,EAAE,GAAG;wBACpB,YAAY,EAAE,IAAI;wBAClB,iBAAiB,EAAE,GAAG;qBACvB;oBAED,cAAc,EAAE;wBACd,aAAa,EAAE,EAAE;wBACjB,aAAa,EAAE,EAAE;wBACjB,gBAAgB,EAAE,EAAE;wBACpB,eAAe,EAAE,KAAK;qBACvB;oBAED,eAAe,EAAE;wBACf,yBAAyB,EAAE,IAAI;wBAC/B,cAAc,EAAE,GAAG;wBACnB,kBAAkB,EAAE,GAAG;qBACxB;iBACF;gBAED,gBAAgB,EAAE;oBAChB,aAAa,EAAE,QAAQ;oBACvB,wBAAwB,EAAE,IAAI;oBAC9B,mBAAmB,EAAE,IAAI;oBAEzB,gBAAgB,EAAE;wBAChB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,SAAS;wBACf,cAAc,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC;qBAC3C;oBAED,gBAAgB,EAAE;wBAChB,OAAO,EAAE,IAAI;wBACb,cAAc,EAAE,IAAI;wBACpB,UAAU,EAAE,IAAI;wBAChB,YAAY,EAAE,GAAG;qBAClB;iBACF;gBAED,oBAAoB,EAAE;oBACpB,eAAe,EAAE,IAAI;oBACrB,WAAW,EAAE,IAAI;oBACjB,yBAAyB,EAAE,IAAI;oBAC/B,YAAY,EAAE,IAAI;iBACnB;gBAED,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,QAAQ;gBACnB,UAAU,EAAE,CAAC;aACd;YAED,mBAAmB,EAAE;gBACnB,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,oDAAoD;gBACjE,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,IAAI;gBAEf,eAAe,EAAE;oBACf,OAAO,EAAE,IAAI;oBACb,mBAAmB,EAAE,CAAC;oBACtB,oBAAoB,EAAE,CAAC;oBACvB,qBAAqB,EAAE,CAAC;oBACxB,iBAAiB,EAAE;wBACjB,OAAO,EAAE,KAAK;wBACd,SAAS,EAAE,EAAE;qBACd;iBACF;gBAED,sBAAsB,EAAE;oBACtB,SAAS,EAAE,gBAAgB;oBAC3B,aAAa,EAAE,CAAC;oBAChB,oBAAoB,EAAE,IAAI;oBAC1B,uBAAuB,EAAE,CAAC;oBAE1B,gBAAgB,EAAE;wBAChB,sBAAsB,EAAE,GAAG;wBAC3B,YAAY,EAAE,GAAG;wBACjB,eAAe,EAAE,GAAG;wBACpB,YAAY,EAAE,IAAI;wBAClB,iBAAiB,EAAE,IAAI;qBACxB;oBAED,cAAc,EAAE;wBACd,aAAa,EAAE,EAAE;wBACjB,aAAa,EAAE,EAAE;wBACjB,gBAAgB,EAAE,EAAE;wBACpB,eAAe,EAAE,KAAK;qBACvB;oBAED,eAAe,EAAE;wBACf,yBAAyB,EAAE,IAAI;wBAC/B,cAAc,EAAE,GAAG;wBACnB,kBAAkB,EAAE,GAAG;qBACxB;iBACF;gBAED,gBAAgB,EAAE;oBAChB,aAAa,EAAE,KAAK;oBACpB,wBAAwB,EAAE,KAAK;oBAC/B,mBAAmB,EAAE,KAAK;oBAE1B,gBAAgB,EAAE;wBAChB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,kBAAkB;wBACxB,cAAc,EAAE,EAAE;qBACnB;oBAED,gBAAgB,EAAE;wBAChB,OAAO,EAAE,KAAK;wBACd,cAAc,EAAE,KAAK;wBACrB,UAAU,EAAE,IAAI;wBAChB,YAAY,EAAE,IAAI;qBACnB;iBACF;gBAED,oBAAoB,EAAE;oBACpB,eAAe,EAAE,IAAI;oBACrB,WAAW,EAAE,IAAI;oBACjB,yBAAyB,EAAE,IAAI;oBAC/B,YAAY,EAAE,KAAK;iBACpB;gBAED,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,QAAQ;gBACnB,UAAU,EAAE,CAAC;aACd;YAED,gBAAgB,EAAE;gBAChB,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,oDAAoD;gBACjE,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,IAAI;gBAEf,eAAe,EAAE;oBACf,OAAO,EAAE,IAAI;oBACb,mBAAmB,EAAE,CAAC;oBACtB,oBAAoB,EAAE,CAAC;oBACvB,qBAAqB,EAAE,CAAC;oBACxB,iBAAiB,EAAE;wBACjB,OAAO,EAAE,KAAK;wBACd,SAAS,EAAE,EAAE;qBACd;iBACF;gBAED,sBAAsB,EAAE;oBACtB,SAAS,EAAE,QAAQ;oBACnB,aAAa,EAAE,CAAC;oBAChB,oBAAoB,EAAE,IAAI;oBAC1B,uBAAuB,EAAE,CAAC;oBAE1B,gBAAgB,EAAE;wBAChB,sBAAsB,EAAE,GAAG;wBAC3B,YAAY,EAAE,IAAI;wBAClB,eAAe,EAAE,IAAI;wBACrB,YAAY,EAAE,GAAG;wBACjB,iBAAiB,EAAE,GAAG;qBACvB;oBAED,cAAc,EAAE;wBACd,aAAa,EAAE,EAAE;wBACjB,aAAa,EAAE,EAAE;wBACjB,gBAAgB,EAAE,EAAE;wBACpB,eAAe,EAAE,KAAK;qBACvB;oBAED,eAAe,EAAE;wBACf,yBAAyB,EAAE,IAAI;wBAC/B,cAAc,EAAE,GAAG;wBACnB,kBAAkB,EAAE,GAAG;qBACxB;iBACF;gBAED,gBAAgB,EAAE;oBAChB,aAAa,EAAE,MAAM;oBACrB,wBAAwB,EAAE,IAAI;oBAC9B,mBAAmB,EAAE,IAAI;oBAEzB,gBAAgB,EAAE;wBAChB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,aAAa;wBACnB,cAAc,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,OAAO,CAAC;qBAChE;oBAED,gBAAgB,EAAE;wBAChB,OAAO,EAAE,IAAI;wBACb,cAAc,EAAE,IAAI;wBACpB,UAAU,EAAE,IAAI;wBAChB,YAAY,EAAE,IAAI;qBACnB;iBACF;gBAED,oBAAoB,EAAE;oBACpB,eAAe,EAAE,IAAI;oBACrB,WAAW,EAAE,IAAI;oBACjB,yBAAyB,EAAE,IAAI;oBAC/B,YAAY,EAAE,IAAI;iBACnB;gBAED,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,QAAQ;gBACnB,UAAU,EAAE,CAAC;aACd;SACF,CAAA;QAED,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAA;IACzC,CAAC;IAKD,MAAM,CAAC,qBAAqB,CAAC,MAAgC;QAC3D,MAAM,MAAM,GAAa,EAAE,CAAA;QAE3B,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QAC/C,CAAC;QAED,IAAI,MAAM,CAAC,sBAAsB,EAAE,CAAC;YAClC,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC,sBAAsB,CAAA;YAEzE,IAAI,aAAa,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,aAAa,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC/D,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAA;YAC1D,CAAC;YAED,IAAI,gBAAgB,EAAE,CAAC;gBACrB,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE,CAAC,CAAC,CAAA;gBAC5F,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;oBACtC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAA;gBAClD,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;YAC3B,MAAM,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,GAAG,MAAM,CAAC,eAAe,CAAA;YAEnG,IAAI,mBAAmB,IAAI,mBAAmB,GAAG,CAAC,EAAE,CAAC;gBACnD,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAA;YACzD,CAAC;YAED,IAAI,oBAAoB,IAAI,oBAAoB,GAAG,CAAC,EAAE,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAA;YACjE,CAAC;YAED,IAAI,qBAAqB,IAAI,qBAAqB,GAAG,CAAC,EAAE,CAAC;gBACvD,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAA;YAC3D,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAA;IACH,CAAC;CACF;AA3gBD,wDA2gBC"}