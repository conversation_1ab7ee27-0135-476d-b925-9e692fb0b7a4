"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FavoritesAnalysisService = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
const session_service_1 = require("./session-service");
class FavoritesAnalysisService {
    static async analyzeConnectedUsersFavorites(sessionType = 'jukebox') {
        try {
            const activeUsers = await session_service_1.UserSessionService.getActiveUsers(sessionType);
            const connectedUserIds = activeUsers.map(user => user.id);
            if (connectedUserIds.length === 0) {
                return this.createEmptyAnalysis(sessionType);
            }
            const favorites = await this.getFavoritesForUsers(connectedUserIds);
            const analysis = await this.performFavoritesAnalysis(connectedUserIds, favorites, sessionType);
            return analysis;
        }
        catch (error) {
            console.error('[FavoritesAnalysis] Analysis failed:', error);
            throw new Error(`Favorites analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async getAutoQueueRecommendations(sessionType = 'jukebox', limit = 10, excludeFilePaths = []) {
        try {
            const analysis = await this.analyzeConnectedUsersFavorites(sessionType);
            const allRecommendations = [
                ...analysis.topSharedFavorites,
                ...analysis.personalizedRecommendations
            ];
            const filteredRecommendations = allRecommendations.filter(rec => !excludeFilePaths.includes(rec.track.filePath));
            return filteredRecommendations
                .sort((a, b) => b.score - a.score)
                .slice(0, limit);
        }
        catch (error) {
            console.error('[FavoritesAnalysis] Auto-queue recommendations failed:', error);
            return [];
        }
    }
    static async getFavoritesForUsers(userIds) {
        const favorites = await prisma_1.default.userFavorite.findMany({
            where: {
                userId: {
                    in: userIds
                }
            },
            include: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        displayName: true
                    }
                }
            },
            orderBy: {
                addedAt: 'desc'
            }
        });
        return favorites.map(fav => ({
            id: fav.id,
            userId: fav.userId,
            songId: fav.songId || undefined,
            filePath: fav.filePath,
            title: fav.title,
            artist: fav.artist,
            album: fav.album || undefined,
            duration: fav.duration || undefined,
            genre: fav.genre || undefined,
            year: fav.year || undefined,
            albumArtUrl: fav.albumArtUrl || undefined,
            addedAt: fav.addedAt
        }));
    }
    static async performFavoritesAnalysis(connectedUserIds, favorites, sessionType) {
        const totalUniqueFavorites = new Set(favorites.map(f => f.filePath)).size;
        const averageFavoritesPerUser = favorites.length / connectedUserIds.length;
        const sharedFavorites = this.findSharedFavorites(favorites);
        const topSharedFavorites = this.scoreSharedFavorites(sharedFavorites, connectedUserIds.length);
        const personalizedRecommendations = this.generatePersonalizedRecommendations(favorites, connectedUserIds);
        const popularGenres = this.analyzeGenres(favorites);
        const popularArtists = this.analyzeArtists(favorites);
        const yearDistribution = this.analyzeYears(favorites);
        const diversityScore = this.calculateDiversityScore(favorites);
        return {
            connectedUsers: connectedUserIds,
            totalConnectedUsers: connectedUserIds.length,
            topSharedFavorites,
            personalizedRecommendations,
            popularGenres,
            popularArtists,
            yearDistribution,
            totalUniqueFavorites,
            averageFavoritesPerUser,
            diversityScore,
            analysisTimestamp: new Date(),
            sessionType
        };
    }
    static findSharedFavorites(favorites) {
        const trackGroups = favorites.reduce((groups, favorite) => {
            const key = `${favorite.artist}-${favorite.title}`.toLowerCase();
            groups[key] = groups[key] || [];
            groups[key].push(favorite);
            return groups;
        }, {});
        const sharedFavorites = new Map();
        Object.entries(trackGroups).forEach(([key, tracks]) => {
            const uniqueUsers = new Set(tracks.map(t => t.userId));
            if (uniqueUsers.size > 1) {
                sharedFavorites.set(key, tracks);
            }
        });
        return sharedFavorites;
    }
    static scoreSharedFavorites(sharedFavorites, totalUsers) {
        const results = [];
        sharedFavorites.forEach((tracks, key) => {
            const uniqueUsers = new Set(tracks.map(t => t.userId));
            const userCount = uniqueUsers.size;
            const userIds = Array.from(uniqueUsers);
            const representativeTrack = tracks.sort((a, b) => b.addedAt.getTime() - a.addedAt.getTime())[0];
            const popularityScore = (userCount / totalUsers) * 100;
            const freshnessScore = this.calculateFreshnessScore(representativeTrack.addedAt);
            const finalScore = (popularityScore * 0.7) + (freshnessScore * 0.3);
            const reasons = [
                `${userCount} connected users have this as favorite`,
                `${Math.round(popularityScore)}% popularity among active users`
            ];
            if (freshnessScore > 50) {
                reasons.push('Recently added to favorites');
            }
            results.push({
                track: representativeTrack,
                score: Math.round(finalScore),
                reasons,
                connectedUserCount: userCount,
                userIds
            });
        });
        return results.sort((a, b) => b.score - a.score);
    }
    static generatePersonalizedRecommendations(favorites, connectedUserIds) {
        const results = [];
        const processedTracks = new Set();
        const artistFrequency = new Map();
        favorites.forEach(fav => {
            if (!artistFrequency.has(fav.artist)) {
                artistFrequency.set(fav.artist, new Set());
            }
            artistFrequency.get(fav.artist).add(fav.userId);
        });
        favorites.forEach(favorite => {
            const trackKey = `${favorite.artist}-${favorite.title}`.toLowerCase();
            if (processedTracks.has(trackKey))
                return;
            const artistUserCount = artistFrequency.get(favorite.artist)?.size || 0;
            if (artistUserCount >= 2) {
                const score = (artistUserCount / connectedUserIds.length) * 80;
                const freshnessScore = this.calculateFreshnessScore(favorite.addedAt);
                const finalScore = (score * 0.8) + (freshnessScore * 0.2);
                const reasons = [
                    `${favorite.artist} is popular among ${artistUserCount} connected users`,
                    'Similar to other favorites in the group'
                ];
                if (favorite.genre) {
                    reasons.push(`${favorite.genre} genre`);
                }
                results.push({
                    track: favorite,
                    score: Math.round(finalScore),
                    reasons,
                    connectedUserCount: 1,
                    userIds: [favorite.userId]
                });
                processedTracks.add(trackKey);
            }
        });
        return results.sort((a, b) => b.score - a.score).slice(0, 20);
    }
    static analyzeGenres(favorites) {
        const genreMap = new Map();
        favorites.forEach(fav => {
            const genre = fav.genre || 'Unknown';
            if (!genreMap.has(genre)) {
                genreMap.set(genre, { users: new Set(), tracks: new Set() });
            }
            genreMap.get(genre).users.add(fav.userId);
            genreMap.get(genre).tracks.add(fav.title);
        });
        const total = favorites.length;
        const results = [];
        genreMap.forEach((data, genre) => {
            results.push({
                genre,
                count: data.tracks.size,
                userCount: data.users.size,
                percentage: (data.tracks.size / total) * 100,
                tracks: Array.from(data.tracks).slice(0, 3)
            });
        });
        return results.sort((a, b) => b.count - a.count).slice(0, 10);
    }
    static analyzeArtists(favorites) {
        const artistMap = new Map();
        favorites.forEach(fav => {
            if (!artistMap.has(fav.artist)) {
                artistMap.set(fav.artist, { users: new Set(), tracks: new Set() });
            }
            artistMap.get(fav.artist).users.add(fav.userId);
            artistMap.get(fav.artist).tracks.add(fav.title);
        });
        const total = favorites.length;
        const results = [];
        artistMap.forEach((data, artist) => {
            results.push({
                artist,
                count: data.tracks.size,
                userCount: data.users.size,
                percentage: (data.tracks.size / total) * 100,
                topTracks: Array.from(data.tracks).slice(0, 3)
            });
        });
        return results.sort((a, b) => b.count - a.count).slice(0, 15);
    }
    static analyzeYears(favorites) {
        const decadeMap = new Map();
        favorites.forEach(fav => {
            if (fav.year) {
                const decade = `${Math.floor(fav.year / 10) * 10}s`;
                decadeMap.set(decade, (decadeMap.get(decade) || 0) + 1);
            }
        });
        const total = favorites.filter(f => f.year).length;
        const results = [];
        decadeMap.forEach((count, decade) => {
            results.push({
                decade,
                count,
                percentage: (count / total) * 100
            });
        });
        return results.sort((a, b) => b.count - a.count);
    }
    static calculateFreshnessScore(addedAt) {
        const daysSinceAdded = (Date.now() - addedAt.getTime()) / (1000 * 60 * 60 * 24);
        if (daysSinceAdded <= 1)
            return 100;
        if (daysSinceAdded <= 7)
            return 80;
        if (daysSinceAdded <= 30)
            return 60;
        if (daysSinceAdded <= 90)
            return 40;
        return 20;
    }
    static calculateDiversityScore(favorites) {
        if (favorites.length === 0)
            return 0;
        const uniqueArtists = new Set(favorites.map(f => f.artist)).size;
        const uniqueGenres = new Set(favorites.map(f => f.genre || 'Unknown')).size;
        const uniqueYears = new Set(favorites.map(f => f.year).filter(Boolean)).size;
        const artistDiversity = Math.min(uniqueArtists / favorites.length, 0.5) * 2;
        const genreDiversity = Math.min(uniqueGenres / 10, 1);
        const yearDiversity = Math.min(uniqueYears / 20, 1);
        return (artistDiversity + genreDiversity + yearDiversity) / 3;
    }
    static createEmptyAnalysis(sessionType) {
        return {
            connectedUsers: [],
            totalConnectedUsers: 0,
            topSharedFavorites: [],
            personalizedRecommendations: [],
            popularGenres: [],
            popularArtists: [],
            yearDistribution: [],
            totalUniqueFavorites: 0,
            averageFavoritesPerUser: 0,
            diversityScore: 0,
            analysisTimestamp: new Date(),
            sessionType
        };
    }
    static async getQuickStats(sessionType = 'jukebox') {
        try {
            const activeUsers = await session_service_1.UserSessionService.getActiveUsers(sessionType);
            const connectedUserIds = activeUsers.map(user => user.id);
            if (connectedUserIds.length === 0) {
                return {
                    connectedUsers: 0,
                    totalFavorites: 0,
                    uniqueTracks: 0,
                    topGenres: [],
                    topArtists: []
                };
            }
            const favorites = await prisma_1.default.userFavorite.findMany({
                where: {
                    userId: { in: connectedUserIds }
                },
                select: {
                    filePath: true,
                    genre: true,
                    artist: true
                }
            });
            const uniqueTracks = new Set(favorites.map(f => f.filePath)).size;
            const genreCounts = new Map();
            favorites.forEach(f => {
                const genre = f.genre || 'Unknown';
                genreCounts.set(genre, (genreCounts.get(genre) || 0) + 1);
            });
            const topGenres = Array.from(genreCounts.entries())
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5)
                .map(([genre, count]) => ({ genre, count }));
            const artistCounts = new Map();
            favorites.forEach(f => {
                artistCounts.set(f.artist, (artistCounts.get(f.artist) || 0) + 1);
            });
            const topArtists = Array.from(artistCounts.entries())
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5)
                .map(([artist, count]) => ({ artist, count }));
            return {
                connectedUsers: connectedUserIds.length,
                totalFavorites: favorites.length,
                uniqueTracks,
                topGenres,
                topArtists
            };
        }
        catch (error) {
            console.error('[FavoritesAnalysis] Quick stats failed:', error);
            return {
                connectedUsers: 0,
                totalFavorites: 0,
                uniqueTracks: 0,
                topGenres: [],
                topArtists: []
            };
        }
    }
}
exports.FavoritesAnalysisService = FavoritesAnalysisService;
//# sourceMappingURL=favorites-analysis-service.js.map