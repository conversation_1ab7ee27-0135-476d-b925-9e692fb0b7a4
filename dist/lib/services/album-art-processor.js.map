{"version": 3, "file": "album-art-processor.js", "sourceRoot": "", "sources": ["../../../lib/services/album-art-processor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2BAAmC;AACnC,gDAAuB;AACvB,oDAA2B;AAC3B,kDAAyB;AACzB,mEAA0C;AAe1C,MAAa,iBAAiB;IAa5B,MAAM,CAAC,KAAK,CAAC,UAAU;QACrB,MAAM,aAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAClD,MAAM,aAAE,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QACxD,MAAM,aAAE,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QACpD,MAAM,aAAE,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;IACzD,CAAC;IAKD,MAAM,CAAC,YAAY,CAAC,MAAc,EAAE,KAAa;QAC/C,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,EAAE,CAAA;QAC1E,OAAO,gBAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAC3D,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe,CAC1B,OAAe,EACf,MAAc,EACd,KAAa,EACb,SAAkB;QAElB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;YAEvB,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAC7C,MAAM,QAAQ,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,CAAA;YAGzG,MAAM,aAAa,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAC,SAAS,CAAC;gBACrD,KAAK,EAAE;oBACL,YAAY,EAAE,IAAI;oBAClB,iBAAiB,EAAE,IAAI;iBACxB;aACF,CAAC,CAAA;YAEF,IAAI,aAAa,EAAE,sBAAsB,EAAE,CAAC;gBAE1C,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE;oBAC1C,SAAS,EAAE,aAAa,CAAC,sBAAsB;oBAC/C,KAAK,EAAE,aAAa,CAAC,kBAAmB;oBACxC,QAAQ,EAAE,aAAa,CAAC,qBAAsB;iBAC/C,EAAE,IAAI,CAAC,CAAA;gBAER,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,KAAK,EAAE;wBACL,SAAS,EAAE,aAAa,CAAC,sBAAsB;wBAC/C,KAAK,EAAE,aAAa,CAAC,kBAAmB;wBACxC,QAAQ,EAAE,aAAa,CAAC,qBAAsB;qBAC/C;oBACD,IAAI;iBACL,CAAA;YACH,CAAC;YAGD,IAAI,WAAmB,CAAA;YAEvB,IAAI,SAAS,EAAE,CAAC;gBACd,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;YACnD,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAA;YAC3D,CAAC;YAED,IAAI,CAAC,WAAW,EAAE,CAAC;gBAEjB,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;YAChD,CAAC;YAGD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAA;YAGpE,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;YAExD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,KAAK;gBACL,IAAI;aACL,CAAA;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;YACpD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAChE,CAAA;QACH,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,GAAW;QAC5C,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;YAChC,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,YAAY,EAAE,wCAAwC;aACvD;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,6BAA6B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;QACjE,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAA;IAClD,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,OAAe;QAC1D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAC,UAAU,CAAC;gBAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;gBACtB,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;aAC9B,CAAC,CAAA;YAEF,IAAI,CAAC,KAAK,EAAE,WAAW;gBAAE,OAAO,IAAI,CAAA;YAGpC,MAAM,EAAE,IAAI,EAAE,GAAG,wDAAa,eAAe,GAAC,CAAA;YAC9C,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,MAAM,GAAC,CAAA;YAC1C,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAA;YAEjC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,OAAO,MAAM,CAAC,CAAA;YAC7D,MAAM,OAAO,GAAG,cAAc,KAAK,CAAC,WAAW,uBAAuB,QAAQ,MAAM,CAAA;YAEpF,MAAM,SAAS,CAAC,OAAO,CAAC,CAAA;YACxB,MAAM,MAAM,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;YAC1C,MAAM,aAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA;YAEzC,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAA;YAC9D,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,mBAAmB;QAEtC,OAAO,MAAM,IAAA,eAAK,EAAC;YACjB,MAAM,EAAE;gBACN,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,CAAC;gBACX,UAAU,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;aACvC;SACF,CAAC;aACD,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;aAC/B,QAAQ,EAAE,CAAA;IACb,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,oBAAoB,CACvC,WAAmB,EACnB,QAAgB;QAEhB,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,QAAQ,MAAM,CAAC,CAAA;QACrE,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAA;QAChE,MAAM,iBAAiB,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,QAAQ,MAAM,CAAC,CAAA;QACvE,MAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAA;QACxE,MAAM,qBAAqB,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,QAAQ,MAAM,CAAC,CAAA;QAG/E,MAAM,aAAE,CAAC,SAAS,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA;QAG7C,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC;aACrB,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YACxC,GAAG,EAAE,OAAO;YACZ,QAAQ,EAAE,QAAQ;SACnB,CAAC;aACD,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;aAC/B,MAAM,CAAC,SAAS,CAAC,CAAA;QAGpB,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC;aACrB,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YACxC,GAAG,EAAE,OAAO;YACZ,QAAQ,EAAE,QAAQ;SACnB,CAAC;aACD,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;aAC/B,MAAM,CAAC,iBAAiB,CAAC,CAAA;QAG5B,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC;aACrB,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YAChD,GAAG,EAAE,OAAO;YACZ,QAAQ,EAAE,QAAQ;SACnB,CAAC;aACD,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;aAC/B,MAAM,CAAC,aAAa,CAAC,CAAA;QAGxB,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC;aACrB,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YAChD,GAAG,EAAE,OAAO;YACZ,QAAQ,EAAE,QAAQ;SACnB,CAAC;aACD,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;aAC/B,MAAM,CAAC,qBAAqB,CAAC,CAAA;QAEhC,OAAO;YACL,SAAS,EAAE,yBAAyB,QAAQ,OAAO;YACnD,KAAK,EAAE,qBAAqB,QAAQ,OAAO;YAC3C,QAAQ,EAAE,wBAAwB,QAAQ,MAAM;SACjD,CAAA;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAC1C,OAAe,EACf,KAAmB,EACnB,IAAY;QAEZ,MAAM,gBAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5B,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;YACtB,IAAI,EAAE;gBACJ,sBAAsB,EAAE,KAAK,CAAC,SAAS;gBACvC,kBAAkB,EAAE,KAAK,CAAC,KAAK;gBAC/B,qBAAqB,EAAE,KAAK,CAAC,QAAQ;gBACrC,YAAY,EAAE,IAAI;gBAClB,iBAAiB,EAAE,IAAI;gBACvB,mBAAmB,EAAE,IAAI,IAAI,EAAE;aAChC;SACF,CAAC,CAAA;IACJ,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAC/B,QAAgB,EAAE,EAClB,UAAuD;QAGvD,MAAM,oBAAoB,GAAG,MAAM,gBAAM,CAAC,eAAe,CAMvD;;;kBAGY,CACb,CAAC;QAEF,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAE/D,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,CAAA;QACtC,OAAO,CAAC,GAAG,CAAC,4BAA4B,KAAK,YAAY,CAAC,CAAA;QAE1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,MAAM,KAAK,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAA;YAElC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,eAAe,CACxB,KAAK,CAAC,EAAE,EACR,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,WAAW,IAAI,SAAS,CAC/B,CAAA;gBAED,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;gBAC1B,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;gBAG5E,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAA;YAExD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAA;YAC3F,CAAC;QACH,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,OAAO;QAClB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA;QAG5C,MAAM,UAAU,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACjD,KAAK,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE;YAClC,MAAM,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;YAC9B,QAAQ,EAAE,CAAC,cAAc,CAAC;SAC3B,CAAC,CAAA;QAEF,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAA;QAGhF,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;QAEvE,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,MAAM,aAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;YAEnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAA;gBAE/C,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;oBACrC,MAAM,aAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;oBACzB,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,EAAE,CAAC,CAAA;gBAClD,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,uBAAuB,CAAC,QAAgB;QACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAA;QAC5D,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAChC,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAe;QACtC,MAAM,KAAK,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;YACtB,MAAM,EAAE;gBACN,sBAAsB,EAAE,IAAI;gBAC5B,kBAAkB,EAAE,IAAI;gBACxB,qBAAqB,EAAE,IAAI;gBAC3B,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,IAAI;aACZ;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAA;QAGvB,IAAI,KAAK,CAAC,sBAAsB,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,qBAAqB,EAAE,CAAC;YAC5F,OAAO;gBACL,SAAS,EAAE,KAAK,CAAC,sBAAsB;gBACvC,KAAK,EAAE,KAAK,CAAC,kBAAkB;gBAC/B,QAAQ,EAAE,KAAK,CAAC,qBAAqB;aACtC,CAAA;QACH,CAAC;QAGD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CACvC,OAAO,EACP,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,WAAW,IAAI,SAAS,CAC/B,CAAA;QAED,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAA;IACrD,CAAC;;AAxXH,8CAyXC;;AAxXyB,0BAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,WAAW,CAAC,AAAlD,CAAkD;AAC1D,gCAAc,GAAG,cAAI,CAAC,IAAI,CAAC,EAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,AAAzC,CAAyC;AACvD,4BAAU,GAAG,cAAI,CAAC,IAAI,CAAC,EAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,AAArC,CAAqC;AAC/C,+BAAa,GAAG,cAAI,CAAC,IAAI,CAAC,EAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,AAAxC,CAAwC;AAErD,gCAAc,GAAG,GAAG,AAAN,CAAM;AACpB,4BAAU,GAAG,GAAG,AAAN,CAAM;AAChB,yBAAO,GAAG,EAAE,AAAL,CAAK"}