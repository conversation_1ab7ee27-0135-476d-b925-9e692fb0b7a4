"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSessionService = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
class UserSessionService {
    static async startSession(userId, sessionType = 'jukebox', ipAddress, userAgent, metadata = {}) {
        try {
            await this.endActiveSessions(userId, sessionType);
            const session = await prisma_1.default.userSession.create({
                data: {
                    userId,
                    sessionType,
                    ipAddress,
                    userAgent,
                    metadata: JSON.stringify(metadata),
                    isActive: true,
                    connectedAt: new Date(),
                    lastActive: new Date()
                }
            });
            await prisma_1.default.user.update({
                where: { id: userId },
                data: { lastActive: new Date() }
            });
            console.log(`[SessionService] Started ${sessionType} session for user ${userId}`);
            return session;
        }
        catch (error) {
            console.error('[SessionService] Failed to start session:', error);
            throw new Error('Failed to start user session');
        }
    }
    static async updateActivity(sessionId, metadata = {}) {
        try {
            const session = await prisma_1.default.userSession.findUnique({
                where: { id: sessionId }
            });
            if (!session || !session.isActive) {
                throw new Error('Session not found or inactive');
            }
            const existingMetadata = JSON.parse(session.metadata || '{}');
            const updatedMetadata = { ...existingMetadata, ...metadata, lastHeartbeat: new Date().toISOString() };
            await prisma_1.default.userSession.update({
                where: { id: sessionId },
                data: {
                    lastActive: new Date(),
                    metadata: JSON.stringify(updatedMetadata)
                }
            });
            await prisma_1.default.user.update({
                where: { id: session.userId },
                data: { lastActive: new Date() }
            });
        }
        catch (error) {
            console.error('[SessionService] Failed to update activity:', error);
            throw new Error('Failed to update session activity');
        }
    }
    static async endSession(sessionId) {
        try {
            await prisma_1.default.userSession.update({
                where: { id: sessionId },
                data: {
                    isActive: false,
                    disconnectedAt: new Date()
                }
            });
            console.log(`[SessionService] Ended session ${sessionId}`);
        }
        catch (error) {
            console.error('[SessionService] Failed to end session:', error);
            throw new Error('Failed to end session');
        }
    }
    static async endActiveSessions(userId, sessionType) {
        try {
            const whereClause = {
                userId,
                isActive: true
            };
            if (sessionType) {
                whereClause.sessionType = sessionType;
            }
            await prisma_1.default.userSession.updateMany({
                where: whereClause,
                data: {
                    isActive: false,
                    disconnectedAt: new Date()
                }
            });
            console.log(`[SessionService] Ended active sessions for user ${userId}`);
        }
        catch (error) {
            console.error('[SessionService] Failed to end active sessions:', error);
            throw new Error('Failed to end active sessions');
        }
    }
    static async getActiveUsers(sessionType = 'jukebox', includeRecentlyDisconnected = false) {
        try {
            const cutoffTime = new Date();
            cutoffTime.setMinutes(cutoffTime.getMinutes() - this.SESSION_TIMEOUT_MINUTES);
            const sessionWhere = {
                sessionType,
                lastActive: { gte: cutoffTime }
            };
            if (!includeRecentlyDisconnected) {
                sessionWhere.isActive = true;
            }
            const users = await prisma_1.default.user.findMany({
                where: {
                    userSessions: {
                        some: sessionWhere
                    }
                },
                include: {
                    userSessions: {
                        where: sessionWhere,
                        orderBy: { lastActive: 'desc' },
                        take: 1
                    }
                }
            });
            const activeUsers = users.map(user => {
                const currentSession = user.userSessions[0];
                const lastSeenMinutesAgo = currentSession
                    ? Math.floor((Date.now() - currentSession.lastActive.getTime()) / (1000 * 60))
                    : 999;
                return {
                    ...user,
                    currentSession,
                    lastSeenMinutesAgo
                };
            });
            return activeUsers.sort((a, b) => a.lastSeenMinutesAgo - b.lastSeenMinutesAgo);
        }
        catch (error) {
            console.error('[SessionService] Failed to get active users:', error);
            throw new Error('Failed to get active users');
        }
    }
    static async getSessionStats(sessionType) {
        try {
            const cutoffTime = new Date();
            cutoffTime.setMinutes(cutoffTime.getMinutes() - this.SESSION_TIMEOUT_MINUTES);
            const whereClause = {};
            if (sessionType) {
                whereClause.sessionType = sessionType;
            }
            const [activeCount, totalSessions, recentSessions] = await Promise.all([
                prisma_1.default.userSession.count({
                    where: {
                        ...whereClause,
                        isActive: true,
                        lastActive: { gte: cutoffTime }
                    }
                }),
                prisma_1.default.userSession.count({
                    where: whereClause
                }),
                prisma_1.default.userSession.findMany({
                    where: {
                        ...whereClause,
                        disconnectedAt: { not: null },
                        connectedAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
                    },
                    select: {
                        connectedAt: true,
                        disconnectedAt: true
                    }
                })
            ]);
            const durations = recentSessions
                .filter(s => s.disconnectedAt)
                .map(s => s.disconnectedAt.getTime() - s.connectedAt.getTime());
            const averageSessionDuration = durations.length > 0
                ? durations.reduce((a, b) => a + b, 0) / durations.length / (1000 * 60)
                : 0;
            const recentCutoff = new Date();
            recentCutoff.setHours(recentCutoff.getHours() - 1);
            const recentlyActive = await prisma_1.default.userSession.count({
                where: {
                    ...whereClause,
                    lastActive: { gte: recentCutoff }
                }
            });
            return {
                activeCount,
                totalSessions,
                averageSessionDuration: Math.round(averageSessionDuration),
                recentlyActive
            };
        }
        catch (error) {
            console.error('[SessionService] Failed to get session stats:', error);
            throw new Error('Failed to get session statistics');
        }
    }
    static async cleanupOldSessions() {
        try {
            const cleanupCutoff = new Date();
            cleanupCutoff.setHours(cleanupCutoff.getHours() - 24);
            const result = await prisma_1.default.userSession.deleteMany({
                where: {
                    isActive: false,
                    disconnectedAt: { lt: cleanupCutoff }
                }
            });
            console.log(`[SessionService] Cleaned up ${result.count} old sessions`);
            return result.count;
        }
        catch (error) {
            console.error('[SessionService] Failed to cleanup sessions:', error);
            return 0;
        }
    }
    static async markTimeoutSessions() {
        try {
            const timeoutCutoff = new Date();
            timeoutCutoff.setMinutes(timeoutCutoff.getMinutes() - this.SESSION_TIMEOUT_MINUTES);
            const result = await prisma_1.default.userSession.updateMany({
                where: {
                    isActive: true,
                    lastActive: { lt: timeoutCutoff }
                },
                data: {
                    isActive: false,
                    disconnectedAt: new Date()
                }
            });
            if (result.count > 0) {
                console.log(`[SessionService] Marked ${result.count} sessions as timed out`);
            }
            return result.count;
        }
        catch (error) {
            console.error('[SessionService] Failed to mark timeout sessions:', error);
            return 0;
        }
    }
}
exports.UserSessionService = UserSessionService;
UserSessionService.SESSION_TIMEOUT_MINUTES = 30;
UserSessionService.HEARTBEAT_INTERVAL_MINUTES = 5;
//# sourceMappingURL=session-service.js.map