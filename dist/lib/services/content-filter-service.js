"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentFilterService = void 0;
const filters_1 = require("@/lib/types/filters");
class ContentFilterService {
    constructor() {
        this.currentFilters = filters_1.DEFAULT_FILTERS;
        this.filterSettings = {
            enabled: false,
            applyToAllModes: true
        };
        this.SPECIAL_CATEGORIES = ['all-time-favorites', 'MyItunes'];
    }
    static getInstance() {
        if (!ContentFilterService.instance) {
            ContentFilterService.instance = new ContentFilterService();
        }
        return ContentFilterService.instance;
    }
    loadSettings() {
        if (typeof window === 'undefined' || typeof document === 'undefined')
            return this.filterSettings;
        try {
            const stored = (typeof window !== 'undefined' && window.localStorage) ? localStorage.getItem('gameFilterSettings') : null;
            if (stored) {
                this.filterSettings = JSON.parse(stored);
                if (this.filterSettings.customFilters) {
                    this.currentFilters = this.filterSettings.customFilters;
                }
                else if (this.filterSettings.activePresetId) {
                    const preset = this.getPreset(this.filterSettings.activePresetId);
                    if (preset) {
                        this.currentFilters = preset.filters;
                    }
                }
            }
        }
        catch (error) {
            console.error('Failed to load filter settings:', error);
        }
        return this.filterSettings;
    }
    saveSettings(settings) {
        this.filterSettings = settings;
        if (typeof window !== 'undefined' && typeof document !== 'undefined') {
            localStorage.setItem('gameFilterSettings', JSON.stringify(settings));
        }
    }
    applyPreset(presetId) {
        const preset = this.getPreset(presetId);
        if (preset) {
            this.currentFilters = preset.filters;
            this.filterSettings.activePresetId = presetId;
            this.filterSettings.customFilters = undefined;
            this.saveSettings(this.filterSettings);
        }
    }
    applyCustomFilters(filters) {
        this.currentFilters = filters;
        this.filterSettings.customFilters = filters;
        this.filterSettings.activePresetId = undefined;
        this.saveSettings(this.filterSettings);
    }
    getPreset(id) {
        return filters_1.FILTER_PRESETS.find(preset => preset.id === id);
    }
    getPresets() {
        return filters_1.FILTER_PRESETS;
    }
    isFilteringEnabled() {
        return this.filterSettings.enabled;
    }
    setFilteringEnabled(enabled) {
        this.filterSettings.enabled = enabled;
        this.saveSettings(this.filterSettings);
    }
    getCurrentFilters() {
        return this.currentFilters;
    }
    getFiltersForMode(gameMode) {
        if (!this.filterSettings.applyToAllModes && this.filterSettings.modeSpecificFilters?.[gameMode]) {
            return this.filterSettings.modeSpecificFilters[gameMode];
        }
        return this.currentFilters;
    }
    filterTracks(tracks, gameMode) {
        if (!this.filterSettings.enabled) {
            return tracks;
        }
        const filters = gameMode ? this.getFiltersForMode(gameMode) : this.currentFilters;
        return tracks.filter(track => this.trackMatchesFilters(track, filters));
    }
    trackMatchesFilters(track, filters) {
        if (filters.genres.values.length > 0) {
            const specialCategories = filters.genres.values.filter(g => this.SPECIAL_CATEGORIES.includes(g));
            const regularGenres = filters.genres.values.filter(g => !this.SPECIAL_CATEGORIES.includes(g));
            let matches = false;
            if (regularGenres.length > 0 && track.genre) {
                matches = regularGenres.some(genre => track.genre?.toLowerCase().includes(genre.toLowerCase()));
            }
            if (specialCategories.length > 0) {
                if (specialCategories.includes('MyItunes') && track.mpdFilePath?.startsWith('MyItunes/')) {
                    matches = true;
                }
                if (specialCategories.includes('all-time-favorites') && track.quizCategories) {
                    try {
                        const categories = JSON.parse(track.quizCategories);
                        if (Array.isArray(categories) && categories.includes('all-time-favorites')) {
                            matches = true;
                        }
                    }
                    catch (e) {
                    }
                }
            }
            if (filters.genres.mode === 'include' && !matches)
                return false;
            if (filters.genres.mode === 'exclude' && matches)
                return false;
        }
        if (filters.yearRange.enabled) {
            if (!track.year)
                return false;
            if (filters.yearRange.min && track.year < filters.yearRange.min)
                return false;
            if (filters.yearRange.max && track.year > filters.yearRange.max)
                return false;
        }
        if (!filters.charts.includeChartMusic && track.chartPosition)
            return false;
        if (!filters.charts.includeNonChartMusic && !track.chartPosition)
            return false;
        if (filters.charts.countries?.length && track.chartPosition) {
            if (!filters.charts.countries.includes(track.chartCountry))
                return false;
        }
        if (filters.quality.minDifficulty && track.difficultyRating < filters.quality.minDifficulty)
            return false;
        if (filters.quality.maxDifficulty && track.difficultyRating > filters.quality.maxDifficulty)
            return false;
        if (filters.quality.minPopularity && track.popularityScore < filters.quality.minPopularity)
            return false;
        if (filters.quality.requireAlbumArt && !track.albumArtUrl)
            return false;
        if (filters.metadata.requireYear && !track.year)
            return false;
        if (filters.metadata.requireGenre && !track.genre)
            return false;
        if (filters.metadata.requireAlbum && !track.album)
            return false;
        if (filters.metadata.excludeCompilations) {
            const albumLower = track.album?.toLowerCase() || '';
            const titleLower = track.title?.toLowerCase() || '';
            if (albumLower.includes('compilation') || albumLower.includes('greatest hits') ||
                albumLower.includes('best of') || titleLower.includes('compilation'))
                return false;
        }
        if (filters.metadata.excludeLiveRecordings && track.title?.toLowerCase().includes('live'))
            return false;
        if (filters.metadata.excludeRemixes && track.title?.toLowerCase().includes('remix'))
            return false;
        if (!filters.sources.includeMyItunes && track.mpdFilePath?.startsWith('MyItunes/'))
            return false;
        if (!filters.sources.includeSharedLibrary && !track.mpdFilePath?.startsWith('MyItunes/'))
            return false;
        if (filters.folders?.values.length > 0) {
            let trackFolders = [];
            if (track.mpdFilePath) {
                const firstPathSegment = track.mpdFilePath.split('/')[0];
                if (firstPathSegment) {
                    trackFolders.push(firstPathSegment);
                }
            }
            if (track.quizCategories) {
                try {
                    const categories = JSON.parse(track.quizCategories);
                    categories.forEach(cat => {
                        if (cat && !trackFolders.includes(cat)) {
                            trackFolders.push(cat);
                        }
                    });
                }
                catch {
                }
            }
            const matches = filters.folders.values.some(filterFolder => trackFolders.some(trackFolder => trackFolder.toLowerCase() === filterFolder.toLowerCase()));
            if (filters.folders.mode === 'include' && !matches)
                return false;
            if (filters.folders.mode === 'exclude' && matches)
                return false;
        }
        return true;
    }
    async getFilterStatistics(totalTracks) {
        return {
            totalTracks,
            filteredTracks: totalTracks,
            percentageRemaining: 100,
            removedByFilter: {}
        };
    }
    exportFilters() {
        return JSON.stringify({
            settings: this.filterSettings,
            filters: this.currentFilters
        }, null, 2);
    }
    importFilters(jsonString) {
        try {
            const data = JSON.parse(jsonString);
            if (data.settings) {
                this.filterSettings = data.settings;
            }
            if (data.filters) {
                this.currentFilters = data.filters;
            }
            this.saveSettings(this.filterSettings);
            return true;
        }
        catch (error) {
            console.error('Failed to import filters:', error);
            return false;
        }
    }
}
exports.ContentFilterService = ContentFilterService;
//# sourceMappingURL=content-filter-service.js.map