export type AlbumArtAvailability = {
    hasLocal: boolean;
    hasUrl: boolean;
    isProcessed: boolean;
    needsProcessing: boolean;
    localPaths: {
        thumbnail?: string;
        cover?: string;
        original?: string;
    };
    lastProcessedAt?: Date;
};
export type AlbumArtSyncStatus = {
    totalTracks: number;
    hasLocalArt: number;
    hasUrl: number;
    isProcessed: number;
    needsProcessing: number;
    orphanedFiles: string[];
    missingFiles: string[];
};
export declare class AlbumArtStatusService {
    private static readonly MEDIA_BASE;
    private static readonly ALBUM_ART_DIR;
    private static availabilityCache;
    private static cacheExpiry;
    private static readonly CACHE_TTL;
    static checkAvailability(trackId: string): Promise<AlbumArtAvailability>;
    static getSyncStatus(): Promise<AlbumArtSyncStatus>;
    static getTracksNeedingProcessing(limit?: number): Promise<Array<{
        id: string;
        artist: string;
        album: string;
        albumArtUrl: string | null;
    }>>;
    static markAsProcessed(trackId: string, localPaths: {
        thumbnail?: string;
        cover?: string;
        original?: string;
    }): Promise<void>;
    static markAsFailed(trackId: string, reason?: string): Promise<void>;
    static resetProcessingStatus(trackIds?: string[]): Promise<number>;
    static getImageUrl(trackId: string, size?: 'thumbnail' | 'cover' | 'original'): Promise<string | null>;
    private static fileExists;
    private static cacheResult;
    private static clearCache;
    private static findOrphanedAndMissingFiles;
}
