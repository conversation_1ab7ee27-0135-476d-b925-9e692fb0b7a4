"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlbumArtStatusService = void 0;
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
class AlbumArtStatusService {
    static async checkAvailability(trackId) {
        const now = Date.now();
        if (this.availabilityCache.has(trackId)) {
            const expiry = this.cacheExpiry.get(trackId);
            if (expiry && now < expiry) {
                return this.availabilityCache.get(trackId);
            }
        }
        try {
            const track = await prisma_1.default.quizTrack.findUnique({
                where: { id: trackId },
                select: {
                    localAlbumArtThumbnail: true,
                    localAlbumArtCover: true,
                    localAlbumArtOriginal: true,
                    albumArtUrl: true,
                    albumArtProcessed: true,
                    albumArtProcessedAt: true,
                    artist: true,
                    album: true
                }
            });
            if (!track) {
                const result = {
                    hasLocal: false,
                    hasUrl: false,
                    isProcessed: false,
                    needsProcessing: false,
                    localPaths: {}
                };
                this.cacheResult(trackId, result);
                return result;
            }
            const localPaths = {};
            let hasLocal = false;
            if (track.localAlbumArtThumbnail) {
                const fullPath = path_1.default.join(this.MEDIA_BASE, track.localAlbumArtThumbnail);
                if (await this.fileExists(fullPath)) {
                    localPaths.thumbnail = track.localAlbumArtThumbnail;
                    hasLocal = true;
                }
            }
            if (track.localAlbumArtCover) {
                const fullPath = path_1.default.join(this.MEDIA_BASE, track.localAlbumArtCover);
                if (await this.fileExists(fullPath)) {
                    localPaths.cover = track.localAlbumArtCover;
                    hasLocal = true;
                }
            }
            if (track.localAlbumArtOriginal) {
                const fullPath = path_1.default.join(this.MEDIA_BASE, track.localAlbumArtOriginal);
                if (await this.fileExists(fullPath)) {
                    localPaths.original = track.localAlbumArtOriginal;
                    hasLocal = true;
                }
            }
            const hasUrl = !!track.albumArtUrl;
            const isProcessed = track.albumArtProcessed;
            const needsProcessing = !isProcessed && hasUrl && !hasLocal;
            const result = {
                hasLocal,
                hasUrl,
                isProcessed,
                needsProcessing,
                localPaths,
                lastProcessedAt: track.albumArtProcessedAt || undefined
            };
            this.cacheResult(trackId, result);
            return result;
        }
        catch (error) {
            console.error('Error checking album art availability:', error);
            const result = {
                hasLocal: false,
                hasUrl: false,
                isProcessed: false,
                needsProcessing: false,
                localPaths: {}
            };
            this.cacheResult(trackId, result);
            return result;
        }
    }
    static async getSyncStatus() {
        try {
            const totalTracks = await prisma_1.default.quizTrack.count();
            const hasLocalArt = await prisma_1.default.quizTrack.count({
                where: {
                    OR: [
                        { localAlbumArtThumbnail: { not: null } },
                        { localAlbumArtCover: { not: null } },
                        { localAlbumArtOriginal: { not: null } }
                    ]
                }
            });
            const hasUrl = await prisma_1.default.quizTrack.count({
                where: { albumArtUrl: { not: null } }
            });
            const isProcessed = await prisma_1.default.quizTrack.count({
                where: { albumArtProcessed: true }
            });
            const needsProcessing = await prisma_1.default.quizTrack.count({
                where: {
                    albumArtProcessed: false,
                    albumArtUrl: { not: null },
                    AND: [
                        { localAlbumArtThumbnail: null },
                        { localAlbumArtCover: null },
                        { localAlbumArtOriginal: null }
                    ]
                }
            });
            const { orphanedFiles, missingFiles } = await this.findOrphanedAndMissingFiles();
            return {
                totalTracks,
                hasLocalArt,
                hasUrl,
                isProcessed,
                needsProcessing,
                orphanedFiles,
                missingFiles
            };
        }
        catch (error) {
            console.error('Error getting sync status:', error);
            return {
                totalTracks: 0,
                hasLocalArt: 0,
                hasUrl: 0,
                isProcessed: 0,
                needsProcessing: 0,
                orphanedFiles: [],
                missingFiles: []
            };
        }
    }
    static async getTracksNeedingProcessing(limit = 100) {
        try {
            return await prisma_1.default.quizTrack.findMany({
                where: {
                    albumArtProcessed: false,
                    albumArtUrl: { not: null },
                    AND: [
                        { localAlbumArtThumbnail: null },
                        { localAlbumArtCover: null },
                        { localAlbumArtOriginal: null }
                    ]
                },
                select: {
                    id: true,
                    artist: true,
                    album: true,
                    albumArtUrl: true
                },
                take: limit,
                orderBy: { createdAt: 'asc' }
            });
        }
        catch (error) {
            console.error('Error getting tracks needing processing:', error);
            return [];
        }
    }
    static async markAsProcessed(trackId, localPaths) {
        try {
            await prisma_1.default.quizTrack.update({
                where: { id: trackId },
                data: {
                    localAlbumArtThumbnail: localPaths.thumbnail || null,
                    localAlbumArtCover: localPaths.cover || null,
                    localAlbumArtOriginal: localPaths.original || null,
                    albumArtProcessed: true,
                    albumArtProcessedAt: new Date()
                }
            });
            this.availabilityCache.delete(trackId);
            this.cacheExpiry.delete(trackId);
        }
        catch (error) {
            console.error('Error marking track as processed:', error);
        }
    }
    static async markAsFailed(trackId, reason) {
        try {
            await prisma_1.default.quizTrack.update({
                where: { id: trackId },
                data: {
                    albumArtProcessed: true,
                    albumArtProcessedAt: new Date(),
                }
            });
            this.availabilityCache.delete(trackId);
            this.cacheExpiry.delete(trackId);
        }
        catch (error) {
            console.error('Error marking track as failed:', error);
        }
    }
    static async resetProcessingStatus(trackIds) {
        try {
            const updateData = {
                albumArtProcessed: false,
                albumArtProcessedAt: null,
                localAlbumArtThumbnail: null,
                localAlbumArtCover: null,
                localAlbumArtOriginal: null
            };
            let result;
            if (trackIds && trackIds.length > 0) {
                result = await prisma_1.default.quizTrack.updateMany({
                    where: { id: { in: trackIds } },
                    data: updateData
                });
            }
            else {
                result = await prisma_1.default.quizTrack.updateMany({
                    data: updateData
                });
            }
            this.clearCache();
            return result.count;
        }
        catch (error) {
            console.error('Error resetting processing status:', error);
            return 0;
        }
    }
    static async getImageUrl(trackId, size = 'thumbnail') {
        const availability = await this.checkAvailability(trackId);
        if (availability.hasLocal) {
            const path = availability.localPaths[size];
            if (path) {
                return `/api/images/${path}`;
            }
            const fallback = availability.localPaths.thumbnail ||
                availability.localPaths.cover ||
                availability.localPaths.original;
            if (fallback) {
                return `/api/images/${fallback}`;
            }
        }
        return null;
    }
    static async fileExists(filePath) {
        try {
            await fs_1.promises.access(filePath);
            return true;
        }
        catch {
            return false;
        }
    }
    static cacheResult(trackId, result) {
        this.availabilityCache.set(trackId, result);
        this.cacheExpiry.set(trackId, Date.now() + this.CACHE_TTL);
    }
    static clearCache() {
        this.availabilityCache.clear();
        this.cacheExpiry.clear();
    }
    static async findOrphanedAndMissingFiles() {
        try {
            const orphanedFiles = [];
            const missingFiles = [];
            const tracksWithLocalArt = await prisma_1.default.quizTrack.findMany({
                where: {
                    OR: [
                        { localAlbumArtThumbnail: { not: null } },
                        { localAlbumArtCover: { not: null } },
                        { localAlbumArtOriginal: { not: null } }
                    ]
                },
                select: {
                    id: true,
                    localAlbumArtThumbnail: true,
                    localAlbumArtCover: true,
                    localAlbumArtOriginal: true
                }
            });
            for (const track of tracksWithLocalArt) {
                for (const pathField of ['localAlbumArtThumbnail', 'localAlbumArtCover', 'localAlbumArtOriginal']) {
                    const relativePath = track[pathField];
                    if (relativePath) {
                        const fullPath = path_1.default.join(this.MEDIA_BASE, relativePath);
                        if (!(await this.fileExists(fullPath))) {
                            missingFiles.push(relativePath);
                        }
                    }
                }
            }
            return { orphanedFiles, missingFiles };
        }
        catch (error) {
            console.error('Error finding orphaned/missing files:', error);
            return { orphanedFiles: [], missingFiles: [] };
        }
    }
}
exports.AlbumArtStatusService = AlbumArtStatusService;
_a = AlbumArtStatusService;
AlbumArtStatusService.MEDIA_BASE = '/media/images';
AlbumArtStatusService.ALBUM_ART_DIR = path_1.default.join(_a.MEDIA_BASE, 'album-art');
AlbumArtStatusService.availabilityCache = new Map();
AlbumArtStatusService.cacheExpiry = new Map();
AlbumArtStatusService.CACHE_TTL = 5 * 60 * 1000;
//# sourceMappingURL=album-art-status-service.js.map