"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MPDVolumeNormalizer = void 0;
exports.getMPDVolumeNormalizer = getMPDVolumeNormalizer;
exports.stopMPDVolumeNormalizer = stopMPDVolumeNormalizer;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
class MPDVolumeNormalizer {
    constructor(mpdClient) {
        this.userVolume = 70;
        this.isEnabled = true;
        this.currentTrackFile = null;
        this.pollingInterval = null;
        this.lastStatus = null;
        this.mpdClient = mpdClient;
    }
    async start(intervalMs = 1000) {
        console.log('[MPDVolumeNormalizer] Starting volume normalization service');
        await this.checkAndAdjustVolume();
        this.pollingInterval = setInterval(async () => {
            try {
                await this.checkAndAdjustVolume();
            }
            catch (error) {
                console.error('[MPDVolumeNormalizer] Error checking volume:', error);
            }
        }, intervalMs);
    }
    stop() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }
    setEnabled(enabled) {
        this.isEnabled = enabled;
        console.log(`[MPDVolumeNormalizer] Volume normalization ${enabled ? 'enabled' : 'disabled'}`);
    }
    setUserVolume(volume) {
        this.userVolume = Math.max(0, Math.min(100, volume));
        console.log(`[MPDVolumeNormalizer] User volume set to ${this.userVolume}`);
    }
    async checkAndAdjustVolume() {
        if (!this.isEnabled)
            return;
        try {
            const status = await this.mpdClient.getStatus();
            const currentSong = await this.mpdClient.getCurrentSong();
            if (!currentSong || !currentSong.file) {
                return;
            }
            if (currentSong.file === this.currentTrackFile) {
                if (this.lastStatus && Math.abs(status.volume - this.lastStatus.volume) > 5) {
                    const currentGain = await this.getTrackGain(currentSong.file);
                    if (currentGain !== null) {
                        const gainLinear = Math.pow(10, currentGain / 20);
                        this.userVolume = Math.round(status.volume / gainLinear);
                        console.log(`[MPDVolumeNormalizer] User manually changed volume to ${this.userVolume}`);
                    }
                }
                this.lastStatus = status;
                return;
            }
            console.log(`[MPDVolumeNormalizer] Track changed: ${currentSong.file}`);
            this.currentTrackFile = currentSong.file;
            const gain = await this.getTrackGain(currentSong.file);
            if (gain !== null) {
                const gainLinear = Math.pow(10, gain / 20);
                const normalizedVolume = Math.round(this.userVolume * gainLinear);
                const clampedVolume = Math.max(0, Math.min(100, normalizedVolume));
                console.log(`[MPDVolumeNormalizer] Applying normalization:`);
                console.log(`  Track: ${currentSong.title || currentSong.file}`);
                console.log(`  Gain: ${gain.toFixed(1)} dB`);
                console.log(`  User volume: ${this.userVolume}%`);
                console.log(`  Normalized volume: ${clampedVolume}%`);
                await this.mpdClient.setVolume(clampedVolume);
            }
            else {
                console.log(`[MPDVolumeNormalizer] No gain data for track, using user volume: ${this.userVolume}%`);
                await this.mpdClient.setVolume(this.userVolume);
            }
            this.lastStatus = status;
        }
        catch (error) {
            console.error('[MPDVolumeNormalizer] Error in checkAndAdjustVolume:', error);
        }
    }
    async getTrackGain(filePath) {
        try {
            const track = await prisma_1.default.quizTrack.findUnique({
                where: { mpdFilePath: filePath },
                select: { calculatedGain: true }
            });
            return track?.calculatedGain ?? null;
        }
        catch (error) {
            console.error('[MPDVolumeNormalizer] Error fetching track gain:', error instanceof Error ? error.message : String(error));
            return null;
        }
    }
}
exports.MPDVolumeNormalizer = MPDVolumeNormalizer;
let normalizerInstance = null;
function getMPDVolumeNormalizer(mpdClient) {
    if (!normalizerInstance) {
        normalizerInstance = new MPDVolumeNormalizer(mpdClient);
    }
    return normalizerInstance;
}
function stopMPDVolumeNormalizer() {
    if (normalizerInstance) {
        normalizerInstance.stop();
        normalizerInstance = null;
    }
}
//# sourceMappingURL=mpd-volume-normalizer.js.map