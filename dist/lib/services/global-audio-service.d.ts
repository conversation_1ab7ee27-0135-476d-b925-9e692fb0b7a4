import { AudioManager } from '@/lib/audio-manager';
import type { QueuedSong } from '@/lib/types';
export interface GlobalAudioState {
    isPlaying: boolean;
    currentTrack: QueuedSong | null;
    volume: number;
    mode: 'jukebox' | 'quiz' | 'idle';
    isInitialized: boolean;
    audioManager: AudioManager | null;
}
export type AudioModeTransition = {
    from: 'jukebox' | 'quiz' | 'idle';
    to: 'jukebox' | 'quiz' | 'idle';
    fadeOutDuration?: number;
    fadeInDuration?: number;
    pauseInsteadOfStop?: boolean;
};
declare class GlobalAudioServiceClass {
    private static instance;
    private audioManager;
    private currentState;
    private listeners;
    private initializationPromise;
    private transitionInProgress;
    private constructor();
    static getInstance(): GlobalAudioServiceClass;
    initialize(): Promise<void>;
    private _initialize;
    transitionToMode(transition: AudioModeTransition): Promise<void>;
    private jukeboxToQuizTransition;
    private quizToJukeboxTransition;
    private fadeOutAndPause;
    private resumeFromIdle;
    getState(): GlobalAudioState;
    setVolume(volume: number): Promise<void>;
    subscribe(listener: (state: GlobalAudioState) => void): () => void;
    getAudioManager(): AudioManager | null;
    refreshState(): Promise<void>;
    cleanup(): Promise<void>;
    private notifyListeners;
}
export declare const GlobalAudioService: GlobalAudioServiceClass;
export declare function useGlobalAudio(): {
    transitionToMode: (transition: AudioModeTransition) => Promise<void>;
    setVolume: (volume: number) => Promise<void>;
    getAudioManager: () => AudioManager | null;
    refreshState: () => Promise<void>;
    isPlaying: boolean;
    currentTrack: QueuedSong | null;
    volume: number;
    mode: "jukebox" | "quiz" | "idle";
    isInitialized: boolean;
    audioManager: AudioManager | null;
};
export {};
