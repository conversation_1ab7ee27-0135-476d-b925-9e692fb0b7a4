"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetadataEnrichmentService = void 0;
const prisma_1 = __importDefault(require("../database/prisma"));
const NodeID3 = __importStar(require("node-id3"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const api_resilience_1 = require("../utils/api-resilience");
const text_normalization_1 = require("../utils/text-normalization");
const release_date_validator_1 = require("../utils/release-date-validator");
const artwork_manager_1 = require("../utils/artwork-manager");
const enhanced_album_art_fetcher_fixed_1 = require("./enhanced-album-art-fetcher-fixed");
class MetadataEnrichmentService {
    constructor() {
        this.spotifyToken = null;
        this.lastFmApiKey = process.env.LASTFM_API_KEY || null;
        this.musicRootPath = process.env.MUSIC_ROOT || '/music';
        this.musicBrainzUserAgent = process.env.MUSICBRAINZ_USER_AGENT || 'MusicQuiz/1.0';
        this._configCheckDone = false;
        this.apiClient = new api_resilience_1.ResilientApiClient();
        this.artworkManager = new artwork_manager_1.ArtworkManager(process.env.IMAGES_ROOT || '/media/images');
        this.albumArtFetcher = new enhanced_album_art_fetcher_fixed_1.EnhancedAlbumArtFetcher();
    }
    async enrich(track) {
        const result = {
            albumArtUrl: null,
            artistImageUrl: null,
            triviaFacts: [],
            chartPosition: null,
            chartCountry: null,
            releaseDate: null,
            spotifyId: null,
            musicbrainzId: null,
            quizCategories: [],
            chartData: {},
            thematicTags: [],
            specialLists: [],
            culturalContext: {},
            similarArtists: [],
            detailedMetadata: {}
        };
        try {
            if (!this._configCheckDone) {
                this._checkConfiguration();
                this._configCheckDone = true;
            }
            const fileMetadata = await this.extractFileMetadata(track.file);
            if (fileMetadata) {
                track.artist = fileMetadata.artist || track.artist;
                track.title = fileMetadata.title || track.title;
                track.album = fileMetadata.album || track.album;
                track.date = fileMetadata.year ? String(fileMetadata.year) : track.date;
                Object.assign(result, fileMetadata);
            }
            if (track.artist)
                track.artist = this.normalizeForProcessing(track.artist);
            if (track.title)
                track.title = this.normalizeForProcessing(track.title);
            await this.enrichFromExternalAPIs(track, result);
            await this.categorizeForQuiz(track, result);
            await this.enrichChartData(track, result);
            const apiResults = {
                spotify: await this.getStoredSpotifyData(track.artist, track.title),
                lastfm: await this.getStoredLastFmData(track.artist, track.title),
                musicbrainz: await this.getStoredMusicBrainzData(track.artist, track.title)
            };
            await this.validateAndEnhanceReleaseDate(track, result, apiResults);
            await this.processArtwork(track, result);
            result.triviaFacts = Array.from(new Set(result.triviaFacts)).filter(Boolean);
            result.quizCategories = Array.from(new Set(result.quizCategories)).filter(Boolean);
            result.thematicTags = Array.from(new Set(result.thematicTags)).filter(Boolean);
            result.specialLists = Array.from(new Set(result.specialLists)).filter(Boolean);
            result.similarArtists = Array.from(new Set(result.similarArtists)).filter(Boolean);
            const hasData = Object.values(result).some(v => {
                if (Array.isArray(v))
                    return v.length > 0;
                if (typeof v === 'object' && v !== null)
                    return Object.keys(v).length > 0;
                return v !== null && v !== undefined;
            });
            return hasData ? result : null;
        }
        catch (error) {
            console.error('[MetadataEnrichment] Enrichment failed:', error);
            return null;
        }
    }
    parseFilenameMetadata(mpdFilePath) {
        const filename = path.basename(mpdFilePath, '.mp3');
        let artist;
        let title;
        let album;
        let year;
        let match = filename.match(/^\d{4}\s*-\s*\d+[\s-]*(.+?)\s*-\s*(.+)$/);
        if (match) {
            artist = this.cleanText(match[1]);
            title = this.cleanText(match[2]);
        }
        else {
            match = filename.match(/^\d+[\s-]*(.+?)\s*-\s*(.+)$/);
            if (match) {
                artist = this.cleanText(match[1]);
                title = this.cleanText(match[2]);
            }
            else {
                match = filename.match(/^(.+?)\s*-\s*(.+)$/);
                if (match) {
                    artist = this.cleanText(match[1]);
                    title = this.cleanText(match[2]);
                }
            }
        }
        const yearMatch = mpdFilePath.match(/DC(\d{4})/);
        if (yearMatch) {
            album = `Deutsche Charts ${yearMatch[1]}`;
        }
        return { artist, title, album, year };
    }
    cleanText(text) {
        return text
            .replace(/_/g, ' ')
            .replace(/\s+/g, ' ')
            .replace(/\([^)]*\)/g, '')
            .replace(/\[[^\]]*\]/g, '')
            .replace(/feat\.?/gi, 'feat')
            .replace(/\s*feat\s*.*/gi, '')
            .replace(/^\d+\s+/, '')
            .trim()
            .replace(/\b\w/g, (c) => c.toUpperCase());
    }
    async extractFileMetadata(mpdFilePath) {
        const absolutePath = path.join(this.musicRootPath, mpdFilePath);
        if (!fs.existsSync(this.musicRootPath)) {
            console.log(`[MetadataEnrichment] Music files not locally accessible - parsing filename: ${mpdFilePath}`);
            const filenameData = this.parseFilenameMetadata(mpdFilePath);
            if (filenameData.artist && filenameData.title) {
                console.log(`[MetadataEnrichment] Parsed from filename: ${filenameData.artist} - ${filenameData.title}`);
                return {
                    artist: filenameData.artist,
                    title: filenameData.title,
                    album: filenameData.album,
                    year: filenameData.year,
                    detailedMetadata: {
                        source: 'filename_parsing'
                    },
                    quizCategories: ['german-charts'],
                    thematicTags: []
                };
            }
            else {
                console.warn(`[MetadataEnrichment] Could not parse artist/title from filename: ${mpdFilePath}`);
                return null;
            }
        }
        if (!fs.existsSync(absolutePath)) {
            console.warn(`[MetadataEnrichment] File not found, skipping metadata extraction: ${absolutePath}`);
            return null;
        }
        try {
            const tags = NodeID3.read(absolutePath);
            if (!tags) {
                console.warn(`[MetadataEnrichment] No ID3 tags found in: ${absolutePath}`);
                return null;
            }
            const extractedData = {
                artist: tags.artist || undefined,
                title: tags.title || undefined,
                album: tags.album || undefined,
                year: tags.year ? parseInt(String(tags.year)) : undefined,
                detailedMetadata: {
                    ...tags,
                    format: {
                        container: 'mp3',
                        codec: 'mp3'
                    }
                },
                quizCategories: tags.genre ? [String(tags.genre).toLowerCase()] : [],
                thematicTags: []
            };
            console.log(`[MetadataEnrichment] Successfully extracted metadata from: ${mpdFilePath} - ${extractedData.artist} - ${extractedData.title}`);
            return extractedData;
        }
        catch (error) {
            console.error(`[MetadataEnrichment] Failed to extract file metadata for ${absolutePath}: ${error}`);
            return null;
        }
    }
    async enrichFromExternalAPIs(track, result) {
        if (!track.artist || !track.title) {
            if (process.env.NODE_ENV === 'development') {
                console.log(`[MetadataEnrichment] Skipping external API search for track without artist/title: ${track.file}`);
            }
            return;
        }
        console.log(`[MetadataEnrichment] Searching APIs for: ${track.artist} - ${track.title}`);
        const spotify = await this.fetchSpotifyData(track.artist, track.title);
        if (spotify) {
            result.albumArtUrl = spotify.albumArt;
            result.artistImageUrl = spotify.artistImage;
            result.spotifyId = spotify.id;
            result.releaseDate = spotify.releaseDate;
            if (spotify.popularity > 80)
                result.quizCategories.push('all-time-favorites');
            if (spotify.popularity > 70)
                result.quizCategories.push('popular-hits');
        }
        const lastfm = await this.fetchLastFmData(track.artist, track.title);
        if (lastfm) {
            if (lastfm.artistImage && !result.artistImageUrl)
                result.artistImageUrl = lastfm.artistImage;
            result.triviaFacts.push(...lastfm.facts);
            result.thematicTags.push(...lastfm.tags);
            if (lastfm.culturalInfo) {
                result.culturalContext = { ...result.culturalContext, ...lastfm.culturalInfo };
            }
            if (lastfm.similarArtists && lastfm.similarArtists.length > 0) {
                result.similarArtists.push(...lastfm.similarArtists);
            }
        }
        const musicbrainz = await this.fetchMusicBrainzData(track.artist, track.title);
        if (musicbrainz) {
            result.musicbrainzId = musicbrainz.id;
            if (musicbrainz.releaseDate && !result.releaseDate)
                result.releaseDate = musicbrainz.releaseDate;
            if (musicbrainz.culturalInfo) {
                result.culturalContext = { ...result.culturalContext, ...musicbrainz.culturalInfo };
            }
        }
    }
    async categorizeForQuiz(track, result) {
        const year = track.date ? parseInt(track.date) : null;
        const artist = track.artist?.toLowerCase() || '';
        const title = track.title?.toLowerCase() || '';
        if (year) {
            const decade = Math.floor(year / 10) * 10;
            result.quizCategories.push(`${decade}s`);
            if (decade >= 1960 && decade <= 1990)
                result.quizCategories.push('classic-era');
            if (decade >= 1950 && decade <= 1970)
                result.quizCategories.push('golden-age');
        }
        await this.analyzeThematicContent(title, artist, result);
        if (this.isSportsRelated(title, artist)) {
            result.thematicTags.push('sports');
            if (this.isSoccerRelated(title, artist)) {
                result.thematicTags.push('soccer');
                result.quizCategories.push('soccer-songs');
            }
        }
        if (this.isSoundtrackRelated(title, artist)) {
            result.thematicTags.push('movies', 'soundtrack');
            result.quizCategories.push('movie-soundtracks');
        }
        if (process.env.NODE_ENV === 'development') {
            console.log(`[MetadataEnrichment] Categorized '${track.title}': ${result.quizCategories.join(', ')}`);
        }
    }
    async analyzeThematicContent(title, artist, result) {
        const text = `${title} ${artist}`.toLowerCase();
        const loveKeywords = ['love', 'heart', 'kiss', 'baby', 'darling', 'honey', 'romantic', 'valentine'];
        if (loveKeywords.some(k => text.includes(k))) {
            result.thematicTags.push('love-songs', 'romantic');
        }
        const partyKeywords = ['dance', 'party', 'night', 'club', 'disco', 'groove', 'beat', 'rhythm'];
        if (partyKeywords.some(k => text.includes(k))) {
            result.thematicTags.push('dance', 'party', 'nightlife');
        }
        if (text.includes('christmas') || text.includes('xmas') || text.includes('holiday')) {
            result.thematicTags.push('christmas', 'holidays');
            result.quizCategories.push('christmas-songs');
        }
        const epicKeywords = ['power', 'strong', 'fight', 'battle', 'victory', 'champion', 'hero', 'legend'];
        if (epicKeywords.some(k => text.includes(k))) {
            result.thematicTags.push('epic', 'power', 'motivational');
        }
    }
    isSportsRelated(title, artist) {
        const sportsKeywords = [
            'sport', 'game', 'play', 'win', 'champion', 'victory', 'team', 'stadium',
            'goal', 'score', 'match', 'tournament', 'cup', 'league', 'world cup',
            'waka', 'africa'
        ];
        const text = `${title} ${artist}`.toLowerCase();
        return sportsKeywords.some(k => text.includes(k));
    }
    isSoccerRelated(title, artist) {
        const soccerKeywords = [
            'soccer', 'football', 'fifa', 'world cup', 'euro', 'championship',
            'goal', 'penalty', 'striker', 'midfielder', 'goalkeeper', 'referee',
            'waka', 'africa'
        ];
        const text = `${title} ${artist}`.toLowerCase();
        return soccerKeywords.some(k => text.includes(k));
    }
    isSoundtrackRelated(title, artist) {
        const soundtrackKeywords = [
            'soundtrack', 'theme', 'main title', 'end credits', 'score',
            'from the movie', 'from the film', 'ost', 'original motion picture'
        ];
        const text = `${title} ${artist}`.toLowerCase();
        return soundtrackKeywords.some(k => text.includes(k));
    }
    async enrichChartData(track, result) {
    }
    async fetchSpotifyData(artist, title) {
        if (!process.env.SPOTIFY_CLIENT_ID || !process.env.SPOTIFY_CLIENT_SECRET) {
            console.log('[MetadataEnrichment] ⚠️  Spotify credentials not configured - skipping Spotify enrichment');
            return null;
        }
        try {
            await this.ensureSpotifyToken();
            if (!artist || !title)
                return null;
            const q = encodeURIComponent(`${title} artist:${artist}`);
            const url = `https://api.spotify.com/v1/search?q=${q}&type=track&limit=1`;
            const res = await fetch(url, {
                headers: { Authorization: `Bearer ${this.spotifyToken}` }
            });
            if (!res.ok) {
                if (process.env.DEBUG_ENRICH === 'true') {
                    console.warn(`[MetadataEnrichment] Spotify API error: ${res.status}`);
                }
                return null;
            }
            const json = await res.json();
            const item = json.tracks?.items?.[0];
            if (!item)
                return null;
            let artistImage = null;
            const firstArtistId = item.artists?.[0]?.id;
            if (firstArtistId) {
                const artistRes = await fetch(`https://api.spotify.com/v1/artists/${firstArtistId}`, {
                    headers: { Authorization: `Bearer ${this.spotifyToken}` }
                });
                if (artistRes.ok) {
                    const artistJson = await artistRes.json();
                    artistImage = artistJson.images?.[0]?.url || null;
                }
            }
            return {
                id: item.id,
                albumArt: item.album?.images?.[0]?.url || null,
                artistImage,
                releaseDate: item.album?.release_date || null,
                popularity: item.popularity || 0
            };
        }
        catch (error) {
            if (process.env.DEBUG_ENRICH === 'true') {
                console.warn('[MetadataEnrichment] Spotify fetch failed', error);
            }
            return null;
        }
    }
    async ensureSpotifyToken() {
        if (this.spotifyToken)
            return;
        const basic = Buffer.from(`${process.env.SPOTIFY_CLIENT_ID}:${process.env.SPOTIFY_CLIENT_SECRET}`).toString('base64');
        const res = await fetch('https://accounts.spotify.com/api/token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded', Authorization: `Basic ${basic}` },
            body: 'grant_type=client_credentials'
        });
        if (!res.ok)
            return;
        const json = await res.json();
        this.spotifyToken = json.access_token;
    }
    async fetchLastFmData(artist, title) {
        if (!this.lastFmApiKey) {
            console.log('[MetadataEnrichment] ⚠️  Last.fm API key not configured - skipping Last.fm enrichment');
            return null;
        }
        if (!artist)
            return null;
        try {
            const verbose = process.env.DEBUG_ENRICH === 'true';
            const artistUrl = `https://ws.audioscrobbler.com/2.0/?method=artist.getinfo&autocorrect=1` +
                `&artist=${encodeURIComponent(artist)}` +
                `&api_key=${this.lastFmApiKey}&format=json`;
            const artistRes = await fetch(artistUrl);
            if (!artistRes.ok) {
                if (verbose) {
                    console.warn(`[LastFM] artist.getinfo HTTP ${artistRes.status}`);
                }
                return null;
            }
            const artistJson = await artistRes.json();
            if (verbose)
                console.log('[LastFM] artist.getinfo', JSON.stringify(artistJson).slice(0, 500));
            const img = artistJson?.artist?.image?.find((i) => i.size === 'extralarge')?.['#text'];
            const bio = artistJson?.artist?.bio?.content;
            const tags = artistJson?.artist?.tags?.tag?.map((t) => t.name) || [];
            const facts = [];
            if (bio) {
                facts.push(this.extractMeaningfulSentence(bio));
            }
            if (title) {
                const trackUrl = `https://ws.audioscrobbler.com/2.0/?method=track.getinfo&autocorrect=1` +
                    `&artist=${encodeURIComponent(artist)}` +
                    `&track=${encodeURIComponent(title)}` +
                    `&api_key=${this.lastFmApiKey}&format=json`;
                const trackRes = await fetch(trackUrl);
                if (trackRes.ok) {
                    const trackJson = await trackRes.json();
                    if (verbose)
                        console.log('[LastFM] track.getinfo', JSON.stringify(trackJson).slice(0, 500));
                    const trackWiki = trackJson?.track?.wiki?.content;
                    if (trackWiki) {
                        facts.push(this.extractMeaningfulSentence(trackWiki));
                    }
                }
                else if (verbose) {
                    console.warn(`[LastFM] track.getinfo HTTP ${trackRes.status}`);
                }
            }
            const similarArtists = [];
            const similar = artistJson?.artist?.similar?.artist || [];
            for (const s of similar.slice(0, 10)) {
                if (s?.name)
                    similarArtists.push(s.name);
            }
            return {
                artistImage: img || null,
                facts,
                similarArtists,
                tags: tags.map((t) => t.toLowerCase()).slice(0, 5),
                culturalInfo: bio ? { lastfmBio: this.extractMeaningfulSentence(bio) } : null
            };
        }
        catch (err) {
            console.warn('[MetadataEnrichment] LastFM fetch failed', err);
            return null;
        }
    }
    extractMeaningfulSentence(text) {
        const sentences = text.replace(/\n/g, ' ').split('. ');
        const isGeneric = (s) => /more than one artist|multiple artists with the name|may refer to/i.test(s);
        for (const s of sentences) {
            const t = s.trim();
            if (t && !isGeneric(t))
                return t;
        }
        return sentences[0]?.trim() || '';
    }
    async fetchMusicBrainzData(artist, title) {
        if (!this.musicBrainzUserAgent || !artist || !title) {
            console.log('[MetadataEnrichment] ⚠️  MusicBrainz user agent not configured or missing artist/title - skipping MusicBrainz enrichment');
            return null;
        }
        try {
            const verbose = process.env.DEBUG_ENRICH === 'true';
            const query = encodeURIComponent(`artist:"${artist}" AND recording:"${title}"`);
            const url = `https://musicbrainz.org/ws/2/recording?query=${query}&limit=1&fmt=json`;
            const response = await fetch(url, {
                headers: {
                    'User-Agent': this.musicBrainzUserAgent,
                    'Accept': 'application/json'
                }
            });
            if (!response.ok) {
                if (verbose) {
                    console.warn(`[MusicBrainz] Recording search HTTP ${response.status}`);
                }
                return null;
            }
            const data = await response.json();
            if (verbose)
                console.log('[MusicBrainz] recording search', JSON.stringify(data).slice(0, 500));
            const recording = data.recordings?.[0];
            if (!recording)
                return null;
            let releaseDate = null;
            let culturalInfo = null;
            if (recording.releases && recording.releases.length > 0) {
                const release = recording.releases[0];
                releaseDate = release.date || null;
                if (release.country) {
                    culturalInfo = {
                        musicbrainz: {
                            country: release.country,
                            recordingId: recording.id,
                            releaseId: release.id
                        }
                    };
                }
            }
            return {
                id: recording.id,
                releaseDate,
                culturalInfo
            };
        }
        catch (error) {
            if (process.env.DEBUG_ENRICH === 'true') {
                console.warn('[MetadataEnrichment] MusicBrainz fetch failed', error);
            }
            return null;
        }
    }
    normalizeForProcessing(text) {
        return text.trim().replace(/\s+/g, ' ');
    }
    async checkForDuplicates(artist, title) {
        if (!artist || !title)
            return [];
        const existingTracks = await prisma_1.default.quizTrack.findMany({
            where: {
                OR: [
                    {
                        AND: [
                            { artist: { contains: (0, text_normalization_1.normalizeArtistName)(artist), mode: 'insensitive' } },
                            { title: { contains: (0, text_normalization_1.normalizeSongTitle)(title), mode: 'insensitive' } }
                        ]
                    },
                    {
                        AND: [
                            { artist: { contains: artist, mode: 'insensitive' } },
                            { title: { contains: title, mode: 'insensitive' } }
                        ]
                    }
                ]
            },
            select: {
                id: true,
                artist: true,
                title: true,
                mpdFilePath: true,
                fileFingerprint: true
            }
        });
        return existingTracks.filter(track => (0, text_normalization_1.areTracksDuplicates)({ artist: artist, title: title }, { artist: track.artist || '', title: track.title || '' }, 0.85));
    }
    async getStoredSpotifyData(artist, title) {
        return await this.fetchSpotifyData(artist, title);
    }
    async getStoredLastFmData(artist, title) {
        return await this.fetchLastFmData(artist, title);
    }
    async getStoredMusicBrainzData(artist, title) {
        return await this.fetchMusicBrainzData(artist, title);
    }
    async validateAndEnhanceReleaseDate(track, result, apiResults) {
        const sources = [];
        if (track.date || track.year) {
            sources.push(release_date_validator_1.ReleaseDateValidator.createId3TagsSource({
                date: track.date,
                year: track.year ? parseInt(track.year) : null
            }));
        }
        if (apiResults.spotify) {
            sources.push(release_date_validator_1.ReleaseDateValidator.createSpotifySource(apiResults.spotify));
        }
        if (apiResults.lastfm) {
            sources.push(release_date_validator_1.ReleaseDateValidator.createLastFmSource(apiResults.lastfm));
        }
        if (apiResults.musicbrainz) {
            sources.push(release_date_validator_1.ReleaseDateValidator.createMusicBrainzSource(apiResults.musicbrainz));
        }
        if (result.chartData && Object.keys(result.chartData).length > 0) {
            sources.push(release_date_validator_1.ReleaseDateValidator.createChartDataSource(result.chartData));
        }
        const validation = release_date_validator_1.ReleaseDateValidator.validateReleaseDate(sources);
        result.releaseDateValidation = {
            finalYear: validation.finalYear,
            confidence: validation.confidence,
            sources: validation.sources,
            warnings: validation.warnings
        };
        if (validation.finalDate && validation.confidence !== 'low') {
            result.releaseDate = validation.finalDate.toISOString().split('T')[0];
        }
        if (validation.warnings.length > 0 && process.env.DEBUG_ENRICH === 'true') {
            console.log(`[MetadataEnrichment] Release date warnings for ${track.artist} - ${track.title}:`);
            validation.warnings.forEach(warning => console.log(`  ⚠️  ${warning}`));
        }
        if (validation.reasoning.length > 0 && process.env.DEBUG_ENRICH === 'true') {
            console.log(`[MetadataEnrichment] Release date reasoning:`);
            validation.reasoning.forEach(reason => console.log(`  💡 ${reason}`));
        }
    }
    async processArtwork(track, result) {
        try {
            const artworkPaths = {};
            if (track.artist) {
                let albumArtProcessed = false;
                if (result.albumArtUrl && track.album) {
                    const albumResult = await this.artworkManager.processAlbumArt(result.albumArtUrl, track.artist, track.album, track.id);
                    if (albumResult.success) {
                        artworkPaths.albumArt = albumResult.paths;
                        albumArtProcessed = true;
                        if (process.env.DEBUG_ENRICH === 'true') {
                            console.log(`[MetadataEnrichment] Album art processed for ${track.artist} - ${track.album}`);
                        }
                    }
                }
                if (!albumArtProcessed) {
                    console.log(`[MetadataEnrichment] Using enhanced album art fetcher for ${track.artist} - ${track.album || track.title}`);
                    const searchResult = await this.albumArtFetcher.searchAlbumArt({
                        artist: track.artist,
                        album: track.album,
                        title: track.title,
                        year: track.date ? parseInt(track.date) : undefined,
                        enableFuzzyMatching: true
                    });
                    if (searchResult) {
                        result.albumArtUrl = searchResult.url;
                        const albumResult = await (0, enhanced_album_art_fetcher_fixed_1.downloadAlbumArtWithRetry)(searchResult, this.artworkManager, track.artist, track.album || track.title, track.id, 3);
                        if (albumResult.success) {
                            artworkPaths.albumArt = albumResult.paths;
                            if (process.env.DEBUG_ENRICH === 'true') {
                                console.log(`[MetadataEnrichment] Album art found and processed via ${searchResult.source} for ${track.artist} - ${track.album || track.title}`);
                            }
                        }
                        else if (process.env.DEBUG_ENRICH === 'true') {
                            console.log(`[MetadataEnrichment] Album art download failed after retries: ${albumResult.error}`);
                        }
                    }
                    else if (process.env.DEBUG_ENRICH === 'true') {
                        console.log(`[MetadataEnrichment] No album art found by enhanced fetcher for ${track.artist} - ${track.album || track.title}`);
                    }
                }
            }
            if (result.artistImageUrl && track.artist) {
                const artistResult = await this.artworkManager.processArtistImage(result.artistImageUrl, track.artist);
                if (artistResult.success) {
                    artworkPaths.artistImage = artistResult.paths;
                    if (process.env.DEBUG_ENRICH === 'true') {
                        console.log(`[MetadataEnrichment] Artist image processed for ${track.artist}`);
                    }
                }
                else if (process.env.DEBUG_ENRICH === 'true') {
                    console.log(`[MetadataEnrichment] Artist image processing failed: ${artistResult.error}`);
                }
            }
            if (Object.keys(artworkPaths).length > 0) {
                result.artworkPaths = artworkPaths;
            }
        }
        catch (error) {
            console.error(`[MetadataEnrichment] Artwork processing failed for ${track.artist} - ${track.title}:`, error);
        }
    }
    _checkConfiguration() {
        const hasSpotify = !!(process.env.SPOTIFY_CLIENT_ID && process.env.SPOTIFY_CLIENT_SECRET);
        const hasLastFm = !!this.lastFmApiKey;
        const hasMusicBrainz = !!this.musicBrainzUserAgent;
        const hasSharp = artwork_manager_1.ArtworkManager.checkSharpAvailability();
        if (!hasSpotify && !hasLastFm && !hasMusicBrainz) {
            console.log('⚠️  [MetadataEnrichment] No API credentials configured!');
            console.log('   Images and trivia will not be fetched from external services.');
            console.log('   To enable enrichment, configure these environment variables:');
            console.log('   - SPOTIFY_CLIENT_ID and SPOTIFY_CLIENT_SECRET');
            console.log('   - LASTFM_API_KEY');
            console.log('   - MUSICBRAINZ_USER_AGENT');
            console.log('   See env.example for details.');
        }
        else {
            const configuredApis = [];
            if (hasSpotify)
                configuredApis.push('Spotify');
            if (hasLastFm)
                configuredApis.push('Last.fm');
            if (hasMusicBrainz)
                configuredApis.push('MusicBrainz');
            console.log(`✅ [MetadataEnrichment] API credentials configured for: ${configuredApis.join(', ')}`);
        }
        if (!hasSharp) {
            console.log('⚠️  [MetadataEnrichment] Sharp not available - artwork scaling disabled');
            console.log('   Install Sharp for image processing: npm install sharp');
        }
        else {
            console.log('✅ [MetadataEnrichment] Sharp available for artwork processing');
        }
    }
}
exports.MetadataEnrichmentService = MetadataEnrichmentService;
//# sourceMappingURL=metadata-enrichment.js.map