"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoQueueConfigService = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
class AutoQueueConfigService {
    static async getAllConfigurations() {
        try {
            const configs = await prisma_1.default.user.findMany({
                where: {
                    autoQueuePreferences: {
                        not: '{}'
                    }
                },
                select: {
                    id: true,
                    autoQueuePreferences: true,
                    username: true,
                    updatedAt: true
                }
            });
            const summaries = [];
            summaries.push(...this.getDefaultConfigurations());
            return summaries;
        }
        catch (error) {
            console.error('[AutoQueueConfig] Failed to get configurations:', error);
            return this.getDefaultConfigurations();
        }
    }
    static async getConfiguration(configId) {
        try {
            const defaultConfig = this.getDefaultConfiguration(configId);
            if (defaultConfig) {
                return defaultConfig;
            }
            return null;
        }
        catch (error) {
            console.error('[AutoQueueConfig] Failed to get configuration:', error);
            return null;
        }
    }
    static async getActiveConfiguration(userId) {
        try {
            if (userId) {
                const user = await prisma_1.default.user.findUnique({
                    where: { id: userId },
                    select: { autoQueuePreferences: true }
                });
                if (user?.autoQueuePreferences && user.autoQueuePreferences !== '{}') {
                    const prefs = JSON.parse(user.autoQueuePreferences);
                    if (prefs.activeConfigId) {
                        const config = await this.getConfiguration(prefs.activeConfigId);
                        if (config)
                            return config;
                    }
                }
            }
            return this.getDefaultConfiguration('intelligent-standard');
        }
        catch (error) {
            console.error('[AutoQueueConfig] Failed to get active configuration:', error);
            return this.getDefaultConfiguration('intelligent-standard');
        }
    }
    static async saveConfiguration(config, userId) {
        try {
            const user = await prisma_1.default.user.findUnique({
                where: { id: userId },
                select: { autoQueuePreferences: true }
            });
            const currentPrefs = user?.autoQueuePreferences
                ? JSON.parse(user.autoQueuePreferences)
                : {};
            currentPrefs.customConfigs = currentPrefs.customConfigs || {};
            currentPrefs.customConfigs[config.id] = config;
            await prisma_1.default.user.update({
                where: { id: userId },
                data: {
                    autoQueuePreferences: JSON.stringify(currentPrefs)
                }
            });
            return config.id;
        }
        catch (error) {
            console.error('[AutoQueueConfig] Failed to save configuration:', error);
            throw new Error('Failed to save configuration');
        }
    }
    static async setActiveConfiguration(configId, userId) {
        try {
            const user = await prisma_1.default.user.findUnique({
                where: { id: userId },
                select: { autoQueuePreferences: true }
            });
            const currentPrefs = user?.autoQueuePreferences
                ? JSON.parse(user.autoQueuePreferences)
                : {};
            currentPrefs.activeConfigId = configId;
            currentPrefs.lastActivatedAt = new Date().toISOString();
            await prisma_1.default.user.update({
                where: { id: userId },
                data: {
                    autoQueuePreferences: JSON.stringify(currentPrefs)
                }
            });
            console.log(`[AutoQueueConfig] Set active configuration to ${configId} for user ${userId}`);
        }
        catch (error) {
            console.error('[AutoQueueConfig] Failed to set active configuration:', error);
            throw new Error('Failed to set active configuration');
        }
    }
    static async recordConfigurationUsage(configId) {
        try {
            console.log(`[AutoQueueConfig] Recording usage for configuration: ${configId}`);
        }
        catch (error) {
            console.error('[AutoQueueConfig] Failed to record usage:', error);
        }
    }
    static getDefaultConfigurations() {
        return [
            {
                id: 'intelligent-standard',
                name: 'Intelligent Standard',
                description: 'Balanced recommendations using all algorithms with smart defaults',
                isActive: true,
                isDefault: true,
                algorithm: 'intelligent',
                maxSongsToAdd: 5,
                createdAt: new Date('2024-01-01'),
                usageCount: 0
            },
            {
                id: 'favorites-focused',
                name: 'Favorites Focused',
                description: 'Prioritizes shared favorites among connected users',
                isActive: false,
                isDefault: true,
                algorithm: 'favorites-only',
                maxSongsToAdd: 3,
                createdAt: new Date('2024-01-01'),
                usageCount: 0
            },
            {
                id: 'discovery-mode',
                name: 'Discovery Mode',
                description: 'High diversity recommendations for music discovery',
                isActive: false,
                isDefault: true,
                algorithm: 'hybrid',
                maxSongsToAdd: 7,
                createdAt: new Date('2024-01-01'),
                usageCount: 0
            },
            {
                id: 'crowd-pleaser',
                name: 'Crowd Pleaser',
                description: 'Popular tracks that most people enjoy',
                isActive: false,
                isDefault: true,
                algorithm: 'popularity',
                maxSongsToAdd: 4,
                createdAt: new Date('2024-01-01'),
                usageCount: 0
            },
            {
                id: 'minimal-auto',
                name: 'Minimal Auto',
                description: 'Conservative auto-queue with minimal intervention',
                isActive: false,
                isDefault: true,
                algorithm: 'favorites-only',
                maxSongsToAdd: 2,
                createdAt: new Date('2024-01-01'),
                usageCount: 0
            }
        ];
    }
    static getDefaultConfiguration(configId) {
        const defaultConfigs = {
            'intelligent-standard': {
                id: 'intelligent-standard',
                name: 'Intelligent Standard',
                description: 'Balanced recommendations using all algorithms with smart defaults',
                isActive: true,
                isDefault: true,
                triggerSettings: {
                    enabled: true,
                    queueEmptyThreshold: 2,
                    noSuggestionsTimeout: 10,
                    minimumConnectedUsers: 1,
                    timeBasedTriggers: {
                        enabled: false,
                        schedules: []
                    }
                },
                recommendationSettings: {
                    algorithm: 'intelligent',
                    maxSongsToAdd: 5,
                    preventRecentRepeats: true,
                    recentRepeatWindowHours: 2,
                    algorithmWeights: {
                        collaborativeFiltering: 0.3,
                        contentBased: 0.25,
                        popularityBased: 0.2,
                        contextAware: 0.15,
                        favoritesAnalysis: 0.1
                    },
                    contentFilters: {
                        allowedGenres: [],
                        blockedGenres: [],
                        allowedYearRange: {},
                        requireAlbumArt: false
                    },
                    userPreferences: {
                        respectUserAutoQueuePrefs: true,
                        userVetoWeight: 0.3,
                        consensusThreshold: 0.6
                    }
                },
                behaviorSettings: {
                    diversityMode: 'medium',
                    energyLevelConsideration: true,
                    timeOfDayAdaptation: true,
                    fallbackBehavior: {
                        enabled: true,
                        mode: 'popular',
                        fallbackGenres: ['Pop', 'Rock', 'Hip-Hop']
                    },
                    adaptiveLearning: {
                        enabled: true,
                        trackUserSkips: true,
                        trackVotes: true,
                        learningRate: 0.1
                    }
                },
                notificationSettings: {
                    notifyOnAutoAdd: true,
                    notifyUsers: true,
                    showRecommendationReasons: true,
                    logAnalytics: true
                },
                createdAt: new Date('2024-01-01'),
                updatedAt: new Date(),
                createdBy: 'system',
                usageCount: 0
            },
            'favorites-focused': {
                id: 'favorites-focused',
                name: 'Favorites Focused',
                description: 'Prioritizes shared favorites among connected users',
                isActive: false,
                isDefault: true,
                triggerSettings: {
                    enabled: true,
                    queueEmptyThreshold: 1,
                    noSuggestionsTimeout: 5,
                    minimumConnectedUsers: 2,
                    timeBasedTriggers: {
                        enabled: false,
                        schedules: []
                    }
                },
                recommendationSettings: {
                    algorithm: 'favorites-only',
                    maxSongsToAdd: 3,
                    preventRecentRepeats: true,
                    recentRepeatWindowHours: 4,
                    algorithmWeights: {
                        collaborativeFiltering: 0.6,
                        contentBased: 0.2,
                        popularityBased: 0.1,
                        contextAware: 0.05,
                        favoritesAnalysis: 0.05
                    },
                    contentFilters: {
                        allowedGenres: [],
                        blockedGenres: [],
                        allowedYearRange: {},
                        requireAlbumArt: false
                    },
                    userPreferences: {
                        respectUserAutoQueuePrefs: true,
                        userVetoWeight: 0.5,
                        consensusThreshold: 0.8
                    }
                },
                behaviorSettings: {
                    diversityMode: 'low',
                    energyLevelConsideration: false,
                    timeOfDayAdaptation: false,
                    fallbackBehavior: {
                        enabled: true,
                        mode: 'recent-favorites',
                        fallbackGenres: []
                    },
                    adaptiveLearning: {
                        enabled: false,
                        trackUserSkips: false,
                        trackVotes: true,
                        learningRate: 0.05
                    }
                },
                notificationSettings: {
                    notifyOnAutoAdd: true,
                    notifyUsers: true,
                    showRecommendationReasons: true,
                    logAnalytics: false
                },
                createdAt: new Date('2024-01-01'),
                updatedAt: new Date(),
                createdBy: 'system',
                usageCount: 0
            },
            'discovery-mode': {
                id: 'discovery-mode',
                name: 'Discovery Mode',
                description: 'High diversity recommendations for music discovery',
                isActive: false,
                isDefault: true,
                triggerSettings: {
                    enabled: true,
                    queueEmptyThreshold: 3,
                    noSuggestionsTimeout: 8,
                    minimumConnectedUsers: 1,
                    timeBasedTriggers: {
                        enabled: false,
                        schedules: []
                    }
                },
                recommendationSettings: {
                    algorithm: 'hybrid',
                    maxSongsToAdd: 7,
                    preventRecentRepeats: true,
                    recentRepeatWindowHours: 6,
                    algorithmWeights: {
                        collaborativeFiltering: 0.2,
                        contentBased: 0.35,
                        popularityBased: 0.15,
                        contextAware: 0.2,
                        favoritesAnalysis: 0.1
                    },
                    contentFilters: {
                        allowedGenres: [],
                        blockedGenres: [],
                        allowedYearRange: {},
                        requireAlbumArt: false
                    },
                    userPreferences: {
                        respectUserAutoQueuePrefs: true,
                        userVetoWeight: 0.2,
                        consensusThreshold: 0.4
                    }
                },
                behaviorSettings: {
                    diversityMode: 'high',
                    energyLevelConsideration: true,
                    timeOfDayAdaptation: true,
                    fallbackBehavior: {
                        enabled: true,
                        mode: 'genre-based',
                        fallbackGenres: ['Indie', 'Alternative', 'Electronic', 'World']
                    },
                    adaptiveLearning: {
                        enabled: true,
                        trackUserSkips: true,
                        trackVotes: true,
                        learningRate: 0.15
                    }
                },
                notificationSettings: {
                    notifyOnAutoAdd: true,
                    notifyUsers: true,
                    showRecommendationReasons: true,
                    logAnalytics: true
                },
                createdAt: new Date('2024-01-01'),
                updatedAt: new Date(),
                createdBy: 'system',
                usageCount: 0
            }
        };
        return defaultConfigs[configId] || null;
    }
    static validateConfiguration(config) {
        const errors = [];
        if (!config.name || config.name.trim().length === 0) {
            errors.push('Configuration name is required');
        }
        if (config.recommendationSettings) {
            const { maxSongsToAdd, algorithmWeights } = config.recommendationSettings;
            if (maxSongsToAdd && (maxSongsToAdd < 1 || maxSongsToAdd > 20)) {
                errors.push('Max songs to add must be between 1 and 20');
            }
            if (algorithmWeights) {
                const totalWeight = Object.values(algorithmWeights).reduce((sum, weight) => sum + weight, 0);
                if (Math.abs(totalWeight - 1) > 0.001) {
                    errors.push('Algorithm weights must sum to 1.0');
                }
            }
        }
        if (config.triggerSettings) {
            const { queueEmptyThreshold, noSuggestionsTimeout, minimumConnectedUsers } = config.triggerSettings;
            if (queueEmptyThreshold && queueEmptyThreshold < 0) {
                errors.push('Queue empty threshold cannot be negative');
            }
            if (noSuggestionsTimeout && noSuggestionsTimeout < 1) {
                errors.push('No suggestions timeout must be at least 1 minute');
            }
            if (minimumConnectedUsers && minimumConnectedUsers < 0) {
                errors.push('Minimum connected users cannot be negative');
            }
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
exports.AutoQueueConfigService = AutoQueueConfigService;
//# sourceMappingURL=auto-queue-config-service.js.map