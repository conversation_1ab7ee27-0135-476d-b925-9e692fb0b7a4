"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.quizService = exports.QuizService = void 0;
const logger_1 = require("@/lib/logger");
const CACHE_TTL = 5 * 60 * 1000;
const MAX_CACHE_SIZE = 100;
class QuizService {
    constructor(config = {}) {
        this.cache = new Map();
        this._quizDataManager = null;
        this._mpdClient = null;
        this._librarySync = null;
        this._isInitialized = false;
        this.config = {
            enableCaching: true,
            cacheSize: MAX_CACHE_SIZE,
            cacheTTL: CACHE_TTL,
            ...config
        };
        logger_1.quizLogger.info('QuizService initialized', { config: this.config });
    }
    static getInstance(config) {
        if (!QuizService.instance) {
            QuizService.instance = new QuizService(config);
        }
        return QuizService.instance;
    }
    async ensureInitialized() {
        if (this._isInitialized)
            return;
        await logger_1.PerformanceLogger.measureAsync('quiz-service-init', async () => {
            logger_1.quizLogger.info('Initializing QuizService dependencies...');
            const [{ QuizDataManager }, { MPDLibrarySync }, { MPDClient }] = await Promise.all([
                Promise.resolve().then(() => __importStar(require('@/lib/database/quiz-data'))),
                Promise.resolve().then(() => __importStar(require('@/lib/database/mpd-sync'))),
                Promise.resolve().then(() => __importStar(require('@/lib/mpd-client')))
            ]);
            const { getAudioConfig } = await Promise.resolve().then(() => __importStar(require('@/lib/env')));
            const config = getAudioConfig();
            this._mpdClient = new MPDClient({
                host: config.mpdHost,
                port: config.mpdPort,
                password: config.mpdPassword,
                httpProxyPort: config.mpdHttpPort
            });
            this._librarySync = new MPDLibrarySync(this._mpdClient);
            this._quizDataManager = new QuizDataManager(this._librarySync);
            this._isInitialized = true;
            logger_1.quizLogger.info('QuizService dependencies initialized successfully');
        });
    }
    async generateQuestions(gameMode, settings) {
        await this.ensureInitialized();
        const cacheKey = this.generateCacheKey('questions', gameMode, settings);
        if (this.config.enableCaching) {
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                logger_1.quizLogger.debug('Returning cached questions', { gameMode, cacheKey });
                return cached;
            }
        }
        const questions = await logger_1.PerformanceLogger.measureAsync(`generate-questions-${gameMode}`, async () => {
            logger_1.quizLogger.info('Generating new questions', { gameMode, settings });
            return await this._quizDataManager.generateQuizQuestions(gameMode, settings);
        });
        if (this.config.enableCaching && questions.length > 0) {
            this.setCache(cacheKey, questions);
            logger_1.quizLogger.debug('Cached generated questions', { gameMode, count: questions.length });
        }
        return questions;
    }
    async getRandomTracks(count, filters = {}) {
        await this.ensureInitialized();
        const cacheKey = this.generateCacheKey('tracks', count, filters);
        if (this.config.enableCaching) {
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                logger_1.quizLogger.debug('Returning cached tracks', { count, cacheKey });
                return cached;
            }
        }
        const tracks = await logger_1.PerformanceLogger.measureAsync('get-random-tracks', async () => {
            return await this._librarySync.getRandomTracks(count, filters);
        });
        if (this.config.enableCaching && tracks.length > 0) {
            this.setCache(cacheKey, tracks);
            logger_1.quizLogger.debug('Cached random tracks', { count: tracks.length });
        }
        return tracks;
    }
    async preloadCommonData() {
        await this.ensureInitialized();
        logger_1.quizLogger.info('Preloading common quiz data...');
        const preloadTasks = [
            this.getRandomTracks(20, { minPopularity: 0 }),
            this.getRandomTracks(20, { genre: 'rock' }),
            this.getRandomTracks(20, { genre: 'pop' }),
            this.getAvailableArtists(50),
            this.getAvailableGenres(20)
        ];
        await Promise.allSettled(preloadTasks);
        logger_1.quizLogger.info('Common data preloaded successfully');
    }
    async getAvailableArtists(limit = 100) {
        await this.ensureInitialized();
        const cacheKey = `artists-${limit}`;
        const cached = this.getFromCache(cacheKey);
        if (cached)
            return cached;
        const artists = await this._librarySync.getAvailableArtists(limit);
        this.setCache(cacheKey, artists);
        return artists;
    }
    async getAvailableGenres(limit = 50) {
        await this.ensureInitialized();
        const cacheKey = `genres-${limit}`;
        const cached = this.getFromCache(cacheKey);
        if (cached)
            return cached;
        const genres = await this._librarySync.getAvailableGenres(limit);
        this.setCache(cacheKey, genres);
        return genres;
    }
    generateCacheKey(type, ...args) {
        const argsStr = JSON.stringify(args);
        return `${type}-${Buffer.from(argsStr).toString('base64').slice(0, 16)}`;
    }
    getFromCache(key) {
        const entry = this.cache.get(key);
        if (!entry)
            return null;
        if (Date.now() - entry.timestamp > this.config.cacheTTL) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    setCache(key, data) {
        if (this.cache.size >= this.config.cacheSize) {
            const oldestKey = this.cache.keys().next().value;
            this.cache.delete(oldestKey);
        }
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            key
        });
    }
    clearCache() {
        this.cache.clear();
        logger_1.quizLogger.info('Quiz service cache cleared');
    }
    getCacheStats() {
        return {
            size: this.cache.size,
            maxSize: this.config.cacheSize
        };
    }
    async cleanup() {
        this.clearCache();
        if (this._mpdClient) {
            await this._mpdClient.disconnect();
        }
        this._isInitialized = false;
        logger_1.quizLogger.info('QuizService cleaned up');
    }
}
exports.QuizService = QuizService;
QuizService.instance = null;
exports.quizService = QuizService.getInstance();
//# sourceMappingURL=quiz-service.js.map