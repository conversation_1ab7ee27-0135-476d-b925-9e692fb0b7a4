"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.performanceMonitor = exports.PerformanceMonitor = void 0;
const logger_1 = require("@/lib/logger");
class PerformanceMonitor {
    constructor() {
        this.metrics = [];
        this.maxMetrics = 1000;
        this.errorCount = 0;
        this.totalOperations = 0;
        this.setupPerformanceObservers();
        logger_1.logger.info('PerformanceMonitor initialized');
    }
    static getInstance() {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor();
        }
        return PerformanceMonitor.instance;
    }
    recordTiming(name, duration, tags) {
        this.addMetric({
            name,
            value: duration,
            timestamp: Date.now(),
            tags,
            type: 'timing'
        });
        this.totalOperations++;
        if (duration > 1000) {
            logger_1.logger.warn(`Slow operation detected: ${name}`, { duration, tags });
        }
    }
    recordCounter(name, value = 1, tags) {
        this.addMetric({
            name,
            value,
            timestamp: Date.now(),
            tags,
            type: 'counter'
        });
    }
    recordGauge(name, value, tags) {
        this.addMetric({
            name,
            value,
            timestamp: Date.now(),
            tags,
            type: 'gauge'
        });
    }
    recordError(operation, error, tags) {
        this.errorCount++;
        this.recordCounter('errors', 1, { operation, error: error.name, ...tags });
        logger_1.logger.error(`Operation failed: ${operation}`, error, tags);
    }
    measure(name, fn, tags) {
        const start = performance.now();
        try {
            const result = fn();
            const duration = performance.now() - start;
            this.recordTiming(name, duration, tags);
            return result;
        }
        catch (error) {
            const duration = performance.now() - start;
            this.recordTiming(name, duration, { ...tags, error: 'true' });
            this.recordError(name, error, tags);
            throw error;
        }
    }
    async measureAsync(name, fn, tags) {
        const start = performance.now();
        try {
            const result = await fn();
            const duration = performance.now() - start;
            this.recordTiming(name, duration, tags);
            return result;
        }
        catch (error) {
            const duration = performance.now() - start;
            this.recordTiming(name, duration, { ...tags, error: 'true' });
            this.recordError(name, error, tags);
            throw error;
        }
    }
    getReport(timeRangeMs = 5 * 60 * 1000) {
        const now = Date.now();
        const cutoff = now - timeRangeMs;
        const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoff);
        const timingMetrics = recentMetrics.filter(m => m.type === 'timing');
        const averageResponseTime = timingMetrics.length > 0
            ? timingMetrics.reduce((sum, m) => sum + m.value, 0) / timingMetrics.length
            : 0;
        const slowestOperations = timingMetrics
            .sort((a, b) => b.value - a.value)
            .slice(0, 10)
            .map(m => ({ name: m.name, duration: m.value }));
        const errorRate = this.totalOperations > 0 ? (this.errorCount / this.totalOperations) * 100 : 0;
        return {
            metrics: recentMetrics,
            summary: {
                totalMetrics: recentMetrics.length,
                averageResponseTime,
                slowestOperations,
                errorRate
            },
            timeRange: {
                start: cutoff,
                end: now
            }
        };
    }
    getMetricsByName(name, timeRangeMs = 5 * 60 * 1000) {
        const cutoff = Date.now() - timeRangeMs;
        return this.metrics.filter(m => m.name === name && m.timestamp >= cutoff);
    }
    getSystemInfo() {
        const info = {
            timestamp: Date.now(),
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
            language: typeof navigator !== 'undefined' ? navigator.language : 'unknown'
        };
        if (typeof performance !== 'undefined' && 'memory' in performance) {
            const memory = performance.memory;
            info.memory = {
                usedJSHeapSize: memory.usedJSHeapSize,
                totalJSHeapSize: memory.totalJSHeapSize,
                jsHeapSizeLimit: memory.jsHeapSizeLimit
            };
        }
        if (typeof navigator !== 'undefined' && 'connection' in navigator) {
            const connection = navigator.connection;
            info.connection = {
                effectiveType: connection.effectiveType,
                downlink: connection.downlink,
                rtt: connection.rtt
            };
        }
        return info;
    }
    cleanup() {
        const cutoff = Date.now() - (30 * 60 * 1000);
        this.metrics = this.metrics.filter(m => m.timestamp >= cutoff);
        if (this.totalOperations > 10000) {
            this.totalOperations = Math.floor(this.totalOperations / 2);
            this.errorCount = Math.floor(this.errorCount / 2);
        }
    }
    exportMetrics() {
        const report = this.getReport();
        return JSON.stringify(report, null, 2);
    }
    addMetric(metric) {
        this.metrics.push(metric);
        if (this.metrics.length > this.maxMetrics) {
            this.metrics.shift();
        }
    }
    setupPerformanceObservers() {
        if (typeof window === 'undefined')
            return;
        if ('PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.entryType === 'navigation') {
                            const navEntry = entry;
                            this.recordTiming('page-load', navEntry.loadEventEnd - navEntry.fetchStart);
                            this.recordTiming('dom-content-loaded', navEntry.domContentLoadedEventEnd - navEntry.fetchStart);
                        }
                        else if (entry.entryType === 'paint') {
                            this.recordTiming(`paint-${entry.name}`, entry.startTime);
                        }
                    }
                });
                observer.observe({ entryTypes: ['navigation', 'paint'] });
            }
            catch (error) {
                logger_1.logger.warn('Failed to setup PerformanceObserver', { error });
            }
        }
        setInterval(() => {
            const systemInfo = this.getSystemInfo();
            if (systemInfo.memory) {
                this.recordGauge('memory-used', systemInfo.memory.usedJSHeapSize);
                this.recordGauge('memory-total', systemInfo.memory.totalJSHeapSize);
            }
        }, 30000);
    }
}
exports.PerformanceMonitor = PerformanceMonitor;
PerformanceMonitor.instance = null;
exports.performanceMonitor = PerformanceMonitor.getInstance();
if (typeof window !== 'undefined') {
    setInterval(() => {
        exports.performanceMonitor.cleanup();
    }, 5 * 60 * 1000);
}
//# sourceMappingURL=performance-monitor.js.map