import { UserSession, User } from '@prisma/client';
export type SessionType = 'jukebox' | 'quiz' | 'general';
export interface ActiveUser extends User {
    currentSession?: UserSession;
    lastSeenMinutesAgo: number;
}
export interface SessionMetadata {
    browserInfo?: string;
    screenResolution?: string;
    connectionType?: string;
    location?: string;
    [key: string]: any;
}
export declare class UserSessionService {
    private static readonly SESSION_TIMEOUT_MINUTES;
    private static readonly HEARTBEAT_INTERVAL_MINUTES;
    static startSession(userId: string, sessionType?: SessionType, ipAddress?: string, userAgent?: string, metadata?: SessionMetadata): Promise<UserSession>;
    static updateActivity(sessionId: string, metadata?: Partial<SessionMetadata>): Promise<void>;
    static endSession(sessionId: string): Promise<void>;
    static endActiveSessions(userId: string, sessionType?: SessionType): Promise<void>;
    static getActiveUsers(sessionType?: SessionType, includeRecentlyDisconnected?: boolean): Promise<ActiveUser[]>;
    static getSessionStats(sessionType?: SessionType): Promise<{
        activeCount: number;
        totalSessions: number;
        averageSessionDuration: number;
        recentlyActive: number;
    }>;
    static cleanupOldSessions(): Promise<number>;
    static markTimeoutSessions(): Promise<number>;
}
