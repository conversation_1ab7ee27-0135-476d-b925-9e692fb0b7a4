import type { SessionType } from './session-service';
export interface Song {
    filePath: string;
    title: string;
    artist: string;
    album?: string;
    duration?: number;
    genre?: string;
    year?: number;
    albumArtUrl?: string;
}
export interface RecommendationResult {
    song: Song;
    score: number;
    algorithm: string;
    reasons: string[];
    confidence: number;
    metadata: Record<string, any>;
}
export interface RecommendationContext {
    sessionType: SessionType;
    connectedUserIds: string[];
    currentQueue: Song[];
    recentlyPlayed: Song[];
    timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
    dayOfWeek: 'weekday' | 'weekend';
    excludeFilePaths: string[];
}
export interface CollaborativeFilteringResult {
    similarities: Map<string, number>;
    recommendations: Song[];
    userProfiles: Map<string, UserMusicProfile>;
}
export interface UserMusicProfile {
    userId: string;
    favoriteGenres: string[];
    favoriteArtists: string[];
    averageYear: number;
    genreDistribution: Map<string, number>;
    artistDistribution: Map<string, number>;
    yearDistribution: Map<number, number>;
    listeningPatterns: {
        preferredDuration: number;
        diversityScore: number;
        popularityPreference: number;
    };
}
export declare class RecommendationService {
    static getIntelligentRecommendations(context: RecommendationContext, maxResults?: number): Promise<RecommendationResult[]>;
    private static collaborativeFiltering;
    private static contentBasedFiltering;
    private static popularityBasedRecommendations;
    private static contextAwareRecommendations;
    private static hybridRecommendations;
    private static buildUserProfiles;
    private static calculateUserSimilarities;
    private static calculateProfileSimilarity;
    private static deduplicateRecommendations;
    private static findSimilarUsers;
    private static getRecommendationsFromSimilarUsers;
    private static calculateCollaborativeScore;
    private static getUserFavorites;
    private static analyzeContentPreferences;
    private static findContentSimilarSongs;
    private static calculateContentScore;
    private static getPopularSongs;
    private static getContextualPreferences;
    private static findContextualSongs;
    private static calculateContextualScore;
    private static calculateHybridScore;
    private static combineReasons;
    private static calculateProfileDiversityScore;
}
