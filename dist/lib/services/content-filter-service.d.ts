import type { ContentFilters, FilterPreset, GameFilterSettings } from '@/lib/types/filters';
import type { QuizTrack } from '@/lib/types';
export declare class ContentFilterService {
    private static instance;
    private currentFilters;
    private filterSettings;
    private readonly SPECIAL_CATEGORIES;
    private constructor();
    static getInstance(): ContentFilterService;
    loadSettings(): GameFilterSettings;
    saveSettings(settings: GameFilterSettings): void;
    applyPreset(presetId: string): void;
    applyCustomFilters(filters: ContentFilters): void;
    getPreset(id: string): FilterPreset | undefined;
    getPresets(): FilterPreset[];
    isFilteringEnabled(): boolean;
    setFilteringEnabled(enabled: boolean): void;
    getCurrentFilters(): ContentFilters;
    getFiltersForMode(gameMode: string): ContentFilters;
    filterTracks(tracks: QuizTrack[], gameMode?: string): QuizTrack[];
    trackMatchesFilters(track: QuizTrack, filters: ContentFilters): boolean;
    getFilterStatistics(totalTracks: number): Promise<{
        totalTracks: number;
        filteredTracks: number;
        percentageRemaining: number;
        removedByFilter: {
            genres?: number;
            years?: number;
            charts?: number;
            quality?: number;
            metadata?: number;
        };
    }>;
    exportFilters(): string;
    importFilters(jsonString: string): boolean;
}
