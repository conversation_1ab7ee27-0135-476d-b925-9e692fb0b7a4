import type { SessionType } from './session-service';
export interface AutoQueueStatus {
    isActive: boolean;
    isRunning: boolean;
    lastTriggerTime?: Date;
    lastAddedSongs?: Date;
    songsAddedCount: number;
    configurationId: string;
    configurationName: string;
    nextCheckTime?: Date;
    triggerReason?: string;
}
export interface QueueAnalysis {
    currentLength: number;
    isEmpty: boolean;
    needsRefill: boolean;
    recommendedSongsToAdd: number;
    triggerCondition: string;
    lastAnalysisTime: Date;
}
export interface AutoQueueEvent {
    type: 'queue-analyzed' | 'songs-added' | 'trigger-activated' | 'error' | 'config-changed';
    timestamp: Date;
    data: any;
    userId?: string;
    configId?: string;
}
export type AutoQueueEventListener = (event: AutoQueueEvent) => void;
export declare class AutoQueueIntegrationService {
    private static eventListeners;
    private static isRunning;
    private static currentUserId;
    private static lastStatus;
    private static checkInterval;
    static start(userId: string, sessionType?: SessionType): Promise<void>;
    static stop(): void;
    static getStatus(): AutoQueueStatus | null;
    static trigger(userId: string, sessionType?: SessionType, reason?: string): Promise<{
        success: boolean;
        songsAdded: number;
        message: string;
    }>;
    static addEventListener(listener: AutoQueueEventListener): void;
    static removeEventListener(listener: AutoQueueEventListener): void;
    private static startMonitoringLoop;
    private static performPeriodicCheck;
    private static analyzeCurrentQueue;
    private static analyzeAndAct;
    private static emitEvent;
    static getAnalytics(): {
        totalSongsAdded: number;
        lastActivity?: Date;
        activeConfiguration?: string;
        isCurrentlyRunning: boolean;
    };
}
