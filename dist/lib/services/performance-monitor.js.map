{"version": 3, "file": "performance-monitor.js", "sourceRoot": "", "sources": ["../../../lib/services/performance-monitor.ts"], "names": [], "mappings": ";;;AAKA,yCAAqC;AA2BrC,MAAa,kBAAkB;IAO7B;QALQ,YAAO,GAAwB,EAAE,CAAA;QACxB,eAAU,GAAG,IAAI,CAAA;QAC1B,eAAU,GAAG,CAAC,CAAA;QACd,oBAAe,GAAG,CAAC,CAAA;QAIzB,IAAI,CAAC,yBAAyB,EAAE,CAAA;QAChC,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;IAC/C,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACjC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAA;QACxD,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAA;IACpC,CAAC;IAKD,YAAY,CAAC,IAAY,EAAE,QAAgB,EAAE,IAA6B;QACxE,IAAI,CAAC,SAAS,CAAC;YACb,IAAI;YACJ,KAAK,EAAE,QAAQ;YACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI;YACJ,IAAI,EAAE,QAAQ;SACf,CAAC,CAAA;QAEF,IAAI,CAAC,eAAe,EAAE,CAAA;QAGtB,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,eAAM,CAAC,IAAI,CAAC,4BAA4B,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAA;QACrE,CAAC;IACH,CAAC;IAKD,aAAa,CAAC,IAAY,EAAE,QAAgB,CAAC,EAAE,IAA6B;QAC1E,IAAI,CAAC,SAAS,CAAC;YACb,IAAI;YACJ,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI;YACJ,IAAI,EAAE,SAAS;SAChB,CAAC,CAAA;IACJ,CAAC;IAKD,WAAW,CAAC,IAAY,EAAE,KAAa,EAAE,IAA6B;QACpE,IAAI,CAAC,SAAS,CAAC;YACb,IAAI;YACJ,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI;YACJ,IAAI,EAAE,OAAO;SACd,CAAC,CAAA;IACJ,CAAC;IAKD,WAAW,CAAC,SAAiB,EAAE,KAAY,EAAE,IAA6B;QACxE,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,CAAA;QAC1E,eAAM,CAAC,KAAK,CAAC,qBAAqB,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;IAC7D,CAAC;IAKD,OAAO,CAAI,IAAY,EAAE,EAAW,EAAE,IAA6B;QACjE,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAA;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,EAAE,EAAE,CAAA;YACnB,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;YAC1C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACvC,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;YAC1C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAA;YAC7D,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAc,EAAE,IAAI,CAAC,CAAA;YAC5C,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAAI,IAAY,EAAE,EAAoB,EAAE,IAA6B;QACrF,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAA;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,EAAE,EAAE,CAAA;YACzB,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;YAC1C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACvC,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;YAC1C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAA;YAC7D,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAc,EAAE,IAAI,CAAC,CAAA;YAC5C,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,SAAS,CAAC,cAAsB,CAAC,GAAG,EAAE,GAAG,IAAI;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAA;QAEhC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,CAAA;QACrE,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAA;QAEpE,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC;YAClD,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM;YAC3E,CAAC,CAAC,CAAC,CAAA;QAEL,MAAM,iBAAiB,GAAG,aAAa;aACpC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;aACZ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QAElD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAE/F,OAAO;YACL,OAAO,EAAE,aAAa;YACtB,OAAO,EAAE;gBACP,YAAY,EAAE,aAAa,CAAC,MAAM;gBAClC,mBAAmB;gBACnB,iBAAiB;gBACjB,SAAS;aACV;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,MAAM;gBACb,GAAG,EAAE,GAAG;aACT;SACF,CAAA;IACH,CAAC;IAKD,gBAAgB,CAAC,IAAY,EAAE,cAAsB,CAAC,GAAG,EAAE,GAAG,IAAI;QAChE,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAA;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,CAAA;IAC3E,CAAC;IAKD,aAAa;QACX,MAAM,IAAI,GAAwB;YAChC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,SAAS,EAAE,OAAO,SAAS,KAAK,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;YAC7E,QAAQ,EAAE,OAAO,SAAS,KAAK,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;SAC5E,CAAA;QAGD,IAAI,OAAO,WAAW,KAAK,WAAW,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;YAClE,MAAM,MAAM,GAAI,WAAmB,CAAC,MAAM,CAAA;YAC1C,IAAI,CAAC,MAAM,GAAG;gBACZ,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,eAAe,EAAE,MAAM,CAAC,eAAe;gBACvC,eAAe,EAAE,MAAM,CAAC,eAAe;aACxC,CAAA;QACH,CAAC;QAGD,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,YAAY,IAAI,SAAS,EAAE,CAAC;YAClE,MAAM,UAAU,GAAI,SAAiB,CAAC,UAAU,CAAA;YAChD,IAAI,CAAC,UAAU,GAAG;gBAChB,aAAa,EAAE,UAAU,CAAC,aAAa;gBACvC,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,GAAG,EAAE,UAAU,CAAC,GAAG;aACpB,CAAA;QACH,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,OAAO;QACL,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,CAAA;QAG9D,IAAI,IAAI,CAAC,eAAe,GAAG,KAAK,EAAE,CAAC;YACjC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAA;YAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;QACnD,CAAC;IACH,CAAC;IAKD,aAAa;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;QAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;IACxC,CAAC;IAEO,SAAS,CAAC,MAAyB;QACzC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAGzB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1C,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;QACtB,CAAC;IACH,CAAC;IAEO,yBAAyB;QAC/B,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAM;QAGzC,IAAI,qBAAqB,IAAI,MAAM,EAAE,CAAC;YACpC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,IAAI,mBAAmB,CAAC,CAAC,IAAI,EAAE,EAAE;oBAChD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;wBACtC,IAAI,KAAK,CAAC,SAAS,KAAK,YAAY,EAAE,CAAC;4BACrC,MAAM,QAAQ,GAAG,KAAoC,CAAA;4BACrD,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAA;4BAC3E,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE,QAAQ,CAAC,wBAAwB,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAA;wBAClG,CAAC;6BAAM,IAAI,KAAK,CAAC,SAAS,KAAK,OAAO,EAAE,CAAC;4BACvC,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAA;wBAC3D,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAA;gBAEF,QAAQ,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,CAAC,CAAA;YAC3D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;YAC/D,CAAC;QACH,CAAC;QAGD,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;YACvC,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBACtB,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAA;gBACjE,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,CAAA;YACrE,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAA;IACX,CAAC;;AA3PH,gDA4PC;AA3PgB,2BAAQ,GAA8B,IAAI,AAAlC,CAAkC;AA8P9C,QAAA,kBAAkB,GAAG,kBAAkB,CAAC,WAAW,EAAE,CAAA;AAGlE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IAClC,WAAW,CAAC,GAAG,EAAE;QACf,0BAAkB,CAAC,OAAO,EAAE,CAAA;IAC9B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;AACnB,CAAC"}