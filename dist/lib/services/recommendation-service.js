"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationService = void 0;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
class RecommendationService {
    static async getIntelligentRecommendations(context, maxResults = 20) {
        try {
            const [collaborativeResults, contentResults, popularityResults, contextResults] = await Promise.all([
                this.collaborativeFiltering(context),
                this.contentBasedFiltering(context),
                this.popularityBasedRecommendations(context),
                this.contextAwareRecommendations(context)
            ]);
            const results = [
                ...collaborativeResults,
                ...contentResults,
                ...popularityResults,
                ...contextResults
            ];
            const hybridResults = await this.hybridRecommendations(context, results);
            results.push(...hybridResults);
            const uniqueResults = this.deduplicateRecommendations(results);
            const sortedResults = uniqueResults.sort((a, b) => b.score - a.score);
            return sortedResults.slice(0, maxResults);
        }
        catch (error) {
            console.error('[RecommendationService] Intelligence recommendations failed:', error);
            return [];
        }
    }
    static async collaborativeFiltering(context) {
        try {
            const results = [];
            if (context.connectedUserIds.length < 2) {
                return results;
            }
            const userProfiles = await this.buildUserProfiles(context.connectedUserIds);
            const similarities = this.calculateUserSimilarities(userProfiles);
            for (const userId of context.connectedUserIds) {
                const similarUsers = this.findSimilarUsers(userId, similarities, 3);
                if (similarUsers.length > 0) {
                    const recommendations = await this.getRecommendationsFromSimilarUsers(userId, similarUsers, context.excludeFilePaths);
                    recommendations.forEach(song => {
                        const score = this.calculateCollaborativeScore(song, similarities, similarUsers);
                        results.push({
                            song,
                            score,
                            algorithm: 'collaborative-filtering',
                            reasons: [
                                `Similar users also like this track`,
                                `Based on ${similarUsers.length} similar users`,
                                `${song.genre ? `${song.genre} genre` : 'Similar musical taste'}`
                            ],
                            confidence: Math.min(similarUsers.length / 3, 1),
                            metadata: {
                                similarUsers: similarUsers.map(u => u.userId),
                                avgSimilarity: similarUsers.reduce((sum, u) => sum + u.similarity, 0) / similarUsers.length
                            }
                        });
                    });
                }
            }
            return results;
        }
        catch (error) {
            console.error('[RecommendationService] Collaborative filtering failed:', error);
            return [];
        }
    }
    static async contentBasedFiltering(context) {
        try {
            const results = [];
            const userFavorites = await this.getUserFavorites(context.connectedUserIds);
            if (userFavorites.length === 0)
                return results;
            const contentProfile = this.analyzeContentPreferences(userFavorites);
            const similarSongs = await this.findContentSimilarSongs(contentProfile, context.excludeFilePaths, 20);
            similarSongs.forEach(song => {
                const score = this.calculateContentScore(song, contentProfile);
                results.push({
                    song,
                    score,
                    algorithm: 'content-based',
                    reasons: [
                        song.genre && contentProfile.preferredGenres.includes(song.genre)
                            ? `Matches preferred ${song.genre} genre`
                            : 'Similar musical style',
                        song.artist ? `Artist: ${song.artist}` : 'Similar artist style',
                        song.year ? `From ${song.year}` : 'Similar era'
                    ].filter(Boolean),
                    confidence: 0.7,
                    metadata: {
                        genreMatch: song.genre && contentProfile.preferredGenres.includes(song.genre),
                        yearRange: contentProfile.preferredYearRange,
                        artistSimilarity: 0.5
                    }
                });
            });
            return results;
        }
        catch (error) {
            console.error('[RecommendationService] Content-based filtering failed:', error);
            return [];
        }
    }
    static async popularityBasedRecommendations(context) {
        try {
            const results = [];
            const popularSongs = await this.getPopularSongs(context.excludeFilePaths, 15);
            popularSongs.forEach((song, index) => {
                const score = Math.max(50, 80 - (index * 2));
                results.push({
                    song,
                    score,
                    algorithm: 'popularity-based',
                    reasons: [
                        'Popular among all users',
                        'Frequently favorited track',
                        song.genre ? `Popular ${song.genre} song` : 'Crowd favorite'
                    ],
                    confidence: 0.6,
                    metadata: {
                        popularityRank: index + 1,
                        favoriteCount: song.metadata?.favoriteCount || 0
                    }
                });
            });
            return results;
        }
        catch (error) {
            console.error('[RecommendationService] Popularity-based recommendations failed:', error);
            return [];
        }
    }
    static async contextAwareRecommendations(context) {
        try {
            const results = [];
            const contextPrefs = this.getContextualPreferences(context);
            const contextualSongs = await this.findContextualSongs(contextPrefs, context.excludeFilePaths, 10);
            contextualSongs.forEach(song => {
                const score = this.calculateContextualScore(song, context, contextPrefs);
                results.push({
                    song,
                    score,
                    algorithm: 'context-aware',
                    reasons: [
                        `Perfect for ${context.timeOfDay}`,
                        `Great for ${context.dayOfWeek}`,
                        song.genre ? `${song.genre} fits the mood` : 'Matches the current vibe'
                    ],
                    confidence: 0.8,
                    metadata: {
                        timeOfDay: context.timeOfDay,
                        dayOfWeek: context.dayOfWeek,
                        contextScore: score
                    }
                });
            });
            return results;
        }
        catch (error) {
            console.error('[RecommendationService] Context-aware recommendations failed:', error);
            return [];
        }
    }
    static async hybridRecommendations(context, existingResults) {
        try {
            const results = [];
            const songGroups = new Map();
            existingResults.forEach(result => {
                const key = `${result.song.artist}-${result.song.title}`.toLowerCase();
                if (!songGroups.has(key)) {
                    songGroups.set(key, []);
                }
                songGroups.get(key).push(result);
            });
            songGroups.forEach((algorithmResults, key) => {
                if (algorithmResults.length >= 2) {
                    const hybridScore = this.calculateHybridScore(algorithmResults);
                    const combinedReasons = this.combineReasons(algorithmResults);
                    const avgConfidence = algorithmResults.reduce((sum, r) => sum + r.confidence, 0) / algorithmResults.length;
                    results.push({
                        song: algorithmResults[0].song,
                        score: hybridScore,
                        algorithm: 'hybrid',
                        reasons: [
                            `Recommended by ${algorithmResults.length} algorithms`,
                            ...combinedReasons.slice(0, 3)
                        ],
                        confidence: Math.min(avgConfidence * 1.2, 1),
                        metadata: {
                            algorithms: algorithmResults.map(r => r.algorithm),
                            originalScores: algorithmResults.map(r => r.score),
                            consensusStrength: algorithmResults.length
                        }
                    });
                }
            });
            return results;
        }
        catch (error) {
            console.error('[RecommendationService] Hybrid recommendations failed:', error);
            return [];
        }
    }
    static async buildUserProfiles(userIds) {
        const profiles = new Map();
        const allFavorites = await prisma_1.default.userFavorite.findMany({
            where: { userId: { in: userIds } },
            select: {
                userId: true,
                genre: true,
                artist: true,
                year: true,
                duration: true,
                title: true
            }
        });
        const favoritesByUser = allFavorites.reduce((acc, favorite) => {
            if (!acc[favorite.userId])
                acc[favorite.userId] = [];
            acc[favorite.userId].push(favorite);
            return acc;
        }, {});
        for (const userId of userIds) {
            const favorites = favoritesByUser[userId] || [];
            if (favorites.length === 0)
                continue;
            const genreDistribution = new Map();
            favorites.forEach(fav => {
                const genre = fav.genre || 'Unknown';
                genreDistribution.set(genre, (genreDistribution.get(genre) || 0) + 1);
            });
            const artistDistribution = new Map();
            favorites.forEach(fav => {
                artistDistribution.set(fav.artist, (artistDistribution.get(fav.artist) || 0) + 1);
            });
            const yearDistribution = new Map();
            const validYears = favorites.filter(f => f.year).map(f => f.year);
            validYears.forEach(year => {
                yearDistribution.set(year, (yearDistribution.get(year) || 0) + 1);
            });
            const averageYear = validYears.length > 0
                ? validYears.reduce((sum, year) => sum + year, 0) / validYears.length
                : new Date().getFullYear();
            const durations = favorites.filter(f => f.duration).map(f => f.duration);
            const preferredDuration = durations.length > 0
                ? durations.reduce((sum, d) => sum + d, 0) / durations.length
                : 240;
            const diversityScore = this.calculateProfileDiversityScore(genreDistribution, artistDistribution, favorites.length);
            profiles.set(userId, {
                userId,
                favoriteGenres: Array.from(genreDistribution.keys()).slice(0, 5),
                favoriteArtists: Array.from(artistDistribution.keys()).slice(0, 10),
                averageYear,
                genreDistribution,
                artistDistribution,
                yearDistribution,
                listeningPatterns: {
                    preferredDuration,
                    diversityScore,
                    popularityPreference: 0.5
                }
            });
        }
        return profiles;
    }
    static calculateUserSimilarities(profiles) {
        const similarities = new Map();
        const userIds = Array.from(profiles.keys());
        for (let i = 0; i < userIds.length; i++) {
            const userId1 = userIds[i];
            const profile1 = profiles.get(userId1);
            if (!similarities.has(userId1)) {
                similarities.set(userId1, new Map());
            }
            for (let j = i + 1; j < userIds.length; j++) {
                const userId2 = userIds[j];
                const profile2 = profiles.get(userId2);
                if (!similarities.has(userId2)) {
                    similarities.set(userId2, new Map());
                }
                const similarity = this.calculateProfileSimilarity(profile1, profile2);
                similarities.get(userId1).set(userId2, similarity);
                similarities.get(userId2).set(userId1, similarity);
            }
        }
        return similarities;
    }
    static calculateProfileSimilarity(profile1, profile2) {
        const genreSet1 = new Set(profile1.favoriteGenres);
        const genreSet2 = new Set(profile2.favoriteGenres);
        const genreIntersection = new Set([...genreSet1].filter(x => genreSet2.has(x)));
        const genreUnion = new Set([...genreSet1, ...genreSet2]);
        const genreSimilarity = genreUnion.size > 0 ? genreIntersection.size / genreUnion.size : 0;
        const artistSet1 = new Set(profile1.favoriteArtists);
        const artistSet2 = new Set(profile2.favoriteArtists);
        const artistIntersection = new Set([...artistSet1].filter(x => artistSet2.has(x)));
        const artistUnion = new Set([...artistSet1, ...artistSet2]);
        const artistSimilarity = artistUnion.size > 0 ? artistIntersection.size / artistUnion.size : 0;
        const yearDiff = Math.abs(profile1.averageYear - profile2.averageYear);
        const yearSimilarity = Math.max(0, 1 - (yearDiff / 20));
        const durationDiff = Math.abs(profile1.listeningPatterns.preferredDuration - profile2.listeningPatterns.preferredDuration);
        const durationSimilarity = Math.max(0, 1 - (durationDiff / 300));
        const diversitySimilarity = 1 - Math.abs(profile1.listeningPatterns.diversityScore - profile2.listeningPatterns.diversityScore);
        return (genreSimilarity * 0.4) +
            (artistSimilarity * 0.3) +
            (yearSimilarity * 0.15) +
            (durationSimilarity * 0.1) +
            (diversitySimilarity * 0.05);
    }
    static deduplicateRecommendations(recommendations) {
        const seen = new Set();
        const unique = [];
        recommendations.forEach(rec => {
            const key = `${rec.song.artist}-${rec.song.title}`.toLowerCase();
            if (!seen.has(key)) {
                seen.add(key);
                unique.push(rec);
            }
        });
        return unique;
    }
    static findSimilarUsers(userId, similarities, limit) {
        const userSims = similarities.get(userId) || new Map();
        return Array.from(userSims.entries())
            .sort(([, a], [, b]) => b - a)
            .slice(0, limit)
            .map(([id, similarity]) => ({ userId: id, similarity }));
    }
    static async getRecommendationsFromSimilarUsers(userId, similarUsers, excludePaths) {
        const userIds = similarUsers.map(u => u.userId);
        const favorites = await prisma_1.default.userFavorite.findMany({
            where: {
                userId: { in: userIds },
                filePath: { notIn: excludePaths }
            },
            take: 10
        });
        return favorites.map(f => ({
            filePath: f.filePath,
            title: f.title,
            artist: f.artist,
            album: f.album,
            duration: f.duration,
            genre: f.genre,
            year: f.year,
            albumArtUrl: f.albumArtUrl
        }));
    }
    static calculateCollaborativeScore(song, similarities, similarUsers) {
        const avgSimilarity = similarUsers.reduce((sum, u) => sum + u.similarity, 0) / similarUsers.length;
        return Math.round(avgSimilarity * 80);
    }
    static async getUserFavorites(userIds) {
        return await prisma_1.default.userFavorite.findMany({
            where: { userId: { in: userIds } },
            select: {
                filePath: true,
                title: true,
                artist: true,
                album: true,
                duration: true,
                genre: true,
                year: true,
                albumArtUrl: true
            }
        });
    }
    static analyzeContentPreferences(favorites) {
        const genres = new Set(favorites.map(f => f.genre).filter(Boolean));
        const artists = new Set(favorites.map(f => f.artist));
        const years = favorites.map(f => f.year).filter(Boolean);
        return {
            preferredGenres: Array.from(genres),
            preferredArtists: Array.from(artists),
            preferredYearRange: {
                min: Math.min(...years),
                max: Math.max(...years),
                avg: years.reduce((sum, y) => sum + y, 0) / years.length
            }
        };
    }
    static async findContentSimilarSongs(contentProfile, excludePaths, limit) {
        const songs = await prisma_1.default.userFavorite.findMany({
            where: {
                filePath: { notIn: excludePaths },
                OR: [
                    { genre: { in: contentProfile.preferredGenres } },
                    { artist: { in: contentProfile.preferredArtists } }
                ]
            },
            take: limit,
            distinct: ['filePath']
        });
        return songs.map(s => ({
            filePath: s.filePath,
            title: s.title,
            artist: s.artist,
            album: s.album,
            duration: s.duration,
            genre: s.genre,
            year: s.year,
            albumArtUrl: s.albumArtUrl
        }));
    }
    static calculateContentScore(song, contentProfile) {
        let score = 50;
        if (song.genre && contentProfile.preferredGenres.includes(song.genre))
            score += 20;
        if (song.artist && contentProfile.preferredArtists.includes(song.artist))
            score += 15;
        if (song.year && song.year >= contentProfile.preferredYearRange.min && song.year <= contentProfile.preferredYearRange.max)
            score += 10;
        return Math.min(score, 85);
    }
    static async getPopularSongs(excludePaths, limit) {
        const popularFavorites = await prisma_1.default.userFavorite.groupBy({
            by: ['filePath', 'title', 'artist', 'album', 'genre', 'year'],
            where: { filePath: { notIn: excludePaths } },
            _count: { filePath: true },
            orderBy: { _count: { filePath: 'desc' } },
            take: limit
        });
        return popularFavorites.map(f => ({
            filePath: f.filePath,
            title: f.title,
            artist: f.artist,
            album: f.album,
            genre: f.genre,
            year: f.year,
            metadata: { favoriteCount: f._count.filePath }
        }));
    }
    static getContextualPreferences(context) {
        const preferences = {
            timeOfDay: context.timeOfDay,
            dayOfWeek: context.dayOfWeek
        };
        if (context.timeOfDay === 'morning') {
            preferences.energyLevel = 'medium';
            preferences.preferredGenres = ['Pop', 'Rock', 'Indie'];
        }
        else if (context.timeOfDay === 'evening') {
            preferences.energyLevel = 'low';
            preferences.preferredGenres = ['Jazz', 'Ambient', 'Classical'];
        }
        return preferences;
    }
    static async findContextualSongs(contextPrefs, excludePaths, limit) {
        return await this.findContentSimilarSongs(contextPrefs, excludePaths, limit);
    }
    static calculateContextualScore(song, context, contextPrefs) {
        let score = 60;
        if (song.genre && contextPrefs.preferredGenres?.includes(song.genre))
            score += 20;
        return score;
    }
    static calculateHybridScore(results) {
        const weightedSum = results.reduce((sum, r) => sum + (r.score * r.confidence), 0);
        const totalWeight = results.reduce((sum, r) => sum + r.confidence, 0);
        return Math.round(totalWeight > 0 ? weightedSum / totalWeight : 0);
    }
    static combineReasons(results) {
        const allReasons = results.flatMap(r => r.reasons);
        return [...new Set(allReasons)];
    }
    static calculateProfileDiversityScore(genreMap, artistMap, totalFavorites) {
        const genreCount = genreMap.size;
        const artistCount = artistMap.size;
        return Math.min((genreCount + artistCount) / totalFavorites, 1);
    }
}
exports.RecommendationService = RecommendationService;
//# sourceMappingURL=recommendation-service.js.map