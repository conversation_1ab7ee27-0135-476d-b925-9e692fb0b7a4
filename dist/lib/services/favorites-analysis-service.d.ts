import type { SessionType } from './session-service';
export interface FavoriteTrack {
    id: string;
    userId: string;
    songId?: string;
    filePath: string;
    title: string;
    artist: string;
    album?: string;
    duration?: number;
    genre?: string;
    year?: number;
    albumArtUrl?: string;
    addedAt: Date;
}
export interface FavoritesAnalysis {
    connectedUsers: string[];
    totalConnectedUsers: number;
    topSharedFavorites: FavoriteAnalysisResult[];
    personalizedRecommendations: FavoriteAnalysisResult[];
    popularGenres: GenreAnalysis[];
    popularArtists: ArtistAnalysis[];
    yearDistribution: YearAnalysis[];
    totalUniqueFavorites: number;
    averageFavoritesPerUser: number;
    diversityScore: number;
    analysisTimestamp: Date;
    sessionType: string;
}
export interface FavoriteAnalysisResult {
    track: FavoriteTrack;
    score: number;
    reasons: string[];
    connectedUserCount: number;
    userIds: string[];
}
export interface GenreAnalysis {
    genre: string;
    count: number;
    userCount: number;
    percentage: number;
    tracks: string[];
}
export interface ArtistAnalysis {
    artist: string;
    count: number;
    userCount: number;
    percentage: number;
    topTracks: string[];
}
export interface YearAnalysis {
    decade: string;
    year?: number;
    count: number;
    percentage: number;
}
export declare class FavoritesAnalysisService {
    static analyzeConnectedUsersFavorites(sessionType?: SessionType): Promise<FavoritesAnalysis>;
    static getAutoQueueRecommendations(sessionType?: SessionType, limit?: number, excludeFilePaths?: string[]): Promise<FavoriteAnalysisResult[]>;
    private static getFavoritesForUsers;
    private static performFavoritesAnalysis;
    private static findSharedFavorites;
    private static scoreSharedFavorites;
    private static generatePersonalizedRecommendations;
    private static analyzeGenres;
    private static analyzeArtists;
    private static analyzeYears;
    private static calculateFreshnessScore;
    private static calculateDiversityScore;
    private static createEmptyAnalysis;
    static getQuickStats(sessionType?: SessionType): Promise<{
        connectedUsers: number;
        totalFavorites: number;
        uniqueTracks: number;
        topGenres: {
            genre: string;
            count: number;
        }[];
        topArtists: {
            artist: string;
            count: number;
        }[];
    }>;
}
