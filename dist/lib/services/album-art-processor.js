"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlbumArtProcessor = void 0;
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
const crypto_1 = __importDefault(require("crypto"));
const sharp_1 = __importDefault(require("sharp"));
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
class AlbumArtProcessor {
    static async initialize() {
        await fs_1.promises.mkdir(this.BASE_DIR, { recursive: true });
        await fs_1.promises.mkdir(this.THUMBNAILS_DIR, { recursive: true });
        await fs_1.promises.mkdir(this.COVERS_DIR, { recursive: true });
        await fs_1.promises.mkdir(this.ORIGINALS_DIR, { recursive: true });
    }
    static generateHash(artist, album) {
        const key = `${artist.toLowerCase().trim()}-${album.toLowerCase().trim()}`;
        return crypto_1.default.createHash('md5').update(key).digest('hex');
    }
    static async processAlbumArt(trackId, artist, album, sourceUrl) {
        try {
            await this.initialize();
            const hash = this.generateHash(artist, album);
            const filename = `${artist.replace(/[^a-zA-Z0-9]/g, '-')}-${album.replace(/[^a-zA-Z0-9]/g, '-')}-${hash}`;
            const existingTrack = await prisma_1.default.quizTrack.findFirst({
                where: {
                    albumArtHash: hash,
                    albumArtProcessed: true
                }
            });
            if (existingTrack?.localAlbumArtThumbnail) {
                await this.updateTrackWithAlbumArt(trackId, {
                    thumbnail: existingTrack.localAlbumArtThumbnail,
                    cover: existingTrack.localAlbumArtCover,
                    original: existingTrack.localAlbumArtOriginal
                }, hash);
                return {
                    success: true,
                    paths: {
                        thumbnail: existingTrack.localAlbumArtThumbnail,
                        cover: existingTrack.localAlbumArtCover,
                        original: existingTrack.localAlbumArtOriginal
                    },
                    hash
                };
            }
            let imageBuffer;
            if (sourceUrl) {
                imageBuffer = await this.downloadImage(sourceUrl);
            }
            else {
                imageBuffer = await this.extractAlbumArtFromFile(trackId);
            }
            if (!imageBuffer) {
                imageBuffer = await this.getPlaceholderImage();
            }
            const paths = await this.processAndSaveImages(imageBuffer, filename);
            await this.updateTrackWithAlbumArt(trackId, paths, hash);
            return {
                success: true,
                paths,
                hash
            };
        }
        catch (error) {
            console.error('Album art processing failed:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    static async downloadImage(url) {
        const response = await fetch(url, {
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; MusicApp/1.0)'
            }
        });
        if (!response.ok) {
            throw new Error(`Failed to download image: ${response.status}`);
        }
        return Buffer.from(await response.arrayBuffer());
    }
    static async extractAlbumArtFromFile(trackId) {
        try {
            const track = await prisma_1.default.quizTrack.findUnique({
                where: { id: trackId },
                select: { mpdFilePath: true }
            });
            if (!track?.mpdFilePath)
                return null;
            const { exec } = await Promise.resolve().then(() => __importStar(require('child_process')));
            const { promisify } = await Promise.resolve().then(() => __importStar(require('util')));
            const execAsync = promisify(exec);
            const tempFile = path_1.default.join('/tmp', `albumart-${trackId}.jpg`);
            const command = `ffmpeg -i "${track.mpdFilePath}" -an -vcodec copy "${tempFile}" -y`;
            await execAsync(command);
            const buffer = await fs_1.promises.readFile(tempFile);
            await fs_1.promises.unlink(tempFile).catch(() => { });
            return buffer;
        }
        catch (error) {
            console.error('Failed to extract album art from file:', error);
            return null;
        }
    }
    static async getPlaceholderImage() {
        return await (0, sharp_1.default)({
            create: {
                width: 500,
                height: 500,
                channels: 3,
                background: { r: 100, g: 100, b: 100 }
            }
        })
            .jpeg({ quality: this.QUALITY })
            .toBuffer();
    }
    static async processAndSaveImages(imageBuffer, filename) {
        const originalPath = path_1.default.join(this.ORIGINALS_DIR, `${filename}.jpg`);
        const coverPath = path_1.default.join(this.COVERS_DIR, `${filename}.webp`);
        const coverFallbackPath = path_1.default.join(this.COVERS_DIR, `${filename}.jpg`);
        const thumbnailPath = path_1.default.join(this.THUMBNAILS_DIR, `${filename}.webp`);
        const thumbnailFallbackPath = path_1.default.join(this.THUMBNAILS_DIR, `${filename}.jpg`);
        await fs_1.promises.writeFile(originalPath, imageBuffer);
        await (0, sharp_1.default)(imageBuffer)
            .resize(this.COVER_SIZE, this.COVER_SIZE, {
            fit: 'cover',
            position: 'center'
        })
            .webp({ quality: this.QUALITY })
            .toFile(coverPath);
        await (0, sharp_1.default)(imageBuffer)
            .resize(this.COVER_SIZE, this.COVER_SIZE, {
            fit: 'cover',
            position: 'center'
        })
            .jpeg({ quality: this.QUALITY })
            .toFile(coverFallbackPath);
        await (0, sharp_1.default)(imageBuffer)
            .resize(this.THUMBNAIL_SIZE, this.THUMBNAIL_SIZE, {
            fit: 'cover',
            position: 'center'
        })
            .webp({ quality: this.QUALITY })
            .toFile(thumbnailPath);
        await (0, sharp_1.default)(imageBuffer)
            .resize(this.THUMBNAIL_SIZE, this.THUMBNAIL_SIZE, {
            fit: 'cover',
            position: 'center'
        })
            .jpeg({ quality: this.QUALITY })
            .toFile(thumbnailFallbackPath);
        return {
            thumbnail: `/album-art/thumbnails/${filename}.webp`,
            cover: `/album-art/covers/${filename}.webp`,
            original: `/album-art/originals/${filename}.jpg`
        };
    }
    static async updateTrackWithAlbumArt(trackId, paths, hash) {
        await prisma_1.default.quizTrack.update({
            where: { id: trackId },
            data: {
                localAlbumArtThumbnail: paths.thumbnail,
                localAlbumArtCover: paths.cover,
                localAlbumArtOriginal: paths.original,
                albumArtHash: hash,
                albumArtProcessed: true,
                albumArtProcessedAt: new Date()
            }
        });
    }
    static async batchProcessAlbumArt(limit = 50, onProgress) {
        const allUnprocessedTracks = await prisma_1.default.$queryRawUnsafe(`SELECT id, artist, album, "album_art_url" as "albumArtUrl"
       FROM quiz_tracks 
       WHERE "album_art_url" IS NOT NULL
       LIMIT 1000`);
        const unprocessedTracks = allUnprocessedTracks.slice(0, limit);
        const total = unprocessedTracks.length;
        console.log(`Processing album art for ${total} tracks...`);
        for (let i = 0; i < unprocessedTracks.length; i++) {
            const track = unprocessedTracks[i];
            try {
                await this.processAlbumArt(track.id, track.artist, track.album, track.albumArtUrl || undefined);
                onProgress?.(i + 1, total);
                console.log(`Processed ${i + 1}/${total}: ${track.artist} - ${track.album}`);
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            catch (error) {
                console.error(`Failed to process album art for ${track.artist} - ${track.album}:`, error);
            }
        }
    }
    static async cleanup() {
        console.log('Starting album art cleanup...');
        const usedHashes = await prisma_1.default.quizTrack.findMany({
            where: { albumArtProcessed: true },
            select: { albumArtHash: true },
            distinct: ['albumArtHash']
        });
        const usedHashSet = new Set(usedHashes.map(t => t.albumArtHash).filter(Boolean));
        const dirs = [this.THUMBNAILS_DIR, this.COVERS_DIR, this.ORIGINALS_DIR];
        for (const dir of dirs) {
            const files = await fs_1.promises.readdir(dir);
            for (const file of files) {
                const hash = this.extractHashFromFilename(file);
                if (hash && !usedHashSet.has(hash)) {
                    const filePath = path_1.default.join(dir, file);
                    await fs_1.promises.unlink(filePath);
                    console.log(`Deleted unused album art: ${file}`);
                }
            }
        }
    }
    static extractHashFromFilename(filename) {
        const match = filename.match(/-([a-f0-9]{32})\.(webp|jpg)$/);
        return match ? match[1] : null;
    }
    static async getAlbumArt(trackId) {
        const track = await prisma_1.default.quizTrack.findUnique({
            where: { id: trackId },
            select: {
                localAlbumArtThumbnail: true,
                localAlbumArtCover: true,
                localAlbumArtOriginal: true,
                albumArtUrl: true,
                artist: true,
                album: true
            }
        });
        if (!track)
            return null;
        if (track.localAlbumArtThumbnail && track.localAlbumArtCover && track.localAlbumArtOriginal) {
            return {
                thumbnail: track.localAlbumArtThumbnail,
                cover: track.localAlbumArtCover,
                original: track.localAlbumArtOriginal
            };
        }
        const result = await this.processAlbumArt(trackId, track.artist, track.album, track.albumArtUrl || undefined);
        return result.success ? result.paths || null : null;
    }
}
exports.AlbumArtProcessor = AlbumArtProcessor;
_a = AlbumArtProcessor;
AlbumArtProcessor.BASE_DIR = path_1.default.join(process.cwd(), 'public', 'album-art');
AlbumArtProcessor.THUMBNAILS_DIR = path_1.default.join(_a.BASE_DIR, 'thumbnails');
AlbumArtProcessor.COVERS_DIR = path_1.default.join(_a.BASE_DIR, 'covers');
AlbumArtProcessor.ORIGINALS_DIR = path_1.default.join(_a.BASE_DIR, 'originals');
AlbumArtProcessor.THUMBNAIL_SIZE = 150;
AlbumArtProcessor.COVER_SIZE = 500;
AlbumArtProcessor.QUALITY = 85;
//# sourceMappingURL=album-art-processor.js.map