{"version": 3, "file": "mpd-volume-normalizer.js", "sourceRoot": "", "sources": ["../../../lib/services/mpd-volume-normalizer.ts"], "names": [], "mappings": ";;;;;;AA4JA,wDAKC;AAED,0DAKC;AAhKD,mEAA0C;AAG1C,MAAa,mBAAmB;IAQ9B,YAAY,SAAoB;QANxB,eAAU,GAAW,EAAE,CAAA;QACvB,cAAS,GAAY,IAAI,CAAA;QACzB,qBAAgB,GAAkB,IAAI,CAAA;QACtC,oBAAe,GAA0B,IAAI,CAAA;QAC7C,eAAU,GAAqB,IAAI,CAAA;QAGzC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;IAC5B,CAAC;IAKD,KAAK,CAAC,KAAK,CAAC,aAAqB,IAAI;QACnC,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAA;QAG1E,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAGjC,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC5C,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAA;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAA;YACtE,CAAC;QACH,CAAC,EAAE,UAAU,CAAC,CAAA;IAChB,CAAC;IAKD,IAAI;QACF,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YACnC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;QAC7B,CAAC;IACH,CAAC;IAKD,UAAU,CAAC,OAAgB;QACzB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAA;QACxB,OAAO,CAAC,GAAG,CAAC,8CAA8C,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAA;IAC/F,CAAC;IAKD,aAAa,CAAC,MAAc;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAA;QACpD,OAAO,CAAC,GAAG,CAAC,4CAA4C,IAAI,CAAC,UAAU,EAAE,CAAC,CAAA;IAC5E,CAAC;IAKO,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAM;QAE3B,IAAI,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAA;YAC/C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAA;YAEzD,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBACtC,OAAM;YACR,CAAC;YAGD,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAE/C,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;oBAE5E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;oBAC7D,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;wBACzB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,CAAA;wBAEjD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,CAAA;wBACxD,OAAO,CAAC,GAAG,CAAC,yDAAyD,IAAI,CAAC,UAAU,EAAE,CAAC,CAAA;oBACzF,CAAC;gBACH,CAAC;gBACD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAA;gBACxB,OAAM;YACR,CAAC;YAGD,OAAO,CAAC,GAAG,CAAC,wCAAwC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAA;YACvE,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,IAAI,CAAA;YAGxC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YAEtD,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAElB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;gBAC1C,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,CAAA;gBACjE,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAA;gBAElE,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAA;gBAC5D,OAAO,CAAC,GAAG,CAAC,YAAY,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC,CAAA;gBAChE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;gBAC5C,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;gBACjD,OAAO,CAAC,GAAG,CAAC,wBAAwB,aAAa,GAAG,CAAC,CAAA;gBAGrD,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,CAAA;YAC/C,CAAC;iBAAM,CAAC;gBAEN,OAAO,CAAC,GAAG,CAAC,oEAAoE,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;gBACnG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACjD,CAAC;YAED,IAAI,CAAC,UAAU,GAAG,MAAM,CAAA;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAA;QAC9E,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAC,UAAU,CAAC;gBAC9C,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE;gBAChC,MAAM,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE;aACjC,CAAC,CAAA;YAEF,OAAO,KAAK,EAAE,cAAc,IAAI,IAAI,CAAA;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;YACzH,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;CACF;AA1ID,kDA0IC;AAKD,IAAI,kBAAkB,GAA+B,IAAI,CAAA;AAEzD,SAAgB,sBAAsB,CAAC,SAAoB;IACzD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxB,kBAAkB,GAAG,IAAI,mBAAmB,CAAC,SAAS,CAAC,CAAA;IACzD,CAAC;IACD,OAAO,kBAAkB,CAAA;AAC3B,CAAC;AAED,SAAgB,uBAAuB;IACrC,IAAI,kBAAkB,EAAE,CAAC;QACvB,kBAAkB,CAAC,IAAI,EAAE,CAAA;QACzB,kBAAkB,GAAG,IAAI,CAAA;IAC3B,CAAC;AACH,CAAC"}