"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioMixer = void 0;
class AudioMixer {
    constructor() {
        this.sources = new Map();
        this.activeFades = new Map();
        this.eventListeners = new Map();
        this.speechVoices = [];
        this.channels = new Map();
        this.audioBuffers = new Map();
        this.speechUtterance = null;
        if (typeof window === 'undefined' || !window.AudioContext) {
            throw new Error('AudioMixer can only be initialized in a browser environment with Web Audio API support.');
        }
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
        catch (e) {
            console.error("Web Audio API is not supported in this browser.", e);
            throw new Error("Web Audio API not supported");
        }
        this.channels = new Map();
        this.audioBuffers = new Map();
        this.audioSources = new Map();
        this.gainNodes = new Map();
        this.speechSynthesis = window.speechSynthesis;
        this.initializeAudioGraph();
        this.initializeDefaultSettings();
        this.loadVoices();
    }
    initializeAudioGraph() {
        this.masterGainNode = this.audioContext.createGain();
        this.masterGainNode.connect(this.audioContext.destination);
        this.musicGainNode = this.audioContext.createGain();
        this.speechGainNode = this.audioContext.createGain();
        this.sfxGainNode = this.audioContext.createGain();
        this.ambientGainNode = this.audioContext.createGain();
        this.compressorNode = this.audioContext.createDynamicsCompressor();
        this.compressorNode.threshold.setValueAtTime(-24, this.audioContext.currentTime);
        this.compressorNode.knee.setValueAtTime(30, this.audioContext.currentTime);
        this.compressorNode.ratio.setValueAtTime(12, this.audioContext.currentTime);
        this.compressorNode.attack.setValueAtTime(0.003, this.audioContext.currentTime);
        this.compressorNode.release.setValueAtTime(0.25, this.audioContext.currentTime);
        this.analyserNode = this.audioContext.createAnalyser();
        this.analyserNode.fftSize = 2048;
        this.bassFilter = this.audioContext.createBiquadFilter();
        this.bassFilter.type = 'lowshelf';
        this.bassFilter.frequency.setValueAtTime(320, this.audioContext.currentTime);
        this.midFilter = this.audioContext.createBiquadFilter();
        this.midFilter.type = 'peaking';
        this.midFilter.frequency.setValueAtTime(1000, this.audioContext.currentTime);
        this.midFilter.Q.setValueAtTime(0.5, this.audioContext.currentTime);
        this.trebleFilter = this.audioContext.createBiquadFilter();
        this.trebleFilter.type = 'highshelf';
        this.trebleFilter.frequency.setValueAtTime(3200, this.audioContext.currentTime);
        this.musicGainNode.connect(this.bassFilter);
        this.speechGainNode.connect(this.bassFilter);
        this.sfxGainNode.connect(this.bassFilter);
        this.ambientGainNode.connect(this.bassFilter);
        this.bassFilter.connect(this.midFilter);
        this.midFilter.connect(this.trebleFilter);
        this.trebleFilter.connect(this.compressorNode);
        this.compressorNode.connect(this.analyserNode);
        this.analyserNode.connect(this.masterGainNode);
    }
    initializeDefaultSettings() {
        this.settings = {
            masterVolume: 0.8,
            musicVolume: 0.7,
            speechVolume: 0.9,
            sfxVolume: 0.6,
            ambientVolume: 0.4,
            crossfadeDuration: 2.0,
            enableDucking: true,
            duckingLevel: 0.3,
            enableEQ: true,
            bassGain: 0,
            midGain: 0,
            trebleGain: 0,
            enableTTS: false
        };
        this.applySettings();
    }
    async loadVoices() {
        return new Promise((resolve) => {
            const loadVoices = () => {
                this.speechVoices = this.speechSynthesis.getVoices();
                if (this.speechVoices.length > 0) {
                    resolve();
                }
                else {
                    setTimeout(loadVoices, 100);
                }
            };
            if (this.speechSynthesis.onvoiceschanged !== undefined) {
                this.speechSynthesis.onvoiceschanged = loadVoices;
            }
            loadVoices();
        });
    }
    async addSource(source) {
        const audio = new Audio(source.url);
        audio.crossOrigin = 'anonymous';
        audio.preload = 'metadata';
        await new Promise((resolve, reject) => {
            audio.onloadedmetadata = () => resolve();
            audio.onerror = () => reject(new Error(`Failed to load audio: ${source.url}`));
        });
        const sourceNode = this.audioContext.createMediaElementSource(audio);
        const gainNode = this.audioContext.createGain();
        gainNode.gain.setValueAtTime(source.volume, this.audioContext.currentTime);
        sourceNode.connect(gainNode);
        switch (source.type) {
            case 'music':
                gainNode.connect(this.musicGainNode);
                break;
            case 'speech':
                gainNode.connect(this.speechGainNode);
                break;
            case 'sfx':
                gainNode.connect(this.sfxGainNode);
                break;
            case 'ambient':
                gainNode.connect(this.ambientGainNode);
                break;
        }
        const fullSource = {
            ...source,
            element: audio,
            gainNode,
            isPlaying: false,
            isPaused: false,
            duration: audio.duration,
            currentTime: 0
        };
        audio.ontimeupdate = () => {
            fullSource.currentTime = audio.currentTime;
            this.emit('timeupdate', { sourceId: source.id, currentTime: audio.currentTime });
        };
        audio.onended = () => {
            fullSource.isPlaying = false;
            this.emit('ended', { sourceId: source.id });
        };
        audio.onplay = () => {
            fullSource.isPlaying = true;
            fullSource.isPaused = false;
            this.emit('play', { sourceId: source.id });
        };
        audio.onpause = () => {
            fullSource.isPaused = true;
            this.emit('pause', { sourceId: source.id });
        };
        this.sources.set(source.id, fullSource);
        return fullSource;
    }
    async play(sourceId, fadeIn) {
        const source = this.sources.get(sourceId);
        if (!source?.element)
            throw new Error(`Source ${sourceId} not found`);
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }
        if (source.type === 'speech' && this.settings.enableDucking) {
            this.duckMusic(true);
        }
        if (fadeIn) {
            const startVolume = fadeIn.startVolume ?? 0;
            source.gainNode.gain.setValueAtTime(startVolume, this.audioContext.currentTime);
            source.element.play();
            await this.fadeVolume(sourceId, {
                ...fadeIn,
                startVolume,
                endVolume: fadeIn.endVolume ?? source.volume
            });
        }
        else {
            source.gainNode.gain.setValueAtTime(source.volume, this.audioContext.currentTime);
            source.element.play();
        }
    }
    async stop(sourceId, fadeOut) {
        const source = this.sources.get(sourceId);
        if (!source?.element)
            return;
        if (fadeOut) {
            await this.fadeVolume(sourceId, {
                ...fadeOut,
                startVolume: fadeOut.startVolume ?? source.volume,
                endVolume: fadeOut.endVolume ?? 0
            });
        }
        source.element.pause();
        source.element.currentTime = 0;
        if (source.type === 'speech' && this.settings.enableDucking) {
            this.duckMusic(false);
        }
    }
    pause(sourceId) {
        const source = this.sources.get(sourceId);
        if (source?.element) {
            source.element.pause();
        }
    }
    resume(sourceId) {
        const source = this.sources.get(sourceId);
        if (source?.element) {
            source.element.play();
        }
    }
    async crossfade(fromSourceId, toSourceId, duration) {
        const fadeTime = duration ?? this.settings.crossfadeDuration;
        await this.play(toSourceId, {
            duration: fadeTime,
            curve: 'exponential',
            startVolume: 0
        });
        await this.stop(fromSourceId, {
            duration: fadeTime,
            curve: 'exponential',
            endVolume: 0
        });
    }
    async fadeVolume(sourceId, config) {
        const source = this.sources.get(sourceId);
        if (!source?.gainNode)
            throw new Error(`Source ${sourceId} not found`);
        const { duration, curve, startVolume, endVolume } = config;
        const start = startVolume ?? source.volume;
        const end = endVolume ?? source.volume;
        const currentTime = this.audioContext.currentTime;
        source.gainNode.gain.cancelScheduledValues(currentTime);
        source.gainNode.gain.setValueAtTime(start, currentTime);
        switch (curve) {
            case 'linear':
                source.gainNode.gain.linearRampToValueAtTime(end, currentTime + duration);
                break;
            case 'exponential':
                const safeStart = Math.max(start, 0.001);
                const safeEnd = Math.max(end, 0.001);
                source.gainNode.gain.setValueAtTime(safeStart, currentTime);
                source.gainNode.gain.exponentialRampToValueAtTime(safeEnd, currentTime + duration);
                break;
            case 'logarithmic':
                const steps = 10;
                for (let i = 1; i <= steps; i++) {
                    const progress = i / steps;
                    const logProgress = Math.log(progress * (Math.E - 1) + 1);
                    const value = start + (end - start) * logProgress;
                    source.gainNode.gain.linearRampToValueAtTime(value, currentTime + (duration * progress));
                }
                break;
        }
        const fadeId = Date.now();
        this.activeFades.set(sourceId, { fadeId, startTime: currentTime, config });
        return new Promise(resolve => {
            setTimeout(() => {
                this.activeFades.delete(sourceId);
                resolve();
            }, duration * 1000);
        });
    }
    duckMusic(enable) {
        const currentTime = this.audioContext.currentTime;
        const targetVolume = enable
            ? this.settings.musicVolume * this.settings.duckingLevel
            : this.settings.musicVolume;
        this.musicGainNode.gain.cancelScheduledValues(currentTime);
        this.musicGainNode.gain.setValueAtTime(this.musicGainNode.gain.value, currentTime);
        this.musicGainNode.gain.exponentialRampToValueAtTime(Math.max(targetVolume, 0.001), currentTime + 0.5);
    }
    async speakText(text, options = {}) {
        return new Promise((resolve, reject) => {
            const utterance = new SpeechSynthesisUtterance(text);
            if (options.voice) {
                const voice = this.speechVoices.find(v => v.name.includes(options.voice) || v.lang.includes(options.voice));
                if (voice)
                    utterance.voice = voice;
            }
            utterance.rate = options.rate ?? 1.0;
            utterance.pitch = options.pitch ?? 1.0;
            utterance.volume = options.volume ?? 1.0;
            if (this.settings.enableDucking) {
                this.duckMusic(true);
            }
            utterance.onstart = () => {
                this.emit('speechStart', { text });
            };
            utterance.onend = () => {
                if (this.settings.enableDucking) {
                    this.duckMusic(false);
                }
                this.emit('speechEnd', { text });
                resolve();
            };
            utterance.onerror = (error) => {
                if (this.settings.enableDucking) {
                    this.duckMusic(false);
                }
                reject(error);
            };
            this.speechSynthesis.speak(utterance);
        });
    }
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.applySettings();
    }
    applySettings() {
        const currentTime = this.audioContext.currentTime;
        this.masterGainNode.gain.setValueAtTime(this.settings.masterVolume, currentTime);
        this.musicGainNode.gain.setValueAtTime(this.settings.musicVolume, currentTime);
        this.speechGainNode.gain.setValueAtTime(this.settings.speechVolume, currentTime);
        this.sfxGainNode.gain.setValueAtTime(this.settings.sfxVolume, currentTime);
        this.ambientGainNode.gain.setValueAtTime(this.settings.ambientVolume, currentTime);
        if (this.settings.enableEQ) {
            this.bassFilter.gain.setValueAtTime(this.settings.bassGain, currentTime);
            this.midFilter.gain.setValueAtTime(this.settings.midGain, currentTime);
            this.trebleFilter.gain.setValueAtTime(this.settings.trebleGain, currentTime);
        }
        else {
            this.bassFilter.gain.setValueAtTime(0, currentTime);
            this.midFilter.gain.setValueAtTime(0, currentTime);
            this.trebleFilter.gain.setValueAtTime(0, currentTime);
        }
    }
    getAudioLevels() {
        const frequencyData = new Uint8Array(this.analyserNode.frequencyBinCount);
        const waveformData = new Uint8Array(this.analyserNode.frequencyBinCount);
        this.analyserNode.getByteFrequencyData(frequencyData);
        this.analyserNode.getByteTimeDomainData(waveformData);
        return {
            frequency: frequencyData,
            waveform: waveformData
        };
    }
    getSources() {
        return Array.from(this.sources.values());
    }
    getSettings() {
        return { ...this.settings };
    }
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }
    off(event, callback) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }
    emit(event, data) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => callback(data));
        }
    }
    dispose() {
        this.sources.forEach(source => this.stop(source.id));
        this.masterGainNode.disconnect();
        this.musicGainNode.disconnect();
        this.speechGainNode.disconnect();
        this.sfxGainNode.disconnect();
        this.ambientGainNode.disconnect();
        this.compressorNode.disconnect();
        this.analyserNode.disconnect();
        this.bassFilter.disconnect();
        this.midFilter.disconnect();
        this.trebleFilter.disconnect();
        if (this.audioContext.state !== 'closed') {
            this.audioContext.close();
        }
    }
}
exports.AudioMixer = AudioMixer;
//# sourceMappingURL=audio-mixer.js.map