"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.enhancedDeduplicateSongs = enhancedDeduplicateSongs;
exports.analyzeArtistVariations = analyzeArtistVariations;
exports.normalizeArtistName = normalizeArtistName;
exports.normalizeSongTitle = normalizeSongTitle;
function normalizeArtistName(artist) {
    if (!artist)
        return 'unknown';
    let normalized = artist.toLowerCase().trim();
    normalized = normalized
        .replace(/^[-\s]+|[-\s]+$/g, '')
        .replace(/^(various|varios|different|diverse)\s*(artists?|artistas?)$/i, 'various')
        .replace(/\s+(feat\.?|featuring|ft\.?|with)\s+.*/i, '')
        .replace(/\s*[\[\(].*?[\]\)]\s*/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();
    if (!normalized)
        return 'unknown';
    return normalized;
}
function normalizeSongTitle(title) {
    if (!title)
        return 'unknown';
    let normalized = title.toLowerCase().trim();
    normalized = normalized
        .replace(/\s*[-\(]\s*(remaster|remix|edit|version|mix|radio|clean|explicit|acoustic|live|demo|instrumental|karaoke).*?[\)\-]?\s*$/i, '')
        .replace(/\s*[\[\(].*?[\]\)]\s*/g, ' ')
        .replace(/\s+(feat\.?|featuring|ft\.?|with)\s+.*/i, '')
        .replace(/\s+/g, ' ')
        .trim();
    if (!normalized)
        return 'unknown';
    return normalized;
}
function enhancedDeduplicateSongs(songs) {
    const songMap = new Map();
    const debugInfo = [];
    songs.forEach(song => {
        const normalizedTitle = normalizeSongTitle(song.title);
        const normalizedArtist = normalizeArtistName(song.artist);
        const key = `${normalizedTitle}-${normalizedArtist}`;
        if (songMap.has(key)) {
            const existing = songMap.get(key);
            const currentScore = (song.votes || 0) * 10 +
                (song.duration || 0) / 60 +
                (song.album && song.album !== 'Unknown Album' ? 5 : 0) +
                (song.year ? 3 : 0) +
                (song.genre && song.genre !== 'Unknown' ? 2 : 0) +
                (song.albumArtUrl ? 2 : 0);
            const existingScore = (existing.votes || 0) * 10 +
                (existing.duration || 0) / 60 +
                (existing.album && existing.album !== 'Unknown Album' ? 5 : 0) +
                (existing.year ? 3 : 0) +
                (existing.genre && existing.genre !== 'Unknown' ? 2 : 0) +
                (existing.albumArtUrl ? 2 : 0);
            const betterSong = currentScore > existingScore ? song : existing;
            const otherSong = currentScore > existingScore ? existing : song;
            songMap.set(key, {
                ...betterSong,
                votes: (existing.votes || 0) + (song.votes || 0),
                duration: betterSong.duration || otherSong.duration,
                album: (betterSong.album && betterSong.album !== 'Unknown Album') ? betterSong.album : otherSong.album,
                year: betterSong.year || otherSong.year,
                genre: (betterSong.genre && betterSong.genre !== 'Unknown') ? betterSong.genre : otherSong.genre,
                albumArtUrl: betterSong.albumArtUrl || otherSong.albumArtUrl,
            });
            if (process.env.NODE_ENV === 'development') {
                console.log(`[Duplicate] Merged "${otherSong.title}" by "${otherSong.artist}" into "${betterSong.title}" by "${betterSong.artist}"`);
            }
        }
        else {
            songMap.set(key, { ...song });
        }
    });
    return Array.from(songMap.values());
}
function analyzeArtistVariations(songs) {
    const analysis = new Map();
    songs.forEach(song => {
        const normalizedTitle = normalizeSongTitle(song.title);
        const normalizedArtist = normalizeArtistName(song.artist);
        const key = `${normalizedTitle}-${normalizedArtist}`;
        if (!analysis.has(key)) {
            analysis.set(key, {
                artists: new Set(),
                titles: new Set()
            });
        }
        const entry = analysis.get(key);
        entry.artists.add(song.artist);
        entry.titles.add(song.title);
    });
    return Array.from(analysis.entries()).map(([key, data]) => ({
        normalizedKey: key,
        originalArtists: Array.from(data.artists),
        originalTitles: Array.from(data.titles),
        wouldMerge: data.artists.size > 1 || data.titles.size > 1
    })).filter(item => item.wouldMerge);
}
//# sourceMappingURL=enhanced-duplicate-detection.js.map