"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CSRFClient = void 0;
let csrfToken = null;
class CSRFClient {
    static async getToken() {
        if (csrfToken) {
            return csrfToken;
        }
        try {
            const response = await fetch('/api/auth/csrf');
            const data = await response.json();
            if (data.success && data.csrfToken) {
                csrfToken = data.csrfToken;
                return csrfToken;
            }
            throw new Error('Failed to get CSRF token');
        }
        catch (error) {
            console.error('CSRF token fetch error:', error);
            throw error;
        }
    }
    static clearToken() {
        csrfToken = null;
    }
    static async fetch(url, options = {}) {
        const token = await this.getToken();
        const headers = new Headers(options.headers);
        headers.set('x-csrf-token', token);
        return fetch(url, {
            ...options,
            headers
        });
    }
    static async addCSRFHeader(headers = {}) {
        const token = await this.getToken();
        if (headers instanceof Headers) {
            headers.set('x-csrf-token', token);
            return headers;
        }
        return {
            ...headers,
            'x-csrf-token': token
        };
    }
}
exports.CSRFClient = CSRFClient;
//# sourceMappingURL=csrf-client.js.map