import { Server as SocketIOServer } from 'socket.io';
export declare class JukeboxSocketHandler {
    private io;
    private skipVoteManager;
    private mpdClient;
    private statusPollingInterval;
    private currentTrack;
    constructor(io: SocketIOServer);
    private setupNamespace;
    private handleMPDControl;
    private startStatusPolling;
    private broadcastStatus;
    private sendCurrentStatus;
    cleanup(): void;
}
