{"version": 3, "file": "jukebox-performance.js", "sourceRoot": "", "sources": ["../../lib/jukebox-performance.ts"], "names": [], "mappings": ";;;AAuFA,oDAqMC;AAGD,8CAIC;AAGD,oDAKC;AAxSD,+DAA+D;AAC/D,qFAA6E;AAGhE,QAAA,mBAAmB,GAAG,IAAI,gCAAY,EAAU,CAAA;AAChD,QAAA,sBAAsB,GAAG,IAAI,gCAAY,EAAY,CAAA;AAGrD,QAAA,wBAAwB,GAAG,IAAA,2BAAO,EAAC,CAAC,KAAa,EAAU,EAAE;IACxE,MAAM,OAAO,GAAG,IAAI,GAAG,EAAgB,CAAA;IAEvC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAEnB,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,EAAE,CAAA;QAEpF,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,CAAA;YAClC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE;gBACf,GAAG,QAAQ;gBACX,KAAK,EAAE,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;gBAEhD,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;gBAC5C,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK;gBACnC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;gBAChC,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK;aACpC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,IAAI,EAAE,CAAC,CAAA;QAC/B,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;AACrC,CAAC,CAAC,CAAA;AAEW,QAAA,qBAAqB,GAAG,IAAA,2BAAO,EAAC,CAC3C,OAAe,EACf,UAAkB,EAClB,MAAc,EACd,gBAAwB,EAChB,EAAE;IACV,IAAI,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QAEnC,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QACpD,IAAI,CAAC,WAAW,EAAE,CAAC;YAEjB,MAAM,eAAe,GAAG,gBAAgB,KAAK,KAAK;gBAChD,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAClE,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,MAAM,aAAa,GACjB,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;YACtD,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;YACvD,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;YACtD,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;YACtD,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;QAG5D,MAAM,eAAe,GAAG,gBAAgB,KAAK,KAAK;YAChD,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAElE,OAAO,aAAa,IAAI,eAAe,CAAC;IAC1C,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACrB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,OAAO;gBACV,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAA;YACxC,KAAK,OAAO;gBACV,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,CAAA;YACrD,KAAK,QAAQ;gBACX,OAAO,CAAC,CAAC,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;YACvD,KAAK,MAAM;gBACT,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAA;YACtC;gBACE,OAAO,CAAC,CAAA;QACZ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,OAAO,QAAQ,CAAA;AACjB,CAAC,CAAC,CAAA;AAGK,KAAK,UAAU,oBAAoB,CACxC,SAAc,EACd,UAAgE,EAChE,sBAAsD,EACtD,YAAyC,EACzC,UAAmB,EACnB,IAAa,EACb,MAAgB,EAChB,gBAAyB,EACzB,aAAsB,EACtB,cAAwB,EACxB,YAAsB;IAEtB,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,CAAA;IAG7B,IAAI,QAAQ,GAAG,sBAAsB,WAAW,EAAE,CAAA;IAClD,IAAI,UAAU;QAAE,QAAQ,IAAI,WAAW,UAAU,EAAE,CAAA;IACnD,IAAI,gBAAgB,IAAI,gBAAgB,KAAK,KAAK;QAAE,QAAQ,IAAI,aAAa,gBAAgB,EAAE,CAAA;IAC/F,IAAI,aAAa,IAAI,aAAa,KAAK,KAAK;QAAE,QAAQ,IAAI,UAAU,aAAa,EAAE,CAAA;IACnF,IAAI,cAAc;QAAE,QAAQ,IAAI,aAAa,CAAA;IAC7C,IAAI,YAAY;QAAE,QAAQ,IAAI,eAAe,CAAA;IAE7C,MAAM,kBAAkB,GAAG,kBAAkB,CAAA;IAG7C,MAAM,UAAU,GAAG,UAAU,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC,IAAI,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC,IAAI,cAAc,IAAI,YAAY,CAAA;IACjK,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAA;IAC7C,MAAM,aAAa,GAAG,2BAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IACvD,MAAM,gBAAgB,GAAG,8BAAsB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAA;IAEvE,IAAI,aAAa,IAAI,WAAW,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,gBAAgB,EAAE,CAAC;QAC1E,UAAU,CAAC,aAAa,CAAC,CAAA;QACzB,sBAAsB,CAAC,gBAAgB,CAAC,CAAA;QACxC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAA;QAClD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAAA;IACzC,CAAC;IAED,YAAY,EAAE,CAAC,IAAI,CAAC,CAAA;IAEpB,IAAI,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,mDAAmD,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,CAAA;QACpK,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC/D,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAA;QAGpD,IAAI,UAAU,EAAE,CAAC;YACf,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QACrC,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QACtC,CAAC;QAGD,IAAI,UAAU,EAAE,CAAC;YACf,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;QAC5C,CAAC;QACD,IAAI,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,EAAE,CAAC;YACnD,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAA;QACpD,CAAC;QACD,IAAI,aAAa,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;YAC7C,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,CAAA;QAC9C,CAAC;QACD,IAAI,cAAc,EAAE,CAAC;YACnB,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAA;QAChD,CAAC;QACD,IAAI,YAAY,EAAE,CAAC;YACjB,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,CAAA;QAC9C,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC9D,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;QAE9F,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;YAChB,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YAEnF,IAAI,YAAY,CAAC;YACjB,IAAI,CAAC;gBACH,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC1C,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,UAAU,CAAC,CAAA;gBACxD,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,YAAY,CAAC,CAAA;gBAC5E,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;YACzD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,YAAY,CAAC,OAAO,CAAC,CAAA;YACpE,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,EAAE,CAAC;YACzC,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,IAAI,EAAE,CAAC;YACjD,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,IAAI,EAAE,cAAc,EAAE,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC,OAAO,CAAC,CAAA;YAErJ,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxB,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAA;YACvD,CAAC;YACD,MAAM,WAAW,GAAW,MAAM;iBAC/B,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC;iBACxE,GAAG,CAAC,CAAC,KAAU,EAAE,CAAS,EAAE,EAAE,CAAC,CAAC;gBAC/B,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;gBACjB,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,eAAe;gBACrC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,gBAAgB;gBACxC,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,eAAe;gBACrC,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,CAAC;gBAC7B,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,SAAS;gBAC/B,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,QAAQ,EAAE,KAAK,CAAC,WAAW;gBAC3B,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,KAAK;gBAChB,OAAO,EAAE,QAAQ;gBACjB,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;gBACnB,UAAU,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE;gBACxE,WAAW,EAAE,KAAK,CAAC,WAAW;aAC/B,CAAC,CAAC,CAAC;YAGN,IAAI,eAAe,GAAa,EAAE,CAAC;YACnC,IAAI,WAAW,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACrC,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;gBACxC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACzB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;wBACpB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;oBACnE,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC;YACrD,CAAC;YAGD,2BAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAA;YACzD,IAAI,CAAC,UAAU,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;gBAErC,8BAAsB,CAAC,GAAG,CAAC,kBAAkB,EAAE,eAAe,EAAE,MAAM,CAAC,CAAA;YACzE,CAAC;YAGD,IAAI,MAAM,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;gBAC9B,UAAU,CAAC,CAAC,WAAW,EAAE,EAAE;oBAEzB,MAAM,QAAQ,GAAG,CAAC,GAAG,WAAW,EAAE,GAAG,WAAW,CAAC,CAAC;oBAClD,OAAO,IAAA,uDAAwB,EAAC,QAAQ,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,WAAW,CAAC,CAAC;YAC1B,CAAC;YAED,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,sBAAsB,CAAC,eAAe,CAAC,CAAC;YAC1C,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,oBAAoB,WAAW,CAAC,MAAM,uCAAuC,CAAC,CAAC;YAG3F,OAAO;gBACL,OAAO,EAAE,UAAU,CAAC,OAAO,IAAI,KAAK;gBACpC,UAAU,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC;aAClC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAA;YAC3E,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxC,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,SAAS,CAAC,CAAA;YAGrD,IAAI,SAAS,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAA;gBAC/C,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,aAAa,EAAE,CAAC;gBAC/C,MAAM,WAAW,GAAW,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,CAAS,EAAE,EAAE,CAAC,CAAC;oBACjE,EAAE,EAAE,CAAC;oBACL,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,eAAe;oBACrC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,gBAAgB;oBACxC,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;oBACzB,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;oBACnD,QAAQ,EAAE,KAAK,CAAC,IAAI;oBACpB,KAAK,EAAE,CAAC;oBACR,SAAS,EAAE,KAAK;oBAChB,OAAO,EAAE,QAAQ;oBACjB,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;oBACnB,UAAU,EAAE,EAAE;iBACf,CAAC,CAAC,CAAC;gBAGJ,2BAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,CAAA;gBAErD,UAAU,CAAC,WAAW,CAAC,CAAC;gBACxB,OAAO,CAAC,GAAG,CAAC,oBAAoB,WAAW,CAAC,MAAM,6BAA6B,CAAC,CAAC;gBACjF,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAA;gBAC/D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;YAC3C,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;IAC3C,CAAC;YAAS,CAAC;QACT,YAAY,EAAE,CAAC,KAAK,CAAC,CAAA;IACvB,CAAC;AACH,CAAC;AAGD,SAAgB,iBAAiB;IAC/B,2BAAmB,CAAC,KAAK,EAAE,CAAA;IAC3B,8BAAsB,CAAC,KAAK,EAAE,CAAA;IAC9B,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAA;AACxC,CAAC;AAGD,SAAgB,oBAAoB;IAClC,OAAO;QACL,OAAO,EAAE,2BAAmB,CAAC,IAAI,EAAE;QACnC,UAAU,EAAE,8BAAsB,CAAC,IAAI,EAAE;KAC1C,CAAA;AACH,CAAC"}