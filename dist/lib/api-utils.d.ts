import { NextResponse } from 'next/server';
export declare const CacheDuration: {
    readonly NONE: 0;
    readonly SHORT: 60;
    readonly MEDIUM: 300;
    readonly LONG: 3600;
    readonly VERY_LONG: 86400;
};
export declare function withCacheHeaders(response: NextResponse, duration?: number, options?: {
    staleWhileRevalidate?: number;
    private?: boolean;
    immutable?: boolean;
}): NextResponse;
export declare function cachedJsonResponse(data: any, duration?: number, options?: Parameters<typeof withCacheHeaders>[2]): NextResponse;
export declare function handleConditionalRequest(request: Request, currentETag: string): NextResponse | null;
export declare function batchQueries<T extends readonly Promise<any>[]>(queries: T): Promise<{
    [K in keyof T]: Awaited<T[K]>;
}>;
export declare function createDebouncedHandler<T extends (...args: any[]) => any>(handler: T, delay?: number): T;
export declare function withPerformanceTiming(handler: (req: Request) => Promise<NextResponse>): (req: Request) => Promise<NextResponse>;
