"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gamePersistence = exports.GamePersistenceService = exports.GameStatus = void 0;
const prisma_1 = require("@/lib/database/prisma");
var GameStatus;
(function (GameStatus) {
    GameStatus["WAITING"] = "waiting";
    GameStatus["COUNTDOWN"] = "countdown";
    GameStatus["PLAYING"] = "playing";
    GameStatus["QUESTION_RESULTS"] = "question-results";
    GameStatus["FINISHED"] = "finished";
    GameStatus["ABANDONED"] = "abandoned";
})(GameStatus || (exports.GameStatus = GameStatus = {}));
class GamePersistenceService {
    static getInstance() {
        if (!GamePersistenceService.instance) {
            GamePersistenceService.instance = new GamePersistenceService();
        }
        return GamePersistenceService.instance;
    }
    async createGame(gameId, gamePin, hostId, hostName, settings) {
        try {
            const game = await prisma_1.prisma.multiplayerGame.create({
                data: {
                    gamePin,
                    hostId,
                    status: GameStatus.WAITING,
                    settings: JSON.stringify(settings),
                    currentQuestionIndex: 0,
                    questions: JSON.stringify([]),
                    leaderboard: JSON.stringify([]),
                    players: {
                        create: {
                            name: hostName,
                            score: 0,
                            isHost: true,
                            isConnected: true,
                            hasAnswered: false,
                            answers: JSON.stringify([])
                        }
                    }
                },
                include: {
                    players: true
                }
            });
            console.log(`[GamePersistence] Created game:`, {
                id: game.id,
                pin: game.gamePin,
                status: game.status,
                hostId: game.hostId
            });
            return this.mapToPersistedGame(game);
        }
        catch (error) {
            console.error('Failed to create game in database:', error);
            throw new Error('Failed to persist game data');
        }
    }
    async getGame(gameId) {
        try {
            const game = await prisma_1.prisma.multiplayerGame.findUnique({
                where: { id: gameId },
                include: { players: true }
            });
            return game ? this.mapToPersistedGame(game) : null;
        }
        catch (error) {
            console.error('Failed to get game from database:', error);
            return null;
        }
    }
    async getGameByPin(gamePin) {
        try {
            const game = await prisma_1.prisma.multiplayerGame.findUnique({
                where: { gamePin: gamePin.toUpperCase() },
                include: { players: true }
            });
            if (game) {
                console.log(`[GamePersistence] Found game by PIN ${gamePin}:`, {
                    id: game.id,
                    pin: game.gamePin,
                    status: game.status,
                    players: game.players.length
                });
            }
            return game ? this.mapToPersistedGame(game) : null;
        }
        catch (error) {
            console.error('Failed to get game by PIN from database:', error);
            return null;
        }
    }
    async updateGameStatus(gameId, status) {
        try {
            await prisma_1.prisma.multiplayerGame.update({
                where: { id: gameId },
                data: {
                    status,
                    ...(status === GameStatus.PLAYING && { startedAt: new Date() }),
                    ...(status === GameStatus.FINISHED && { endedAt: new Date() })
                }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to update game status:', error);
            return false;
        }
    }
    async updateGameQuestions(gameId, questions) {
        try {
            await prisma_1.prisma.multiplayerGame.update({
                where: { id: gameId },
                data: { questions: JSON.stringify(questions) }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to update game questions:', error);
            return false;
        }
    }
    async updateCurrentQuestion(gameId, questionIndex) {
        try {
            await prisma_1.prisma.multiplayerGame.update({
                where: { id: gameId },
                data: { currentQuestionIndex: questionIndex }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to update current question:', error);
            return false;
        }
    }
    async updateLeaderboard(gameId, leaderboard) {
        try {
            await prisma_1.prisma.multiplayerGame.update({
                where: { id: gameId },
                data: { leaderboard: JSON.stringify(leaderboard) }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to update leaderboard:', error);
            return false;
        }
    }
    async addPlayer(gameId, playerId, playerName, avatar) {
        try {
            const player = await prisma_1.prisma.multiplayerGamePlayer.create({
                data: {
                    gameId,
                    name: playerName,
                    avatar,
                    score: 0,
                    isHost: false,
                    isConnected: true,
                    hasAnswered: false,
                    answers: JSON.stringify([])
                }
            });
            return player.id;
        }
        catch (error) {
            console.error('Failed to add player to database:', error);
            return null;
        }
    }
    async updatePlayerConnection(playerId, isConnected) {
        try {
            await prisma_1.prisma.multiplayerGamePlayer.update({
                where: { id: playerId },
                data: {
                    isConnected,
                    lastSeen: new Date()
                }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to update player connection:', error);
            return false;
        }
    }
    async updatePlayerScore(playerId, score) {
        try {
            await prisma_1.prisma.multiplayerGamePlayer.update({
                where: { id: playerId },
                data: { score }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to update player score:', error);
            return false;
        }
    }
    async updatePlayerAnswerStatus(playerId, hasAnswered) {
        try {
            await prisma_1.prisma.multiplayerGamePlayer.update({
                where: { id: playerId },
                data: { hasAnswered }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to update player answer status:', error);
            return false;
        }
    }
    async addPlayerAnswer(playerId, questionId, answer, isCorrect, score, timeSpent) {
        try {
            const player = await prisma_1.prisma.multiplayerGamePlayer.findUnique({
                where: { id: playerId }
            });
            if (!player)
                return false;
            const answers = JSON.parse(player.answers || '[]');
            answers.push({
                questionId,
                answer,
                isCorrect,
                score,
                timeSpent,
                submittedAt: new Date()
            });
            await prisma_1.prisma.multiplayerGamePlayer.update({
                where: { id: playerId },
                data: {
                    answers: JSON.stringify(answers),
                    hasAnswered: true
                }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to add player answer:', error);
            return false;
        }
    }
    async removePlayer(playerId) {
        try {
            await prisma_1.prisma.multiplayerGamePlayer.delete({
                where: { id: playerId }
            });
            return true;
        }
        catch (error) {
            console.error('Failed to remove player from database:', error);
            return false;
        }
    }
    async changeHost(gameId, oldHostId, newHostId) {
        try {
            await prisma_1.prisma.$transaction([
                prisma_1.prisma.multiplayerGamePlayer.updateMany({
                    where: { gameId, id: oldHostId },
                    data: { isHost: false }
                }),
                prisma_1.prisma.multiplayerGamePlayer.updateMany({
                    where: { gameId, id: newHostId },
                    data: { isHost: true }
                }),
                prisma_1.prisma.multiplayerGame.update({
                    where: { id: gameId },
                    data: { hostId: newHostId }
                })
            ]);
            return true;
        }
        catch (error) {
            console.error('Failed to change host:', error);
            return false;
        }
    }
    async getActiveGames() {
        try {
            const games = await prisma_1.prisma.multiplayerGame.findMany({
                where: {
                    status: {
                        in: [GameStatus.WAITING, GameStatus.COUNTDOWN, GameStatus.PLAYING, GameStatus.QUESTION_RESULTS]
                    }
                },
                include: { players: true },
                orderBy: { createdAt: 'desc' }
            });
            return games.map(game => this.mapToPersistedGame(game));
        }
        catch (error) {
            console.error('Failed to get active games:', error);
            return [];
        }
    }
    async cleanupOldGames(olderThanHours = 24) {
        try {
            const cutoffDate = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
            const result = await prisma_1.prisma.multiplayerGame.deleteMany({
                where: {
                    OR: [
                        {
                            status: GameStatus.FINISHED,
                            endedAt: { lt: cutoffDate }
                        },
                        {
                            status: GameStatus.ABANDONED,
                            updatedAt: { lt: cutoffDate }
                        },
                        {
                            status: GameStatus.WAITING,
                            createdAt: { lt: cutoffDate }
                        }
                    ]
                }
            });
            console.log(`Cleaned up ${result.count} old games`);
            return result.count;
        }
        catch (error) {
            console.error('Failed to cleanup old games:', error);
            return 0;
        }
    }
    async markAbandonedGames(inactiveMinutes = 30) {
        try {
            const cutoffDate = new Date(Date.now() - inactiveMinutes * 60 * 1000);
            const result = await prisma_1.prisma.multiplayerGame.updateMany({
                where: {
                    status: {
                        in: [GameStatus.WAITING, GameStatus.COUNTDOWN, GameStatus.PLAYING, GameStatus.QUESTION_RESULTS]
                    },
                    updatedAt: { lt: cutoffDate }
                },
                data: { status: GameStatus.ABANDONED }
            });
            console.log(`Marked ${result.count} games as abandoned`);
            return result.count;
        }
        catch (error) {
            console.error('Failed to mark abandoned games:', error);
            return 0;
        }
    }
    async getGameStats() {
        try {
            const [totalGames, activeGames, finishedGames, totalPlayers] = await Promise.all([
                prisma_1.prisma.multiplayerGame.count(),
                prisma_1.prisma.multiplayerGame.count({
                    where: {
                        status: {
                            in: [GameStatus.WAITING, GameStatus.COUNTDOWN, GameStatus.PLAYING, GameStatus.QUESTION_RESULTS]
                        }
                    }
                }),
                prisma_1.prisma.multiplayerGame.count({
                    where: { status: GameStatus.FINISHED }
                }),
                prisma_1.prisma.multiplayerGamePlayer.count()
            ]);
            const finishedGamesWithDuration = await prisma_1.prisma.multiplayerGame.findMany({
                where: {
                    status: GameStatus.FINISHED,
                    startedAt: { not: null },
                    endedAt: { not: null }
                },
                select: { startedAt: true, endedAt: true }
            });
            const averageGameDuration = finishedGamesWithDuration.length > 0
                ? finishedGamesWithDuration.reduce((sum, game) => {
                    const duration = game.endedAt.getTime() - game.startedAt.getTime();
                    return sum + duration;
                }, 0) / finishedGamesWithDuration.length / 1000 / 60
                : 0;
            return {
                totalGames,
                activeGames,
                finishedGames,
                totalPlayers,
                averageGameDuration: Math.round(averageGameDuration)
            };
        }
        catch (error) {
            console.error('Failed to get game stats:', error);
            return {
                totalGames: 0,
                activeGames: 0,
                finishedGames: 0,
                totalPlayers: 0,
                averageGameDuration: 0
            };
        }
    }
    mapToPersistedGame(game) {
        return {
            id: game.id,
            gamePin: game.gamePin,
            hostId: game.hostId,
            status: game.status,
            settings: JSON.parse(game.settings || '{}'),
            players: game.players.map((player) => ({
                id: player.id,
                name: player.name,
                avatar: player.avatar,
                score: player.score,
                isHost: player.isHost,
                isConnected: player.isConnected,
                hasAnswered: player.hasAnswered,
                joinedAt: player.createdAt,
                lastSeen: player.lastSeen,
                answers: JSON.parse(player.answers || '[]')
            })),
            currentQuestionIndex: game.currentQuestionIndex,
            questions: JSON.parse(game.questions || '[]'),
            leaderboard: JSON.parse(game.leaderboard || '[]'),
            createdAt: game.createdAt,
            updatedAt: game.updatedAt,
            startedAt: game.startedAt,
            endedAt: game.endedAt
        };
    }
}
exports.GamePersistenceService = GamePersistenceService;
exports.gamePersistence = GamePersistenceService.getInstance();
//# sourceMappingURL=game-persistence.js.map