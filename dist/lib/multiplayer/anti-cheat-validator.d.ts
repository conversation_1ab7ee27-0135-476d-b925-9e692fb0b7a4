import { EventEmitter } from 'events';
export interface CheatIndicator {
    type: 'timing' | 'pattern' | 'impossible' | 'suspicious';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    evidence: any;
    timestamp: number;
    confidence: number;
}
export interface PlayerAnswer {
    questionId: string;
    answer: any;
    isCorrect: boolean;
    submittedAt: number;
    timeSpent: number;
    questionStartTime: number;
    clientTimestamp?: number;
}
export interface ValidationConfig {
    enableTimingValidation: boolean;
    enablePatternDetection: boolean;
    enableImpossibleAnswerDetection: boolean;
    timingToleranceMs: number;
    suspiciousSpeedThreshold: number;
    maxConsecutiveCorrect: number;
    accuracyThreshold: number;
    responseTimeVarianceThreshold: number;
}
export interface PlayerProfile {
    playerId: string;
    answers: PlayerAnswer[];
    averageResponseTime: number;
    accuracyRate: number;
    responseTimeVariance: number;
    consecutiveCorrect: number;
    suspiciousEvents: CheatIndicator[];
    riskScore: number;
}
export declare class AntiCheatValidator extends EventEmitter {
    private config;
    private playerProfiles;
    private questionDatabase;
    private serverTimeOffset;
    constructor(config?: Partial<ValidationConfig>);
    validateAnswer(playerId: string, answer: PlayerAnswer, questionMetadata: QuestionMetadata): CheatIndicator[];
    private validateAnswerTiming;
    private detectSuspiciousPatterns;
    private detectImpossibleAnswers;
    private isValidAnswerFormat;
    private getPlayerProfile;
    private updatePlayerProfile;
    private calculateRiskScore;
    getPlayerRiskAssessment(playerId: string): {
        riskScore: number;
        riskLevel: 'low' | 'medium' | 'high' | 'critical';
        indicators: CheatIndicator[];
        statistics: any;
    } | null;
    flagPlayerForReview(playerId: string, reason: string): void;
    resetPlayerProfile(playerId: string): boolean;
    getHighRiskPlayers(): Array<{
        playerId: string;
        riskScore: number;
        riskLevel: string;
    }>;
    cleanup(): void;
}
interface QuestionMetadata {
    id: string;
    type: 'multiple-choice' | 'true-false' | 'slider';
    timeLimit: number;
    options?: string[];
    sliderMin?: number;
    sliderMax?: number;
    difficulty: number;
}
export {};
