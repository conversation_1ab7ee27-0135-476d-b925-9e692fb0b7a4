"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReconnectionManager = void 0;
const events_1 = require("events");
const game_persistence_1 = require("./game-persistence");
class ReconnectionManager extends events_1.EventEmitter {
    constructor(strategy = {}) {
        super();
        this.reconnectionData = new Map();
        this.reconnectionTokens = new Map();
        this.cleanupInterval = null;
        this.strategy = {
            maxAttempts: 5,
            backoffStrategy: 'exponential',
            baseDelay: 1000,
            maxDelay: 30000,
            stateRecoveryWindow: 15 * 60 * 1000,
            gracefulHandoff: true,
            preserveScore: true,
            preserveAnswers: true,
            ...strategy
        };
        this.startCleanupTask();
    }
    async registerDisconnection(playerId, gameId, socketId, gameState) {
        try {
            const token = this.generateReconnectionToken();
            const game = await game_persistence_1.gamePersistence.getGame(gameId);
            if (!game) {
                throw new Error('Game not found');
            }
            const player = game.players.find(p => p.id === playerId);
            if (!player) {
                throw new Error('Player not found');
            }
            const reconnectionData = {
                playerId,
                gameId,
                playerName: player.name,
                lastSocketId: socketId,
                disconnectedAt: Date.now(),
                gameState,
                reconnectionToken: token,
                attemptCount: 0
            };
            this.reconnectionData.set(playerId, reconnectionData);
            this.reconnectionTokens.set(token, playerId);
            await game_persistence_1.gamePersistence.updatePlayerConnection(playerId, false);
            this.emit('player_disconnected', {
                playerId,
                gameId,
                playerName: player.name,
                canReconnect: true,
                reconnectionWindow: this.strategy.stateRecoveryWindow
            });
            console.log(`Registered disconnection for player ${playerId} in game ${gameId}`);
            return token;
        }
        catch (error) {
            console.error('Failed to register disconnection:', error);
            throw error;
        }
    }
    async attemptReconnection(token, newSocketId) {
        try {
            const playerId = this.reconnectionTokens.get(token);
            if (!playerId) {
                return {
                    success: false,
                    playerId: '',
                    gameId: '',
                    error: 'Invalid reconnection token'
                };
            }
            const reconnectionData = this.reconnectionData.get(playerId);
            if (!reconnectionData) {
                return {
                    success: false,
                    playerId,
                    gameId: '',
                    error: 'Reconnection data not found'
                };
            }
            const elapsed = Date.now() - reconnectionData.disconnectedAt;
            if (elapsed > this.strategy.stateRecoveryWindow) {
                this.cleanup(playerId);
                return {
                    success: false,
                    playerId,
                    gameId: reconnectionData.gameId,
                    error: 'Reconnection window expired'
                };
            }
            reconnectionData.attemptCount++;
            if (reconnectionData.attemptCount > this.strategy.maxAttempts) {
                this.cleanup(playerId);
                return {
                    success: false,
                    playerId,
                    gameId: reconnectionData.gameId,
                    error: 'Maximum reconnection attempts exceeded'
                };
            }
            const game = await game_persistence_1.gamePersistence.getGame(reconnectionData.gameId);
            if (!game || game.status === 'finished' || game.status === 'abandoned') {
                this.cleanup(playerId);
                return {
                    success: false,
                    playerId,
                    gameId: reconnectionData.gameId,
                    error: 'Game no longer active'
                };
            }
            await game_persistence_1.gamePersistence.updatePlayerConnection(playerId, true);
            const recoveredState = this.strategy.gracefulHandoff
                ? this.recoverPlayerState(reconnectionData)
                : undefined;
            this.cleanup(playerId);
            this.emit('player_reconnected', {
                playerId,
                gameId: reconnectionData.gameId,
                playerName: reconnectionData.playerName,
                newSocketId,
                recoveredState,
                disconnectionDuration: elapsed
            });
            console.log(`Player ${playerId} successfully reconnected to game ${reconnectionData.gameId}`);
            return {
                success: true,
                playerId,
                gameId: reconnectionData.gameId,
                recoveredState,
                newSocketId
            };
        }
        catch (error) {
            console.error('Reconnection attempt failed:', error);
            return {
                success: false,
                playerId: '',
                gameId: '',
                error: error.message
            };
        }
    }
    canReconnect(playerId) {
        const data = this.reconnectionData.get(playerId);
        if (!data)
            return false;
        const elapsed = Date.now() - data.disconnectedAt;
        return elapsed <= this.strategy.stateRecoveryWindow &&
            data.attemptCount < this.strategy.maxAttempts;
    }
    getReconnectionInfo(playerId) {
        const data = this.reconnectionData.get(playerId);
        if (!data)
            return null;
        const elapsed = Date.now() - data.disconnectedAt;
        const remainingTime = Math.max(0, this.strategy.stateRecoveryWindow - elapsed);
        const remainingAttempts = Math.max(0, this.strategy.maxAttempts - data.attemptCount);
        return {
            canReconnect: remainingTime > 0 && remainingAttempts > 0,
            remainingTime,
            remainingAttempts
        };
    }
    getReconnectionDelay(attemptCount) {
        if (this.strategy.backoffStrategy === 'exponential') {
            const delay = this.strategy.baseDelay * Math.pow(2, attemptCount - 1);
            return Math.min(delay, this.strategy.maxDelay);
        }
        else {
            const delay = this.strategy.baseDelay * attemptCount;
            return Math.min(delay, this.strategy.maxDelay);
        }
    }
    recoverPlayerState(data) {
        const state = {
            score: this.strategy.preserveScore ? data.gameState.score : 0,
            answers: this.strategy.preserveAnswers ? data.gameState.answers : [],
            currentQuestionIndex: data.gameState.currentQuestionIndex,
            hasAnswered: data.gameState.hasAnswered,
            lastActivity: Date.now(),
            achievements: data.gameState.achievements || [],
            powerUps: data.gameState.powerUps || []
        };
        return state;
    }
    generateReconnectionToken() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 15);
        const random2 = Math.random().toString(36).substring(2, 15);
        return `${timestamp}-${random}-${random2}`;
    }
    cleanup(playerId) {
        const data = this.reconnectionData.get(playerId);
        if (data) {
            this.reconnectionTokens.delete(data.reconnectionToken);
            this.reconnectionData.delete(playerId);
        }
    }
    startCleanupTask() {
        this.cleanupInterval = setInterval(() => {
            const now = Date.now();
            const expired = [];
            for (const [playerId, data] of this.reconnectionData) {
                const elapsed = now - data.disconnectedAt;
                if (elapsed > this.strategy.stateRecoveryWindow) {
                    expired.push(playerId);
                }
            }
            for (const playerId of expired) {
                this.cleanup(playerId);
                console.log(`Cleaned up expired reconnection data for player ${playerId}`);
            }
        }, 60000);
    }
    forceCleanup(playerId) {
        const had = this.reconnectionData.has(playerId);
        this.cleanup(playerId);
        return had;
    }
    getPendingReconnections() {
        const pending = [];
        const now = Date.now();
        for (const [playerId, data] of this.reconnectionData) {
            const elapsed = now - data.disconnectedAt;
            const remainingTime = Math.max(0, this.strategy.stateRecoveryWindow - elapsed);
            if (remainingTime > 0) {
                pending.push({
                    playerId,
                    gameId: data.gameId,
                    playerName: data.playerName,
                    disconnectedAt: data.disconnectedAt,
                    remainingTime
                });
            }
        }
        return pending;
    }
    shutdown() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.reconnectionData.clear();
        this.reconnectionTokens.clear();
        this.removeAllListeners();
    }
}
exports.ReconnectionManager = ReconnectionManager;
//# sourceMappingURL=reconnection-manager.js.map