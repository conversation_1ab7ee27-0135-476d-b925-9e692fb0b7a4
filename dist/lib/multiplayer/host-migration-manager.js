"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HostMigrationManager = void 0;
const events_1 = require("events");
const game_persistence_1 = require("./game-persistence");
class HostMigrationManager extends events_1.EventEmitter {
    constructor(strategy = {}) {
        super();
        this.pendingMigrations = new Map();
        this.migrationHistory = new Map();
        this.hostQueue = new Map();
        this.strategy = {
            selectionMethod: 'longest-connected',
            gracePeriod: 5000,
            requireConfirmation: false,
            preserveGameSettings: true,
            notifyPlayers: true,
            ...strategy
        };
    }
    async detectHostDisconnection(gameId, hostId) {
        try {
            console.log(`Host disconnection detected for game ${gameId}, host ${hostId}`);
            this.cancelPendingMigration(gameId);
            const timeout = setTimeout(async () => {
                await this.performMigration(gameId, hostId);
                this.pendingMigrations.delete(gameId);
            }, this.strategy.gracePeriod);
            this.pendingMigrations.set(gameId, timeout);
            this.emit('host_disconnection_detected', {
                gameId,
                hostId,
                gracePeriod: this.strategy.gracePeriod
            });
        }
        catch (error) {
            console.error('Failed to handle host disconnection:', error);
            this.emit('migration_error', { gameId, error });
        }
    }
    cancelMigration(gameId) {
        if (this.cancelPendingMigration(gameId)) {
            this.emit('migration_cancelled', { gameId });
            return true;
        }
        return false;
    }
    async performMigration(gameId, oldHostId) {
        const startTime = Date.now();
        try {
            const game = await game_persistence_1.gamePersistence.getGame(gameId);
            if (!game) {
                throw new Error('Game not found');
            }
            if (game.status === game_persistence_1.GameStatus.FINISHED || game.status === game_persistence_1.GameStatus.ABANDONED) {
                console.log(`Game ${gameId} is no longer active, skipping migration`);
                return;
            }
            const candidates = await this.getHostCandidates(game);
            const newHost = await this.selectNewHost(candidates, game);
            if (!newHost) {
                console.error(`No eligible host found for game ${gameId}`);
                await game_persistence_1.gamePersistence.updateGameStatus(gameId, game_persistence_1.GameStatus.ABANDONED);
                this.emit('migration_failed', {
                    gameId,
                    reason: 'No eligible host found'
                });
                return;
            }
            const success = await game_persistence_1.gamePersistence.changeHost(gameId, oldHostId, newHost.playerId);
            if (!success) {
                throw new Error('Database migration failed');
            }
            const result = {
                success: true,
                oldHostId,
                newHostId: newHost.playerId,
                newHostName: newHost.playerName,
                migrationTime: Date.now() - startTime,
                reason: 'Host disconnection'
            };
            this.recordMigration(gameId, result);
            this.emit('host_migrated', {
                gameId,
                ...result
            });
            console.log(`Successfully migrated host for game ${gameId} from ${oldHostId} to ${newHost.playerId}`);
        }
        catch (error) {
            console.error('Host migration failed:', error);
            const result = {
                success: false,
                oldHostId,
                newHostId: '',
                newHostName: '',
                migrationTime: Date.now() - startTime,
                reason: error.message
            };
            this.recordMigration(gameId, result);
            this.emit('migration_failed', {
                gameId,
                oldHostId,
                error: error.message
            });
        }
    }
    async getHostCandidates(game) {
        const candidates = [];
        const now = Date.now();
        for (const player of game.players) {
            if (!player.isConnected || player.isHost) {
                continue;
            }
            const candidate = {
                playerId: player.id,
                playerName: player.name,
                score: player.score,
                joinedAt: player.joinedAt.getTime(),
                lastActivity: player.lastSeen.getTime(),
                connectionQuality: this.calculateConnectionQuality(player),
                isEligible: true,
                priority: 0
            };
            if (now - candidate.lastActivity > 60000) {
                candidate.isEligible = false;
            }
            if (candidate.connectionQuality < 0.7) {
                candidate.isEligible = false;
            }
            candidate.priority = this.calculatePriority(candidate, game);
            candidates.push(candidate);
        }
        return candidates.filter(c => c.isEligible);
    }
    async selectNewHost(candidates, game) {
        if (candidates.length === 0) {
            return null;
        }
        const sorted = candidates.sort((a, b) => b.priority - a.priority);
        if (this.strategy.requireConfirmation) {
            for (const candidate of sorted) {
                const confirmed = await this.requestHostConfirmation(candidate, game.id);
                if (confirmed) {
                    return candidate;
                }
            }
            return null;
        }
        else {
            return sorted[0];
        }
    }
    calculatePriority(candidate, game) {
        const now = Date.now();
        switch (this.strategy.selectionMethod) {
            case 'longest-connected':
                return (now - candidate.joinedAt) / 1000;
            case 'highest-score':
                return candidate.score * 100 + (now - candidate.joinedAt) / 10000;
            case 'most-active':
                const inactivityPenalty = (now - candidate.lastActivity) / 1000;
                return 10000 - inactivityPenalty;
            case 'round-robin':
                const queue = this.hostQueue.get(game.id) || [];
                const position = queue.indexOf(candidate.playerId);
                return position >= 0 ? queue.length - position : 0;
            default:
                return candidate.connectionQuality * 1000;
        }
    }
    calculateConnectionQuality(player) {
        const now = Date.now();
        const timeSinceActivity = now - player.lastSeen.getTime();
        if (timeSinceActivity < 5000)
            return 1.0;
        if (timeSinceActivity < 15000)
            return 0.9;
        if (timeSinceActivity < 30000)
            return 0.8;
        if (timeSinceActivity < 60000)
            return 0.7;
        return 0.5;
    }
    async requestHostConfirmation(candidate, gameId) {
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                resolve(false);
            }, 10000);
            this.emit('host_confirmation_request', {
                gameId,
                candidateId: candidate.playerId,
                candidateName: candidate.playerName,
                callback: (accepted) => {
                    clearTimeout(timeout);
                    resolve(accepted);
                }
            });
        });
    }
    initializeHostQueue(gameId, playerIds) {
        if (this.strategy.selectionMethod === 'round-robin') {
            const shuffled = [...playerIds].sort(() => Math.random() - 0.5);
            this.hostQueue.set(gameId, shuffled);
        }
    }
    updateHostQueue(gameId, playerId, action) {
        const queue = this.hostQueue.get(gameId) || [];
        if (action === 'add' && !queue.includes(playerId)) {
            queue.push(playerId);
        }
        else if (action === 'remove') {
            const index = queue.indexOf(playerId);
            if (index >= 0) {
                queue.splice(index, 1);
            }
        }
        this.hostQueue.set(gameId, queue);
    }
    cancelPendingMigration(gameId) {
        const timeout = this.pendingMigrations.get(gameId);
        if (timeout) {
            clearTimeout(timeout);
            this.pendingMigrations.delete(gameId);
            return true;
        }
        return false;
    }
    recordMigration(gameId, result) {
        const history = this.migrationHistory.get(gameId) || [];
        history.push(result);
        if (history.length > 10) {
            history.shift();
        }
        this.migrationHistory.set(gameId, history);
    }
    getMigrationHistory(gameId) {
        return this.migrationHistory.get(gameId) || [];
    }
    async forceHostMigration(gameId, newHostId) {
        const startTime = Date.now();
        try {
            const game = await game_persistence_1.gamePersistence.getGame(gameId);
            if (!game) {
                throw new Error('Game not found');
            }
            const oldHostId = game.hostId;
            const newHost = game.players.find(p => p.id === newHostId);
            if (!newHost) {
                throw new Error('New host not found in game');
            }
            const success = await game_persistence_1.gamePersistence.changeHost(gameId, oldHostId, newHostId);
            if (!success) {
                throw new Error('Database migration failed');
            }
            const result = {
                success: true,
                oldHostId,
                newHostId,
                newHostName: newHost.name,
                migrationTime: Date.now() - startTime,
                reason: 'Admin force migration'
            };
            this.recordMigration(gameId, result);
            this.emit('host_migrated', { gameId, ...result });
            return result;
        }
        catch (error) {
            return {
                success: false,
                oldHostId: '',
                newHostId,
                newHostName: '',
                migrationTime: Date.now() - startTime,
                reason: error.message
            };
        }
    }
    cleanupGame(gameId) {
        this.cancelPendingMigration(gameId);
        this.hostQueue.delete(gameId);
        this.migrationHistory.delete(gameId);
    }
    shutdown() {
        for (const timeout of this.pendingMigrations.values()) {
            clearTimeout(timeout);
        }
        this.pendingMigrations.clear();
        this.migrationHistory.clear();
        this.hostQueue.clear();
        this.removeAllListeners();
    }
}
exports.HostMigrationManager = HostMigrationManager;
//# sourceMappingURL=host-migration-manager.js.map