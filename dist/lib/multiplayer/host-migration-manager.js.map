{"version": 3, "file": "host-migration-manager.js", "sourceRoot": "", "sources": ["../../../lib/multiplayer/host-migration-manager.ts"], "names": [], "mappings": ";;;AAKA,mCAAqC;AACrC,yDAAgE;AA8BhE,MAAa,oBAAqB,SAAQ,qBAAY;IAMpD,YAAY,WAA2C,EAAE;QACvD,KAAK,EAAE,CAAA;QALD,sBAAiB,GAAgC,IAAI,GAAG,EAAE,CAAA;QAC1D,qBAAgB,GAAmC,IAAI,GAAG,EAAE,CAAA;QAC5D,cAAS,GAA0B,IAAI,GAAG,EAAE,CAAA;QAKlD,IAAI,CAAC,QAAQ,GAAG;YACd,eAAe,EAAE,mBAAmB;YACpC,WAAW,EAAE,IAAI;YACjB,mBAAmB,EAAE,KAAK;YAC1B,oBAAoB,EAAE,IAAI;YAC1B,aAAa,EAAE,IAAI;YACnB,GAAG,QAAQ;SACZ,CAAA;IACH,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,MAAc,EAAE,MAAc;QAC1D,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wCAAwC,MAAM,UAAU,MAAM,EAAE,CAAC,CAAA;YAG7E,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAA;YAGnC,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;gBACpC,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;gBAC3C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;YACvC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;YAE7B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;YAE3C,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBACvC,MAAM;gBACN,MAAM;gBACN,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;aACvC,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAA;YAC5D,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAKD,eAAe,CAAC,MAAc;QAC5B,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;YAC5C,OAAO,IAAI,CAAA;QACb,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,SAAiB;QAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAE5B,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,kCAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YAClD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;YACnC,CAAC;YAGD,IAAI,IAAI,CAAC,MAAM,KAAK,6BAAU,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,6BAAU,CAAC,SAAS,EAAE,CAAC;gBAChF,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,0CAA0C,CAAC,CAAA;gBACrE,OAAM;YACR,CAAC;YAGD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;YACrD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;YAE1D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,MAAM,EAAE,CAAC,CAAA;gBAE1D,MAAM,kCAAe,CAAC,gBAAgB,CAAC,MAAM,EAAE,6BAAU,CAAC,SAAS,CAAC,CAAA;gBACpE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;oBAC5B,MAAM;oBACN,MAAM,EAAE,wBAAwB;iBACjC,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAGD,MAAM,OAAO,GAAG,MAAM,kCAAe,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAA;YAErF,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;YAC9C,CAAC;YAED,MAAM,MAAM,GAAoB;gBAC9B,OAAO,EAAE,IAAI;gBACb,SAAS;gBACT,SAAS,EAAE,OAAO,CAAC,QAAQ;gBAC3B,WAAW,EAAE,OAAO,CAAC,UAAU;gBAC/B,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACrC,MAAM,EAAE,oBAAoB;aAC7B,CAAA;YAGD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAGpC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACzB,MAAM;gBACN,GAAG,MAAM;aACV,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,uCAAuC,MAAM,SAAS,SAAS,OAAO,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAA;QAEvG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;YAE9C,MAAM,MAAM,GAAoB;gBAC9B,OAAO,EAAE,KAAK;gBACd,SAAS;gBACT,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,EAAE;gBACf,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACrC,MAAM,EAAE,KAAK,CAAC,OAAO;aACtB,CAAA;YAED,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAEpC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC5B,MAAM;gBACN,SAAS;gBACT,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,iBAAiB,CAAC,IAAS;QACvC,MAAM,UAAU,GAAoB,EAAE,CAAA;QACtC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEtB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAElC,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACzC,SAAQ;YACV,CAAC;YAED,MAAM,SAAS,GAAkB;gBAC/B,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,UAAU,EAAE,MAAM,CAAC,IAAI;gBACvB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACnC,YAAY,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACvC,iBAAiB,EAAE,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC;gBAC1D,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,CAAC;aACZ,CAAA;YAGD,IAAI,GAAG,GAAG,SAAS,CAAC,YAAY,GAAG,KAAK,EAAE,CAAC;gBACzC,SAAS,CAAC,UAAU,GAAG,KAAK,CAAA;YAC9B,CAAC;YAED,IAAI,SAAS,CAAC,iBAAiB,GAAG,GAAG,EAAE,CAAC;gBACtC,SAAS,CAAC,UAAU,GAAG,KAAK,CAAA;YAC9B,CAAC;YAGD,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;YAE5D,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAC5B,CAAC;QAED,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;IAC7C,CAAC;IAKO,KAAK,CAAC,aAAa,CACzB,UAA2B,EAC3B,IAAS;QAET,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAA;QACb,CAAC;QAGD,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAA;QAEjE,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;YAEtC,KAAK,MAAM,SAAS,IAAI,MAAM,EAAE,CAAC;gBAC/B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;gBACxE,IAAI,SAAS,EAAE,CAAC;oBACd,OAAO,SAAS,CAAA;gBAClB,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;aAAM,CAAC;YAEN,OAAO,MAAM,CAAC,CAAC,CAAC,CAAA;QAClB,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,SAAwB,EAAE,IAAS;QAC3D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEtB,QAAQ,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;YACtC,KAAK,mBAAmB;gBAEtB,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;YAE1C,KAAK,eAAe;gBAElB,OAAO,SAAS,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;YAEnE,KAAK,aAAa;gBAEhB,MAAM,iBAAiB,GAAG,CAAC,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,IAAI,CAAA;gBAC/D,OAAO,KAAK,GAAG,iBAAiB,CAAA;YAElC,KAAK,aAAa;gBAEhB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;gBAC/C,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;gBAClD,OAAO,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YAEpD;gBACE,OAAO,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAA;QAC7C,CAAC;IACH,CAAC;IAKO,0BAA0B,CAAC,MAAW;QAE5C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,iBAAiB,GAAG,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;QAEzD,IAAI,iBAAiB,GAAG,IAAI;YAAE,OAAO,GAAG,CAAA;QACxC,IAAI,iBAAiB,GAAG,KAAK;YAAE,OAAO,GAAG,CAAA;QACzC,IAAI,iBAAiB,GAAG,KAAK;YAAE,OAAO,GAAG,CAAA;QACzC,IAAI,iBAAiB,GAAG,KAAK;YAAE,OAAO,GAAG,CAAA;QACzC,OAAO,GAAG,CAAA;IACZ,CAAC;IAKO,KAAK,CAAC,uBAAuB,CACnC,SAAwB,EACxB,MAAc;QAEd,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,OAAO,CAAC,KAAK,CAAC,CAAA;YAChB,CAAC,EAAE,KAAK,CAAC,CAAA;YAET,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACrC,MAAM;gBACN,WAAW,EAAE,SAAS,CAAC,QAAQ;gBAC/B,aAAa,EAAE,SAAS,CAAC,UAAU;gBACnC,QAAQ,EAAE,CAAC,QAAiB,EAAE,EAAE;oBAC9B,YAAY,CAAC,OAAO,CAAC,CAAA;oBACrB,OAAO,CAAC,QAAQ,CAAC,CAAA;gBACnB,CAAC;aACF,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAKD,mBAAmB,CAAC,MAAc,EAAE,SAAmB;QACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,KAAK,aAAa,EAAE,CAAC;YAEpD,MAAM,QAAQ,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAA;YAC/D,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;QACtC,CAAC;IACH,CAAC;IAKD,eAAe,CAAC,MAAc,EAAE,QAAgB,EAAE,MAAwB;QACxE,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;QAE9C,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClD,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACtB,CAAC;aAAM,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;YACrC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;gBACf,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;YACxB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACnC,CAAC;IAKO,sBAAsB,CAAC,MAAc;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAClD,IAAI,OAAO,EAAE,CAAC;YACZ,YAAY,CAAC,OAAO,CAAC,CAAA;YACrB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;YACrC,OAAO,IAAI,CAAA;QACb,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAKO,eAAe,CAAC,MAAc,EAAE,MAAuB;QAC7D,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;QACvD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAGpB,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACxB,OAAO,CAAC,KAAK,EAAE,CAAA;QACjB,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC5C,CAAC;IAKD,mBAAmB,CAAC,MAAc;QAChC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;IAChD,CAAC;IAKD,KAAK,CAAC,kBAAkB,CACtB,MAAc,EACd,SAAiB;QAEjB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAE5B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,kCAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YAClD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;YACnC,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAA;YAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAA;YAE1D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;YAC/C,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,kCAAe,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;YAE9E,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;YAC9C,CAAC;YAED,MAAM,MAAM,GAAoB;gBAC9B,OAAO,EAAE,IAAI;gBACb,SAAS;gBACT,SAAS;gBACT,WAAW,EAAE,OAAO,CAAC,IAAI;gBACzB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACrC,MAAM,EAAE,uBAAuB;aAChC,CAAA;YAED,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YACpC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,CAAA;YAEjD,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,EAAE;gBACb,SAAS;gBACT,WAAW,EAAE,EAAE;gBACf,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACrC,MAAM,EAAE,KAAK,CAAC,OAAO;aACtB,CAAA;QACH,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,MAAc;QACxB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAC7B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IACtC,CAAC;IAKD,QAAQ;QAEN,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC;YACtD,YAAY,CAAC,OAAO,CAAC,CAAA;QACvB,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;QAC9B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAA;QAC7B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC3B,CAAC;CACF;AAvaD,oDAuaC"}