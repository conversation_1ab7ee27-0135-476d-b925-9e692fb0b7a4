"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AntiCheatValidator = void 0;
const events_1 = require("events");
class AntiCheatValidator extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.playerProfiles = new Map();
        this.questionDatabase = new Map();
        this.serverTimeOffset = 0;
        this.config = {
            enableTimingValidation: true,
            enablePatternDetection: true,
            enableImpossibleAnswerDetection: true,
            timingToleranceMs: 1000,
            suspiciousSpeedThreshold: 500,
            maxConsecutiveCorrect: 10,
            accuracyThreshold: 0.95,
            responseTimeVarianceThreshold: 0.3,
            ...config
        };
    }
    validateAnswer(playerId, answer, questionMetadata) {
        const indicators = [];
        const profile = this.getPlayerProfile(playerId);
        if (this.config.enableTimingValidation) {
            const timingIndicators = this.validateAnswerTiming(answer, questionMetadata);
            indicators.push(...timingIndicators);
        }
        if (this.config.enablePatternDetection) {
            const patternIndicators = this.detectSuspiciousPatterns(profile, answer);
            indicators.push(...patternIndicators);
        }
        if (this.config.enableImpossibleAnswerDetection) {
            const impossibleIndicators = this.detectImpossibleAnswers(answer, questionMetadata);
            indicators.push(...impossibleIndicators);
        }
        this.updatePlayerProfile(profile, answer, indicators);
        if (indicators.length > 0) {
            this.emit('cheat_indicators_detected', {
                playerId,
                indicators,
                riskScore: profile.riskScore
            });
        }
        return indicators;
    }
    validateAnswerTiming(answer, question) {
        const indicators = [];
        const serverTime = Date.now();
        if (answer.timeSpent < this.config.suspiciousSpeedThreshold) {
            indicators.push({
                type: 'timing',
                severity: 'high',
                description: `Answer submitted suspiciously fast: ${answer.timeSpent}ms`,
                evidence: {
                    timeSpent: answer.timeSpent,
                    threshold: this.config.suspiciousSpeedThreshold
                },
                timestamp: serverTime,
                confidence: 0.8
            });
        }
        const timeLimit = question.timeLimit * 1000;
        if (answer.timeSpent > timeLimit + this.config.timingToleranceMs) {
            indicators.push({
                type: 'timing',
                severity: 'medium',
                description: `Answer submitted after time limit: ${answer.timeSpent}ms vs ${timeLimit}ms`,
                evidence: {
                    timeSpent: answer.timeSpent,
                    timeLimit,
                    tolerance: this.config.timingToleranceMs
                },
                timestamp: serverTime,
                confidence: 0.9
            });
        }
        if (answer.clientTimestamp) {
            const expectedClientTime = answer.questionStartTime + answer.timeSpent;
            const discrepancy = Math.abs(answer.clientTimestamp - expectedClientTime);
            if (discrepancy > 5000) {
                indicators.push({
                    type: 'timing',
                    severity: 'medium',
                    description: `Large client-server time discrepancy: ${discrepancy}ms`,
                    evidence: {
                        clientTimestamp: answer.clientTimestamp,
                        expectedClientTime,
                        discrepancy
                    },
                    timestamp: serverTime,
                    confidence: 0.7
                });
            }
        }
        return indicators;
    }
    detectSuspiciousPatterns(profile, answer) {
        const indicators = [];
        const answers = [...profile.answers, answer];
        if (answer.isCorrect) {
            profile.consecutiveCorrect++;
            if (profile.consecutiveCorrect > this.config.maxConsecutiveCorrect) {
                indicators.push({
                    type: 'pattern',
                    severity: 'high',
                    description: `Too many consecutive correct answers: ${profile.consecutiveCorrect}`,
                    evidence: {
                        consecutiveCorrect: profile.consecutiveCorrect,
                        threshold: this.config.maxConsecutiveCorrect
                    },
                    timestamp: Date.now(),
                    confidence: 0.8
                });
            }
        }
        else {
            profile.consecutiveCorrect = 0;
        }
        const correctAnswers = answers.filter(a => a.isCorrect).length;
        const accuracyRate = correctAnswers / answers.length;
        if (accuracyRate > this.config.accuracyThreshold && answers.length >= 5) {
            indicators.push({
                type: 'pattern',
                severity: 'medium',
                description: `Suspiciously high accuracy rate: ${(accuracyRate * 100).toFixed(1)}%`,
                evidence: {
                    accuracyRate,
                    threshold: this.config.accuracyThreshold,
                    totalAnswers: answers.length
                },
                timestamp: Date.now(),
                confidence: 0.6
            });
        }
        if (answers.length >= 3) {
            const recentAnswers = answers.slice(-5);
            const avgResponseTime = recentAnswers.reduce((sum, a) => sum + a.timeSpent, 0) / recentAnswers.length;
            if (avgResponseTime < this.config.suspiciousSpeedThreshold * 2) {
                indicators.push({
                    type: 'pattern',
                    severity: 'medium',
                    description: `Consistently fast responses: ${avgResponseTime.toFixed(0)}ms average`,
                    evidence: {
                        averageResponseTime: avgResponseTime,
                        threshold: this.config.suspiciousSpeedThreshold * 2,
                        sampleSize: recentAnswers.length
                    },
                    timestamp: Date.now(),
                    confidence: 0.7
                });
            }
        }
        if (answers.length >= 5) {
            const times = answers.map(a => a.timeSpent);
            const mean = times.reduce((a, b) => a + b, 0) / times.length;
            const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
            const coefficientOfVariation = Math.sqrt(variance) / mean;
            if (coefficientOfVariation < this.config.responseTimeVarianceThreshold) {
                indicators.push({
                    type: 'pattern',
                    severity: 'medium',
                    description: `Unnaturally consistent response times: CV=${coefficientOfVariation.toFixed(3)}`,
                    evidence: {
                        coefficientOfVariation,
                        threshold: this.config.responseTimeVarianceThreshold,
                        mean,
                        variance
                    },
                    timestamp: Date.now(),
                    confidence: 0.6
                });
            }
        }
        return indicators;
    }
    detectImpossibleAnswers(answer, question) {
        const indicators = [];
        const minHumanResponseTime = 100;
        if (answer.timeSpent < minHumanResponseTime) {
            indicators.push({
                type: 'impossible',
                severity: 'critical',
                description: `Answer faster than human reaction time: ${answer.timeSpent}ms`,
                evidence: {
                    timeSpent: answer.timeSpent,
                    minHumanTime: minHumanResponseTime
                },
                timestamp: Date.now(),
                confidence: 1.0
            });
        }
        if (!this.isValidAnswerFormat(answer.answer, question)) {
            indicators.push({
                type: 'impossible',
                severity: 'high',
                description: 'Invalid answer format',
                evidence: {
                    answer: answer.answer,
                    expectedType: question.type,
                    questionId: question.id
                },
                timestamp: Date.now(),
                confidence: 0.9
            });
        }
        if (question.type === 'slider' && typeof answer.answer === 'number') {
            if (answer.answer < question.sliderMin || answer.answer > question.sliderMax) {
                indicators.push({
                    type: 'impossible',
                    severity: 'high',
                    description: `Slider answer out of bounds: ${answer.answer}`,
                    evidence: {
                        answer: answer.answer,
                        min: question.sliderMin,
                        max: question.sliderMax
                    },
                    timestamp: Date.now(),
                    confidence: 1.0
                });
            }
        }
        return indicators;
    }
    isValidAnswerFormat(answer, question) {
        switch (question.type) {
            case 'multiple-choice':
                return typeof answer === 'number' &&
                    answer >= 0 &&
                    answer < (question.options?.length || 0);
            case 'true-false':
                return typeof answer === 'boolean' ||
                    (typeof answer === 'number' && (answer === 0 || answer === 1));
            case 'slider':
                return typeof answer === 'number' &&
                    answer >= question.sliderMin &&
                    answer <= question.sliderMax;
            default:
                return true;
        }
    }
    getPlayerProfile(playerId) {
        if (!this.playerProfiles.has(playerId)) {
            this.playerProfiles.set(playerId, {
                playerId,
                answers: [],
                averageResponseTime: 0,
                accuracyRate: 0,
                responseTimeVariance: 0,
                consecutiveCorrect: 0,
                suspiciousEvents: [],
                riskScore: 0
            });
        }
        return this.playerProfiles.get(playerId);
    }
    updatePlayerProfile(profile, answer, indicators) {
        profile.answers.push(answer);
        if (profile.answers.length > 50) {
            profile.answers = profile.answers.slice(-50);
        }
        const answers = profile.answers;
        profile.averageResponseTime = answers.reduce((sum, a) => sum + a.timeSpent, 0) / answers.length;
        profile.accuracyRate = answers.filter(a => a.isCorrect).length / answers.length;
        const mean = profile.averageResponseTime;
        const variance = answers.reduce((sum, a) => sum + Math.pow(a.timeSpent - mean, 2), 0) / answers.length;
        profile.responseTimeVariance = Math.sqrt(variance) / mean;
        profile.suspiciousEvents.push(...indicators);
        const cutoff = Date.now() - 24 * 60 * 60 * 1000;
        profile.suspiciousEvents = profile.suspiciousEvents.filter(e => e.timestamp > cutoff);
        profile.riskScore = this.calculateRiskScore(profile);
    }
    calculateRiskScore(profile) {
        let score = 0;
        for (const event of profile.suspiciousEvents) {
            switch (event.severity) {
                case 'critical':
                    score += 10 * event.confidence;
                    break;
                case 'high':
                    score += 5 * event.confidence;
                    break;
                case 'medium':
                    score += 2 * event.confidence;
                    break;
                case 'low':
                    score += 1 * event.confidence;
                    break;
            }
        }
        if (profile.accuracyRate > 0.95 && profile.answers.length >= 10) {
            score += 3;
        }
        if (profile.averageResponseTime < 1000 && profile.answers.length >= 5) {
            score += 2;
        }
        if (profile.responseTimeVariance < 0.2 && profile.answers.length >= 5) {
            score += 2;
        }
        return Math.min(100, score);
    }
    getPlayerRiskAssessment(playerId) {
        const profile = this.playerProfiles.get(playerId);
        if (!profile)
            return null;
        let riskLevel;
        if (profile.riskScore < 10)
            riskLevel = 'low';
        else if (profile.riskScore < 25)
            riskLevel = 'medium';
        else if (profile.riskScore < 50)
            riskLevel = 'high';
        else
            riskLevel = 'critical';
        return {
            riskScore: profile.riskScore,
            riskLevel,
            indicators: profile.suspiciousEvents,
            statistics: {
                totalAnswers: profile.answers.length,
                accuracyRate: profile.accuracyRate,
                averageResponseTime: profile.averageResponseTime,
                responseTimeVariance: profile.responseTimeVariance,
                consecutiveCorrect: profile.consecutiveCorrect
            }
        };
    }
    flagPlayerForReview(playerId, reason) {
        this.emit('player_flagged', {
            playerId,
            reason,
            timestamp: Date.now(),
            riskAssessment: this.getPlayerRiskAssessment(playerId)
        });
    }
    resetPlayerProfile(playerId) {
        if (this.playerProfiles.has(playerId)) {
            const profile = this.getPlayerProfile(playerId);
            profile.suspiciousEvents = [];
            profile.riskScore = 0;
            profile.consecutiveCorrect = 0;
            return true;
        }
        return false;
    }
    getHighRiskPlayers() {
        const highRisk = [];
        for (const [playerId, profile] of this.playerProfiles) {
            if (profile.riskScore >= 25) {
                const assessment = this.getPlayerRiskAssessment(playerId);
                highRisk.push({
                    playerId,
                    riskScore: profile.riskScore,
                    riskLevel: assessment.riskLevel
                });
            }
        }
        return highRisk.sort((a, b) => b.riskScore - a.riskScore);
    }
    cleanup() {
        const cutoff = Date.now() - 24 * 60 * 60 * 1000;
        for (const [playerId, profile] of this.playerProfiles) {
            const lastActivity = Math.max(...profile.answers.map(a => a.submittedAt));
            if (lastActivity < cutoff) {
                this.playerProfiles.delete(playerId);
            }
        }
    }
}
exports.AntiCheatValidator = AntiCheatValidator;
//# sourceMappingURL=anti-cheat-validator.js.map