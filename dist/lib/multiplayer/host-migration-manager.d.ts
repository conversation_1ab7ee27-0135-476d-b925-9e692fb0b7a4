import { EventEmitter } from 'events';
export interface HostMigrationStrategy {
    selectionMethod: 'longest-connected' | 'highest-score' | 'most-active' | 'round-robin';
    gracePeriod: number;
    requireConfirmation: boolean;
    preserveGameSettings: boolean;
    notifyPlayers: boolean;
}
export interface HostCandidate {
    playerId: string;
    playerName: string;
    score: number;
    joinedAt: number;
    lastActivity: number;
    connectionQuality: number;
    isEligible: boolean;
    priority: number;
}
export interface MigrationResult {
    success: boolean;
    oldHostId: string;
    newHostId: string;
    newHostName: string;
    migrationTime: number;
    reason: string;
}
export declare class HostMigrationManager extends EventEmitter {
    private strategy;
    private pendingMigrations;
    private migrationHistory;
    private hostQueue;
    constructor(strategy?: Partial<HostMigrationStrategy>);
    detectHostDisconnection(gameId: string, hostId: string): Promise<void>;
    cancelMigration(gameId: string): boolean;
    private performMigration;
    private getHostCandidates;
    private selectNewHost;
    private calculatePriority;
    private calculateConnectionQuality;
    private requestHostConfirmation;
    initializeHostQueue(gameId: string, playerIds: string[]): void;
    updateHostQueue(gameId: string, playerId: string, action: 'add' | 'remove'): void;
    private cancelPendingMigration;
    private recordMigration;
    getMigrationHistory(gameId: string): MigrationResult[];
    forceHostMigration(gameId: string, newHostId: string): Promise<MigrationResult>;
    cleanupGame(gameId: string): void;
    shutdown(): void;
}
