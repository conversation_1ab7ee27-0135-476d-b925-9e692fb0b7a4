export interface PersistedGame {
    id: string;
    gamePin: string;
    hostId: string;
    status: GameStatus;
    settings: GameSettings;
    players: PersistedPlayer[];
    currentQuestionIndex: number;
    questions: any[];
    leaderboard: LeaderboardEntry[];
    createdAt: Date;
    updatedAt: Date;
    startedAt?: Date;
    endedAt?: Date;
}
export interface PersistedPlayer {
    id: string;
    name: string;
    avatar?: string;
    score: number;
    isHost: boolean;
    isConnected: boolean;
    hasAnswered: boolean;
    joinedAt: Date;
    lastSeen: Date;
    answers: PlayerAnswer[];
}
export interface PlayerAnswer {
    questionId: string;
    answer: any;
    isCorrect: boolean;
    score: number;
    timeSpent: number;
    submittedAt: Date;
}
export interface GameSettings {
    gameMode: string;
    category: string;
    totalQuestions: number;
    timePerQuestion: number;
    difficulty: number;
    enableHints: boolean;
    enableAudio: boolean;
    allowSpectators: boolean;
    maxPlayers: number;
    isPrivate: boolean;
}
export interface LeaderboardEntry {
    playerId: string;
    playerName: string;
    score: number;
    correctAnswers: number;
    averageResponseTime: number;
    currentStreak: number;
    maxStreak: number;
    position: number;
}
export declare enum GameStatus {
    WAITING = "waiting",
    COUNTDOWN = "countdown",
    PLAYING = "playing",
    QUESTION_RESULTS = "question-results",
    FINISHED = "finished",
    ABANDONED = "abandoned"
}
export declare class GamePersistenceService {
    private static instance;
    static getInstance(): GamePersistenceService;
    createGame(gameId: string, gamePin: string, hostId: string, hostName: string, settings: GameSettings): Promise<PersistedGame>;
    getGame(gameId: string): Promise<PersistedGame | null>;
    getGameByPin(gamePin: string): Promise<PersistedGame | null>;
    updateGameStatus(gameId: string, status: GameStatus): Promise<boolean>;
    updateGameQuestions(gameId: string, questions: any[]): Promise<boolean>;
    updateCurrentQuestion(gameId: string, questionIndex: number): Promise<boolean>;
    updateLeaderboard(gameId: string, leaderboard: LeaderboardEntry[]): Promise<boolean>;
    addPlayer(gameId: string, playerId: string, playerName: string, avatar?: string): Promise<string | null>;
    updatePlayerConnection(playerId: string, isConnected: boolean): Promise<boolean>;
    updatePlayerScore(playerId: string, score: number): Promise<boolean>;
    updatePlayerAnswerStatus(playerId: string, hasAnswered: boolean): Promise<boolean>;
    addPlayerAnswer(playerId: string, questionId: string, answer: any, isCorrect: boolean, score: number, timeSpent: number): Promise<boolean>;
    removePlayer(playerId: string): Promise<boolean>;
    changeHost(gameId: string, oldHostId: string, newHostId: string): Promise<boolean>;
    getActiveGames(): Promise<PersistedGame[]>;
    cleanupOldGames(olderThanHours?: number): Promise<number>;
    markAbandonedGames(inactiveMinutes?: number): Promise<number>;
    getGameStats(): Promise<{
        totalGames: number;
        activeGames: number;
        finishedGames: number;
        totalPlayers: number;
        averageGameDuration: number;
    }>;
    private mapToPersistedGame;
}
export declare const gamePersistence: GamePersistenceService;
