{"version": 3, "file": "anti-cheat-validator.js", "sourceRoot": "", "sources": ["../../../lib/multiplayer/anti-cheat-validator.ts"], "names": [], "mappings": ";;;AAKA,mCAAqC;AA2CrC,MAAa,kBAAmB,SAAQ,qBAAY;IAMlD,YAAY,SAAoC,EAAE;QAChD,KAAK,EAAE,CAAA;QALD,mBAAc,GAA+B,IAAI,GAAG,EAAE,CAAA;QACtD,qBAAgB,GAAkC,IAAI,GAAG,EAAE,CAAA;QAC3D,qBAAgB,GAAW,CAAC,CAAA;QAKlC,IAAI,CAAC,MAAM,GAAG;YACZ,sBAAsB,EAAE,IAAI;YAC5B,sBAAsB,EAAE,IAAI;YAC5B,+BAA+B,EAAE,IAAI;YACrC,iBAAiB,EAAE,IAAI;YACvB,wBAAwB,EAAE,GAAG;YAC7B,qBAAqB,EAAE,EAAE;YACzB,iBAAiB,EAAE,IAAI;YACvB,6BAA6B,EAAE,GAAG;YAClC,GAAG,MAAM;SACV,CAAA;IACH,CAAC;IAKD,cAAc,CACZ,QAAgB,EAChB,MAAoB,EACpB,gBAAkC;QAElC,MAAM,UAAU,GAAqB,EAAE,CAAA;QAGvC,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;QAG/C,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;YACvC,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAA;YAC5E,UAAU,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAA;QACtC,CAAC;QAGD,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;YACvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YACxE,UAAU,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAA;QACvC,CAAC;QAGD,IAAI,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAE,CAAC;YAChD,MAAM,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAA;YACnF,UAAU,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,CAAA;QAC1C,CAAC;QAGD,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,CAAA;QAGrD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACrC,QAAQ;gBACR,UAAU;gBACV,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,UAAU,CAAA;IACnB,CAAC;IAKO,oBAAoB,CAC1B,MAAoB,EACpB,QAA0B;QAE1B,MAAM,UAAU,GAAqB,EAAE,CAAA;QACvC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAG7B,IAAI,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,EAAE,CAAC;YAC5D,UAAU,CAAC,IAAI,CAAC;gBACd,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,MAAM;gBAChB,WAAW,EAAE,uCAAuC,MAAM,CAAC,SAAS,IAAI;gBACxE,QAAQ,EAAE;oBACR,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,wBAAwB;iBAChD;gBACD,SAAS,EAAE,UAAU;gBACrB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAA;QACJ,CAAC;QAGD,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAA;QAC3C,IAAI,MAAM,CAAC,SAAS,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YACjE,UAAU,CAAC,IAAI,CAAC;gBACd,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,sCAAsC,MAAM,CAAC,SAAS,SAAS,SAAS,IAAI;gBACzF,QAAQ,EAAE;oBACR,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,SAAS;oBACT,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB;iBACzC;gBACD,SAAS,EAAE,UAAU;gBACrB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAA;QACJ,CAAC;QAGD,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;YAC3B,MAAM,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,GAAG,MAAM,CAAC,SAAS,CAAA;YACtE,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,eAAe,GAAG,kBAAkB,CAAC,CAAA;YAEzE,IAAI,WAAW,GAAG,IAAI,EAAE,CAAC;gBACvB,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,QAAQ;oBAClB,WAAW,EAAE,yCAAyC,WAAW,IAAI;oBACrE,QAAQ,EAAE;wBACR,eAAe,EAAE,MAAM,CAAC,eAAe;wBACvC,kBAAkB;wBAClB,WAAW;qBACZ;oBACD,SAAS,EAAE,UAAU;oBACrB,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAA;IACnB,CAAC;IAKO,wBAAwB,CAC9B,OAAsB,EACtB,MAAoB;QAEpB,MAAM,UAAU,GAAqB,EAAE,CAAA;QACvC,MAAM,OAAO,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QAG5C,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,OAAO,CAAC,kBAAkB,EAAE,CAAA;YAE5B,IAAI,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;gBACnE,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,SAAS;oBACf,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,yCAAyC,OAAO,CAAC,kBAAkB,EAAE;oBAClF,QAAQ,EAAE;wBACR,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;wBAC9C,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,qBAAqB;qBAC7C;oBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAA;QAChC,CAAC;QAGD,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAA;QAC9D,MAAM,YAAY,GAAG,cAAc,GAAG,OAAO,CAAC,MAAM,CAAA;QAEpD,IAAI,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACxE,UAAU,CAAC,IAAI,CAAC;gBACd,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,oCAAoC,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBACnF,QAAQ,EAAE;oBACR,YAAY;oBACZ,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB;oBACxC,YAAY,EAAE,OAAO,CAAC,MAAM;iBAC7B;gBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAA;QACJ,CAAC;QAGD,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACxB,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;YACvC,MAAM,eAAe,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAA;YAErG,IAAI,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,GAAG,CAAC,EAAE,CAAC;gBAC/D,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,SAAS;oBACf,QAAQ,EAAE,QAAQ;oBAClB,WAAW,EAAE,gCAAgC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY;oBACnF,QAAQ,EAAE;wBACR,mBAAmB,EAAE,eAAe;wBACpC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,wBAAwB,GAAG,CAAC;wBACnD,UAAU,EAAE,aAAa,CAAC,MAAM;qBACjC;oBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAGD,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACxB,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;YAC3C,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;YAC5D,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;YAC9F,MAAM,sBAAsB,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;YAEzD,IAAI,sBAAsB,GAAG,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC;gBACvE,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,SAAS;oBACf,QAAQ,EAAE,QAAQ;oBAClB,WAAW,EAAE,6CAA6C,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBAC7F,QAAQ,EAAE;wBACR,sBAAsB;wBACtB,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,6BAA6B;wBACpD,IAAI;wBACJ,QAAQ;qBACT;oBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAA;IACnB,CAAC;IAKO,uBAAuB,CAC7B,MAAoB,EACpB,QAA0B;QAE1B,MAAM,UAAU,GAAqB,EAAE,CAAA;QAGvC,MAAM,oBAAoB,GAAG,GAAG,CAAA;QAChC,IAAI,MAAM,CAAC,SAAS,GAAG,oBAAoB,EAAE,CAAC;YAC5C,UAAU,CAAC,IAAI,CAAC;gBACd,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,UAAU;gBACpB,WAAW,EAAE,2CAA2C,MAAM,CAAC,SAAS,IAAI;gBAC5E,QAAQ,EAAE;oBACR,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,YAAY,EAAE,oBAAoB;iBACnC;gBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAA;QACJ,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC;YACvD,UAAU,CAAC,IAAI,CAAC;gBACd,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,MAAM;gBAChB,WAAW,EAAE,uBAAuB;gBACpC,QAAQ,EAAE;oBACR,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,YAAY,EAAE,QAAQ,CAAC,IAAI;oBAC3B,UAAU,EAAE,QAAQ,CAAC,EAAE;iBACxB;gBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAA;QACJ,CAAC;QAGD,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACpE,IAAI,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC7E,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,YAAY;oBAClB,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,gCAAgC,MAAM,CAAC,MAAM,EAAE;oBAC5D,QAAQ,EAAE;wBACR,MAAM,EAAE,MAAM,CAAC,MAAM;wBACrB,GAAG,EAAE,QAAQ,CAAC,SAAS;wBACvB,GAAG,EAAE,QAAQ,CAAC,SAAS;qBACxB;oBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAA;IACnB,CAAC;IAKO,mBAAmB,CAAC,MAAW,EAAE,QAA0B;QACjE,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,iBAAiB;gBACpB,OAAO,OAAO,MAAM,KAAK,QAAQ;oBAC1B,MAAM,IAAI,CAAC;oBACX,MAAM,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,CAAA;YAEjD,KAAK,YAAY;gBACf,OAAO,OAAO,MAAM,KAAK,SAAS;oBAC3B,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,CAAC,CAAC,CAAA;YAEvE,KAAK,QAAQ;gBACX,OAAO,OAAO,MAAM,KAAK,QAAQ;oBAC1B,MAAM,IAAI,QAAQ,CAAC,SAAS;oBAC5B,MAAM,IAAI,QAAQ,CAAC,SAAS,CAAA;YAErC;gBACE,OAAO,IAAI,CAAA;QACf,CAAC;IACH,CAAC;IAKO,gBAAgB,CAAC,QAAgB;QACvC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAChC,QAAQ;gBACR,OAAO,EAAE,EAAE;gBACX,mBAAmB,EAAE,CAAC;gBACtB,YAAY,EAAE,CAAC;gBACf,oBAAoB,EAAE,CAAC;gBACvB,kBAAkB,EAAE,CAAC;gBACrB,gBAAgB,EAAE,EAAE;gBACpB,SAAS,EAAE,CAAC;aACb,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAA;IAC3C,CAAC;IAKO,mBAAmB,CACzB,OAAsB,EACtB,MAAoB,EACpB,UAA4B;QAG5B,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAG5B,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAChC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAA;QAC9C,CAAC;QAGD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QAC/B,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAA;QAC/F,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAG/E,MAAM,IAAI,GAAG,OAAO,CAAC,mBAAmB,CAAA;QACxC,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAA;QACtG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;QAGzD,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAA;QAG5C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;QAC/C,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,CAAA;QAGrF,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;IACtD,CAAC;IAKO,kBAAkB,CAAC,OAAsB;QAC/C,IAAI,KAAK,GAAG,CAAC,CAAA;QAGb,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7C,QAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACvB,KAAK,UAAU;oBAAE,KAAK,IAAI,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC;oBAAC,MAAK;gBACtD,KAAK,MAAM;oBAAE,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;oBAAC,MAAK;gBACjD,KAAK,QAAQ;oBAAE,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;oBAAC,MAAK;gBACnD,KAAK,KAAK;oBAAE,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;oBAAC,MAAK;YAClD,CAAC;QACH,CAAC;QAGD,IAAI,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;YAChE,KAAK,IAAI,CAAC,CAAA;QACZ,CAAC;QAED,IAAI,OAAO,CAAC,mBAAmB,GAAG,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACtE,KAAK,IAAI,CAAC,CAAA;QACZ,CAAC;QAED,IAAI,OAAO,CAAC,oBAAoB,GAAG,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACtE,KAAK,IAAI,CAAC,CAAA;QACZ,CAAC;QAGD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IAC7B,CAAC;IAKD,uBAAuB,CAAC,QAAgB;QAMtC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAA;QAEzB,IAAI,SAAiD,CAAA;QACrD,IAAI,OAAO,CAAC,SAAS,GAAG,EAAE;YAAE,SAAS,GAAG,KAAK,CAAA;aACxC,IAAI,OAAO,CAAC,SAAS,GAAG,EAAE;YAAE,SAAS,GAAG,QAAQ,CAAA;aAChD,IAAI,OAAO,CAAC,SAAS,GAAG,EAAE;YAAE,SAAS,GAAG,MAAM,CAAA;;YAC9C,SAAS,GAAG,UAAU,CAAA;QAE3B,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,SAAS;YACT,UAAU,EAAE,OAAO,CAAC,gBAAgB;YACpC,UAAU,EAAE;gBACV,YAAY,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;gBACpC,YAAY,EAAE,OAAO,CAAC,YAAY;gBAClC,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;gBAChD,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;gBAClD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;aAC/C;SACF,CAAA;IACH,CAAC;IAKD,mBAAmB,CAAC,QAAgB,EAAE,MAAc;QAClD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,QAAQ;YACR,MAAM;YACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;SACvD,CAAC,CAAA;IACJ,CAAC;IAKD,kBAAkB,CAAC,QAAgB;QACjC,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;YAC/C,OAAO,CAAC,gBAAgB,GAAG,EAAE,CAAA;YAC7B,OAAO,CAAC,SAAS,GAAG,CAAC,CAAA;YACrB,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAA;YAC9B,OAAO,IAAI,CAAA;QACb,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAKD,kBAAkB;QAKhB,MAAM,QAAQ,GAAG,EAAE,CAAA;QAEnB,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtD,IAAI,OAAO,CAAC,SAAS,IAAI,EAAE,EAAE,CAAC;gBAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAE,CAAA;gBAC1D,QAAQ,CAAC,IAAI,CAAC;oBACZ,QAAQ;oBACR,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,SAAS,EAAE,UAAU,CAAC,SAAS;iBAChC,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAA;IAC3D,CAAC;IAKD,OAAO;QACL,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;QAE/C,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA;YAEzE,IAAI,YAAY,GAAG,MAAM,EAAE,CAAC;gBAC1B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YACtC,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAxfD,gDAwfC"}