{"version": 3, "file": "reconnection-manager.js", "sourceRoot": "", "sources": ["../../../lib/multiplayer/reconnection-manager.ts"], "names": [], "mappings": ";;;AAKA,mCAAqC;AACrC,yDAAoD;AA2CpD,MAAa,mBAAoB,SAAQ,qBAAY;IAMnD,YAAY,WAA0C,EAAE;QACtD,KAAK,EAAE,CAAA;QALD,qBAAgB,GAAwC,IAAI,GAAG,EAAE,CAAA;QACjE,uBAAkB,GAAwB,IAAI,GAAG,EAAE,CAAA;QACnD,oBAAe,GAA0B,IAAI,CAAA;QAKnD,IAAI,CAAC,QAAQ,GAAG;YACd,WAAW,EAAE,CAAC;YACd,eAAe,EAAE,aAAa;YAC9B,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,mBAAmB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;YACnC,eAAe,EAAE,IAAI;YACrB,aAAa,EAAE,IAAI;YACnB,eAAe,EAAE,IAAI;YACrB,GAAG,QAAQ;SACZ,CAAA;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAA;IACzB,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,QAAgB,EAChB,MAAc,EACd,QAAgB,EAChB,SAA0B;QAE1B,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAA;YAG9C,MAAM,IAAI,GAAG,MAAM,kCAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YAClD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;YACnC,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAA;YACxD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAA;YACrC,CAAC;YAGD,MAAM,gBAAgB,GAA2B;gBAC/C,QAAQ;gBACR,MAAM;gBACN,UAAU,EAAE,MAAM,CAAC,IAAI;gBACvB,YAAY,EAAE,QAAQ;gBACtB,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE;gBAC1B,SAAS;gBACT,iBAAiB,EAAE,KAAK;gBACxB,YAAY,EAAE,CAAC;aAChB,CAAA;YAED,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAA;YACrD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;YAG5C,MAAM,kCAAe,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;YAG7D,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC/B,QAAQ;gBACR,MAAM;gBACN,UAAU,EAAE,MAAM,CAAC,IAAI;gBACvB,YAAY,EAAE,IAAI;gBAClB,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,mBAAmB;aACtD,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,uCAAuC,QAAQ,YAAY,MAAM,EAAE,CAAC,CAAA;YAEhF,OAAO,KAAK,CAAA;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAA;YACzD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,KAAa,EACb,WAAmB;QAEnB,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,4BAA4B;iBACpC,CAAA;YACH,CAAC;YAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAC5D,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,QAAQ;oBACR,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,6BAA6B;iBACrC,CAAA;YACH,CAAC;YAGD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,cAAc,CAAA;YAC5D,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;gBAChD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;gBACtB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,QAAQ;oBACR,MAAM,EAAE,gBAAgB,CAAC,MAAM;oBAC/B,KAAK,EAAE,6BAA6B;iBACrC,CAAA;YACH,CAAC;YAGD,gBAAgB,CAAC,YAAY,EAAE,CAAA;YAC/B,IAAI,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAC9D,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;gBACtB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,QAAQ;oBACR,MAAM,EAAE,gBAAgB,CAAC,MAAM;oBAC/B,KAAK,EAAE,wCAAwC;iBAChD,CAAA;YACH,CAAC;YAGD,MAAM,IAAI,GAAG,MAAM,kCAAe,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;YACnE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBACvE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;gBACtB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,QAAQ;oBACR,MAAM,EAAE,gBAAgB,CAAC,MAAM;oBAC/B,KAAK,EAAE,uBAAuB;iBAC/B,CAAA;YACH,CAAC;YAGD,MAAM,kCAAe,CAAC,sBAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;YAG5D,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe;gBAClD,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC;gBAC3C,CAAC,CAAC,SAAS,CAAA;YAGb,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;YAGtB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC9B,QAAQ;gBACR,MAAM,EAAE,gBAAgB,CAAC,MAAM;gBAC/B,UAAU,EAAE,gBAAgB,CAAC,UAAU;gBACvC,WAAW;gBACX,cAAc;gBACd,qBAAqB,EAAE,OAAO;aAC/B,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,UAAU,QAAQ,qCAAqC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAA;YAE7F,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,QAAQ;gBACR,MAAM,EAAE,gBAAgB,CAAC,MAAM;gBAC/B,cAAc;gBACd,WAAW;aACZ,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;YACpD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAA;QACH,CAAC;IACH,CAAC;IAKD,YAAY,CAAC,QAAgB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAChD,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAA;QAEvB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAA;QAChD,OAAO,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB;YAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAA;IACtD,CAAC;IAKD,mBAAmB,CAAC,QAAgB;QAKlC,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAChD,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAA;QAEtB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAA;QAChD,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,OAAO,CAAC,CAAA;QAC9E,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,CAAA;QAEpF,OAAO;YACL,YAAY,EAAE,aAAa,GAAG,CAAC,IAAI,iBAAiB,GAAG,CAAC;YACxD,aAAa;YACb,iBAAiB;SAClB,CAAA;IACH,CAAC;IAKD,oBAAoB,CAAC,YAAoB;QACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,KAAK,aAAa,EAAE,CAAC;YACpD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAA;YACrE,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;QAChD,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAA;YACpD,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;QAChD,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,IAA4B;QACrD,MAAM,KAAK,GAAoB;YAC7B,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7D,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACpE,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,oBAAoB;YACzD,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW;YACvC,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;YACxB,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,EAAE;YAC/C,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,EAAE;SACxC,CAAA;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAKO,yBAAyB;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QAC3D,OAAO,GAAG,SAAS,IAAI,MAAM,IAAI,OAAO,EAAE,CAAA;IAC5C,CAAC;IAKO,OAAO,CAAC,QAAgB;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAChD,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;YACtD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAKO,gBAAgB;QACtB,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACtB,MAAM,OAAO,GAAa,EAAE,CAAA;YAE5B,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACrD,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAA;gBACzC,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;oBAChD,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBACxB,CAAC;YACH,CAAC;YAED,KAAK,MAAM,QAAQ,IAAI,OAAO,EAAE,CAAC;gBAC/B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;gBACtB,OAAO,CAAC,GAAG,CAAC,mDAAmD,QAAQ,EAAE,CAAC,CAAA;YAC5E,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAA;IACX,CAAC;IAKD,YAAY,CAAC,QAAgB;QAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC/C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QACtB,OAAO,GAAG,CAAA;IACZ,CAAC;IAKD,uBAAuB;QAOrB,MAAM,OAAO,GAAG,EAAE,CAAA;QAClB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEtB,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACrD,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAA;YACzC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,OAAO,CAAC,CAAA;YAE9E,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;gBACtB,OAAO,CAAC,IAAI,CAAC;oBACX,QAAQ;oBACR,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;oBACnC,aAAa;iBACd,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAKD,QAAQ;QACN,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QACrC,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAA;QAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC3B,CAAC;CACF;AA7VD,kDA6VC"}