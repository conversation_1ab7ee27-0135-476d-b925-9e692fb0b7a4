import { EventEmitter } from 'events';
export interface ReconnectionStrategy {
    maxAttempts: number;
    backoffStrategy: 'exponential' | 'linear';
    baseDelay: number;
    maxDelay: number;
    stateRecoveryWindow: number;
    gracefulHandoff: boolean;
    preserveScore: boolean;
    preserveAnswers: boolean;
}
export interface PlayerReconnectionData {
    playerId: string;
    gameId: string;
    playerName: string;
    lastSocketId: string;
    disconnectedAt: number;
    gameState: PlayerGameState;
    reconnectionToken: string;
    attemptCount: number;
}
export interface PlayerGameState {
    score: number;
    answers: any[];
    currentQuestionIndex: number;
    hasAnswered: boolean;
    lastActivity: number;
    achievements: string[];
    powerUps: string[];
}
export interface ReconnectionResult {
    success: boolean;
    playerId: string;
    gameId: string;
    recoveredState?: PlayerGameState;
    newSocketId?: string;
    error?: string;
}
export declare class ReconnectionManager extends EventEmitter {
    private strategy;
    private reconnectionData;
    private reconnectionTokens;
    private cleanupInterval;
    constructor(strategy?: Partial<ReconnectionStrategy>);
    registerDisconnection(playerId: string, gameId: string, socketId: string, gameState: PlayerGameState): Promise<string>;
    attemptReconnection(token: string, newSocketId: string): Promise<ReconnectionResult>;
    canReconnect(playerId: string): boolean;
    getReconnectionInfo(playerId: string): {
        canReconnect: boolean;
        remainingTime: number;
        remainingAttempts: number;
    } | null;
    getReconnectionDelay(attemptCount: number): number;
    private recoverPlayerState;
    private generateReconnectionToken;
    private cleanup;
    private startCleanupTask;
    forceCleanup(playerId: string): boolean;
    getPendingReconnections(): Array<{
        playerId: string;
        gameId: string;
        playerName: string;
        disconnectedAt: number;
        remainingTime: number;
    }>;
    shutdown(): void;
}
