declare class MemoryCache {
    private cache;
    private maxSize;
    private maxEntries;
    private currentSize;
    private cleanupInterval;
    set<T>(key: string, data: T, ttlSeconds: number): void;
    get<T>(key: string): T | null;
    delete(key: string): void;
    clear(): void;
    private estimateSize;
    private evictOldest;
    startCleanup(intervalMs?: number): void;
    stopCleanup(): void;
    getStats(): {
        entries: number;
        sizeBytes: number;
        sizeMB: number;
        maxSizeMB: number;
        maxEntries: number;
    };
}
export declare const cache: MemoryCache;
export declare function cachedResponse<T>(key: string, fetcher: () => Promise<T>, ttlSeconds: number): Promise<T>;
export {};
