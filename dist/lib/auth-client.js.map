{"version": 3, "file": "auth-client.js", "sourceRoot": "", "sources": ["../../lib/auth-client.ts"], "names": [], "mappings": ";;;AACA,+DAAyD;AACzD,6CAAkC;AAclC,MAAa,UAAU;IAIrB,MAAM,CAAC,eAAe;QACpB,OAAO,CAAC,CAAC,IAAA,uCAAiB,GAAE,CAAA;IAC9B,CAAC;IAKD,MAAM,CAAC,cAAc;QACnB,OAAO,IAAA,uCAAiB,GAAE,CAAA;IAC5B,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAG,CAAC,IAAI,CAI1B,oBAAoB,CAAC,CAAA;YAExB,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAA;YAClD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAA;QAC3B,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAa,EAAE,QAAgB;QAKhD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAG,CAAC,IAAI,CAI1B,iBAAiB,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAA;YAE1C,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,cAAc;aACzC,CAAA;QACH,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAIrB;QAKC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAG,CAAC,IAAI,CAI1B,oBAAoB,EAAE,IAAI,CAAC,CAAA;YAE9B,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,qBAAqB;aAChD,CAAA;QACH,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,MAAM;QACjB,IAAI,CAAC;YACH,MAAM,gBAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAA;QACvC,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAG,CAAC,GAAG,CAGzB,mBAAmB,CAAC,CAAA;YAEvB,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAA;YAC7C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAA;QAC3B,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAM1B;QACC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAG,CAAC,GAAG,CAGzB,mBAAmB,EAAE,OAAO,CAAC,CAAA;YAEhC,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;YAC9C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAA;QAC3B,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,OAAO,CAAC,IAAiC;QAC9C,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;QAClC,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAA;QAGvB,IAAI,IAAI,KAAK,MAAM;YAAE,OAAO,IAAI,CAAA;QAChC,IAAI,IAAI,KAAK,IAAI;YAAE,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,CAAA;QACzE,IAAI,IAAI,KAAK,WAAW;YAAE,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW,CAAA;QAE1D,OAAO,KAAK,CAAA;IACd,CAAC;IAKD,MAAM,CAAC,OAAO;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;IAClC,CAAC;IAKD,MAAM,CAAC,WAAW;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3B,CAAC;CACF;AApKD,gCAoKC"}