"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePlaylistSchema = exports.createPlaylistSchema = exports.voteSongSchema = exports.addToQueueSchema = exports.quizQuestionSchema = exports.mpdControlSchema = exports.fileUploadSchema = exports.registerSchema = exports.loginSchema = void 0;
exports.validateRequest = validateRequest;
const zod_1 = require("zod");
exports.loginSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email address'),
    password: zod_1.z.string().min(6, 'Password must be at least 6 characters')
});
exports.registerSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email address'),
    password: zod_1.z.string().min(6, 'Password must be at least 6 characters'),
    displayName: zod_1.z.string().min(2, 'Display name must be at least 2 characters').max(50)
});
exports.fileUploadSchema = zod_1.z.object({
    files: zod_1.z.array(zod_1.z.instanceof(File)).min(1).max(10),
    metadata: zod_1.z.object({
        title: zod_1.z.string().optional(),
        artist: zod_1.z.string().optional(),
        album: zod_1.z.string().optional()
    }).optional()
});
exports.mpdControlSchema = zod_1.z.object({
    action: zod_1.z.enum(['play', 'pause', 'stop', 'next', 'previous']),
    volume: zod_1.z.number().min(0).max(100).optional(),
    seek: zod_1.z.number().min(0).optional()
});
exports.quizQuestionSchema = zod_1.z.object({
    gameMode: zod_1.z.enum([
        'classic', 'quick-fire', 'audio-tricks', 'album-art',
        'audio-fingerprint', 'general-knowledge'
    ]),
    settings: zod_1.z.object({
        totalQuestions: zod_1.z.number().min(1).max(50),
        difficultyLevel: zod_1.z.number().min(1).max(5),
        timeLimit: zod_1.z.number().min(10).max(300),
        category: zod_1.z.string().optional(),
        categories: zod_1.z.array(zod_1.z.string()).optional()
    })
});
exports.addToQueueSchema = zod_1.z.object({
    filePath: zod_1.z.string(),
    action: zod_1.z.enum(['add', 'play-next', 'play-now']),
    addedBy: zod_1.z.string(),
    userId: zod_1.z.string(),
    userRole: zod_1.z.string()
});
exports.voteSongSchema = zod_1.z.object({
    songId: zod_1.z.string(),
    userId: zod_1.z.string(),
    voteType: zod_1.z.enum(['up', 'down'])
});
exports.createPlaylistSchema = zod_1.z.object({
    name: zod_1.z.string().min(1).max(100),
    description: zod_1.z.string().max(500).optional(),
    isPublic: zod_1.z.boolean().default(false),
    tags: zod_1.z.array(zod_1.z.string()).max(10).optional()
});
exports.updatePlaylistSchema = zod_1.z.object({
    name: zod_1.z.string().min(1).max(100).optional(),
    description: zod_1.z.string().max(500).optional(),
    isPublic: zod_1.z.boolean().optional(),
    tags: zod_1.z.array(zod_1.z.string()).max(10).optional()
});
async function validateRequest(schema, data) {
    try {
        const validated = await schema.parseAsync(data);
        return { success: true, data: validated };
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return { success: false, errors: error };
        }
        throw error;
    }
}
//# sourceMappingURL=api-schemas.js.map