import { z } from 'zod';
export declare const loginSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    password: string;
    email: string;
}, {
    password: string;
    email: string;
}>;
export declare const registerSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    displayName: z.ZodString;
}, "strip", z.ZodTypeAny, {
    password: string;
    email: string;
    displayName: string;
}, {
    password: string;
    email: string;
    displayName: string;
}>;
export declare const fileUploadSchema: z.ZodObject<{
    files: z.ZodArray<z.ZodType<File, z.ZodTypeDef, File>, "many">;
    metadata: z.ZodOptional<z.ZodObject<{
        title: z.ZodOptional<z.ZodString>;
        artist: z.ZodOptional<z.ZodString>;
        album: z.ZodOptional<z.ZodString>;
    }, "strip", z.Zod<PERSON>ype<PERSON>ny, {
        title?: string | undefined;
        artist?: string | undefined;
        album?: string | undefined;
    }, {
        title?: string | undefined;
        artist?: string | undefined;
        album?: string | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    files: File[];
    metadata?: {
        title?: string | undefined;
        artist?: string | undefined;
        album?: string | undefined;
    } | undefined;
}, {
    files: File[];
    metadata?: {
        title?: string | undefined;
        artist?: string | undefined;
        album?: string | undefined;
    } | undefined;
}>;
export declare const mpdControlSchema: z.ZodObject<{
    action: z.ZodEnum<["play", "pause", "stop", "next", "previous"]>;
    volume: z.ZodOptional<z.ZodNumber>;
    seek: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    action: "play" | "pause" | "stop" | "next" | "previous";
    volume?: number | undefined;
    seek?: number | undefined;
}, {
    action: "play" | "pause" | "stop" | "next" | "previous";
    volume?: number | undefined;
    seek?: number | undefined;
}>;
export declare const quizQuestionSchema: z.ZodObject<{
    gameMode: z.ZodEnum<["classic", "quick-fire", "audio-tricks", "album-art", "audio-fingerprint", "general-knowledge"]>;
    settings: z.ZodObject<{
        totalQuestions: z.ZodNumber;
        difficultyLevel: z.ZodNumber;
        timeLimit: z.ZodNumber;
        category: z.ZodOptional<z.ZodString>;
        categories: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        timeLimit: number;
        totalQuestions: number;
        difficultyLevel: number;
        category?: string | undefined;
        categories?: string[] | undefined;
    }, {
        timeLimit: number;
        totalQuestions: number;
        difficultyLevel: number;
        category?: string | undefined;
        categories?: string[] | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    settings: {
        timeLimit: number;
        totalQuestions: number;
        difficultyLevel: number;
        category?: string | undefined;
        categories?: string[] | undefined;
    };
    gameMode: "classic" | "album-art" | "audio-fingerprint" | "quick-fire" | "audio-tricks" | "general-knowledge";
}, {
    settings: {
        timeLimit: number;
        totalQuestions: number;
        difficultyLevel: number;
        category?: string | undefined;
        categories?: string[] | undefined;
    };
    gameMode: "classic" | "album-art" | "audio-fingerprint" | "quick-fire" | "audio-tricks" | "general-knowledge";
}>;
export declare const addToQueueSchema: z.ZodObject<{
    filePath: z.ZodString;
    action: z.ZodEnum<["add", "play-next", "play-now"]>;
    addedBy: z.ZodString;
    userId: z.ZodString;
    userRole: z.ZodString;
}, "strip", z.ZodTypeAny, {
    action: "add" | "play-next" | "play-now";
    userId: string;
    filePath: string;
    addedBy: string;
    userRole: string;
}, {
    action: "add" | "play-next" | "play-now";
    userId: string;
    filePath: string;
    addedBy: string;
    userRole: string;
}>;
export declare const voteSongSchema: z.ZodObject<{
    songId: z.ZodString;
    userId: z.ZodString;
    voteType: z.ZodEnum<["up", "down"]>;
}, "strip", z.ZodTypeAny, {
    userId: string;
    songId: string;
    voteType: "down" | "up";
}, {
    userId: string;
    songId: string;
    voteType: "down" | "up";
}>;
export declare const createPlaylistSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    isPublic: z.ZodDefault<z.ZodBoolean>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    name: string;
    isPublic: boolean;
    description?: string | undefined;
    tags?: string[] | undefined;
}, {
    name: string;
    description?: string | undefined;
    isPublic?: boolean | undefined;
    tags?: string[] | undefined;
}>;
export declare const updatePlaylistSchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodString>;
    isPublic: z.ZodOptional<z.ZodBoolean>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    description?: string | undefined;
    name?: string | undefined;
    isPublic?: boolean | undefined;
    tags?: string[] | undefined;
}, {
    description?: string | undefined;
    name?: string | undefined;
    isPublic?: boolean | undefined;
    tags?: string[] | undefined;
}>;
export declare function validateRequest<T>(schema: z.ZodSchema<T>, data: unknown): Promise<{
    success: true;
    data: T;
} | {
    success: false;
    errors: z.ZodError;
}>;
