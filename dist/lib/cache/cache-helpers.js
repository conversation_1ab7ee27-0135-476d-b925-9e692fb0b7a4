"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilterCache = exports.CacheUtils = exports.RateLimitCache = exports.AnalyticsCache = exports.SessionCache = exports.MediaCache = exports.SearchCache = exports.SuggestionsCache = exports.UserCache = exports.LibraryCache = void 0;
const redis_cache_1 = require("./redis-cache");
class LibraryCache {
    static async getLibrary() {
        return await redis_cache_1.cache.getLibrary();
    }
    static async setLibrary(library, ttl = 1800) {
        await redis_cache_1.cache.cacheLibrary(library, ttl);
    }
    static async getFilteredLibrary(filters) {
        const cacheKey = `library:filtered:${JSON.stringify(filters)}`;
        return await redis_cache_1.cache.get(cacheKey);
    }
    static async setFilteredLibrary(filters, library, ttl = 600) {
        const cacheKey = `library:filtered:${JSON.stringify(filters)}`;
        await redis_cache_1.cache.set(cacheKey, library, ttl);
    }
    static async getGenres() {
        return await redis_cache_1.cache.get('library:genres');
    }
    static async setGenres(genres, ttl = 3600) {
        await redis_cache_1.cache.set('library:genres', genres, ttl);
    }
    static async getArtists() {
        return await redis_cache_1.cache.get('library:artists');
    }
    static async setArtists(artists, ttl = 3600) {
        await redis_cache_1.cache.set('library:artists', artists, ttl);
    }
    static async invalidate() {
        await redis_cache_1.cache.clear('library:*');
    }
}
exports.LibraryCache = LibraryCache;
class UserCache {
    static async getFavorites(userId) {
        return await redis_cache_1.cache.getUserFavorites(userId);
    }
    static async setFavorites(userId, favorites, ttl = 3600) {
        await redis_cache_1.cache.cacheUserFavorites(userId, favorites, ttl);
    }
    static async getProfile(userId) {
        return await redis_cache_1.cache.get(`user:${userId}:profile`);
    }
    static async setProfile(userId, profile, ttl = 7200) {
        await redis_cache_1.cache.set(`user:${userId}:profile`, profile, ttl);
    }
    static async getPreferences(userId) {
        return await redis_cache_1.cache.get(`user:${userId}:preferences`);
    }
    static async setPreferences(userId, preferences, ttl = 86400) {
        await redis_cache_1.cache.set(`user:${userId}:preferences`, preferences, ttl);
    }
    static async getListeningHistory(userId) {
        return await redis_cache_1.cache.get(`user:${userId}:history`);
    }
    static async setListeningHistory(userId, history, ttl = 7200) {
        await redis_cache_1.cache.set(`user:${userId}:history`, history, ttl);
    }
    static async invalidateUser(userId) {
        await redis_cache_1.cache.clear(`user:${userId}:*`);
    }
}
exports.UserCache = UserCache;
class SuggestionsCache {
    static async get(userId, type) {
        return await redis_cache_1.cache.getSuggestions(userId, type);
    }
    static async set(userId, type, suggestions, ttl = 900) {
        await redis_cache_1.cache.cacheSuggestions(userId, type, suggestions, ttl);
    }
    static async getPopular(timeframe = 'day') {
        return await redis_cache_1.cache.get(`suggestions:popular:${timeframe}`);
    }
    static async setPopular(timeframe, songs, ttl = 3600) {
        await redis_cache_1.cache.set(`suggestions:popular:${timeframe}`, songs, ttl);
    }
    static async getTrending() {
        return await redis_cache_1.cache.get('suggestions:trending');
    }
    static async setTrending(songs, ttl = 1800) {
        await redis_cache_1.cache.set('suggestions:trending', songs, ttl);
    }
    static async getRecommendations(userId, context) {
        const contextKey = Buffer.from(JSON.stringify(context)).toString('base64');
        return await redis_cache_1.cache.get(`recommendations:${userId}:${contextKey}`);
    }
    static async setRecommendations(userId, context, recommendations, ttl = 1800) {
        const contextKey = Buffer.from(JSON.stringify(context)).toString('base64');
        await redis_cache_1.cache.set(`recommendations:${userId}:${contextKey}`, recommendations, ttl);
    }
    static async invalidateUser(userId) {
        await redis_cache_1.cache.clear(`suggestions:${userId}:*`);
        await redis_cache_1.cache.clear(`recommendations:${userId}:*`);
    }
}
exports.SuggestionsCache = SuggestionsCache;
class SearchCache {
    static async getResults(query) {
        return await redis_cache_1.cache.getSearchResults(query);
    }
    static async setResults(query, results, ttl = 600) {
        await redis_cache_1.cache.cacheSearchResults(query, results, ttl);
    }
    static async getYouTubeResults(query) {
        const searchKey = `youtube:${Buffer.from(query).toString('base64')}`;
        return await redis_cache_1.cache.get(searchKey);
    }
    static async setYouTubeResults(query, results, ttl = 1800) {
        const searchKey = `youtube:${Buffer.from(query).toString('base64')}`;
        await redis_cache_1.cache.set(searchKey, results, ttl);
    }
    static async getAutoComplete(query) {
        const autoCompleteKey = `autocomplete:${Buffer.from(query).toString('base64')}`;
        return await redis_cache_1.cache.get(autoCompleteKey);
    }
    static async setAutoComplete(query, suggestions, ttl = 3600) {
        const autoCompleteKey = `autocomplete:${Buffer.from(query).toString('base64')}`;
        await redis_cache_1.cache.set(autoCompleteKey, suggestions, ttl);
    }
    static async clear() {
        await redis_cache_1.cache.clear('search:*');
        await redis_cache_1.cache.clear('youtube:*');
        await redis_cache_1.cache.clear('autocomplete:*');
    }
}
exports.SearchCache = SearchCache;
class MediaCache {
    static async getAlbumArt(artist, album) {
        return await redis_cache_1.cache.getAlbumArt(artist, album);
    }
    static async setAlbumArt(artist, album, urls, ttl = 86400) {
        await redis_cache_1.cache.cacheAlbumArt(artist, album, urls, ttl);
    }
    static async getMetadata(trackId) {
        return await redis_cache_1.cache.get(`metadata:${trackId}`);
    }
    static async setMetadata(trackId, metadata, ttl = 86400) {
        await redis_cache_1.cache.set(`metadata:${trackId}`, metadata, ttl);
    }
    static async getThumbnail(videoId) {
        return await redis_cache_1.cache.get(`thumbnail:${videoId}`);
    }
    static async setThumbnail(videoId, url, ttl = 86400) {
        await redis_cache_1.cache.set(`thumbnail:${videoId}`, url, ttl);
    }
    static async clear() {
        await redis_cache_1.cache.clear('albumart:*');
        await redis_cache_1.cache.clear('metadata:*');
        await redis_cache_1.cache.clear('thumbnail:*');
    }
}
exports.MediaCache = MediaCache;
class SessionCache {
    static async getQueue(sessionId) {
        return await redis_cache_1.cache.get(`queue:${sessionId}`);
    }
    static async setQueue(sessionId, queue, ttl = 3600) {
        await redis_cache_1.cache.set(`queue:${sessionId}`, queue, ttl);
    }
    static async getCurrentTrack(sessionId) {
        return await redis_cache_1.cache.get(`current:${sessionId}`);
    }
    static async setCurrentTrack(sessionId, track, ttl = 3600) {
        await redis_cache_1.cache.set(`current:${sessionId}`, track, ttl);
    }
    static async getSessionData(sessionId) {
        return await redis_cache_1.cache.getSession(sessionId);
    }
    static async setSessionData(sessionId, data, ttl = 3600) {
        await redis_cache_1.cache.cacheSession(sessionId, data, ttl);
    }
    static async getActiveUsers() {
        return await redis_cache_1.cache.get('session:active_users');
    }
    static async setActiveUsers(users, ttl = 300) {
        await redis_cache_1.cache.set('session:active_users', users, ttl);
    }
    static async invalidateSession(sessionId) {
        await redis_cache_1.cache.clear(`queue:${sessionId}`);
        await redis_cache_1.cache.clear(`current:${sessionId}`);
        await redis_cache_1.cache.del(`session:${sessionId}`);
    }
}
exports.SessionCache = SessionCache;
class AnalyticsCache {
    static async getPlayStats(period = 'day') {
        return await redis_cache_1.cache.get(`analytics:plays:${period}`);
    }
    static async setPlayStats(period, stats, ttl = 3600) {
        await redis_cache_1.cache.set(`analytics:plays:${period}`, stats, ttl);
    }
    static async getTopTracks(period = 'week', limit = 50) {
        return await redis_cache_1.cache.get(`analytics:top_tracks:${period}:${limit}`);
    }
    static async setTopTracks(period, limit, tracks, ttl = 7200) {
        await redis_cache_1.cache.set(`analytics:top_tracks:${period}:${limit}`, tracks, ttl);
    }
    static async getTopArtists(period = 'week', limit = 20) {
        return await redis_cache_1.cache.get(`analytics:top_artists:${period}:${limit}`);
    }
    static async setTopArtists(period, limit, artists, ttl = 7200) {
        await redis_cache_1.cache.set(`analytics:top_artists:${period}:${limit}`, artists, ttl);
    }
    static async getUserActivity(userId) {
        return await redis_cache_1.cache.get(`analytics:user:${userId}`);
    }
    static async setUserActivity(userId, activity, ttl = 3600) {
        await redis_cache_1.cache.set(`analytics:user:${userId}`, activity, ttl);
    }
    static async clear() {
        await redis_cache_1.cache.clear('analytics:*');
    }
}
exports.AnalyticsCache = AnalyticsCache;
class RateLimitCache {
    static async checkRateLimit(key, limit, windowSeconds) {
        const count = await redis_cache_1.cache.incr(`ratelimit:${key}`, windowSeconds);
        const resetTime = Date.now() + (windowSeconds * 1000);
        return {
            allowed: count <= limit,
            count,
            resetTime
        };
    }
    static async getRemainingRequests(key, limit) {
        const current = await redis_cache_1.cache.get(`ratelimit:${key}`) || 0;
        return Math.max(0, limit - current);
    }
}
exports.RateLimitCache = RateLimitCache;
class CacheUtils {
    static async warmUp() {
        console.log('Warming up cache...');
        try {
            console.log('Cache warm-up completed');
        }
        catch (error) {
            console.error('Cache warm-up failed:', error);
        }
    }
    static async getStats() {
        return await redis_cache_1.cache.getStats();
    }
    static async clearAll() {
        await redis_cache_1.cache.clear();
    }
    static async healthCheck() {
        try {
            const stats = await redis_cache_1.cache.getStats();
            return {
                status: redis_cache_1.cache.isAvailable() ? 'healthy' : 'degraded',
                mode: redis_cache_1.cache.getMode(),
                stats
            };
        }
        catch (error) {
            return {
                status: 'error',
                mode: 'unknown',
                stats: null
            };
        }
    }
}
exports.CacheUtils = CacheUtils;
class FilterCache {
    static async getFilters() {
        return await redis_cache_1.cache.get('library:filters');
    }
    static async setFilters(filters, ttl = 3600) {
        await redis_cache_1.cache.set('library:filters', filters, ttl);
    }
    static async invalidate() {
        await redis_cache_1.cache.delete('library:filters');
    }
}
exports.FilterCache = FilterCache;
//# sourceMappingURL=cache-helpers.js.map