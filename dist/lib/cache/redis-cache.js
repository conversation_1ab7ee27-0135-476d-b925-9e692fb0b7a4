"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.cache = exports.RedisCache = void 0;
const events_1 = require("events");
let Redis = null;
let redisAvailable = false;
async function loadRedis() {
    if (Redis)
        return Redis;
    try {
        const ioredis = await Promise.resolve().then(() => __importStar(require('ioredis')));
        Redis = ioredis.Redis || ioredis.default?.Redis || ioredis.default;
        redisAvailable = true;
        console.log('Redis cache module loaded successfully');
        return Redis;
    }
    catch (error) {
        console.warn('Redis not available for caching - using memory cache:', error.message);
        redisAvailable = false;
        return null;
    }
}
class RedisCache extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.isConnected = false;
        this.memoryCache = new Map();
        this.hitCount = 0;
        this.missCount = 0;
        this.cleanupTimer = null;
        this.config = {
            host: process.env.REDIS_CACHE_HOST || process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_CACHE_PORT || process.env.REDIS_PORT || '6379'),
            password: process.env.REDIS_CACHE_PASSWORD || process.env.REDIS_PASSWORD,
            db: parseInt(process.env.REDIS_CACHE_DB || '1'),
            keyPrefix: 'cache:music-quiz:',
            defaultTTL: 3600,
            maxMemoryItems: 1000,
            ...config
        };
        if (!this.isBuildTime()) {
            this.initializeCache();
            this.startCleanupTimer();
        }
    }
    isBuildTime() {
        return process.env.NODE_ENV === 'development' && process.argv.includes('build') ||
            process.env.NEXT_PHASE === 'phase-production-build' ||
            process.env.CI === 'true' ||
            process.env.BUILD_MODE === 'true';
    }
    shouldDisableRedis() {
        return this.isBuildTime() ||
            process.env.DISABLE_REDIS === 'true' ||
            (process.env.NODE_ENV === 'development' && !process.env.REDIS_URL && !process.env.REDIS_HOST);
    }
    async ensureInitialized() {
        if (this.shouldDisableRedis()) {
            return;
        }
        if (!this.isConnected && !this.redis && redisAvailable !== false) {
            await this.initializeCache();
            this.startCleanupTimer();
        }
    }
    async initializeCache() {
        try {
            const RedisClass = await loadRedis();
            if (!RedisClass || !redisAvailable) {
                console.warn('Redis not available - using memory cache');
                this.isConnected = false;
                this.emit('memory_mode');
                return;
            }
            this.redis = new RedisClass({
                host: this.config.host,
                port: this.config.port,
                password: this.config.password,
                db: this.config.db,
                keyPrefix: this.config.keyPrefix,
                retryDelayOnFailover: 100,
                maxRetriesPerRequest: 3,
                lazyConnect: true
            });
            this.redis.on('error', (error) => {
                console.warn('Redis cache connection error:', error.message);
                this.isConnected = false;
                this.emit('error', error);
                process.removeAllListeners('uncaughtException');
            });
            this.redis.on('connect', () => {
                console.log('Redis cache connected');
                this.isConnected = true;
                this.emit('connected');
            });
            this.redis.on('close', () => {
                console.log('Redis cache connection closed');
                this.isConnected = false;
            });
            await this.redis.connect();
            this.isConnected = true;
            console.log('Redis cache initialized successfully');
        }
        catch (error) {
            console.warn('Failed to initialize Redis cache, using memory cache:', error.message);
            this.isConnected = false;
            this.emit('memory_mode');
        }
    }
    async get(key) {
        await this.ensureInitialized();
        try {
            if (this.isConnected && this.redis) {
                const value = await this.redis.get(key);
                if (value) {
                    this.hitCount++;
                    return JSON.parse(value);
                }
                this.missCount++;
                return null;
            }
            else {
                const item = this.memoryCache.get(key);
                if (item) {
                    if (Date.now() - item.createdAt > item.ttl * 1000) {
                        this.memoryCache.delete(key);
                        this.missCount++;
                        return null;
                    }
                    item.lastAccessed = Date.now();
                    item.hitCount++;
                    this.hitCount++;
                    return item.value;
                }
                this.missCount++;
                return null;
            }
        }
        catch (error) {
            console.error('Cache get error:', error);
            this.missCount++;
            return null;
        }
    }
    async set(key, value, ttl) {
        await this.ensureInitialized();
        const cacheTTL = ttl || this.config.defaultTTL;
        try {
            if (this.isConnected && this.redis) {
                await this.redis.setex(key, cacheTTL, JSON.stringify(value));
            }
            else {
                const item = {
                    key,
                    value,
                    ttl: cacheTTL,
                    createdAt: Date.now(),
                    lastAccessed: Date.now(),
                    hitCount: 0
                };
                this.memoryCache.set(key, item);
                if (this.memoryCache.size > this.config.maxMemoryItems) {
                    this.evictOldestItems();
                }
            }
        }
        catch (error) {
            console.error('Cache set error:', error);
        }
    }
    async del(key) {
        try {
            if (this.isConnected && this.redis) {
                await this.redis.del(key);
            }
            else {
                this.memoryCache.delete(key);
            }
        }
        catch (error) {
            console.error('Cache delete error:', error);
        }
    }
    async mget(keys) {
        try {
            if (this.isConnected && this.redis) {
                const values = await this.redis.mget(keys);
                return values.map(value => {
                    if (value) {
                        this.hitCount++;
                        return JSON.parse(value);
                    }
                    this.missCount++;
                    return null;
                });
            }
            else {
                return keys.map(key => {
                    const item = this.memoryCache.get(key);
                    if (item && Date.now() - item.createdAt <= item.ttl * 1000) {
                        item.lastAccessed = Date.now();
                        item.hitCount++;
                        this.hitCount++;
                        return item.value;
                    }
                    this.missCount++;
                    return null;
                });
            }
        }
        catch (error) {
            console.error('Cache mget error:', error);
            return keys.map(() => null);
        }
    }
    async mset(items) {
        try {
            if (this.isConnected && this.redis) {
                const pipeline = this.redis.pipeline();
                for (const item of items) {
                    const ttl = item.ttl || this.config.defaultTTL;
                    pipeline.setex(item.key, ttl, JSON.stringify(item.value));
                }
                await pipeline.exec();
            }
            else {
                for (const item of items) {
                    await this.set(item.key, item.value, item.ttl);
                }
            }
        }
        catch (error) {
            console.error('Cache mset error:', error);
        }
    }
    async exists(key) {
        try {
            if (this.isConnected && this.redis) {
                return (await this.redis.exists(key)) === 1;
            }
            else {
                const item = this.memoryCache.get(key);
                if (item && Date.now() - item.createdAt <= item.ttl * 1000) {
                    return true;
                }
                if (item) {
                    this.memoryCache.delete(key);
                }
                return false;
            }
        }
        catch (error) {
            console.error('Cache exists error:', error);
            return false;
        }
    }
    async getOrSet(key, getter, ttl) {
        try {
            const cached = await this.get(key);
            if (cached !== null) {
                return cached;
            }
            const value = await getter();
            await this.set(key, value, ttl);
            return value;
        }
        catch (error) {
            console.error('Cache getOrSet error:', error);
            return await getter();
        }
    }
    async cacheLibrary(library, ttl = 1800) {
        await this.set('library:tracks', library, ttl);
    }
    async getLibrary() {
        return await this.get('library:tracks');
    }
    async cacheUserFavorites(userId, favorites, ttl = 3600) {
        await this.set(`user:${userId}:favorites`, favorites, ttl);
    }
    async getUserFavorites(userId) {
        return await this.get(`user:${userId}:favorites`);
    }
    async cacheSuggestions(userId, type, suggestions, ttl = 900) {
        await this.set(`suggestions:${userId}:${type}`, suggestions, ttl);
    }
    async getSuggestions(userId, type) {
        return await this.get(`suggestions:${userId}:${type}`);
    }
    async cacheSearchResults(query, results, ttl = 600) {
        const searchKey = `search:${Buffer.from(query).toString('base64')}`;
        await this.set(searchKey, results, ttl);
    }
    async getSearchResults(query) {
        const searchKey = `search:${Buffer.from(query).toString('base64')}`;
        return await this.get(searchKey);
    }
    async cacheAlbumArt(artist, album, urls, ttl = 86400) {
        const artKey = `albumart:${artist}:${album}`.replace(/[^a-zA-Z0-9:]/g, '_');
        await this.set(artKey, urls, ttl);
    }
    async getAlbumArt(artist, album) {
        const artKey = `albumart:${artist}:${album}`.replace(/[^a-zA-Z0-9:]/g, '_');
        return await this.get(artKey);
    }
    async cacheSession(sessionId, data, ttl = 3600) {
        await this.set(`session:${sessionId}`, data, ttl);
    }
    async getSession(sessionId) {
        return await this.get(`session:${sessionId}`);
    }
    async incr(key, ttl) {
        try {
            if (this.isConnected && this.redis) {
                const value = await this.redis.incr(key);
                if (ttl && value === 1) {
                    await this.redis.expire(key, ttl);
                }
                return value;
            }
            else {
                const item = this.memoryCache.get(key);
                const newValue = (item?.value || 0) + 1;
                await this.set(key, newValue, ttl || this.config.defaultTTL);
                return newValue;
            }
        }
        catch (error) {
            console.error('Cache incr error:', error);
            return 0;
        }
    }
    async clear(pattern) {
        try {
            if (this.isConnected && this.redis) {
                if (pattern) {
                    const keys = await this.redis.keys(pattern);
                    if (keys.length > 0) {
                        return await this.redis.del(...keys);
                    }
                    return 0;
                }
                else {
                    return await this.redis.flushdb();
                }
            }
            else {
                if (pattern) {
                    let deleted = 0;
                    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                    for (const [key] of this.memoryCache) {
                        if (regex.test(key)) {
                            this.memoryCache.delete(key);
                            deleted++;
                        }
                    }
                    return deleted;
                }
                else {
                    const size = this.memoryCache.size;
                    this.memoryCache.clear();
                    return size;
                }
            }
        }
        catch (error) {
            console.error('Cache clear error:', error);
            return 0;
        }
    }
    async getStats() {
        try {
            if (this.isConnected && this.redis) {
                const info = await this.redis.info('memory');
                const keys = await this.redis.dbsize();
                return {
                    totalKeys: keys,
                    hitRate: this.hitCount / (this.hitCount + this.missCount) || 0,
                    memoryUsage: this.parseMemoryUsage(info),
                    topKeys: [],
                    mode: 'redis'
                };
            }
            else {
                const topKeys = Array.from(this.memoryCache.values())
                    .sort((a, b) => b.hitCount - a.hitCount)
                    .slice(0, 10)
                    .map(item => ({
                    key: item.key,
                    hitCount: item.hitCount,
                    lastAccessed: item.lastAccessed
                }));
                return {
                    totalKeys: this.memoryCache.size,
                    hitRate: this.hitCount / (this.hitCount + this.missCount) || 0,
                    memoryUsage: this.estimateMemoryUsage(),
                    topKeys,
                    mode: 'memory'
                };
            }
        }
        catch (error) {
            console.error('Cache stats error:', error);
            return {
                totalKeys: 0,
                hitRate: 0,
                memoryUsage: 0,
                topKeys: [],
                mode: this.isConnected ? 'redis' : 'memory'
            };
        }
    }
    evictOldestItems() {
        const items = Array.from(this.memoryCache.entries())
            .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
        const evictCount = Math.floor(this.config.maxMemoryItems * 0.1);
        for (let i = 0; i < evictCount && items.length > 0; i++) {
            this.memoryCache.delete(items[i][0]);
        }
    }
    startCleanupTimer() {
        if (this.isBuildTime()) {
            return;
        }
        this.cleanupTimer = setInterval(() => {
            if (!this.isConnected) {
                this.cleanupExpiredMemoryItems();
            }
        }, 60000);
    }
    cleanupExpiredMemoryItems() {
        const now = Date.now();
        for (const [key, item] of this.memoryCache) {
            if (now - item.createdAt > item.ttl * 1000) {
                this.memoryCache.delete(key);
            }
        }
    }
    parseMemoryUsage(info) {
        const match = info.match(/used_memory:(\d+)/);
        return match ? parseInt(match[1]) : 0;
    }
    estimateMemoryUsage() {
        let size = 0;
        for (const item of this.memoryCache.values()) {
            size += JSON.stringify(item).length * 2;
        }
        return size;
    }
    isAvailable() {
        return this.isConnected || this.memoryCache.size >= 0;
    }
    getMode() {
        return this.isConnected ? 'redis' : 'memory';
    }
    async shutdown() {
        if (this.isBuildTime()) {
            return;
        }
        console.log('Shutting down cache...');
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        if (this.redis && this.isConnected) {
            try {
                await this.redis.quit();
            }
            catch (error) {
                console.warn('Redis quit error (expected if connection already closed):', error.message);
            }
        }
        this.memoryCache.clear();
        this.removeAllListeners();
        console.log('Cache shutdown complete');
    }
}
exports.RedisCache = RedisCache;
exports.cache = new RedisCache();
if (typeof window === 'undefined' && process.env.NEXT_PHASE !== 'phase-production-build') {
    process.on('SIGTERM', () => exports.cache.shutdown());
    process.on('SIGINT', () => exports.cache.shutdown());
}
//# sourceMappingURL=redis-cache.js.map