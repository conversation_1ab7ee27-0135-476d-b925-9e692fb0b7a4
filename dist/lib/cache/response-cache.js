"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheInvalidation = exports.CacheTTL = exports.CacheKeys = exports.sessionCache = exports.favoritesCache = exports.recommendationsCache = exports.autoQueueCache = void 0;
class ResponseCache {
    constructor() {
        this.cache = new Map();
        this.maxSize = 1000;
    }
    set(key, data, ttlSeconds = 300) {
        if (this.cache.size >= this.maxSize) {
            this.clearExpired();
        }
        if (this.cache.size >= this.maxSize) {
            const oldestKey = this.cache.keys().next().value;
            if (oldestKey) {
                this.cache.delete(oldestKey);
            }
        }
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl: ttlSeconds * 1000
        });
    }
    get(key) {
        const entry = this.cache.get(key);
        if (!entry)
            return null;
        const now = Date.now();
        if (now - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    has(key) {
        return this.get(key) !== null;
    }
    delete(key) {
        return this.cache.delete(key);
    }
    clearExpired() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
                this.cache.delete(key);
            }
        }
    }
    clear() {
        this.cache.clear();
    }
    getStats() {
        const memoryUsage = JSON.stringify([...this.cache.entries()]).length;
        return {
            size: this.cache.size,
            hitRate: 0,
            memoryUsage
        };
    }
    generateKey(prefix, params) {
        const sortedParams = Object.keys(params)
            .sort()
            .map(key => `${key}=${JSON.stringify(params[key])}`)
            .join('&');
        return `${prefix}:${sortedParams}`;
    }
}
exports.autoQueueCache = new ResponseCache();
exports.recommendationsCache = new ResponseCache();
exports.favoritesCache = new ResponseCache();
exports.sessionCache = new ResponseCache();
exports.CacheKeys = {
    autoQueueRecommendations: (sessionType, limit, excludePaths) => exports.autoQueueCache.generateKey('auto-queue', { sessionType, limit, excludePaths }),
    favoritesAnalysis: (sessionType) => exports.favoritesCache.generateKey('favorites-analysis', { sessionType }),
    intelligentRecommendations: (context, maxResults) => exports.recommendationsCache.generateKey('intelligent-recs', {
        sessionType: context.sessionType,
        userIds: context.connectedUserIds.sort(),
        timeOfDay: context.timeOfDay,
        dayOfWeek: context.dayOfWeek,
        maxResults
    }),
    activeUsers: (sessionType) => exports.sessionCache.generateKey('active-users', { sessionType }),
    userFavorites: (userIds) => exports.favoritesCache.generateKey('user-favorites', { userIds: userIds.sort() })
};
exports.CacheTTL = {
    autoQueueRecommendations: 30,
    favoritesAnalysis: 60,
    intelligentRecommendations: 120,
    activeUsers: 30,
    userFavorites: 300,
    popularSongs: 600
};
exports.CacheInvalidation = {
    invalidateUserCaches(userId) {
        exports.favoritesCache.clear();
        exports.autoQueueCache.clear();
        exports.recommendationsCache.clear();
    },
    invalidateSessionCaches(sessionType) {
        exports.sessionCache.clear();
        exports.autoQueueCache.clear();
    },
    invalidateAll() {
        exports.autoQueueCache.clear();
        exports.recommendationsCache.clear();
        exports.favoritesCache.clear();
        exports.sessionCache.clear();
    }
};
setInterval(() => {
    exports.autoQueueCache.clearExpired();
    exports.recommendationsCache.clearExpired();
    exports.favoritesCache.clearExpired();
    exports.sessionCache.clearExpired();
}, 5 * 60 * 1000);
const cacheExports = { autoQueueCache: exports.autoQueueCache, recommendationsCache: exports.recommendationsCache, favoritesCache: exports.favoritesCache, sessionCache: exports.sessionCache };
exports.default = cacheExports;
//# sourceMappingURL=response-cache.js.map