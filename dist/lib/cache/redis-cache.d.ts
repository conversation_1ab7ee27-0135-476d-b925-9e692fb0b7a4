import { EventEmitter } from 'events';
export interface CacheConfig {
    host: string;
    port: number;
    password?: string;
    db?: number;
    keyPrefix: string;
    defaultTTL: number;
    maxMemoryItems: number;
}
export interface CacheItem<T = any> {
    key: string;
    value: T;
    ttl: number;
    createdAt: number;
    lastAccessed: number;
    hitCount: number;
}
export interface CacheStats {
    totalKeys: number;
    hitRate: number;
    memoryUsage: number;
    topKeys: Array<{
        key: string;
        hitCount: number;
        lastAccessed: number;
    }>;
    mode: 'redis' | 'memory';
}
export declare class RedisCache extends EventEmitter {
    private redis;
    private config;
    private isConnected;
    private memoryCache;
    private hitCount;
    private missCount;
    private cleanupTimer;
    constructor(config?: Partial<CacheConfig>);
    private isBuildTime;
    private shouldDisableRedis;
    private ensureInitialized;
    private initializeCache;
    get<T = any>(key: string): Promise<T | null>;
    set<T = any>(key: string, value: T, ttl?: number): Promise<void>;
    del(key: string): Promise<void>;
    mget<T = any>(keys: string[]): Promise<(T | null)[]>;
    mset<T = any>(items: Array<{
        key: string;
        value: T;
        ttl?: number;
    }>): Promise<void>;
    exists(key: string): Promise<boolean>;
    getOrSet<T = any>(key: string, getter: () => Promise<T> | T, ttl?: number): Promise<T>;
    cacheLibrary(library: any[], ttl?: number): Promise<void>;
    getLibrary(): Promise<any[] | null>;
    cacheUserFavorites(userId: string, favorites: any[], ttl?: number): Promise<void>;
    getUserFavorites(userId: string): Promise<any[] | null>;
    cacheSuggestions(userId: string, type: string, suggestions: any[], ttl?: number): Promise<void>;
    getSuggestions(userId: string, type: string): Promise<any[] | null>;
    cacheSearchResults(query: string, results: any[], ttl?: number): Promise<void>;
    getSearchResults(query: string): Promise<any[] | null>;
    cacheAlbumArt(artist: string, album: string, urls: any, ttl?: number): Promise<void>;
    getAlbumArt(artist: string, album: string): Promise<any | null>;
    cacheSession(sessionId: string, data: any, ttl?: number): Promise<void>;
    getSession(sessionId: string): Promise<any | null>;
    incr(key: string, ttl?: number): Promise<number>;
    clear(pattern?: string): Promise<number>;
    getStats(): Promise<CacheStats>;
    private evictOldestItems;
    private startCleanupTimer;
    private cleanupExpiredMemoryItems;
    private parseMemoryUsage;
    private estimateMemoryUsage;
    isAvailable(): boolean;
    getMode(): 'redis' | 'memory';
    shutdown(): Promise<void>;
}
export declare const cache: RedisCache;
