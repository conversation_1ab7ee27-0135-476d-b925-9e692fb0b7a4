declare class ResponseCache {
    private cache;
    private readonly maxSize;
    set<T>(key: string, data: T, ttlSeconds?: number): void;
    get<T>(key: string): T | null;
    has(key: string): boolean;
    delete(key: string): boolean;
    clearExpired(): void;
    clear(): void;
    getStats(): {
        size: number;
        hitRate: number;
        memoryUsage: number;
    };
    generateKey(prefix: string, params: Record<string, any>): string;
}
export declare const autoQueueCache: ResponseCache;
export declare const recommendationsCache: ResponseCache;
export declare const favoritesCache: ResponseCache;
export declare const sessionCache: ResponseCache;
export declare const CacheKeys: {
    autoQueueRecommendations: (sessionType: string, limit: number, excludePaths: string[]) => string;
    favoritesAnalysis: (sessionType: string) => string;
    intelligentRecommendations: (context: any, maxResults: number) => string;
    activeUsers: (sessionType: string) => string;
    userFavorites: (userIds: string[]) => string;
};
export declare const CacheTTL: {
    autoQueueRecommendations: number;
    favoritesAnalysis: number;
    intelligentRecommendations: number;
    activeUsers: number;
    userFavorites: number;
    popularSongs: number;
};
export declare const CacheInvalidation: {
    invalidateUserCaches(userId: string): void;
    invalidateSessionCaches(sessionType: string): void;
    invalidateAll(): void;
};
declare const cacheExports: {
    autoQueueCache: ResponseCache;
    recommendationsCache: ResponseCache;
    favoritesCache: ResponseCache;
    sessionCache: ResponseCache;
};
export default cacheExports;
