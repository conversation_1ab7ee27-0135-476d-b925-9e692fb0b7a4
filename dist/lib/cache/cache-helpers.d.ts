import type { Song, QueuedSong } from '@/lib/types';
declare class LibraryCache {
    static getLibrary(): Promise<Song[] | null>;
    static setLibrary(library: Song[], ttl?: number): Promise<void>;
    static getFilteredLibrary(filters: {
        genre?: string;
        artist?: string;
        year?: number;
        searchTerm?: string;
    }): Promise<Song[] | null>;
    static setFilteredLibrary(filters: any, library: Song[], ttl?: number): Promise<void>;
    static getGenres(): Promise<string[] | null>;
    static setGenres(genres: string[], ttl?: number): Promise<void>;
    static getArtists(): Promise<string[] | null>;
    static setArtists(artists: string[], ttl?: number): Promise<void>;
    static invalidate(): Promise<void>;
}
declare class UserCache {
    static getFavorites(userId: string): Promise<Song[] | null>;
    static setFavorites(userId: string, favorites: Song[], ttl?: number): Promise<void>;
    static getProfile(userId: string): Promise<any | null>;
    static setProfile(userId: string, profile: any, ttl?: number): Promise<void>;
    static getPreferences(userId: string): Promise<any | null>;
    static setPreferences(userId: string, preferences: any, ttl?: number): Promise<void>;
    static getListeningHistory(userId: string): Promise<Song[] | null>;
    static setListeningHistory(userId: string, history: Song[], ttl?: number): Promise<void>;
    static invalidateUser(userId: string): Promise<void>;
}
declare class SuggestionsCache {
    static get(userId: string, type: string): Promise<Song[] | null>;
    static set(userId: string, type: string, suggestions: Song[], ttl?: number): Promise<void>;
    static getPopular(timeframe?: string): Promise<Song[] | null>;
    static setPopular(timeframe: string, songs: Song[], ttl?: number): Promise<void>;
    static getTrending(): Promise<Song[] | null>;
    static setTrending(songs: Song[], ttl?: number): Promise<void>;
    static getRecommendations(userId: string, context: any): Promise<Song[] | null>;
    static setRecommendations(userId: string, context: any, recommendations: Song[], ttl?: number): Promise<void>;
    static invalidateUser(userId: string): Promise<void>;
}
declare class SearchCache {
    static getResults(query: string): Promise<any[] | null>;
    static setResults(query: string, results: any[], ttl?: number): Promise<void>;
    static getYouTubeResults(query: string): Promise<any[] | null>;
    static setYouTubeResults(query: string, results: any[], ttl?: number): Promise<void>;
    static getAutoComplete(query: string): Promise<string[] | null>;
    static setAutoComplete(query: string, suggestions: string[], ttl?: number): Promise<void>;
    static clear(): Promise<void>;
}
declare class MediaCache {
    static getAlbumArt(artist: string, album: string): Promise<any | null>;
    static setAlbumArt(artist: string, album: string, urls: any, ttl?: number): Promise<void>;
    static getMetadata(trackId: string): Promise<any | null>;
    static setMetadata(trackId: string, metadata: any, ttl?: number): Promise<void>;
    static getThumbnail(videoId: string): Promise<string | null>;
    static setThumbnail(videoId: string, url: string, ttl?: number): Promise<void>;
    static clear(): Promise<void>;
}
declare class SessionCache {
    static getQueue(sessionId: string): Promise<QueuedSong[] | null>;
    static setQueue(sessionId: string, queue: QueuedSong[], ttl?: number): Promise<void>;
    static getCurrentTrack(sessionId: string): Promise<QueuedSong | null>;
    static setCurrentTrack(sessionId: string, track: QueuedSong, ttl?: number): Promise<void>;
    static getSessionData(sessionId: string): Promise<any | null>;
    static setSessionData(sessionId: string, data: any, ttl?: number): Promise<void>;
    static getActiveUsers(): Promise<any[] | null>;
    static setActiveUsers(users: any[], ttl?: number): Promise<void>;
    static invalidateSession(sessionId: string): Promise<void>;
}
declare class AnalyticsCache {
    static getPlayStats(period?: string): Promise<any | null>;
    static setPlayStats(period: string, stats: any, ttl?: number): Promise<void>;
    static getTopTracks(period?: string, limit?: number): Promise<Song[] | null>;
    static setTopTracks(period: string, limit: number, tracks: Song[], ttl?: number): Promise<void>;
    static getTopArtists(period?: string, limit?: number): Promise<any[] | null>;
    static setTopArtists(period: string, limit: number, artists: any[], ttl?: number): Promise<void>;
    static getUserActivity(userId: string): Promise<any | null>;
    static setUserActivity(userId: string, activity: any, ttl?: number): Promise<void>;
    static clear(): Promise<void>;
}
declare class RateLimitCache {
    static checkRateLimit(key: string, limit: number, windowSeconds: number): Promise<{
        allowed: boolean;
        count: number;
        resetTime: number;
    }>;
    static getRemainingRequests(key: string, limit: number): Promise<number>;
}
declare class CacheUtils {
    static warmUp(): Promise<void>;
    static getStats(): Promise<any>;
    static clearAll(): Promise<void>;
    static healthCheck(): Promise<{
        status: string;
        mode: string;
        stats: any;
    }>;
}
declare class FilterCache {
    static getFilters(): Promise<{
        genres: string[];
        years: string[];
        artists: string[];
        categories: string[];
    } | null>;
    static setFilters(filters: {
        genres: string[];
        years: string[];
        artists: string[];
        categories: string[];
    }, ttl?: number): Promise<void>;
    static invalidate(): Promise<void>;
}
export { LibraryCache, UserCache, SuggestionsCache, SearchCache, MediaCache, SessionCache, AnalyticsCache, RateLimitCache, CacheUtils, FilterCache };
