import { MPDClient } from './mpd-client';
export type OutputMode = 'host' | 'stream' | 'both';
export interface OutputConfig {
    mode: OutputMode;
    streamUrl: string;
    volume: number;
}
export declare class AudioOutputSelector {
    private mpdClient;
    private config;
    private streamAudio;
    private isInitialized;
    constructor(mpdClient: MPDClient);
    initialize(): Promise<void>;
    private checkAvailableOutputs;
    setOutputMode(mode: OutputMode): Promise<void>;
    private enableHostPlayback;
    private disableHostPlayback;
    private enableStreamPlayback;
    private disableStreamPlayback;
    startClientStream(): Promise<void>;
    stopClientStream(): void;
    setClientVolume(volume: number): void;
    setClientMuted(muted: boolean): void;
    getClientVolume(): number;
    isClientMuted(): boolean;
    getConfig(): OutputConfig;
    getStreamStatus(): {
        available: boolean;
        playing: boolean;
        connected: boolean;
    };
}
