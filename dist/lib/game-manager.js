"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameManager = void 0;
const voting_socket_handler_1 = require("./voting-socket-handler");
const multiplayer_quiz_generator_1 = require("./multiplayer-quiz-generator");
class GameManager {
    constructor(config) {
        this.currentVotingPhase = null;
        this.quizGenerator = new multiplayer_quiz_generator_1.MultiplayerQuizGenerator();
        this.votingEnabled = config.enableVoting || false;
        this.ultimoteConfig = config.ultimoteConfig;
        this.gameState = {
            gameId: config.gameId,
            status: 'waiting',
            gameMode: config.gameMode,
            players: [],
            teams: config.teamMode ? [] : undefined,
            teamMode: config.teamMode || false,
            teamSettings: config.teamSettings,
            questions: [],
            currentQuestionIndex: -1,
            currentQuestion: null,
            totalQuestions: config.totalQuestions || 10,
            timePerQuestion: config.timePerQuestion || 30,
            createdAt: Date.now(),
            hostId: config.hostId,
            settings: {
                maxPlayers: config.maxPlayers || 6,
                allowSpectators: false,
                autoNextQuestion: true
            }
        };
    }
    async createGame(host) {
        this.gameState.players = [host];
        console.log('[GameManager] Creating game with mode:', this.gameState.gameMode);
        try {
            if (this.gameState.gameMode === 'ultimote') {
                console.log('[GameManager] Generating ultimote questions...');
                const multiplayerQuestions = await this.quizGenerator.generateUltimoteQuestions(this.gameState.totalQuestions, this.ultimoteConfig, this.gameState.timePerQuestion);
                console.log('[GameManager] Generated questions:', multiplayerQuestions.length);
                this.gameState.questions = this.convertToGameQuestions(multiplayerQuestions);
                console.log('[GameManager] Converted questions:', this.gameState.questions.length);
            }
            else if (this.gameState.gameMode === 'general') {
                console.log('[GameManager] Generating general knowledge questions...');
                const multiplayerQuestions = await this.quizGenerator.generateGeneralKnowledgeQuestions(this.gameState.totalQuestions, ['general'], 5, this.gameState.timePerQuestion);
                console.log('[GameManager] Generated general questions:', multiplayerQuestions.length);
                this.gameState.questions = this.convertToGameQuestions(multiplayerQuestions);
            }
            else {
                console.log('[GameManager] Generating music quiz questions for mode:', this.gameState.gameMode);
                const multiplayerQuestions = await this.quizGenerator.generateQuiz({
                    gameMode: this.gameState.gameMode,
                    totalQuestions: this.gameState.totalQuestions,
                    timePerQuestion: this.gameState.timePerQuestion,
                    filters: undefined
                });
                console.log('[GameManager] Generated music questions:', multiplayerQuestions.length);
                this.gameState.questions = this.convertToGameQuestions(multiplayerQuestions);
            }
        }
        catch (error) {
            console.error(`[GameManager] Failed to generate questions for mode ${this.gameState.gameMode}, using mock questions:`, error);
            this.gameState.questions = this.generateMockQuestions();
        }
        this.gameState.totalQuestions = this.gameState.questions.length;
        return { ...this.gameState };
    }
    addPlayer(player) {
        if (this.gameState.status !== 'waiting') {
            throw new Error('Cannot add player to game in progress');
        }
        this.gameState.players.push(player);
        return { ...this.gameState };
    }
    removePlayer(playerId) {
        const index = this.gameState.players.findIndex(p => p.id === playerId);
        if (index !== -1) {
            this.gameState.players.splice(index, 1);
        }
        return { ...this.gameState };
    }
    startGame() {
        this.gameState.status = 'playing';
        this.gameState.startedAt = Date.now();
        this.gameState.currentQuestionIndex = 0;
        this.gameState.currentQuestion = this.gameState.questions[0];
        return { ...this.gameState };
    }
    submitAnswer(playerId, answerIndex, timeTaken) {
        const player = this.gameState.players.find(p => p.id === playerId);
        if (player) {
            player.hasAnswered = true;
            player.lastAnswer = answerIndex;
            player.lastAnswerTime = timeTaken;
        }
        return { ...this.gameState };
    }
    processQuestionResults() {
        this.gameState.status = 'question-results';
        if (this.gameState.teamMode) {
            this.processTeamScoring();
        }
        else {
        }
        return { ...this.gameState };
    }
    nextQuestion() {
        this.gameState.currentQuestionIndex++;
        this.gameState.currentQuestion = this.gameState.questions[this.gameState.currentQuestionIndex];
        this.gameState.status = 'playing';
        this.gameState.players.forEach(p => p.hasAnswered = false);
        if (this.gameState.teams) {
            this.gameState.teams.forEach(team => {
                team.hasAnswered = false;
                team.lastAnswer = undefined;
                team.lastAnswerTime = undefined;
            });
        }
        return { ...this.gameState };
    }
    endGame() {
        this.gameState.status = 'finished';
        this.gameState.finishedAt = Date.now();
        return { ...this.gameState };
    }
    getGameState() {
        return { ...this.gameState };
    }
    toggleTeamMode(enabled, settings) {
        this.gameState.teamMode = enabled;
        this.gameState.teamSettings = enabled ? settings : undefined;
        this.gameState.teams = enabled ? (this.gameState.teams || []) : undefined;
        return { ...this.gameState };
    }
    addTeam(team) {
        if (!this.gameState.teamMode) {
            throw new Error('Team mode is not enabled');
        }
        if (!this.gameState.teams) {
            this.gameState.teams = [];
        }
        this.gameState.teams.push(team);
        return { ...this.gameState };
    }
    removeTeam(teamId) {
        if (!this.gameState.teams) {
            return { ...this.gameState };
        }
        const index = this.gameState.teams.findIndex(t => t.id === teamId);
        if (index !== -1) {
            this.gameState.teams.splice(index, 1);
        }
        return { ...this.gameState };
    }
    addPlayerToTeam(playerId, teamId) {
        if (!this.gameState.teams) {
            throw new Error('No teams available');
        }
        const team = this.gameState.teams.find(t => t.id === teamId);
        const player = this.gameState.players.find(p => p.id === playerId);
        if (!team || !player) {
            throw new Error('Team or player not found');
        }
        if (this.gameState.teamSettings?.maxTeamSize &&
            team.players.length >= this.gameState.teamSettings.maxTeamSize) {
            throw new Error('Team is full');
        }
        this.removePlayerFromAllTeams(playerId);
        team.players.push(player);
        return { ...this.gameState };
    }
    removePlayerFromTeam(playerId) {
        this.removePlayerFromAllTeams(playerId);
        return { ...this.gameState };
    }
    submitTeamAnswer(teamId, answerIndex, timeTaken, submittedBy) {
        if (!this.gameState.teams) {
            throw new Error('Team mode is not enabled');
        }
        const team = this.gameState.teams.find(t => t.id === teamId);
        if (!team) {
            throw new Error('Team not found');
        }
        const isPlayerInTeam = team.players.some(p => p.id === submittedBy);
        if (!isPlayerInTeam) {
            throw new Error('Player is not in this team');
        }
        team.hasAnswered = true;
        team.lastAnswer = answerIndex;
        team.lastAnswerTime = timeTaken;
        team.players.forEach(player => {
            const gamePlayer = this.gameState.players.find(p => p.id === player.id);
            if (gamePlayer) {
                gamePlayer.hasAnswered = true;
                gamePlayer.lastAnswer = answerIndex;
                gamePlayer.lastAnswerTime = timeTaken;
            }
        });
        return { ...this.gameState };
    }
    processTeamScoring() {
        if (!this.gameState.teamMode || !this.gameState.teams || !this.gameState.currentQuestion) {
            return { ...this.gameState };
        }
        const correctAnswer = this.gameState.currentQuestion.correctAnswer;
        const scoringMode = this.gameState.teamSettings?.teamScoringMode || 'average';
        this.gameState.teams.forEach(team => {
            if (team.hasAnswered && team.lastAnswer === correctAnswer) {
                const basePoints = this.calculateBasePoints(team.lastAnswerTime || 30);
                let teamPoints = 0;
                switch (scoringMode) {
                    case 'average':
                        teamPoints = basePoints;
                        break;
                    case 'sum':
                        teamPoints = basePoints * team.players.length;
                        break;
                    case 'best':
                        teamPoints = basePoints * 1.2;
                        break;
                    case 'captain':
                        teamPoints = basePoints;
                        break;
                }
                team.score += teamPoints;
                team.players.forEach(player => {
                    const gamePlayer = this.gameState.players.find(p => p.id === player.id);
                    if (gamePlayer) {
                        gamePlayer.score += teamPoints;
                    }
                });
            }
            team.hasAnswered = false;
            team.lastAnswer = undefined;
            team.lastAnswerTime = undefined;
        });
        return { ...this.gameState };
    }
    getTeamsLeaderboard() {
        if (!this.gameState.teams) {
            return [];
        }
        return [...this.gameState.teams].sort((a, b) => b.score - a.score);
    }
    removePlayerFromAllTeams(playerId) {
        if (!this.gameState.teams) {
            return;
        }
        this.gameState.teams.forEach(team => {
            const playerIndex = team.players.findIndex(p => p.id === playerId);
            if (playerIndex !== -1) {
                team.players.splice(playerIndex, 1);
                if (team.players.length === 0) {
                    const teamIndex = this.gameState.teams.findIndex(t => t.id === team.id);
                    if (teamIndex !== -1) {
                        this.gameState.teams.splice(teamIndex, 1);
                    }
                }
                else if (team.captainId === playerId && team.players.length > 0) {
                    team.captainId = team.players[0].id;
                }
            }
        });
    }
    calculateBasePoints(timeTaken) {
        const maxTime = this.gameState.timePerQuestion;
        const timeBonus = Math.max(0, (maxTime - timeTaken) / maxTime);
        return Math.round(100 + (timeBonus * 50));
    }
    generateMockQuestions() {
        const questions = [];
        for (let i = 0; i < 5; i++) {
            questions.push({
                id: `q${i + 1}`,
                type: 'multiple-choice',
                audioFile: `/audio/sample-${i + 1}.mp3`,
                question: `What is this song?`,
                options: ['Song A', 'Song B', 'Song C', 'Song D'],
                correctAnswer: 0,
                points: 100,
                timeLimit: 30
            });
        }
        return questions;
    }
    convertToGameQuestions(multiplayerQuestions) {
        return multiplayerQuestions.map((mq, index) => ({
            id: mq.id || `q${index + 1}`,
            type: mq.type || 'multiple-choice',
            audioFile: mq.track?.file || `/audio/sample-${index + 1}.mp3`,
            question: mq.question,
            options: mq.options || [],
            correctAnswer: typeof mq.correctAnswer === 'number' ? mq.correctAnswer : 0,
            points: 100,
            timeLimit: mq.timeLimit || 30,
            track: mq.track
        }));
    }
    allPlayersAnswered() {
        return this.gameState.players.every(player => player.hasAnswered);
    }
    getLeaderboard() {
        return [...this.gameState.players].sort((a, b) => b.score - a.score);
    }
    startVotingPhase(type, socket, customOptions) {
        if (!this.votingEnabled) {
            return null;
        }
        const totalPlayers = this.gameState.players.length;
        if (totalPlayers === 0) {
            return null;
        }
        let sessionId;
        const votingPhase = {
            type,
            title: customOptions?.title || this.getDefaultVotingTitle(type),
            description: customOptions?.description || this.getDefaultVotingDescription(type),
            isActive: true
        };
        switch (type) {
            case 'category':
                sessionId = voting_socket_handler_1.votingSocketHandler.createCategoryVoting(this.gameState.gameId, totalPlayers, socket);
                break;
            case 'decade':
                sessionId = voting_socket_handler_1.votingSocketHandler.createDecadeVoting(this.gameState.gameId, totalPlayers, socket);
                break;
            case 'game-mode':
                sessionId = voting_socket_handler_1.votingSocketHandler.createGameModeVoting(this.gameState.gameId, totalPlayers, socket);
                break;
            default:
                return null;
        }
        votingPhase.sessionId = sessionId;
        this.currentVotingPhase = votingPhase;
        return votingPhase;
    }
    completeVotingPhase() {
        if (!this.currentVotingPhase || !this.currentVotingPhase.sessionId) {
            return null;
        }
        const votingSession = voting_socket_handler_1.votingSocketHandler.getCurrentVotingSession(this.gameState.gameId);
        if (!votingSession || !votingSession.result) {
            return null;
        }
        const result = votingSession.result;
        let nextAction = '';
        switch (this.currentVotingPhase.type) {
            case 'category':
                nextAction = `Next round will feature ${result.winnerOption.label}`;
                this.applyCategory(result.winnerOption.value);
                break;
            case 'decade':
                nextAction = `Next round will feature music from ${result.winnerOption.label}`;
                this.applyDecade(result.winnerOption.value);
                break;
            case 'game-mode':
                nextAction = `Game mode changed to ${result.winnerOption.label}`;
                this.applyGameMode(result.winnerOption.value);
                break;
            default:
                nextAction = `${result.winnerOption.label} was selected`;
        }
        this.currentVotingPhase = null;
        return { result, nextAction };
    }
    getCurrentVotingSession() {
        return voting_socket_handler_1.votingSocketHandler.getCurrentVotingSession(this.gameState.gameId);
    }
    hasPlayerVoted(playerId) {
        return voting_socket_handler_1.votingSocketHandler.hasPlayerVoted(this.gameState.gameId, playerId);
    }
    getPlayerVote(playerId) {
        return voting_socket_handler_1.votingSocketHandler.getPlayerVote(this.gameState.gameId, playerId);
    }
    shouldTriggerVoting(questionIndex) {
        if (!this.votingEnabled)
            return false;
        return (questionIndex + 1) % 3 === 0 && questionIndex > 0;
    }
    getVotingType(questionIndex) {
        const cycle = Math.floor(questionIndex / 3) % 3;
        switch (cycle) {
            case 0: return 'category';
            case 1: return 'decade';
            case 2: return 'game-mode';
            default: return 'category';
        }
    }
    getCurrentVotingPhase() {
        return this.currentVotingPhase;
    }
    setVotingEnabled(enabled) {
        this.votingEnabled = enabled;
    }
    getDefaultVotingTitle(type) {
        switch (type) {
            case 'category': return 'Choose Next Category';
            case 'decade': return 'Choose Time Period';
            case 'game-mode': return 'Choose Game Mode';
            case 'theme': return 'Choose Theme';
            default: return 'Vote Now';
        }
    }
    getDefaultVotingDescription(type) {
        switch (type) {
            case 'category': return 'Vote for the music category for the next round';
            case 'decade': return 'Vote for the era of music for the next round';
            case 'game-mode': return 'Vote for the game mode for the next round';
            case 'theme': return 'Vote for the theme for the next round';
            default: return 'Cast your vote to decide what happens next';
        }
    }
    applyCategory(category) {
        console.log(`Applying category filter: ${category}`);
    }
    applyDecade(decade) {
        console.log(`Applying decade filter: ${decade}`);
    }
    applyGameMode(gameMode) {
        console.log(`Applying game mode: ${gameMode}`);
        switch (gameMode) {
            case 'speed':
                this.gameState.timePerQuestion = 15;
                break;
            case 'challenge':
                this.gameState.timePerQuestion = 45;
                break;
            default:
                this.gameState.timePerQuestion = 30;
        }
    }
    cleanupVoting() {
        voting_socket_handler_1.votingSocketHandler.cleanupGame(this.gameState.gameId);
        this.currentVotingPhase = null;
    }
}
exports.GameManager = GameManager;
//# sourceMappingURL=game-manager.js.map