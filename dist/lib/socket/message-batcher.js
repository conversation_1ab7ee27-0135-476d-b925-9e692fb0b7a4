"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageBatcher = void 0;
const events_1 = require("events");
class MessageBatcher extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.messageQueues = new Map();
        this.batchTimers = new Map();
        this.messageIdCounter = 0;
        this.config = {
            maxBatchSize: 50,
            maxBatchDelay: 100,
            enableCompression: true,
            priorityLevels: 3,
            adaptiveBatching: true,
            ...config
        };
        this.stats = {
            totalMessages: 0,
            totalBatches: 0,
            averageBatchSize: 0,
            compressionRatio: 0,
            averageDelay: 0,
            bytesReduced: 0
        };
    }
    queueMessage(gameId, type, data, options = {}) {
        const messageId = this.generateMessageId();
        const message = {
            id: messageId,
            type,
            data,
            priority: options.priority || 1,
            timestamp: Date.now(),
            gameId,
            playerId: options.playerId,
            recipients: options.recipients
        };
        console.log(`[MessageBatcher] Priority check: ${options.priority} >= ${this.config.priorityLevels} = ${options.priority >= this.config.priorityLevels}, immediate: ${options.immediate}`);
        if (options.immediate || options.priority >= this.config.priorityLevels) {
            console.log(`[MessageBatcher] Flushing immediately for gameId: ${gameId}`);
            this.flushImmediate(gameId, message);
            return messageId;
        }
        if (!this.messageQueues.has(gameId)) {
            this.messageQueues.set(gameId, []);
        }
        const queue = this.messageQueues.get(gameId);
        queue.push(message);
        queue.sort((a, b) => b.priority - a.priority);
        this.stats.totalMessages++;
        if (this.shouldFlushBatch(gameId)) {
            this.flushBatch(gameId);
        }
        else {
            this.scheduleBatchFlush(gameId);
        }
        return messageId;
    }
    queuePlayerUpdate(gameId, playerId, updateType, data, priority = 1) {
        return this.queueMessage(gameId, `player:${updateType}`, {
            playerId,
            ...data
        }, { priority, playerId });
    }
    queueGameStateUpdate(gameId, updates, priority = 2) {
        return this.queueMessage(gameId, 'game:state_update', updates, { priority });
    }
    queueLeaderboardUpdate(gameId, leaderboard, priority = 2) {
        return this.queueMessage(gameId, 'game:leaderboard_update', {
            leaderboard,
            timestamp: Date.now()
        }, { priority });
    }
    queueQuestionStart(gameId, question, questionIndex, priority = 3) {
        return this.queueMessage(gameId, 'game:question_start', {
            question,
            questionIndex,
            serverTime: Date.now()
        }, { priority, immediate: true });
    }
    queueTimerUpdate(gameId, timeLeft) {
        return this.queueMessage(gameId, 'game:timer_update', {
            timeLeft,
            timestamp: Date.now()
        }, { priority: 0 });
    }
    shouldFlushBatch(gameId) {
        const queue = this.messageQueues.get(gameId);
        if (!queue)
            return false;
        if (queue.length >= this.config.maxBatchSize) {
            return true;
        }
        const highPriorityCount = queue.filter(m => m.priority >= 2).length;
        if (highPriorityCount >= 5) {
            return true;
        }
        if (this.config.adaptiveBatching) {
            const totalQueuedMessages = Array.from(this.messageQueues.values())
                .reduce((sum, q) => sum + q.length, 0);
            if (totalQueuedMessages > 100 && queue.length >= 10) {
                return true;
            }
        }
        return false;
    }
    scheduleBatchFlush(gameId) {
        const existingTimer = this.batchTimers.get(gameId);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }
        let delay = this.config.maxBatchDelay;
        if (this.config.adaptiveBatching) {
            const queue = this.messageQueues.get(gameId);
            if (queue) {
                const avgPriority = queue.reduce((sum, m) => sum + m.priority, 0) / queue.length;
                delay = Math.max(10, this.config.maxBatchDelay * (1 - avgPriority / this.config.priorityLevels));
            }
        }
        const timer = setTimeout(() => {
            this.flushBatch(gameId);
            this.batchTimers.delete(gameId);
        }, delay);
        this.batchTimers.set(gameId, timer);
    }
    flushImmediate(gameId, message) {
        console.log(`[MessageBatcher] flushImmediate called for gameId: ${gameId}, message type: ${message.type}`);
        const batch = {
            batchId: this.generateBatchId(),
            messages: [message],
            totalSize: this.calculateMessageSize(message),
            timestamp: Date.now()
        };
        console.log(`[MessageBatcher] About to emit batch_ready event for gameId: ${gameId}`);
        this.emitBatch(gameId, batch);
        this.updateStats(batch);
    }
    flushBatch(gameId) {
        const queue = this.messageQueues.get(gameId);
        if (!queue || queue.length === 0) {
            return;
        }
        const timer = this.batchTimers.get(gameId);
        if (timer) {
            clearTimeout(timer);
            this.batchTimers.delete(gameId);
        }
        const messages = [...queue];
        queue.length = 0;
        const optimizedMessages = this.optimizeBatch(messages);
        const batch = {
            batchId: this.generateBatchId(),
            messages: optimizedMessages,
            totalSize: optimizedMessages.reduce((sum, m) => sum + this.calculateMessageSize(m), 0),
            timestamp: Date.now()
        };
        if (this.config.enableCompression && batch.totalSize > 1000) {
            batch.compression = 'gzip';
        }
        this.emitBatch(gameId, batch);
        this.updateStats(batch);
    }
    optimizeBatch(messages) {
        const optimized = [];
        const mergeGroups = new Map();
        for (const message of messages) {
            const mergeKey = this.getMergeKey(message);
            if (mergeKey && this.canMerge(message)) {
                if (!mergeGroups.has(mergeKey)) {
                    mergeGroups.set(mergeKey, []);
                }
                mergeGroups.get(mergeKey).push(message);
            }
            else {
                optimized.push(message);
            }
        }
        for (const [key, group] of mergeGroups) {
            if (group.length > 1) {
                const merged = this.mergeMessages(group);
                optimized.push(merged);
            }
            else {
                optimized.push(group[0]);
            }
        }
        return optimized.sort((a, b) => b.priority - a.priority);
    }
    getMergeKey(message) {
        switch (message.type) {
            case 'game:timer_update':
                return `timer:${message.gameId}`;
            case 'game:leaderboard_update':
                return `leaderboard:${message.gameId}`;
            case 'player:score_update':
                return `score:${message.gameId}:${message.playerId}`;
            case 'player:status_update':
                return `status:${message.gameId}:${message.playerId}`;
            default:
                return null;
        }
    }
    canMerge(message) {
        const mergeable = [
            'game:timer_update',
            'game:leaderboard_update',
            'player:score_update',
            'player:status_update'
        ];
        return mergeable.includes(message.type);
    }
    mergeMessages(messages) {
        const latest = messages[messages.length - 1];
        const type = latest.type;
        switch (type) {
            case 'game:timer_update':
                return latest;
            case 'game:leaderboard_update':
                return latest;
            case 'player:score_update':
                return latest;
            case 'player:status_update':
                const mergedData = messages.reduce((acc, msg) => ({
                    ...acc,
                    ...msg.data
                }), {});
                return {
                    ...latest,
                    data: mergedData
                };
            default:
                return latest;
        }
    }
    calculateMessageSize(message) {
        return JSON.stringify(message).length;
    }
    emitBatch(gameId, batch) {
        this.emit('batch_ready', {
            gameId,
            batch
        });
    }
    updateStats(batch) {
        this.stats.totalBatches++;
        const totalMessages = this.stats.totalMessages;
        this.stats.averageBatchSize = totalMessages / this.stats.totalBatches;
        const oldestMessage = batch.messages.reduce((oldest, msg) => msg.timestamp < oldest.timestamp ? msg : oldest);
        const delay = batch.timestamp - oldestMessage.timestamp;
        this.stats.averageDelay = (this.stats.averageDelay + delay) / 2;
        const individualSize = batch.messages.length * 50;
        const batchOverhead = 100;
        this.stats.bytesReduced += Math.max(0, individualSize - batchOverhead);
    }
    flushAll() {
        for (const gameId of this.messageQueues.keys()) {
            this.flushBatch(gameId);
        }
    }
    cancelGame(gameId) {
        const queue = this.messageQueues.get(gameId);
        const cancelledCount = queue ? queue.length : 0;
        this.messageQueues.delete(gameId);
        const timer = this.batchTimers.get(gameId);
        if (timer) {
            clearTimeout(timer);
            this.batchTimers.delete(gameId);
        }
        return cancelledCount;
    }
    getStats() {
        return { ...this.stats };
    }
    getQueueStatus() {
        const status = [];
        for (const [gameId, queue] of this.messageQueues) {
            if (queue.length > 0) {
                const oldestTimestamp = Math.min(...queue.map(m => m.timestamp));
                const timer = this.batchTimers.get(gameId);
                const nextFlush = timer ? Date.now() + this.config.maxBatchDelay : 0;
                status.push({
                    gameId,
                    queueLength: queue.length,
                    oldestMessage: Date.now() - oldestTimestamp,
                    nextFlush
                });
            }
        }
        return status;
    }
    generateMessageId() {
        return `msg_${Date.now()}_${++this.messageIdCounter}`;
    }
    generateBatchId() {
        return `batch_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    }
    shutdown() {
        this.flushAll();
        for (const timer of this.batchTimers.values()) {
            clearTimeout(timer);
        }
        this.messageQueues.clear();
        this.batchTimers.clear();
        this.removeAllListeners();
    }
}
exports.MessageBatcher = MessageBatcher;
//# sourceMappingURL=message-batcher.js.map