"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateSocket = authenticateSocket;
exports.requireAuth = requireAuth;
exports.requireRole = requireRole;
exports.generateSecurePlayerId = generateSecurePlayerId;
exports.validatePlayerOwnership = validatePlayerOwnership;
const auth_service_1 = require("@/lib/auth-service");
async function authenticateSocket(socket, next) {
    try {
        const token = socket.handshake.auth?.token || socket.handshake.query?.token;
        if (!token) {
            console.log(`[SocketAuth] No token provided for socket ${socket.id}`);
            socket.isAuthenticated = false;
            return next();
        }
        const user = await auth_service_1.AuthService.verifyToken(token);
        if (!user) {
            console.log(`[SocketAuth] Invalid token for socket ${socket.id}`);
            socket.isAuthenticated = false;
            return next();
        }
        socket.user = user;
        socket.isAuthenticated = true;
        socket.playerId = user.id;
        console.log(`[SocketAuth] Socket ${socket.id} authenticated as ${user.username} (${user.role})`);
        next();
    }
    catch (error) {
        console.error(`[SocketAuth] Authentication error for socket ${socket.id}:`, error);
        socket.isAuthenticated = false;
        next();
    }
}
function requireAuth(socket, next) {
    if (!socket.isAuthenticated || !socket.user) {
        return next(new Error('Authentication required'));
    }
    next();
}
function requireRole(role) {
    return (socket, next) => {
        if (!socket.isAuthenticated || !socket.user) {
            return next(new Error('Authentication required'));
        }
        if (socket.user.role !== role && socket.user.role !== 'superuser') {
            return next(new Error(`Role '${role}' required`));
        }
        next();
    };
}
function generateSecurePlayerId(userId, gameId) {
    return `${userId}_${gameId}_${Date.now()}`;
}
function validatePlayerOwnership(socket, playerId, gameId) {
    if (!socket.user)
        return false;
    return playerId.startsWith(socket.user.id);
}
//# sourceMappingURL=socket-auth.js.map