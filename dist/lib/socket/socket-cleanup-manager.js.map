{"version": 3, "file": "socket-cleanup-manager.js", "sourceRoot": "", "sources": ["../../../lib/socket/socket-cleanup-manager.ts"], "names": [], "mappings": ";;;AAYA,MAAa,oBAAoB;IAM/B,YAAY,YAA8B,EAAE;QALpC,iBAAY,GAAG,IAAI,OAAO,EAA+B,CAAA;QACzD,oBAAe,GAAG,IAAI,OAAO,EAAwE,CAAA;QACrG,mBAAc,GAAG,IAAI,OAAO,EAAuE,CAAA;QAIzG,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAA;IACnC,CAAC;IAKD,cAAc,CAAC,MAAc,EAAE,QAAiD;QAC9E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE;YAC9B,GAAG,QAAQ;YACX,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC,CAAA;QACF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;QACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;IACtC,CAAC;IAKD,QAAQ,CAAC,MAAc,EAAE,KAAqB;QAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QACnB,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,MAAc,EAAE,KAAqB;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACtB,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,MAAc,EAAE,KAAa,EAAE,QAAkC;QAC3E,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAClD,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAA;QACrC,CAAC;IACH,CAAC;IAKD,aAAa,CAAC,MAAc;QAC1B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;YAGhD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;YAC5C,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;gBAC5C,MAAM,CAAC,KAAK,EAAE,CAAA;YAChB,CAAC;YAGD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;YAClD,IAAI,SAAS,EAAE,CAAC;gBACd,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;oBACxC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBACxC,CAAC,CAAC,CAAA;gBACF,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;YACtB,CAAC;YAGD,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjD,MAAM,CAAC,GAAG,GAAG,EAAE,CAAA;YACjB,CAAC;YAGD,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;gBACvC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;YACnE,CAAC;YAGD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;YAChC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;YACnC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;YACpD,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAClC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAc,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;YAC1D,CAAC;QACH,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,MAAc,EAAE,QAAoB,EAAE,KAAa;QAC7D,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAC/B,QAAQ,EAAE,CAAA;QACZ,CAAC,EAAE,KAAK,CAAC,CAAA;QACT,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;QAC5B,OAAO,KAAK,CAAA;IACd,CAAC;IAKD,cAAc,CACZ,MAAc,EACd,KAAa,EACb,QAAW;QAEX,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;QACzC,OAAO,QAAQ,CAAA;IACjB,CAAC;IAKD,WAAW,CAAC,MAAc;QACxB,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IACxC,CAAC;IAKD,cAAc,CAAC,MAAc,EAAE,OAAsD;QACnF,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC/C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC,CAAA;QAC7D,CAAC;IACH,CAAC;CACF;AAzID,oDAyIC"}