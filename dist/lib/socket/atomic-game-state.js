"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.atomicGameStateManager = exports.AtomicGameStateManager = void 0;
const uuid_1 = require("uuid");
class AtomicGameStateManager {
    constructor() {
        this.locks = new Map();
        this.snapshots = new Map();
        this.LOCK_TIMEOUT = 30000;
        this.MAX_SNAPSHOTS = 10;
    }
    async acquireLock(gameId, operation) {
        const existingLock = this.locks.get(gameId);
        if (existingLock && Date.now() - existingLock.timestamp < this.LOCK_TIMEOUT) {
            console.log(`[AtomicState] Game ${gameId} is locked by operation: ${existingLock.operation}`);
            return null;
        }
        const lockId = (0, uuid_1.v4)();
        const lock = {
            gameId,
            lockId,
            timestamp: Date.now(),
            operation
        };
        this.locks.set(gameId, lock);
        console.log(`[AtomicState] Acquired lock ${lockId} for game ${gameId} operation: ${operation}`);
        setTimeout(() => {
            const currentLock = this.locks.get(gameId);
            if (currentLock && currentLock.lockId === lockId) {
                this.releaseLock(gameId, lockId);
                console.warn(`[AtomicState] Auto-released expired lock ${lockId} for game ${gameId}`);
            }
        }, this.LOCK_TIMEOUT);
        return lockId;
    }
    releaseLock(gameId, lockId) {
        const lock = this.locks.get(gameId);
        if (!lock || lock.lockId !== lockId) {
            console.warn(`[AtomicState] Attempted to release invalid lock ${lockId} for game ${gameId}`);
            return false;
        }
        this.locks.delete(gameId);
        console.log(`[AtomicState] Released lock ${lockId} for game ${gameId}`);
        return true;
    }
    createSnapshot(gameId, gameState) {
        const snapshot = {
            gameId,
            state: JSON.parse(JSON.stringify(gameState)),
            version: Date.now(),
            timestamp: Date.now()
        };
        const key = `${gameId}_${snapshot.version}`;
        this.snapshots.set(key, snapshot);
        this.cleanOldSnapshots(gameId);
        console.log(`[AtomicState] Created snapshot for game ${gameId}`);
    }
    restoreSnapshot(gameId, version) {
        if (version) {
            const key = `${gameId}_${version}`;
            return this.snapshots.get(key) || null;
        }
        const gameSnapshots = Array.from(this.snapshots.values())
            .filter(s => s.gameId === gameId)
            .sort((a, b) => b.version - a.version);
        return gameSnapshots[0] || null;
    }
    async executeAtomicOperation(gameId, operation, gameState, operations, updateCallback) {
        const lockId = await this.acquireLock(gameId, operation);
        if (!lockId) {
            return { success: false, error: 'Could not acquire lock - game state busy' };
        }
        try {
            this.createSnapshot(gameId, gameState);
            let newState = JSON.parse(JSON.stringify(gameState));
            const rollbackOperations = [];
            for (const op of operations) {
                try {
                    switch (op.type) {
                        case 'ADD_PLAYER':
                            newState.players.push(op.data);
                            rollbackOperations.push(() => {
                                const index = newState.players.findIndex((p) => p.id === op.data.id);
                                if (index >= 0)
                                    newState.players.splice(index, 1);
                            });
                            break;
                        case 'UPDATE_PLAYER':
                            const playerIndex = newState.players.findIndex((p) => p.id === op.data.id);
                            if (playerIndex >= 0) {
                                const oldPlayer = { ...newState.players[playerIndex] };
                                newState.players[playerIndex] = { ...newState.players[playerIndex], ...op.data };
                                rollbackOperations.push(() => {
                                    newState.players[playerIndex] = oldPlayer;
                                });
                            }
                            break;
                        case 'REMOVE_PLAYER':
                            const removeIndex = newState.players.findIndex((p) => p.id === op.data.id);
                            if (removeIndex >= 0) {
                                const removedPlayer = newState.players[removeIndex];
                                newState.players.splice(removeIndex, 1);
                                rollbackOperations.push(() => {
                                    newState.players.splice(removeIndex, 0, removedPlayer);
                                });
                            }
                            break;
                        case 'UPDATE_SCORE':
                            const scorePlayerIndex = newState.players.findIndex((p) => p.id === op.data.playerId);
                            if (scorePlayerIndex >= 0) {
                                const oldScore = newState.players[scorePlayerIndex].score;
                                newState.players[scorePlayerIndex].score = op.data.newScore;
                                rollbackOperations.push(() => {
                                    newState.players[scorePlayerIndex].score = oldScore;
                                });
                            }
                            break;
                        case 'SET_GAME_STATE':
                            const oldGameState = newState.gameState;
                            newState.gameState = op.data.newState;
                            rollbackOperations.push(() => {
                                newState.gameState = oldGameState;
                            });
                            break;
                        case 'UPDATE_QUESTION':
                            const oldQuestion = newState.currentQuestion;
                            newState.currentQuestion = op.data;
                            rollbackOperations.push(() => {
                                newState.currentQuestion = oldQuestion;
                            });
                            break;
                        case 'ADD_TEAM':
                            newState.teams = newState.teams || [];
                            newState.teams.push(op.data);
                            rollbackOperations.push(() => {
                                const teamIndex = newState.teams.findIndex((t) => t.id === op.data.id);
                                if (teamIndex >= 0)
                                    newState.teams.splice(teamIndex, 1);
                            });
                            break;
                        case 'UPDATE_TEAM':
                            const teamUpdateIndex = newState.teams?.findIndex((t) => t.id === op.data.id);
                            if (teamUpdateIndex >= 0) {
                                const oldTeam = { ...newState.teams[teamUpdateIndex] };
                                newState.teams[teamUpdateIndex] = { ...newState.teams[teamUpdateIndex], ...op.data };
                                rollbackOperations.push(() => {
                                    newState.teams[teamUpdateIndex] = oldTeam;
                                });
                            }
                            break;
                        default:
                            if (op.rollback) {
                                rollbackOperations.push(op.rollback);
                            }
                    }
                }
                catch (error) {
                    console.error(`[AtomicState] Error in operation ${op.type}:`, error);
                    rollbackOperations.reverse().forEach(rollback => {
                        try {
                            rollback();
                        }
                        catch (rollbackError) {
                            console.error('[AtomicState] Rollback operation failed:', rollbackError);
                        }
                    });
                    throw error;
                }
            }
            await updateCallback(newState);
            this.releaseLock(gameId, lockId);
            console.log(`[AtomicState] Successfully executed atomic operation: ${operation}`);
            return { success: true, newState };
        }
        catch (error) {
            console.error(`[AtomicState] Atomic operation failed for game ${gameId}:`, error);
            const snapshot = this.restoreSnapshot(gameId);
            if (snapshot) {
                await updateCallback(snapshot.state);
                console.log(`[AtomicState] Restored game ${gameId} from snapshot`);
            }
            this.releaseLock(gameId, lockId);
            return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
        }
    }
    cleanOldSnapshots(gameId) {
        const gameSnapshots = Array.from(this.snapshots.entries())
            .filter(([key]) => key.startsWith(gameId + '_'))
            .sort(([, a], [, b]) => b.version - a.version);
        if (gameSnapshots.length > this.MAX_SNAPSHOTS) {
            const toDelete = gameSnapshots.slice(this.MAX_SNAPSHOTS);
            toDelete.forEach(([key]) => {
                this.snapshots.delete(key);
            });
            console.log(`[AtomicState] Cleaned ${toDelete.length} old snapshots for game ${gameId}`);
        }
    }
    cleanupGame(gameId) {
        this.locks.delete(gameId);
        const keysToDelete = [];
        for (const key of this.snapshots.keys()) {
            if (key.startsWith(gameId + '_')) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach(key => this.snapshots.delete(key));
        console.log(`[AtomicState] Cleaned up atomic state data for game ${gameId}`);
    }
    getLockStatus(gameId) {
        return this.locks.get(gameId) || null;
    }
    cleanExpiredLocks() {
        const now = Date.now();
        const expiredLocks = [];
        for (const [gameId, lock] of this.locks.entries()) {
            if (now - lock.timestamp > this.LOCK_TIMEOUT) {
                expiredLocks.push(gameId);
            }
        }
        expiredLocks.forEach(gameId => {
            this.locks.delete(gameId);
        });
        if (expiredLocks.length > 0) {
            console.log(`[AtomicState] Cleaned ${expiredLocks.length} expired locks`);
        }
    }
}
exports.AtomicGameStateManager = AtomicGameStateManager;
exports.atomicGameStateManager = new AtomicGameStateManager();
setInterval(() => {
    exports.atomicGameStateManager.cleanExpiredLocks();
}, 60000);
//# sourceMappingURL=atomic-game-state.js.map