import { EventEmitter } from 'events';
export interface CacheConfig {
    maxGameStates: number;
    maxLeaderboards: number;
    maxQuestions: number;
    gameStateTTL: number;
    leaderboardTTL: number;
    questionTTL: number;
    enablePersistentCache: boolean;
    compressionEnabled: boolean;
    writeThrough: boolean;
}
export interface CacheStats {
    gameStateHits: number;
    gameStateMisses: number;
    leaderboardHits: number;
    leaderboardMisses: number;
    questionHits: number;
    questionMisses: number;
    evictions: number;
    memoryUsage: number;
    compressionRatio: number;
}
export interface CachedGameState {
    gameId: string;
    status: string;
    players: Map<string, any>;
    questions: any[];
    currentQuestionIndex: number;
    settings: any;
    lastUpdated: number;
    version: number;
    compressed?: boolean;
    checksum?: string;
}
export interface CachedLeaderboard {
    gameId: string;
    entries: any[];
    lastUpdated: number;
    questionIndex: number;
    version: number;
}
export interface CachedQuestion {
    questionId: string;
    gameMode: string;
    difficulty: number;
    data: any;
    metadata: any;
    createdAt: number;
}
export declare class GameStateCache extends EventEmitter {
    private config;
    private gameStateCache;
    private leaderboardCache;
    private questionCache;
    private stats;
    private compressionCache;
    private compressionCacheCleanupInterval;
    private static readonly MAX_COMPRESSION_CACHE_SIZE;
    private static readonly COMPRESSION_CACHE_TTL;
    private static readonly COMPRESSION_CLEANUP_INTERVAL;
    constructor(config?: Partial<CacheConfig>);
    private initializeCaches;
    private initializeStats;
    cacheGameState(gameId: string, gameState: any): Promise<void>;
    getCachedGameState(gameId: string): Promise<CachedGameState | null>;
    cacheLeaderboard(gameId: string, leaderboard: any[], questionIndex: number): Promise<void>;
    getCachedLeaderboard(gameId: string, questionIndex?: number): CachedLeaderboard | null;
    cacheQuestion(questionId: string, question: any, metadata?: any): Promise<void>;
    getCachedQuestion(questionId: string): CachedQuestion | null;
    batchCacheQuestions(questions: Array<{
        id: string;
        data: any;
        metadata?: any;
    }>): Promise<void>;
    getQuestionsByGameMode(gameMode: string, limit?: number): CachedQuestion[];
    invalidateGameCache(gameId: string): void;
    warmUpCache(gameIds: string[]): Promise<void>;
    private compressGameState;
    private decompressGameState;
    private calculateChecksum;
    private estimateSize;
    private updateMemoryStats;
    getStats(): CacheStats & {
        gameStateCacheSize: number;
        leaderboardCacheSize: number;
        questionCacheSize: number;
        hitRates: {
            gameState: number;
            leaderboard: number;
            question: number;
        };
    };
    clearAll(): void;
    prune(): void;
    private startCompressionCacheCleanup;
    private cleanupCompressionCache;
    removeGame(gameId: string): void;
    shutdown(): void;
}
