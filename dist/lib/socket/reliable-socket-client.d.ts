import { EventEmitter } from 'events';
export interface SocketConfig {
    urls: string[];
    maxReconnectAttempts: number;
    reconnectDelay: number;
    maxReconnectDelay: number;
    timeout: number;
    enableQueue: boolean;
    enableHeartbeat: boolean;
    heartbeatInterval: number;
    enableMockMode: boolean;
}
export interface QueuedMessage {
    id: string;
    event: string;
    data: any;
    timestamp: number;
    retries: number;
    maxRetries: number;
    callback?: (error?: Error, data?: any) => void;
}
export interface ConnectionState {
    isConnected: boolean;
    isConnecting: boolean;
    isReconnecting: boolean;
    currentUrl: string | null;
    lastConnected: number | null;
    totalReconnectAttempts: number;
    consecutiveFailures: number;
    latency: number;
}
export type SocketEvent = 'connect' | 'disconnect' | 'reconnect' | 'reconnect_failed' | 'error' | 'connection_state_change' | 'message_queued' | 'message_sent' | 'message_failed';
export declare class ReliableSocketClient extends EventEmitter {
    private socket;
    private config;
    private connectionState;
    private messageQueue;
    private heartbeatInterval;
    private reconnectTimeout;
    private currentUrlIndex;
    private isDestroyed;
    private pendingAcks;
    constructor(config?: Partial<SocketConfig>);
    connect(): Promise<void>;
    private attemptConnection;
    private connectToUrl;
    private setupSocket;
    private scheduleReconnect;
    emit(event: string, data?: any, options?: {
        timeout?: number;
        maxRetries?: number;
        requireAck?: boolean;
    }): Promise<any>;
    private sendMessage;
    private queueMessage;
    private processMessageQueue;
    private enableMockMode;
    private generateMockResponse;
    private startHeartbeat;
    private stopHeartbeat;
    private updateConnectionState;
    private setupUnloadHandlers;
    private generateMessageId;
    getConnectionState(): ConnectionState;
    getQueuedMessageCount(): number;
    on(event: string, listener: (...args: any[]) => void): this;
    off(event: string, listener?: (...args: any[]) => void): this;
    disconnect(): void;
    reconnect(): Promise<void>;
    isConnected(): boolean;
    getLatency(): number;
}
