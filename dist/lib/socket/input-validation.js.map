{"version": 3, "file": "input-validation.js", "sourceRoot": "", "sources": ["../../../lib/socket/input-validation.ts"], "names": [], "mappings": ";;;AA+EA,oCA0BC;AAKD,gDAqBC;AAnID,6BAAuB;AAEvB,IAAI,SAAS,GAAQ,IAAI,CAAA;AACzB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IAElC,IAAI,CAAC;QACH,SAAS,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAA;IAC7C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAA;IACrE,CAAC;AACH,CAAC;KAAM,CAAC;IAEN,IAAI,CAAC;QACH,SAAS,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAA;IAC7C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAA;IACrE,CAAC;AACH,CAAC;AAGY,QAAA,OAAO,GAAG;IAErB,UAAU,EAAE,OAAC,CAAC,MAAM,CAAC;QACnB,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAClF,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QACpC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACjD,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QACpD,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QACnD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;KAChD,CAAC;IAGF,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;QACjB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC;QACvD,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;KACtC,CAAC;IAGF,YAAY,EAAE,OAAC,CAAC,MAAM,CAAC;QACrB,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;QAC7B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAClC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;QACtC,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KACvC,CAAC;IAGF,UAAU,EAAE,OAAC,CAAC,MAAM,CAAC;QACnB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;QACnC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,QAAQ,EAAE;KACtD,CAAC;IAEF,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;QACjB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;KAC1B,CAAC;IAGF,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;QACjB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACnC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;KAC1B,CAAC;IAGF,kBAAkB,EAAE,OAAC,CAAC,MAAM,CAAC;QAC3B,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QAClD,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QACnD,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAChC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;KAClD,CAAC;IAGF,eAAe,EAAE,OAAC,CAAC,MAAM,CAAC;QACxB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;QACzB,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KACtC,CAAC;CACH,CAAA;AAKD,SAAgB,YAAY,CAAC,KAAa;IACxC,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,EAAE,CAAA;IAExC,IAAI,SAAS,GAAG,KAAK,CAAA;IAGrB,IAAI,SAAS,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;QACpC,IAAI,CAAC;YACH,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACpC,YAAY,EAAE,EAAE;gBAChB,YAAY,EAAE,EAAE;aACjB,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAA;QACzE,CAAC;IACH,CAAC;IAGD,SAAS,GAAG,SAAS;SAClB,IAAI,EAAE;SACN,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;SACvB,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;SAC5B,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;SACvB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;IAErB,OAAO,SAAS,CAAA;AAClB,CAAC;AAKD,SAAgB,kBAAkB,CAAI,MAAsB,EAAE,IAAa;IAKzE,IAAI,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QAErC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACjG,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,YAAY,EAAE,EAAE,CAAA;QACxE,CAAC;QAGD,MAAM,aAAa,GAAG,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QAExD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,aAAkB,EAAE,CAAA;IACpD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAA;IACzD,CAAC;AACH,CAAC;AAKD,SAAS,qBAAqB,CAAC,GAAQ;IACrC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,YAAY,CAAC,GAAG,CAAC,CAAA;IAC1B,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;IACvC,CAAC;IAED,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QACnC,MAAM,SAAS,GAAQ,EAAE,CAAA;QACzB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/C,SAAS,CAAC,GAAG,CAAC,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;QACD,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAKY,QAAA,UAAU,GAAG;IAExB,aAAa,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE;IACxC,WAAW,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;IACvC,YAAY,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE;IAGvC,eAAe,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;IACzC,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE;IAG1C,aAAa,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE;IACxC,WAAW,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;IACvC,YAAY,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;IAGxC,WAAW,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;IAGvC,mBAAmB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;IAG/C,SAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;CACtC,CAAA;AAKD,MAAM,iBAAiB;IAAvB;QACU,WAAM,GAAG,IAAI,GAAG,EAAiC,CAAA;IA4D3D,CAAC;IA1DC,KAAK,CAAC,QAAgB,EAAE,SAAiB;QACvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,KAAK,GAAG,kBAAU,CAAC,SAAoC,CAAC,IAAI,kBAAU,CAAC,OAAO,CAAA;QAEpF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;QACtC,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAA;QAE/C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,SAAS,CAAE,CAAA;QAG3C,MAAM,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;QACjC,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,MAAM,CAAC,CAAA;QAGxD,IAAI,WAAW,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC;YACpC,OAAO,KAAK,CAAA;QACd,CAAC;QAGD,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACrB,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAExC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,OAAO,CAAC,QAAgB;QACtB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IAC9B,CAAC;IAGD,eAAe;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAA;QAE3E,KAAK,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7D,KAAK,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;gBACzD,MAAM,MAAM,GAAG,GAAG,GAAG,SAAS,CAAA;gBAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,MAAM,CAAC,CAAA;gBAExD,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC7B,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;gBAChC,CAAC;qBAAM,CAAC;oBACN,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;gBAC1C,CAAC;YACH,CAAC;YAED,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAEY,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAA;AAGxD,WAAW,CAAC,GAAG,EAAE;IACf,yBAAiB,CAAC,eAAe,EAAE,CAAA;AACrC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA"}