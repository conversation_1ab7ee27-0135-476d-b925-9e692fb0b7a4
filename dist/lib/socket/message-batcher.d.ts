import { EventEmitter } from 'events';
export interface BatchConfig {
    maxBatchSize: number;
    maxBatchDelay: number;
    enableCompression: boolean;
    priorityLevels: number;
    adaptiveBatching: boolean;
}
export interface QueuedMessage {
    id: string;
    type: string;
    data: any;
    priority: number;
    timestamp: number;
    gameId?: string;
    playerId?: string;
    recipients?: string[];
}
export interface BatchedMessage {
    batchId: string;
    messages: QueuedMessage[];
    totalSize: number;
    compression?: 'gzip' | 'deflate';
    timestamp: number;
}
export interface BatchStats {
    totalMessages: number;
    totalBatches: number;
    averageBatchSize: number;
    compressionRatio: number;
    averageDelay: number;
    bytesReduced: number;
}
export declare class MessageBatcher extends EventEmitter {
    private config;
    private messageQueues;
    private batchTimers;
    private stats;
    private messageIdCounter;
    constructor(config?: Partial<BatchConfig>);
    queueMessage(gameId: string, type: string, data: any, options?: {
        priority?: number;
        playerId?: string;
        recipients?: string[];
        immediate?: boolean;
    }): string;
    queuePlayerUpdate(gameId: string, playerId: string, updateType: string, data: any, priority?: number): string;
    queueGameStateUpdate(gameId: string, updates: any, priority?: number): string;
    queueLeaderboardUpdate(gameId: string, leaderboard: any[], priority?: number): string;
    queueQuestionStart(gameId: string, question: any, questionIndex: number, priority?: number): string;
    queueTimerUpdate(gameId: string, timeLeft: number): string;
    private shouldFlushBatch;
    private scheduleBatchFlush;
    private flushImmediate;
    flushBatch(gameId: string): void;
    private optimizeBatch;
    private getMergeKey;
    private canMerge;
    private mergeMessages;
    private calculateMessageSize;
    private emitBatch;
    private updateStats;
    flushAll(): void;
    cancelGame(gameId: string): number;
    getStats(): BatchStats;
    getQueueStatus(): Array<{
        gameId: string;
        queueLength: number;
        oldestMessage: number;
        nextFlush: number;
    }>;
    private generateMessageId;
    private generateBatchId;
    shutdown(): void;
}
