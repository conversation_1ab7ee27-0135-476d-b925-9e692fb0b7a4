import { Server as SocketIOServer } from 'socket.io';
export interface DebugOptions {
    logToFile?: boolean;
    logFilePath?: string;
    consoleOutput?: boolean;
    includeData?: boolean;
    filterEvents?: string[];
    excludeEvents?: string[];
}
export declare class SocketDebugMonitor {
    private io;
    private options;
    private eventLog;
    private startTime;
    constructor(io: SocketIOServer, options?: DebugOptions);
    private setupInterceptors;
    private interceptSocketEvents;
    private log;
    private printToConsole;
    private writeToFile;
    getEventLog(): any[];
    getEventSummary(): {
        [key: string]: number;
    };
    clearLog(): void;
    exportLog(filePath: string): void;
}
export declare function attachDebugMonitor(io: SocketIOServer, options?: DebugOptions): SocketDebugMonitor;
