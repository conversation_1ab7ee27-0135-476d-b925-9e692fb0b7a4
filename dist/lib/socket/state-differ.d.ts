export interface DeltaUpdate {
    delta: any;
    version: number;
    fullStateChecksum?: string;
}
export declare class StateDiffer {
    private previousStates;
    private stateVersions;
    createDelta(gameId: string, newState: any): DeltaUpdate | null;
    applyDelta(baseState: any, delta: any): any;
    private applyDeltaRecursive;
    private diff;
    removeGameState(gameId: string): void;
    getVersion(gameId: string): number;
    private deepClone;
    private calculateChecksum;
    getStats(): {
        games: number;
        totalSize: number;
    };
}
export declare const stateDiffer: StateDiffer;
