import { EventEmitter } from 'events';
import { ReliableSocketClient } from './reliable-socket-client';
export interface PoolConfig {
    servers: ServerEndpoint[];
    maxConnectionsPerServer: number;
    healthCheckInterval: number;
    loadBalancingStrategy: 'round-robin' | 'least-connections' | 'latency-based' | 'geographic';
    reconnectionConfig: {
        maxAttempts: number;
        baseDelay: number;
        maxDelay: number;
    };
}
export interface ServerEndpoint {
    id: string;
    url: string;
    region: string;
    priority: number;
    maxConnections: number;
}
export interface ServerHealth {
    serverId: string;
    isHealthy: boolean;
    latency: number;
    activeConnections: number;
    lastChecked: number;
    errorRate: number;
    successRate: number;
}
export interface ConnectionMetrics {
    totalConnections: number;
    activeConnections: number;
    failedConnections: number;
    averageLatency: number;
    connectionDistribution: Map<string, number>;
    errorRates: Map<string, number>;
}
export declare class ConnectionPoolManager extends EventEmitter {
    private config;
    private pools;
    private healthStatus;
    private healthCheckInterval;
    private metrics;
    private currentRoundRobinIndex;
    constructor(config: PoolConfig);
    private initializePools;
    getOptimalConnection(region?: string): Promise<ReliableSocketClient>;
    private getRoundRobinConnection;
    private getLeastConnectionsConnection;
    private getLowestLatencyConnection;
    private getGeographicConnection;
    private getHealthyServers;
    private startHealthChecks;
    private performHealthChecks;
    private updateConnectionDistribution;
    private updateErrorRate;
    private calculateSuccessRate;
    private calculateErrorRate;
    private updateMetrics;
    getMetrics(): ConnectionMetrics;
    getHealthStatus(): Map<string, ServerHealth>;
    reconnectToServer(serverId: string): Promise<void>;
    shutdown(): Promise<void>;
}
