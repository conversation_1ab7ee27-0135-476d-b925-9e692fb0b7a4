{"version": 3, "file": "resource-manager.js", "sourceRoot": "", "sources": ["../../../lib/socket/resource-manager.ts"], "names": [], "mappings": ";;;AAAA,mCAAqC;AA0BrC,MAAa,eAAgB,SAAQ,qBAAY;IAuB/C;QACE,KAAK,EAAE,CAAA;QAvBD,cAAS,GAAG,IAAI,GAAG,EAA2B,CAAA;QAC9C,gBAAW,GAAG,IAAI,GAAG,EAAuB,CAAA;QAC5C,kBAAa,GAAG,IAAI,GAAG,EAAuB,CAAA;QAC9C,4BAAuB,GAAG,IAAI,GAAG,EAAkB,CAAA;QACnD,sBAAiB,GAAG,IAAI,GAAG,EAA0B,CAAA;QAE5C,WAAM,GAAkB;YACvC,aAAa,EAAE,CAAC;YAChB,iBAAiB,EAAE,EAAE;YACrB,eAAe,EAAE,EAAE;YACnB,sBAAsB,EAAE,IAAI;YAC5B,oBAAoB,EAAE,EAAE;SACzB,CAAA;QAEgB,QAAG,GAAG;YACrB,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;YACzB,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;YAC1B,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;YACtB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;YACrB,gBAAgB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;SACjC,CAAA;QAIC,IAAI,CAAC,oBAAoB,EAAE,CAAA;IAC7B,CAAC;IAKD,gBAAgB,CACd,EAAU,EACV,IAA6B,EAC7B,WAAgB,EAAE,EAClB,EAAW;QAGX,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,wCAAwC,IAAI,YAAY,EAAE,EAAE,CAAC,CAAA;YAC1E,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,QAAQ,GAAoB;YAChC,EAAE;YACF,IAAI;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;YACxB,QAAQ;SACT,CAAA;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;QAGhC,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;gBACpB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;oBAC9B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;gBACrC,CAAC;gBACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACnC,CAAC;iBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC7B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;oBAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;gBACvC,CAAC;gBACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACrC,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI,cAAc,EAAE,EAAE,CAAC,CAAA;QACnE,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAA;QAEvD,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,aAAa,CAAC,EAAU;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QACvC,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACpC,CAAC;IACH,CAAC;IAKD,kBAAkB,CAAC,EAAU,EAAE,EAAW;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QACvC,IAAI,CAAC,QAAQ;YAAE,OAAM;QAGrB,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,QAAQ,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC7B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;gBACpC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC;oBACzC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;gBAC7B,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACtC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;gBACtC,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC;oBAC3C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;gBAC/B,CAAC;YACH,CAAC;QACH,CAAC;QAGD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QAC5C,IAAI,KAAK,EAAE,CAAC;YACV,YAAY,CAAC,KAAK,CAAC,CAAA;YACnB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;QACnC,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;QACzB,OAAO,CAAC,GAAG,CAAC,kCAAkC,QAAQ,CAAC,IAAI,cAAc,EAAE,EAAE,CAAC,CAAA;QAC9E,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;IAChE,CAAC;IAKD,eAAe,CAAC,EAAU,EAAE,OAAe,EAAE,eAA2B;QAEtE,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QACpD,IAAI,aAAa,EAAE,CAAC;YAClB,YAAY,CAAC,aAAa,CAAC,CAAA;QAC7B,CAAC;QAGD,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC;gBACH,eAAe,EAAE,CAAA;gBACjB,OAAO,CAAC,GAAG,CAAC,8DAA8D,EAAE,EAAE,CAAC,CAAA;YACjF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,GAAG,EAAE,KAAK,CAAC,CAAA;YAC9E,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YACnC,CAAC;QACH,CAAC,EAAE,OAAO,CAAC,CAAA;QAEX,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;QACrC,OAAO,CAAC,GAAG,CAAC,2CAA2C,EAAE,OAAO,OAAO,IAAI,CAAC,CAAA;IAC9E,CAAC;IAKO,WAAW,CAAC,IAA6B,EAAE,EAAW,EAAE,QAAc;QAC5E,IAAI,CAAC,EAAE;YAAE,OAAO,IAAI,CAAA;QAEpB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM;gBACT,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,CAAA;gBACrD,OAAO,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAA;YAE9C,KAAK,QAAQ;gBACX,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,CAAA;gBACzD,OAAO,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAA;YAElD,KAAK,QAAQ;gBACX,IAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;oBACrB,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC;yBACpD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA;oBACrD,OAAO,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAA;gBAC7D,CAAC;gBACD,OAAO,IAAI,CAAA;YAEb;gBACE,OAAO,IAAI,CAAA;QACf,CAAC;IACH,CAAC;IAKD,kBAAkB,CAAC,IAA6B;QAC9C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;IACzE,CAAC;IAKD,WAAW,CAAC,EAAU;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAC/B,CAAC;IAKD,qBAAqB;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,QAAQ,GAAa,EAAE,CAAA;QAE7B,KAAK,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YACtD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;YACnC,MAAM,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,YAAY,CAAA;YAEvC,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;gBACd,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACnB,CAAC;QACH,CAAC;QAED,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAE,CAAA;YACxC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAA;YAC3B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;QAC3D,CAAC,CAAC,CAAA;QAEF,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,6BAA6B,QAAQ,CAAC,MAAM,oBAAoB,CAAC,CAAA;QAC/E,CAAC;IACH,CAAC;IAKD,yBAAyB,CAAC,QAAgB;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAEhE,IAAI,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;YACjD,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAA;QAGxD,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;QAC/C,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;QAElB,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,QAAQ;QAaN,MAAM,MAAM,GAA2B,EAAE,CAAA;QAEzC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QAC1D,CAAC;QAED,MAAM,UAAU,GAA2B,EAAE,CAAA;QAC7C,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YACrD,UAAU,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAA;QAC7B,CAAC;QAED,MAAM,YAAY,GAA2B,EAAE,CAAA;QAC/C,KAAK,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YACzD,YAAY,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAA;QACjC,CAAC;QAED,OAAO;YACL,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YACnC,MAAM;YACN,OAAO,EAAE;gBACP,UAAU;gBACV,YAAY;aACb;YACD,WAAW,EAAE;gBACX,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;gBAC9B,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;gBACnC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI;aACrD;SACF,CAAA;IACH,CAAC;IAKD,eAAe;QACb,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAA;QAG7D,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC;YACpD,YAAY,CAAC,KAAK,CAAC,CAAA;QACrB,CAAC;QAGD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QACtB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;QACxB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;QAC1B,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAA;QACpC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;QAE9B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;IAClC,CAAC;IAKO,oBAAoB;QAE1B,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAC9B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;QAGjB,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;YAC7B,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;QACzE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;QAElB,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAA;IACrE,CAAC;IAKD,gBAAgB;QACd,OAAO,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAA;QAE7D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,QAAQ,GAAa,EAAE,CAAA;QAG7B,KAAK,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YACtD,MAAM,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,YAAY,CAAA;YACvC,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;gBACzB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACnB,CAAC;QACH,CAAC;QAED,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAA;QAGnD,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAA;QAEpC,OAAO,CAAC,GAAG,CAAC,+CAA+C,QAAQ,CAAC,MAAM,YAAY,CAAC,CAAA;QACvF,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,YAAY,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;IAClE,CAAC;CACF;AAxVD,0CAwVC;AAGY,QAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAA"}