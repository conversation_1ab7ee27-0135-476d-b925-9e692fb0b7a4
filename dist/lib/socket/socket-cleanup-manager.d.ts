import { Socket } from 'socket.io';
export interface CleanupCallbacks {
    onDisconnect?: (socketId: string, playerId?: string) => void;
    onTimeout?: (socketId: string, playerId?: string) => void;
    onError?: (error: Error, socketId: string) => void;
}
export declare class SocketCleanupManager {
    private socketTimers;
    private socketListeners;
    private socketMetadata;
    private cleanupCallbacks;
    constructor(callbacks?: CleanupCallbacks);
    registerSocket(socket: Socket, metadata?: {
        playerId?: string;
        gameId?: string;
    }): void;
    addTimer(socket: Socket, timer: NodeJS.Timeout): void;
    removeTimer(socket: Socket, timer: NodeJS.Timeout): void;
    addListener(socket: Socket, event: string, listener: (...args: any[]) => void): void;
    cleanupSocket(socket: Socket): void;
    createTimer(socket: Socket, callback: () => void, delay: number): NodeJS.Timeout;
    createListener<T extends (...args: any[]) => void>(socket: Socket, event: string, listener: T): T;
    getMetadata(socket: Socket): {
        playerId?: string;
        gameId?: string;
        connectedAt: number;
    } | undefined;
    updateMetadata(socket: Socket, updates: Partial<{
        playerId: string;
        gameId: string;
    }>): void;
}
