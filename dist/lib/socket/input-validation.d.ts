import { z } from 'zod';
export declare const schemas: {
    createGame: z.ZodObject<{
        gameMode: z.ZodEnum<["classic", "guess-the-year", "album-challenge", "artist-focus"]>;
        teamMode: z.<PERSON><PERSON><z.ZodBoolean>;
        maxTeamSize: z.<PERSON><PERSON><PERSON><z.ZodNumber>;
        questionCount: z.<PERSON><z.ZodNumber>;
        questionTime: z.ZodDefault<z.ZodNumber>;
        gameName: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        maxTeamSize: number;
        gameMode: "classic" | "guess-the-year" | "album-challenge" | "artist-focus";
        teamMode: boolean;
        questionCount: number;
        questionTime: number;
        gameName?: string | undefined;
    }, {
        gameMode: "classic" | "guess-the-year" | "album-challenge" | "artist-focus";
        maxTeamSize?: number | undefined;
        teamMode?: boolean | undefined;
        questionCount?: number | undefined;
        questionTime?: number | undefined;
        gameName?: string | undefined;
    }>;
    joinGame: z.ZodObject<{
        gameCode: z.ZodString;
        playerName: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        gameCode: string;
        playerName: string;
    }, {
        gameCode: string;
        playerName: string;
    }>;
    submitAnswer: z.ZodObject<{
        questionId: z.ZodString;
        answer: z.ZodString;
        answerIndex: z.ZodNumber;
        clientTimestamp: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        answer: string;
        questionId: string;
        answerIndex: number;
        clientTimestamp: number;
    }, {
        answer: string;
        questionId: string;
        answerIndex: number;
        clientTimestamp: number;
    }>;
    createTeam: z.ZodObject<{
        teamName: z.ZodString;
        color: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        teamName: string;
        color?: string | undefined;
    }, {
        teamName: string;
        color?: string | undefined;
    }>;
    joinTeam: z.ZodObject<{
        teamId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        teamId: string;
    }, {
        teamId: string;
    }>;
    teamChat: z.ZodObject<{
        message: z.ZodString;
        teamId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        teamId: string;
        message: string;
    }, {
        teamId: string;
        message: string;
    }>;
    updateGameSettings: z.ZodObject<{
        questionTime: z.ZodOptional<z.ZodNumber>;
        questionCount: z.ZodOptional<z.ZodNumber>;
        teamMode: z.ZodOptional<z.ZodBoolean>;
        maxTeamSize: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        maxTeamSize?: number | undefined;
        teamMode?: boolean | undefined;
        questionCount?: number | undefined;
        questionTime?: number | undefined;
    }, {
        maxTeamSize?: number | undefined;
        teamMode?: boolean | undefined;
        questionCount?: number | undefined;
        questionTime?: number | undefined;
    }>;
    reconnectToGame: z.ZodObject<{
        gameId: z.ZodString;
        lastKnownState: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        gameId: string;
        lastKnownState?: string | undefined;
    }, {
        gameId: string;
        lastKnownState?: string | undefined;
    }>;
};
export declare function sanitizeText(input: string): string;
export declare function validateSocketData<T>(schema: z.ZodSchema<T>, data: unknown): {
    success: boolean;
    data?: T;
    error?: string;
};
export declare const rateLimits: {
    'create-game': {
        window: number;
        max: number;
    };
    'join-game': {
        window: number;
        max: number;
    };
    'start-game': {
        window: number;
        max: number;
    };
    'submit-answer': {
        window: number;
        max: number;
    };
    'skip-question': {
        window: number;
        max: number;
    };
    'create-team': {
        window: number;
        max: number;
    };
    'join-team': {
        window: number;
        max: number;
    };
    'leave-team': {
        window: number;
        max: number;
    };
    'team-chat': {
        window: number;
        max: number;
    };
    reconnect_to_game: {
        window: number;
        max: number;
    };
    default: {
        window: number;
        max: number;
    };
};
declare class SocketRateLimiter {
    private limits;
    check(socketId: string, eventType: string): boolean;
    cleanup(socketId: string): void;
    periodicCleanup(): void;
}
export declare const socketRateLimiter: SocketRateLimiter;
export {};
