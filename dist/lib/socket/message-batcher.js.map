{"version": 3, "file": "message-batcher.js", "sourceRoot": "", "sources": ["../../../lib/socket/message-batcher.ts"], "names": [], "mappings": ";;;AAKA,mCAAqC;AAsCrC,MAAa,cAAe,SAAQ,qBAAY;IAO9C,YAAY,SAA+B,EAAE;QAC3C,KAAK,EAAE,CAAA;QAND,kBAAa,GAAiC,IAAI,GAAG,EAAE,CAAA;QACvD,gBAAW,GAAgC,IAAI,GAAG,EAAE,CAAA;QAEpD,qBAAgB,GAAG,CAAC,CAAA;QAK1B,IAAI,CAAC,MAAM,GAAG;YACZ,YAAY,EAAE,EAAE;YAChB,aAAa,EAAE,GAAG;YAClB,iBAAiB,EAAE,IAAI;YACvB,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,IAAI;YACtB,GAAG,MAAM;SACV,CAAA;QAED,IAAI,CAAC,KAAK,GAAG;YACX,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,CAAC;YACf,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,CAAC;YACnB,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;SAChB,CAAA;IACH,CAAC;IAKD,YAAY,CACV,MAAc,EACd,IAAY,EACZ,IAAS,EACT,UAKI,EAAE;QAEN,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAE1C,MAAM,OAAO,GAAkB;YAC7B,EAAE,EAAE,SAAS;YACb,IAAI;YACJ,IAAI;YACJ,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,CAAC;YAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,MAAM;YACN,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,UAAU,EAAE,OAAO,CAAC,UAAU;SAC/B,CAAA;QAGD,OAAO,CAAC,GAAG,CAAC,oCAAoC,OAAO,CAAC,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,MAAM,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,gBAAgB,OAAO,CAAC,SAAS,EAAE,CAAC,CAAA;QACzL,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YACxE,OAAO,CAAC,GAAG,CAAC,qDAAqD,MAAM,EAAE,CAAC,CAAA;YAC1E,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;YACpC,OAAO,SAAS,CAAA;QAClB,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;QACpC,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAE,CAAA;QAC7C,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAGnB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAA;QAE7C,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAA;QAG1B,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;QACjC,CAAC;QAED,OAAO,SAAS,CAAA;IAClB,CAAC;IAKD,iBAAiB,CACf,MAAc,EACd,QAAgB,EAChB,UAAkB,EAClB,IAAS,EACT,WAAmB,CAAC;QAEpB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,UAAU,EAAE,EAAE;YACvD,QAAQ;YACR,GAAG,IAAI;SACR,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;IAC5B,CAAC;IAKD,oBAAoB,CAClB,MAAc,EACd,OAAY,EACZ,WAAmB,CAAC;QAEpB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAA;IAC9E,CAAC;IAKD,sBAAsB,CACpB,MAAc,EACd,WAAkB,EAClB,WAAmB,CAAC;QAEpB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,yBAAyB,EAAE;YAC1D,WAAW;YACX,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAA;IAClB,CAAC;IAKD,kBAAkB,CAChB,MAAc,EACd,QAAa,EACb,aAAqB,EACrB,WAAmB,CAAC;QAEpB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,qBAAqB,EAAE;YACtD,QAAQ;YACR,aAAa;YACb,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;SACvB,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;IACnC,CAAC;IAKD,gBAAgB,CAAC,MAAc,EAAE,QAAgB;QAE/C,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,mBAAmB,EAAE;YACpD,QAAQ;YACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAA;IACrB,CAAC;IAKO,gBAAgB,CAAC,MAAc;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAA;QAGxB,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAA;QACb,CAAC;QAGD,MAAM,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QACnE,IAAI,iBAAiB,IAAI,CAAC,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAA;QACb,CAAC;QAGD,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACjC,MAAM,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;iBAChE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;YAExC,IAAI,mBAAmB,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;gBACpD,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAKO,kBAAkB,CAAC,MAAc;QAEvC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAClD,IAAI,aAAa,EAAE,CAAC;YAClB,YAAY,CAAC,aAAa,CAAC,CAAA;QAC7B,CAAC;QAGD,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAA;QAErC,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACjC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;YAC5C,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;gBAEhF,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAA;YAClG,CAAC;QACH,CAAC;QAGD,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;YACvB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACjC,CAAC,EAAE,KAAK,CAAC,CAAA;QAET,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACrC,CAAC;IAKO,cAAc,CAAC,MAAc,EAAE,OAAsB;QAC3D,OAAO,CAAC,GAAG,CAAC,sDAAsD,MAAM,mBAAmB,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;QAC1G,MAAM,KAAK,GAAmB;YAC5B,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC/B,QAAQ,EAAE,CAAC,OAAO,CAAC;YACnB,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YAC7C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;QACD,OAAO,CAAC,GAAG,CAAC,gEAAgE,MAAM,EAAE,CAAC,CAAA;QACrF,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;QAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAKD,UAAU,CAAC,MAAc;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAM;QACR,CAAC;QAGD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC1C,IAAI,KAAK,EAAE,CAAC;YACV,YAAY,CAAC,KAAK,CAAC,CAAA;YACnB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACjC,CAAC;QAGD,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;QAC3B,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;QAGhB,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAEtD,MAAM,KAAK,GAAmB;YAC5B,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;YAC/B,QAAQ,EAAE,iBAAiB;YAC3B,SAAS,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACtF,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;QAGD,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,KAAK,CAAC,SAAS,GAAG,IAAI,EAAE,CAAC;YAC5D,KAAK,CAAC,WAAW,GAAG,MAAM,CAAA;QAC5B,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;QAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAKO,aAAa,CAAC,QAAyB;QAC7C,MAAM,SAAS,GAAoB,EAAE,CAAA;QACrC,MAAM,WAAW,GAAiC,IAAI,GAAG,EAAE,CAAA;QAE3D,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;YAE1C,IAAI,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACvC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC/B,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;gBAC/B,CAAC;gBACD,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC1C,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACzB,CAAC;QACH,CAAC;QAGD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,WAAW,EAAE,CAAC;YACvC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;gBACxC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACxB,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAA;IAC1D,CAAC;IAKO,WAAW,CAAC,OAAsB;QACxC,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,mBAAmB;gBACtB,OAAO,SAAS,OAAO,CAAC,MAAM,EAAE,CAAA;YAElC,KAAK,yBAAyB;gBAC5B,OAAO,eAAe,OAAO,CAAC,MAAM,EAAE,CAAA;YAExC,KAAK,qBAAqB;gBACxB,OAAO,SAAS,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAA;YAEtD,KAAK,sBAAsB;gBACzB,OAAO,UAAU,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAA;YAEvD;gBACE,OAAO,IAAI,CAAA;QACf,CAAC;IACH,CAAC;IAKO,QAAQ,CAAC,OAAsB;QACrC,MAAM,SAAS,GAAG;YAChB,mBAAmB;YACnB,yBAAyB;YACzB,qBAAqB;YACrB,sBAAsB;SACvB,CAAA;QAED,OAAO,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IACzC,CAAC;IAKO,aAAa,CAAC,QAAyB;QAC7C,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAC5C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QAExB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,mBAAmB;gBAEtB,OAAO,MAAM,CAAA;YAEf,KAAK,yBAAyB;gBAE5B,OAAO,MAAM,CAAA;YAEf,KAAK,qBAAqB;gBAExB,OAAO,MAAM,CAAA;YAEf,KAAK,sBAAsB;gBAEzB,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;oBAChD,GAAG,GAAG;oBACN,GAAG,GAAG,CAAC,IAAI;iBACZ,CAAC,EAAE,EAAE,CAAC,CAAA;gBAEP,OAAO;oBACL,GAAG,MAAM;oBACT,IAAI,EAAE,UAAU;iBACjB,CAAA;YAEH;gBACE,OAAO,MAAM,CAAA;QACjB,CAAC;IACH,CAAC;IAKO,oBAAoB,CAAC,OAAsB;QACjD,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM,CAAA;IACvC,CAAC;IAKO,SAAS,CAAC,MAAc,EAAE,KAAqB;QACrD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,MAAM;YACN,KAAK;SACN,CAAC,CAAA;IACJ,CAAC;IAKO,WAAW,CAAC,KAAqB;QACvC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAA;QAGzB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAA;QAC9C,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAA;QAGrE,MAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAC1D,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAChD,CAAA;QACD,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,CAAA;QACvD,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;QAG/D,MAAM,cAAc,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAA;QACjD,MAAM,aAAa,GAAG,GAAG,CAAA;QACzB,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,aAAa,CAAC,CAAA;IACxE,CAAC;IAKD,QAAQ;QACN,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC;YAC/C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;QACzB,CAAC;IACH,CAAC;IAKD,UAAU,CAAC,MAAc;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,MAAM,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QAE/C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAEjC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC1C,IAAI,KAAK,EAAE,CAAC;YACV,YAAY,CAAC,KAAK,CAAC,CAAA;YACnB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACjC,CAAC;QAED,OAAO,cAAc,CAAA;IACvB,CAAC;IAKD,QAAQ;QACN,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAA;IAC1B,CAAC;IAKD,cAAc;QAMZ,MAAM,MAAM,GAAG,EAAE,CAAA;QAEjB,KAAK,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACjD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA;gBAChE,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;gBAC1C,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAA;gBAEpE,MAAM,CAAC,IAAI,CAAC;oBACV,MAAM;oBACN,WAAW,EAAE,KAAK,CAAC,MAAM;oBACzB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,eAAe;oBAC3C,SAAS;iBACV,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAKO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAA;IACvD,CAAC;IAKO,eAAe;QACrB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA;IAC5E,CAAC;IAKD,QAAQ;QAEN,IAAI,CAAC,QAAQ,EAAE,CAAA;QAGf,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,YAAY,CAAC,KAAK,CAAC,CAAA;QACrB,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;QAC1B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;QACxB,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC3B,CAAC;CACF;AA/fD,wCA+fC"}