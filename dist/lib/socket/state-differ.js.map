{"version": 3, "file": "state-differ.js", "sourceRoot": "", "sources": ["../../../lib/socket/state-differ.ts"], "names": [], "mappings": ";;;AAWA,MAAa,WAAW;IAAxB;QACU,mBAAc,GAAqB,IAAI,GAAG,EAAE,CAAA;QAC5C,kBAAa,GAAwB,IAAI,GAAG,EAAE,CAAA;IAoLxD,CAAC;IA/KC,WAAW,CAAC,MAAc,EAAE,QAAa;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAChD,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QAGzD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAA;YACzD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;YACvC,OAAO;gBACL,KAAK,EAAE,QAAQ;gBACf,OAAO;gBACP,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;aACpD,CAAA;QACH,CAAC;QAGD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QAG3C,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAA;QACb,CAAC;QAGD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAA;QACzD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAEvC,OAAO;YACL,KAAK;YACL,OAAO;YACP,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;SACpD,CAAA;IACH,CAAC;IAKD,UAAU,CAAC,SAAc,EAAE,KAAU;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;QACxC,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAChD,CAAC;IAEO,mBAAmB,CAAC,MAAW,EAAE,KAAU;QACjD,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;YACxB,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;YAGxB,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAClE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA;gBAClB,SAAQ;YACV,CAAC;YAGD,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC3D,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAClF,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;YAC5D,CAAC;iBAAM,CAAC;gBAEN,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;YACrB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAKO,IAAI,CAAC,MAAW,EAAE,MAAW,EAAE,OAAe,EAAE;QAEtD,IAAI,MAAM,KAAK,MAAM;YAAE,OAAO,IAAI,CAAA;QAClC,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;YAAE,OAAO,MAAM,CAAA;QAGnD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC7D,OAAO,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QAC1C,CAAC;QAGD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QAC1E,CAAC;QAED,MAAM,KAAK,GAAQ,EAAE,CAAA;QACrB,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAEzE,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;YAC1B,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAG5B,IAAI,GAAG,IAAI,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC;gBACtC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAA;gBAC/B,SAAQ;YACV,CAAC;YAGD,IAAI,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAE9C,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;oBACpE,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACzE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,CAAA;oBAClE,IAAI,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACrD,KAAK,CAAC,GAAG,CAAC,GAAG,UAAU,CAAA;oBACzB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBAEN,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAA;gBACvB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;IACrD,CAAC;IAKD,eAAe,CAAC,MAAc;QAC5B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAClC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IACnC,CAAC;IAKD,UAAU,CAAC,MAAc;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAKO,SAAS,CAAC,GAAQ;QACxB,IAAI,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;YAAE,OAAO,GAAG,CAAA;QACvD,IAAI,GAAG,YAAY,IAAI;YAAE,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAA;QACvD,IAAI,GAAG,YAAY,KAAK;YAAE,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;QACtE,IAAI,GAAG,YAAY,MAAM,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAQ,EAAE,CAAA;YACtB,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;gBACtB,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC5B,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;gBACxC,CAAC;YACH,CAAC;YACD,OAAO,MAAM,CAAA;QACf,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,KAAU;QAClC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;QAC5D,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;YAC9B,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAA;YAClC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAA;QACpB,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;IAC1B,CAAC;IAKD,QAAQ;QACN,IAAI,SAAS,GAAG,CAAC,CAAA;QACjB,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC7C,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,MAAM,CAAA;QAC3C,CAAC;QACD,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI;YAC/B,SAAS;SACV,CAAA;IACH,CAAC;CACF;AAtLD,kCAsLC;AAGY,QAAA,WAAW,GAAG,IAAI,WAAW,EAAE,CAAA"}