"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketDebugMonitor = void 0;
exports.attachDebugMonitor = attachDebugMonitor;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
class SocketDebugMonitor {
    constructor(io, options = {}) {
        this.eventLog = [];
        this.startTime = Date.now();
        this.io = io;
        this.options = {
            logToFile: options.logToFile ?? false,
            logFilePath: options.logFilePath ?? path_1.default.join(process.cwd(), 'socket-debug.log'),
            consoleOutput: options.consoleOutput ?? true,
            includeData: options.includeData ?? true,
            filterEvents: options.filterEvents ?? [],
            excludeEvents: options.excludeEvents ?? [],
        };
        this.setupInterceptors();
    }
    setupInterceptors() {
        const originalOn = this.io.on.bind(this.io);
        const self = this;
        this.io.on = function (event, handler) {
            const wrappedHandler = function (...args) {
                if (event === 'connection') {
                    const socket = args[0];
                    self.interceptSocketEvents(socket);
                }
                return handler.apply(this, args);
            };
            return originalOn(event, wrappedHandler);
        };
        this.log('server', 'init', 'Socket.IO Debug Monitor initialized', null);
    }
    interceptSocketEvents(socket) {
        const originalOn = socket.on.bind(socket);
        const originalEmit = socket.emit.bind(socket);
        const self = this;
        this.log('connection', 'new', `Client connected: ${socket.id}`, null, socket.id);
        socket.on = function (event, handler) {
            const wrappedHandler = function (...args) {
                const callback = args[args.length - 1];
                const hasCallback = typeof callback === 'function';
                const data = hasCallback ? args.slice(0, -1) : args;
                self.log('receive', event, `Client -> Server`, data.length > 0 ? data[0] : null, socket.id);
                if (hasCallback) {
                    const wrappedCallback = function (response) {
                        self.log('callback', event, `Server -> Client (callback)`, response, socket.id);
                        return callback(response);
                    };
                    args[args.length - 1] = wrappedCallback;
                }
                return handler.apply(this, args);
            };
            return originalOn(event, wrappedHandler);
        };
        socket.emit = function (event, ...args) {
            self.log('emit', event, `Server -> Client`, args[0], socket.id);
            return originalEmit(event, ...args);
        };
        const originalJoin = socket.join.bind(socket);
        const originalLeave = socket.leave.bind(socket);
        socket.join = function (room) {
            self.log('room', 'join', `Socket joined room: ${room}`, null, socket.id);
            return originalJoin(room);
        };
        socket.leave = function (room) {
            self.log('room', 'leave', `Socket left room: ${room}`, null, socket.id);
            return originalLeave(room);
        };
        socket.on('disconnect', () => {
            self.log('connection', 'disconnect', `Client disconnected: ${socket.id}`, null, socket.id);
        });
    }
    log(type, event, description, data, socketId) {
        if (this.options.filterEvents.length > 0 && !this.options.filterEvents.includes(event)) {
            return;
        }
        if (this.options.excludeEvents.includes(event)) {
            return;
        }
        const timestamp = new Date().toISOString();
        const elapsed = ((Date.now() - this.startTime) / 1000).toFixed(3);
        const logEntry = {
            timestamp,
            elapsed: `${elapsed}s`,
            type,
            event,
            socketId: socketId || 'server',
            description,
            data: this.options.includeData ? data : undefined
        };
        this.eventLog.push(logEntry);
        if (this.options.consoleOutput) {
            this.printToConsole(logEntry);
        }
        if (this.options.logToFile) {
            this.writeToFile(logEntry);
        }
    }
    printToConsole(entry) {
        const colors = {
            'server': '\x1b[35m',
            'connection': '\x1b[36m',
            'receive': '\x1b[32m',
            'emit': '\x1b[34m',
            'callback': '\x1b[33m',
            'room': '\x1b[90m',
            'reset': '\x1b[0m'
        };
        const color = colors[entry.type] || '';
        const prefix = `[${entry.elapsed}]`;
        const eventInfo = `${entry.type.toUpperCase()}:${entry.event}`;
        const socket = entry.socketId !== 'server' ? `[${entry.socketId.substring(0, 8)}...]` : '';
        console.log(`${colors.room}${prefix}${colors.reset}`, `${color}${eventInfo.padEnd(20)}${colors.reset}`, socket ? `${colors.room}${socket}${colors.reset}` : '', entry.description);
        if (entry.data && this.options.includeData) {
            const dataStr = JSON.stringify(entry.data, null, 2);
            const lines = dataStr.split('\n');
            lines.forEach(line => {
                console.log(`${colors.room}  ${line}${colors.reset}`);
            });
        }
    }
    writeToFile(entry) {
        const line = JSON.stringify(entry) + '\n';
        fs_1.default.appendFileSync(this.options.logFilePath, line);
    }
    getEventLog() {
        return this.eventLog;
    }
    getEventSummary() {
        const summary = {};
        this.eventLog.forEach(entry => {
            const key = `${entry.type}:${entry.event}`;
            summary[key] = (summary[key] || 0) + 1;
        });
        return summary;
    }
    clearLog() {
        this.eventLog = [];
        if (this.options.logToFile) {
            fs_1.default.writeFileSync(this.options.logFilePath, '');
        }
    }
    exportLog(filePath) {
        fs_1.default.writeFileSync(filePath, JSON.stringify(this.eventLog, null, 2));
    }
}
exports.SocketDebugMonitor = SocketDebugMonitor;
function attachDebugMonitor(io, options) {
    return new SocketDebugMonitor(io, options);
}
//# sourceMappingURL=socket-debug-monitor.js.map