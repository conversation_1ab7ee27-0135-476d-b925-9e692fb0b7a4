{"version": 3, "file": "timing-validation.js", "sourceRoot": "", "sources": ["../../../lib/socket/timing-validation.ts"], "names": [], "mappings": ";;;AAqBA,MAAa,mBAAmB;IAAhC;QACU,oBAAe,GAAG,IAAI,GAAG,EAA0B,CAAA;QAC1C,qBAAgB,GAAG,IAAI,CAAA;QACvB,sBAAiB,GAAG,MAAM,CAAA;IAmN7C,CAAC;IA9MC,aAAa,CAAC,UAAkB,EAAE,MAAc,EAAE,QAAgB;QAChE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,MAAM,GAAmB;YAC7B,UAAU;YACV,SAAS,EAAE,GAAG;YACd,OAAO,EAAE,GAAG,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;YAChC,QAAQ;SACT,CAAA;QAED,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,UAAU,EAAE,CAAA;QACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;QAGrC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QAClC,CAAC,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAA;QAEpD,OAAO,CAAC,GAAG,CAAC,6BAA6B,UAAU,QAAQ,QAAQ,GAAG,CAAC,CAAA;QACvE,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,oBAAoB,CAClB,MAAc,EACd,UAAkB,EAClB,eAAuB;QAEvB,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,UAAU,EAAE,CAAA;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAElC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,eAAe;gBACf,SAAS,EAAE,CAAC;gBACZ,KAAK,EAAE,+BAA+B;aACvC,CAAA;QACH,CAAC;QAGD,IAAI,eAAe,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC7D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,eAAe;gBACf,SAAS,EAAE,MAAM,CAAC,QAAQ,GAAG,IAAI;gBACjC,KAAK,EAAE,uCAAuC;aAC/C,CAAA;QACH,CAAC;QAGD,IAAI,eAAe,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACvC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,eAAe;gBACf,SAAS,EAAE,CAAC;gBACZ,KAAK,EAAE,0CAA0C;aAClD,CAAA;QACH,CAAC;QAGD,MAAM,SAAS,GAAG,eAAe,GAAG,MAAM,CAAC,SAAS,CAAA;QAGpD,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,eAAe,CAAC,CAAA;QACpE,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,iDAAiD,gBAAgB,IAAI,CAAC,CAAA;QACrF,CAAC;QAGD,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,sCAAsC,SAAS,mBAAmB,UAAU,EAAE,CAAC,CAAA;QAC9F,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,eAAe;YACf,SAAS;SACV,CAAA;IACH,CAAC;IAKD,gBAAgB,CAAC,MAAc,EAAE,UAAkB;QACjD,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,UAAU,EAAE,CAAA;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAE5C,IAAI,CAAC,MAAM;YAAE,OAAO,CAAC,CAAA;QAErB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,CAAA;QAEnD,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAA;IACpC,CAAC;IAKD,WAAW,CAAC,MAAc,EAAE,UAAkB;QAC5C,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,UAAU,EAAE,CAAA;QACrC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QAChC,OAAO,CAAC,GAAG,CAAC,oCAAoC,UAAU,EAAE,CAAC,CAAA;IAC/D,CAAC;IAKD,kBAAkB,CAAC,MAAc;QAC/B,MAAM,SAAS,GAAqB,EAAE,CAAA;QAEtC,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,CAAC;gBACjC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACxB,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAA;IAClB,CAAC;IAKD,WAAW,CAAC,MAAc;QACxB,MAAM,YAAY,GAAa,EAAE,CAAA;QAEjC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC;YAC9C,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,CAAC;gBACjC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACxB,CAAC;QACH,CAAC;QAED,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;QAC7D,OAAO,CAAC,GAAG,CAAC,uBAAuB,YAAY,CAAC,MAAM,uBAAuB,MAAM,EAAE,CAAC,CAAA;IACxF,CAAC;IAKD,uBAAuB,CACrB,SAAiB,EACjB,OAAe,EACf,YAAoB,IAAI;QAGxB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,OAAO,CAAC,CAAA;QAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,GAAG,GAAG,CAAC,CAAA;QAE1D,OAAO,SAAS,GAAG,UAAU,CAAA;IAC/B,CAAC;IAKD,sBAAsB,CAAC,WAAqB;QAK1C,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,CAAA;QAC7C,CAAC;QAGD,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAA;QAC3E,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM,CAAA;QAClE,MAAM,SAAS,GAAG,WAAW,GAAG,WAAW,CAAC,MAAM,CAAA;QAElD,IAAI,OAAO,GAAG,IAAI,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YACtC,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,gCAAgC;gBACxC,UAAU,EAAE,GAAG;aAChB,CAAA;QACH,CAAC;QAGD,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;QAC5E,IAAI,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAChD,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,4BAA4B;gBACpC,UAAU,EAAE,GAAG;aAChB,CAAA;QACH,CAAC;QAGD,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;QACxD,IAAI,YAAY,GAAG,MAAM,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,gCAAgC;gBACxC,UAAU,EAAE,GAAG;aAChB,CAAA;QACH,CAAC;QAED,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,CAAA;IAC7C,CAAC;IAEO,iBAAiB,CAAC,OAAiB;QACzC,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAA;QAChE,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAA;QAChG,OAAO,QAAQ,CAAA;IACjB,CAAC;CACF;AAtND,kDAsNC;AAGY,QAAA,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAA"}