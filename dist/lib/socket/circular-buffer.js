"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeBasedCircularBuffer = exports.CircularBuffer = void 0;
class CircularBuffer {
    constructor(capacity) {
        this.writeIndex = 0;
        this.size = 0;
        if (capacity <= 0) {
            throw new Error('Capacity must be positive');
        }
        this.capacity = capacity;
        this.buffer = new Array(capacity);
    }
    push(item) {
        this.buffer[this.writeIndex] = item;
        this.writeIndex = (this.writeIndex + 1) % this.capacity;
        this.size = Math.min(this.size + 1, this.capacity);
    }
    pushMany(items) {
        items.forEach(item => this.push(item));
    }
    getAll() {
        const result = [];
        if (this.size === 0)
            return result;
        const start = this.size < this.capacity ? 0 : this.writeIndex;
        for (let i = 0; i < this.size; i++) {
            const index = (start + i) % this.capacity;
            const item = this.buffer[index];
            if (item !== undefined) {
                result.push(item);
            }
        }
        return result;
    }
    getLatest(count) {
        const all = this.getAll();
        return all.slice(-count);
    }
    filter(predicate) {
        return this.getAll().filter(predicate);
    }
    clear() {
        this.buffer = new Array(this.capacity);
        this.writeIndex = 0;
        this.size = 0;
    }
    getSize() {
        return this.size;
    }
    isFull() {
        return this.size === this.capacity;
    }
    getCapacity() {
        return this.capacity;
    }
    getMemoryUsage() {
        const itemSize = this.size > 0 ? JSON.stringify(this.buffer[0]).length : 0;
        return this.size * (itemSize + 50);
    }
}
exports.CircularBuffer = CircularBuffer;
class TimeBasedCircularBuffer extends CircularBuffer {
    constructor(capacity, ttlMs) {
        super(capacity);
        this.cleanupInterval = null;
        this.ttl = ttlMs;
        this.startCleanup();
    }
    startCleanup() {
        this.cleanupInterval = setInterval(() => {
            this.removeExpired();
        }, Math.min(this.ttl / 10, 60000));
    }
    removeExpired() {
        const now = Date.now();
        const valid = this.getAll().filter(item => now - item.timestamp < this.ttl);
        if (valid.length < this.getSize()) {
            this.clear();
            valid.forEach(item => this.push(item));
        }
    }
    getValid() {
        const now = Date.now();
        return this.getAll().filter(item => now - item.timestamp < this.ttl);
    }
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
    }
}
exports.TimeBasedCircularBuffer = TimeBasedCircularBuffer;
//# sourceMappingURL=circular-buffer.js.map