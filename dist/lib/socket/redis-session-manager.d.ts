import { EventEmitter } from 'events';
export interface RedisConfig {
    host: string;
    port: number;
    password?: string;
    db?: number;
    keyPrefix: string;
    sessionTTL: number;
    heartbeatInterval: number;
}
export interface SessionData {
    gameId: string;
    playerId: string;
    socketId: string;
    serverId: string;
    playerName: string;
    connectionTime: number;
    lastActivity: number;
    gameState: any;
}
export interface GameStateSnapshot {
    gameId: string;
    serverId: string;
    status: string;
    players: Array<{
        id: string;
        name: string;
        socketId: string;
        serverId: string;
        isConnected: boolean;
        lastActivity: number;
    }>;
    currentQuestionIndex: number;
    questions: any[];
    leaderboard: any[];
    settings: any;
    lastUpdated: number;
}
export interface ServerFailoverData {
    failedServerId: string;
    affectedGames: string[];
    affectedPlayers: string[];
    timestamp: number;
}
export declare class RedisSessionManager extends EventEmitter {
    private redis;
    private subscriber;
    private config;
    private serverId;
    private heartbeatTimer;
    private isConnected;
    private localMode;
    constructor(config?: Partial<RedisConfig>);
    private initializeRedis;
    private setupEventHandlers;
    private setupSubscriptions;
    shareGameState(gameId: string, gameState: any): Promise<void>;
    syncPlayerConnection(playerId: string, gameId: string, socketId: string, playerName: string, gameState?: any): Promise<void>;
    removePlayerConnection(playerId: string, gameId: string): Promise<void>;
    getPlayerSession(playerId: string): Promise<SessionData | null>;
    getGameState(gameId: string): Promise<GameStateSnapshot | null>;
    getGamePlayers(gameId: string): Promise<SessionData[]>;
    handleServerFailover(failedServerId: string): Promise<ServerFailoverData>;
    private startHeartbeat;
    private checkServerHealth;
    private handlePubSubMessage;
    private generateServerId;
    isRedisAvailable(): boolean;
    getMode(): 'redis' | 'local';
    getServerStats(): Promise<{
        serverId: string;
        playerCount: number;
        gameCount: number;
        uptime: number;
        memoryUsage: any;
        mode: string;
    }>;
    getActiveServers(): Promise<any[]>;
    cleanup(): Promise<void>;
    shutdown(): Promise<void>;
}
