{"version": 3, "file": "socket-debug-monitor.js", "sourceRoot": "", "sources": ["../../../lib/socket/socket-debug-monitor.ts"], "names": [], "mappings": ";;;;;;AAoNA,gDAEC;AAhND,4CAAmB;AACnB,gDAAuB;AAWvB,MAAa,kBAAkB;IAM7B,YAAY,EAAkB,EAAE,UAAwB,EAAE;QAHlD,aAAQ,GAAU,EAAE,CAAA;QACpB,cAAS,GAAW,IAAI,CAAC,GAAG,EAAE,CAAA;QAGpC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,OAAO,GAAG;YACb,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,KAAK;YACrC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,kBAAkB,CAAC;YAChF,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,IAAI;YAC5C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI;YACxC,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE;YACxC,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,EAAE;SAC3C,CAAA;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAA;IAC1B,CAAC;IAEO,iBAAiB;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAA;QAGjB,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,UAAS,KAAa,EAAE,OAAY;YAC/C,MAAM,cAAc,GAAG,UAAS,GAAG,IAAW;gBAC5C,IAAI,KAAK,KAAK,YAAY,EAAE,CAAC;oBAC3B,MAAM,MAAM,GAAW,IAAI,CAAC,CAAC,CAAC,CAAA;oBAC9B,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAA;gBACpC,CAAC;gBACD,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YAClC,CAAC,CAAA;YACD,OAAO,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;QAC1C,CAAC,CAAA;QAGD,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,qCAAqC,EAAE,IAAI,CAAC,CAAA;IACzE,CAAC;IAEO,qBAAqB,CAAC,MAAc;QAC1C,MAAM,UAAU,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACzC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC7C,MAAM,IAAI,GAAG,IAAI,CAAA;QAGjB,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,EAAE,qBAAqB,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;QAGhF,MAAM,CAAC,EAAE,GAAG,UAAS,KAAa,EAAE,OAAY;YAC9C,MAAM,cAAc,GAAG,UAAS,GAAG,IAAW;gBAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;gBACtC,MAAM,WAAW,GAAG,OAAO,QAAQ,KAAK,UAAU,CAAA;gBAClD,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;gBAEnD,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;gBAE3F,IAAI,WAAW,EAAE,CAAC;oBAEhB,MAAM,eAAe,GAAG,UAAS,QAAa;wBAC5C,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,6BAA6B,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;wBAC/E,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAA;oBAC3B,CAAC,CAAA;oBACD,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,eAAe,CAAA;gBACzC,CAAC;gBAED,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YAClC,CAAC,CAAA;YACD,OAAO,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;QAC1C,CAAC,CAAA;QAGD,MAAM,CAAC,IAAI,GAAG,UAAS,KAAa,EAAE,GAAG,IAAW;YAClD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;YAC/D,OAAO,YAAY,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAA;QACrC,CAAC,CAAA;QAGD,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC7C,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAE/C,MAAM,CAAC,IAAI,GAAG,UAAS,IAAuB;YAC5C,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,uBAAuB,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;YACxE,OAAO,YAAY,CAAC,IAAI,CAAC,CAAA;QAC3B,CAAC,CAAA;QAED,MAAM,CAAC,KAAK,GAAG,UAAS,IAAY;YAClC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,qBAAqB,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;YACvE,OAAO,aAAa,CAAC,IAAI,CAAC,CAAA;QAC5B,CAAC,CAAA;QAGD,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,wBAAwB,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;QAC5F,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,GAAG,CAAC,IAAY,EAAE,KAAa,EAAE,WAAmB,EAAE,IAAS,EAAE,QAAiB;QAExF,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACvF,OAAM;QACR,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/C,OAAM;QACR,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;QAC1C,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;QAEjE,MAAM,QAAQ,GAAG;YACf,SAAS;YACT,OAAO,EAAE,GAAG,OAAO,GAAG;YACtB,IAAI;YACJ,KAAK;YACL,QAAQ,EAAE,QAAQ,IAAI,QAAQ;YAC9B,WAAW;YACX,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;SAClD,CAAA;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAE5B,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAC/B,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAC/B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;QAC5B,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,KAAU;QAE/B,MAAM,MAAM,GAA8B;YACxC,QAAQ,EAAE,UAAU;YACpB,YAAY,EAAE,UAAU;YACxB,SAAS,EAAE,UAAU;YACrB,MAAM,EAAE,UAAU;YAClB,UAAU,EAAE,UAAU;YACtB,MAAM,EAAE,UAAU;YAClB,OAAO,EAAE,SAAS;SACnB,CAAA;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;QACtC,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,OAAO,GAAG,CAAA;QACnC,MAAM,SAAS,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE,CAAA;QAC9D,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAA;QAE1F,OAAO,CAAC,GAAG,CACT,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,EACxC,GAAG,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,EAChD,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EACtD,KAAK,CAAC,WAAW,CAClB,CAAA;QAED,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;YACnD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACjC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACnB,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAA;YACvD,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,KAAU;QAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;QACzC,YAAE,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;IACnD,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAA;IACtB,CAAC;IAEM,eAAe;QACpB,MAAM,OAAO,GAA8B,EAAE,CAAA;QAC7C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC5B,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE,CAAA;YAC1C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QACxC,CAAC,CAAC,CAAA;QACF,OAAO,OAAO,CAAA;IAChB,CAAC;IAEM,QAAQ;QACb,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;QAClB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,YAAE,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;QAChD,CAAC;IACH,CAAC;IAEM,SAAS,CAAC,QAAgB;QAC/B,YAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;IACpE,CAAC;CACF;AA/LD,gDA+LC;AAGD,SAAgB,kBAAkB,CAAC,EAAkB,EAAE,OAAsB;IAC3E,OAAO,IAAI,kBAAkB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;AAC5C,CAAC"}