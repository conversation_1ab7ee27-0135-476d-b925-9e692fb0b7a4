import { Socket } from 'socket.io';
import type { AuthUser } from '@/lib/user-context';
export interface AuthenticatedSocket extends Socket {
    user?: AuthUser;
    isAuthenticated: boolean;
    playerId?: string;
}
export declare function authenticateSocket(socket: AuthenticatedSocket, next: (err?: Error) => void): Promise<void>;
export declare function requireAuth(socket: AuthenticatedSocket, next: (err?: Error) => void): void;
export declare function requireRole(role: string): (socket: AuthenticatedSocket, next: (err?: Error) => void) => void;
export declare function generateSecurePlayerId(userId: string, gameId: string): string;
export declare function validatePlayerOwnership(socket: AuthenticatedSocket, playerId: string, gameId: string): boolean;
