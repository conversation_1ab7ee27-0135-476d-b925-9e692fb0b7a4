export declare class CircularBuffer<T> {
    private buffer;
    private writeIndex;
    private size;
    private readonly capacity;
    constructor(capacity: number);
    push(item: T): void;
    pushMany(items: T[]): void;
    getAll(): T[];
    getLatest(count: number): T[];
    filter(predicate: (item: T) => boolean): T[];
    clear(): void;
    getSize(): number;
    isFull(): boolean;
    getCapacity(): number;
    getMemoryUsage(): number;
}
export declare class TimeBasedCircularBuffer<T extends {
    timestamp: number;
}> extends CircularBuffer<T> {
    private ttl;
    private cleanupInterval;
    constructor(capacity: number, ttlMs: number);
    private startCleanup;
    private removeExpired;
    getValid(): T[];
    destroy(): void;
}
