"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResourceTracker = exports.MemoryMonitor = void 0;
class MemoryMonitor {
    constructor(config = {}, callbacks = {}) {
        this.stats = [];
        this.config = {
            checkInterval: 60000,
            heapGrowthThreshold: 20,
            warningThreshold: 500,
            criticalThreshold: 1000,
            sampleSize: 10,
            ...config
        };
        this.callbacks = callbacks;
    }
    start() {
        if (this.interval)
            return;
        this.interval = setInterval(() => {
            this.checkMemory();
        }, this.config.checkInterval);
        this.checkMemory();
    }
    stop() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = undefined;
        }
    }
    checkMemory() {
        const memUsage = process.memoryUsage();
        const stats = {
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            external: memUsage.external,
            rss: memUsage.rss,
            timestamp: Date.now()
        };
        this.stats.push(stats);
        if (this.stats.length > this.config.sampleSize) {
            this.stats.shift();
        }
        const heapUsedMB = stats.heapUsed / 1024 / 1024;
        if (heapUsedMB > this.config.criticalThreshold) {
            console.error(`CRITICAL: Memory usage ${heapUsedMB.toFixed(2)}MB exceeds critical threshold`);
            this.callbacks.onCritical?.(stats);
        }
        else if (heapUsedMB > this.config.warningThreshold) {
            console.warn(`WARNING: Memory usage ${heapUsedMB.toFixed(2)}MB exceeds warning threshold`);
            this.callbacks.onWarning?.(stats);
        }
        if (this.stats.length >= 3) {
            const growth = this.calculateGrowthRate();
            if (growth > this.config.heapGrowthThreshold) {
                console.error(`MEMORY LEAK DETECTED: Heap growing at ${growth.toFixed(2)}% rate`);
                this.callbacks.onLeak?.(growth);
            }
        }
    }
    calculateGrowthRate() {
        if (this.stats.length < 2)
            return 0;
        const first = this.stats[0];
        const last = this.stats[this.stats.length - 1];
        const growthBytes = last.heapUsed - first.heapUsed;
        const growthPercentage = (growthBytes / first.heapUsed) * 100;
        return growthPercentage;
    }
    getStats() {
        return [...this.stats];
    }
    getCurrentStats() {
        return this.stats[this.stats.length - 1];
    }
    getReport() {
        const current = this.getCurrentStats();
        const heapUsages = this.stats.map(s => s.heapUsed);
        return {
            current,
            average: heapUsages.reduce((a, b) => a + b, 0) / heapUsages.length,
            peak: Math.max(...heapUsages),
            growthRate: this.calculateGrowthRate()
        };
    }
    forceGC() {
        if (global.gc) {
            console.log('Forcing garbage collection...');
            global.gc();
        }
        else {
            console.warn('Garbage collection not exposed. Run with --expose-gc flag');
        }
    }
}
exports.MemoryMonitor = MemoryMonitor;
class ResourceTracker {
    constructor() {
        this.resources = new Map();
    }
    track(id, type, metadata) {
        this.resources.set(id, {
            type,
            created: Date.now(),
            metadata
        });
    }
    untrack(id) {
        return this.resources.delete(id);
    }
    getResourceCount() {
        return this.resources.size;
    }
    getResourcesByType(type) {
        const results = [];
        for (const [id, resource] of this.resources) {
            if (resource.type === type) {
                results.push(id);
            }
        }
        return results;
    }
    getOldestResources(count = 10) {
        const now = Date.now();
        const sorted = Array.from(this.resources.entries())
            .map(([id, resource]) => ({
            id,
            type: resource.type,
            age: now - resource.created,
            metadata: resource.metadata
        }))
            .sort((a, b) => b.age - a.age)
            .slice(0, count);
        return sorted;
    }
    clear() {
        this.resources.clear();
    }
    getReport() {
        const byType = {};
        for (const resource of this.resources.values()) {
            byType[resource.type] = (byType[resource.type] || 0) + 1;
        }
        return {
            total: this.resources.size,
            byType,
            oldest: this.getOldestResources(5)
        };
    }
}
exports.ResourceTracker = ResourceTracker;
//# sourceMappingURL=memory-monitor.js.map