{"version": 3, "file": "atomic-game-state.js", "sourceRoot": "", "sources": ["../../../lib/socket/atomic-game-state.ts"], "names": [], "mappings": ";;;AAAA,+BAAmC;AA8BnC,MAAa,sBAAsB;IAAnC;QACU,UAAK,GAAG,IAAI,GAAG,EAAyB,CAAA;QACxC,cAAS,GAAG,IAAI,GAAG,EAA6B,CAAA;QACvC,iBAAY,GAAG,KAAK,CAAA;QACpB,kBAAa,GAAG,EAAE,CAAA;IAuTrC,CAAC;IAlTC,KAAK,CAAC,WAAW,CAAC,MAAc,EAAE,SAAiB;QACjD,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAG3C,IAAI,YAAY,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5E,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,4BAA4B,YAAY,CAAC,SAAS,EAAE,CAAC,CAAA;YAC7F,OAAO,IAAI,CAAA;QACb,CAAC;QAGD,MAAM,MAAM,GAAG,IAAA,SAAM,GAAE,CAAA;QACvB,MAAM,IAAI,GAAkB;YAC1B,MAAM;YACN,MAAM;YACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,SAAS;SACV,CAAA;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC5B,OAAO,CAAC,GAAG,CAAC,+BAA+B,MAAM,aAAa,MAAM,eAAe,SAAS,EAAE,CAAC,CAAA;QAG/F,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;YAC1C,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBACjD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;gBAChC,OAAO,CAAC,IAAI,CAAC,4CAA4C,MAAM,aAAa,MAAM,EAAE,CAAC,CAAA;YACvF,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;QAErB,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,WAAW,CAAC,MAAc,EAAE,MAAc;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAEnC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,mDAAmD,MAAM,aAAa,MAAM,EAAE,CAAC,CAAA;YAC5F,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACzB,OAAO,CAAC,GAAG,CAAC,+BAA+B,MAAM,aAAa,MAAM,EAAE,CAAC,CAAA;QACvE,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,cAAc,CAAC,MAAc,EAAE,SAAc;QAC3C,MAAM,QAAQ,GAAsB;YAClC,MAAM;YACN,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC5C,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;YACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;QAED,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAA;QAC3C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;QAGjC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAE9B,OAAO,CAAC,GAAG,CAAC,2CAA2C,MAAM,EAAE,CAAC,CAAA;IAClE,CAAC;IAKD,eAAe,CAAC,MAAc,EAAE,OAAgB;QAC9C,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,OAAO,EAAE,CAAA;YAClC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAA;QACxC,CAAC;QAGD,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;aACtD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC;aAChC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAA;QAExC,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;IACjC,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,MAAc,EACd,SAAiB,EACjB,SAAc,EACd,UAAgC,EAChC,cAAgD;QAIhD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;QACxD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0CAA0C,EAAE,CAAA;QAC9E,CAAC;QAED,IAAI,CAAC;YAEH,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;YAGtC,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAA;YACpD,MAAM,kBAAkB,GAAmB,EAAE,CAAA;YAG7C,KAAK,MAAM,EAAE,IAAI,UAAU,EAAE,CAAC;gBAC5B,IAAI,CAAC;oBACH,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;wBAChB,KAAK,YAAY;4BACf,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;4BAC9B,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE;gCAC3B,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gCACzE,IAAI,KAAK,IAAI,CAAC;oCAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;4BACnD,CAAC,CAAC,CAAA;4BACF,MAAK;wBAEP,KAAK,eAAe;4BAClB,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;4BAC/E,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;gCACrB,MAAM,SAAS,GAAG,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAA;gCACtD,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAA;gCAChF,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE;oCAC3B,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,SAAS,CAAA;gCAC3C,CAAC,CAAC,CAAA;4BACJ,CAAC;4BACD,MAAK;wBAEP,KAAK,eAAe;4BAClB,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;4BAC/E,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;gCACrB,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;gCACnD,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAA;gCACvC,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE;oCAC3B,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,aAAa,CAAC,CAAA;gCACxD,CAAC,CAAC,CAAA;4BACJ,CAAC;4BACD,MAAK;wBAEP,KAAK,cAAc;4BACjB,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;4BAC1F,IAAI,gBAAgB,IAAI,CAAC,EAAE,CAAC;gCAC1B,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAA;gCACzD,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAA;gCAC3D,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE;oCAC3B,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAA;gCACrD,CAAC,CAAC,CAAA;4BACJ,CAAC;4BACD,MAAK;wBAEP,KAAK,gBAAgB;4BACnB,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAA;4BACvC,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAA;4BACrC,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE;gCAC3B,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAA;4BACnC,CAAC,CAAC,CAAA;4BACF,MAAK;wBAEP,KAAK,iBAAiB;4BACpB,MAAM,WAAW,GAAG,QAAQ,CAAC,eAAe,CAAA;4BAC5C,QAAQ,CAAC,eAAe,GAAG,EAAE,CAAC,IAAI,CAAA;4BAClC,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE;gCAC3B,QAAQ,CAAC,eAAe,GAAG,WAAW,CAAA;4BACxC,CAAC,CAAC,CAAA;4BACF,MAAK;wBAEP,KAAK,UAAU;4BACb,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAA;4BACrC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;4BAC5B,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE;gCAC3B,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gCAC3E,IAAI,SAAS,IAAI,CAAC;oCAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;4BACzD,CAAC,CAAC,CAAA;4BACF,MAAK;wBAEP,KAAK,aAAa;4BAChB,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;4BAClF,IAAI,eAAe,IAAI,CAAC,EAAE,CAAC;gCACzB,MAAM,OAAO,GAAG,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAA;gCACtD,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAA;gCACpF,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE;oCAC3B,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,OAAO,CAAA;gCAC3C,CAAC,CAAC,CAAA;4BACJ,CAAC;4BACD,MAAK;wBAEP;4BACE,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;gCAChB,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAA;4BACtC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAA;oBAGpE,kBAAkB,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;wBAC9C,IAAI,CAAC;4BACH,QAAQ,EAAE,CAAA;wBACZ,CAAC;wBAAC,OAAO,aAAa,EAAE,CAAC;4BACvB,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,aAAa,CAAC,CAAA;wBAC1E,CAAC;oBACH,CAAC,CAAC,CAAA;oBAEF,MAAM,KAAK,CAAA;gBACb,CAAC;YACH,CAAC;YAGD,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAA;YAG9B,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAEhC,OAAO,CAAC,GAAG,CAAC,yDAAyD,SAAS,EAAE,CAAC,CAAA;YACjF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAA;QAEpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kDAAkD,MAAM,GAAG,EAAE,KAAK,CAAC,CAAA;YAGjF,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;YAC7C,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;gBACpC,OAAO,CAAC,GAAG,CAAC,+BAA+B,MAAM,gBAAgB,CAAC,CAAA;YACpE,CAAC;YAGD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAEhC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAA;QAC5F,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,MAAc;QACtC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;aACvD,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;aAC/C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAA;QAGhD,IAAI,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9C,MAAM,QAAQ,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YACxD,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE;gBACzB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAC5B,CAAC,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,yBAAyB,QAAQ,CAAC,MAAM,2BAA2B,MAAM,EAAE,CAAC,CAAA;QAC1F,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,MAAc;QAExB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAGzB,MAAM,YAAY,GAAa,EAAE,CAAA;QACjC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC;YACxC,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,CAAC;gBACjC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACxB,CAAC;QACH,CAAC;QAED,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;QAEvD,OAAO,CAAC,GAAG,CAAC,uDAAuD,MAAM,EAAE,CAAC,CAAA;IAC9E,CAAC;IAKD,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAA;IACvC,CAAC;IAKD,iBAAiB;QACf,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,YAAY,GAAa,EAAE,CAAA;QAEjC,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAClD,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC7C,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC3B,CAAC;QACH,CAAC;QAED,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAC3B,CAAC,CAAC,CAAA;QAEF,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,yBAAyB,YAAY,CAAC,MAAM,gBAAgB,CAAC,CAAA;QAC3E,CAAC;IACH,CAAC;CACF;AA3TD,wDA2TC;AAGY,QAAA,sBAAsB,GAAG,IAAI,sBAAsB,EAAE,CAAA;AAGlE,WAAW,CAAC,GAAG,EAAE;IACf,8BAAsB,CAAC,iBAAiB,EAAE,CAAA;AAC5C,CAAC,EAAE,KAAK,CAAC,CAAA"}