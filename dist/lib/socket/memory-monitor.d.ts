export interface MemoryStats {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
    timestamp: number;
}
export interface LeakDetectionConfig {
    checkInterval: number;
    heapGrowthThreshold: number;
    warningThreshold: number;
    criticalThreshold: number;
    sampleSize: number;
}
export declare class MemoryMonitor {
    private stats;
    private interval?;
    private config;
    private callbacks;
    constructor(config?: Partial<LeakDetectionConfig>, callbacks?: MemoryMonitor['callbacks']);
    start(): void;
    stop(): void;
    private checkMemory;
    private calculateGrowthRate;
    getStats(): MemoryStats[];
    getCurrentStats(): MemoryStats | undefined;
    getReport(): {
        current: MemoryStats | undefined;
        average: number;
        peak: number;
        growthRate: number;
    };
    forceGC(): void;
}
export declare class ResourceTracker {
    private resources;
    track(id: string, type: string, metadata?: any): void;
    untrack(id: string): boolean;
    getResourceCount(): number;
    getResourcesByType(type: string): string[];
    getOldestResources(count?: number): Array<{
        id: string;
        type: string;
        age: number;
        metadata?: any;
    }>;
    clear(): void;
    getReport(): {
        total: number;
        byType: Record<string, number>;
        oldest: Array<{
            id: string;
            type: string;
            age: number;
        }>;
    };
}
