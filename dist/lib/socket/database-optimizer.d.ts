import { PrismaClient } from '@prisma/client';
interface QueryStats {
    totalQueries: number;
    averageTime: number;
    slowQueries: number;
    cacheHits: number;
    cacheMisses: number;
}
export declare class DatabaseOptimizer {
    private pool;
    private cache;
    private queryStats;
    private readonly SLOW_QUERY_THRESHOLD;
    private readonly MAX_CACHE_SIZE;
    private readonly DEFAULT_TTL;
    constructor(maxConnections?: number);
    private initializePool;
    private getClient;
    private releaseClient;
    executeQuery<T>(cacheKey: string, queryFn: (client: PrismaClient) => Promise<T>, ttl?: number): Promise<T>;
    executeWrite<T>(queryFn: (client: PrismaClient) => Promise<T>, invalidateKeys?: string[]): Promise<T>;
    executeBatch<T>(operations: Array<{
        cacheKey?: string;
        queryFn: (client: PrismaClient) => Promise<T>;
        ttl?: number;
    }>): Promise<T[]>;
    getGameState(gameId: string): Promise<any>;
    updatePlayerScore(playerId: string, gameId: string, newScore: number): Promise<void>;
    saveAnswer(answerData: {
        playerId: string;
        gameId: string;
        questionId: string;
        answer: string;
        isCorrect: boolean;
        timeTaken: number;
        points: number;
    }): Promise<void>;
    getLeaderboard(gameId: string): Promise<any[]>;
    createGameSession(gameData: {
        id: string;
        hostId: string;
        gameMode: string;
        settings: any;
    }): Promise<void>;
    addPlayerToGame(playerId: string, gameId: string, isHost?: boolean): Promise<void>;
    private getFromCache;
    private setCache;
    private invalidateCache;
    private evictOldestCacheEntries;
    private updateQueryStats;
    private startCacheCleanup;
    private cleanExpiredCache;
    private logStats;
    getStats(): {
        queries: QueryStats;
        cache: {
            size: number;
            hitRate: number;
        };
        connections: {
            active: number;
            queued: number;
            max: number;
        };
    };
    cleanup(): Promise<void>;
}
export declare const databaseOptimizer: DatabaseOptimizer;
export {};
