export interface QuestionTiming {
    questionId: string;
    startTime: number;
    endTime: number;
    duration: number;
}
export interface TimingValidationResult {
    isValid: boolean;
    serverTimestamp: number;
    timeTaken: number;
    error?: string;
}
export declare class ServerTimingManager {
    private activeQuestions;
    private readonly TIMING_TOLERANCE;
    private readonly MAX_QUESTION_TIME;
    startQuestion(questionId: string, gameId: string, duration: number): QuestionTiming;
    validateAnswerTiming(gameId: string, questionId: string, clientTimestamp: number): TimingValidationResult;
    getRemainingTime(gameId: string, questionId: string): number;
    endQuestion(gameId: string, questionId: string): void;
    getActiveQuestions(gameId: string): QuestionTiming[];
    cleanupGame(gameId: string): void;
    calculateTimeBasedScore(timeTaken: number, maxTime: number, baseScore?: number): number;
    detectCheatingPatterns(answerTimes: number[]): {
        suspicious: boolean;
        reason?: string;
        confidence: number;
    };
    private calculateVariance;
}
export declare const serverTimingManager: ServerTimingManager;
