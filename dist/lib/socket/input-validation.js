"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.socketRateLimiter = exports.rateLimits = exports.schemas = void 0;
exports.sanitizeText = sanitizeText;
exports.validateSocketData = validateSocketData;
const zod_1 = require("zod");
let DOMPurify = null;
if (typeof window !== 'undefined') {
    try {
        DOMPurify = require('isomorphic-dompurify');
    }
    catch (e) {
        console.warn('[InputValidation] DOMPurify not available on client');
    }
}
else {
    try {
        DOMPurify = require('isomorphic-dompurify');
    }
    catch (e) {
        console.warn('[InputValidation] DOMPurify not available on server');
    }
}
exports.schemas = {
    createGame: zod_1.z.object({
        gameMode: zod_1.z.enum(['classic', 'guess-the-year', 'album-challenge', 'artist-focus']),
        teamMode: zod_1.z.boolean().default(false),
        maxTeamSize: zod_1.z.number().min(1).max(10).default(4),
        questionCount: zod_1.z.number().min(1).max(50).default(10),
        questionTime: zod_1.z.number().min(5).max(60).default(30),
        gameName: zod_1.z.string().min(1).max(100).optional()
    }),
    joinGame: zod_1.z.object({
        gameCode: zod_1.z.string().min(4).max(8).regex(/^[A-Z0-9]+$/),
        playerName: zod_1.z.string().min(1).max(50)
    }),
    submitAnswer: zod_1.z.object({
        questionId: zod_1.z.string().uuid(),
        answer: zod_1.z.string().min(1).max(500),
        answerIndex: zod_1.z.number().min(0).max(10),
        clientTimestamp: zod_1.z.number().positive()
    }),
    createTeam: zod_1.z.object({
        teamName: zod_1.z.string().min(1).max(50),
        color: zod_1.z.string().regex(/^#[0-9A-F]{6}$/i).optional()
    }),
    joinTeam: zod_1.z.object({
        teamId: zod_1.z.string().uuid()
    }),
    teamChat: zod_1.z.object({
        message: zod_1.z.string().min(1).max(500),
        teamId: zod_1.z.string().uuid()
    }),
    updateGameSettings: zod_1.z.object({
        questionTime: zod_1.z.number().min(5).max(60).optional(),
        questionCount: zod_1.z.number().min(1).max(50).optional(),
        teamMode: zod_1.z.boolean().optional(),
        maxTeamSize: zod_1.z.number().min(1).max(10).optional()
    }),
    reconnectToGame: zod_1.z.object({
        gameId: zod_1.z.string().uuid(),
        lastKnownState: zod_1.z.string().optional()
    })
};
function sanitizeText(input) {
    if (typeof input !== 'string')
        return '';
    let sanitized = input;
    if (DOMPurify && DOMPurify.sanitize) {
        try {
            sanitized = DOMPurify.sanitize(input, {
                ALLOWED_TAGS: [],
                ALLOWED_ATTR: []
            });
        }
        catch (error) {
            console.warn('[InputValidation] DOMPurify sanitization failed:', error);
        }
    }
    sanitized = sanitized
        .trim()
        .replace(/[<>'"&]/g, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+=/gi, '')
        .substring(0, 1000);
    return sanitized;
}
function validateSocketData(schema, data) {
    try {
        const result = schema.safeParse(data);
        if (!result.success) {
            const errorMessage = result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ');
            return { success: false, error: `Validation failed: ${errorMessage}` };
        }
        const sanitizedData = sanitizeObjectStrings(result.data);
        return { success: true, data: sanitizedData };
    }
    catch (error) {
        return { success: false, error: 'Invalid data format' };
    }
}
function sanitizeObjectStrings(obj) {
    if (typeof obj === 'string') {
        return sanitizeText(obj);
    }
    if (Array.isArray(obj)) {
        return obj.map(sanitizeObjectStrings);
    }
    if (obj && typeof obj === 'object') {
        const sanitized = {};
        for (const [key, value] of Object.entries(obj)) {
            sanitized[key] = sanitizeObjectStrings(value);
        }
        return sanitized;
    }
    return obj;
}
exports.rateLimits = {
    'create-game': { window: 60000, max: 5 },
    'join-game': { window: 60000, max: 10 },
    'start-game': { window: 60000, max: 3 },
    'submit-answer': { window: 1000, max: 2 },
    'skip-question': { window: 30000, max: 5 },
    'create-team': { window: 60000, max: 5 },
    'join-team': { window: 30000, max: 10 },
    'leave-team': { window: 30000, max: 10 },
    'team-chat': { window: 60000, max: 30 },
    'reconnect_to_game': { window: 60000, max: 20 },
    'default': { window: 60000, max: 60 }
};
class SocketRateLimiter {
    constructor() {
        this.limits = new Map();
    }
    check(socketId, eventType) {
        const now = Date.now();
        const limit = exports.rateLimits[eventType] || exports.rateLimits.default;
        if (!this.limits.has(socketId)) {
            this.limits.set(socketId, new Map());
        }
        const socketLimits = this.limits.get(socketId);
        if (!socketLimits.has(eventType)) {
            socketLimits.set(eventType, []);
        }
        const events = socketLimits.get(eventType);
        const cutoff = now - limit.window;
        const validEvents = events.filter(time => time > cutoff);
        if (validEvents.length >= limit.max) {
            return false;
        }
        validEvents.push(now);
        socketLimits.set(eventType, validEvents);
        return true;
    }
    cleanup(socketId) {
        this.limits.delete(socketId);
    }
    periodicCleanup() {
        const now = Date.now();
        const maxWindow = Math.max(...Object.values(exports.rateLimits).map(l => l.window));
        for (const [socketId, socketLimits] of this.limits.entries()) {
            for (const [eventType, events] of socketLimits.entries()) {
                const cutoff = now - maxWindow;
                const validEvents = events.filter(time => time > cutoff);
                if (validEvents.length === 0) {
                    socketLimits.delete(eventType);
                }
                else {
                    socketLimits.set(eventType, validEvents);
                }
            }
            if (socketLimits.size === 0) {
                this.limits.delete(socketId);
            }
        }
    }
}
exports.socketRateLimiter = new SocketRateLimiter();
setInterval(() => {
    exports.socketRateLimiter.periodicCleanup();
}, 5 * 60 * 1000);
//# sourceMappingURL=input-validation.js.map