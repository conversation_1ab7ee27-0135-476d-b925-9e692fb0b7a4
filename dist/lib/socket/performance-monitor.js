"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceMonitor = void 0;
const events_1 = require("events");
const circular_buffer_1 = require("./circular-buffer");
class PerformanceMonitor extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.metricsHistory = new Map();
        this.gameStartTimes = new Map();
        this.alerts = new Map();
        this.monitorInterval = null;
        this.gcObserver = null;
        this.config = {
            sampleInterval: 1000,
            metricsWindow: 60000,
            alertThresholds: {
                maxLatency: 1000,
                maxErrorRate: 0.05,
                maxCpuUsage: 80,
                maxMemoryUsage: 90,
                minGameCompletionRate: 0.8
            },
            ...config
        };
        this.latencyBuffer = new circular_buffer_1.CircularBuffer(PerformanceMonitor.LATENCY_BUFFER_SIZE);
        this.connectionEvents = new circular_buffer_1.TimeBasedCircularBuffer(PerformanceMonitor.EVENT_BUFFER_SIZE, PerformanceMonitor.EVENT_TTL);
        this.disconnectionEvents = new circular_buffer_1.TimeBasedCircularBuffer(PerformanceMonitor.EVENT_BUFFER_SIZE, PerformanceMonitor.EVENT_TTL);
        this.messageEvents = new circular_buffer_1.TimeBasedCircularBuffer(PerformanceMonitor.EVENT_BUFFER_SIZE, PerformanceMonitor.EVENT_TTL);
        this.errorEvents = new circular_buffer_1.TimeBasedCircularBuffer(PerformanceMonitor.EVENT_BUFFER_SIZE, PerformanceMonitor.EVENT_TTL);
        this.metrics = this.initializeMetrics();
        this.setupGCMonitoring();
        this.startMonitoring();
    }
    initializeMetrics() {
        return {
            connectionsPerSecond: 0,
            disconnectionsPerSecond: 0,
            averageLatency: 0,
            p95Latency: 0,
            p99Latency: 0,
            reconnectionRate: 0,
            messageRate: 0,
            errorRate: 0,
            gameMetrics: {
                activeGames: 0,
                totalPlayers: 0,
                gamesPerHour: 0,
                averageGameDuration: 0,
                gameCompletionRate: 0,
                questionsPerSecond: 0,
                averagePlayersPerGame: 0
            },
            resourceMetrics: {
                cpuUsage: 0,
                memoryUsage: 0,
                eventLoopDelay: 0,
                gcFrequency: 0,
                networkBandwidth: 0
            }
        };
    }
    recordConnection() {
        this.connectionEvents.push({ timestamp: Date.now() });
    }
    recordDisconnection() {
        this.disconnectionEvents.push({ timestamp: Date.now() });
    }
    recordLatency(latency) {
        this.latencyBuffer.push(latency);
    }
    recordMessage() {
        this.messageEvents.push({ timestamp: Date.now() });
    }
    recordError(error) {
        this.errorEvents.push({
            timestamp: Date.now(),
            error: error?.message || error
        });
        console.error('Socket error recorded:', error);
    }
    recordGameStart(gameId) {
        this.gameStartTimes.set(gameId, Date.now());
    }
    recordGameCompletion(gameId) {
        const startTime = this.gameStartTimes.get(gameId);
        if (startTime) {
            const duration = Date.now() - startTime;
            this.emit('game_completed', { gameId, duration });
            this.gameStartTimes.delete(gameId);
        }
    }
    updateGameMetrics(metrics) {
        Object.assign(this.metrics.gameMetrics, metrics);
    }
    startMonitoring() {
        this.monitorInterval = setInterval(() => {
            this.calculateMetrics();
            this.checkAlerts();
            this.pruneOldData();
        }, this.config.sampleInterval);
        this.monitorEventLoop();
    }
    calculateMetrics() {
        const now = Date.now();
        const windowStart = now - this.config.metricsWindow;
        const recentConnections = this.connectionEvents.getValid()
            .filter(e => e.timestamp > windowStart);
        const recentDisconnections = this.disconnectionEvents.getValid()
            .filter(e => e.timestamp > windowStart);
        this.metrics.connectionsPerSecond = recentConnections.length / (this.config.metricsWindow / 1000);
        this.metrics.disconnectionsPerSecond = recentDisconnections.length / (this.config.metricsWindow / 1000);
        const reconnections = Math.min(recentConnections.length, recentDisconnections.length);
        this.metrics.reconnectionRate = reconnections / Math.max(recentConnections.length, 1);
        const recentMessages = this.messageEvents.getValid()
            .filter(e => e.timestamp > windowStart);
        this.metrics.messageRate = recentMessages.length / (this.config.metricsWindow / 1000);
        const recentErrors = this.errorEvents.getValid()
            .filter(e => e.timestamp > windowStart);
        this.metrics.errorRate = recentErrors.length / Math.max(recentMessages.length, 1);
        const latencies = this.latencyBuffer.getAll();
        if (latencies.length > 0) {
            const sorted = [...latencies].sort((a, b) => a - b);
            this.metrics.averageLatency = sorted.reduce((a, b) => a + b, 0) / sorted.length;
            this.metrics.p95Latency = sorted[Math.floor(sorted.length * 0.95)] || 0;
            this.metrics.p99Latency = sorted[Math.floor(sorted.length * 0.99)] || 0;
        }
        this.updateResourceMetrics();
        this.metricsHistory.set(now, { ...this.metrics });
        this.emit('metrics_updated', this.metrics);
    }
    updateResourceMetrics() {
        const usage = process.cpuUsage();
        const mem = process.memoryUsage();
        const cpuPercent = (usage.user + usage.system) / 1000000;
        this.metrics.resourceMetrics.cpuUsage = Math.min(cpuPercent, 100);
        const totalMem = require('os').totalmem();
        this.metrics.resourceMetrics.memoryUsage = (mem.heapUsed / totalMem) * 100;
    }
    monitorEventLoop() {
        let lastCheck = Date.now();
        setInterval(() => {
            const now = Date.now();
            const delay = now - lastCheck - 100;
            if (delay > 0) {
                this.metrics.resourceMetrics.eventLoopDelay = delay;
            }
            lastCheck = now;
        }, 100);
    }
    setupGCMonitoring() {
        if (typeof PerformanceObserver !== 'undefined') {
            try {
                let gcCount = 0;
                const resetInterval = 60000;
                this.gcObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    gcCount += entries.length;
                });
                this.gcObserver.observe({ entryTypes: ['gc'] });
                setInterval(() => {
                    this.metrics.resourceMetrics.gcFrequency = gcCount;
                    gcCount = 0;
                }, resetInterval);
            }
            catch (error) {
                console.warn('GC monitoring not available:', error);
            }
        }
    }
    checkAlerts() {
        const { alertThresholds } = this.config;
        if (this.metrics.averageLatency > alertThresholds.maxLatency) {
            this.createAlert('high_latency', 'warning', 'averageLatency', this.metrics.averageLatency, alertThresholds.maxLatency, `Average latency (${Math.round(this.metrics.averageLatency)}ms) exceeds threshold`);
        }
        if (this.metrics.errorRate > alertThresholds.maxErrorRate) {
            this.createAlert('high_error_rate', 'critical', 'errorRate', this.metrics.errorRate, alertThresholds.maxErrorRate, `Error rate (${(this.metrics.errorRate * 100).toFixed(2)}%) exceeds threshold`);
        }
        if (this.metrics.resourceMetrics.cpuUsage > alertThresholds.maxCpuUsage) {
            this.createAlert('high_cpu', 'warning', 'cpuUsage', this.metrics.resourceMetrics.cpuUsage, alertThresholds.maxCpuUsage, `CPU usage (${Math.round(this.metrics.resourceMetrics.cpuUsage)}%) exceeds threshold`);
        }
        if (this.metrics.resourceMetrics.memoryUsage > alertThresholds.maxMemoryUsage) {
            this.createAlert('high_memory', 'critical', 'memoryUsage', this.metrics.resourceMetrics.memoryUsage, alertThresholds.maxMemoryUsage, `Memory usage (${Math.round(this.metrics.resourceMetrics.memoryUsage)}%) exceeds threshold`);
        }
        const totalGames = this.metrics.gameMetrics.totalGames || 0;
        if (process.env.NODE_ENV === 'development' &&
            totalGames >= 5 &&
            this.metrics.gameMetrics.gameCompletionRate < alertThresholds.minGameCompletionRate) {
            this.createAlert('low_completion_rate', 'warning', 'gameCompletionRate', this.metrics.gameMetrics.gameCompletionRate, alertThresholds.minGameCompletionRate, `Game completion rate (${(this.metrics.gameMetrics.gameCompletionRate * 100).toFixed(2)}%) below threshold (${totalGames} games)`);
        }
    }
    createAlert(id, type, metric, value, threshold, message) {
        const alert = {
            id,
            type,
            metric,
            value,
            threshold,
            message,
            timestamp: Date.now()
        };
        const existingAlert = this.alerts.get(id);
        const suppressionTime = process.env.NODE_ENV === 'development' ? 60000 : 600000;
        if (!existingAlert || existingAlert.timestamp < Date.now() - suppressionTime) {
            this.alerts.set(id, alert);
            this.emit('alert_triggered', alert);
        }
    }
    pruneOldData() {
        const cutoff = Date.now() - this.config.metricsWindow * 10;
        for (const [timestamp] of this.metricsHistory) {
            if (timestamp < cutoff) {
                this.metricsHistory.delete(timestamp);
            }
        }
        const alertCutoff = Date.now() - 3600000;
        for (const [id, alert] of this.alerts) {
            if (alert.timestamp < alertCutoff) {
                this.alerts.delete(id);
            }
        }
        for (const [gameId, startTime] of this.gameStartTimes) {
            if (startTime < cutoff) {
                this.gameStartTimes.delete(gameId);
            }
        }
    }
    getMetrics() {
        return { ...this.metrics };
    }
    getMetricsHistory(duration) {
        const cutoff = Date.now() - duration;
        const history = [];
        for (const [timestamp, metrics] of this.metricsHistory) {
            if (timestamp > cutoff) {
                history.push(metrics);
            }
        }
        return history.sort((a, b) => a.timestamp - b.timestamp);
    }
    getActiveAlerts() {
        return Array.from(this.alerts.values());
    }
    generateReport() {
        const report = [];
        const m = this.metrics;
        report.push('=== Socket Performance Report ===');
        report.push(`Generated: ${new Date().toISOString()}`);
        report.push('');
        report.push('Connection Metrics:');
        report.push(`  Connections/sec: ${m.connectionsPerSecond.toFixed(2)}`);
        report.push(`  Disconnections/sec: ${m.disconnectionsPerSecond.toFixed(2)}`);
        report.push(`  Reconnection Rate: ${(m.reconnectionRate * 100).toFixed(2)}%`);
        report.push('');
        report.push('Performance Metrics:');
        report.push(`  Average Latency: ${Math.round(m.averageLatency)}ms`);
        report.push(`  P95 Latency: ${Math.round(m.p95Latency)}ms`);
        report.push(`  P99 Latency: ${Math.round(m.p99Latency)}ms`);
        report.push(`  Message Rate: ${m.messageRate.toFixed(2)}/sec`);
        report.push(`  Error Rate: ${(m.errorRate * 100).toFixed(2)}%`);
        report.push('');
        report.push('Game Metrics:');
        report.push(`  Active Games: ${m.gameMetrics.activeGames}`);
        report.push(`  Total Players: ${m.gameMetrics.totalPlayers}`);
        report.push(`  Games/hour: ${m.gameMetrics.gamesPerHour.toFixed(2)}`);
        report.push(`  Avg Game Duration: ${Math.round(m.gameMetrics.averageGameDuration / 60000)} min`);
        report.push(`  Completion Rate: ${(m.gameMetrics.gameCompletionRate * 100).toFixed(2)}%`);
        report.push('');
        report.push('Resource Metrics:');
        report.push(`  CPU Usage: ${Math.round(m.resourceMetrics.cpuUsage)}%`);
        report.push(`  Memory Usage: ${Math.round(m.resourceMetrics.memoryUsage)}%`);
        report.push(`  Event Loop Delay: ${Math.round(m.resourceMetrics.eventLoopDelay)}ms`);
        report.push(`  GC Frequency: ${m.resourceMetrics.gcFrequency}/min`);
        report.push('');
        if (this.alerts.size > 0) {
            report.push('Active Alerts:');
            for (const alert of this.alerts.values()) {
                report.push(`  [${alert.type.toUpperCase()}] ${alert.message}`);
            }
        }
        return report.join('\n');
    }
    shutdown() {
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
        }
        if (this.gcObserver) {
            this.gcObserver.disconnect();
        }
        this.connectionEvents.destroy();
        this.disconnectionEvents.destroy();
        this.messageEvents.destroy();
        this.errorEvents.destroy();
        this.latencyBuffer.clear();
        this.gameStartTimes.clear();
        this.alerts.clear();
        this.metricsHistory.clear();
        this.removeAllListeners();
    }
    getMemoryStats() {
        return {
            latencyBufferSize: this.latencyBuffer.getSize(),
            connectionEventsSize: this.connectionEvents.getSize(),
            disconnectionEventsSize: this.disconnectionEvents.getSize(),
            messageEventsSize: this.messageEvents.getSize(),
            errorEventsSize: this.errorEvents.getSize(),
            gameStartTimesCount: this.gameStartTimes.size,
            alertsCount: this.alerts.size,
            metricsHistoryCount: this.metricsHistory.size,
            estimatedMemoryUsage: {
                latencyBuffer: this.latencyBuffer.getMemoryUsage(),
                events: (this.connectionEvents.getMemoryUsage() +
                    this.disconnectionEvents.getMemoryUsage() +
                    this.messageEvents.getMemoryUsage() +
                    this.errorEvents.getMemoryUsage()),
                total: 'See above components'
            }
        };
    }
}
exports.PerformanceMonitor = PerformanceMonitor;
PerformanceMonitor.LATENCY_BUFFER_SIZE = 10000;
PerformanceMonitor.EVENT_BUFFER_SIZE = 5000;
PerformanceMonitor.EVENT_TTL = 5 * 60 * 1000;
//# sourceMappingURL=performance-monitor.js.map