export interface GameStateLock {
    gameId: string;
    lockId: string;
    timestamp: number;
    operation: string;
}
export interface AtomicOperation<T = any> {
    type: string;
    data: T;
    rollback?: () => void;
}
export interface GameStateSnapshot {
    gameId: string;
    state: any;
    version: number;
    timestamp: number;
}
export declare class AtomicGameStateManager {
    private locks;
    private snapshots;
    private readonly LOCK_TIMEOUT;
    private readonly MAX_SNAPSHOTS;
    acquireLock(gameId: string, operation: string): Promise<string | null>;
    releaseLock(gameId: string, lockId: string): boolean;
    createSnapshot(gameId: string, gameState: any): void;
    restoreSnapshot(gameId: string, version?: number): GameStateSnapshot | null;
    executeAtomicOperation<T>(gameId: string, operation: string, gameState: any, operations: AtomicOperation<T>[], updateCallback: (newState: any) => Promise<void>): Promise<{
        success: boolean;
        error?: string;
        newState?: any;
    }>;
    private cleanOldSnapshots;
    cleanupGame(gameId: string): void;
    getLockStatus(gameId: string): GameStateLock | null;
    cleanExpiredLocks(): void;
}
export declare const atomicGameStateManager: AtomicGameStateManager;
