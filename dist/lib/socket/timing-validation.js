"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverTimingManager = exports.ServerTimingManager = void 0;
class ServerTimingManager {
    constructor() {
        this.activeQuestions = new Map();
        this.TIMING_TOLERANCE = 2000;
        this.MAX_QUESTION_TIME = 120000;
    }
    startQuestion(questionId, gameId, duration) {
        const now = Date.now();
        const timing = {
            questionId,
            startTime: now,
            endTime: now + (duration * 1000),
            duration
        };
        const key = `${gameId}_${questionId}`;
        this.activeQuestions.set(key, timing);
        setTimeout(() => {
            this.activeQuestions.delete(key);
        }, (duration * 1000) + this.TIMING_TOLERANCE + 5000);
        console.log(`[Timing] Started question ${questionId} for ${duration}s`);
        return timing;
    }
    validateAnswerTiming(gameId, questionId, clientTimestamp) {
        const key = `${gameId}_${questionId}`;
        const timing = this.activeQuestions.get(key);
        const serverTimestamp = Date.now();
        if (!timing) {
            return {
                isValid: false,
                serverTimestamp,
                timeTaken: 0,
                error: 'Question not found or expired'
            };
        }
        if (serverTimestamp > timing.endTime + this.TIMING_TOLERANCE) {
            return {
                isValid: false,
                serverTimestamp,
                timeTaken: timing.duration * 1000,
                error: 'Answer submitted after question ended'
            };
        }
        if (serverTimestamp < timing.startTime) {
            return {
                isValid: false,
                serverTimestamp,
                timeTaken: 0,
                error: 'Answer submitted before question started'
            };
        }
        const timeTaken = serverTimestamp - timing.startTime;
        const clientServerDiff = Math.abs(clientTimestamp - serverTimestamp);
        if (clientServerDiff > this.TIMING_TOLERANCE * 2) {
            console.warn(`[Timing] Large client-server time difference: ${clientServerDiff}ms`);
        }
        if (timeTaken < 500) {
            console.warn(`[Timing] Suspiciously fast answer: ${timeTaken}ms for question ${questionId}`);
        }
        return {
            isValid: true,
            serverTimestamp,
            timeTaken,
        };
    }
    getRemainingTime(gameId, questionId) {
        const key = `${gameId}_${questionId}`;
        const timing = this.activeQuestions.get(key);
        if (!timing)
            return 0;
        const now = Date.now();
        const remaining = Math.max(0, timing.endTime - now);
        return Math.ceil(remaining / 1000);
    }
    endQuestion(gameId, questionId) {
        const key = `${gameId}_${questionId}`;
        this.activeQuestions.delete(key);
        console.log(`[Timing] Manually ended question ${questionId}`);
    }
    getActiveQuestions(gameId) {
        const questions = [];
        for (const [key, timing] of this.activeQuestions.entries()) {
            if (key.startsWith(gameId + '_')) {
                questions.push(timing);
            }
        }
        return questions;
    }
    cleanupGame(gameId) {
        const keysToDelete = [];
        for (const key of this.activeQuestions.keys()) {
            if (key.startsWith(gameId + '_')) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach(key => this.activeQuestions.delete(key));
        console.log(`[Timing] Cleaned up ${keysToDelete.length} questions for game ${gameId}`);
    }
    calculateTimeBasedScore(timeTaken, maxTime, baseScore = 1000) {
        const timeRatio = Math.max(0, (maxTime - timeTaken) / maxTime);
        const speedBonus = Math.floor(baseScore * timeRatio * 0.5);
        return baseScore + speedBonus;
    }
    detectCheatingPatterns(answerTimes) {
        if (answerTimes.length < 3) {
            return { suspicious: false, confidence: 0 };
        }
        const avgTime = answerTimes.reduce((a, b) => a + b, 0) / answerTimes.length;
        const fastAnswers = answerTimes.filter(time => time < 1000).length;
        const fastRatio = fastAnswers / answerTimes.length;
        if (avgTime < 2000 && fastRatio > 0.7) {
            return {
                suspicious: true,
                reason: 'Consistently very fast answers',
                confidence: 0.8
            };
        }
        const uniqueTimes = new Set(answerTimes.map(t => Math.floor(t / 100) * 100));
        if (uniqueTimes.size < answerTimes.length * 0.5) {
            return {
                suspicious: true,
                reason: 'Suspicious timing patterns',
                confidence: 0.6
            };
        }
        const timeVariance = this.calculateVariance(answerTimes);
        if (timeVariance < 100000 && answerTimes.length > 5) {
            return {
                suspicious: true,
                reason: 'Unrealistic timing consistency',
                confidence: 0.7
            };
        }
        return { suspicious: false, confidence: 0 };
    }
    calculateVariance(numbers) {
        const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
        const variance = numbers.reduce((acc, num) => acc + Math.pow(num - mean, 2), 0) / numbers.length;
        return variance;
    }
}
exports.ServerTimingManager = ServerTimingManager;
exports.serverTimingManager = new ServerTimingManager();
//# sourceMappingURL=timing-validation.js.map