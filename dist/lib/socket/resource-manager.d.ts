import { EventEmitter } from 'events';
export interface ResourceTracker {
    id: string;
    type: 'game' | 'player' | 'socket' | 'timer' | 'event-listener';
    createdAt: number;
    lastAccessed: number;
    metadata: any;
}
export interface ResourceQuota {
    maxGamesPerIP: number;
    maxPlayersPerGame: number;
    maxSocketsPerIP: number;
    maxChatMessagesPerGame: number;
    maxReconnectAttempts: number;
}
export declare class ResourceManager extends EventEmitter {
    private resources;
    private ipGameCount;
    private ipSocketCount;
    private playerReconnectAttempts;
    private gameCleanupTimers;
    private readonly quotas;
    private readonly TTL;
    constructor();
    registerResource(id: string, type: ResourceTracker['type'], metadata?: any, ip?: string): boolean;
    touchResource(id: string): void;
    unregisterResource(id: string, ip?: string): void;
    scheduleCleanup(id: string, delayMs: number, cleanupCallback: () => void): void;
    private checkQuotas;
    getResourcesByType(type: ResourceTracker['type']): ResourceTracker[];
    getResource(id: string): ResourceTracker | undefined;
    cleanExpiredResources(): void;
    checkReconnectionAttempts(playerId: string): boolean;
    getStats(): {
        totalResources: number;
        byType: Record<string, number>;
        ipStats: {
            gamesPerIP: Record<string, number>;
            socketsPerIP: Record<string, number>;
        };
        memoryUsage: {
            resources: number;
            timers: number;
            reconnectAttempts: number;
        };
    };
    forceCleanupAll(): void;
    private startPeriodicCleanup;
    emergencyCleanup(): void;
}
export declare const resourceManager: ResourceManager;
