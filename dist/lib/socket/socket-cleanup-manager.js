"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketCleanupManager = void 0;
class SocketCleanupManager {
    constructor(callbacks = {}) {
        this.socketTimers = new WeakMap();
        this.socketListeners = new WeakMap();
        this.socketMetadata = new WeakMap();
        this.cleanupCallbacks = callbacks;
    }
    registerSocket(socket, metadata) {
        this.socketMetadata.set(socket, {
            ...metadata,
            connectedAt: Date.now()
        });
        this.socketTimers.set(socket, new Set());
        this.socketListeners.set(socket, []);
    }
    addTimer(socket, timer) {
        const timers = this.socketTimers.get(socket);
        if (timers) {
            timers.add(timer);
        }
    }
    removeTimer(socket, timer) {
        const timers = this.socketTimers.get(socket);
        if (timers) {
            timers.delete(timer);
        }
    }
    addListener(socket, event, listener) {
        const listeners = this.socketListeners.get(socket);
        if (listeners) {
            listeners.push({ event, listener });
        }
    }
    cleanupSocket(socket) {
        try {
            const metadata = this.socketMetadata.get(socket);
            const timers = this.socketTimers.get(socket);
            if (timers) {
                timers.forEach(timer => clearTimeout(timer));
                timers.clear();
            }
            const listeners = this.socketListeners.get(socket);
            if (listeners) {
                listeners.forEach(({ event, listener }) => {
                    socket.removeListener(event, listener);
                });
                listeners.length = 0;
            }
            if ('fns' in socket && Array.isArray(socket.fns)) {
                socket.fns = [];
            }
            if (this.cleanupCallbacks.onDisconnect) {
                this.cleanupCallbacks.onDisconnect(socket.id, metadata?.playerId);
            }
            this.socketTimers.delete(socket);
            this.socketListeners.delete(socket);
            this.socketMetadata.delete(socket);
        }
        catch (error) {
            console.error('Error during socket cleanup:', error);
            if (this.cleanupCallbacks.onError) {
                this.cleanupCallbacks.onError(error, socket.id);
            }
        }
    }
    createTimer(socket, callback, delay) {
        const timer = setTimeout(() => {
            this.removeTimer(socket, timer);
            callback();
        }, delay);
        this.addTimer(socket, timer);
        return timer;
    }
    createListener(socket, event, listener) {
        this.addListener(socket, event, listener);
        return listener;
    }
    getMetadata(socket) {
        return this.socketMetadata.get(socket);
    }
    updateMetadata(socket, updates) {
        const current = this.socketMetadata.get(socket);
        if (current) {
            this.socketMetadata.set(socket, { ...current, ...updates });
        }
    }
}
exports.SocketCleanupManager = SocketCleanupManager;
//# sourceMappingURL=socket-cleanup-manager.js.map