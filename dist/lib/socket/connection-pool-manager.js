"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionPoolManager = void 0;
const events_1 = require("events");
const reliable_socket_client_1 = require("./reliable-socket-client");
class ConnectionPoolManager extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.pools = new Map();
        this.healthStatus = new Map();
        this.healthCheckInterval = null;
        this.currentRoundRobinIndex = 0;
        this.config = config;
        this.metrics = {
            totalConnections: 0,
            activeConnections: 0,
            failedConnections: 0,
            averageLatency: 0,
            connectionDistribution: new Map(),
            errorRates: new Map()
        };
        this.initializePools();
        this.startHealthChecks();
    }
    initializePools() {
        for (const server of this.config.servers) {
            const pool = new SocketPool(server, this.config.maxConnectionsPerServer);
            this.pools.set(server.id, pool);
            this.healthStatus.set(server.id, {
                serverId: server.id,
                isHealthy: true,
                latency: 0,
                activeConnections: 0,
                lastChecked: Date.now(),
                errorRate: 0,
                successRate: 100
            });
            pool.on('connection_established', () => {
                this.metrics.totalConnections++;
                this.metrics.activeConnections++;
                this.updateConnectionDistribution(server.id, 1);
            });
            pool.on('connection_lost', () => {
                this.metrics.activeConnections--;
                this.updateConnectionDistribution(server.id, -1);
            });
            pool.on('connection_failed', () => {
                this.metrics.failedConnections++;
                this.updateErrorRate(server.id);
            });
        }
    }
    async getOptimalConnection(region) {
        const strategy = region ? 'geographic' : this.config.loadBalancingStrategy;
        switch (strategy) {
            case 'round-robin':
                return this.getRoundRobinConnection();
            case 'least-connections':
                return this.getLeastConnectionsConnection();
            case 'latency-based':
                return this.getLowestLatencyConnection();
            case 'geographic':
                return this.getGeographicConnection(region);
            default:
                return this.getRoundRobinConnection();
        }
    }
    async getRoundRobinConnection() {
        const healthyServers = this.getHealthyServers();
        if (healthyServers.length === 0) {
            throw new Error('No healthy servers available');
        }
        const server = healthyServers[this.currentRoundRobinIndex % healthyServers.length];
        this.currentRoundRobinIndex++;
        const pool = this.pools.get(server.serverId);
        return pool.getConnection();
    }
    async getLeastConnectionsConnection() {
        const healthyServers = this.getHealthyServers();
        if (healthyServers.length === 0) {
            throw new Error('No healthy servers available');
        }
        const sorted = healthyServers.sort((a, b) => a.activeConnections - b.activeConnections);
        const pool = this.pools.get(sorted[0].serverId);
        return pool.getConnection();
    }
    async getLowestLatencyConnection() {
        const healthyServers = this.getHealthyServers();
        if (healthyServers.length === 0) {
            throw new Error('No healthy servers available');
        }
        const sorted = healthyServers.sort((a, b) => a.latency - b.latency);
        const pool = this.pools.get(sorted[0].serverId);
        return pool.getConnection();
    }
    async getGeographicConnection(region) {
        const healthyServers = this.getHealthyServers();
        const regionalServers = healthyServers.filter(s => {
            const server = this.config.servers.find(srv => srv.id === s.serverId);
            return server?.region === region;
        });
        if (regionalServers.length === 0) {
            return this.getLowestLatencyConnection();
        }
        const sorted = regionalServers.sort((a, b) => a.latency - b.latency);
        const pool = this.pools.get(sorted[0].serverId);
        return pool.getConnection();
    }
    getHealthyServers() {
        return Array.from(this.healthStatus.values())
            .filter(health => health.isHealthy);
    }
    startHealthChecks() {
        this.healthCheckInterval = setInterval(() => {
            this.performHealthChecks();
        }, this.config.healthCheckInterval);
        this.performHealthChecks();
    }
    async performHealthChecks() {
        const promises = Array.from(this.pools.entries()).map(async ([serverId, pool]) => {
            try {
                const startTime = Date.now();
                const testConnection = await pool.getConnection();
                await testConnection.emit('ping', Date.now(), { requireAck: true, timeout: 5000 });
                const latency = Date.now() - startTime;
                const health = this.healthStatus.get(serverId);
                health.isHealthy = true;
                health.latency = latency;
                health.activeConnections = pool.getActiveConnectionCount();
                health.lastChecked = Date.now();
                health.successRate = this.calculateSuccessRate(serverId);
                pool.returnConnection(testConnection);
                this.emit('health_check_passed', { serverId, latency });
            }
            catch (error) {
                const health = this.healthStatus.get(serverId);
                health.isHealthy = false;
                health.lastChecked = Date.now();
                health.errorRate = this.calculateErrorRate(serverId);
                this.emit('health_check_failed', { serverId, error });
                console.error(`Health check failed for server ${serverId}:`, error);
            }
        });
        await Promise.allSettled(promises);
        this.updateMetrics();
    }
    updateConnectionDistribution(serverId, delta) {
        const current = this.metrics.connectionDistribution.get(serverId) || 0;
        this.metrics.connectionDistribution.set(serverId, current + delta);
    }
    updateErrorRate(serverId) {
        const current = this.metrics.errorRates.get(serverId) || 0;
        this.metrics.errorRates.set(serverId, current + 1);
    }
    calculateSuccessRate(serverId) {
        const pool = this.pools.get(serverId);
        const stats = pool.getStatistics();
        return (stats.successfulConnections / (stats.totalAttempts || 1)) * 100;
    }
    calculateErrorRate(serverId) {
        const errors = this.metrics.errorRates.get(serverId) || 0;
        const total = this.metrics.connectionDistribution.get(serverId) || 1;
        return (errors / total) * 100;
    }
    updateMetrics() {
        const healthyServers = this.getHealthyServers();
        if (healthyServers.length > 0) {
            const totalLatency = healthyServers.reduce((sum, h) => sum + h.latency, 0);
            this.metrics.averageLatency = totalLatency / healthyServers.length;
        }
        this.emit('metrics_updated', this.getMetrics());
    }
    getMetrics() {
        return { ...this.metrics };
    }
    getHealthStatus() {
        return new Map(this.healthStatus);
    }
    async reconnectToServer(serverId) {
        const pool = this.pools.get(serverId);
        if (pool) {
            await pool.reconnectAll();
        }
    }
    async shutdown() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }
        const promises = Array.from(this.pools.values()).map(pool => pool.shutdown());
        await Promise.all(promises);
        this.removeAllListeners();
    }
}
exports.ConnectionPoolManager = ConnectionPoolManager;
class SocketPool extends events_1.EventEmitter {
    constructor(endpoint, maxConnections) {
        super();
        this.connections = new Set();
        this.availableConnections = [];
        this.statistics = {
            totalAttempts: 0,
            successfulConnections: 0,
            failedConnections: 0
        };
        this.endpoint = endpoint;
        this.maxConnections = maxConnections;
    }
    async getConnection() {
        if (this.availableConnections.length > 0) {
            return this.availableConnections.pop();
        }
        if (this.connections.size < this.maxConnections) {
            return this.createConnection();
        }
        return new Promise((resolve) => {
            const checkInterval = setInterval(() => {
                if (this.availableConnections.length > 0) {
                    clearInterval(checkInterval);
                    resolve(this.availableConnections.pop());
                }
            }, 100);
        });
    }
    async createConnection() {
        this.statistics.totalAttempts++;
        try {
            const client = new reliable_socket_client_1.ReliableSocketClient({
                urls: [this.endpoint.url],
                maxReconnectAttempts: 5,
                reconnectDelay: 1000,
                enableQueue: true,
                enableHeartbeat: true
            });
            await client.connect();
            this.connections.add(client);
            this.statistics.successfulConnections++;
            this.emit('connection_established');
            client.on('disconnect', () => {
                this.connections.delete(client);
                this.emit('connection_lost');
            });
            return client;
        }
        catch (error) {
            this.statistics.failedConnections++;
            this.emit('connection_failed', error);
            throw error;
        }
    }
    returnConnection(client) {
        if (client.isConnected() && this.connections.has(client)) {
            this.availableConnections.push(client);
        }
    }
    getActiveConnectionCount() {
        return this.connections.size;
    }
    getStatistics() {
        return { ...this.statistics };
    }
    async reconnectAll() {
        const promises = Array.from(this.connections).map(client => client.reconnect());
        await Promise.allSettled(promises);
    }
    async shutdown() {
        for (const client of this.connections) {
            client.disconnect();
        }
        this.connections.clear();
        this.availableConnections = [];
        this.removeAllListeners();
    }
}
//# sourceMappingURL=connection-pool-manager.js.map