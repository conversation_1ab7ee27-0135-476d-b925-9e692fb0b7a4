"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.stateDiffer = exports.StateDiffer = void 0;
class StateDiffer {
    constructor() {
        this.previousStates = new Map();
        this.stateVersions = new Map();
    }
    createDelta(gameId, newState) {
        const previous = this.previousStates.get(gameId);
        const version = (this.stateVersions.get(gameId) || 0) + 1;
        if (!previous) {
            this.previousStates.set(gameId, this.deepClone(newState));
            this.stateVersions.set(gameId, version);
            return {
                delta: newState,
                version,
                fullStateChecksum: this.calculateChecksum(newState)
            };
        }
        const delta = this.diff(previous, newState);
        if (!delta || Object.keys(delta).length === 0) {
            return null;
        }
        this.previousStates.set(gameId, this.deepClone(newState));
        this.stateVersions.set(gameId, version);
        return {
            delta,
            version,
            fullStateChecksum: this.calculateChecksum(newState)
        };
    }
    applyDelta(baseState, delta) {
        const result = this.deepClone(baseState);
        return this.applyDeltaRecursive(result, delta);
    }
    applyDeltaRecursive(target, delta) {
        for (const key in delta) {
            const value = delta[key];
            if (value && typeof value === 'object' && value._deleted === true) {
                delete target[key];
                continue;
            }
            if (value && typeof value === 'object' && !Array.isArray(value) &&
                target[key] && typeof target[key] === 'object' && !Array.isArray(target[key])) {
                target[key] = this.applyDeltaRecursive(target[key], value);
            }
            else {
                target[key] = value;
            }
        }
        return target;
    }
    diff(oldObj, newObj, path = '') {
        if (oldObj === newObj)
            return null;
        if (oldObj == null || newObj == null)
            return newObj;
        if (typeof oldObj !== 'object' || typeof newObj !== 'object') {
            return oldObj === newObj ? null : newObj;
        }
        if (Array.isArray(oldObj) || Array.isArray(newObj)) {
            return JSON.stringify(oldObj) === JSON.stringify(newObj) ? null : newObj;
        }
        const delta = {};
        const allKeys = new Set([...Object.keys(oldObj), ...Object.keys(newObj)]);
        for (const key of allKeys) {
            const oldValue = oldObj[key];
            const newValue = newObj[key];
            if (key in oldObj && !(key in newObj)) {
                delta[key] = { _deleted: true };
                continue;
            }
            if (!(key in oldObj) || oldValue !== newValue) {
                if (newValue && typeof newValue === 'object' && !Array.isArray(newValue) &&
                    oldValue && typeof oldValue === 'object' && !Array.isArray(oldValue)) {
                    const nestedDiff = this.diff(oldValue, newValue, `${path}.${key}`);
                    if (nestedDiff && Object.keys(nestedDiff).length > 0) {
                        delta[key] = nestedDiff;
                    }
                }
                else {
                    delta[key] = newValue;
                }
            }
        }
        return Object.keys(delta).length > 0 ? delta : null;
    }
    removeGameState(gameId) {
        this.previousStates.delete(gameId);
        this.stateVersions.delete(gameId);
    }
    getVersion(gameId) {
        return this.stateVersions.get(gameId) || 0;
    }
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object')
            return obj;
        if (obj instanceof Date)
            return new Date(obj.getTime());
        if (obj instanceof Array)
            return obj.map(item => this.deepClone(item));
        if (obj instanceof Object) {
            const cloned = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }
    }
    calculateChecksum(state) {
        const str = JSON.stringify(state, Object.keys(state).sort());
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString(16);
    }
    getStats() {
        let totalSize = 0;
        for (const [_, state] of this.previousStates) {
            totalSize += JSON.stringify(state).length;
        }
        return {
            games: this.previousStates.size,
            totalSize
        };
    }
}
exports.StateDiffer = StateDiffer;
exports.stateDiffer = new StateDiffer();
//# sourceMappingURL=state-differ.js.map