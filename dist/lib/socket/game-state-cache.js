"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameStateCache = void 0;
const events_1 = require("events");
const lru_cache_1 = require("lru-cache");
class GameStateCache extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.compressionCache = new Map();
        this.compressionCacheCleanupInterval = null;
        this.config = {
            maxGameStates: 1000,
            maxLeaderboards: 2000,
            maxQuestions: 5000,
            gameStateTTL: 30 * 60 * 1000,
            leaderboardTTL: 5 * 60 * 1000,
            questionTTL: 60 * 60 * 1000,
            enablePersistentCache: false,
            compressionEnabled: true,
            writeThrough: false,
            ...config
        };
        this.initializeCaches();
        this.initializeStats();
        this.startCompressionCacheCleanup();
    }
    initializeCaches() {
        this.gameStateCache = new lru_cache_1.LRUCache({
            max: this.config.maxGameStates,
            ttl: this.config.gameStateTTL,
            allowStale: false,
            updateAgeOnGet: true,
            dispose: (value, key) => {
                this.stats.evictions++;
                this.emit('cache_eviction', { type: 'gameState', key });
            }
        });
        this.leaderboardCache = new lru_cache_1.LRUCache({
            max: this.config.maxLeaderboards,
            ttl: this.config.leaderboardTTL,
            allowStale: false,
            updateAgeOnGet: true,
            dispose: (value, key) => {
                this.stats.evictions++;
                this.emit('cache_eviction', { type: 'leaderboard', key });
            }
        });
        this.questionCache = new lru_cache_1.LRUCache({
            max: this.config.maxQuestions,
            ttl: this.config.questionTTL,
            allowStale: false,
            updateAgeOnGet: true,
            dispose: (value, key) => {
                this.stats.evictions++;
                this.emit('cache_eviction', { type: 'question', key });
            }
        });
    }
    initializeStats() {
        this.stats = {
            gameStateHits: 0,
            gameStateMisses: 0,
            leaderboardHits: 0,
            leaderboardMisses: 0,
            questionHits: 0,
            questionMisses: 0,
            evictions: 0,
            memoryUsage: 0,
            compressionRatio: 0
        };
    }
    async cacheGameState(gameId, gameState) {
        try {
            const cached = {
                gameId,
                status: gameState.status,
                players: new Map(gameState.players),
                questions: gameState.questions,
                currentQuestionIndex: gameState.currentQuestionIndex,
                settings: gameState.settings,
                lastUpdated: Date.now(),
                version: gameState.version || 1,
                checksum: this.calculateChecksum(gameState)
            };
            if (this.config.compressionEnabled) {
                const compressed = await this.compressGameState(cached);
                if (compressed) {
                    cached.compressed = true;
                }
            }
            this.gameStateCache.set(gameId, cached);
            this.emit('game_state_cached', { gameId, size: this.estimateSize(cached) });
        }
        catch (error) {
            console.error('Failed to cache game state:', error);
        }
    }
    async getCachedGameState(gameId) {
        try {
            const cached = this.gameStateCache.get(gameId);
            if (cached) {
                this.stats.gameStateHits++;
                if (cached.compressed) {
                    await this.decompressGameState(cached);
                }
                this.emit('cache_hit', { type: 'gameState', gameId });
                return cached;
            }
            else {
                this.stats.gameStateMisses++;
                this.emit('cache_miss', { type: 'gameState', gameId });
                return null;
            }
        }
        catch (error) {
            console.error('Failed to get cached game state:', error);
            this.stats.gameStateMisses++;
            return null;
        }
    }
    async cacheLeaderboard(gameId, leaderboard, questionIndex) {
        try {
            const cached = {
                gameId,
                entries: leaderboard,
                lastUpdated: Date.now(),
                questionIndex,
                version: Date.now()
            };
            this.leaderboardCache.set(`${gameId}:${questionIndex}`, cached);
            this.leaderboardCache.set(`${gameId}:current`, cached);
            this.emit('leaderboard_cached', {
                gameId,
                questionIndex,
                size: this.estimateSize(cached)
            });
        }
        catch (error) {
            console.error('Failed to cache leaderboard:', error);
        }
    }
    getCachedLeaderboard(gameId, questionIndex) {
        try {
            const key = questionIndex !== undefined
                ? `${gameId}:${questionIndex}`
                : `${gameId}:current`;
            const cached = this.leaderboardCache.get(key);
            if (cached) {
                this.stats.leaderboardHits++;
                this.emit('cache_hit', { type: 'leaderboard', gameId, questionIndex });
                return cached;
            }
            else {
                this.stats.leaderboardMisses++;
                this.emit('cache_miss', { type: 'leaderboard', gameId, questionIndex });
                return null;
            }
        }
        catch (error) {
            console.error('Failed to get cached leaderboard:', error);
            this.stats.leaderboardMisses++;
            return null;
        }
    }
    async cacheQuestion(questionId, question, metadata = {}) {
        try {
            const cached = {
                questionId,
                gameMode: metadata.gameMode || 'unknown',
                difficulty: metadata.difficulty || 1,
                data: question,
                metadata,
                createdAt: Date.now()
            };
            this.questionCache.set(questionId, cached);
            this.emit('question_cached', {
                questionId,
                gameMode: cached.gameMode,
                size: this.estimateSize(cached)
            });
        }
        catch (error) {
            console.error('Failed to cache question:', error);
        }
    }
    getCachedQuestion(questionId) {
        try {
            const cached = this.questionCache.get(questionId);
            if (cached) {
                this.stats.questionHits++;
                this.emit('cache_hit', { type: 'question', questionId });
                return cached;
            }
            else {
                this.stats.questionMisses++;
                this.emit('cache_miss', { type: 'question', questionId });
                return null;
            }
        }
        catch (error) {
            console.error('Failed to get cached question:', error);
            this.stats.questionMisses++;
            return null;
        }
    }
    async batchCacheQuestions(questions) {
        const promises = questions.map(q => this.cacheQuestion(q.id, q.data, q.metadata));
        await Promise.allSettled(promises);
        this.emit('batch_questions_cached', { count: questions.length });
    }
    getQuestionsByGameMode(gameMode, limit = 50) {
        const questions = [];
        for (const [key, question] of this.questionCache.entries()) {
            if (question.gameMode === gameMode && questions.length < limit) {
                questions.push(question);
            }
        }
        return questions.sort((a, b) => b.createdAt - a.createdAt);
    }
    invalidateGameCache(gameId) {
        this.gameStateCache.delete(gameId);
        for (const key of this.leaderboardCache.keys()) {
            if (key.startsWith(`${gameId}:`)) {
                this.leaderboardCache.delete(key);
            }
        }
        this.emit('game_cache_invalidated', { gameId });
    }
    async warmUpCache(gameIds) {
        const startTime = Date.now();
        try {
            this.emit('cache_warmup_started', { gameIds: gameIds.length });
            for (const gameId of gameIds) {
                const mockGameState = {
                    gameId,
                    status: 'waiting',
                    players: new Map(),
                    questions: [],
                    currentQuestionIndex: 0,
                    settings: {},
                    version: 1
                };
                await this.cacheGameState(gameId, mockGameState);
            }
            const duration = Date.now() - startTime;
            this.emit('cache_warmup_completed', {
                gameIds: gameIds.length,
                duration
            });
        }
        catch (error) {
            console.error('Cache warmup failed:', error);
            this.emit('cache_warmup_failed', { error });
        }
    }
    async compressGameState(gameState) {
        try {
            const zlib = require('zlib');
            const data = JSON.stringify(gameState);
            const originalSize = Buffer.byteLength(data);
            const compressed = await new Promise((resolve, reject) => {
                zlib.gzip(data, (err, result) => {
                    if (err)
                        reject(err);
                    else
                        resolve(result);
                });
            });
            const compressedSize = compressed.length;
            const compressionRatio = compressedSize / originalSize;
            if (compressionRatio < 0.8) {
                this.compressionCache.set(gameState.gameId, {
                    data: compressed,
                    timestamp: Date.now()
                });
                this.stats.compressionRatio =
                    (this.stats.compressionRatio + compressionRatio) / 2;
                return true;
            }
            return false;
        }
        catch (error) {
            console.error('Failed to compress game state:', error);
            return false;
        }
    }
    async decompressGameState(gameState) {
        try {
            const compressedEntry = this.compressionCache.get(gameState.gameId);
            if (!compressedEntry)
                return;
            const zlib = require('zlib');
            const decompressed = await new Promise((resolve, reject) => {
                zlib.gunzip(compressedEntry.data, (err, result) => {
                    if (err)
                        reject(err);
                    else
                        resolve(result.toString());
                });
            });
            const data = JSON.parse(decompressed);
            Object.assign(gameState, data);
            gameState.compressed = false;
            this.compressionCache.delete(gameState.gameId);
        }
        catch (error) {
            console.error('Failed to decompress game state:', error);
        }
    }
    calculateChecksum(data) {
        const crypto = require('crypto');
        const hash = crypto.createHash('md5');
        hash.update(JSON.stringify(data));
        return hash.digest('hex');
    }
    estimateSize(obj) {
        return JSON.stringify(obj).length * 2;
    }
    updateMemoryStats() {
        const gameStateSize = this.gameStateCache.size * 1000;
        const leaderboardSize = this.leaderboardCache.size * 500;
        const questionSize = this.questionCache.size * 2000;
        const compressionSize = Array.from(this.compressionCache.values())
            .reduce((sum, entry) => sum + entry.data.length, 0);
        this.stats.memoryUsage = gameStateSize + leaderboardSize + questionSize + compressionSize;
    }
    getStats() {
        this.updateMemoryStats();
        const gameStateTotal = this.stats.gameStateHits + this.stats.gameStateMisses;
        const leaderboardTotal = this.stats.leaderboardHits + this.stats.leaderboardMisses;
        const questionTotal = this.stats.questionHits + this.stats.questionMisses;
        return {
            ...this.stats,
            gameStateCacheSize: this.gameStateCache.size,
            leaderboardCacheSize: this.leaderboardCache.size,
            questionCacheSize: this.questionCache.size,
            hitRates: {
                gameState: gameStateTotal > 0 ? this.stats.gameStateHits / gameStateTotal : 0,
                leaderboard: leaderboardTotal > 0 ? this.stats.leaderboardHits / leaderboardTotal : 0,
                question: questionTotal > 0 ? this.stats.questionHits / questionTotal : 0
            }
        };
    }
    clearAll() {
        this.gameStateCache.clear();
        this.leaderboardCache.clear();
        this.questionCache.clear();
        this.compressionCache.clear();
        this.initializeStats();
        this.emit('caches_cleared');
    }
    prune() {
        const beforeSize = {
            gameState: this.gameStateCache.size,
            leaderboard: this.leaderboardCache.size,
            question: this.questionCache.size
        };
        this.gameStateCache.purgeStale();
        this.leaderboardCache.purgeStale();
        this.questionCache.purgeStale();
        const afterSize = {
            gameState: this.gameStateCache.size,
            leaderboard: this.leaderboardCache.size,
            question: this.questionCache.size
        };
        const pruned = {
            gameState: beforeSize.gameState - afterSize.gameState,
            leaderboard: beforeSize.leaderboard - afterSize.leaderboard,
            question: beforeSize.question - afterSize.question
        };
        this.emit('cache_pruned', { pruned });
    }
    startCompressionCacheCleanup() {
        this.compressionCacheCleanupInterval = setInterval(() => {
            this.cleanupCompressionCache();
        }, GameStateCache.COMPRESSION_CLEANUP_INTERVAL);
    }
    cleanupCompressionCache() {
        const now = Date.now();
        let removedCount = 0;
        for (const [key, entry] of this.compressionCache) {
            if (now - entry.timestamp > GameStateCache.COMPRESSION_CACHE_TTL) {
                this.compressionCache.delete(key);
                removedCount++;
            }
        }
        if (this.compressionCache.size > GameStateCache.MAX_COMPRESSION_CACHE_SIZE) {
            const sortedEntries = Array.from(this.compressionCache.entries())
                .sort((a, b) => a[1].timestamp - b[1].timestamp);
            const toRemove = sortedEntries.slice(0, sortedEntries.length - GameStateCache.MAX_COMPRESSION_CACHE_SIZE);
            toRemove.forEach(([key]) => {
                this.compressionCache.delete(key);
                removedCount++;
            });
        }
        if (removedCount > 0) {
            this.emit('compression_cache_cleaned', {
                removed: removedCount,
                remaining: this.compressionCache.size
            });
        }
    }
    removeGame(gameId) {
        this.invalidateGameCache(gameId);
        this.compressionCache.delete(gameId);
        this.emit('game_removed_from_cache', { gameId });
    }
    shutdown() {
        if (this.compressionCacheCleanupInterval) {
            clearInterval(this.compressionCacheCleanupInterval);
            this.compressionCacheCleanupInterval = null;
        }
        this.clearAll();
        this.removeAllListeners();
    }
}
exports.GameStateCache = GameStateCache;
GameStateCache.MAX_COMPRESSION_CACHE_SIZE = 100;
GameStateCache.COMPRESSION_CACHE_TTL = 5 * 60 * 1000;
GameStateCache.COMPRESSION_CLEANUP_INTERVAL = 60 * 1000;
//# sourceMappingURL=game-state-cache.js.map