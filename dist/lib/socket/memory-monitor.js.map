{"version": 3, "file": "memory-monitor.js", "sourceRoot": "", "sources": ["../../../lib/socket/memory-monitor.ts"], "names": [], "mappings": ";;;AAoBA,MAAa,aAAa;IAUxB,YACE,SAAuC,EAAE,EACzC,YAAwC,EAAE;QAXpC,UAAK,GAAkB,EAAE,CAAA;QAa/B,IAAI,CAAC,MAAM,GAAG;YACZ,aAAa,EAAE,KAAK;YACpB,mBAAmB,EAAE,EAAE;YACvB,gBAAgB,EAAE,GAAG;YACrB,iBAAiB,EAAE,IAAI;YACvB,UAAU,EAAE,EAAE;YACd,GAAG,MAAM;SACV,CAAA;QACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;IAC5B,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAM;QAEzB,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAC/B,IAAI,CAAC,WAAW,EAAE,CAAA;QACpB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;QAG7B,IAAI,CAAC,WAAW,EAAE,CAAA;IACpB,CAAC;IAED,IAAI;QACF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC5B,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAA;QAC3B,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAA;QACtC,MAAM,KAAK,GAAgB;YACzB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;YAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,GAAG,EAAE,QAAQ,CAAC,GAAG;YACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAGtB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC/C,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;QACpB,CAAC;QAGD,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAA;QAE/C,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC/C,OAAO,CAAC,KAAK,CAAC,0BAA0B,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,+BAA+B,CAAC,CAAA;YAC7F,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAA;QACpC,CAAC;aAAM,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,yBAAyB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAA;YAC1F,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,CAAA;QACnC,CAAC;QAGD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAA;YACzC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;gBAC7C,OAAO,CAAC,KAAK,CAAC,yCAAyC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;gBACjF,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAA;YACjC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,CAAA;QAEnC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAA;QAClD,MAAM,gBAAgB,GAAG,CAAC,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAA;QAE7D,OAAO,gBAAgB,CAAA;IACzB,CAAC;IAED,QAAQ;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;IACxB,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAC1C,CAAC;IAED,SAAS;QAMP,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;QACtC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;QAElD,OAAO;YACL,OAAO;YACP,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM;YAClE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;YAC7B,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE;SACvC,CAAA;IACH,CAAC;IAKD,OAAO;QACL,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA;YAC5C,MAAM,CAAC,EAAE,EAAE,CAAA;QACb,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAA;QAC3E,CAAC;IACH,CAAC;CACF;AAhID,sCAgIC;AAKD,MAAa,eAAe;IAA5B;QACU,cAAS,GAAG,IAAI,GAAG,EAIvB,CAAA;IAqEN,CAAC;IAnEC,KAAK,CAAC,EAAU,EAAE,IAAY,EAAE,QAAc;QAC5C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE;YACrB,IAAI;YACJ,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;YACnB,QAAQ;SACT,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,CAAC,EAAU;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;IAClC,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA;IAC5B,CAAC;IAED,kBAAkB,CAAC,IAAY;QAC7B,MAAM,OAAO,GAAa,EAAE,CAAA;QAC5B,KAAK,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC5C,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBAC3B,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAClB,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,kBAAkB,CAAC,QAAgB,EAAE;QAMnC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;aAChD,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;YACxB,EAAE;YACF,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,GAAG,EAAE,GAAG,GAAG,QAAQ,CAAC,OAAO;YAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;SAC5B,CAAC,CAAC;aACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;aAC7B,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;QAElB,OAAO,MAAM,CAAA;IACf,CAAC;IAED,KAAK;QACH,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;IACxB,CAAC;IAED,SAAS;QAKP,MAAM,MAAM,GAA2B,EAAE,CAAA;QAEzC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QAC1D,CAAC;QAED,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YAC1B,MAAM;YACN,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;SACnC,CAAA;IACH,CAAC;CACF;AA1ED,0CA0EC"}