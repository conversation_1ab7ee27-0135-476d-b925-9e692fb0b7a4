import { EventEmitter } from 'events';
export interface SocketMetrics {
    connectionsPerSecond: number;
    disconnectionsPerSecond: number;
    averageLatency: number;
    p95Latency: number;
    p99Latency: number;
    reconnectionRate: number;
    messageRate: number;
    errorRate: number;
    gameMetrics: GameMetrics;
    resourceMetrics: ResourceMetrics;
}
export interface GameMetrics {
    activeGames: number;
    totalPlayers: number;
    gamesPerHour: number;
    averageGameDuration: number;
    gameCompletionRate: number;
    questionsPerSecond: number;
    averagePlayersPerGame: number;
}
export interface ResourceMetrics {
    cpuUsage: number;
    memoryUsage: number;
    eventLoopDelay: number;
    gcFrequency: number;
    networkBandwidth: number;
}
export interface PerformanceAlert {
    id: string;
    type: 'warning' | 'critical';
    metric: string;
    value: number;
    threshold: number;
    message: string;
    timestamp: number;
}
export interface MonitorConfig {
    sampleInterval: number;
    metricsWindow: number;
    alertThresholds: {
        maxLatency: number;
        maxErrorRate: number;
        maxCpuUsage: number;
        maxMemoryUsage: number;
        minGameCompletionRate: number;
    };
}
export declare class PerformanceMonitor extends EventEmitter {
    private config;
    private metrics;
    private metricsHistory;
    private latencyBuffer;
    private connectionEvents;
    private disconnectionEvents;
    private messageEvents;
    private errorEvents;
    private gameStartTimes;
    private alerts;
    private monitorInterval;
    private gcObserver;
    private static readonly LATENCY_BUFFER_SIZE;
    private static readonly EVENT_BUFFER_SIZE;
    private static readonly EVENT_TTL;
    constructor(config?: Partial<MonitorConfig>);
    private initializeMetrics;
    recordConnection(): void;
    recordDisconnection(): void;
    recordLatency(latency: number): void;
    recordMessage(): void;
    recordError(error: any): void;
    recordGameStart(gameId: string): void;
    recordGameCompletion(gameId: string): void;
    updateGameMetrics(metrics: Partial<GameMetrics>): void;
    private startMonitoring;
    private calculateMetrics;
    private updateResourceMetrics;
    private monitorEventLoop;
    private setupGCMonitoring;
    private checkAlerts;
    private createAlert;
    private pruneOldData;
    getMetrics(): SocketMetrics;
    getMetricsHistory(duration: number): SocketMetrics[];
    getActiveAlerts(): PerformanceAlert[];
    generateReport(): string;
    shutdown(): void;
    getMemoryStats(): any;
}
