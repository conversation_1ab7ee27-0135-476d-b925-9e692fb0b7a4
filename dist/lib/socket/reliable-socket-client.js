"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReliableSocketClient = void 0;
const socket_io_client_1 = require("socket.io-client");
const events_1 = require("events");
class ReliableSocketClient extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.socket = null;
        this.messageQueue = new Map();
        this.heartbeatInterval = null;
        this.reconnectTimeout = null;
        this.currentUrlIndex = 0;
        this.isDestroyed = false;
        this.pendingAcks = new Map();
        this.config = {
            urls: ['http://localhost:3001', 'ws://localhost:3001'],
            maxReconnectAttempts: 10,
            reconnectDelay: 1000,
            maxReconnectDelay: 30000,
            timeout: 10000,
            enableQueue: true,
            enableHeartbeat: true,
            heartbeatInterval: 30000,
            enableMockMode: false,
            ...config
        };
        this.connectionState = {
            isConnected: false,
            isConnecting: false,
            isReconnecting: false,
            currentUrl: null,
            lastConnected: null,
            totalReconnectAttempts: 0,
            consecutiveFailures: 0,
            latency: 0
        };
        this.setupUnloadHandlers();
    }
    async connect() {
        if (this.isDestroyed) {
            throw new Error('SocketClient has been destroyed');
        }
        if (this.connectionState.isConnected || this.connectionState.isConnecting) {
            return;
        }
        this.updateConnectionState({ isConnecting: true });
        try {
            await this.attemptConnection();
        }
        catch (error) {
            this.updateConnectionState({ isConnecting: false });
            throw error;
        }
    }
    async attemptConnection() {
        const startTime = Date.now();
        let lastError = null;
        for (let urlIndex = 0; urlIndex < this.config.urls.length; urlIndex++) {
            if (this.isDestroyed)
                return;
            const url = this.config.urls[urlIndex];
            this.currentUrlIndex = urlIndex;
            try {
                await this.connectToUrl(url);
                this.updateConnectionState({
                    isConnected: true,
                    isConnecting: false,
                    currentUrl: url,
                    lastConnected: Date.now(),
                    consecutiveFailures: 0
                });
                this.setupSocket();
                this.processMessageQueue();
                this.startHeartbeat();
                this.emit('connect');
                return;
            }
            catch (error) {
                lastError = error;
                console.warn(`Failed to connect to ${url}:`, error);
                this.updateConnectionState({ consecutiveFailures: this.connectionState.consecutiveFailures + 1 });
            }
        }
        this.updateConnectionState({ isConnecting: false });
        if (this.config.enableMockMode) {
            this.enableMockMode();
            return;
        }
        if (this.connectionState.totalReconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
        else {
            this.emit('reconnect_failed', lastError);
            throw new Error(`Failed to connect to any server after ${this.config.maxReconnectAttempts} attempts`);
        }
    }
    connectToUrl(url) {
        return new Promise((resolve, reject) => {
            if (this.socket) {
                this.socket.disconnect();
                this.socket.removeAllListeners();
            }
            const timeout = setTimeout(() => {
                reject(new Error(`Connection timeout after ${this.config.timeout}ms`));
            }, this.config.timeout);
            this.socket = (0, socket_io_client_1.io)(url, {
                transports: ['websocket', 'polling'],
                timeout: this.config.timeout,
                forceNew: true,
                autoConnect: false
            });
            this.socket.once('connect', () => {
                clearTimeout(timeout);
                resolve();
            });
            this.socket.once('connect_error', (error) => {
                clearTimeout(timeout);
                reject(error);
            });
            this.socket.connect();
        });
    }
    setupSocket() {
        if (!this.socket)
            return;
        this.socket.on('disconnect', (reason) => {
            this.updateConnectionState({
                isConnected: false,
                currentUrl: null
            });
            this.stopHeartbeat();
            this.emit('disconnect', reason);
            if (reason !== 'io client disconnect' && !this.isDestroyed) {
                this.scheduleReconnect();
            }
        });
        this.socket.on('error', (error) => {
            console.error('Socket error:', error);
            this.emit('error', error);
        });
        this.socket.on('reconnect', () => {
            this.updateConnectionState({
                isConnected: true,
                isReconnecting: false,
                totalReconnectAttempts: 0
            });
            this.emit('reconnect');
        });
        this.socket.onAny((event, ...args) => {
            const lastArg = args[args.length - 1];
            if (typeof lastArg === 'string' && this.pendingAcks.has(lastArg)) {
                const pending = this.pendingAcks.get(lastArg);
                clearTimeout(pending.timeout);
                this.pendingAcks.delete(lastArg);
                pending.resolve(args.slice(0, -1));
            }
        });
    }
    scheduleReconnect() {
        if (this.isDestroyed || this.connectionState.isReconnecting)
            return;
        this.updateConnectionState({
            isReconnecting: true,
            totalReconnectAttempts: this.connectionState.totalReconnectAttempts + 1
        });
        const delay = Math.min(this.config.reconnectDelay * Math.pow(2, this.connectionState.totalReconnectAttempts), this.config.maxReconnectDelay);
        this.reconnectTimeout = setTimeout(() => {
            if (!this.isDestroyed) {
                this.attemptConnection().catch(() => {
                });
            }
        }, delay);
    }
    async emit(event, data, options = {}) {
        const messageId = this.generateMessageId();
        const message = {
            id: messageId,
            event,
            data,
            timestamp: Date.now(),
            retries: 0,
            maxRetries: options.maxRetries || 3
        };
        if (this.connectionState.isConnected && this.socket) {
            return this.sendMessage(message, options);
        }
        else if (this.config.enableQueue) {
            return this.queueMessage(message, options);
        }
        else {
            throw new Error('Socket not connected and queuing disabled');
        }
    }
    async sendMessage(message, options = {}) {
        if (!this.socket || !this.connectionState.isConnected) {
            throw new Error('Socket not connected');
        }
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error(`Message timeout after ${options.timeout || this.config.timeout}ms`));
            }, options.timeout || this.config.timeout);
            if (options.requireAck) {
                const ackId = this.generateMessageId();
                this.pendingAcks.set(ackId, {
                    resolve: (data) => {
                        clearTimeout(timeout);
                        resolve(data);
                    },
                    reject: (error) => {
                        clearTimeout(timeout);
                        reject(error);
                    },
                    timeout
                });
                this.socket.emit(message.event, message.data, ackId);
            }
            else {
                this.socket.emit(message.event, message.data);
                clearTimeout(timeout);
                resolve(undefined);
            }
            this.emit('message_sent', message);
        });
    }
    queueMessage(message, options = {}) {
        return new Promise((resolve, reject) => {
            message.callback = (error, data) => {
                if (error)
                    reject(error);
                else
                    resolve(data);
            };
            this.messageQueue.set(message.id, message);
            this.emit('message_queued', message);
        });
    }
    async processMessageQueue() {
        const messages = Array.from(this.messageQueue.values());
        this.messageQueue.clear();
        for (const message of messages) {
            try {
                const result = await this.sendMessage(message);
                message.callback?.(undefined, result);
            }
            catch (error) {
                message.retries++;
                if (message.retries < message.maxRetries) {
                    this.messageQueue.set(message.id, message);
                }
                else {
                    message.callback?.(error);
                    this.emit('message_failed', message, error);
                }
            }
        }
    }
    enableMockMode() {
        console.warn('Enabling mock mode - socket functionality will be simulated');
        this.updateConnectionState({
            isConnected: true,
            isConnecting: false,
            currentUrl: 'mock://localhost',
            lastConnected: Date.now()
        });
        setTimeout(() => {
            this.emit('connect');
        }, 100);
        const originalEmit = this.emit.bind(this);
        this.emit = async (event, data) => {
            await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
            const mockResponse = this.generateMockResponse(event, data);
            setTimeout(() => {
                originalEmit('mock_response', { event, data, response: mockResponse });
            }, 10);
            return mockResponse;
        };
    }
    generateMockResponse(event, data) {
        switch (event) {
            case 'create_game':
                return {
                    success: true,
                    gamePin: Math.random().toString(36).substring(2, 8).toUpperCase(),
                    gameId: 'mock-game-' + Date.now()
                };
            case 'join_game':
                return {
                    success: true,
                    gameState: {
                        status: 'waiting',
                        players: [{ id: 'mock-player', name: data.playerName || 'Mock Player' }]
                    }
                };
            default:
                return { success: true, mock: true };
        }
    }
    startHeartbeat() {
        if (!this.config.enableHeartbeat || this.heartbeatInterval)
            return;
        this.heartbeatInterval = setInterval(() => {
            if (this.socket && this.connectionState.isConnected) {
                const startTime = Date.now();
                this.socket.emit('ping', startTime, (response) => {
                    this.updateConnectionState({
                        latency: Date.now() - startTime
                    });
                });
            }
        }, this.config.heartbeatInterval);
    }
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    updateConnectionState(updates) {
        const oldState = { ...this.connectionState };
        this.connectionState = { ...this.connectionState, ...updates };
        this.emit('connection_state_change', this.connectionState, oldState);
    }
    setupUnloadHandlers() {
        if (typeof window !== 'undefined') {
            window.addEventListener('beforeunload', () => {
                this.disconnect();
            });
        }
    }
    generateMessageId() {
        return `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    }
    getConnectionState() {
        return { ...this.connectionState };
    }
    getQueuedMessageCount() {
        return this.messageQueue.size;
    }
    on(event, listener) {
        if (this.socket && !['connect', 'disconnect', 'error', 'reconnect', 'reconnect_failed'].includes(event)) {
            this.socket.on(event, listener);
        }
        return super.on(event, listener);
    }
    off(event, listener) {
        if (this.socket) {
            this.socket.off(event, listener);
        }
        return super.off(event, listener);
    }
    disconnect() {
        this.isDestroyed = true;
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        this.stopHeartbeat();
        this.pendingAcks.forEach(pending => {
            clearTimeout(pending.timeout);
            pending.reject(new Error('Socket disconnected'));
        });
        this.pendingAcks.clear();
        this.messageQueue.forEach(message => {
            message.callback?.(new Error('Socket disconnected'));
        });
        this.messageQueue.clear();
        if (this.socket) {
            this.socket.disconnect();
            this.socket.removeAllListeners();
            this.socket = null;
        }
        this.updateConnectionState({
            isConnected: false,
            isConnecting: false,
            isReconnecting: false,
            currentUrl: null
        });
        this.removeAllListeners();
    }
    async reconnect() {
        if (this.connectionState.isConnected) {
            this.disconnect();
        }
        this.isDestroyed = false;
        this.connectionState.totalReconnectAttempts = 0;
        await this.connect();
    }
    isConnected() {
        return this.connectionState.isConnected;
    }
    getLatency() {
        return this.connectionState.latency;
    }
}
exports.ReliableSocketClient = ReliableSocketClient;
//# sourceMappingURL=reliable-socket-client.js.map