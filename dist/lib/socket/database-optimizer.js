"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseOptimizer = exports.DatabaseOptimizer = void 0;
const client_1 = require("@prisma/client");
class DatabaseOptimizer {
    constructor(maxConnections = 10) {
        this.cache = new Map();
        this.queryStats = {
            totalQueries: 0,
            averageTime: 0,
            slowQueries: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        this.SLOW_QUERY_THRESHOLD = 1000;
        this.MAX_CACHE_SIZE = 1000;
        this.DEFAULT_TTL = 5 * 60 * 1000;
        this.pool = {
            client: new client_1.PrismaClient({
                log: ['error', 'warn'],
                datasources: {
                    db: {
                        url: process.env.DATABASE_URL
                    }
                }
            }),
            isConnected: false,
            activeConnections: 0,
            maxConnections,
            connectionQueue: []
        };
        this.initializePool();
        this.startCacheCleanup();
    }
    async initializePool() {
        try {
            await this.pool.client.$connect();
            this.pool.isConnected = true;
            console.log('[DatabaseOptimizer] Connection pool initialized');
        }
        catch (error) {
            console.error('[DatabaseOptimizer] Failed to initialize pool:', error);
            this.pool.isConnected = false;
        }
    }
    async getClient() {
        if (!this.pool.isConnected) {
            throw new Error('Database pool not connected');
        }
        if (this.pool.activeConnections >= this.pool.maxConnections) {
            return new Promise((resolve, reject) => {
                this.pool.connectionQueue.push({
                    resolve,
                    reject,
                    timestamp: Date.now()
                });
                setTimeout(() => {
                    const index = this.pool.connectionQueue.findIndex(item => item.resolve === resolve);
                    if (index > -1) {
                        this.pool.connectionQueue.splice(index, 1);
                        reject(new Error('Database connection timeout'));
                    }
                }, 30000);
            });
        }
        this.pool.activeConnections++;
        return this.pool.client;
    }
    releaseClient() {
        this.pool.activeConnections--;
        if (this.pool.connectionQueue.length > 0) {
            const queued = this.pool.connectionQueue.shift();
            if (queued) {
                this.pool.activeConnections++;
                queued.resolve(this.pool.client);
            }
        }
    }
    async executeQuery(cacheKey, queryFn, ttl = this.DEFAULT_TTL) {
        const startTime = Date.now();
        try {
            const cached = this.getFromCache(cacheKey);
            if (cached !== null) {
                this.queryStats.cacheHits++;
                return cached;
            }
            this.queryStats.cacheMisses++;
            const client = await this.getClient();
            const result = await queryFn(client);
            this.releaseClient();
            this.setCache(cacheKey, result, ttl);
            const queryTime = Date.now() - startTime;
            this.updateQueryStats(queryTime);
            return result;
        }
        catch (error) {
            this.releaseClient();
            console.error(`[DatabaseOptimizer] Query failed for key ${cacheKey}:`, error);
            throw error;
        }
    }
    async executeWrite(queryFn, invalidateKeys = []) {
        const startTime = Date.now();
        try {
            const client = await this.getClient();
            const result = await queryFn(client);
            this.releaseClient();
            invalidateKeys.forEach(key => {
                this.invalidateCache(key);
            });
            const queryTime = Date.now() - startTime;
            this.updateQueryStats(queryTime);
            return result;
        }
        catch (error) {
            this.releaseClient();
            console.error('[DatabaseOptimizer] Write operation failed:', error);
            throw error;
        }
    }
    async executeBatch(operations) {
        const results = [];
        const client = await this.getClient();
        try {
            const batchResults = await client.$transaction(operations.map(op => op.queryFn(client)));
            batchResults.forEach((result, index) => {
                const operation = operations[index];
                if (operation.cacheKey) {
                    this.setCache(operation.cacheKey, result, operation.ttl || this.DEFAULT_TTL);
                }
                results.push(result);
            });
            return results;
        }
        finally {
            this.releaseClient();
        }
    }
    async getGameState(gameId) {
        return this.executeQuery(`game_state_${gameId}`, async (client) => {
            return await client.gameSession.findUnique({
                where: { id: gameId },
                include: {
                    players: {
                        include: {
                            user: {
                                select: {
                                    id: true,
                                    username: true,
                                    displayName: true,
                                    role: true
                                }
                            }
                        }
                    },
                    teams: true
                }
            });
        }, 30000);
    }
    async updatePlayerScore(playerId, gameId, newScore) {
        return this.executeWrite(async (client) => {
            return await client.gamePlayer.updateMany({
                where: {
                    userId: playerId,
                    gameSessionId: gameId
                },
                data: {
                    score: newScore,
                    lastActivity: new Date()
                }
            });
        }, [`game_state_${gameId}`, `player_${playerId}_${gameId}`, `leaderboard_${gameId}`]);
    }
    async saveAnswer(answerData) {
        return this.executeWrite(async (client) => {
            return await client.gameAnswer.create({
                data: {
                    playerId: answerData.playerId,
                    gameSessionId: answerData.gameId,
                    questionId: answerData.questionId,
                    answer: answerData.answer,
                    isCorrect: answerData.isCorrect,
                    timeTaken: answerData.timeTaken,
                    points: answerData.points,
                    submittedAt: new Date()
                }
            });
        }, [`game_state_${answerData.gameId}`, `player_${answerData.playerId}_${answerData.gameId}`]);
    }
    async getLeaderboard(gameId) {
        return this.executeQuery(`leaderboard_${gameId}`, async (client) => {
            return await client.gamePlayer.findMany({
                where: { gameSessionId: gameId },
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            displayName: true
                        }
                    }
                },
                orderBy: { score: 'desc' },
                take: 50
            });
        }, 10000);
    }
    async createGameSession(gameData) {
        return this.executeWrite(async (client) => {
            return await client.gameSession.create({
                data: {
                    id: gameData.id,
                    hostId: gameData.hostId,
                    gameMode: gameData.gameMode,
                    settings: JSON.stringify(gameData.settings),
                    status: 'waiting',
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            });
        });
    }
    async addPlayerToGame(playerId, gameId, isHost = false) {
        return this.executeWrite(async (client) => {
            return await client.gamePlayer.create({
                data: {
                    userId: playerId,
                    gameSessionId: gameId,
                    isHost,
                    score: 0,
                    joinedAt: new Date(),
                    lastActivity: new Date()
                }
            });
        }, [`game_state_${gameId}`]);
    }
    getFromCache(key) {
        const entry = this.cache.get(key);
        if (!entry)
            return null;
        if (Date.now() > entry.timestamp + entry.ttl) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    setCache(key, data, ttl) {
        if (this.cache.size >= this.MAX_CACHE_SIZE) {
            this.evictOldestCacheEntries();
        }
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl
        });
    }
    invalidateCache(key) {
        this.cache.delete(key);
    }
    evictOldestCacheEntries() {
        const entries = Array.from(this.cache.entries());
        entries.sort(([, a], [, b]) => a.timestamp - b.timestamp);
        const toRemove = Math.floor(entries.length * 0.25);
        for (let i = 0; i < toRemove; i++) {
            this.cache.delete(entries[i][0]);
        }
        console.log(`[DatabaseOptimizer] Evicted ${toRemove} cache entries`);
    }
    updateQueryStats(queryTime) {
        this.queryStats.totalQueries++;
        this.queryStats.averageTime =
            (this.queryStats.averageTime * (this.queryStats.totalQueries - 1) + queryTime) / this.queryStats.totalQueries;
        if (queryTime > this.SLOW_QUERY_THRESHOLD) {
            this.queryStats.slowQueries++;
            console.warn(`[DatabaseOptimizer] Slow query detected: ${queryTime}ms`);
        }
    }
    startCacheCleanup() {
        setInterval(() => {
            this.cleanExpiredCache();
        }, 5 * 60 * 1000);
        setInterval(() => {
            this.logStats();
        }, 15 * 60 * 1000);
    }
    cleanExpiredCache() {
        const now = Date.now();
        let cleaned = 0;
        for (const [key, entry] of this.cache.entries()) {
            if (now > entry.timestamp + entry.ttl) {
                this.cache.delete(key);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            console.log(`[DatabaseOptimizer] Cleaned ${cleaned} expired cache entries`);
        }
    }
    logStats() {
        console.log('[DatabaseOptimizer] Stats:', {
            ...this.queryStats,
            cacheSize: this.cache.size,
            activeConnections: this.pool.activeConnections,
            queuedConnections: this.pool.connectionQueue.length
        });
    }
    getStats() {
        const totalCacheRequests = this.queryStats.cacheHits + this.queryStats.cacheMisses;
        const hitRate = totalCacheRequests > 0 ? this.queryStats.cacheHits / totalCacheRequests : 0;
        return {
            queries: { ...this.queryStats },
            cache: {
                size: this.cache.size,
                hitRate: Math.round(hitRate * 100) / 100
            },
            connections: {
                active: this.pool.activeConnections,
                queued: this.pool.connectionQueue.length,
                max: this.pool.maxConnections
            }
        };
    }
    async cleanup() {
        await this.pool.client.$disconnect();
        this.cache.clear();
        console.log('[DatabaseOptimizer] Cleanup completed');
    }
}
exports.DatabaseOptimizer = DatabaseOptimizer;
exports.databaseOptimizer = new DatabaseOptimizer();
process.on('SIGTERM', () => {
    exports.databaseOptimizer.cleanup();
});
process.on('SIGINT', () => {
    exports.databaseOptimizer.cleanup();
});
//# sourceMappingURL=database-optimizer.js.map