"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisSessionManager = void 0;
const events_1 = require("events");
let Redis = null;
let redisAvailable = false;
async function loadRedis() {
    if (Redis)
        return Redis;
    try {
        const ioredis = await Promise.resolve().then(() => __importStar(require('ioredis')));
        Redis = ioredis.Redis || ioredis.default?.Redis || ioredis.default;
        redisAvailable = true;
        console.log('Redis module loaded successfully');
        return Redis;
    }
    catch (error) {
        console.warn('Redis not available - running in local mode:', error.message);
        redisAvailable = false;
        return null;
    }
}
class RedisSessionManager extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.heartbeatTimer = null;
        this.isConnected = false;
        this.localMode = false;
        this.config = {
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT || '6379'),
            password: process.env.REDIS_PASSWORD,
            db: parseInt(process.env.REDIS_DB || '0'),
            keyPrefix: 'music-quiz:',
            sessionTTL: 3600,
            heartbeatInterval: 30000,
            ...config
        };
        this.serverId = this.generateServerId();
        this.initializeRedis();
    }
    async initializeRedis() {
        try {
            const RedisClass = await loadRedis();
            if (!RedisClass || !redisAvailable) {
                console.warn('Redis not available - running in local mode');
                this.localMode = true;
                this.isConnected = false;
                this.emit('local_mode');
                return;
            }
            this.redis = new RedisClass({
                host: this.config.host,
                port: this.config.port,
                password: this.config.password,
                db: this.config.db,
                keyPrefix: this.config.keyPrefix,
                retryDelayOnFailover: 100,
                maxRetriesPerRequest: 3,
                lazyConnect: true
            });
            this.subscriber = new RedisClass({
                host: this.config.host,
                port: this.config.port,
                password: this.config.password,
                db: this.config.db,
                keyPrefix: this.config.keyPrefix,
                lazyConnect: true
            });
            this.setupEventHandlers();
            await this.redis.connect();
            await this.subscriber.connect();
            this.isConnected = true;
            this.localMode = false;
            console.log(`Redis Session Manager connected - Server ID: ${this.serverId}`);
            await this.setupSubscriptions();
            this.startHeartbeat();
            this.emit('connected');
        }
        catch (error) {
            console.warn('Failed to initialize Redis, falling back to local mode:', error.message);
            this.localMode = true;
            this.isConnected = false;
            this.emit('local_mode');
        }
    }
    setupEventHandlers() {
        this.redis.on('error', (error) => {
            console.error('Redis connection error:', error);
            this.isConnected = false;
            this.emit('error', error);
        });
        this.redis.on('connect', () => {
            console.log('Redis connected');
            this.isConnected = true;
            this.emit('reconnected');
        });
        this.redis.on('close', () => {
            console.log('Redis connection closed');
            this.isConnected = false;
        });
        this.subscriber.on('message', (channel, message) => {
            this.handlePubSubMessage(channel, message);
        });
    }
    async setupSubscriptions() {
        await this.subscriber.subscribe('game:state:update', 'game:player:join', 'game:player:leave', 'server:heartbeat', 'server:failover');
    }
    async shareGameState(gameId, gameState) {
        if (!this.isConnected || this.localMode)
            return;
        try {
            const snapshot = {
                gameId,
                serverId: this.serverId,
                status: gameState.status,
                players: Array.from(gameState.players.values()).map(player => ({
                    id: player.id,
                    name: player.name,
                    socketId: player.socketId,
                    serverId: this.serverId,
                    isConnected: player.isConnected,
                    lastActivity: player.lastSeen
                })),
                currentQuestionIndex: gameState.currentQuestionIndex,
                questions: gameState.questions,
                leaderboard: gameState.leaderboard || [],
                settings: gameState.settings,
                lastUpdated: Date.now()
            };
            await this.redis.setex(`game:${gameId}`, this.config.sessionTTL, JSON.stringify(snapshot));
            await this.redis.publish('game:state:update', JSON.stringify({
                gameId,
                serverId: this.serverId,
                timestamp: Date.now()
            }));
        }
        catch (error) {
            console.error('Failed to share game state:', error);
        }
    }
    async syncPlayerConnection(playerId, gameId, socketId, playerName, gameState) {
        if (!this.isConnected || this.localMode)
            return;
        try {
            const sessionData = {
                gameId,
                playerId,
                socketId,
                serverId: this.serverId,
                playerName,
                connectionTime: Date.now(),
                lastActivity: Date.now(),
                gameState: gameState || {}
            };
            await this.redis.setex(`session:${playerId}`, this.config.sessionTTL, JSON.stringify(sessionData));
            await this.redis.sadd(`game:${gameId}:players`, playerId);
            await this.redis.expire(`game:${gameId}:players`, this.config.sessionTTL);
            await this.redis.sadd(`server:${this.serverId}:players`, playerId);
            await this.redis.publish('game:player:join', JSON.stringify({
                gameId,
                playerId,
                playerName,
                serverId: this.serverId,
                timestamp: Date.now()
            }));
        }
        catch (error) {
            console.error('Failed to sync player connection:', error);
        }
    }
    async removePlayerConnection(playerId, gameId) {
        if (!this.isConnected || this.localMode)
            return;
        try {
            const sessionData = await this.getPlayerSession(playerId);
            await this.redis.del(`session:${playerId}`);
            await this.redis.srem(`game:${gameId}:players`, playerId);
            await this.redis.srem(`server:${this.serverId}:players`, playerId);
            if (sessionData) {
                await this.redis.publish('game:player:leave', JSON.stringify({
                    gameId,
                    playerId,
                    playerName: sessionData.playerName,
                    serverId: this.serverId,
                    timestamp: Date.now()
                }));
            }
        }
        catch (error) {
            console.error('Failed to remove player connection:', error);
        }
    }
    async getPlayerSession(playerId) {
        if (!this.isConnected)
            return null;
        try {
            const data = await this.redis.get(`session:${playerId}`);
            return data ? JSON.parse(data) : null;
        }
        catch (error) {
            console.error('Failed to get player session:', error);
            return null;
        }
    }
    async getGameState(gameId) {
        if (!this.isConnected)
            return null;
        try {
            const data = await this.redis.get(`game:${gameId}`);
            return data ? JSON.parse(data) : null;
        }
        catch (error) {
            console.error('Failed to get game state:', error);
            return null;
        }
    }
    async getGamePlayers(gameId) {
        if (!this.isConnected)
            return [];
        try {
            const playerIds = await this.redis.smembers(`game:${gameId}:players`);
            const players = [];
            for (const playerId of playerIds) {
                const session = await this.getPlayerSession(playerId);
                if (session) {
                    players.push(session);
                }
            }
            return players;
        }
        catch (error) {
            console.error('Failed to get game players:', error);
            return [];
        }
    }
    async handleServerFailover(failedServerId) {
        if (!this.isConnected)
            return null;
        try {
            console.log(`Handling failover for server: ${failedServerId}`);
            const affectedPlayers = await this.redis.smembers(`server:${failedServerId}:players`);
            const affectedGames = new Set();
            for (const playerId of affectedPlayers) {
                const session = await this.getPlayerSession(playerId);
                if (session && session.serverId === failedServerId) {
                    affectedGames.add(session.gameId);
                    session.serverId = 'DISCONNECTED';
                    session.lastActivity = Date.now();
                    await this.redis.setex(`session:${playerId}`, 300, JSON.stringify(session));
                }
            }
            await this.redis.del(`server:${failedServerId}:players`);
            await this.redis.del(`server:${failedServerId}:heartbeat`);
            const failoverData = {
                failedServerId,
                affectedGames: Array.from(affectedGames),
                affectedPlayers,
                timestamp: Date.now()
            };
            await this.redis.publish('server:failover', JSON.stringify(failoverData));
            console.log(`Failover completed: ${affectedPlayers.length} players, ${affectedGames.size} games affected`);
            return failoverData;
        }
        catch (error) {
            console.error('Failed to handle server failover:', error);
            return null;
        }
    }
    startHeartbeat() {
        this.heartbeatTimer = setInterval(async () => {
            if (!this.isConnected)
                return;
            try {
                const heartbeat = {
                    serverId: this.serverId,
                    timestamp: Date.now(),
                    playerCount: await this.redis.scard(`server:${this.serverId}:players`),
                    memoryUsage: process.memoryUsage(),
                    uptime: process.uptime()
                };
                await this.redis.setex(`server:${this.serverId}:heartbeat`, 60, JSON.stringify(heartbeat));
                await this.redis.publish('server:heartbeat', JSON.stringify(heartbeat));
                await this.checkServerHealth();
            }
            catch (error) {
                console.error('Heartbeat failed:', error);
            }
        }, this.config.heartbeatInterval);
    }
    async checkServerHealth() {
        try {
            const serverKeys = await this.redis.keys('server:*:heartbeat');
            const now = Date.now();
            const timeout = this.config.heartbeatInterval * 3;
            for (const key of serverKeys) {
                const match = key.match(/server:(.+):heartbeat/);
                if (match) {
                    const serverId = match[1];
                    if (serverId === this.serverId)
                        continue;
                    const heartbeatData = await this.redis.get(key);
                    if (heartbeatData) {
                        const heartbeat = JSON.parse(heartbeatData);
                        const timeSinceHeartbeat = now - heartbeat.timestamp;
                        if (timeSinceHeartbeat > timeout) {
                            console.warn(`Server ${serverId} appears to be down (last heartbeat: ${timeSinceHeartbeat}ms ago)`);
                            await this.handleServerFailover(serverId);
                        }
                    }
                }
            }
        }
        catch (error) {
            console.error('Server health check failed:', error);
        }
    }
    handlePubSubMessage(channel, message) {
        try {
            const data = JSON.parse(message);
            switch (channel) {
                case 'game:state:update':
                    if (data.serverId !== this.serverId) {
                        this.emit('game_state_updated', data);
                    }
                    break;
                case 'game:player:join':
                    if (data.serverId !== this.serverId) {
                        this.emit('player_joined_remote', data);
                    }
                    break;
                case 'game:player:leave':
                    if (data.serverId !== this.serverId) {
                        this.emit('player_left_remote', data);
                    }
                    break;
                case 'server:heartbeat':
                    if (data.serverId !== this.serverId) {
                        this.emit('server_heartbeat', data);
                    }
                    break;
                case 'server:failover':
                    this.emit('server_failover', data);
                    break;
            }
        }
        catch (error) {
            console.error('Failed to handle pub/sub message:', error);
        }
    }
    generateServerId() {
        const hostname = require('os').hostname();
        const pid = process.pid;
        const timestamp = Date.now().toString(36);
        return `${hostname}-${pid}-${timestamp}`;
    }
    isRedisAvailable() {
        return this.isConnected && !this.localMode;
    }
    getMode() {
        return this.localMode ? 'local' : 'redis';
    }
    async getServerStats() {
        if (this.localMode) {
            return {
                serverId: this.serverId,
                playerCount: 0,
                gameCount: 0,
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
                mode: 'local'
            };
        }
        if (!this.isConnected)
            return null;
        try {
            const playerCount = await this.redis.scard(`server:${this.serverId}:players`);
            const gameKeys = await this.redis.keys('game:*');
            const gameCount = gameKeys.length;
            return {
                serverId: this.serverId,
                playerCount,
                gameCount,
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
                mode: 'redis'
            };
        }
        catch (error) {
            console.error('Failed to get server stats:', error);
            return null;
        }
    }
    async getActiveServers() {
        if (!this.isConnected)
            return [];
        try {
            const heartbeatKeys = await this.redis.keys('server:*:heartbeat');
            const servers = [];
            for (const key of heartbeatKeys) {
                const data = await this.redis.get(key);
                if (data) {
                    servers.push(JSON.parse(data));
                }
            }
            return servers.sort((a, b) => b.timestamp - a.timestamp);
        }
        catch (error) {
            console.error('Failed to get active servers:', error);
            return [];
        }
    }
    async cleanup() {
        if (!this.isConnected)
            return;
        try {
            await this.redis.del(`server:${this.serverId}:players`);
            await this.redis.del(`server:${this.serverId}:heartbeat`);
            console.log('Session cleanup completed');
        }
        catch (error) {
            console.error('Cleanup failed:', error);
        }
    }
    async shutdown() {
        console.log('Shutting down Redis Session Manager...');
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
        }
        await this.cleanup();
        if (this.redis) {
            await this.redis.quit();
        }
        if (this.subscriber) {
            await this.subscriber.quit();
        }
        this.removeAllListeners();
        console.log('Redis Session Manager shutdown complete');
    }
}
exports.RedisSessionManager = RedisSessionManager;
//# sourceMappingURL=redis-session-manager.js.map