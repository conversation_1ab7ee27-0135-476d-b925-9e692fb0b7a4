"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resourceManager = exports.ResourceManager = void 0;
const events_1 = require("events");
class ResourceManager extends events_1.EventEmitter {
    constructor() {
        super();
        this.resources = new Map();
        this.ipGameCount = new Map();
        this.ipSocketCount = new Map();
        this.playerReconnectAttempts = new Map();
        this.gameCleanupTimers = new Map();
        this.quotas = {
            maxGamesPerIP: 5,
            maxPlayersPerGame: 50,
            maxSocketsPerIP: 10,
            maxChatMessagesPerGame: 1000,
            maxReconnectAttempts: 20
        };
        this.TTL = {
            game: 24 * 60 * 60 * 1000,
            player: 2 * 60 * 60 * 1000,
            socket: 30 * 60 * 1000,
            timer: 60 * 60 * 1000,
            'event-listener': 60 * 60 * 1000
        };
        this.startPeriodicCleanup();
    }
    registerResource(id, type, metadata = {}, ip) {
        if (!this.checkQuotas(type, ip, metadata)) {
            console.warn(`[ResourceManager] Quota exceeded for ${type} from IP ${ip}`);
            return false;
        }
        const resource = {
            id,
            type,
            createdAt: Date.now(),
            lastAccessed: Date.now(),
            metadata
        };
        this.resources.set(id, resource);
        if (ip) {
            if (type === 'game') {
                if (!this.ipGameCount.has(ip)) {
                    this.ipGameCount.set(ip, new Set());
                }
                this.ipGameCount.get(ip).add(id);
            }
            else if (type === 'socket') {
                if (!this.ipSocketCount.has(ip)) {
                    this.ipSocketCount.set(ip, new Set());
                }
                this.ipSocketCount.get(ip).add(id);
            }
        }
        console.log(`[ResourceManager] Registered ${type} resource: ${id}`);
        this.emit('resourceRegistered', { id, type, metadata });
        return true;
    }
    touchResource(id) {
        const resource = this.resources.get(id);
        if (resource) {
            resource.lastAccessed = Date.now();
        }
    }
    unregisterResource(id, ip) {
        const resource = this.resources.get(id);
        if (!resource)
            return;
        if (ip) {
            if (resource.type === 'game') {
                this.ipGameCount.get(ip)?.delete(id);
                if (this.ipGameCount.get(ip)?.size === 0) {
                    this.ipGameCount.delete(ip);
                }
            }
            else if (resource.type === 'socket') {
                this.ipSocketCount.get(ip)?.delete(id);
                if (this.ipSocketCount.get(ip)?.size === 0) {
                    this.ipSocketCount.delete(ip);
                }
            }
        }
        const timer = this.gameCleanupTimers.get(id);
        if (timer) {
            clearTimeout(timer);
            this.gameCleanupTimers.delete(id);
        }
        this.resources.delete(id);
        console.log(`[ResourceManager] Unregistered ${resource.type} resource: ${id}`);
        this.emit('resourceUnregistered', { id, type: resource.type });
    }
    scheduleCleanup(id, delayMs, cleanupCallback) {
        const existingTimer = this.gameCleanupTimers.get(id);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }
        const timer = setTimeout(() => {
            try {
                cleanupCallback();
                console.log(`[ResourceManager] Executed scheduled cleanup for resource: ${id}`);
            }
            catch (error) {
                console.error(`[ResourceManager] Cleanup callback failed for ${id}:`, error);
            }
            finally {
                this.gameCleanupTimers.delete(id);
            }
        }, delayMs);
        this.gameCleanupTimers.set(id, timer);
        console.log(`[ResourceManager] Scheduled cleanup for ${id} in ${delayMs}ms`);
    }
    checkQuotas(type, ip, metadata) {
        if (!ip)
            return true;
        switch (type) {
            case 'game':
                const gameCount = this.ipGameCount.get(ip)?.size || 0;
                return gameCount < this.quotas.maxGamesPerIP;
            case 'socket':
                const socketCount = this.ipSocketCount.get(ip)?.size || 0;
                return socketCount < this.quotas.maxSocketsPerIP;
            case 'player':
                if (metadata?.gameId) {
                    const gameResources = this.getResourcesByType('player')
                        .filter(r => r.metadata.gameId === metadata.gameId);
                    return gameResources.length < this.quotas.maxPlayersPerGame;
                }
                return true;
            default:
                return true;
        }
    }
    getResourcesByType(type) {
        return Array.from(this.resources.values()).filter(r => r.type === type);
    }
    getResource(id) {
        return this.resources.get(id);
    }
    cleanExpiredResources() {
        const now = Date.now();
        const toDelete = [];
        for (const [id, resource] of this.resources.entries()) {
            const ttl = this.TTL[resource.type];
            const age = now - resource.lastAccessed;
            if (age > ttl) {
                toDelete.push(id);
            }
        }
        toDelete.forEach(id => {
            const resource = this.resources.get(id);
            this.unregisterResource(id);
            this.emit('resourceExpired', { id, type: resource.type });
        });
        if (toDelete.length > 0) {
            console.log(`[ResourceManager] Cleaned ${toDelete.length} expired resources`);
        }
    }
    checkReconnectionAttempts(playerId) {
        const attempts = this.playerReconnectAttempts.get(playerId) || 0;
        if (attempts >= this.quotas.maxReconnectAttempts) {
            return false;
        }
        this.playerReconnectAttempts.set(playerId, attempts + 1);
        setTimeout(() => {
            this.playerReconnectAttempts.delete(playerId);
        }, 60 * 60 * 1000);
        return true;
    }
    getStats() {
        const byType = {};
        for (const resource of this.resources.values()) {
            byType[resource.type] = (byType[resource.type] || 0) + 1;
        }
        const gamesPerIP = {};
        for (const [ip, games] of this.ipGameCount.entries()) {
            gamesPerIP[ip] = games.size;
        }
        const socketsPerIP = {};
        for (const [ip, sockets] of this.ipSocketCount.entries()) {
            socketsPerIP[ip] = sockets.size;
        }
        return {
            totalResources: this.resources.size,
            byType,
            ipStats: {
                gamesPerIP,
                socketsPerIP
            },
            memoryUsage: {
                resources: this.resources.size,
                timers: this.gameCleanupTimers.size,
                reconnectAttempts: this.playerReconnectAttempts.size
            }
        };
    }
    forceCleanupAll() {
        console.log('[ResourceManager] Force cleaning all resources');
        for (const timer of this.gameCleanupTimers.values()) {
            clearTimeout(timer);
        }
        this.resources.clear();
        this.ipGameCount.clear();
        this.ipSocketCount.clear();
        this.playerReconnectAttempts.clear();
        this.gameCleanupTimers.clear();
        this.emit('allResourcesCleared');
    }
    startPeriodicCleanup() {
        setInterval(() => {
            this.cleanExpiredResources();
        }, 5 * 60 * 1000);
        setInterval(() => {
            const stats = this.getStats();
            console.log('[ResourceManager] Stats:', JSON.stringify(stats, null, 2));
        }, 15 * 60 * 1000);
        console.log('[ResourceManager] Started periodic cleanup processes');
    }
    emergencyCleanup() {
        console.warn('[ResourceManager] Executing emergency cleanup');
        const now = Date.now();
        const toDelete = [];
        for (const [id, resource] of this.resources.entries()) {
            const age = now - resource.lastAccessed;
            if (age > 30 * 60 * 1000) {
                toDelete.push(id);
            }
        }
        toDelete.forEach(id => this.unregisterResource(id));
        this.playerReconnectAttempts.clear();
        console.log(`[ResourceManager] Emergency cleanup removed ${toDelete.length} resources`);
        this.emit('emergencyCleanup', { removedCount: toDelete.length });
    }
}
exports.ResourceManager = ResourceManager;
exports.resourceManager = new ResourceManager();
//# sourceMappingURL=resource-manager.js.map