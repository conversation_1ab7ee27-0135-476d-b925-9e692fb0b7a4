{"version": 3, "file": "connection-pool-manager.js", "sourceRoot": "", "sources": ["../../../lib/socket/connection-pool-manager.ts"], "names": [], "mappings": ";;;AAKA,mCAAqC;AACrC,qEAA+D;AAyC/D,MAAa,qBAAsB,SAAQ,qBAAY;IAQrD,YAAY,MAAkB;QAC5B,KAAK,EAAE,CAAA;QAPD,UAAK,GAA4B,IAAI,GAAG,EAAE,CAAA;QAC1C,iBAAY,GAA8B,IAAI,GAAG,EAAE,CAAA;QACnD,wBAAmB,GAA0B,IAAI,CAAA;QAEjD,2BAAsB,GAAG,CAAC,CAAA;QAIhC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,OAAO,GAAG;YACb,gBAAgB,EAAE,CAAC;YACnB,iBAAiB,EAAE,CAAC;YACpB,iBAAiB,EAAE,CAAC;YACpB,cAAc,EAAE,CAAC;YACjB,sBAAsB,EAAE,IAAI,GAAG,EAAE;YACjC,UAAU,EAAE,IAAI,GAAG,EAAE;SACtB,CAAA;QAED,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,iBAAiB,EAAE,CAAA;IAC1B,CAAC;IAKO,eAAe;QACrB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACzC,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAA;YACxE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;YAG/B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;gBAC/B,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,CAAC;gBACV,iBAAiB,EAAE,CAAC;gBACpB,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;gBACvB,SAAS,EAAE,CAAC;gBACZ,WAAW,EAAE,GAAG;aACjB,CAAC,CAAA;YAGF,IAAI,CAAC,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;gBACrC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAA;gBAC/B,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAA;gBAChC,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;YACjD,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,EAAE,CAAC,iBAAiB,EAAE,GAAG,EAAE;gBAC9B,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAA;gBAChC,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;YAClD,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;gBAChC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAA;gBAChC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YACjC,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,MAAe;QACxC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAA;QAE1E,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAA;YAEvC,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAAC,6BAA6B,EAAE,CAAA;YAE7C,KAAK,eAAe;gBAClB,OAAO,IAAI,CAAC,0BAA0B,EAAE,CAAA;YAE1C,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAO,CAAC,CAAA;YAE9C;gBACE,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACzC,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,uBAAuB;QACnC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC/C,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACjD,CAAC;QAED,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,sBAAsB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAA;QAClF,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAE7B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAE,CAAA;QAC7C,OAAO,IAAI,CAAC,aAAa,EAAE,CAAA;IAC7B,CAAC;IAKO,KAAK,CAAC,6BAA6B;QACzC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC/C,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACjD,CAAC;QAGD,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC1C,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,CAC1C,CAAA;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAE,CAAA;QAChD,OAAO,IAAI,CAAC,aAAa,EAAE,CAAA;IAC7B,CAAC;IAKO,KAAK,CAAC,0BAA0B;QACtC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC/C,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACjD,CAAC;QAGD,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAA;QAEnE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAE,CAAA;QAChD,OAAO,IAAI,CAAC,aAAa,EAAE,CAAA;IAC7B,CAAC;IAKO,KAAK,CAAC,uBAAuB,CAAC,MAAc;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC/C,MAAM,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;YAChD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAA;YACrE,OAAO,MAAM,EAAE,MAAM,KAAK,MAAM,CAAA;QAClC,CAAC,CAAC,CAAA;QAEF,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAEjC,OAAO,IAAI,CAAC,0BAA0B,EAAE,CAAA;QAC1C,CAAC;QAGD,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAA;QACpE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAE,CAAA;QAChD,OAAO,IAAI,CAAC,aAAa,EAAE,CAAA;IAC7B,CAAC;IAKO,iBAAiB;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;aAC1C,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IACvC,CAAC;IAKO,iBAAiB;QACvB,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,GAAG,EAAE;YAC1C,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC5B,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAA;QAGnC,IAAI,CAAC,mBAAmB,EAAE,CAAA;IAC5B,CAAC;IAKO,KAAK,CAAC,mBAAmB;QAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;YAC/E,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBAC5B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAA;gBAGjD,MAAM,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;gBAElF,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;gBAGtC,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAA;gBAC/C,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;gBACvB,MAAM,CAAC,OAAO,GAAG,OAAO,CAAA;gBACxB,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAA;gBAC1D,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBAC/B,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAA;gBAGxD,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAA;gBAErC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAA;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAA;gBAC/C,MAAM,CAAC,SAAS,GAAG,KAAK,CAAA;gBACxB,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBAC/B,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAA;gBAEpD,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAA;gBACrD,OAAO,CAAC,KAAK,CAAC,kCAAkC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAA;YACrE,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;QAClC,IAAI,CAAC,aAAa,EAAE,CAAA;IACtB,CAAC;IAKO,4BAA4B,CAAC,QAAgB,EAAE,KAAa;QAClE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACtE,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,GAAG,KAAK,CAAC,CAAA;IACpE,CAAC;IAKO,eAAe,CAAC,QAAgB;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC1D,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,GAAG,CAAC,CAAC,CAAA;IACpD,CAAC;IAKO,oBAAoB,CAAC,QAAgB;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAA;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAClC,OAAO,CAAC,KAAK,CAAC,qBAAqB,GAAG,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;IACzE,CAAC;IAKO,kBAAkB,CAAC,QAAgB;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACzD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACpE,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,CAAA;IAC/B,CAAC;IAKO,aAAa;QAEnB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC/C,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;YAC1E,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,YAAY,GAAG,cAAc,CAAC,MAAM,CAAA;QACpE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAA;IACjD,CAAC;IAKD,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAKD,eAAe;QACb,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IACnC,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QACtC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACrC,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;QAC3B,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,QAAQ;QACZ,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;QACzC,CAAC;QAGD,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;QAC7E,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAE3B,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC3B,CAAC;CACF;AAjTD,sDAiTC;AAKD,MAAM,UAAW,SAAQ,qBAAY;IAWnC,YAAY,QAAwB,EAAE,cAAsB;QAC1D,KAAK,EAAE,CAAA;QATD,gBAAW,GAA8B,IAAI,GAAG,EAAE,CAAA;QAClD,yBAAoB,GAA2B,EAAE,CAAA;QACjD,eAAU,GAAG;YACnB,aAAa,EAAE,CAAC;YAChB,qBAAqB,EAAE,CAAC;YACxB,iBAAiB,EAAE,CAAC;SACrB,CAAA;QAIC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;IACtC,CAAC;IAKD,KAAK,CAAC,aAAa;QAEjB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAG,CAAA;QACzC,CAAC;QAGD,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAChC,CAAC;QAGD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;gBACrC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzC,aAAa,CAAC,aAAa,CAAC,CAAA;oBAC5B,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAG,CAAC,CAAA;gBAC3C,CAAC;YACH,CAAC,EAAE,GAAG,CAAC,CAAA;QACT,CAAC,CAAC,CAAA;IACJ,CAAC;IAKO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAA;QAE/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,6CAAoB,CAAC;gBACtC,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACzB,oBAAoB,EAAE,CAAC;gBACvB,cAAc,EAAE,IAAI;gBACpB,WAAW,EAAE,IAAI;gBACjB,eAAe,EAAE,IAAI;aACtB,CAAC,CAAA;YAEF,MAAM,MAAM,CAAC,OAAO,EAAE,CAAA;YAEtB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;YAC5B,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAA;YACvC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;YAGnC,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC3B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;gBAC/B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;YAC9B,CAAC,CAAC,CAAA;YAEF,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;YACnC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;YACrC,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,gBAAgB,CAAC,MAA4B;QAC3C,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAKD,wBAAwB;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;IAC9B,CAAC;IAKD,aAAa;QACX,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;IAC/B,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAA;QAC/E,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;IACpC,CAAC;IAKD,KAAK,CAAC,QAAQ;QACZ,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,CAAC,UAAU,EAAE,CAAA;QACrB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;QACxB,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAA;QAC9B,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC3B,CAAC;CACF"}