{"version": 3, "file": "circular-buffer.js", "sourceRoot": "", "sources": ["../../../lib/socket/circular-buffer.ts"], "names": [], "mappings": ";;;AAKA,MAAa,cAAc;IAMzB,YAAY,QAAgB;QAJpB,eAAU,GAAG,CAAC,CAAA;QACd,SAAI,GAAG,CAAC,CAAA;QAId,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAC9C,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAA;IACnC,CAAC;IAKD,IAAI,CAAC,IAAO;QACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAA;QACnC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAA;QACvD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;IACpD,CAAC;IAKD,QAAQ,CAAC,KAAU;QACjB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IACxC,CAAC;IAKD,MAAM;QACJ,MAAM,MAAM,GAAQ,EAAE,CAAA;QAEtB,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;YAAE,OAAO,MAAM,CAAA;QAGlC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAA;QAG7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAA;YACzC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;YAC/B,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,SAAS,CAAC,KAAa;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;QACzB,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAA;IAC1B,CAAC;IAKD,MAAM,CAAC,SAA+B;QACpC,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IACxC,CAAC;IAKD,KAAK;QACH,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACtC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;QACnB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;IACf,CAAC;IAKD,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAA;IAClB,CAAC;IAKD,MAAM;QACJ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAA;IACpC,CAAC;IAKD,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAA;IACtB,CAAC;IAKD,cAAc;QAEZ,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QAC1E,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAA;IACpC,CAAC;CACF;AA1GD,wCA0GC;AAKD,MAAa,uBAAyD,SAAQ,cAAiB;IAI7F,YAAY,QAAgB,EAAE,KAAa;QACzC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAHT,oBAAe,GAA0B,IAAI,CAAA;QAInD,IAAI,CAAC,GAAG,GAAG,KAAK,CAAA;QAGhB,IAAI,CAAC,YAAY,EAAE,CAAA;IACrB,CAAC;IAEO,YAAY;QAClB,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,aAAa,EAAE,CAAA;QACtB,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC,CAAA;IACpC,CAAC;IAEO,aAAa;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACxC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAChC,CAAA;QAGD,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YAClC,IAAI,CAAC,KAAK,EAAE,CAAA;YACZ,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAKD,QAAQ;QACN,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACjC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAChC,CAAA;IACH,CAAC;IAKD,OAAO;QACL,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YACnC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;QAC7B,CAAC;IACH,CAAC;CACF;AAlDD,0DAkDC"}