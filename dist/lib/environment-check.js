"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateEnvironment = validateEnvironment;
exports.logEnvironmentCheck = logEnvironmentCheck;
exports.setupErrorTracking = setupErrorTracking;
exports.checkReactCompatibility = checkReactCompatibility;
exports.runAllChecks = runAllChecks;
function validateEnvironment() {
    const errors = [];
    const warnings = [];
    const timestamp = new Date().toISOString();
    let reflectSupport = true;
    if (typeof Reflect === 'undefined') {
        errors.push('Reflect API not available');
        reflectSupport = false;
    }
    else {
        const requiredMethods = ['apply', 'construct', 'defineProperty', 'get', 'has', 'set'];
        for (const method of requiredMethods) {
            if (typeof Reflect[method] !== 'function') {
                errors.push(`Reflect.${method} not available`);
                reflectSupport = false;
            }
        }
    }
    if (typeof window !== 'undefined') {
        const globalObj = window;
        if (globalObj._babelPolyfill) {
            warnings.push('Babel polyfill detected - may cause conflicts');
        }
        if (globalObj.core) {
            warnings.push('Core-js polyfill detected - may cause conflicts');
        }
    }
    const info = {
        userAgent: typeof window !== 'undefined' ? window.navigator?.userAgent : undefined,
        nodeVersion: typeof process !== 'undefined' ? process.version : undefined,
        reflectSupport,
        timestamp
    };
    const isValid = errors.length === 0;
    return {
        isValid,
        errors,
        warnings,
        info
    };
}
function logEnvironmentCheck() {
    const result = validateEnvironment();
    if (result.isValid) {
        console.log('✅ Environment validation passed', result.info);
        if (result.warnings.length > 0) {
            console.warn('⚠️ Environment warnings:', result.warnings);
        }
    }
    else {
        console.error('❌ Environment validation failed:', {
            errors: result.errors,
            warnings: result.warnings,
            info: result.info
        });
    }
    return result;
}
function setupErrorTracking() {
    if (typeof window === 'undefined')
        return;
    window.addEventListener('error', (event) => {
        const errorMessage = event.error?.message || event.message || 'Unknown error';
        if (errorMessage.includes('ReflectApply') || errorMessage.includes('Reflect')) {
            console.error('🔍 Reflect API Error Detected:', {
                message: errorMessage,
                stack: event.error?.stack,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                timestamp: new Date().toISOString(),
                userAgent: window.navigator?.userAgent
            });
            const validation = validateEnvironment();
            console.error('🔍 Environment state during error:', validation);
        }
    });
    window.addEventListener('unhandledrejection', (event) => {
        const reason = event.reason;
        const reasonMessage = reason?.message || reason?.toString() || 'Unknown rejection';
        if (reasonMessage.includes('ReflectApply') || reasonMessage.includes('Reflect')) {
            console.error('🔍 Reflect API Promise Rejection:', {
                reason: reasonMessage,
                stack: reason?.stack,
                timestamp: new Date().toISOString()
            });
        }
    });
    console.log('🔍 Error tracking initialized for Reflect API issues');
}
function checkReactCompatibility() {
    if (typeof window === 'undefined')
        return true;
    try {
        const React = window.React;
        if (!React) {
            console.warn('⚠️ React not found on window object');
            return false;
        }
        const version = React.version;
        if (version) {
            console.log(`📦 React version: ${version}`);
            if (version.startsWith('19.')) {
                console.log('ℹ️ React 19 detected - using latest features');
            }
        }
        return true;
    }
    catch (error) {
        console.error('❌ React compatibility check failed:', error);
        return false;
    }
}
function runAllChecks() {
    console.group('🔍 Environment Diagnostics');
    const envResult = logEnvironmentCheck();
    const reactOk = checkReactCompatibility();
    console.log('📊 Summary:', {
        environmentValid: envResult.isValid,
        reactCompatible: reactOk,
        totalErrors: envResult.errors.length,
        totalWarnings: envResult.warnings.length
    });
    console.groupEnd();
}
//# sourceMappingURL=environment-check.js.map