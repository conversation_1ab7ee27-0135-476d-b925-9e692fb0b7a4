import { Server as HTTPServer } from 'http';
export declare class MultiplayerSocketServer {
    private io;
    private gameStore;
    private gameManagers;
    private connectionCount;
    private ipConnectionCounts;
    constructor(httpServer: HTTPServer);
    private setupEventHandlers;
    private handlePlayerLeave;
    private handleNextAction;
    getStats(): {
        connectedClients: number;
        activeGames: number;
        games: {
            id: string;
            status: "waiting" | "countdown" | "playing" | "question-results" | "finished";
            players: number;
            host: string | undefined;
        }[];
    };
}
export declare function initializeSocketServer(httpServer: HTTPServer): MultiplayerSocketServer;
