"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleQuizGenerator = void 0;
class SimpleQuizGenerator {
    constructor() {
        this.musicQuestions = [
            {
                id: 'q1',
                type: 'multiple-choice',
                question: 'Which band released the album "Abbey Road"?',
                options: ['The Beatles', 'The Rolling Stones', 'Led Zeppelin', 'Pink Floyd'],
                correctAnswer: 'The Beatles',
                category: 'Classic Rock',
                timeLimit: 30,
                explanation: 'Abbey Road is the eleventh studio album by The Beatles, released in 1969.'
            },
            {
                id: 'q2',
                type: 'multiple-choice',
                question: 'Who is known as the "King of Pop"?',
                options: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
                correctAnswer: '<PERSON>',
                category: 'Pop Music',
                timeLimit: 30,
                explanation: '<PERSON> earned the title "King of Pop" for his contributions to music, dance, and fashion.'
            },
            {
                id: 'q3',
                type: 'multiple-choice',
                question: 'Which instrument does <PERSON><PERSON><PERSON> famously play?',
                options: ['Violin', 'Piano', '<PERSON>o', '<PERSON>'],
                correctAnswer: 'Cello',
                category: 'Classical Music',
                timeLimit: 30,
                explanation: '<PERSON><PERSON><PERSON> is a world-renowned cellist who has won multiple Grammy Awards.'
            },
            {
                id: 'q4',
                type: 'multiple-choice',
                question: 'What year did MTV first go on the air?',
                options: ['1979', '1981', '1983', '1985'],
                correctAnswer: '1981',
                category: 'Music History',
                timeLimit: 30,
                explanation: 'MTV launched on August 1, 1981, with the words "Ladies and gentlemen, rock and roll."'
            },
            {
                id: 'q5',
                type: 'multiple-choice',
                question: 'Which artist painted the cover of The Velvet Underground & Nico album?',
                options: ['Pablo Picasso', 'Andy Warhol', 'Salvador Dalí', 'Jean-Michel Basquiat'],
                correctAnswer: 'Andy Warhol',
                category: 'Album Art',
                timeLimit: 30,
                explanation: 'Andy Warhol designed the famous banana cover for The Velvet Underground\'s debut album.'
            },
            {
                id: 'q6',
                type: 'multiple-choice',
                question: 'What is the best-selling album of all time?',
                options: ['Back in Black', 'The Dark Side of the Moon', 'Thriller', 'Rumours'],
                correctAnswer: 'Thriller',
                category: 'Music Records',
                timeLimit: 30,
                explanation: 'Michael Jackson\'s "Thriller" has sold an estimated 66 million copies worldwide.'
            },
            {
                id: 'q7',
                type: 'multiple-choice',
                question: 'Which city is considered the birthplace of jazz?',
                options: ['Chicago', 'New York', 'New Orleans', 'Memphis'],
                correctAnswer: 'New Orleans',
                category: 'Music History',
                timeLimit: 30,
                explanation: 'New Orleans is widely considered the birthplace of jazz music in the early 20th century.'
            },
            {
                id: 'q8',
                type: 'multiple-choice',
                question: 'Who wrote the opera "The Magic Flute"?',
                options: ['Ludwig van Beethoven', 'Wolfgang Amadeus Mozart', 'Johann Sebastian Bach', 'Giuseppe Verdi'],
                correctAnswer: 'Wolfgang Amadeus Mozart',
                category: 'Classical Music',
                timeLimit: 30,
                explanation: 'Mozart composed "The Magic Flute" in 1791, shortly before his death.'
            },
            {
                id: 'q9',
                type: 'multiple-choice',
                question: 'Which band performed at the first Woodstock festival in 1969?',
                options: ['The Who', 'Queen', 'AC/DC', 'Metallica'],
                correctAnswer: 'The Who',
                category: 'Music History',
                timeLimit: 30,
                explanation: 'The Who performed at Woodstock on August 17, 1969, playing through the night into the early morning.'
            },
            {
                id: 'q10',
                type: 'multiple-choice',
                question: 'What does "DJ" stand for?',
                options: ['Dance Jockey', 'Disc Jockey', 'Digital Jockey', 'Dynamic Jockey'],
                correctAnswer: 'Disc Jockey',
                category: 'Music Terminology',
                timeLimit: 30,
                explanation: 'DJ stands for "Disc Jockey," originally referring to radio announcers who played vinyl records.'
            }
        ];
    }
    async generateQuiz(settings) {
        const totalQuestions = settings.totalQuestions || 10;
        const shuffled = [...this.musicQuestions].sort(() => Math.random() - 0.5);
        return shuffled.slice(0, Math.min(totalQuestions, this.musicQuestions.length));
    }
}
exports.SimpleQuizGenerator = SimpleQuizGenerator;
//# sourceMappingURL=quiz-generator.js.map