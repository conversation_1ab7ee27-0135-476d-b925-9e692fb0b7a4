"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.votingManager = exports.VotingManager = void 0;
class VotingManager {
    constructor() {
        this.votingSessions = new Map();
        this.sessionVotes = new Map();
        this.sessionTimers = new Map();
    }
    createVotingSession(sessionId, type, title, description, options, timeLimit = 30, totalPlayers, hostId) {
        const session = {
            id: sessionId,
            type,
            title,
            description,
            options,
            timeLimit,
            totalPlayers,
            hostId,
            status: 'active',
            votes: [],
            result: null,
            createdAt: Date.now(),
            expiresAt: Date.now() + (timeLimit * 1000)
        };
        this.votingSessions.set(sessionId, session);
        this.sessionVotes.set(sessionId, []);
        const timer = setTimeout(() => {
            this.completeVoting(sessionId);
        }, timeLimit * 1000);
        this.sessionTimers.set(sessionId, timer);
        return session;
    }
    submitVote(sessionId, playerId, playerName, optionIndex) {
        const session = this.votingSessions.get(sessionId);
        if (!session || session.status !== 'active') {
            return false;
        }
        if (optionIndex < 0 || optionIndex >= session.options.length) {
            return false;
        }
        const votes = this.sessionVotes.get(sessionId) || [];
        const existingVoteIndex = votes.findIndex(vote => vote.playerId === playerId);
        const vote = {
            playerId,
            playerName,
            optionIndex,
            timestamp: Date.now()
        };
        if (existingVoteIndex >= 0) {
            votes[existingVoteIndex] = vote;
        }
        else {
            votes.push(vote);
        }
        this.sessionVotes.set(sessionId, votes);
        session.votes = votes;
        if (votes.length >= session.totalPlayers) {
            this.completeVoting(sessionId);
        }
        return true;
    }
    getVotingSession(sessionId) {
        const session = this.votingSessions.get(sessionId);
        if (!session)
            return null;
        const votes = this.sessionVotes.get(sessionId) || [];
        return {
            ...session,
            votes
        };
    }
    getPlayerVote(sessionId, playerId) {
        const votes = this.sessionVotes.get(sessionId) || [];
        const playerVote = votes.find(vote => vote.playerId === playerId);
        return playerVote ? playerVote.optionIndex : null;
    }
    completeVoting(sessionId) {
        const session = this.votingSessions.get(sessionId);
        if (!session)
            return null;
        const timer = this.sessionTimers.get(sessionId);
        if (timer) {
            clearTimeout(timer);
            this.sessionTimers.delete(sessionId);
        }
        const votes = this.sessionVotes.get(sessionId) || [];
        const result = this.calculateResult(session.options, votes);
        session.status = 'completed';
        session.result = result;
        session.votes = votes;
        return result;
    }
    skipVoting(sessionId, hostId) {
        const session = this.votingSessions.get(sessionId);
        if (!session || session.hostId !== hostId) {
            return null;
        }
        return this.completeVoting(sessionId);
    }
    calculateResult(options, votes) {
        const voteCounts = new Array(options.length).fill(0);
        const voteDetails = {};
        options.forEach((_, index) => {
            voteDetails[index] = [];
        });
        votes.forEach(vote => {
            if (vote.optionIndex >= 0 && vote.optionIndex < options.length) {
                voteCounts[vote.optionIndex]++;
                voteDetails[vote.optionIndex].push(vote);
            }
        });
        const maxVotes = Math.max(...voteCounts);
        const winningIndices = voteCounts
            .map((count, index) => ({ count, index }))
            .filter(item => item.count === maxVotes)
            .map(item => item.index);
        const winnerIndex = winningIndices[0];
        const isTie = winningIndices.length > 1;
        const result = {
            winnerIndex,
            winnerOption: options[winnerIndex],
            totalVotes: votes.length,
            voteCounts,
            voteDetails,
            isTie,
            tiedOptions: isTie ? winningIndices : undefined
        };
        return result;
    }
    getVotingStats(sessionId) {
        const session = this.getVotingSession(sessionId);
        if (!session)
            return null;
        const votes = session.votes;
        const totalVotes = votes.length;
        const voteCounts = new Array(session.options.length).fill(0);
        votes.forEach(vote => {
            if (vote.optionIndex >= 0 && vote.optionIndex < session.options.length) {
                voteCounts[vote.optionIndex]++;
            }
        });
        const votePercentages = voteCounts.map(count => totalVotes > 0 ? (count / totalVotes) * 100 : 0);
        const maxVotes = Math.max(...voteCounts);
        const topOptionIndex = voteCounts.indexOf(maxVotes);
        return {
            totalVotes,
            votePercentages,
            topOption: {
                index: topOptionIndex,
                votes: maxVotes,
                percentage: totalVotes > 0 ? (maxVotes / totalVotes) * 100 : 0
            }
        };
    }
    getActiveSessions() {
        return Array.from(this.votingSessions.values())
            .filter(session => session.status === 'active');
    }
    cleanupExpiredSessions() {
        const now = Date.now();
        const expiredSessions = [];
        this.votingSessions.forEach((session, sessionId) => {
            if (session.expiresAt < now && session.status === 'active') {
                this.completeVoting(sessionId);
                expiredSessions.push(sessionId);
            }
        });
    }
    removeSession(sessionId) {
        const timer = this.sessionTimers.get(sessionId);
        if (timer) {
            clearTimeout(timer);
            this.sessionTimers.delete(sessionId);
        }
        this.votingSessions.delete(sessionId);
        this.sessionVotes.delete(sessionId);
    }
    getTimeRemaining(sessionId) {
        const session = this.votingSessions.get(sessionId);
        if (!session || session.status !== 'active')
            return 0;
        const remaining = Math.max(0, session.expiresAt - Date.now());
        return Math.ceil(remaining / 1000);
    }
    hasPlayerVoted(sessionId, playerId) {
        const votes = this.sessionVotes.get(sessionId) || [];
        return votes.some(vote => vote.playerId === playerId);
    }
    getParticipationRate(sessionId) {
        const session = this.votingSessions.get(sessionId);
        if (!session)
            return 0;
        const votes = this.sessionVotes.get(sessionId) || [];
        return session.totalPlayers > 0 ? (votes.length / session.totalPlayers) * 100 : 0;
    }
    static createCategoryVotingOptions() {
        return [
            { label: 'Rock & Metal', value: 'rock', description: 'Hard rock, metal, punk', emoji: '🎸' },
            { label: 'Pop & Dance', value: 'pop', description: 'Pop hits, dance, electronic', emoji: '🎵' },
            { label: 'Hip Hop & R&B', value: 'hiphop', description: 'Rap, hip hop, R&B', emoji: '🎤' },
            { label: 'Classic & Jazz', value: 'classic', description: 'Classical, jazz, blues', emoji: '🎼' }
        ];
    }
    static createDecadeVotingOptions() {
        return [
            { label: '80s Hits', value: '80s', description: 'Music from the 1980s', emoji: '📻' },
            { label: '90s Classics', value: '90s', description: 'Music from the 1990s', emoji: '💿' },
            { label: '2000s Pop', value: '2000s', description: 'Music from the 2000s', emoji: '💽' },
            { label: '2010s & Beyond', value: '2010s', description: 'Modern hits', emoji: '📱' }
        ];
    }
    static createGameModeVotingOptions() {
        return [
            { label: 'Classic Quiz', value: 'classic', description: 'Traditional music quiz', emoji: '🎯' },
            { label: 'Speed Round', value: 'speed', description: 'Fast-paced questions', emoji: '⚡' },
            { label: 'Challenge Mode', value: 'challenge', description: 'Harder questions', emoji: '🏆' },
            { label: 'Collaborative', value: 'collaborative', description: 'Team-based play', emoji: '🤝' }
        ];
    }
}
exports.VotingManager = VotingManager;
exports.votingManager = new VotingManager();
//# sourceMappingURL=voting-manager.js.map