"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheDuration = void 0;
exports.withCacheHeaders = withCacheHeaders;
exports.cachedJsonResponse = cachedJsonResponse;
exports.handleConditionalRequest = handleConditionalRequest;
exports.batchQueries = batchQueries;
exports.createDebouncedHandler = createDebouncedHandler;
exports.withPerformanceTiming = withPerformanceTiming;
const server_1 = require("next/server");
exports.CacheDuration = {
    NONE: 0,
    SHORT: 60,
    MEDIUM: 300,
    LONG: 3600,
    VERY_LONG: 86400,
};
function withCacheHeaders(response, duration = exports.CacheDuration.MEDIUM, options = {}) {
    const { staleWhileRevalidate = duration * 2, private: isPrivate = false, immutable = false } = options;
    if (duration === 0) {
        response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
        return response;
    }
    const directives = [
        isPrivate ? 'private' : 'public',
        `max-age=${duration}`,
        `s-maxage=${duration}`,
        `stale-while-revalidate=${staleWhileRevalidate}`
    ];
    if (immutable) {
        directives.push('immutable');
    }
    response.headers.set('Cache-Control', directives.join(', '));
    if (!response.headers.has('ETag')) {
        const body = response.body;
        if (body) {
            const etag = `"${Date.now()}-${Math.random().toString(36).substr(2, 9)}"`;
            response.headers.set('ETag', etag);
        }
    }
    return response;
}
function cachedJsonResponse(data, duration = exports.CacheDuration.MEDIUM, options) {
    const response = server_1.NextResponse.json(data);
    return withCacheHeaders(response, duration, options);
}
function handleConditionalRequest(request, currentETag) {
    const ifNoneMatch = request.headers.get('If-None-Match');
    if (ifNoneMatch && ifNoneMatch === currentETag) {
        return new server_1.NextResponse(null, {
            status: 304,
            headers: {
                'ETag': currentETag,
                'Cache-Control': 'public, max-age=0, must-revalidate'
            }
        });
    }
    return null;
}
async function batchQueries(queries) {
    return Promise.all(queries);
}
function createDebouncedHandler(handler, delay = 100) {
    let timeoutId = null;
    let pendingPromise = null;
    return ((...args) => {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        if (!pendingPromise) {
            pendingPromise = new Promise((resolve) => {
                timeoutId = setTimeout(async () => {
                    try {
                        const result = await handler(...args);
                        resolve(result);
                    }
                    catch (error) {
                        resolve(server_1.NextResponse.json({ error: 'Request failed' }, { status: 500 }));
                    }
                    finally {
                        pendingPromise = null;
                        timeoutId = null;
                    }
                }, delay);
            });
        }
        return pendingPromise;
    });
}
function withPerformanceTiming(handler) {
    return async (req) => {
        const start = performance.now();
        try {
            const response = await handler(req);
            const duration = Math.round(performance.now() - start);
            response.headers.set('X-Response-Time', `${duration}ms`);
            response.headers.set('X-Server-Timing', `total;dur=${duration}`);
            return response;
        }
        catch (error) {
            const duration = Math.round(performance.now() - start);
            const errorResponse = server_1.NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
            errorResponse.headers.set('X-Response-Time', `${duration}ms`);
            errorResponse.headers.set('X-Server-Timing', `total;dur=${duration};desc="error"`);
            throw error;
        }
    };
}
//# sourceMappingURL=api-utils.js.map