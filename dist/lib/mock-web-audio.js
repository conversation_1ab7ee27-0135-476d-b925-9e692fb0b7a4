"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockAudioContext = void 0;
exports.loadTrackAndApplyEffects = loadTrackAndApplyEffects;
exports.updateEffectParameter = updateEffectParameter;
class MockAudioParam {
    constructor(defaultValue, minValue, maxValue) {
        this._defaultValue = defaultValue;
        this.value = defaultValue;
        this.minValue = minValue;
        this.maxValue = maxValue;
        console.log(`MockAudioParam created with default value: ${defaultValue}`);
    }
    setValueAtTime(value, startTime) {
        console.log(`MockAudioParam: setValueAtTime(${value}, ${startTime}) called. Current time: ${MockAudioContext.currentTime}`);
        if (MockAudioContext.currentTime >= startTime) {
            this.value = value;
        }
    }
    linearRampToValueAtTime(value, endTime) {
        console.log(`MockAudioParam: linearRampToValueAtTime(${value}, ${endTime}) called.`);
        this.value = value;
    }
    exponentialRampToValueAtTime(value, endTime) {
        console.log(`MockAudioParam: exponentialRampToValueAtTime(${value}, ${endTime}) called.`);
        this.value = value;
    }
    setTargetAtTime(target, startTime, timeConstant) {
        console.log(`MockAudioParam: setTargetAtTime(${target}, ${startTime}, ${timeConstant}) called.`);
        this.value = target;
    }
    get defaultValue() {
        return this._defaultValue;
    }
}
class MockAudioNode {
    constructor(context) {
        this.connectedTo = [];
        this.numberOfInputs = 1;
        this.numberOfOutputs = 1;
        this.context = context;
        console.log(`MockAudioNode (${this.constructor.name}) created.`);
    }
    connect(destinationNode, outputIndex, inputIndex) {
        if (destinationNode instanceof MockAudioNode) {
            console.log(`MockAudioNode (${this.constructor.name}): connect to ${destinationNode.constructor.name}`);
            this.connectedTo.push(destinationNode);
            return destinationNode;
        }
        else if (destinationNode instanceof MockAudioParam) {
            console.log(`MockAudioNode (${this.constructor.name}): connect to AudioParam of value ${destinationNode.value}`);
        }
    }
    disconnect(destination, output, input) {
        console.log(`MockAudioNode (${this.constructor.name}): disconnect called.`);
        if (destination instanceof MockAudioNode) {
            this.connectedTo = this.connectedTo.filter((node) => node !== destination);
        }
        else {
            this.connectedTo = [];
        }
    }
}
class MockAudioBufferSourceNode extends MockAudioNode {
    constructor(context) {
        super(context);
        this.buffer = null;
        this.loop = false;
        this.loopStart = 0;
        this.loopEnd = 0;
        this.onended = null;
        this._isPlaying = false;
        this.numberOfInputs = 0;
    }
    start(when, offset, duration) {
        console.log(`MockAudioBufferSourceNode: start(when=${when}, offset=${offset}, duration=${duration}) called.`);
        this._isPlaying = true;
        if (duration) {
            setTimeout(() => {
                this.stop();
                if (this.onended)
                    this.onended();
            }, duration * 1000);
        }
    }
    stop(when) {
        console.log(`MockAudioBufferSourceNode: stop(when=${when}) called.`);
        this._isPlaying = false;
    }
}
class MockGainNode extends MockAudioNode {
    constructor(context) {
        super(context);
        this.gain = new MockAudioParam(1, 0);
    }
}
class MockBiquadFilterNode extends MockAudioNode {
    constructor(context) {
        super(context);
        this.type = "lowpass";
        this.frequency = new MockAudioParam(350, 0, context.sampleRate / 2);
        this.Q = new MockAudioParam(1, 0.0001, 1000);
        this.gain = new MockAudioParam(0, -40, 40);
    }
    getFrequencyResponse(frequencyHz, magResponse, phaseResponse) {
        console.log("MockBiquadFilterNode: getFrequencyResponse called.");
        for (let i = 0; i < magResponse.length; i++)
            magResponse[i] = 1;
        for (let i = 0; i < phaseResponse.length; i++)
            phaseResponse[i] = 0;
    }
}
class MockPannerNode extends MockAudioNode {
    constructor(context) {
        super(context);
        this.panningModel = "equalpower";
        this.distanceModel = "inverse";
        this.refDistance = 1;
        this.maxDistance = 10000;
        this.rolloffFactor = 1;
        this.coneInnerAngle = 360;
        this.coneOuterAngle = 360;
        this.coneOuterGain = 0;
        this.pan = new MockAudioParam(0, -1, 1);
    }
    setPosition(x, y, z) {
        console.log(`MockPannerNode: setPosition(${x}, ${y}, ${z}) called.`);
    }
    setOrientation(x, y, z) {
        console.log(`MockPannerNode: setOrientation(${x}, ${y}, ${z}) called.`);
    }
}
class MockDelayNode extends MockAudioNode {
    constructor(context, maxDelayTime = 1.0) {
        super(context);
        this.delayTime = new MockAudioParam(0, 0, maxDelayTime);
    }
}
class MockConvolverNode extends MockAudioNode {
    constructor(context) {
        super(context);
        this.buffer = null;
        this.normalize = true;
    }
}
class MockAnalyserNode extends MockAudioNode {
    constructor(context) {
        super(context);
        this.fftSize = 2048;
        this.minDecibels = -100;
        this.maxDecibels = -30;
        this.smoothingTimeConstant = 0.8;
        this.frequencyBinCount = this.fftSize / 2;
    }
    getByteFrequencyData(array) {
        for (let i = 0; i < array.length; i++)
            array[i] = Math.random() * 255;
    }
    getFloatFrequencyData(array) {
        for (let i = 0; i < array.length; i++)
            array[i] = this.minDecibels + Math.random() * (this.maxDecibels - this.minDecibels);
    }
    getByteTimeDomainData(array) {
        for (let i = 0; i < array.length; i++)
            array[i] = 128 + (Math.random() - 0.5) * 255;
    }
}
class MockAudioContext {
    constructor() {
        this.sampleRate = 44100;
        this._state = "suspended";
        this.destination = new MockAudioNode(this);
        this.destination.constructor.name = "AudioDestinationNode";
        console.log("MockAudioContext created.");
        setInterval(() => {
            MockAudioContext.currentTime += 0.1;
        }, 100);
    }
    get state() {
        return this._state;
    }
    resume() {
        console.log("MockAudioContext: resume() called.");
        this._state = "running";
        return Promise.resolve();
    }
    suspend() {
        console.log("MockAudioContext: suspend() called.");
        this._state = "suspended";
        return Promise.resolve();
    }
    close() {
        console.log("MockAudioContext: close() called.");
        this._state = "closed";
        return Promise.resolve();
    }
    createBufferSource() {
        return new MockAudioBufferSourceNode(this);
    }
    createGain() {
        return new MockGainNode(this);
    }
    createBiquadFilter() {
        return new MockBiquadFilterNode(this);
    }
    createPanner() {
        return new MockPannerNode(this);
    }
    createDelay(maxDelayTime) {
        return new MockDelayNode(this, maxDelayTime);
    }
    createConvolver() {
        return new MockConvolverNode(this);
    }
    createAnalyser() {
        return new MockAnalyserNode(this);
    }
    decodeAudioData(audioData, successCallback, errorCallback) {
        console.log("MockAudioContext: decodeAudioData called.");
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                const mockBuffer = {
                    sampleRate: this.sampleRate,
                    length: this.sampleRate * 3,
                    duration: 3,
                    numberOfChannels: 2,
                    getChannelData: (channel) => new Float32Array(this.sampleRate * 3),
                    copyFromChannel: () => { },
                    copyToChannel: () => { },
                };
                if (successCallback)
                    successCallback(mockBuffer);
                resolve(mockBuffer);
            }, 50);
        });
    }
}
exports.MockAudioContext = MockAudioContext;
MockAudioContext.currentTime = 0;
let audioContext = null;
let sourceNode = null;
let gainNode = null;
let filterNode = null;
let pannerNode = null;
let delayNode = null;
let convolverNode = null;
async function loadTrackAndApplyEffects(track, effects) {
    if (!audioContext) {
        audioContext = new MockAudioContext();
    }
    await audioContext.resume();
    if (sourceNode) {
        try {
            sourceNode.stop();
        }
        catch (e) {
        }
        sourceNode.disconnect();
    }
    sourceNode = audioContext.createBufferSource();
    const mockDecodedBuffer = await audioContext.decodeAudioData(new ArrayBuffer(0), () => { });
    sourceNode.buffer = mockDecodedBuffer;
    sourceNode.loop = true;
    console.log(`Mock: Loaded track "${track.title}" into buffer source.`);
    let currentNode = sourceNode;
    if (effects.gain !== undefined) {
        if (!gainNode)
            gainNode = audioContext.createGain();
        gainNode.gain.setValueAtTime(effects.gain, audioContext.currentTime);
        currentNode.connect(gainNode);
        currentNode = gainNode;
        console.log(`Mock: Applied gain: ${effects.gain}`);
    }
    else if (gainNode) {
        gainNode.gain.setValueAtTime(gainNode.gain.defaultValue, audioContext.currentTime);
    }
    if (effects.filter) {
        if (!filterNode)
            filterNode = audioContext.createBiquadFilter();
        filterNode.type = effects.filter.type;
        filterNode.frequency.setValueAtTime(effects.filter.frequency, audioContext.currentTime);
        if (effects.filter.Q) {
            filterNode.Q.setValueAtTime(effects.filter.Q, audioContext.currentTime);
        }
        currentNode.connect(filterNode);
        currentNode = filterNode;
        console.log(`Mock: Applied filter: type=${effects.filter.type}, freq=${effects.filter.frequency}`);
    }
    else if (filterNode) {
        filterNode.type = "lowpass";
        filterNode.frequency.setValueAtTime(filterNode.frequency.defaultValue, audioContext.currentTime);
        filterNode.Q.setValueAtTime(filterNode.Q.defaultValue, audioContext.currentTime);
    }
    if (effects.pan !== undefined) {
        if (!pannerNode)
            pannerNode = audioContext.createPanner();
        pannerNode.pan.setValueAtTime(effects.pan, audioContext.currentTime);
        currentNode.connect(pannerNode);
        currentNode = pannerNode;
        console.log(`Mock: Applied pan: ${effects.pan}`);
    }
    else if (pannerNode) {
        pannerNode.pan.setValueAtTime(pannerNode.pan.defaultValue, audioContext.currentTime);
    }
    if (effects.delay) {
        if (!delayNode)
            delayNode = audioContext.createDelay();
        delayNode.delayTime.setValueAtTime(effects.delay.delayTime, audioContext.currentTime);
        currentNode.connect(delayNode);
        currentNode = delayNode;
        console.log(`Mock: Applied delay: ${effects.delay.delayTime}s`);
    }
    else if (delayNode) {
        delayNode.delayTime.setValueAtTime(delayNode.delayTime.defaultValue, audioContext.currentTime);
    }
    if (effects.reverb && effects.reverb.impulseResponseUrl) {
        if (!convolverNode)
            convolverNode = audioContext.createConvolver();
        console.log(`Mock: (Concept) Would load impulse response from ${effects.reverb.impulseResponseUrl}`);
        currentNode.connect(convolverNode);
        currentNode = convolverNode;
        console.log(`Mock: Applied reverb.`);
    }
    currentNode.connect(audioContext.destination);
    console.log("Mock: Effect chain connected to destination.");
    sourceNode.start(0);
    console.log(`Mock: Started playback of "${track.title}".`);
}
function updateEffectParameter(paramName, value) {
    if (!audioContext || audioContext.state !== "running") {
        console.warn("MockAudioContext not running. Cannot update parameters.");
        return;
    }
    console.log(`Mock: updateEffectParameter called for ${paramName} with value ${value}`);
    switch (paramName) {
        case "gain":
            if (gainNode)
                gainNode.gain.setValueAtTime(value, audioContext.currentTime);
            break;
        case "filter.frequency":
            if (filterNode)
                filterNode.frequency.setValueAtTime(value, audioContext.currentTime);
            break;
        case "filter.Q":
            if (filterNode)
                filterNode.Q.setValueAtTime(value, audioContext.currentTime);
            break;
        case "filter.type":
            if (filterNode)
                filterNode.type = value;
            break;
        case "pan":
            if (pannerNode)
                pannerNode.pan.setValueAtTime(value, audioContext.currentTime);
            break;
        case "delay.delayTime":
            if (delayNode)
                delayNode.delayTime.setValueAtTime(value, audioContext.currentTime);
            break;
        default:
            console.warn(`Mock: Unknown effect parameter: ${paramName}`);
    }
}
//# sourceMappingURL=mock-web-audio.js.map