"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildArtistOptions = buildArtistOptions;
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
async function buildArtistOptions(opts) {
    const total = opts.totalOptions ?? 4;
    const excludeSet = new Set([opts.correctArtist, ...(opts.exclude ?? [])]);
    const options = new Set();
    options.add(opts.correctArtist);
    if (opts.similarArtists && opts.similarArtists.length > 0) {
        for (const a of opts.similarArtists) {
            if (options.size >= total)
                break;
            if (!excludeSet.has(a)) {
                options.add(a);
                excludeSet.add(a);
            }
        }
    }
    if (options.size < total) {
        const needed = total - options.size;
        const randoms = await prisma_1.default.quizTrack.findMany({
            where: {
                artist: {
                    notIn: Array.from(excludeSet)
                }
            },
            select: { artist: true },
            distinct: ['artist'],
            take: needed * 3
        });
        for (const r of randoms) {
            if (options.size >= total)
                break;
            if (r.artist) {
                options.add(r.artist);
                excludeSet.add(r.artist);
            }
        }
    }
    const { RandomUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/utils')));
    return RandomUtils.shuffle(Array.from(options));
}
//# sourceMappingURL=question-engine.js.map