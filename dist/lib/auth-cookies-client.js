"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getClientUserInfo = getClientUserInfo;
exports.isAuthenticated = isAuthenticated;
exports.hasRole = hasRole;
function getClientUserInfo() {
    if (typeof window === 'undefined') {
        return null;
    }
    try {
        const userInfoCookie = document.cookie
            .split('; ')
            .find(row => row.startsWith('user_info='))
            ?.split('=')[1];
        if (!userInfoCookie) {
            return null;
        }
        return JSON.parse(decodeURIComponent(userInfoCookie));
    }
    catch (error) {
        console.error('Failed to parse user info:', error);
        return null;
    }
}
function isAuthenticated() {
    return !!getClientUserInfo();
}
function hasRole(role) {
    const userInfo = getClientUserInfo();
    if (!userInfo)
        return false;
    if (role === 'user')
        return true;
    if (role === 'dj')
        return userInfo.role === 'dj' || userInfo.role === 'superuser';
    if (role === 'superuser')
        return userInfo.role === 'superuser';
    return false;
}
//# sourceMappingURL=auth-cookies-client.js.map