"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileManager = void 0;
const PROFILE_KEY = 'jukebox-user-profile';
exports.ProfileManager = {
    getProfile() {
        if (typeof window === 'undefined') {
            return null;
        }
        try {
            const profileJson = localStorage.getItem(PROFILE_KEY);
            if (profileJson) {
                const profile = JSON.parse(profileJson);
                if (profile && profile.id && profile.name && profile.avatar) {
                    return profile;
                }
            }
        }
        catch (error) {
            console.error("Failed to parse user profile from localStorage", error);
        }
        return null;
    },
    saveProfile(profile) {
        if (typeof window === 'undefined') {
            return;
        }
        try {
            localStorage.setItem(PROFILE_KEY, JSON.stringify(profile));
        }
        catch (error) {
            console.error("Failed to save user profile to localStorage", error);
        }
    },
    hasProfile() {
        return this.getProfile() !== null;
    },
    generateProfile(name, avatar) {
        return {
            id: `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            name,
            displayName: name,
            avatar,
            soundPreference: 'default',
        };
    },
    createGuestProfile() {
        const guestProfile = {
            id: `guest-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            name: 'Guest',
            displayName: 'Guest',
            avatar: '👤',
            soundPreference: 'default',
            isGuest: true,
        };
        this.saveProfile(guestProfile);
        return guestProfile;
    },
    playAnswerSound(sound = 'default') {
        console.log(`Playing sound: ${sound}`);
    }
};
//# sourceMappingURL=profile-manager.js.map