import type { Player, GameState, Team, TeamGameSettings, VotingSession } from './types';
export interface GameConfig {
    gameId: string;
    hostId: string;
    gameMode: string;
    maxPlayers?: number;
    timePerQuestion?: number;
    teamMode?: boolean;
    teamSettings?: TeamGameSettings;
    enableVoting?: boolean;
}
export interface VotingPhase {
    type: 'category' | 'decade' | 'game-mode' | 'theme';
    title: string;
    description: string;
    sessionId?: string;
    isActive: boolean;
}
export declare class GameManager {
    private gameState;
    private votingEnabled;
    private currentVotingPhase;
    constructor(config: GameConfig);
    createGame(host: Player): GameState;
    addPlayer(player: Player): GameState;
    removePlayer(playerId: string): GameState;
    startGame(): GameState;
    submitAnswer(playerId: string, answerIndex: number, timeTaken: number): GameState;
    processQuestionResults(): GameState;
    nextQuestion(): GameState;
    endGame(): GameState;
    getGameState(): GameState;
    toggleTeamMode(enabled: boolean, settings?: TeamGameSettings): GameState;
    addTeam(team: Team): GameState;
    removeTeam(teamId: string): GameState;
    addPlayerToTeam(playerId: string, teamId: string): GameState;
    removePlayerFromTeam(playerId: string): GameState;
    submitTeamAnswer(teamId: string, answerIndex: number, timeTaken: number, submittedBy: string): GameState;
    processTeamScoring(): GameState;
    getTeamsLeaderboard(): Team[];
    private removePlayerFromAllTeams;
    private calculateBasePoints;
    private generateMockQuestions;
    allPlayersAnswered(): boolean;
    getLeaderboard(): Player[];
    startVotingPhase(type: VotingPhase['type'], socket: any, customOptions?: {
        title?: string;
        description?: string;
    }): VotingPhase | null;
    completeVotingPhase(): {
        result: any;
        nextAction: string;
    } | null;
    getCurrentVotingSession(): VotingSession | null;
    hasPlayerVoted(playerId: string): boolean;
    getPlayerVote(playerId: string): number | null;
    shouldTriggerVoting(questionIndex: number): boolean;
    getVotingType(questionIndex: number): VotingPhase['type'];
    getCurrentVotingPhase(): VotingPhase | null;
    setVotingEnabled(enabled: boolean): void;
    private getDefaultVotingTitle;
    private getDefaultVotingDescription;
    private applyCategory;
    private applyDecade;
    private applyGameMode;
    cleanupVoting(): void;
}
