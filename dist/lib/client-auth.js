"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientTokenManager = void 0;
class ClientTokenManager {
    static getToken() {
        if (typeof window === 'undefined')
            return null;
        return localStorage.getItem(this.TOKEN_KEY);
    }
    static setToken(token) {
        if (typeof window === 'undefined')
            return;
        localStorage.setItem(this.TOKEN_KEY, token);
    }
    static removeToken() {
        if (typeof window === 'undefined')
            return;
        localStorage.removeItem(this.TOKEN_KEY);
        localStorage.removeItem(this.USER_KEY);
    }
    static getStoredUser() {
        if (typeof window === 'undefined')
            return null;
        try {
            const userData = localStorage.getItem(this.USER_KEY);
            return userData ? JSON.parse(userData) : null;
        }
        catch {
            return null;
        }
    }
    static setStoredUser(user) {
        if (typeof window === 'undefined')
            return;
        localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
}
exports.ClientTokenManager = ClientTokenManager;
ClientTokenManager.TOKEN_KEY = 'auth_token';
ClientTokenManager.USER_KEY = 'current_user';
//# sourceMappingURL=client-auth.js.map