export interface TokenPayload {
    id: string;
    email: string;
    username: string;
    role: string;
}
export declare class CookieAuth {
    static readonly TOKEN_NAME = "auth_token";
    static readonly USER_INFO_NAME = "user_info";
    static setAuthCookie(payload: TokenPayload): Promise<void>;
    static getAuthFromCookie(): Promise<TokenPayload | null>;
    static clearAuthCookies(): Promise<void>;
    static refreshToken(): Promise<boolean>;
}
export declare function getClientUserInfo(): {
    id: string;
    username: string;
    role: string;
} | null;
