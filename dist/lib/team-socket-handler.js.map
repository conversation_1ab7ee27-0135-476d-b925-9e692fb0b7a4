{"version": 3, "file": "team-socket-handler.js", "sourceRoot": "", "sources": ["../../lib/team-socket-handler.ts"], "names": [], "mappings": ";;;AAMA,iDAA4C;AA8B5C,MAAa,iBAAiB;IAI5B;QAHQ,qBAAgB,GAAkC,IAAI,GAAG,EAAE,CAAA;QAC3D,kBAAa,GAAmC,IAAI,GAAG,EAAE,CAAA;IAElD,CAAC;IAKhB,gBAAgB,CAAC,MAAc,EAAE,QAAgB,EAAE,UAAkB,EAAE,MAAc;QAEnF,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE;YAChC,IAAI,CAAC;gBACH,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAA;gBACvC,MAAM,IAAI,GAAG,0BAAW,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;gBAEnE,IAAI,IAAI,EAAE,CAAC;oBAET,MAAM,UAAU,GAAG,GAAG,MAAM,SAAS,IAAI,CAAC,EAAE,EAAE,CAAA;oBAC9C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACzB,CAAC;gBAGD,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,CAAC,CAAA;gBAChD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,CAAC,CAAA;gBAErC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAC1C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAA;YAC5D,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,EAAE;YAC9B,IAAI,CAAC;gBACH,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;gBACvB,MAAM,MAAM,GAAW;oBACrB,EAAE,EAAE,QAAQ;oBACZ,IAAI,EAAE,UAAU;oBAChB,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,KAAK;oBACb,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;iBACrB,CAAA;gBAED,MAAM,OAAO,GAAG,0BAAW,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;gBAC3D,IAAI,OAAO,EAAE,CAAC;oBACZ,MAAM,IAAI,GAAG,0BAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;oBACxC,IAAI,IAAI,EAAE,CAAC;wBAET,MAAM,UAAU,GAAG,GAAG,MAAM,SAAS,MAAM,EAAE,CAAA;wBAC7C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;wBAEvB,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAA;wBAChE,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAA;wBACrD,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;oBAC1C,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC,CAAA;gBAC1D,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC,CAAA;YAC1D,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,0BAAW,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAA;gBAEvD,IAAI,IAAI,EAAE,CAAC;oBAET,MAAM,UAAU,GAAG,GAAG,MAAM,SAAS,IAAI,CAAC,EAAE,EAAE,CAAA;oBAC9C,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;oBAExB,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAA;oBACzE,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAA;gBAChE,CAAC;gBAED,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAC1C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAA;YAC3D,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE;YAChC,IAAI,CAAC;gBACH,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAA;gBAC3C,MAAM,IAAI,GAAG,0BAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;gBAGxC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;oBACxC,MAAM,OAAO,GAAQ,EAAE,CAAA;oBACvB,IAAI,IAAI;wBAAE,OAAO,CAAC,IAAI,GAAG,IAAI,CAAA;oBAC7B,IAAI,KAAK;wBAAE,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;oBAChC,IAAI,KAAK;wBAAE,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;oBAEhC,MAAM,OAAO,GAAG,0BAAW,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;oBACvD,IAAI,OAAO,EAAE,CAAC;wBACZ,MAAM,WAAW,GAAG,0BAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;wBAC/C,IAAI,WAAW,EAAE,CAAC;4BAChB,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAA;4BAC7D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAA;wBACpD,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC,CAAA;gBACxE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAA;YAC5D,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,IAAI,EAAE,EAAE;YACvC,IAAI,CAAC;gBACH,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAA;gBAC/C,MAAM,IAAI,GAAG,0BAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;gBAGxC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;oBACxC,MAAM,OAAO,GAAG,0BAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;oBAClE,IAAI,OAAO,EAAE,CAAC;wBACZ,MAAM,WAAW,GAAG,0BAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;wBAC/C,IAAI,WAAW,EAAE,CAAC;4BAChB,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAA;4BAC7D,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAA;wBACpD,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC,CAAA;gBAC5E,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC,CAAA;YAChE,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,EAAE;YAC9B,IAAI,CAAC;gBACH,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAA;gBAChC,MAAM,IAAI,GAAG,0BAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;gBAEhD,IAAI,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,EAAE,CAAC;oBAC/B,MAAM,WAAW,GAAoB;wBACnC,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;wBAClE,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,QAAQ;wBAClB,UAAU,EAAE,UAAU;wBACtB,OAAO,EAAE,OAAO;wBAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;wBACrB,IAAI,EAAE,QAAQ;qBACf,CAAA;oBAGD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;wBACpC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;oBACpC,CAAC;oBACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;oBAGjD,MAAM,UAAU,GAAG,GAAG,MAAM,SAAS,MAAM,EAAE,CAAA;oBAC7C,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAA;oBACzE,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAA;gBAC5D,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAA;gBAC/D,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC,CAAA;YAClE,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE;YAChC,IAAI,CAAC;gBACH,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAA;gBAC9C,MAAM,IAAI,GAAG,0BAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;gBAEhD,IAAI,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,EAAE,CAAC;oBAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,aAAa,CAAA;oBAE9D,IAAI,SAAS,EAAE,CAAC;wBACd,MAAM,UAAU,GAAe;4BAC7B,MAAM,EAAE,MAAM;4BACd,MAAM,EAAE,MAAM;4BACd,WAAW,EAAE,QAAQ;4BACrB,aAAa,EAAE,aAAa,IAAI,KAAK;4BACrC,cAAc,EAAE,CAAC;4BACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;yBACtB,CAAA;wBAED,MAAM,OAAO,GAAG,0BAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;wBACxD,IAAI,OAAO,EAAE,CAAC;4BACZ,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,UAAU,EAAE,CAAC,CAAA;4BAC/D,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,UAAU,EAAE,CAAC,CAAA;wBACtD,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,mDAAmD,EAAE,CAAC,CAAA;oBACxF,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAA;gBAC/D,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC,CAAA;YACnE,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,CAAC,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YACnC,IAAI,CAAC;gBAGH,MAAM,KAAK,GAAG,0BAAW,CAAC,WAAW,EAAE,CAAA;gBACvC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;gBACnD,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;YAC1C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAA;YAC9D,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE,EAAE;YACrC,IAAI,CAAC;gBACH,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAA;gBAElC,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;gBAC7C,CAAC;gBAED,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAA;gBAClE,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAA;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAA;YACjE,CAAC;QACH,CAAC,CAAC,CAAA;QAGF,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE;YACjC,IAAI,CAAC;gBACH,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;gBACvB,MAAM,IAAI,GAAG,0BAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;gBAGxC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;oBACxC,MAAM,OAAO,GAAG,0BAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;oBAC/C,IAAI,OAAO,EAAE,CAAC;wBACZ,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;wBACpD,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;wBACzC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;oBAC1C,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC,CAAA;gBACzE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAA;YAC7D,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAKD,mBAAmB,CAAC,QAAgB,EAAE,MAAc;QAClD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,0BAAW,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAA;QAEzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAA;QAC5D,CAAC;IACH,CAAC;IAKD,mBAAmB,CAAC,MAAc;QAChC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;IAC7C,CAAC;IAKD,eAAe,CAAC,MAAc;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAC1C,CAAC;IAKD,mBAAmB,CAAC,MAAc,EAAE,aAAqB,EAAE,MAAc;QACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAClD,IAAI,CAAC,QAAQ;YAAE,OAAM;QAIrB,MAAM,KAAK,GAAG,0BAAW,CAAC,kBAAkB,EAAE,CAAA;IAEhD,CAAC;IAKD,wBAAwB,CAAC,MAAc;QACrC,0BAAW,CAAC,gBAAgB,EAAE,CAAA;IAChC,CAAC;IAKO,mBAAmB,CAAC,MAAc,EAAE,MAAc;QACxD,MAAM,KAAK,GAAG,0BAAW,CAAC,kBAAkB,EAAE,CAAA;QAC9C,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;QACrD,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;IAC5C,CAAC;IAKD,WAAW,CAAC,MAAc;QACxB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACpC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IACnC,CAAC;CACF;AApUD,8CAoUC;AAEY,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAA"}