export interface AudioStatusCache {
    status: any;
    timestamp: number;
    ttl: number;
}
export interface OptimizedAudioStatus {
    isPlaying: boolean;
    currentTrack: any | null;
    volume: number;
    progress: number;
    duration: number;
    cached: boolean;
}
export declare class AudioPerformanceOptimizer {
    private statusCache;
    private readonly CACHE_TTL;
    private readonly SLOW_OPERATION_THRESHOLD;
    private pendingOperations;
    getOptimizedAudioStatus(audioManager: any): Promise<OptimizedAudioStatus>;
    private getAudioStatusWithTimeout;
    getQuickAudioStatus(audioManager: any): Promise<OptimizedAudioStatus>;
    preloadAudioStatus(audioManager: any): void;
    private getFallbackStatus;
    clearCache(): void;
    isAudioSystemResponsive(audioManager: any): Promise<boolean>;
    getPerformanceMetrics(): {
        cacheAge: number | null;
        hasPendingOperations: boolean;
        pendingOperationCount: number;
    };
}
export declare const audioPerformanceOptimizer: AudioPerformanceOptimizer;
