export interface AlbumArtResult {
    url: string;
    source: string;
    quality: 'high' | 'medium' | 'low';
    dimensions?: {
        width: number;
        height: number;
    };
}
export declare class EnhancedAlbumArtScraper {
    private automation;
    private cacheDir;
    constructor(cacheDir?: string);
    initialize(): Promise<void>;
    close(): Promise<void>;
    private getCacheKey;
    searchAlbumArt(artist: string, album: string): Promise<AlbumArtResult[]>;
    private scrapeMusicBrainz;
    private scrapeLastFm;
    private scrapeDiscogs;
    private scrapeGoogleImages;
    downloadAndProcessImage(imageUrl: string, outputPath: string, options?: {
        width?: number;
        height?: number;
        format?: 'jpeg' | 'png' | 'webp';
        quality?: number;
    }): Promise<void>;
    validateImageQuality(imagePath: string): Promise<{
        isValid: boolean;
        width?: number;
        height?: number;
        format?: string;
    }>;
}
export declare function enhancedAlbumArtSearch(artist: string, album: string, downloadPath?: string): Promise<AlbumArtResult[]>;
