export interface AuthUser {
    id: string;
    email: string;
    username: string;
    displayName?: string;
    role: 'user' | 'dj' | 'superuser';
    avatarUrl?: string;
    preferences: string;
    lastLogin?: Date;
    createdAt: Date;
}
export declare class AuthClient {
    static isAuthenticated(): boolean;
    static getCurrentUser(): {
        id: string;
        username: string;
        role: string;
    } | null;
    static validateSession(): Promise<{
        success: boolean;
        user?: AuthUser;
    }>;
    static login(email: string, password: string): Promise<{
        success: boolean;
        user?: AuthUser;
        message?: string;
    }>;
    static register(data: {
        email: string;
        password: string;
        displayName?: string;
    }): Promise<{
        success: boolean;
        user?: AuthUser;
        message?: string;
    }>;
    static logout(): Promise<void>;
    static getProfile(): Promise<{
        success: boolean;
        user?: AuthUser;
    }>;
    static updateProfile(updates: {
        username?: string;
        displayName?: string;
        avatarUrl?: string;
        favoriteGenres?: string[];
        location?: string;
    }): Promise<{
        success: boolean;
        user?: AuthUser;
    }>;
    static hasRole(role: 'user' | 'dj' | 'superuser'): boolean;
    static isAdmin(): boolean;
    static isDJOrAdmin(): boolean;
}
