import { AudioManager } from './audio-manager';
import type { MPDConnectionConfig } from './types';
export declare class EnhancedAudioManager extends AudioManager {
    private crossfadeConfig;
    private normalizationConfig;
    private audioContext;
    private gainNode;
    private currentSource;
    private nextAudio;
    private crossfading;
    constructor(config: MPDConnectionConfig);
    initialize(): Promise<void>;
    setCrossfadeEnabled(enabled: boolean): void;
    setCrossfadeDuration(seconds: number): void;
    setNormalizationEnabled(enabled: boolean): void;
    skip(direction: 'forward' | 'back'): Promise<void>;
    private crossfadeToNext;
    private getNextTrackUrl;
    private calculateNormalizationGain;
    private sendMPDCommand;
    applyNormalization(): Promise<void>;
    setVolume(volume: number): void;
    cleanup(): void;
}
