export interface Track {
    id: string;
    title: string;
    artist: string;
    album: string;
    year: number;
    duration: number;
    genre: string;
    subgenre: string;
    peakPosition: number;
    chartType: string;
    weeksOnChart: number;
    bpm: number;
    key: string;
    energy: number;
    danceability: number;
    acousticness: number;
    decade: string;
    country: string;
    language: string;
    recordLabel: string;
    audioUrl: string;
    albumArtUrl: string;
    artistImageUrl?: string;
    trivia?: string[];
}
export declare const musicDatabase: Track[];
