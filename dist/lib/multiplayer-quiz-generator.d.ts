import type { ContentFilters } from './types/filters';
export interface MultiplayerQuestion {
    id: string;
    type: 'multiple-choice' | 'slider' | 'visual';
    question: string;
    options?: string[];
    correctAnswer: string | number;
    category: string;
    timeLimit: number;
    explanation?: string;
    track?: {
        id: number;
        file: string;
        title: string;
        artist: string;
        album?: string;
        year?: number;
        previewStart?: number;
        albumArtUrl?: string;
        genre?: string;
        duration?: number;
        popularityScore?: number;
        chartPosition?: number;
        releaseDate?: string;
        triviaFacts?: string[];
        interestingFacts?: any;
        calculatedGain?: number | null;
        lufsVolume?: number | null;
    };
    minValue?: number;
    maxValue?: number;
}
export declare class MultiplayerQuizGenerator {
    private prisma;
    private quizDataManager;
    constructor();
    private createTrackObject;
    generateQuiz(settings: {
        gameMode: string;
        totalQuestions: number;
        timePerQuestion?: number;
        filters?: ContentFilters;
        ultimoteConfig?: any;
    }): Promise<MultiplayerQuestion[]>;
    private generateDatabaseQuestions;
    private shuffleArray;
    private buildWhereClause;
    private generateUltimoteQuestions;
    private generateGeneralKnowledgeQuestions;
    private generateCategoryQuestions;
    private generateSingleQuestion;
    private getCategoryDisplayName;
    cleanup(): Promise<void>;
}
