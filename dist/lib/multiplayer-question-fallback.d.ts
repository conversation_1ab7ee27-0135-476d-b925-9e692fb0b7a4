export interface FallbackQuestion {
    id: string;
    type: 'multiple-choice' | 'true-false' | 'slider';
    question: string;
    options?: string[];
    correctAnswer: string | number;
    track?: {
        id: string;
        title: string;
        artist: string;
        album: string;
        year: number;
        file: string;
    };
    timeLimit: number;
    points: number;
    difficulty: number;
}
export declare class MultiplayerQuestionFallback {
    private fallbackQuestions;
    getFallbackQuestions(count?: number): FallbackQuestion[];
    generateEmergencyQuestion(): FallbackQuestion;
    checkAndProvideFallback(multiplayerQuestions: any[], isMultiplayer: boolean, quizState: string, gameCode: string | null): {
        needsFallback: boolean;
        fallbackQuestions: FallbackQuestion[];
        reason: string;
    };
    createMockMultiplayerQuestion(fallbackQuestion: FallbackQuestion): any;
    emergencyQuizRecovery(setMultiplayerQuestions: (questions: any[]) => void, setMultiplayerCurrentQuestion: (question: any) => void, setQuizState: (state: string) => void, setQuizSubState: (substate: string) => void): void;
    addDiagnosticInfo(question: FallbackQuestion): FallbackQuestion & {
        diagnostic: any;
    };
    validateQuestion(question: any): boolean;
    getQuizContinuationStrategy(currentQuestionIndex: number, totalQuestions: number, multiplayerQuestions: any[]): {
        action: 'continue' | 'extend' | 'complete';
        newQuestions?: FallbackQuestion[];
        message: string;
    };
}
export declare const multiplayerQuestionFallback: MultiplayerQuestionFallback;
