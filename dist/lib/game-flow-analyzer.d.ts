export interface GameModeConfig {
    mode: 'classic' | 'quick-fire' | 'audio-tricks' | 'album-art' | 'audio-fingerprint' | 'chart-position' | 'decade-challenge' | 'genre-specialist' | 'general-knowledge' | 'ultimote';
    roundsPerGame: number;
    secondsPerRound: number;
    categories?: string[];
    difficulty?: 'easy' | 'medium' | 'hard';
    customSettings?: Record<string, any>;
}
export interface QuestionData {
    questionNumber: number;
    questionText: string;
    category?: string;
    options: string[];
    correctAnswer?: string;
    timeStarted: number;
    timeAnswered?: number;
    responseTime?: number;
    playerAnswers: Map<string, {
        answer: string;
        time: number;
    }>;
}
export interface RoundData {
    roundNumber: number;
    startTime: number;
    endTime?: number;
    duration?: number;
    questions: QuestionData[];
    scores: Map<string, number>;
}
export interface GameFlowAnalysis {
    gameMode: string;
    configuredRounds: number;
    actualRounds: number;
    configuredSecondsPerRound: number;
    averageSecondsPerRound: number;
    totalGameDuration: number;
    rounds: RoundData[];
    categoryDistribution: Map<string, number>;
    questionTypes: Map<string, number>;
    playerPerformance: Map<string, PlayerPerformance>;
    timingAccuracy: {
        roundTimingVariance: number;
        totalTimingAccuracy: number;
    };
    validationResults: ValidationResult[];
}
export interface PlayerPerformance {
    playerId: string;
    totalScore: number;
    correctAnswers: number;
    averageResponseTime: number;
    fastestResponse: number;
    slowestResponse: number;
}
export interface ValidationResult {
    check: string;
    expected: any;
    actual: any;
    passed: boolean;
    message?: string;
}
export declare class GameFlowAnalyzer {
    private automation;
    private currentRound;
    private rounds;
    private gameStartTime;
    private config;
    private pages;
    constructor(config: GameModeConfig);
    initialize(): Promise<void>;
    close(): Promise<void>;
    analyzeGameFlow(playerCount?: number): Promise<GameFlowAnalysis>;
    private setupGame;
    private handleOnboarding;
    private monitorGame;
    private monitorRound;
    private captureQuestionData;
    private monitorPlayerAnswers;
    private waitForAnswers;
    private checkRoundEnded;
    private checkGameEnded;
    private getPlayerScore;
    private identifyQuestionType;
    private validateGameBehavior;
    private validateGameModeRules;
}
export declare function analyzeGameMode(config: GameModeConfig, playerCount?: number): Promise<GameFlowAnalysis>;
