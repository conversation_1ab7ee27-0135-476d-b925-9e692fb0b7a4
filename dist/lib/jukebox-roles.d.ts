import type { UserRole, RolePermissions, JukeboxUser } from './types';
export declare const ROLE_PERMISSIONS: Record<UserRole, RolePermissions>;
export declare const ROLE_INFO: Record<UserRole, {
    name: string;
    color: string;
    description: string;
}>;
export declare class JukeboxRoleManager {
    private static readonly STORAGE_KEY;
    private static readonly DEFAULT_ROLE;
    static getUserRole(): UserRole;
    static setUserRole(role: UserRole): void;
    static getPermissions(role: UserRole): RolePermissions;
    static hasPermission(role: UserRole, permission: keyof RolePermissions): boolean;
    static getRoleInfo(role: UserRole): {
        name: string;
        color: string;
        description: string;
    };
    static createJukeboxUser(profile: {
        id: string;
        name: string;
        avatar: string;
    }, role?: UserRole): JukeboxUser;
    static getAllRoles(): UserRole[];
}
