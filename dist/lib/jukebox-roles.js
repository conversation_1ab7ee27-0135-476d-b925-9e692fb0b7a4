"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JukeboxRoleManager = exports.ROLE_INFO = exports.ROLE_PERMISSIONS = void 0;
exports.ROLE_PERMISSIONS = {
    user: {
        canPlay: false,
        canPause: false,
        canSkip: false,
        canAdjustVolume: false,
        canManageQueue: false,
        canAddToLibrary: false,
        canRemoveFromLibrary: false,
        canApproveSuggestions: false,
        canRejectSuggestions: false,
        canManageUsers: false,
        canVote: true,
        canSuggest: true,
        canSearch: true,
    },
    dj: {
        canPlay: true,
        canPause: true,
        canSkip: true,
        canAdjustVolume: true,
        canManageQueue: true,
        canAddToLibrary: true,
        canRemoveFromLibrary: false,
        canApproveSuggestions: true,
        canRejectSuggestions: true,
        canManageUsers: false,
        canVote: true,
        canSuggest: true,
        canSearch: true,
    },
    superuser: {
        canPlay: true,
        canPause: true,
        canSkip: true,
        canAdjustVolume: true,
        canManageQueue: true,
        canAddToLibrary: true,
        canRemoveFromLibrary: true,
        canApproveSuggestions: true,
        canRejectSuggestions: true,
        canManageUsers: true,
        canVote: true,
        canSuggest: true,
        canSearch: true,
    },
};
exports.ROLE_INFO = {
    user: {
        name: 'Benutzer',
        color: 'bg-gray-500',
        description: 'Kann suchen, abstimmen und Songs vorschlagen',
    },
    dj: {
        name: 'DJ',
        color: 'bg-blue-500',
        description: 'Kann Musik steuern, Warteschlange verwalten und Vorschläge genehmigen',
    },
    superuser: {
        name: 'Admin',
        color: 'bg-red-500',
        description: 'Vollzugriff auf alle Jukebox-Funktionen',
    },
};
class JukeboxRoleManager {
    static getUserRole() {
        if (typeof window === 'undefined')
            return this.DEFAULT_ROLE;
        const stored = localStorage.getItem(this.STORAGE_KEY);
        if (stored && ['user', 'dj', 'superuser'].includes(stored)) {
            return stored;
        }
        return this.DEFAULT_ROLE;
    }
    static setUserRole(role) {
        if (typeof window === 'undefined')
            return;
        localStorage.setItem(this.STORAGE_KEY, role);
    }
    static getPermissions(role) {
        return exports.ROLE_PERMISSIONS[role];
    }
    static hasPermission(role, permission) {
        return exports.ROLE_PERMISSIONS[role][permission];
    }
    static getRoleInfo(role) {
        return exports.ROLE_INFO[role];
    }
    static createJukeboxUser(profile, role) {
        return {
            id: profile.id,
            name: profile.name,
            avatar: profile.avatar,
            role: role || this.getUserRole(),
            joinedAt: Date.now(),
        };
    }
    static getAllRoles() {
        return ['user', 'dj', 'superuser'];
    }
}
exports.JukeboxRoleManager = JukeboxRoleManager;
JukeboxRoleManager.STORAGE_KEY = 'jukebox_user_role';
JukeboxRoleManager.DEFAULT_ROLE = 'user';
//# sourceMappingURL=jukebox-roles.js.map