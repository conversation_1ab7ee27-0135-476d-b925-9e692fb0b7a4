import type { GameState } from './types';
export declare class GameStore {
    private games;
    generateGamePin(): string;
    setGame(gameId: string, game: GameState): void;
    getGame(gameId: string): GameState | undefined;
    getAvailableGames(): GameState[];
    deleteGame(gameId: string): boolean;
    getGamesByStatus(status: string): GameState[];
    cleanupOldGames(maxAgeMs?: number): void;
    getStats(): {
        totalGames: number;
        waitingGames: number;
        activeGames: number;
        completedGames: number;
        totalPlayers: number;
    };
}
