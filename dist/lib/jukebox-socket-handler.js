"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JukeboxSocketHandler = void 0;
const skip_vote_manager_1 = require("./skip-vote-manager");
const mpd_client_1 = require("./mpd-client");
const env_1 = require("./env");
const auth_service_1 = require("./auth-service");
const mpd_status_cache_1 = require("./mpd-status-cache");
class JukeboxSocketHandler {
    constructor(io) {
        this.statusPollingInterval = null;
        this.currentTrack = null;
        this.io = io;
        this.skipVoteManager = new skip_vote_manager_1.SkipVoteManager(io);
        const config = (0, env_1.getAudioConfig)();
        this.mpdClient = new mpd_client_1.MPDClient({
            host: config.mpdHost,
            port: config.mpdPort,
            password: config.mpdPassword,
            httpProxyPort: config.mpdHttpPort
        });
        this.setupNamespace();
        this.startStatusPolling();
    }
    setupNamespace() {
        const jukeboxNamespace = this.io.of('/jukebox');
        jukeboxNamespace.on('connection', (socket) => {
            console.log(`🎵 Jukebox client connected: ${socket.id}`);
            socket.on('authenticate', async (data) => {
                try {
                    let userInfo = null;
                    if (data.token) {
                        userInfo = await auth_service_1.AuthService.verifyToken(data.token);
                    }
                    else if (data.userId) {
                        userInfo = {
                            id: data.userId,
                            role: data.role || 'user'
                        };
                    }
                    if (userInfo) {
                        socket.data.userId = userInfo.id;
                        socket.data.userName = userInfo.username || userInfo.id;
                        socket.data.role = userInfo.role;
                        this.skipVoteManager.setConnectedUser(userInfo.id, true);
                        await this.sendCurrentStatus(socket);
                        socket.emit('authenticated', { success: true, user: userInfo });
                    }
                    else {
                        socket.emit('authenticated', { success: false, error: 'Invalid token' });
                    }
                }
                catch (error) {
                    console.error('Authentication error:', error);
                    socket.emit('authenticated', { success: false, error: 'Authentication failed' });
                }
            });
            socket.on('skip-vote', async (data) => {
                try {
                    console.log('Received skip vote:', data);
                    if (!socket.data.userId) {
                        socket.emit('skip-vote-error', { message: 'Not authenticated' });
                        return;
                    }
                    const status = await this.skipVoteManager.addVote(socket.data.userId, socket.data.userName || 'Anonymous');
                    console.log('Skip vote added, current status:', {
                        votes: status.totalVotes,
                        needed: status.votesNeeded,
                        percentage: status.percentage
                    });
                }
                catch (error) {
                    console.error('Skip vote error:', error);
                    socket.emit('error', { message: 'Failed to register vote' });
                }
            });
            socket.on('get-skip-vote-status', () => {
                const status = this.skipVoteManager.getVoteStatus();
                socket.emit('skip-vote-update', status);
            });
            socket.on('disconnect', () => {
                console.log(`🎵 Jukebox client disconnected: ${socket.id}`);
                if (socket.data.userId) {
                    this.skipVoteManager.setConnectedUser(socket.data.userId, false);
                }
            });
            socket.on('mpd-control', async (data) => {
                if (!socket.data.role || !['dj', 'superuser'].includes(socket.data.role)) {
                    socket.emit('error', { message: 'Insufficient permissions' });
                    return;
                }
                try {
                    await this.handleMPDControl(data.action, data.params);
                    await this.broadcastStatus();
                }
                catch (error) {
                    console.error('MPD control error:', error);
                    socket.emit('error', { message: `Failed to execute ${data.action}` });
                }
            });
        });
    }
    async handleMPDControl(action, params) {
        await this.mpdClient.connect();
        try {
            switch (action) {
                case 'play':
                    await this.mpdClient.play();
                    break;
                case 'pause':
                    await this.mpdClient.pause();
                    break;
                case 'next':
                    await this.mpdClient.nextTrack();
                    break;
                case 'previous':
                    await this.mpdClient.previousTrack();
                    break;
                case 'volume':
                    if (params?.volume !== undefined) {
                        await this.mpdClient.setVolume(params.volume);
                    }
                    break;
                case 'seek':
                    if (params?.position !== undefined) {
                        await this.mpdClient.seek(params.position);
                    }
                    break;
                default:
                    throw new Error(`Unknown action: ${action}`);
            }
        }
        finally {
            await this.mpdClient.disconnect();
        }
    }
    async startStatusPolling() {
        this.statusPollingInterval = setInterval(async () => {
            try {
                await this.broadcastStatus();
            }
            catch (error) {
                console.error('Status polling error:', error);
            }
        }, 5000);
    }
    async broadcastStatus() {
        try {
            const cache = mpd_status_cache_1.MPDStatusCache.getInstance();
            const cached = cache.get();
            let status, currentSong, queue;
            if (cached) {
                status = cached.status;
                currentSong = cached.currentSong;
                await this.mpdClient.connect();
                queue = await this.mpdClient.getCurrentPlaylist();
                await this.mpdClient.disconnect();
            }
            else {
                await this.mpdClient.connect();
                status = await this.mpdClient.getStatus();
                currentSong = await this.mpdClient.getCurrentSong();
                queue = await this.mpdClient.getCurrentPlaylist();
                await this.mpdClient.disconnect();
                cache.set(status, currentSong);
            }
            if (currentSong) {
                const trackId = currentSong.id?.toString() || currentSong.file;
                if (!this.currentTrack || this.currentTrack.id !== trackId) {
                    this.currentTrack = {
                        id: trackId,
                        title: currentSong.title || 'Unknown Title',
                        artist: currentSong.artist || 'Unknown Artist'
                    };
                    this.skipVoteManager.setCurrentTrack(trackId, this.currentTrack.title, this.currentTrack.artist);
                }
            }
            this.io.of('/jukebox').emit('mpd-status', {
                status,
                currentSong,
                queue,
                timestamp: Date.now()
            });
        }
        catch (error) {
            console.error('Failed to broadcast MPD status:', error);
        }
    }
    async sendCurrentStatus(socket) {
        try {
            const cache = mpd_status_cache_1.MPDStatusCache.getInstance();
            const cached = cache.get();
            let status, currentSong, queue;
            if (cached) {
                status = cached.status;
                currentSong = cached.currentSong;
                await this.mpdClient.connect();
                queue = await this.mpdClient.getCurrentPlaylist();
                await this.mpdClient.disconnect();
            }
            else {
                await this.mpdClient.connect();
                status = await this.mpdClient.getStatus();
                currentSong = await this.mpdClient.getCurrentSong();
                queue = await this.mpdClient.getCurrentPlaylist();
                await this.mpdClient.disconnect();
                cache.set(status, currentSong);
            }
            socket.emit('mpd-status', {
                status,
                currentSong,
                queue,
                timestamp: Date.now()
            });
            const skipVoteStatus = this.skipVoteManager.getVoteStatus();
            socket.emit('skip-vote-update', skipVoteStatus);
        }
        catch (error) {
            console.error('Failed to send current status:', error);
            socket.emit('error', { message: 'Failed to get current status' });
        }
    }
    cleanup() {
        if (this.statusPollingInterval) {
            clearInterval(this.statusPollingInterval);
            this.statusPollingInterval = null;
        }
    }
}
exports.JukeboxSocketHandler = JukeboxSocketHandler;
//# sourceMappingURL=jukebox-socket-handler.js.map