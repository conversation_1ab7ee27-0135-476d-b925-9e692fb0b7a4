import { Page } from '@playwright/test';
export interface PlaywrightUtilsConfig {
    headless?: boolean;
    timeout?: number;
    viewport?: {
        width: number;
        height: number;
    };
}
export declare class MusicQuizAutomation {
    private browser;
    private context;
    private config;
    constructor(config?: PlaywrightUtilsConfig);
    initialize(): Promise<void>;
    close(): Promise<void>;
    createPage(): Promise<Page>;
    scrapeAlbumArt(artist: string, album: string): Promise<string[]>;
    simulateMultiplayerGame(config: {
        gameUrl: string;
        playerCount: number;
        roomCode?: string;
    }): Promise<Page[]>;
    monitorWebSocket(page: Page): Promise<{
        messages: any[];
        connections: number;
    }>;
    captureGameState(page: Page, stateName: string): Promise<Buffer>;
    measureGamePerformance(page: Page): Promise<{
        fps: number;
        memoryUsage: number;
        cpuUsage: number;
    }>;
    verifyQuizQuestion(page: Page, expectedQuestion: {
        text: string;
        options?: string[];
        correctAnswer?: string;
    }): Promise<boolean>;
}
export declare class SocketTestClient {
    private socket;
    private messages;
    connect(url: string, options?: any): Promise<void>;
    emit(event: string, data: any): Promise<void>;
    waitForEvent(event: string, timeout?: number): Promise<any>;
    getMessages(): any[];
    disconnect(): void;
}
export declare function runBatchTests(tests: Array<{
    name: string;
    test: () => Promise<void>;
}>): Promise<{
    passed: number;
    failed: number;
    results: any[];
}>;
