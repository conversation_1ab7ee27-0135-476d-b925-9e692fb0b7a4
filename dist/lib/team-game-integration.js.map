{"version": 3, "file": "team-game-integration.js", "sourceRoot": "", "sources": ["../../lib/team-game-integration.ts"], "names": [], "mappings": ";;;AAOA,iDAA+D;AAC/D,uDAAsE;AAkBtE,MAAa,mBAAmB;IAAhC;QACU,gBAAW,GAAiC,IAAI,GAAG,EAAE,CAAA;QACrD,mBAAc,GAAgC,IAAI,GAAG,EAAE,CAAA;QACvD,wBAAmB,GAAgC,IAAI,GAAG,EAAE,CAAA;IAyZtE,CAAC;IApZC,kBAAkB,CAChB,MAAc,EACd,KAAa,EACb,QAA0B,EAC1B,cAAsB;QAEtB,MAAM,SAAS,GAAG,qCAAmB,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;QAEjF,MAAM,OAAO,GAAoB;YAC/B,MAAM;YACN,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;YACjB,QAAQ;YACR,SAAS;YACT,eAAe,EAAE,CAAC;YAClB,cAAc;YACd,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE;YAC7B,WAAW,EAAE,IAAI,GAAG,EAAE;YACtB,gBAAgB,EAAE,EAAE;YACpB,YAAY,EAAE,EAAE;YAChB,YAAY,EAAE,IAAI,GAAG,EAAE;SACxB,CAAA;QAED,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACrC,OAAO,OAAO,CAAA;IAChB,CAAC;IAKD,aAAa,CACX,MAAc,EACd,cAAsB,EACtB,YAAiB,EACjB,SAAiB;QAOjB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,EAAE,CAAA;QAGxD,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;QAC3B,OAAO,CAAC,eAAe,GAAG,cAAc,CAAA;QACxC,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAGtC,qCAAmB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAA;QAG/C,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,KAAK,eAAe,EAAE,CAAC;YACtD,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC3C,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAA;QAC1E,CAAC;QAGD,MAAM,QAAQ,GAAG,qCAAmB,CAAC,kBAAkB,CAAC,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAA;QACtF,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAGrD,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;QAE1C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,iBAAiB,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,KAAK,eAAe;gBAClE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB;gBACpC,CAAC,CAAC,SAAS;YACb,WAAW;YACX,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,cAAc;SACnE,CAAA;IACH,CAAC;IAKD,gBAAgB,CACd,MAAc,EACd,UAAsB;QAOtB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAA;QAG1F,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK;aACzB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,MAAM,CAAC;YACtC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,WAAW,CAAC,CAAA;QAEtD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAA;QAChF,CAAC;QAGD,MAAM,MAAM,GAAG,qCAAmB,CAAC,gBAAgB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;QAEtF,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAEpB,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;YAGtD,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,MAAM,CAAC,CAAA;YAC1E,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG;oBACzB,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;oBAC3B,WAAW,EAAE,IAAI;oBACjB,UAAU,EAAE,UAAU,CAAC,MAAM;oBAC7B,cAAc,EAAE,UAAU,CAAC,SAAS;iBACrC,CAAA;YACH,CAAC;QACH,CAAC;QAGD,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAA;QAEvD,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,QAAQ;YACxB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,gBAAgB,EAAE,WAAW;SAC9B,CAAA;IACH,CAAC;IAKD,sBAAsB,CACpB,MAAc,EACd,aAAqB,EACrB,kBAA0B,EAC1B,SAAiB;QAMjB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,CAAA;QAG7E,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAA;QAG5D,MAAM,YAAY,GAAG,gCAAiB,CAAC,mBAAmB,CACxD,OAAO,CAAC,KAAK,EACb,WAAW,EACX,aAAa,EACb,kBAAkB,EAClB,SAAS,EACT,OAAO,CAAC,QAAQ,CAAC,eAAe,EAChC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAC9B,CAAA;QAGD,MAAM,YAAY,GAAG,gCAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;QACrF,OAAO,CAAC,KAAK,GAAG,YAAY,CAAA;QAG5B,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAGvC,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAA;QAGtE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAA;QAEhC,OAAO;YACL,YAAY;YACZ,YAAY;YACZ,YAAY;SACb,CAAA;IACH,CAAC;IAKD,cAAc,CACZ,MAAc,EACd,OAAwB;QAExB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAO,KAAK,CAAA;QAG1B,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA;QAC7D,MAAM,MAAM,GAAG,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAA;QAEjE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAA;QAGlC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAGtC,IAAI,OAAO,CAAC,gBAAgB,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC1C,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAA;QACjE,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,kBAAkB,CAAC,MAAc;QAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IACrC,CAAC;IAKD,kBAAkB,CAAC,MAAc;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,CAAA;QAEvB,OAAO,gCAAiB,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAC5D,CAAC;IAKD,mBAAmB,CAAC,MAAc,EAAE,MAAe;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,CAAA;QAEvB,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;QACtE,CAAC;QAED,OAAO,OAAO,CAAC,gBAAgB,CAAA;IACjC,CAAC;IAKD,yBAAyB,CAAC,MAAc;QAItC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC;YAChD,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,EAAE,CAAA;QAC5C,CAAC;QAED,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAA;QACpE,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,iBAAiB,GAAG,OAAO,CAAC,CAAA;QAE/E,OAAO;YACL,MAAM,EAAE,aAAa,GAAG,CAAC;YACzB,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;SACxC,CAAA;IACH,CAAC;IAKD,gBAAgB,CAAC,MAAc;QAM7B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;gBACL,UAAU,EAAE,EAAE;gBACd,WAAW,EAAE,EAAE;gBACf,SAAS,EAAE,EAAE;gBACb,YAAY,EAAE,IAAI,GAAG,EAAE;aACxB,CAAA;QACH,CAAC;QAGD,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;QAGlD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAExB,OAAO;YACL,UAAU,EAAE,OAAO,CAAC,KAAK;YACzB,WAAW;YACX,SAAS;YACT,YAAY,EAAE,OAAO,CAAC,YAAY;SACnC,CAAA;IACH,CAAC;IAKD,WAAW,CAAC,MAAc;QACxB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAA;QAChC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAC/B,qCAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;IACzC,CAAC;IAMO,kBAAkB,CAAC,MAAc,EAAE,SAAiB;QAC1D,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAA;QACpC,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,CAAA;QAEpB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACxC,CAAC;IAEO,uBAAuB,CAAC,MAAc,EAAE,iBAAyB;QACvE,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAA;QACzC,CAAC,EAAE,iBAAiB,GAAG,IAAI,CAAC,CAAA;QAE5B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC7C,CAAC;IAEO,mBAAmB,CAAC,MAAc;QACxC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACrD,IAAI,aAAa,EAAE,CAAC;YAClB,YAAY,CAAC,aAAa,CAAC,CAAA;YAC3B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACpC,CAAC;QAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC/D,IAAI,kBAAkB,EAAE,CAAC;YACvB,YAAY,CAAC,kBAAkB,CAAC,CAAA;YAChC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACzC,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,MAAc;QAE1C,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,OAAO,EAAE,CAAC;QAGd,CAAC;IACH,CAAC;IAEO,0BAA0B,CAAC,MAAc;QAE/C,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC5C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,OAAO,CAAC,sBAAsB,CAAA;QACvC,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,OAAwB;QACpD,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QACzE,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IACpD,CAAC;IAEO,qBAAqB,CAAC,OAAwB,EAAE,YAA2B;QACjF,MAAM,YAAY,GAAa,EAAE,CAAA;QAGjC,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAChD,MAAM,CAAC,SAAS,CAAC,aAAa,GAAG,CAAC;YAClC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM;gBAC/D,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM,CAChE,CAAA;QAED,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QACnC,CAAC;QAGD,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC,CAAA;QACzF,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QACnC,CAAC;QAGD,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC5B,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7C,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;YAC7C,CAAC;YACD,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC/D,CAAC,CAAC,CAAA;QAEF,OAAO,YAAY,CAAA;IACrB,CAAC;IAEO,kBAAkB,CAAC,OAAwB;QACjD,MAAM,cAAc,GAAG,OAAO,CAAC,eAAe,GAAG,CAAC,CAAA;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,iBAAiB,CAAA;QACxD,MAAM,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC;YAClD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;iBACrC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI;YAChH,CAAC,CAAC,CAAC,CAAA;QAEL,OAAO;YACL,cAAc;YACd,SAAS;YACT,eAAe;YACf,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM;YAChC,YAAY,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC/E,YAAY,EAAE,OAAO,CAAC,gBAAgB,CAAC,MAAM;SAC9C,CAAA;IACH,CAAC;CACF;AA5ZD,kDA4ZC;AAEY,QAAA,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAA"}