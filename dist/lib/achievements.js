"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ACHIEVEMENTS = void 0;
exports.checkQuizAchievements = checkQuizAchievements;
exports.checkJukeboxAchievements = checkJukeboxAchievements;
exports.ACHIEVEMENTS = {
    'first-win': {
        id: 'first-win',
        name: 'First Victory',
        description: 'Win your first quiz game',
        icon: '🏆',
        category: 'quiz',
        rarity: 'common',
        points: 10
    },
    'perfect-score': {
        id: 'perfect-score',
        name: 'Perfectionist',
        description: 'Get 100% correct answers in a quiz',
        icon: '💯',
        category: 'quiz',
        rarity: 'rare',
        points: 50
    },
    'speed-demon': {
        id: 'speed-demon',
        name: 'Speed Demon',
        description: 'Answer 10 questions correctly in under 5 seconds each',
        icon: '⚡',
        category: 'quiz',
        rarity: 'epic',
        points: 100
    },
    'quiz-master': {
        id: 'quiz-master',
        name: 'Quiz Master',
        description: 'Win 100 quiz games',
        icon: '👑',
        category: 'quiz',
        rarity: 'legendary',
        points: 500,
        maxProgress: 100
    },
    'dj-debut': {
        id: 'dj-debut',
        name: 'DJ De<PERSON>',
        description: 'Add your first song to the queue',
        icon: '🎵',
        category: 'jukebox',
        rarity: 'common',
        points: 10
    },
    'crowd-pleaser': {
        id: 'crowd-pleaser',
        name: 'Crowd Pleaser',
        description: 'Have a song you added receive 10 upvotes',
        icon: '👍',
        category: 'jukebox',
        rarity: 'rare',
        points: 50
    },
    'music-explorer': {
        id: 'music-explorer',
        name: 'Music Explorer',
        description: 'Play songs from 20 different genres',
        icon: '🌍',
        category: 'jukebox',
        rarity: 'epic',
        points: 100,
        maxProgress: 20
    },
    'social-butterfly': {
        id: 'social-butterfly',
        name: 'Social Butterfly',
        description: 'Play in 10 multiplayer games',
        icon: '🦋',
        category: 'social',
        rarity: 'rare',
        points: 50,
        maxProgress: 10
    },
    'team-player': {
        id: 'team-player',
        name: 'Team Player',
        description: 'Win a team-based quiz',
        icon: '🤝',
        category: 'social',
        rarity: 'common',
        points: 25
    },
    'night-owl': {
        id: 'night-owl',
        name: 'Night Owl',
        description: 'Play a game between 2 AM and 5 AM',
        icon: '🦉',
        category: 'special',
        rarity: 'rare',
        points: 30
    },
    'streak-master': {
        id: 'streak-master',
        name: 'Streak Master',
        description: 'Maintain a 20-answer correct streak',
        icon: '🔥',
        category: 'special',
        rarity: 'epic',
        points: 100
    },
    'knowledge-encyclopedia': {
        id: 'knowledge-encyclopedia',
        name: 'Knowledge Encyclopedia',
        description: 'Answer correctly in all 16 general knowledge categories',
        icon: '📚',
        category: 'special',
        rarity: 'legendary',
        points: 200,
        maxProgress: 16
    }
};
function checkQuizAchievements(gameResults, userStats) {
    const newAchievements = [];
    if (gameResults.won && userStats.totalWins === 1) {
        newAchievements.push('first-win');
    }
    if (gameResults.accuracy === 100) {
        newAchievements.push('perfect-score');
    }
    const fastAnswers = gameResults.answers.filter((a) => a.correct && a.timeSpent < 5);
    if (fastAnswers.length >= 10) {
        newAchievements.push('speed-demon');
    }
    if (gameResults.maxStreak >= 20) {
        newAchievements.push('streak-master');
    }
    return newAchievements;
}
function checkJukeboxAchievements(action, userStats) {
    const newAchievements = [];
    if (action === 'first-song-added' && userStats.songsAdded === 1) {
        newAchievements.push('dj-debut');
    }
    if (action === 'song-upvoted' && userStats.totalUpvotes >= 10) {
        newAchievements.push('crowd-pleaser');
    }
    return newAchievements;
}
//# sourceMappingURL=achievements.js.map