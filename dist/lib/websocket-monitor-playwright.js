"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MultiplayerGameMonitor = void 0;
exports.monitorMultiplayerGame = monitorMultiplayerGame;
exports.runGameStressTest = runGameStressTest;
const playwright_utils_1 = require("./playwright-utils");
class MultiplayerGameMonitor {
    constructor() {
        this.socketClients = new Map();
        this.gameEvents = [];
        this.playerMetrics = new Map();
        this.automation = new playwright_utils_1.MusicQuizAutomation({ headless: true });
    }
    async initialize() {
        await this.automation.initialize();
    }
    async close() {
        for (const [, client] of this.socketClients) {
            client.disconnect();
        }
        await this.automation.close();
    }
    async monitorGameSession(config) {
        const screenshots = [];
        const pages = [];
        try {
            for (let i = 0; i < config.playerCount; i++) {
                const playerId = `player-${i + 1}`;
                const page = await this.automation.createPage();
                pages.push(page);
                await this.setupWebSocketInterception(page, playerId);
                const socketClient = new playwright_utils_1.SocketTestClient();
                await socketClient.connect(config.socketUrl, {
                    query: { playerId }
                });
                this.socketClients.set(playerId, socketClient);
                this.playerMetrics.set(playerId, {
                    playerId,
                    joinTime: Date.now(),
                    score: 0,
                    correctAnswers: 0,
                    responseTime: [],
                    connectionQuality: 'good'
                });
                await page.goto(config.gameUrl);
                await page.fill('[data-testid="player-name"]', `Player ${i + 1}`);
                if (i === 0) {
                    await page.click('[data-testid="create-room"]');
                    const roomCode = await page.textContent('[data-testid="room-code-display"]');
                    for (let j = 1; j < config.playerCount; j++) {
                        const otherPage = pages[j];
                        await otherPage.fill('[data-testid="room-code"]', roomCode);
                        await otherPage.click('[data-testid="join-room"]');
                    }
                }
            }
            const monitoringInterval = setInterval(async () => {
                for (const [playerId, client] of this.socketClients) {
                    const messages = client.getMessages();
                    for (const msg of messages) {
                        this.gameEvents.push({
                            event: msg.event,
                            timestamp: msg.timestamp,
                            data: { ...msg.data, playerId }
                        });
                    }
                }
                await this.updatePlayerMetrics(pages);
                if (config.captureScreenshots) {
                    const screenshotPath = `./screenshots/game-${Date.now()}.png`;
                    await pages[0].screenshot({ path: screenshotPath });
                    screenshots.push(screenshotPath);
                }
            }, 1000);
            await new Promise(resolve => setTimeout(resolve, config.duration));
            clearInterval(monitoringInterval);
            const summary = this.generateGameSummary();
            return {
                events: this.gameEvents,
                metrics: Array.from(this.playerMetrics.values()),
                screenshots,
                summary
            };
        }
        finally {
            for (const page of pages) {
                await page.close();
            }
        }
    }
    async setupWebSocketInterception(page, playerId) {
        await page.evaluateOnNewDocument((playerId) => {
            window.__wsMessages = [];
            window.__playerId = playerId;
            const originalWebSocket = window.WebSocket;
            window.WebSocket = new Proxy(originalWebSocket, {
                construct(target, args) {
                    const ws = new target(...args);
                    const originalSend = ws.send.bind(ws);
                    ws.send = function (data) {
                        try {
                            const parsed = JSON.parse(data);
                            window.__wsMessages.push({
                                type: 'sent',
                                event: parsed.event || 'unknown',
                                data: parsed,
                                timestamp: Date.now(),
                                playerId: window.__playerId
                            });
                        }
                        catch {
                        }
                        return originalSend(data);
                    };
                    ws.addEventListener('message', (event) => {
                        try {
                            const parsed = JSON.parse(event.data);
                            window.__wsMessages.push({
                                type: 'received',
                                event: parsed.event || 'unknown',
                                data: parsed,
                                timestamp: Date.now(),
                                playerId: window.__playerId
                            });
                        }
                        catch {
                        }
                    });
                    return ws;
                }
            });
        }, playerId);
    }
    async updatePlayerMetrics(pages) {
        for (let i = 0; i < pages.length; i++) {
            const page = pages[i];
            const playerId = `player-${i + 1}`;
            const metrics = this.playerMetrics.get(playerId);
            if (!metrics)
                continue;
            try {
                const scoreText = await page.textContent('[data-testid="player-score"]');
                if (scoreText) {
                    metrics.score = parseInt(scoreText) || 0;
                }
                const wsMessages = await page.evaluate(() => window.__wsMessages || []);
                const recentMessages = wsMessages.filter((m) => Date.now() - m.timestamp < 5000);
                if (recentMessages.length === 0) {
                    metrics.connectionQuality = 'poor';
                }
                else if (recentMessages.length < 5) {
                    metrics.connectionQuality = 'fair';
                }
                else {
                    metrics.connectionQuality = 'good';
                }
            }
            catch {
                metrics.connectionQuality = 'poor';
            }
        }
    }
    generateGameSummary() {
        const eventCounts = {};
        const playerScores = {};
        for (const event of this.gameEvents) {
            eventCounts[event.event] = (eventCounts[event.event] || 0) + 1;
        }
        for (const [playerId, metrics] of this.playerMetrics) {
            playerScores[playerId] = metrics.score;
        }
        const avgResponseTimes = {};
        for (const [playerId, metrics] of this.playerMetrics) {
            if (metrics.responseTime.length > 0) {
                avgResponseTimes[playerId] =
                    metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length;
            }
        }
        return {
            totalEvents: this.gameEvents.length,
            eventCounts,
            playerScores,
            avgResponseTimes,
            connectionIssues: Array.from(this.playerMetrics.values())
                .filter(m => m.connectionQuality !== 'good')
                .map(m => ({ playerId: m.playerId, quality: m.connectionQuality }))
        };
    }
    async simulateScenarios(scenarios) {
        const results = [];
        for (const scenario of scenarios) {
            const pages = await this.automation.simulateMultiplayerGame({
                gameUrl: 'http://localhost:3000/multiplayer',
                playerCount: 4
            });
            try {
                await scenario.setup(pages);
                await new Promise(resolve => setTimeout(resolve, 5000));
                const success = await scenario.verify(pages);
                results.push({
                    scenario: scenario.name,
                    success,
                    timestamp: Date.now()
                });
            }
            finally {
                for (const page of pages) {
                    await page.close();
                }
            }
        }
        return results;
    }
    async stressTest(config) {
        const performanceData = [];
        const pages = [];
        let currentPlayers = 0;
        let errors = 0;
        let totalRequests = 0;
        const playerJoinInterval = config.rampUpTime / config.maxPlayers;
        const joinInterval = setInterval(async () => {
            if (currentPlayers >= config.maxPlayers) {
                clearInterval(joinInterval);
                return;
            }
            try {
                const page = await this.automation.createPage();
                await page.goto(config.gameUrl);
                pages.push(page);
                currentPlayers++;
            }
            catch (error) {
                errors++;
            }
        }, playerJoinInterval);
        const monitorInterval = setInterval(async () => {
            for (const page of pages) {
                try {
                    const metrics = await this.automation.measureGamePerformance(page);
                    performanceData.push({
                        timestamp: Date.now(),
                        players: currentPlayers,
                        ...metrics
                    });
                    totalRequests++;
                }
                catch {
                    errors++;
                    totalRequests++;
                }
            }
        }, 1000);
        await new Promise(resolve => setTimeout(resolve, config.testDuration));
        clearInterval(joinInterval);
        clearInterval(monitorInterval);
        for (const page of pages) {
            await page.close();
        }
        const latencies = performanceData.map(d => d.fps ? 1000 / d.fps : 0);
        const averageLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
        return {
            maxConcurrentPlayers: currentPlayers,
            averageLatency,
            errorRate: errors / totalRequests,
            performanceData
        };
    }
}
exports.MultiplayerGameMonitor = MultiplayerGameMonitor;
async function monitorMultiplayerGame() {
    const monitor = new MultiplayerGameMonitor();
    try {
        await monitor.initialize();
        const results = await monitor.monitorGameSession({
            gameUrl: 'http://localhost:3000/multiplayer',
            socketUrl: 'http://localhost:3001',
            playerCount: 4,
            duration: 60000,
            captureScreenshots: true
        });
        console.log('Game Summary:', results.summary);
        console.log('Total Events:', results.events.length);
        console.log('Player Metrics:', results.metrics);
        return results;
    }
    finally {
        await monitor.close();
    }
}
async function runGameStressTest() {
    const monitor = new MultiplayerGameMonitor();
    try {
        await monitor.initialize();
        const results = await monitor.stressTest({
            gameUrl: 'http://localhost:3000/multiplayer',
            socketUrl: 'http://localhost:3001',
            maxPlayers: 20,
            rampUpTime: 30000,
            testDuration: 120000
        });
        console.log('Stress Test Results:');
        console.log('Max Concurrent Players:', results.maxConcurrentPlayers);
        console.log('Average Latency:', results.averageLatency, 'ms');
        console.log('Error Rate:', (results.errorRate * 100).toFixed(2), '%');
        return results;
    }
    finally {
        await monitor.close();
    }
}
//# sourceMappingURL=websocket-monitor-playwright.js.map