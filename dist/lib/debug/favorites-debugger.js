"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FavoritesDebugger = void 0;
class FavoritesDebugger {
    static log(action, data, error) {
        const entry = {
            timestamp: new Date(),
            action,
            data,
            error
        };
        this.logs.push(entry);
        if (this.logs.length > 50) {
            this.logs.shift();
        }
        if (error) {
            console.error(`❌ [Favorites] ${action}:`, data, error);
        }
        else {
            console.log(`✅ [Favorites] ${action}:`, data);
        }
    }
    static validateSong(song) {
        const issues = [];
        if (!song.filePath) {
            issues.push('Missing filePath');
        }
        if (!song.title) {
            issues.push('Missing title');
        }
        if (!song.artist) {
            issues.push('Missing artist');
        }
        if (typeof song.filePath !== 'string') {
            issues.push(`Invalid filePath type: ${typeof song.filePath}`);
        }
        return {
            valid: issues.length === 0,
            issues
        };
    }
    static debugToggleFavorite(song, currentState, userId) {
        const validation = this.validateSong(song);
        this.log('toggle-favorite-start', {
            song: {
                id: song.id,
                title: song.title,
                artist: song.artist,
                filePath: song.filePath
            },
            currentState,
            userId,
            validation
        });
        return validation.valid;
    }
    static debugApiCall(method, url, payload) {
        this.log(`api-call-${method.toLowerCase()}`, {
            method,
            url,
            payload: payload ? {
                userId: payload.userId,
                songFilePath: payload.song?.filePath,
                songTitle: payload.song?.title
            } : undefined
        });
    }
    static debugApiResponse(success, data, error) {
        this.log(success ? 'api-response-success' : 'api-response-error', {
            success,
            data,
            alreadyExists: data?.alreadyExists
        }, error);
    }
    static debugStateUpdate(action, filePath, setSize) {
        this.log(`state-${action}`, {
            filePath,
            newSetSize: setSize,
            action
        });
    }
    static getLogs() {
        return [...this.logs];
    }
    static getReport() {
        const report = [
            '=== Favorites Debug Report ===',
            `Total logs: ${this.logs.length}`,
            '',
            'Recent Actions:',
            ...this.logs.slice(-10).map(log => `[${log.timestamp.toISOString()}] ${log.action}: ${JSON.stringify(log.data)} ${log.error ? '❌ ERROR' : '✅'}`),
            '',
            'Error Summary:',
            ...this.logs.filter(l => l.error).slice(-5).map(log => `- ${log.action}: ${log.error?.message || JSON.stringify(log.error)}`)
        ].join('\n');
        return report;
    }
    static clear() {
        this.logs = [];
        console.log('🧹 [Favorites] Debug logs cleared');
    }
}
exports.FavoritesDebugger = FavoritesDebugger;
FavoritesDebugger.logs = [];
if (typeof window !== 'undefined') {
    window.FavoritesDebugger = FavoritesDebugger;
}
//# sourceMappingURL=favorites-debugger.js.map