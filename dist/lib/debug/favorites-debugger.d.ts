import type { Song, QueuedSong } from '@/lib/types';
export declare class FavoritesDebugger {
    private static logs;
    static log(action: string, data: any, error?: any): void;
    static validateSong(song: Song | QueuedSong): {
        valid: boolean;
        issues: string[];
    };
    static debugToggleFavorite(song: Song, currentState: boolean, userId?: string): boolean;
    static debugApiCall(method: 'POST' | 'DELETE', url: string, payload?: any): void;
    static debugApiResponse(success: boolean, data: any, error?: any): void;
    static debugStateUpdate(action: 'add' | 'remove' | 'rollback', filePath: string, setSize: number): void;
    static getLogs(): {
        timestamp: Date;
        action: string;
        data: any;
        error?: any;
    }[];
    static getReport(): string;
    static clear(): void;
}
