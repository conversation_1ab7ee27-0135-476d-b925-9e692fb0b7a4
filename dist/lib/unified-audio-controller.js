"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedAudioController = void 0;
exports.getUnifiedAudioController = getUnifiedAudioController;
exports.resetUnifiedAudioController = resetUnifiedAudioController;
class UnifiedAudioController {
    constructor() {
        this.systems = {};
        this.volumeState = {
            volume: 70,
            muted: false,
            lastVolume: 70
        };
        console.log('[UnifiedAudio] Controller initialized');
    }
    registerSystems(systems) {
        this.systems = { ...this.systems, ...systems };
        console.log('[UnifiedAudio] Systems registered:', Object.keys(this.systems));
        if (typeof window !== 'undefined') {
            window.unifiedAudioController = this;
        }
    }
    async setVolume(volume) {
        const clampedVolume = Math.max(0, Math.min(100, volume));
        console.log(`[UnifiedAudio] Setting volume to ${clampedVolume}%`);
        this.volumeState.volume = clampedVolume;
        this.volumeState.muted = clampedVolume === 0;
        const results = [];
        if (this.systems.audioManager) {
            try {
                await this.systems.audioManager.setVolume(clampedVolume);
                results.push({ system: 'MPD Server', success: true });
            }
            catch (error) {
                const errorMsg = error instanceof Error ? error.message : 'Unknown error';
                results.push({ system: 'MPD Server', success: false, error: errorMsg });
                console.error(`[UnifiedAudio] ❌ MPD server volume failed:`, error);
            }
        }
        if (this.systems.audioOutputSelector) {
            try {
                this.systems.audioOutputSelector.setClientVolume(clampedVolume);
                results.push({ system: 'Client Stream', success: true });
                console.log(`[UnifiedAudio] ✅ Client stream volume set to ${clampedVolume}%`);
            }
            catch (error) {
                const errorMsg = error instanceof Error ? error.message : 'Unknown error';
                results.push({ system: 'Client Stream', success: false, error: errorMsg });
                console.error(`[UnifiedAudio] ❌ Client stream volume failed:`, error);
            }
        }
        if (this.systems.audioMixer) {
            try {
                this.systems.audioMixer.updateSettings({ masterVolume: clampedVolume / 100 });
                results.push({ system: 'Web Audio Mixer', success: true });
                console.log(`[UnifiedAudio] ✅ Web Audio mixer volume set to ${clampedVolume}%`);
            }
            catch (error) {
                const errorMsg = error instanceof Error ? error.message : 'Unknown error';
                results.push({ system: 'Web Audio Mixer', success: false, error: errorMsg });
                console.error(`[UnifiedAudio] ❌ Web Audio mixer volume failed:`, error);
            }
        }
        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;
        if (successCount === totalCount) {
            console.log(`[UnifiedAudio] ✅ Volume set to ${clampedVolume}% across all ${totalCount} systems`);
        }
        else {
            console.warn(`[UnifiedAudio] ⚠️ Volume set on ${successCount}/${totalCount} systems`);
            results.filter(r => !r.success).forEach(r => {
                console.error(`[UnifiedAudio] Failed on ${r.system}: ${r.error}`);
            });
        }
        return Promise.resolve();
    }
    async setMuted(muted) {
        console.log(`[UnifiedAudio] ${muted ? 'Muting' : 'Unmuting'} all audio systems`);
        if (muted) {
            if (this.volumeState.volume > 0) {
                this.volumeState.lastVolume = this.volumeState.volume;
            }
            await this.setVolume(0);
        }
        else {
            const restoreVolume = this.volumeState.lastVolume || 70;
            await this.setVolume(restoreVolume);
        }
        this.volumeState.muted = muted;
        if (this.systems.audioOutputSelector) {
            this.systems.audioOutputSelector.setClientMuted(muted);
        }
    }
    getVolumeState() {
        return { ...this.volumeState };
    }
    getVolume() {
        return this.volumeState.volume;
    }
    isMuted() {
        return this.volumeState.muted;
    }
    getSystemStatus() {
        const status = {};
        if (this.systems.audioManager) {
            status.mpdServer = {
                connected: this.systems.audioManager.isReady(),
                volume: this.systems.audioManager.getUserVolume()
            };
        }
        if (this.systems.audioOutputSelector) {
            status.clientStream = {
                volume: this.systems.audioOutputSelector.getClientVolume(),
                muted: this.systems.audioOutputSelector.isClientMuted()
            };
        }
        if (this.systems.audioMixer) {
            status.webAudioMixer = {
                available: true,
                masterVolume: this.volumeState.volume
            };
        }
        return status;
    }
    async testAllSystems() {
        const results = {};
        if (this.systems.audioManager) {
            try {
                results.mpdServer = this.systems.audioManager.isReady();
            }
            catch (error) {
                results.mpdServer = false;
            }
        }
        if (this.systems.audioOutputSelector) {
            try {
                const currentVol = this.systems.audioOutputSelector.getClientVolume();
                this.systems.audioOutputSelector.setClientVolume(currentVol);
                results.clientStream = true;
            }
            catch (error) {
                results.clientStream = false;
            }
        }
        if (this.systems.audioMixer) {
            try {
                this.systems.audioMixer.getSettings();
                results.webAudioMixer = true;
            }
            catch (error) {
                results.webAudioMixer = false;
            }
        }
        console.log('[UnifiedAudio] System test results:', results);
        return results;
    }
    async initialize(defaultVolume = 70) {
        console.log(`[UnifiedAudio] Initializing with default volume: ${defaultVolume}%`);
        await this.setVolume(defaultVolume);
    }
    cleanup() {
        console.log('[UnifiedAudio] Cleaning up');
        this.systems = {};
        if (typeof window !== 'undefined') {
            delete window.unifiedAudioController;
        }
    }
}
exports.UnifiedAudioController = UnifiedAudioController;
let globalController = null;
function getUnifiedAudioController() {
    if (!globalController) {
        globalController = new UnifiedAudioController();
    }
    return globalController;
}
function resetUnifiedAudioController() {
    if (globalController) {
        globalController.cleanup();
    }
    globalController = null;
}
//# sourceMappingURL=unified-audio-controller.js.map