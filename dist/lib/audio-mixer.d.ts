export interface AudioSource {
    id: string;
    type: 'music' | 'speech' | 'sfx' | 'ambient';
    url: string;
    element?: HTMLAudioElement;
    gainNode?: GainNode;
    volume: number;
    isPlaying: boolean;
    isPaused: boolean;
    duration: number;
    currentTime: number;
    loop: boolean;
    fadeInDuration?: number;
    fadeOutDuration?: number;
}
export interface MixerSettings {
    masterVolume: number;
    musicVolume: number;
    speechVolume: number;
    sfxVolume: number;
    ambientVolume: number;
    crossfadeDuration: number;
    enableDucking: boolean;
    duckingLevel: number;
    enableEQ: boolean;
    bassGain: number;
    midGain: number;
    trebleGain: number;
    enableTTS: boolean;
}
export interface FadeConfig {
    duration: number;
    curve: 'linear' | 'exponential' | 'logarithmic';
    startVolume?: number;
    endVolume?: number;
}
export declare class AudioMixer {
    private audioContext;
    private masterGainNode;
    private musicGainNode;
    private speechGainNode;
    private sfxGainNode;
    private ambientGainNode;
    private compressorNode;
    private analyserNode;
    private bassFilter;
    private midFilter;
    private trebleFilter;
    private sources;
    private settings;
    private activeFades;
    private eventListeners;
    private speechSynthesis;
    private speechVoices;
    private channels;
    private audioBuffers;
    private audioSources;
    private gainNodes;
    private speechUtterance;
    constructor();
    private initializeAudioGraph;
    private initializeDefaultSettings;
    private loadVoices;
    addSource(source: Omit<AudioSource, 'element' | 'gainNode' | 'isPlaying' | 'isPaused' | 'duration' | 'currentTime'>): Promise<AudioSource>;
    play(sourceId: string, fadeIn?: FadeConfig): Promise<void>;
    stop(sourceId: string, fadeOut?: FadeConfig): Promise<void>;
    pause(sourceId: string): void;
    resume(sourceId: string): void;
    crossfade(fromSourceId: string, toSourceId: string, duration?: number): Promise<void>;
    fadeVolume(sourceId: string, config: FadeConfig): Promise<void>;
    private duckMusic;
    speakText(text: string, options?: {
        voice?: string;
        rate?: number;
        pitch?: number;
        volume?: number;
        fadeIn?: FadeConfig;
        fadeOut?: FadeConfig;
    }): Promise<void>;
    updateSettings(newSettings: Partial<MixerSettings>): void;
    private applySettings;
    getAudioLevels(): {
        frequency: Uint8Array;
        waveform: Uint8Array;
    };
    getSources(): AudioSource[];
    getSettings(): MixerSettings;
    on(event: string, callback: Function): void;
    off(event: string, callback: Function): void;
    private emit;
    dispose(): void;
}
