import { Song } from "@/lib/types";
declare function normalizeArtistName(artist: string): string;
declare function normalizeSongTitle(title: string): string;
export declare function enhancedDeduplicateSongs(songs: Song[]): Song[];
export declare function analyzeArtistVariations(songs: Song[]): Array<{
    normalizedKey: string;
    originalArtists: string[];
    originalTitles: string[];
    wouldMerge: boolean;
}>;
export { normalizeArtistName, normalizeSongTitle };
