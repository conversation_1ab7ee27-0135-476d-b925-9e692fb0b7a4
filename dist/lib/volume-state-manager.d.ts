export interface VolumeState {
    userVolume: number;
    lastSetAt: number;
    mode: 'quiz' | 'jukebox' | 'unknown';
}
declare class VolumeStateManager {
    private currentState;
    private listeners;
    constructor();
    private loadVolumeState;
    private saveVolumeState;
    getVolumeState(): VolumeState;
    getUserVolume(): number;
    setUserVolume(volume: number, mode?: 'quiz' | 'jukebox'): void;
    setMode(mode: 'quiz' | 'jukebox'): void;
    addListener(listener: (state: VolumeState) => void): () => void;
    private notifyListeners;
    initializeForMode(mode: 'quiz' | 'jukebox'): number;
    transitionToMode(fromMode: 'quiz' | 'jukebox', toMode: 'quiz' | 'jukebox'): number;
    resetToDefault(): void;
    getDebugInfo(): {
        state: VolumeState;
        storage: string | null;
        listeners: number;
    };
}
export declare const volumeStateManager: VolumeStateManager;
export declare function useVolumeState(): {
    setUserVolume: (volume: number, mode?: "quiz" | "jukebox") => void;
    setMode: (mode: "quiz" | "jukebox") => void;
    getUserVolume: () => number;
    initializeForMode: (mode: "quiz" | "jukebox") => number;
    transitionToMode: (fromMode: "quiz" | "jukebox", toMode: "quiz" | "jukebox") => number;
    userVolume: number;
    lastSetAt: number;
    mode: "quiz" | "jukebox" | "unknown";
};
export {};
