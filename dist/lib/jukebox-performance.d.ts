import { Song } from "@/lib/types";
import { CacheManager } from "@/lib/performance-utils";
export declare const jukeboxLibraryCache: CacheManager<Song[]>;
export declare const jukeboxCategoriesCache: CacheManager<string[]>;
export declare const memoizedDeduplicateSongs: (songs: Song[]) => Song[];
export declare const memoizedFilterAndSort: (library: Song[], searchTerm: string, sortBy: string, selectedCategory: string) => Song[];
export declare function loadLibraryWithCache(mpdClient: any, setLibrary: (songs: Song[] | ((prev: Song[]) => Song[])) => void, setAvailableCategories: (categories: string[]) => void, setIsLoading?: (loading: boolean) => void, searchTerm?: string, page?: number, append?: boolean, selectedCategory?: string, selectedGenre?: string, hideChartSongs?: boolean, hideMyItunes?: boolean): Promise<{
    hasMore: boolean;
    totalPages: number;
}>;
export declare function clearJukeboxCache(): void;
export declare function getJukeboxCacheStats(): {
    library: number;
    categories: number;
};
