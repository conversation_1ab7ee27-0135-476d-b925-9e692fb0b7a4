"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.teamScoringSystem = exports.TeamScoringSystem = void 0;
class TeamScoringSystem {
    constructor(config) {
        this.config = {
            basePoints: 100,
            difficultyMultiplier: 1.2,
            speedBonusThreshold: 10,
            maxSpeedBonus: 50,
            collaborationBonusPoints: 25,
            accuracyStreakBonus: 20,
            perfectTeamBonus: 100
        };
        this.config = { ...this.config, ...config };
    }
    calculateTeamScores(teams, teamAnswers, correctAnswer, questionDifficulty, timeLimit, scoringMode, gameMode) {
        const results = [];
        for (const team of teams) {
            const teamAnswer = teamAnswers.find(ta => ta.teamId === team.id);
            if (!teamAnswer) {
                results.push({
                    teamId: team.id,
                    pointsAwarded: 0,
                    bonusPoints: 0,
                    reason: 'No answer submitted',
                    breakdown: {
                        basePoints: 0,
                        difficultyMultiplier: 0,
                        speedBonus: 0,
                        collaborationBonus: 0,
                        accuracyBonus: 0
                    }
                });
                continue;
            }
            const isCorrect = teamAnswer.answer === correctAnswer;
            if (!isCorrect) {
                results.push({
                    teamId: team.id,
                    pointsAwarded: 0,
                    bonusPoints: 0,
                    reason: 'Incorrect answer',
                    breakdown: {
                        basePoints: 0,
                        difficultyMultiplier: 0,
                        speedBonus: 0,
                        collaborationBonus: 0,
                        accuracyBonus: 0
                    }
                });
                continue;
            }
            const scoreResult = this.calculateCorrectAnswerScore(team, teamAnswer, questionDifficulty, timeLimit, scoringMode, gameMode);
            results.push(scoreResult);
        }
        return results;
    }
    calculateCorrectAnswerScore(team, teamAnswer, difficulty, timeLimit, scoringMode, gameMode) {
        const timeTaken = (Date.now() - teamAnswer.timestamp) / 1000;
        let basePoints = this.config.basePoints;
        let bonusPoints = 0;
        let reason = '';
        const breakdown = {
            basePoints: basePoints,
            difficultyMultiplier: 0,
            speedBonus: 0,
            collaborationBonus: 0,
            accuracyBonus: 0
        };
        const difficultyBonus = basePoints * (difficulty - 1) * (this.config.difficultyMultiplier - 1);
        breakdown.difficultyMultiplier = difficultyBonus;
        basePoints += difficultyBonus;
        switch (scoringMode) {
            case 'sum':
                basePoints *= team.players.length;
                reason = `All ${team.players.length} team members contributed`;
                break;
            case 'average':
                reason = 'Team average scoring';
                break;
            case 'best':
                breakdown.collaborationBonus = this.config.collaborationBonusPoints;
                bonusPoints += this.config.collaborationBonusPoints;
                reason = 'Best team answer bonus';
                break;
            case 'captain':
                if (teamAnswer.submittedBy === team.captainId) {
                    breakdown.collaborationBonus = this.config.collaborationBonusPoints / 2;
                    bonusPoints += this.config.collaborationBonusPoints / 2;
                    reason = 'Captain leadership bonus';
                }
                break;
        }
        if (timeTaken <= this.config.speedBonusThreshold) {
            const speedMultiplier = Math.max(0, 1 - (timeTaken / this.config.speedBonusThreshold));
            const speedBonus = Math.round(this.config.maxSpeedBonus * speedMultiplier);
            breakdown.speedBonus = speedBonus;
            bonusPoints += speedBonus;
        }
        if (teamAnswer.collaborative && gameMode === 'collaborative') {
            const collabBonus = Math.round(this.config.collaborationBonusPoints * 0.5);
            breakdown.collaborationBonus += collabBonus;
            bonusPoints += collabBonus;
        }
        switch (gameMode) {
            case 'relay':
                bonusPoints += this.config.collaborationBonusPoints / 2;
                break;
            case 'specialist':
                bonusPoints += this.config.accuracyStreakBonus;
                break;
        }
        const totalPoints = basePoints + bonusPoints;
        return {
            teamId: team.id,
            pointsAwarded: Math.round(totalPoints),
            bonusPoints: Math.round(bonusPoints),
            reason: reason || 'Correct answer',
            breakdown
        };
    }
    calculateStreakBonuses(teams, streakCounts) {
        const results = [];
        for (const team of teams) {
            const streak = streakCounts.get(team.id) || 0;
            if (streak >= 3) {
                const streakBonus = this.config.accuracyStreakBonus * Math.floor(streak / 3);
                results.push({
                    teamId: team.id,
                    pointsAwarded: streakBonus,
                    bonusPoints: streakBonus,
                    reason: `${streak} answer streak bonus`,
                    breakdown: {
                        basePoints: 0,
                        difficultyMultiplier: 0,
                        speedBonus: 0,
                        collaborationBonus: 0,
                        accuracyBonus: streakBonus
                    }
                });
            }
        }
        return results;
    }
    calculatePerfectTeamBonus(teams, individualAnswers) {
        const results = [];
        for (const team of teams) {
            const teamMemberAnswers = team.players.map(player => individualAnswers.get(player.id)).filter(Boolean);
            const allCorrect = teamMemberAnswers.length === team.players.length &&
                teamMemberAnswers.every(answer => answer.correct);
            if (allCorrect) {
                results.push({
                    teamId: team.id,
                    pointsAwarded: this.config.perfectTeamBonus,
                    bonusPoints: this.config.perfectTeamBonus,
                    reason: 'Perfect team - all members correct!',
                    breakdown: {
                        basePoints: 0,
                        difficultyMultiplier: 0,
                        speedBonus: 0,
                        collaborationBonus: 0,
                        accuracyBonus: this.config.perfectTeamBonus
                    }
                });
            }
        }
        return results;
    }
    applyScoreResults(teams, scoreResults) {
        const updatedTeams = [...teams];
        for (const result of scoreResults) {
            const teamIndex = updatedTeams.findIndex(t => t.id === result.teamId);
            if (teamIndex !== -1) {
                updatedTeams[teamIndex] = {
                    ...updatedTeams[teamIndex],
                    score: updatedTeams[teamIndex].score + result.pointsAwarded
                };
            }
        }
        return updatedTeams;
    }
    getTeamLeaderboard(teams) {
        const sortedTeams = [...teams].sort((a, b) => b.score - a.score);
        return sortedTeams.map((team, index) => ({
            ...team,
            rank: index + 1,
            rankChange: 0
        }));
    }
    calculateIndividualContributions(team, teamAnswers, scoringMode) {
        const contributions = [];
        for (const player of team.players) {
            let contribution = 0;
            switch (scoringMode) {
                case 'sum':
                    contribution = team.score / team.players.length;
                    break;
                case 'captain':
                    if (player.isTeamCaptain) {
                        contribution = team.score * 0.7;
                    }
                    else {
                        contribution = (team.score * 0.3) / (team.players.length - 1);
                    }
                    break;
                case 'best':
                case 'average':
                default:
                    contribution = team.score / team.players.length;
                    break;
            }
            const percentage = team.score > 0 ? (contribution / team.score) * 100 : 0;
            contributions.push({
                playerId: player.id,
                contribution: Math.round(contribution),
                percentage: Math.round(percentage)
            });
        }
        return contributions;
    }
    generateScoringSummary(scoreResults, teams) {
        const totalPoints = scoreResults.reduce((sum, result) => sum + result.pointsAwarded, 0);
        const averageScore = scoreResults.length > 0 ? totalPoints / scoreResults.length : 0;
        const highestScore = Math.max(...scoreResults.map(r => r.pointsAwarded));
        const teamPerformance = teams.map(team => {
            const teamResult = scoreResults.find(r => r.teamId === team.id);
            const score = teamResult?.pointsAwarded || 0;
            const bonuses = teamResult?.bonusPoints || 0;
            const efficiency = team.players.length > 0 ? score / team.players.length : 0;
            return {
                teamName: team.name,
                score,
                bonuses,
                efficiency: Math.round(efficiency)
            };
        });
        return {
            totalPointsAwarded: totalPoints,
            averageScore: Math.round(averageScore),
            highestScore,
            teamPerformance
        };
    }
}
exports.TeamScoringSystem = TeamScoringSystem;
exports.teamScoringSystem = new TeamScoringSystem();
//# sourceMappingURL=team-scoring.js.map