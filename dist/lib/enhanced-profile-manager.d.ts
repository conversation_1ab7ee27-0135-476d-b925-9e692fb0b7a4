import type { UserRole } from '@/lib/user-context';
export interface EnhancedUserProfile {
    id: string;
    name: string;
    displayName?: string;
    avatar: string;
    soundPreference?: string;
    isGuest?: boolean;
    role?: UserRole;
    email?: string;
    username?: string;
    createdAt?: Date;
    lastActive?: Date;
}
export declare class EnhancedProfileManager {
    private static instance;
    static getInstance(): EnhancedProfileManager;
    getCurrentProfile(): EnhancedUserProfile | null;
    saveProfile(profile: EnhancedUserProfile): void;
    syncToDatabase(profile: EnhancedUserProfile): Promise<boolean>;
    createGuestProfile(): EnhancedUserProfile;
    updateUserRole(userId: string, newRole: UserRole): Promise<boolean>;
    getAllUsers(): Promise<any[]>;
    private normalizeProfile;
    reloadUserRole(userId?: string): Promise<UserRole | null>;
    initializeProfile(): Promise<EnhancedUserProfile>;
}
