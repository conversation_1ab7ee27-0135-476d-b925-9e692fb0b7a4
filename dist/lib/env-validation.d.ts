export declare function validateEnvironment(): void;
export declare function getEnvironment(): {
    readonly DATABASE_URL: string;
    readonly JWT_SECRET: string;
    readonly NEXTAUTH_SECRET: string;
    readonly NEXTAUTH_URL: string;
    readonly MPD_HOST: string;
    readonly MPD_PORT: number;
    readonly MPD_HTTP_PORT: number;
    readonly SOCKET_HOST: string;
    readonly WEBSOCKET_PORT: number;
    readonly SPOTIFY_CLIENT_ID: string | undefined;
    readonly SPOTIFY_CLIENT_SECRET: string | undefined;
    readonly LASTFM_API_KEY: string | undefined;
    readonly NODE_ENV: string;
    readonly IS_DEVELOPMENT: boolean;
    readonly IS_PRODUCTION: boolean;
    readonly SIMPLIFIED_ONBOARDING: boolean;
    readonly MULTIPLAYER_ENABLED: boolean;
};
