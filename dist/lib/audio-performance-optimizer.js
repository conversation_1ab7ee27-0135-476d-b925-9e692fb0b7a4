"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.audioPerformanceOptimizer = exports.AudioPerformanceOptimizer = void 0;
class AudioPerformanceOptimizer {
    constructor() {
        this.statusCache = null;
        this.CACHE_TTL = 1000;
        this.SLOW_OPERATION_THRESHOLD = 500;
        this.pendingOperations = new Map();
    }
    async getOptimizedAudioStatus(audioManager) {
        const now = Date.now();
        if (this.statusCache && (now - this.statusCache.timestamp) < this.statusCache.ttl) {
            return {
                ...this.statusCache.status,
                cached: true
            };
        }
        const operationKey = 'getAudioStatus';
        if (this.pendingOperations.has(operationKey)) {
            try {
                const result = await this.pendingOperations.get(operationKey);
                return {
                    ...result,
                    cached: false
                };
            }
            catch (error) {
                console.warn('[AudioOptimizer] Pending operation failed:', error);
            }
        }
        const operation = this.getAudioStatusWithTimeout(audioManager, 3000);
        this.pendingOperations.set(operationKey, operation);
        try {
            const status = await operation;
            this.statusCache = {
                status,
                timestamp: now,
                ttl: this.CACHE_TTL
            };
            this.pendingOperations.delete(operationKey);
            return {
                ...status,
                cached: false
            };
        }
        catch (error) {
            console.error('[AudioOptimizer] Failed to get audio status:', error);
            this.pendingOperations.delete(operationKey);
            return this.getFallbackStatus();
        }
    }
    async getAudioStatusWithTimeout(audioManager, timeoutMs) {
        const startTime = Date.now();
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Audio status timeout after ${timeoutMs}ms`));
            }, timeoutMs);
        });
        try {
            const status = await Promise.race([
                audioManager.getAudioStatus(),
                timeoutPromise
            ]);
            const duration = Date.now() - startTime;
            if (duration > this.SLOW_OPERATION_THRESHOLD) {
                console.warn(`[AudioOptimizer] Slow audio operation: ${duration}ms`);
            }
            return {
                isPlaying: status.isPlaying || false,
                currentTrack: status.currentTrack || null,
                volume: status.volume || 70,
                progress: status.progress || 0,
                duration: status.duration || 0,
                cached: false
            };
        }
        catch (error) {
            if (error.message.includes('timeout')) {
                console.warn('[AudioOptimizer] Audio status operation timed out, using cached/fallback data');
                return this.statusCache?.status || this.getFallbackStatus();
            }
            throw error;
        }
    }
    async getQuickAudioStatus(audioManager) {
        const now = Date.now();
        if (this.statusCache && (now - this.statusCache.timestamp) < 5000) {
            return {
                ...this.statusCache.status,
                cached: true
            };
        }
        try {
            const status = await this.getAudioStatusWithTimeout(audioManager, 1000);
            return status;
        }
        catch (error) {
            console.warn('[AudioOptimizer] Quick status failed, using fallback');
            return this.getFallbackStatus();
        }
    }
    preloadAudioStatus(audioManager) {
        if (!this.pendingOperations.has('preload')) {
            const preloadOperation = this.getAudioStatusWithTimeout(audioManager, 5000)
                .then(status => {
                this.statusCache = {
                    status,
                    timestamp: Date.now(),
                    ttl: this.CACHE_TTL
                };
            })
                .catch(error => {
                console.warn('[AudioOptimizer] Preload failed:', error);
            })
                .finally(() => {
                this.pendingOperations.delete('preload');
            });
            this.pendingOperations.set('preload', preloadOperation);
        }
    }
    getFallbackStatus() {
        return {
            isPlaying: false,
            currentTrack: null,
            volume: 70,
            progress: 0,
            duration: 0,
            cached: true
        };
    }
    clearCache() {
        this.statusCache = null;
        this.pendingOperations.clear();
    }
    async isAudioSystemResponsive(audioManager) {
        try {
            const startTime = Date.now();
            await this.getAudioStatusWithTimeout(audioManager, 2000);
            const duration = Date.now() - startTime;
            return duration < this.SLOW_OPERATION_THRESHOLD;
        }
        catch (error) {
            return false;
        }
    }
    getPerformanceMetrics() {
        return {
            cacheAge: this.statusCache ? Date.now() - this.statusCache.timestamp : null,
            hasPendingOperations: this.pendingOperations.size > 0,
            pendingOperationCount: this.pendingOperations.size
        };
    }
}
exports.AudioPerformanceOptimizer = AudioPerformanceOptimizer;
exports.audioPerformanceOptimizer = new AudioPerformanceOptimizer();
//# sourceMappingURL=audio-performance-optimizer.js.map