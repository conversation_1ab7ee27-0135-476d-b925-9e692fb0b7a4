import type { MPDStatus, MPDConnectionConfig, TrackWithMetadata } from '@/lib/types';
export interface AudioManagerConfig extends MPDConnectionConfig {
    statusUpdateInterval?: number;
    enableLogging?: boolean;
    autoReconnect?: boolean;
    maxReconnectAttempts?: number;
}
export interface AudioStatus {
    isConnected: boolean;
    isPlaying: boolean;
    isPaused: boolean;
    currentTrack: TrackWithMetadata | null;
    volume: number;
    elapsed: number;
    duration: number;
    position: string;
    error?: string;
    mpdStatus?: MPDStatus;
}
export type AudioEventType = 'statusUpdate' | 'trackChanged' | 'trackEnded' | 'connected' | 'disconnected' | 'error' | 'volumeChanged';
export interface AudioEvent {
    type: AudioEventType;
    data?: any;
    timestamp: number;
}
export type AudioEventCallback = (event: AudioEvent) => void;
export declare class AudioManager {
    private mpdClient;
    private config;
    private isInitialized;
    private isConnected;
    private statusInterval;
    private lastStatus;
    private lastStatusTime;
    private eventCallbacks;
    private currentTrack;
    private trackHistory;
    private trackQueue;
    private reconnectAttempts;
    private reconnectTimer;
    private fadeOutTimer;
    private quizTimeLimit;
    private quizTimeoutTimer;
    private isFading;
    private userVolume;
    private volumeNormalizationEnabled;
    constructor(config: AudioManagerConfig);
    initialize(): Promise<void>;
    isReady(): boolean;
    getConnectionStatus(): {
        connected: boolean;
        host: string;
        port: number;
    };
    playTrack(track: TrackWithMetadata | string, options?: {
        clearQueue?: boolean;
        addToHistory?: boolean;
        startTime?: number;
    }): Promise<void>;
    pause(): Promise<void>;
    resume(): Promise<void>;
    stop(): Promise<void>;
    togglePlayback(): Promise<void>;
    nextTrack(): Promise<void>;
    previousTrack(): Promise<void>;
    seek(time: number): Promise<void>;
    setVolume(volume: number, isInternalFade?: boolean): Promise<void>;
    private calculateNormalizedVolume;
    setVolumeNormalizationEnabled(enabled: boolean): void;
    isVolumeNormalizationEnabled(): boolean;
    fadeOut(duration?: number): Promise<void>;
    fadeIn(targetVolume?: number, duration?: number): Promise<void>;
    playQuizTrack(track: TrackWithMetadata | string, timeLimit?: number, previewStart?: number): Promise<void>;
    stopQuizTrack(): Promise<void>;
    getUserVolume(): number;
    isFadingVolume(): boolean;
    fadeOutQuizTrack(duration?: number): Promise<void>;
    addToQueue(tracks: TrackWithMetadata[]): void;
    clearQueue(): void;
    getTrackHistory(limit?: number): TrackWithMetadata[];
    setQuizTimeLimit(seconds: number): void;
    getAudioStatus(): Promise<AudioStatus>;
    getCurrentTrack(): TrackWithMetadata | null;
    on(eventType: AudioEventType, callback: AudioEventCallback): void;
    off(eventType: AudioEventType, callback: AudioEventCallback): void;
    private emitEvent;
    private startStatusMonitoring;
    private scheduleReconnect;
    private clearFadeTimer;
    private formatTime;
    private formatPosition;
    private log;
    cleanup(): Promise<void>;
    private generateDefaultStatus;
}
