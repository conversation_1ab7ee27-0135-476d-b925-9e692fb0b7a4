export declare class MemoryManager {
    private static instance;
    private memoryThresholds;
    static getInstance(): MemoryManager;
    checkMemoryPressure(): 'normal' | 'warning' | 'critical';
    getMemoryStats(): {
        used: any;
        total: any;
        limit: any;
        usedMB: number;
        pressure: "normal" | "warning" | "critical";
    } | null;
    adaptToMemoryPressure(pressure: 'normal' | 'warning' | 'critical'): {
        virtualListOverscan: number;
        maxCacheSize: number;
        pageSize: number;
    };
}
export declare function useDebounce<T>(value: T, delay: number): T;
export declare function useThrottle<T extends (...args: any[]) => any>(callback: T, delay: number): T;
export interface VirtualScrollOptions {
    itemHeight: number;
    containerHeight: number;
    overscan?: number;
}
export declare function calculateVirtualItems(scrollTop: number, itemCount: number, options: VirtualScrollOptions): {
    startIndex: number;
    endIndex: number;
    offsetY: number;
    totalHeight: number;
    visibleItems: number;
};
export declare function fuzzySearch(items: any[], searchTerm: string, searchFields: string[]): any[];
export declare function memoize<T extends (...args: any[]) => any>(fn: T): T;
export declare class CacheManager<T> {
    private cache;
    set(key: string, data: T, ttl?: number): void;
    get(key: string): T | null;
    has(key: string): boolean;
    delete(key: string): void;
    clear(): void;
    private cleanup;
    size(): number;
}
