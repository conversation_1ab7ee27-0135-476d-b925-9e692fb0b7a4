"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeneralQuizService = exports.quizCategories = void 0;
exports.quizCategories = [
    {
        id: 'general',
        name: 'General Knowledge',
        description: 'A mix of questions from various topics',
        icon: 'brain',
        color: 'from-blue-500 to-indigo-500',
        totalQuestions: 200
    },
    {
        id: 'science',
        name: 'Science & Nature',
        description: 'Physics, chemistry, biology, and nature',
        icon: 'lightbulb',
        color: 'from-green-500 to-teal-500',
        totalQuestions: 150
    },
    {
        id: 'history',
        name: 'History',
        description: 'World history, ancient civilizations, and historical events',
        icon: 'book-open',
        color: 'from-amber-500 to-orange-500',
        totalQuestions: 180
    },
    {
        id: 'geography',
        name: 'Geography',
        description: 'Countries, capitals, landmarks, and physical geography',
        icon: 'globe',
        color: 'from-cyan-500 to-blue-500',
        totalQuestions: 160
    },
    {
        id: 'sports',
        name: 'Sports',
        description: 'Sports trivia, athletes, and sporting events',
        icon: 'target',
        color: 'from-red-500 to-pink-500',
        totalQuestions: 120
    },
    {
        id: 'entertainment',
        name: 'Entertainment',
        description: 'Movies, TV shows, celebrities, and pop culture',
        icon: 'star',
        color: 'from-yellow-500 to-amber-500',
        totalQuestions: 140
    },
    {
        id: 'technology',
        name: 'Technology',
        description: 'Computers, internet, gadgets, and tech innovations',
        icon: 'laptop',
        color: 'from-purple-500 to-violet-500',
        totalQuestions: 100
    },
    {
        id: 'literature',
        name: 'Literature',
        description: 'Books, authors, poetry, and literary works',
        icon: 'book',
        color: 'from-rose-500 to-pink-500',
        totalQuestions: 110
    }
];
const generalKnowledgeQuestions = [
    {
        id: 'gen_001',
        type: 'multiple-choice',
        category: 'general',
        difficulty: 2,
        question: 'What is the capital of Australia?',
        options: ['Sydney', 'Melbourne', 'Canberra', 'Perth'],
        correctAnswer: 'Canberra',
        explanation: 'While Sydney is the largest city, Canberra is the capital of Australia.',
        timeLimit: 30,
        points: 100
    },
    {
        id: 'gen_002',
        type: 'true-false',
        category: 'general',
        difficulty: 1,
        question: 'The Great Wall of China is visible from space with the naked eye.',
        correctAnswer: 'False',
        explanation: 'This is a common myth. The Great Wall is not visible from space without aid.',
        timeLimit: 20,
        points: 80
    },
    {
        id: 'gen_003',
        type: 'multiple-choice',
        category: 'general',
        difficulty: 3,
        question: 'Which planet in our solar system has the most moons?',
        options: ['Jupiter', 'Saturn', 'Uranus', 'Neptune'],
        correctAnswer: 'Saturn',
        explanation: 'Saturn has 146 confirmed moons, more than any other planet in our solar system.',
        timeLimit: 35,
        points: 150
    }
];
const scienceQuestions = [
    {
        id: 'sci_001',
        type: 'multiple-choice',
        category: 'science',
        difficulty: 3,
        question: 'What is the chemical symbol for gold?',
        options: ['Go', 'Gd', 'Au', 'Ag'],
        correctAnswer: 'Au',
        explanation: 'Au comes from the Latin word "aurum" meaning gold.',
        timeLimit: 25,
        points: 120
    },
    {
        id: 'sci_002',
        type: 'true-false',
        category: 'science',
        difficulty: 2,
        question: 'Sound travels faster in water than in air.',
        correctAnswer: 'True',
        explanation: 'Sound travels about 4 times faster in water than in air due to water\'s higher density.',
        timeLimit: 25,
        points: 100
    },
    {
        id: 'sci_003',
        type: 'slider',
        category: 'science',
        difficulty: 4,
        question: 'At what temperature (in Celsius) does water boil at sea level?',
        correctAnswer: 100,
        explanation: 'Water boils at 100°C (212°F) at sea level atmospheric pressure.',
        timeLimit: 30,
        points: 160,
        hints: ['Think about the most common temperature reference point']
    }
];
const historyQuestions = [
    {
        id: 'hist_001',
        type: 'multiple-choice',
        category: 'history',
        difficulty: 2,
        question: 'In which year did World War II end?',
        options: ['1944', '1945', '1946', '1947'],
        correctAnswer: '1945',
        explanation: 'World War II ended in 1945 with Japan\'s surrender in September.',
        timeLimit: 30,
        points: 100
    },
    {
        id: 'hist_002',
        type: 'multiple-choice',
        category: 'history',
        difficulty: 4,
        question: 'Who was the first person to walk on the moon?',
        options: ['Neil Armstrong', 'Buzz Aldrin', 'John Glenn', 'Alan Shepard'],
        correctAnswer: 'Neil Armstrong',
        explanation: 'Neil Armstrong was the first human to step onto the moon on July 20, 1969.',
        timeLimit: 25,
        points: 140
    }
];
const geographyQuestions = [
    {
        id: 'geo_001',
        type: 'multiple-choice',
        category: 'geography',
        difficulty: 2,
        question: 'Which is the longest river in the world?',
        options: ['Amazon River', 'Nile River', 'Yangtze River', 'Mississippi River'],
        correctAnswer: 'Nile River',
        explanation: 'The Nile River is approximately 6,650 km long, making it the longest river.',
        timeLimit: 30,
        points: 100
    },
    {
        id: 'geo_002',
        type: 'true-false',
        category: 'geography',
        difficulty: 3,
        question: 'Russia spans 11 time zones.',
        correctAnswer: 'True',
        explanation: 'Russia is so large that it spans 11 time zones, more than any other country.',
        timeLimit: 25,
        points: 120
    }
];
const sportsQuestions = [
    {
        id: 'sport_001',
        type: 'multiple-choice',
        category: 'sports',
        difficulty: 2,
        question: 'How many players are on a basketball team on the court at one time?',
        options: ['4', '5', '6', '7'],
        correctAnswer: '5',
        explanation: 'Each basketball team has 5 players on the court at any given time.',
        timeLimit: 20,
        points: 80
    },
    {
        id: 'sport_002',
        type: 'multiple-choice',
        category: 'sports',
        difficulty: 3,
        question: 'In which sport would you perform a slam dunk?',
        options: ['Volleyball', 'Basketball', 'Tennis', 'Baseball'],
        correctAnswer: 'Basketball',
        explanation: 'A slam dunk is a basketball move where the player jumps and scores by putting the ball directly through the hoop.',
        timeLimit: 25,
        points: 120
    }
];
const entertainmentQuestions = [
    {
        id: 'ent_001',
        type: 'multiple-choice',
        category: 'entertainment',
        difficulty: 2,
        question: 'Which movie won the Academy Award for Best Picture in 2020?',
        options: ['1917', 'Joker', 'Parasite', 'Once Upon a Time in Hollywood'],
        correctAnswer: 'Parasite',
        explanation: 'Parasite became the first non-English film to win Best Picture at the Academy Awards.',
        timeLimit: 30,
        points: 100
    },
    {
        id: 'ent_002',
        type: 'true-false',
        category: 'entertainment',
        difficulty: 3,
        question: 'The TV series "Friends" ran for 10 seasons.',
        correctAnswer: 'True',
        explanation: 'Friends aired for 10 seasons from 1994 to 2004, with 236 episodes total.',
        timeLimit: 25,
        points: 120
    }
];
const questionPools = {
    general: generalKnowledgeQuestions,
    science: scienceQuestions,
    history: historyQuestions,
    geography: geographyQuestions,
    sports: sportsQuestions,
    entertainment: entertainmentQuestions,
    technology: [],
    literature: []
};
class GeneralQuizService {
    static getInstance() {
        if (!GeneralQuizService.instance) {
            GeneralQuizService.instance = new GeneralQuizService();
        }
        return GeneralQuizService.instance;
    }
    getCategories() {
        return exports.quizCategories;
    }
    getCategoryById(id) {
        return exports.quizCategories.find(cat => cat.id === id);
    }
    getQuestionsByCategory(categoryId, count = 10, difficulty) {
        const questions = questionPools[categoryId] || [];
        let filteredQuestions = questions;
        if (difficulty) {
            filteredQuestions = questions.filter(q => q.difficulty === difficulty);
        }
        const shuffled = [...filteredQuestions].sort(() => Math.random() - 0.5);
        return shuffled.slice(0, count);
    }
    getMixedQuestions(count = 10, difficulty) {
        const allQuestions = [];
        Object.values(questionPools).forEach(pool => {
            allQuestions.push(...pool);
        });
        let filteredQuestions = allQuestions;
        if (difficulty) {
            filteredQuestions = allQuestions.filter(q => q.difficulty === difficulty);
        }
        const shuffled = [...filteredQuestions].sort(() => Math.random() - 0.5);
        return shuffled.slice(0, count);
    }
    getRandomQuestion(categoryId, difficulty) {
        const questions = categoryId
            ? this.getQuestionsByCategory(categoryId, 1000, difficulty)
            : this.getMixedQuestions(1000, difficulty);
        if (questions.length === 0)
            return null;
        const randomIndex = Math.floor(Math.random() * questions.length);
        return questions[randomIndex];
    }
    validateAnswer(questionId, answer) {
        for (const pool of Object.values(questionPools)) {
            const question = pool.find(q => q.id === questionId);
            if (question) {
                return question.correctAnswer === answer;
            }
        }
        return false;
    }
    getQuestionStats(categoryId) {
        const questions = categoryId
            ? questionPools[categoryId] || []
            : Object.values(questionPools).flat();
        const stats = {
            total: questions.length,
            byDifficulty: {
                1: questions.filter(q => q.difficulty === 1).length,
                2: questions.filter(q => q.difficulty === 2).length,
                3: questions.filter(q => q.difficulty === 3).length,
                4: questions.filter(q => q.difficulty === 4).length,
                5: questions.filter(q => q.difficulty === 5).length
            },
            byType: {
                'multiple-choice': questions.filter(q => q.type === 'multiple-choice').length,
                'true-false': questions.filter(q => q.type === 'true-false').length,
                'slider': questions.filter(q => q.type === 'slider').length,
                'audio-identification': questions.filter(q => q.type === 'audio-identification').length
            }
        };
        return stats;
    }
}
exports.GeneralQuizService = GeneralQuizService;
//# sourceMappingURL=general-knowledge.js.map