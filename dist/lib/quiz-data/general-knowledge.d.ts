import type { QuizQuestion } from '@/components/enhanced-quiz-interface';
export interface QuizCategory {
    id: string;
    name: string;
    description: string;
    icon: string;
    color: string;
    totalQuestions: number;
}
export declare const quizCategories: QuizCategory[];
export declare class GeneralQuizService {
    private static instance;
    static getInstance(): GeneralQuizService;
    getCategories(): QuizCategory[];
    getCategoryById(id: string): QuizCategory | undefined;
    getQuestionsByCategory(categoryId: string, count?: number, difficulty?: number): QuizQuestion[];
    getMixedQuestions(count?: number, difficulty?: number): QuizQuestion[];
    getRandomQuestion(categoryId?: string, difficulty?: number): QuizQuestion | null;
    validateAnswer(questionId: string, answer: string | number): boolean;
    getQuestionStats(categoryId?: string): {
        total: number;
        byDifficulty: {
            1: number;
            2: number;
            3: number;
            4: number;
            5: number;
        };
        byType: {
            'multiple-choice': number;
            'true-false': number;
            slider: number;
            'audio-identification': number;
        };
    };
}
