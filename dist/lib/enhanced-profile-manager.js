"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedProfileManager = void 0;
class EnhancedProfileManager {
    static getInstance() {
        if (!Enhanced) {
            Enhanced = new EnhancedProfileManager();
        }
        return Enhanced;
    }
    getCurrentProfile() {
        if (typeof window === 'undefined')
            return null;
        try {
            const profileJson = localStorage.getItem('jukebox-profile');
            if (profileJson) {
                const profile = JSON.parse(profileJson);
                if (profile && profile.id && profile.name && profile.avatar) {
                    return this.normalizeProfile(profile);
                }
            }
        }
        catch (error) {
            console.error('Failed to get jukebox profile:', error);
        }
        try {
            const userJson = localStorage.getItem('user');
            if (userJson) {
                const user = JSON.parse(userJson);
                if (user && user.id) {
                    return this.normalizeProfile({
                        id: user.id,
                        name: user.username || user.displayName || 'User',
                        displayName: user.displayName,
                        avatar: user.avatar || '👤',
                        role: user.role,
                        email: user.email,
                        username: user.username
                    });
                }
            }
        }
        catch (error) {
            console.error('Failed to get user context profile:', error);
        }
        return null;
    }
    saveProfile(profile) {
        if (typeof window === 'undefined')
            return;
        try {
            const profileManagerFormat = {
                id: profile.id,
                name: profile.name,
                displayName: profile.displayName || profile.name,
                avatar: profile.avatar,
                soundPreference: profile.soundPreference || 'default',
                isGuest: profile.isGuest || false
            };
            const userContextFormat = {
                id: profile.id,
                username: profile.username || profile.name,
                displayName: profile.displayName || profile.name,
                email: profile.email || `${profile.id}@local.user`,
                avatar: profile.avatar,
                role: profile.role || 'user',
                level: 1,
                xp: 0,
                xpToNext: 100,
                joinDate: new Date().toLocaleDateString(),
                stats: {
                    totalGames: 0,
                    winRate: 0,
                    averageScore: 0,
                    bestStreak: 0,
                    hoursPlayed: 0,
                    achievements: 0,
                    rank: 0,
                    favoriteMode: 'Classic Quiz'
                },
                achievements: [],
                playlists: [],
                settings: {
                    emailNotifications: true,
                    soundEffects: true,
                    autoPlay: true,
                    showHints: true,
                    publicProfile: true,
                    shareStats: false
                }
            };
            console.log('Profile saved to localStorage:', profile.displayName || profile.name);
        }
        catch (error) {
            console.error('Failed to save profile:', error);
        }
    }
    async syncToDatabase(profile) {
        try {
            const existingUsersResponse = await fetch('/api/admin/roles');
            let existingUser = null;
            if (existingUsersResponse.ok) {
                const data = await existingUsersResponse.json();
                if (data.success && data.users) {
                    existingUser = data.users.find((u) => u.id === profile.id ||
                        u.username === profile.username ||
                        u.email === profile.email);
                }
            }
            if (existingUser) {
                console.log(`[ProfileManager] Updating existing user: ${existingUser.username}`);
                const response = await fetch('/api/admin/roles', {
                    method: 'PATCH',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: existingUser.id,
                        role: profile.role || 'user'
                    })
                });
                if (response.ok) {
                    console.log('Profile updated in database:', profile.displayName || profile.name);
                    return true;
                }
                else {
                    console.error('Failed to update profile in database:', await response.text());
                    return false;
                }
            }
            else {
                console.log(`[ProfileManager] Creating new user: ${profile.username || profile.name}`);
                const response = await fetch('/api/admin/roles', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        displayName: profile.displayName || profile.name,
                        username: profile.username || profile.name || `user_${profile.id}`,
                        email: profile.email || `${profile.id}@local.user`,
                        role: profile.role || 'user',
                        adminUserId: 'system'
                    })
                });
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.user) {
                        profile.id = data.user.id;
                        this.saveProfile(profile);
                    }
                    console.log('Profile synced to database:', profile.displayName || profile.name);
                    return true;
                }
                else {
                    console.error('Failed to sync profile to database:', await response.text());
                    return false;
                }
            }
        }
        catch (error) {
            console.error('Error syncing profile to database:', error);
            return false;
        }
    }
    createGuestProfile() {
        const guestProfile = {
            id: `guest-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            name: 'Guest',
            displayName: 'Guest',
            avatar: '👤',
            soundPreference: 'default',
            isGuest: true,
            role: 'user',
            createdAt: new Date()
        };
        this.saveProfile(guestProfile);
        return guestProfile;
    }
    async updateUserRole(userId, newRole) {
        try {
            const response = await fetch('/api/admin/roles', {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId,
                    role: newRole
                })
            });
            if (response.ok) {
                const currentProfile = this.getCurrentProfile();
                if (currentProfile && currentProfile.id === userId) {
                    currentProfile.role = newRole;
                    this.saveProfile(currentProfile);
                }
                return true;
            }
            return false;
        }
        catch (error) {
            console.error('Failed to update user role:', error);
            return false;
        }
    }
    async getAllUsers() {
        try {
            const response = await fetch('/api/admin/roles');
            const data = await response.json();
            if (data.success) {
                return data.users;
            }
            return [];
        }
        catch (error) {
            console.error('Failed to get users:', error);
            return [];
        }
    }
    normalizeProfile(profile) {
        return {
            id: profile.id,
            name: profile.name || profile.username || 'User',
            displayName: profile.displayName || profile.name || profile.username,
            avatar: profile.avatar || '👤',
            soundPreference: profile.soundPreference || 'default',
            isGuest: profile.isGuest || false,
            role: profile.role || 'user',
            email: profile.email,
            username: profile.username || profile.name,
            createdAt: profile.createdAt ? new Date(profile.createdAt) : new Date(),
            lastActive: profile.lastActive ? new Date(profile.lastActive) : new Date()
        };
    }
    async reloadUserRole(userId) {
        try {
            const response = await fetch('/api/admin/roles');
            const data = await response.json();
            if (data.success && data.users) {
                const currentProfile = this.getCurrentProfile();
                if (!currentProfile && !userId)
                    return null;
                const targetUserId = userId || currentProfile?.id;
                const dbUser = data.users.find((u) => u.id === targetUserId ||
                    u.username === currentProfile?.username ||
                    u.email === currentProfile?.email);
                if (dbUser && currentProfile) {
                    currentProfile.role = dbUser.role;
                    this.saveProfile(currentProfile);
                    console.log(`[ProfileManager] Role reloaded from database: ${dbUser.role}`);
                    return dbUser.role;
                }
            }
            return null;
        }
        catch (error) {
            console.error('Failed to reload user role:', error);
            return null;
        }
    }
    async initializeProfile() {
        let profile = this.getCurrentProfile();
        if (!profile) {
            profile = this.createGuestProfile();
        }
        await this.syncToDatabase(profile);
        return profile;
    }
}
exports.EnhancedProfileManager = EnhancedProfileManager;
//# sourceMappingURL=enhanced-profile-manager.js.map