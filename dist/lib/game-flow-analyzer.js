"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameFlowAnalyzer = void 0;
exports.analyzeGameMode = analyzeGameMode;
const playwright_utils_1 = require("./playwright-utils");
class GameFlowAnalyzer {
    constructor(config) {
        this.currentRound = null;
        this.rounds = [];
        this.gameStartTime = 0;
        this.pages = [];
        this.automation = new playwright_utils_1.MusicQuizAutomation({ headless: false });
        this.config = config;
    }
    async initialize() {
        await this.automation.initialize();
    }
    async close() {
        for (const page of this.pages) {
            await page.close();
        }
        await this.automation.close();
    }
    async analyzeGameFlow(playerCount = 2) {
        try {
            await this.setupGame(playerCount);
            const analysis = await this.monitorGame();
            const validationResults = this.validateGameBehavior(analysis);
            return {
                ...analysis,
                validationResults
            };
        }
        catch (error) {
            console.error('Game flow analysis error:', error);
            throw error;
        }
    }
    async setupGame(playerCount) {
        const hostPage = await this.automation.createPage();
        this.pages.push(hostPage);
        await hostPage.goto('http://localhost:3000');
        await this.handleOnboarding(hostPage);
        let multiplayerLink = await hostPage.locator('a[href="/multiplayer"]').first();
        if (await multiplayerLink.count() > 0) {
            console.log('Found multiplayer link, clicking...');
            await multiplayerLink.click();
            await hostPage.waitForURL('**/multiplayer', { timeout: 10000 });
            await hostPage.waitForLoadState('networkidle');
            console.log('Navigated to multiplayer page');
        }
        else {
            console.log('Multiplayer link not found, trying direct navigation...');
            await hostPage.goto('http://localhost:3000/multiplayer');
            await hostPage.waitForLoadState('networkidle');
        }
        const currentUrl = hostPage.url();
        console.log(`Current URL: ${currentUrl}`);
        await hostPage.screenshot({ path: 'debug-multiplayer-page.png' });
        console.log('Screenshot saved as debug-multiplayer-page.png');
        let hostGameButton = null;
        const hostButtonSelectors = [
            'button:has-text("Host Game")',
            'button:has(svg.lucide-tv)',
            'button.bg-gradient-to-r.from-blue-500.to-purple-600',
            'div.grid button:first-child',
            'text=Host Game'
        ];
        for (const selector of hostButtonSelectors) {
            console.log(`Trying selector: ${selector}`);
            hostGameButton = await hostPage.$(selector);
            if (hostGameButton) {
                console.log(`Found host button with selector: ${selector}`);
                break;
            }
        }
        if (!hostGameButton) {
            console.log('Host button not found immediately, waiting...');
            await hostPage.waitForTimeout(3000);
            const errorMessage = await hostPage.$('text=Error, text=error, .error-message');
            if (errorMessage) {
                const errorText = await errorMessage.textContent();
                console.error(`Error on page: ${errorText}`);
            }
            hostGameButton = await hostPage.$('button:has-text("Host Game")');
        }
        if (hostGameButton) {
            console.log('Selecting host role...');
            await hostGameButton.click();
            await hostPage.waitForTimeout(1000);
        }
        else {
            console.error('Could not find Host Game button!');
            console.log('Trying direct URL navigation to multiplayer...');
            await hostPage.goto('http://localhost:3000/multiplayer');
            await hostPage.waitForLoadState('networkidle');
            await hostPage.waitForTimeout(2000);
            hostGameButton = await hostPage.$('button:has-text("Host Game")');
            if (hostGameButton) {
                await hostGameButton.click();
                await hostPage.waitForTimeout(1000);
            }
        }
        const connectingMessage = await hostPage.locator('text=Connecting to game server').count();
        if (connectingMessage > 0) {
            console.log('Waiting for WebSocket connection...');
            try {
                await hostPage.waitForSelector('text=Connecting to game server', { state: 'hidden', timeout: 15000 });
                console.log('WebSocket connected');
            }
            catch (e) {
                console.error('WebSocket connection timeout - is the socket server running?');
                console.error('Run: npm run socket-server');
                throw new Error('WebSocket connection failed');
            }
        }
        const nameInputSelectors = [
            'input#host-name',
            'input[placeholder*="name"]',
            'input[placeholder*="Name"]'
        ];
        let playerNameInput = null;
        for (const selector of nameInputSelectors) {
            playerNameInput = await hostPage.$(selector);
            if (playerNameInput) {
                console.log(`Found name input with selector: ${selector}`);
                break;
            }
        }
        if (playerNameInput) {
            console.log('Setting player name...');
            await playerNameInput.fill('Host');
            await hostPage.waitForTimeout(1000);
        }
        else {
            console.log('No name input found - user might be logged in');
        }
        console.log('Waiting for game creation...');
        console.log('Waiting for game to be created...');
        await hostPage.waitForSelector('[data-testid="room-code"], .room-code, text=/[A-Z0-9]{4,6}/', { timeout: 10000 });
        let roomCode = await hostPage.textContent('[data-testid="room-code"], .room-code');
        if (!roomCode) {
            const roomCodeElement = await hostPage.locator('text=/[A-Z0-9]{4,6}/').first();
            roomCode = await roomCodeElement.textContent();
        }
        console.log(`Room code: ${roomCode}`);
        const gameModeSelect = await hostPage.$('select[name="gameMode"], [data-testid="game-mode"]');
        if (gameModeSelect) {
            await gameModeSelect.selectOption(this.config.mode);
            await hostPage.waitForTimeout(500);
        }
        const roundsInput = await hostPage.$('input[name="rounds"], [data-testid="rounds-per-game"]');
        if (roundsInput) {
            await roundsInput.fill('');
            await roundsInput.fill(this.config.roundsPerGame.toString());
        }
        const secondsInput = await hostPage.$('input[name="secondsPerRound"], [data-testid="seconds-per-round"]');
        if (secondsInput) {
            await secondsInput.fill('');
            await secondsInput.fill(this.config.secondsPerRound.toString());
        }
        if (this.config.categories && this.config.categories.length > 0) {
            for (const category of this.config.categories) {
                const categoryCheckbox = await hostPage.$(`input[value="${category}"], [data-testid="category-${category}"]`);
                if (categoryCheckbox) {
                    await categoryCheckbox.check();
                }
            }
        }
        if (this.config.mode === 'ultimote' && this.config.customSettings) {
            for (const [key, value] of Object.entries(this.config.customSettings)) {
                const selector = `[data-testid="setting-${key}"], input[name="${key}"]`;
                const element = await hostPage.$(selector);
                if (element) {
                    if (typeof value === 'boolean') {
                        if (value)
                            await element.check();
                        else
                            await element.uncheck();
                    }
                    else {
                        await element.fill(value.toString());
                    }
                }
            }
        }
        for (let i = 1; i < playerCount; i++) {
            const playerPage = await this.automation.createPage();
            this.pages.push(playerPage);
            await playerPage.goto('http://localhost:3000');
            await this.handleOnboarding(playerPage);
            const multiplayerBtn = await playerPage.$('a[href="/multiplayer"], button:has-text("Multiplayer")');
            if (multiplayerBtn) {
                await multiplayerBtn.click();
                await playerPage.waitForLoadState('networkidle');
            }
            const joinGameButton = await playerPage.$('button:has-text("Join Game")');
            if (joinGameButton) {
                await joinGameButton.click();
                await playerPage.waitForTimeout(1000);
            }
            const playerNameInput = await playerPage.$('input[placeholder*="name"], input[placeholder*="Name"], input#player-name');
            if (playerNameInput) {
                await playerNameInput.fill(`Player${i}`);
                await playerPage.keyboard.press('Enter');
                await playerPage.waitForTimeout(500);
            }
            const roomCodeInput = await playerPage.$('input[placeholder*="code"], input[placeholder*="Code"], input#room-code');
            if (roomCodeInput) {
                await roomCodeInput.fill(roomCode);
                await playerPage.waitForTimeout(500);
            }
            const joinButton = await playerPage.$('button:has-text("Join"), button:has-text("Enter Room")');
            if (joinButton) {
                await joinButton.click();
            }
            await playerPage.waitForSelector('[data-testid="game-lobby"], .game-lobby, text=Lobby', { timeout: 10000 });
        }
        await hostPage.waitForTimeout(2000);
        await hostPage.click('[data-testid="start-game"]');
        this.gameStartTime = Date.now();
    }
    async handleOnboarding(page) {
        try {
            const continueAsGuestButton = await page.$('button:has-text("Continue as Guest")');
            const guestModeButton = await page.$('button:has-text("Guest Mode")');
            if (continueAsGuestButton) {
                console.log('Clicking "Continue as Guest" button...');
                await continueAsGuestButton.click();
                await page.waitForTimeout(1000);
            }
            else if (guestModeButton) {
                console.log('Clicking "Guest Mode" button...');
                await guestModeButton.click();
                await page.waitForTimeout(1000);
            }
            const welcomeText = await page.locator('text=Welcome to Music Quiz').count();
            const welcomeHeading = await page.locator('h1:has-text("Welcome"), h2:has-text("Welcome")').count();
            if (welcomeText === 0 && welcomeHeading === 0) {
                const playerNameInput = await page.$('input[placeholder*="name"], input[placeholder*="Name"]');
                if (playerNameInput) {
                    console.log('Already past onboarding');
                    return;
                }
            }
            console.log('Onboarding screen detected, handling it...');
            let attempts = 0;
            const maxAttempts = 10;
            while (attempts < maxAttempts) {
                await page.waitForTimeout(500);
                const skipButton = await page.locator('button:has-text("Skip")').first();
                if (await skipButton.count() > 0) {
                    console.log('Found Skip button, clicking it...');
                    await skipButton.click();
                    await page.waitForTimeout(1000);
                    break;
                }
                const nextButton = await page.locator('button:has-text("Next"):visible').first();
                const finishButton = await page.locator('button:has-text("Finish"):visible').first();
                const startPlayingButton = await page.locator('button:has-text("Start Playing"):visible').first();
                const allSetButton = await page.locator('text=All Set').first();
                if (await nextButton.count() > 0) {
                    console.log('Clicking Next button...');
                    await nextButton.click();
                    await page.waitForTimeout(500);
                }
                else if (await finishButton.count() > 0) {
                    console.log('Clicking Finish button...');
                    await finishButton.click();
                    await page.waitForTimeout(500);
                    break;
                }
                else if (await startPlayingButton.count() > 0) {
                    console.log('Clicking Start Playing button...');
                    await startPlayingButton.click();
                    await page.waitForTimeout(500);
                    break;
                }
                else if (await allSetButton.count() > 0) {
                    console.log('Found "All Set" text, looking for final button...');
                    const finalButton = await page.locator('button:visible').last();
                    if (await finalButton.count() > 0) {
                        await finalButton.click();
                        await page.waitForTimeout(500);
                    }
                    break;
                }
                else {
                    const nameInput = await page.locator('input[placeholder*="name"], input[placeholder*="Name"]').first();
                    if (await nameInput.count() > 0) {
                        console.log('Found name input, filling it...');
                        await nameInput.fill('TestPlayer');
                        const nextAfterName = await page.locator('button:has-text("Next"):visible').first();
                        if (await nextAfterName.count() > 0) {
                            await nextAfterName.click();
                            await page.waitForTimeout(500);
                        }
                    }
                    else {
                        console.log('No more onboarding buttons found');
                        break;
                    }
                }
                attempts++;
            }
            await page.waitForTimeout(1000);
            await page.evaluate(() => {
                localStorage.setItem('music-quiz-onboarding-complete', 'true');
                localStorage.setItem('tour-completed-landing-tour', 'true');
            });
            console.log('Onboarding handling completed');
        }
        catch (error) {
            console.error('Error handling onboarding:', error);
            try {
                await page.evaluate(() => {
                    localStorage.setItem('music-quiz-onboarding-complete', 'true');
                    localStorage.setItem('tour-completed-landing-tour', 'true');
                });
                await page.reload();
                await page.waitForTimeout(2000);
            }
            catch {
            }
        }
    }
    async monitorGame() {
        const categoryDistribution = new Map();
        const questionTypes = new Map();
        const playerPerformance = new Map();
        let roundNumber = 0;
        let gameEnded = false;
        for (let i = 0; i < this.pages.length; i++) {
            const playerId = i === 0 ? 'Host' : `Player${i}`;
            playerPerformance.set(playerId, {
                playerId,
                totalScore: 0,
                correctAnswers: 0,
                averageResponseTime: 0,
                fastestResponse: Infinity,
                slowestResponse: 0
            });
        }
        while (!gameEnded && roundNumber < this.config.roundsPerGame) {
            roundNumber++;
            const roundData = await this.monitorRound(roundNumber);
            if (!roundData) {
                gameEnded = true;
                break;
            }
            this.rounds.push(roundData);
            for (const question of roundData.questions) {
                if (question.category) {
                    categoryDistribution.set(question.category, (categoryDistribution.get(question.category) || 0) + 1);
                }
                const questionType = this.identifyQuestionType(question.questionText);
                questionTypes.set(questionType, (questionTypes.get(questionType) || 0) + 1);
                for (const [playerId, answer] of question.playerAnswers) {
                    const perf = playerPerformance.get(playerId);
                    if (answer.answer === question.correctAnswer) {
                        perf.correctAnswers++;
                    }
                    const responseTime = answer.time - question.timeStarted;
                    perf.fastestResponse = Math.min(perf.fastestResponse, responseTime);
                    perf.slowestResponse = Math.max(perf.slowestResponse, responseTime);
                }
            }
            gameEnded = await this.checkGameEnded();
        }
        const totalGameDuration = Date.now() - this.gameStartTime;
        const actualRounds = this.rounds.length;
        const totalSeconds = this.rounds.reduce((sum, round) => sum + (round.duration || 0), 0);
        const averageSecondsPerRound = totalSeconds / actualRounds / 1000;
        const expectedRoundDuration = this.config.secondsPerRound * 1000;
        const roundTimingVariances = this.rounds.map(round => Math.abs((round.duration || 0) - expectedRoundDuration));
        const roundTimingVariance = roundTimingVariances.reduce((a, b) => a + b, 0) / roundTimingVariances.length;
        const expectedTotalDuration = this.config.roundsPerGame * this.config.secondsPerRound * 1000;
        const totalTimingAccuracy = 1 - Math.abs(totalGameDuration - expectedTotalDuration) / expectedTotalDuration;
        for (const [playerId, perf] of playerPerformance) {
            const finalScore = await this.getPlayerScore(playerId);
            perf.totalScore = finalScore;
            let totalResponseTime = 0;
            let responseCount = 0;
            for (const round of this.rounds) {
                for (const question of round.questions) {
                    const answer = question.playerAnswers.get(playerId);
                    if (answer) {
                        totalResponseTime += (answer.time - question.timeStarted);
                        responseCount++;
                    }
                }
            }
            perf.averageResponseTime = responseCount > 0 ? totalResponseTime / responseCount : 0;
        }
        return {
            gameMode: this.config.mode,
            configuredRounds: this.config.roundsPerGame,
            actualRounds,
            configuredSecondsPerRound: this.config.secondsPerRound,
            averageSecondsPerRound,
            totalGameDuration,
            rounds: this.rounds,
            categoryDistribution,
            questionTypes,
            playerPerformance,
            timingAccuracy: {
                roundTimingVariance,
                totalTimingAccuracy
            }
        };
    }
    async monitorRound(roundNumber) {
        const hostPage = this.pages[0];
        try {
            await hostPage.waitForSelector(`[data-testid="round-${roundNumber}"]`, { timeout: 10000 });
        }
        catch {
            return null;
        }
        const roundData = {
            roundNumber,
            startTime: Date.now(),
            questions: [],
            scores: new Map()
        };
        let questionNumber = 0;
        let roundEnded = false;
        while (!roundEnded) {
            questionNumber++;
            try {
                await hostPage.waitForSelector('[data-testid="question-text"]', { timeout: 5000 });
                const questionData = await this.captureQuestionData(questionNumber);
                if (questionData) {
                    roundData.questions.push(questionData);
                    await this.waitForAnswers(questionData);
                }
                roundEnded = await this.checkRoundEnded(roundNumber);
            }
            catch {
                roundEnded = true;
            }
        }
        roundData.endTime = Date.now();
        roundData.duration = roundData.endTime - roundData.startTime;
        for (let i = 0; i < this.pages.length; i++) {
            const playerId = i === 0 ? 'Host' : `Player${i}`;
            const score = await this.getPlayerScore(playerId);
            roundData.scores.set(playerId, score);
        }
        return roundData;
    }
    async captureQuestionData(questionNumber) {
        const hostPage = this.pages[0];
        try {
            const questionText = await hostPage.textContent('[data-testid="question-text"]');
            if (!questionText)
                return null;
            const category = await hostPage.textContent('[data-testid="question-category"]').catch(() => null);
            const options = await hostPage.$$eval('[data-testid="answer-option"]', elements => elements.map(el => el.textContent?.trim() || ''));
            const questionData = {
                questionNumber,
                questionText,
                category: category || undefined,
                options,
                timeStarted: Date.now(),
                playerAnswers: new Map()
            };
            await this.monitorPlayerAnswers(questionData);
            return questionData;
        }
        catch (error) {
            console.error('Error capturing question data:', error);
            return null;
        }
    }
    async monitorPlayerAnswers(questionData) {
        const answerPromises = this.pages.map(async (page, index) => {
            const playerId = index === 0 ? 'Host' : `Player${index}`;
            try {
                await page.waitForSelector('[data-testid="answer-option"].selected', {
                    timeout: this.config.secondsPerRound * 1000
                });
                const selectedAnswer = await page.textContent('[data-testid="answer-option"].selected');
                const answerTime = Date.now();
                questionData.playerAnswers.set(playerId, {
                    answer: selectedAnswer || '',
                    time: answerTime
                });
            }
            catch {
                questionData.playerAnswers.set(playerId, {
                    answer: '',
                    time: questionData.timeStarted + (this.config.secondsPerRound * 1000)
                });
            }
        });
        await Promise.all(answerPromises);
        try {
            const correctAnswer = await this.pages[0].textContent('[data-testid="correct-answer"]');
            questionData.correctAnswer = correctAnswer || undefined;
        }
        catch {
        }
    }
    async waitForAnswers(questionData) {
        const maxWaitTime = this.config.secondsPerRound * 1000;
        const startTime = Date.now();
        while (Date.now() - startTime < maxWaitTime) {
            if (questionData.playerAnswers.size === this.pages.length) {
                break;
            }
            const timerExpired = await this.pages[0].$('[data-testid="timer-expired"]');
            if (timerExpired) {
                break;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        questionData.timeAnswered = Date.now();
        questionData.responseTime = questionData.timeAnswered - questionData.timeStarted;
    }
    async checkRoundEnded(roundNumber) {
        try {
            const roundEndIndicator = await this.pages[0].$('[data-testid="round-end"]');
            const nextRoundIndicator = await this.pages[0].$(`[data-testid="round-${roundNumber + 1}"]`);
            const gameEndIndicator = await this.pages[0].$('[data-testid="game-end"]');
            return !!(roundEndIndicator || nextRoundIndicator || gameEndIndicator);
        }
        catch {
            return true;
        }
    }
    async checkGameEnded() {
        try {
            const gameEndIndicator = await this.pages[0].$('[data-testid="game-end"]');
            const finalScoresIndicator = await this.pages[0].$('[data-testid="final-scores"]');
            return !!(gameEndIndicator || finalScoresIndicator);
        }
        catch {
            return false;
        }
    }
    async getPlayerScore(playerId) {
        const playerIndex = playerId === 'Host' ? 0 : parseInt(playerId.replace('Player', ''));
        const page = this.pages[playerIndex];
        try {
            const scoreText = await page.textContent('[data-testid="player-score"]');
            return parseInt(scoreText || '0') || 0;
        }
        catch {
            return 0;
        }
    }
    identifyQuestionType(questionText) {
        const text = questionText.toLowerCase();
        if (text.includes('who') || text.includes('artist') || text.includes('band')) {
            return 'artist';
        }
        else if (text.includes('year') || text.includes('when')) {
            return 'year';
        }
        else if (text.includes('album')) {
            return 'album';
        }
        else if (text.includes('genre') || text.includes('style')) {
            return 'genre';
        }
        else if (text.includes('lyrics') || text.includes('words')) {
            return 'lyrics';
        }
        else if (text.includes('chart') || text.includes('position')) {
            return 'chart';
        }
        else {
            return 'general';
        }
    }
    validateGameBehavior(analysis) {
        const results = [];
        results.push({
            check: 'Number of rounds',
            expected: this.config.roundsPerGame,
            actual: analysis.actualRounds,
            passed: analysis.actualRounds === this.config.roundsPerGame,
            message: analysis.actualRounds !== this.config.roundsPerGame
                ? `Game ended with ${analysis.actualRounds} rounds instead of ${this.config.roundsPerGame}`
                : undefined
        });
        const timingTolerance = 2;
        const avgRoundTime = analysis.averageSecondsPerRound;
        const expectedTime = this.config.secondsPerRound;
        const timingPassed = Math.abs(avgRoundTime - expectedTime) <= timingTolerance;
        results.push({
            check: 'Average seconds per round',
            expected: expectedTime,
            actual: avgRoundTime,
            passed: timingPassed,
            message: !timingPassed
                ? `Average round time ${avgRoundTime.toFixed(1)}s differs from configured ${expectedTime}s`
                : undefined
        });
        if (this.config.categories && this.config.categories.length > 0) {
            const actualCategories = Array.from(analysis.categoryDistribution.keys());
            const unexpectedCategories = actualCategories.filter(cat => !this.config.categories.includes(cat));
            results.push({
                check: 'Category selection',
                expected: this.config.categories,
                actual: actualCategories,
                passed: unexpectedCategories.length === 0,
                message: unexpectedCategories.length > 0
                    ? `Found unexpected categories: ${unexpectedCategories.join(', ')}`
                    : undefined
            });
        }
        results.push(...this.validateGameModeRules(analysis));
        results.push({
            check: 'Overall timing accuracy',
            expected: 0.9,
            actual: analysis.timingAccuracy.totalTimingAccuracy,
            passed: analysis.timingAccuracy.totalTimingAccuracy >= 0.9,
            message: analysis.timingAccuracy.totalTimingAccuracy < 0.9
                ? `Timing accuracy ${(analysis.timingAccuracy.totalTimingAccuracy * 100).toFixed(1)}% is below 90%`
                : undefined
        });
        return results;
    }
    validateGameModeRules(analysis) {
        const results = [];
        switch (this.config.mode) {
            case 'quick-fire':
                const avgResponseTime = Array.from(analysis.playerPerformance.values())
                    .reduce((sum, perf) => sum + perf.averageResponseTime, 0) / analysis.playerPerformance.size;
                results.push({
                    check: 'Quick-fire response time',
                    expected: '< 5000ms',
                    actual: `${avgResponseTime.toFixed(0)}ms`,
                    passed: avgResponseTime < 5000,
                    message: avgResponseTime >= 5000
                        ? 'Quick-fire mode should have faster response times'
                        : undefined
                });
                break;
            case 'album-art':
                const nonAlbumQuestions = Array.from(analysis.questionTypes.entries())
                    .filter(([type]) => type !== 'album')
                    .reduce((sum, [, count]) => sum + count, 0);
                results.push({
                    check: 'Album art mode questions',
                    expected: 'All album questions',
                    actual: `${nonAlbumQuestions} non-album questions`,
                    passed: nonAlbumQuestions === 0,
                    message: nonAlbumQuestions > 0
                        ? 'Album art mode should only contain album-related questions'
                        : undefined
                });
                break;
            case 'decade-challenge':
                const yearQuestions = analysis.questionTypes.get('year') || 0;
                const totalQuestions = Array.from(analysis.questionTypes.values())
                    .reduce((sum, count) => sum + count, 0);
                const yearQuestionRatio = yearQuestions / totalQuestions;
                results.push({
                    check: 'Decade challenge year questions',
                    expected: '> 50%',
                    actual: `${(yearQuestionRatio * 100).toFixed(1)}%`,
                    passed: yearQuestionRatio > 0.5,
                    message: yearQuestionRatio <= 0.5
                        ? 'Decade challenge should have mostly year-related questions'
                        : undefined
                });
                break;
        }
        return results;
    }
}
exports.GameFlowAnalyzer = GameFlowAnalyzer;
async function analyzeGameMode(config, playerCount = 2) {
    const analyzer = new GameFlowAnalyzer(config);
    try {
        await analyzer.initialize();
        const analysis = await analyzer.analyzeGameFlow(playerCount);
        console.log('\n=== Game Flow Analysis Summary ===');
        console.log(`Game Mode: ${analysis.gameMode}`);
        console.log(`Rounds: ${analysis.actualRounds}/${analysis.configuredRounds}`);
        console.log(`Avg Round Time: ${analysis.averageSecondsPerRound.toFixed(1)}s (configured: ${analysis.configuredSecondsPerRound}s)`);
        console.log(`Total Duration: ${(analysis.totalGameDuration / 1000).toFixed(1)}s`);
        console.log(`\nValidation Results:`);
        for (const result of analysis.validationResults) {
            const status = result.passed ? '✓' : '✗';
            console.log(`${status} ${result.check}: ${result.actual} (expected: ${result.expected})`);
            if (result.message) {
                console.log(`  → ${result.message}`);
            }
        }
        return analysis;
    }
    finally {
        await analyzer.close();
    }
}
//# sourceMappingURL=game-flow-analyzer.js.map