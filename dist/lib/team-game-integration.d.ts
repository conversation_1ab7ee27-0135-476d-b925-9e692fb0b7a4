import { Team, TeamGameSettings, TeamAnswer, TeamChatMessage } from './types';
import { ScoreResult } from './team-scoring';
import { TeamGameState } from './team-game-modes';
export interface TeamGameSession {
    gameId: string;
    teams: Team[];
    settings: TeamGameSettings;
    gameState: TeamGameState;
    currentQuestion: number;
    totalQuestions: number;
    questionStartTime: number;
    collaborationStartTime?: number;
    teamAnswers: Map<string, TeamAnswer>;
    teamChatMessages: TeamChatMessage[];
    scoreHistory: ScoreResult[][];
    achievements: Map<string, string[]>;
}
export declare class TeamGameIntegration {
    private activeGames;
    private questionTimers;
    private collaborationTimers;
    initializeTeamGame(gameId: string, teams: Team[], settings: TeamGameSettings, totalQuestions: number): TeamGameSession;
    startQuestion(gameId: string, questionNumber: number, questionData: any, timeLimit: number): {
        success: boolean;
        collaborationTime?: number;
        activeTeams: string[];
        currentTurn?: string;
    };
    submitTeamAnswer(gameId: string, teamAnswer: TeamAnswer): {
        success: boolean;
        reason: string;
        nextTurn?: string;
        allTeamsAnswered: boolean;
    };
    processQuestionResults(gameId: string, correctAnswer: number, questionDifficulty: number, timeLimit: number): {
        scoreResults: ScoreResult[];
        updatedTeams: Team[];
        achievements: string[];
    };
    handleTeamChat(gameId: string, message: TeamChatMessage): boolean;
    getTeamGameSession(gameId: string): TeamGameSession | undefined;
    getTeamLeaderboard(gameId: string): Team[];
    getTeamChatMessages(gameId: string, teamId?: string): TeamChatMessage[];
    isCollaborationTimeActive(gameId: string): {
        active: boolean;
        timeRemaining: number;
    };
    finalizeTeamGame(gameId: string): {
        finalTeams: Team[];
        finalScores: ScoreResult[];
        gameStats: any;
        achievements: Map<string, string[]>;
    };
    cleanupGame(gameId: string): void;
    private startQuestionTimer;
    private startCollaborationTimer;
    private clearQuestionTimers;
    private handleQuestionTimeout;
    private handleCollaborationTimeout;
    private checkAllTeamsAnswered;
    private checkTeamAchievements;
    private calculateGameStats;
}
export declare const teamGameIntegration: TeamGameIntegration;
