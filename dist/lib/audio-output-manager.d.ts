import { MPDClient } from './mpd-client';
export type OutputMode = 'host' | 'stream' | 'both';
export interface AudioOutput {
    id: string;
    name: string;
    type: 'pulse' | 'alsa' | 'httpd' | 'null';
    enabled: boolean;
    config?: Record<string, any>;
}
export interface OutputConfig {
    mode: OutputMode;
    selectedOutputs: string[];
    streamUrl?: string;
    hostVolume: number;
    streamVolume: number;
}
export declare class AudioOutputManager {
    private mpdClient;
    private config;
    private availableOutputs;
    private streamElement;
    constructor(mpdClient: MPDClient, initialConfig?: Partial<OutputConfig>);
    initialize(): Promise<void>;
    private discoverOutputs;
    private parseOutputs;
    private configureOutputs;
    private enableHostOutput;
    private disableHostOutput;
    private enableStreamOutput;
    private disableStreamOutput;
    private setupClientStream;
    setOutputMode(mode: OutputMode): Promise<void>;
    setVolume(type: 'host' | 'stream', volume: number): Promise<void>;
    getAvailableOutputs(): AudioOutput[];
    getConfig(): OutputConfig;
    isStreamingAvailable(): boolean;
    startStream(): Promise<void>;
    stopStream(): void;
    getStreamStatus(): {
        connected: boolean;
        playing: boolean;
        url?: string;
    };
}
