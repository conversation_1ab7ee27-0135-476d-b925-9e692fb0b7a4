{"version": 3, "file": "performance-utils.js", "sourceRoot": "", "sources": ["../../lib/performance-utils.ts"], "names": [], "mappings": ";;;AAyDA,kCAcC;AAGD,kCAeC;AASD,sDAoBC;AAGD,kCAkBC;AAGD,0BAqBC;AAjKD,iCAAgE;AAGhE,MAAa,aAAa;IAA1B;QAEU,qBAAgB,GAAG;YACzB,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;YACzB,QAAQ,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;SAC5B,CAAA;IA4CH,CAAC;IA1CC,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC5B,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAA;QAC9C,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAA;IAC/B,CAAC;IAED,mBAAmB;QACjB,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAI,WAAmB,CAAC,MAAM,CAAA;YAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,cAAc,CAAA;YAElC,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ;gBAAE,OAAO,UAAU,CAAA;YAC5D,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO;gBAAE,OAAO,SAAS,CAAA;QAC5D,CAAC;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,cAAc;QACZ,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAI,WAAmB,CAAC,MAAM,CAAA;YAC1C,OAAO;gBACL,IAAI,EAAE,MAAM,CAAC,cAAc;gBAC3B,KAAK,EAAE,MAAM,CAAC,eAAe;gBAC7B,KAAK,EAAE,MAAM,CAAC,eAAe;gBAC7B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC;gBACvD,QAAQ,EAAE,IAAI,CAAC,mBAAmB,EAAE;aACrC,CAAA;QACH,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,qBAAqB,CAAC,QAA2C;QAC/D,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,UAAU;gBACb,OAAO,EAAE,mBAAmB,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAA;YACnE,KAAK,SAAS;gBACZ,OAAO,EAAE,mBAAmB,EAAE,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAA;YACrE;gBACE,OAAO,EAAE,mBAAmB,EAAE,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAA;QACvE,CAAC;IACH,CAAC;CACF;AAjDD,sCAiDC;AAGD,SAAgB,WAAW,CAAI,KAAQ,EAAE,KAAa;IACpD,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,IAAA,gBAAQ,EAAI,KAAK,CAAC,CAAA;IAE9D,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;YAC9B,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC1B,CAAC,EAAE,KAAK,CAAC,CAAA;QAET,OAAO,GAAG,EAAE;YACV,YAAY,CAAC,OAAO,CAAC,CAAA;QACvB,CAAC,CAAA;IACH,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;IAElB,OAAO,cAAc,CAAA;AACvB,CAAC;AAGD,SAAgB,WAAW,CACzB,QAAW,EACX,KAAa;IAEb,MAAM,OAAO,GAAG,IAAA,cAAM,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;IAElC,OAAO,IAAA,mBAAW,EAChB,CAAC,CAAC,GAAG,IAAI,EAAE,EAAE;QACX,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,OAAO,IAAI,KAAK,EAAE,CAAC;YAC1C,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAA;YACjB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC9B,CAAC;IACH,CAAC,CAAM,EACP,CAAC,QAAQ,EAAE,KAAK,CAAC,CAClB,CAAA;AACH,CAAC;AASD,SAAgB,qBAAqB,CACnC,SAAiB,EACjB,SAAiB,EACjB,OAA6B;IAE7B,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,OAAO,CAAA;IAE7D,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,QAAQ,CAAC,CAAA;IAC7E,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CACvB,SAAS,GAAG,CAAC,EACb,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,UAAU,CAAC,GAAG,QAAQ,CACjE,CAAA;IAED,OAAO;QACL,UAAU;QACV,QAAQ;QACR,OAAO,EAAE,UAAU,GAAG,UAAU;QAChC,WAAW,EAAE,SAAS,GAAG,UAAU;QACnC,YAAY,EAAE,QAAQ,GAAG,UAAU,GAAG,CAAC;KACxC,CAAA;AACH,CAAC;AAGD,SAAgB,WAAW,CACzB,KAAY,EACZ,UAAkB,EAClB,YAAsB;IAEtB,IAAI,CAAC,UAAU;QAAE,OAAO,KAAK,CAAA;IAE7B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,EAAE,CAAA;IAC5C,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAE1E,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QACzB,MAAM,UAAU,GAAG,YAAY;aAC5B,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;aACvC,IAAI,CAAC,GAAG,CAAC;aACT,WAAW,EAAE,CAAA;QAEhB,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC,CAAC,CAAA;AACJ,CAAC;AAGD,SAAgB,OAAO,CAAoC,EAAK;IAC9D,MAAM,KAAK,GAAG,IAAI,GAAG,EAAE,CAAA;IAEvB,OAAO,CAAC,CAAC,GAAG,IAAmB,EAAE,EAAE;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QAEhC,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACvB,CAAC;QAED,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAA;QAC1B,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;QAGtB,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAA;YAC1C,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;QACxB,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC,CAAM,CAAA;AACT,CAAC;AAGD,MAAa,YAAY;IAAzB;QACU,UAAK,GAAG,IAAI,GAAG,EAAuD,CAAA;IAoDhF,CAAC;IAlDC,GAAG,CAAC,GAAW,EAAE,IAAO,EAAE,MAAc,MAAM;QAC5C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;YAClB,IAAI;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,GAAG;SACJ,CAAC,CAAA;QAGF,IAAI,CAAC,OAAO,EAAE,CAAA;IAChB,CAAC;IAED,GAAG,CAAC,GAAW;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAEjC,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAA;QAEvB,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YACtB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAA;IACnB,CAAC;IAED,GAAG,CAAC,GAAW;QACb,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,CAAA;IAC/B,CAAC;IAED,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;IACxB,CAAC;IAED,KAAK;QACH,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;IACpB,CAAC;IAEO,OAAO;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEtB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAChD,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;gBACtC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI;QACF,IAAI,CAAC,OAAO,EAAE,CAAA;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;IACxB,CAAC;CACF;AArDD,oCAqDC"}