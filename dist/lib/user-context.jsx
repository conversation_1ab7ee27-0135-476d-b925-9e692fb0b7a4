"use strict";
"use client";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserProvider = UserProvider;
exports.useUser = useUser;
const react_1 = __importStar(require("react"));
const sonner_1 = require("sonner");
const api_client_1 = require("@/lib/api-client");
const auth_cookies_client_1 = require("@/lib/auth-cookies-client");
const UserContext = (0, react_1.createContext)(undefined);
const SAMPLE_ACHIEVEMENTS = [
    {
        id: 'first-steps',
        name: 'First Steps',
        description: 'Complete your first quiz',
        icon: '🎵',
        rarity: 'Common',
        earned: true,
        earnedAt: new Date('2024-06-20')
    },
    {
        id: 'music-novice',
        name: 'Music Novice',
        description: 'Score 100+ points in a quiz',
        icon: '🎶',
        rarity: 'Common',
        earned: true,
        earnedAt: new Date('2024-06-21')
    },
    {
        id: 'rock-expert',
        name: 'Rock Expert',
        description: 'Get 90%+ accuracy in Rock genre',
        icon: '🎸',
        rarity: 'Rare',
        earned: true,
        earnedAt: new Date('2024-06-25')
    },
    {
        id: 'speed-demon',
        name: 'Speed Demon',
        description: 'Answer 10 questions in under 5 seconds each',
        icon: '⚡',
        rarity: 'Epic',
        earned: true,
        earnedAt: new Date('2024-06-24')
    },
    {
        id: 'pop-master',
        name: 'Pop Master',
        description: 'Complete 10 Pop quizzes',
        icon: '🎤',
        rarity: 'Rare',
        earned: false,
        progress: 7,
        maxProgress: 10
    },
    {
        id: 'decade-detective',
        name: 'Decade Detective',
        description: 'Perfect score in Decade Challenge',
        icon: '📅',
        rarity: 'Epic',
        earned: false
    },
    {
        id: 'streak-master',
        name: 'Streak Master',
        description: 'Achieve a 15+ answer streak',
        icon: '🔥',
        rarity: 'Legendary',
        earned: false,
        progress: 12,
        maxProgress: 15
    },
    {
        id: 'genre-guru',
        name: 'Genre Guru',
        description: 'Master 5 different genres',
        icon: '🎭',
        rarity: 'Epic',
        earned: false,
        progress: 3,
        maxProgress: 5
    }
];
function UserProvider({ children }) {
    const [user, setUser] = (0, react_1.useState)(null);
    const [isLoading, setIsLoading] = (0, react_1.useState)(true);
    const [isFirstTime, setIsFirstTime] = (0, react_1.useState)(false);
    (0, react_1.useEffect)(() => {
        const initializeUser = async () => {
            try {
                const userInfo = (0, auth_cookies_client_1.getClientUserInfo)();
                const hasCompletedOnboarding = localStorage.getItem('music-quiz-onboarding-complete');
                if (userInfo) {
                    try {
                        const data = await api_client_1.api.post('/api/auth/validate');
                        if (data.success && data.user) {
                            const user = {
                                id: data.user.id,
                                role: data.user.role,
                                username: data.user.username,
                                displayName: data.user.displayName || data.user.username,
                                email: data.user.email,
                                bio: '',
                                avatar: 'rockstar',
                                favoriteGenres: ['rock', 'pop'],
                                preferences: {
                                    theme: 'system',
                                    difficulty: 3,
                                    volume: 70,
                                    autoPlay: true,
                                    showHints: true,
                                    soundEffects: true
                                },
                                location: '',
                                isFirstTime: false,
                                level: 1,
                                xp: 0,
                                xpToNext: 100,
                                joinDate: new Date(data.user.createdAt).toLocaleDateString(),
                                stats: {
                                    totalGames: 0,
                                    winRate: 0,
                                    averageScore: 0,
                                    bestStreak: 0,
                                    hoursPlayed: 0,
                                    achievements: 0,
                                    rank: 0,
                                    favoriteMode: 'Classic Quiz'
                                },
                                achievements: SAMPLE_ACHIEVEMENTS.map(a => ({ ...a, earned: false, earnedAt: undefined })),
                                playlists: [],
                                settings: {
                                    emailNotifications: true,
                                    soundEffects: true,
                                    autoPlay: true,
                                    showHints: true,
                                    publicProfile: true,
                                    shareStats: false
                                }
                            };
                            setUser(user);
                            setIsFirstTime(false);
                        }
                        else {
                            if (!hasCompletedOnboarding) {
                                setIsFirstTime(true);
                            }
                        }
                    }
                    catch (sessionError) {
                        console.error('Session validation failed:', sessionError);
                        const fallbackUser = {
                            id: userInfo.id,
                            role: userInfo.role,
                            username: userInfo.username,
                            displayName: userInfo.username,
                            email: '',
                            bio: '',
                            avatar: 'rockstar',
                            favoriteGenres: ['rock', 'pop'],
                            preferences: {
                                theme: 'system',
                                difficulty: 3,
                                volume: 70,
                                autoPlay: true,
                                showHints: true,
                                soundEffects: true
                            },
                            location: '',
                            isFirstTime: false,
                            level: 1,
                            xp: 0,
                            xpToNext: 100,
                            joinDate: new Date().toLocaleDateString(),
                            stats: {
                                totalGames: 0,
                                winRate: 0,
                                averageScore: 0,
                                bestStreak: 0,
                                hoursPlayed: 0,
                                achievements: 0,
                                rank: 0,
                                favoriteMode: 'Classic Quiz'
                            },
                            achievements: SAMPLE_ACHIEVEMENTS.map(a => ({ ...a, earned: false, earnedAt: undefined })),
                            playlists: [],
                            settings: {
                                emailNotifications: true,
                                soundEffects: true,
                                autoPlay: true,
                                showHints: true,
                                publicProfile: true,
                                shareStats: false
                            }
                        };
                        setUser(fallbackUser);
                        setIsFirstTime(false);
                        sonner_1.toast.warning('Session validation warning', {
                            description: 'Unable to verify session with server, but you remain logged in',
                            duration: 4000
                        });
                    }
                }
                else if (!hasCompletedOnboarding) {
                    setIsFirstTime(true);
                }
            }
            catch (error) {
                console.error('Error initializing user:', error);
                setIsFirstTime(true);
                sonner_1.toast.error('User initialization failed', {
                    description: 'Unable to load user profile. Please refresh the page.',
                    duration: 5000
                });
            }
            finally {
                setIsLoading(false);
            }
        };
        initializeUser();
    }, []);
    const login = async (email, password) => {
        console.log('[UserContext] Login attempt for:', email);
        try {
            setIsLoading(true);
            const data = await api_client_1.api.post('/api/auth/login', { email, password });
            if (data.success && data.user) {
                const user = {
                    id: data.user.id,
                    role: data.user.role,
                    username: data.user.username,
                    displayName: data.user.displayName || data.user.username,
                    email: data.user.email,
                    bio: 'Music lover',
                    avatar: 'rockstar',
                    favoriteGenres: ['rock', 'pop'],
                    preferences: {
                        theme: 'system',
                        difficulty: 3,
                        volume: 70,
                        autoPlay: true,
                        showHints: true,
                        soundEffects: true
                    },
                    location: '',
                    isFirstTime: false,
                    level: 1,
                    xp: 0,
                    xpToNext: 100,
                    joinDate: new Date(data.user.createdAt).toLocaleDateString(),
                    stats: {
                        totalGames: 0,
                        winRate: 0,
                        averageScore: 0,
                        bestStreak: 0,
                        hoursPlayed: 0,
                        achievements: 0,
                        rank: 0,
                        favoriteMode: 'Classic Quiz'
                    },
                    achievements: SAMPLE_ACHIEVEMENTS.map(a => ({ ...a, earned: false, earnedAt: undefined })),
                    playlists: [],
                    settings: {
                        emailNotifications: true,
                        soundEffects: true,
                        autoPlay: true,
                        showHints: true,
                        publicProfile: true,
                        shareStats: false
                    }
                };
                setUser(user);
                setIsFirstTime(false);
                await new Promise(resolve => setTimeout(resolve, 100));
                const userInfo = (0, auth_cookies_client_1.getClientUserInfo)();
                if (!userInfo) {
                    console.error('[UserContext] Cookie not found after login');
                    sonner_1.toast.error('Login failed - please try again');
                    return false;
                }
                console.log('[UserContext] Login successful, user state updated, cookie verified');
                sonner_1.toast.success('Welcome back!');
                return true;
            }
            else {
                sonner_1.toast.error(data.message || 'Invalid credentials');
                return false;
            }
        }
        catch (error) {
            console.error('Login error:', error);
            sonner_1.toast.error('Login failed', {
                description: error instanceof Error ? error.message : 'Please check your credentials and try again',
                duration: 5000
            });
            return false;
        }
        finally {
            setIsLoading(false);
        }
    };
    const register = async (userData, password) => {
        try {
            setIsLoading(true);
            const uniqueId = `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            const email = userData.email || (userData.username
                ? `${userData.username}_${uniqueId}@local.network`
                : `user_${uniqueId}@local.network`);
            const data = await api_client_1.api.post('/api/auth/register', {
                email: email,
                password: password || 'guest123',
                displayName: userData.displayName || userData.username || 'User'
            });
            if (data.success && data.user) {
                const newUser = {
                    id: data.user.id,
                    role: data.user.role,
                    username: data.user.username,
                    displayName: data.user.displayName || data.user.username,
                    email: data.user.email,
                    bio: userData.bio || '',
                    avatar: userData.avatar || 'rockstar',
                    favoriteGenres: userData.favoriteGenres || [],
                    preferences: {
                        theme: 'system',
                        difficulty: 3,
                        volume: 70,
                        autoPlay: true,
                        showHints: true,
                        soundEffects: true
                    },
                    location: userData.location,
                    isFirstTime: true,
                    level: 1,
                    xp: 0,
                    xpToNext: 100,
                    joinDate: new Date().toLocaleDateString(),
                    stats: {
                        totalGames: 0,
                        winRate: 0,
                        averageScore: 0,
                        bestStreak: 0,
                        hoursPlayed: 0,
                        achievements: 0,
                        rank: 0,
                        favoriteMode: 'Classic Quiz'
                    },
                    achievements: SAMPLE_ACHIEVEMENTS.map(a => ({ ...a, earned: false, earnedAt: undefined })),
                    playlists: [{
                            id: 'favorites',
                            name: 'Quiz Favorites',
                            description: 'Songs I discovered and loved during quizzes',
                            tracks: [],
                            isPublic: false,
                            createdAt: new Date(),
                            updatedAt: new Date(),
                            createdBy: data.user.id,
                            totalDuration: 0,
                            genre: 'Mixed',
                            mood: 'Favorites'
                        }],
                    settings: {
                        emailNotifications: true,
                        soundEffects: true,
                        autoPlay: userData.preferences?.autoPlay ?? true,
                        showHints: userData.preferences?.showHints ?? true,
                        publicProfile: true,
                        shareStats: false
                    }
                };
                setUser(newUser);
                setIsFirstTime(false);
                sonner_1.toast.success('Account created successfully!');
                return { success: true, email: email };
            }
            else {
                sonner_1.toast.error(data.message || 'Registration failed');
                return { success: false };
            }
        }
        catch (error) {
            console.error('Registration error:', error);
            sonner_1.toast.error('Registration failed', {
                description: error instanceof Error ? error.message : 'Please try again with different details',
                duration: 5000
            });
            return { success: false };
        }
        finally {
            setIsLoading(false);
        }
    };
    const logout = async () => {
        try {
            await api_client_1.api.post('/api/auth/logout');
            setUser(null);
            localStorage.removeItem('music-quiz-onboarding-complete');
            sonner_1.toast.success('Logged out successfully');
            window.location.href = '/';
        }
        catch (error) {
            console.error('Logout error:', error);
            setUser(null);
            sonner_1.toast.warning('Logout completed', {
                description: 'Session ended locally (server logout may have failed)',
                duration: 3000
            });
        }
    };
    const updateProfile = async (updates) => {
        if (!user)
            return false;
        try {
            const data = await api_client_1.api.put('/api/auth/profile', {
                username: updates.username || user.username,
                displayName: updates.displayName || user.displayName,
                avatarUrl: updates.avatar || user.avatar,
                favoriteGenres: updates.favoriteGenres || user.favoriteGenres,
                location: updates.location || user.location
            });
            if (data.success && data.user) {
                const updatedUser = {
                    ...user,
                    username: data.user.username,
                    displayName: data.user.displayName,
                    avatar: data.user.avatarUrl,
                    favoriteGenres: data.user.favoriteGenres || [],
                    location: data.user.location || ''
                };
                setUser(updatedUser);
                sonner_1.toast.success('Profile updated successfully!');
                return true;
            }
            else {
                sonner_1.toast.error(data.error || 'Failed to update profile');
                return false;
            }
        }
        catch (error) {
            console.error('Profile update error:', error);
            sonner_1.toast.error('Profile update failed', {
                description: error instanceof Error ? error.message : 'Unable to save profile changes',
                duration: 5000
            });
            return false;
        }
    };
    const completeOnboarding = async (profile) => {
        try {
            setIsLoading(true);
            const result = await register(profile);
            if (result.success) {
                localStorage.setItem('music-quiz-onboarding-complete', 'true');
                setIsFirstTime(false);
                sonner_1.toast.success(`Welcome to Music Quiz, ${profile.displayName}!`);
            }
            return result.success;
        }
        catch (error) {
            console.error('Onboarding completion error:', error);
            sonner_1.toast.error('Onboarding failed', {
                description: error instanceof Error ? error.message : 'Unable to complete account setup',
                duration: 5000
            });
            return false;
        }
        finally {
            setIsLoading(false);
        }
    };
    const skipOnboarding = async () => {
        try {
            const timestamp = Date.now();
            const randomNum = Math.floor(Math.random() * 10000);
            const guestProfile = {
                username: `guest_${randomNum}`,
                displayName: `Guest ${randomNum}`,
                bio: '',
                avatar: 'rockstar',
                favoriteGenres: ['pop', 'rock'],
                preferences: {
                    theme: 'system',
                    difficulty: 3,
                    volume: 70,
                    autoPlay: true,
                    showHints: true,
                    soundEffects: true
                },
                location: '',
                isFirstTime: false
            };
            const result = await register(guestProfile, 'guest123');
            if (result.success) {
                localStorage.setItem('music-quiz-onboarding-complete', 'true');
                setIsFirstTime(false);
                sonner_1.toast.success('Guest account created! You can now vote and participate in all features.');
            }
            else {
                localStorage.setItem('music-quiz-onboarding-complete', 'true');
                setIsFirstTime(false);
                sonner_1.toast.info('You can set up your profile later in settings');
            }
        }
        catch (error) {
            console.error('Failed to create guest account:', error);
            localStorage.setItem('music-quiz-onboarding-complete', 'true');
            setIsFirstTime(false);
            sonner_1.toast.warning('Guest account creation failed', {
                description: 'Continuing in offline mode - you can set up your profile later in settings',
                duration: 6000
            });
        }
    };
    const resetOnboarding = () => {
        localStorage.removeItem('music-quiz-onboarding-complete');
        setUser(null);
        setIsFirstTime(true);
        sonner_1.toast.info('Onboarding reset - refresh page to see onboarding flow');
    };
    const addToPlaylist = async (playlistId, track) => {
        if (!user)
            return false;
        try {
            const updatedUser = {
                ...user,
                playlists: user.playlists.map(playlist => {
                    if (playlist.id === playlistId) {
                        const trackExists = playlist.tracks.some(t => t.id === track.id);
                        if (trackExists) {
                            sonner_1.toast.error('Track already in playlist');
                            return playlist;
                        }
                        return {
                            ...playlist,
                            tracks: [...playlist.tracks, { ...track, addedAt: new Date() }],
                            totalDuration: playlist.totalDuration + track.duration,
                            updatedAt: new Date()
                        };
                    }
                    return playlist;
                })
            };
            setUser(updatedUser);
            const playlist = user.playlists.find(p => p.id === playlistId);
            sonner_1.toast.success(`Added "${track.title}" to "${playlist?.name}"`);
            return true;
        }
        catch (error) {
            console.error('Add to playlist error:', error);
            sonner_1.toast.error('Failed to add track to playlist', {
                description: error instanceof Error ? error.message : 'Unable to save track to playlist',
                duration: 4000
            });
            return false;
        }
    };
    const createPlaylist = async (playlistData) => {
        if (!user)
            return false;
        try {
            const newPlaylist = {
                ...playlistData,
                id: Date.now().toString(),
                createdAt: new Date(),
                updatedAt: new Date(),
                createdBy: user.id
            };
            const updatedUser = {
                ...user,
                playlists: [newPlaylist, ...user.playlists]
            };
            setUser(updatedUser);
            sonner_1.toast.success(`Playlist "${newPlaylist.name}" created!`);
            return true;
        }
        catch (error) {
            console.error('Create playlist error:', error);
            sonner_1.toast.error('Failed to create playlist', {
                description: error instanceof Error ? error.message : 'Unable to create new playlist',
                duration: 4000
            });
            return false;
        }
    };
    const updateXP = (points) => {
        if (!user)
            return;
        const newXP = user.xp + points;
        let newLevel = user.level;
        let newXPToNext = user.xpToNext;
        const xpForNextLevel = 100 + (user.level - 1) * 50;
        if (newXP >= user.xp + user.xpToNext) {
            newLevel++;
            newXPToNext = xpForNextLevel - (newXP - user.xp - user.xpToNext);
            sonner_1.toast.success(`Level up! You're now level ${newLevel}!`, {
                duration: 5000,
            });
        }
        else {
            newXPToNext = user.xpToNext - points;
        }
        const updatedUser = {
            ...user,
            xp: newXP,
            level: newLevel,
            xpToNext: newXPToNext
        };
        setUser(updatedUser);
        if (points > 0) {
            sonner_1.toast.success(`+${points} XP earned!`);
        }
    };
    const unlockAchievement = (achievementId) => {
        if (!user)
            return;
        const achievement = user.achievements.find(a => a.id === achievementId);
        if (!achievement || achievement.earned)
            return;
        const updatedUser = {
            ...user,
            achievements: user.achievements.map(a => a.id === achievementId
                ? { ...a, earned: true, earnedAt: new Date() }
                : a),
            stats: {
                ...user.stats,
                achievements: user.stats.achievements + 1
            }
        };
        setUser(updatedUser);
        sonner_1.toast.success(`Achievement unlocked: ${achievement.name}!`, {
            description: achievement.description,
            duration: 5000,
        });
        const bonusXP = {
            'Common': 50,
            'Rare': 100,
            'Epic': 200,
            'Legendary': 500
        }[achievement.rarity] || 50;
        setTimeout(() => updateXP(bonusXP), 1000);
    };
    const favoriteTrack = async (track) => {
        if (!user)
            return false;
        const favoritesPlaylist = user.playlists.find(p => p.name === 'Quiz Favorites');
        if (favoritesPlaylist) {
            return await addToPlaylist(favoritesPlaylist.id, { ...track, isFavorite: true });
        }
        return false;
    };
    const value = {
        user,
        isAuthenticated: !!user,
        isFirstTime,
        isLoading,
        login,
        register,
        logout,
        updateProfile,
        completeOnboarding,
        skipOnboarding,
        resetOnboarding,
        addToPlaylist,
        createPlaylist,
        updateXP,
        unlockAchievement,
        favoriteTrack,
        isAdmin: () => user?.role === 'superuser',
        isDJ: () => user?.role === 'dj',
        isDJOrAdmin: () => user?.role === 'dj' || user?.role === 'superuser',
        hasRole: (role) => user?.role === role,
        refreshUser: async () => {
            try {
                const userInfo = (0, auth_cookies_client_1.getClientUserInfo)();
                if (userInfo) {
                    const data = await api_client_1.api.get('/api/auth/profile');
                    if (data.success && data.user) {
                        const refreshedUser = {
                            ...user,
                            id: data.user.id,
                            role: data.user.role,
                            username: data.user.username,
                            displayName: data.user.displayName || data.user.username,
                            email: data.user.email,
                            avatar: data.user.avatarUrl || user?.avatar || 'rockstar',
                            favoriteGenres: data.user.favoriteGenres || [],
                            location: data.user.location || '',
                            joinDate: new Date(data.user.createdAt).toLocaleDateString(),
                            bio: user?.bio || '',
                            preferences: user?.preferences || {
                                theme: 'system',
                                difficulty: 3,
                                volume: 70,
                                autoPlay: true,
                                showHints: true,
                                soundEffects: true
                            },
                            isFirstTime: false,
                            level: user?.level || 1,
                            xp: user?.xp || 0,
                            xpToNext: user?.xpToNext || 100,
                            stats: user?.stats || {
                                totalGames: 0,
                                winRate: 0,
                                averageScore: 0,
                                bestStreak: 0,
                                hoursPlayed: 0,
                                achievements: 0,
                                rank: 0,
                                favoriteMode: 'Classic Quiz'
                            },
                            achievements: user?.achievements || SAMPLE_ACHIEVEMENTS.map(a => ({ ...a, earned: false, earnedAt: undefined })),
                            playlists: user?.playlists || [],
                            settings: user?.settings || {
                                emailNotifications: true,
                                soundEffects: true,
                                autoPlay: true,
                                showHints: true,
                                publicProfile: true,
                                shareStats: false
                            }
                        };
                        setUser(refreshedUser);
                        setIsFirstTime(false);
                    }
                    else {
                        const hasCompletedOnboarding = localStorage.getItem('music-quiz-onboarding-complete');
                        if (!hasCompletedOnboarding) {
                            setIsFirstTime(true);
                        }
                    }
                }
                else {
                    const hasCompletedOnboarding = localStorage.getItem('music-quiz-onboarding-complete');
                    if (!hasCompletedOnboarding) {
                        setIsFirstTime(true);
                    }
                }
            }
            catch (error) {
                console.error('Role refresh error:', error);
                sonner_1.toast.warning('Profile refresh failed', {
                    description: 'Unable to update user profile from server',
                    duration: 4000
                });
            }
        },
        lastRoleCheck: null
    };
    return (<UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>);
}
function useUser() {
    const context = (0, react_1.useContext)(UserContext);
    if (context === undefined) {
        throw new Error('useUser must be used within a UserProvider');
    }
    return context;
}
//# sourceMappingURL=user-context.jsx.map