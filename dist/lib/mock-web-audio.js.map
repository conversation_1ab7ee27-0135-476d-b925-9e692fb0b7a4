{"version": 3, "file": "mock-web-audio.js", "sourceRoot": "", "sources": ["../../lib/mock-web-audio.ts"], "names": [], "mappings": ";;;AAgaA,4DAoIC;AAKD,sDA+BC;AA/jBD,MAAM,cAAc;IAMlB,YAAY,YAAoB,EAAE,QAAiB,EAAE,QAAiB;QACpE,IAAI,CAAC,aAAa,GAAG,YAAY,CAAA;QACjC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAA;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,OAAO,CAAC,GAAG,CAAC,8CAA8C,YAAY,EAAE,CAAC,CAAA;IAC3E,CAAC;IAED,cAAc,CAAC,KAAa,EAAE,SAAiB;QAI7C,OAAO,CAAC,GAAG,CACT,kCAAkC,KAAK,KAAK,SAAS,2BAA2B,gBAAgB,CAAC,WAAW,EAAE,CAC/G,CAAA;QACD,IAAI,gBAAgB,CAAC,WAAW,IAAI,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QACpB,CAAC;IAEH,CAAC;IAED,uBAAuB,CAAC,KAAa,EAAE,OAAe;QACpD,OAAO,CAAC,GAAG,CAAC,2CAA2C,KAAK,KAAK,OAAO,WAAW,CAAC,CAAA;QAGpF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,CAAC;IAED,4BAA4B,CAAC,KAAa,EAAE,OAAe;QACzD,OAAO,CAAC,GAAG,CAAC,gDAAgD,KAAK,KAAK,OAAO,WAAW,CAAC,CAAA;QAEzF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,CAAC;IAED,eAAe,CAAC,MAAc,EAAE,SAAiB,EAAE,YAAoB;QACrE,OAAO,CAAC,GAAG,CAAC,mCAAmC,MAAM,KAAK,SAAS,KAAK,YAAY,WAAW,CAAC,CAAA;QAEhG,IAAI,CAAC,KAAK,GAAG,MAAM,CAAA;IACrB,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAA;IAC3B,CAAC;CACF;AAKD,MAAM,aAAa;IAMjB,YAAY,OAAyB;QAJ3B,gBAAW,GAAoB,EAAE,CAAA;QACpC,mBAAc,GAAG,CAAC,CAAA;QAClB,oBAAe,GAAG,CAAC,CAAA;QAGxB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC,CAAA;IAClE,CAAC;IAED,OAAO,CACL,eAA+C,EAC/C,WAAoB,EACpB,UAAmB;QAEnB,IAAI,eAAe,YAAY,aAAa,EAAE,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,WAAW,CAAC,IAAI,iBAAiB,eAAe,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAA;YACvG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YAItC,OAAO,eAAe,CAAA;QACxB,CAAC;aAAM,IAAI,eAAe,YAAY,cAAc,EAAE,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,WAAW,CAAC,IAAI,qCAAqC,eAAe,CAAC,KAAK,EAAE,CAAC,CAAA;QAGlH,CAAC;IACH,CAAC;IAED,UAAU,CAAC,WAAqD,EAAE,MAAe,EAAE,KAAc;QAC/F,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,WAAW,CAAC,IAAI,uBAAuB,CAAC,CAAA;QAC3E,IAAI,WAAW,YAAY,aAAa,EAAE,CAAC;YACzC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QAC5E,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;QACvB,CAAC;IAEH,CAAC;CACF;AAKD,MAAM,yBAA0B,SAAQ,aAAa;IAQnD,YAAY,OAAyB;QACnC,KAAK,CAAC,OAAO,CAAC,CAAA;QART,WAAM,GAAuB,IAAI,CAAA;QACjC,SAAI,GAAG,KAAK,CAAA;QACZ,cAAS,GAAG,CAAC,CAAA;QACb,YAAO,GAAG,CAAC,CAAA;QACX,YAAO,GAAwB,IAAI,CAAA;QAClC,eAAU,GAAG,KAAK,CAAA;QAIxB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;IACzB,CAAC;IAED,KAAK,CAAC,IAAa,EAAE,MAAe,EAAE,QAAiB;QACrD,OAAO,CAAC,GAAG,CAAC,yCAAyC,IAAI,YAAY,MAAM,cAAc,QAAQ,WAAW,CAAC,CAAA;QAC7G,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QAKtB,IAAI,QAAQ,EAAE,CAAC;YACb,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,IAAI,EAAE,CAAA;gBACX,IAAI,IAAI,CAAC,OAAO;oBAAE,IAAI,CAAC,OAAO,EAAE,CAAA;YAClC,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,CAAA;QACrB,CAAC;IACH,CAAC;IAED,IAAI,CAAC,IAAa;QAChB,OAAO,CAAC,GAAG,CAAC,wCAAwC,IAAI,WAAW,CAAC,CAAA;QACpE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;IAGzB,CAAC;CACF;AAKD,MAAM,YAAa,SAAQ,aAAa;IAGtC,YAAY,OAAyB;QACnC,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,IAAI,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACtC,CAAC;CACF;AAKD,MAAM,oBAAqB,SAAQ,aAAa;IAM9C,YAAY,OAAyB;QACnC,KAAK,CAAC,OAAO,CAAC,CAAA;QANT,SAAI,GAAqB,SAAS,CAAA;QAOvC,IAAI,CAAC,SAAS,GAAG,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;QACnE,IAAI,CAAC,CAAC,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,IAAI,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;IAC5C,CAAC;IAED,oBAAoB,CAAC,WAAyB,EAAE,WAAyB,EAAE,aAA2B;QACpG,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAA;QAGjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE;YAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QAC/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE;YAAE,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;IACrE,CAAC;CACF;AAOD,MAAM,cAAe,SAAQ,aAAa;IAaxC,YAAY,OAAyB;QACnC,KAAK,CAAC,OAAO,CAAC,CAAA;QAbT,iBAAY,GAAqB,YAAY,CAAA;QAC7C,kBAAa,GAAsB,SAAS,CAAA;QAC5C,gBAAW,GAAG,CAAC,CAAA;QACf,gBAAW,GAAG,KAAK,CAAA;QACnB,kBAAa,GAAG,CAAC,CAAA;QACjB,mBAAc,GAAG,GAAG,CAAA;QACpB,mBAAc,GAAG,GAAG,CAAA;QACpB,kBAAa,GAAG,CAAC,CAAA;QAOtB,IAAI,CAAC,GAAG,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAIzC,CAAC;IAED,WAAW,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QACzC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;IACtE,CAAC;IAED,cAAc,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QAC5C,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;IACzE,CAAC;CACF;AAKD,MAAM,aAAc,SAAQ,aAAa;IAGvC,YAAY,OAAyB,EAAE,YAAY,GAAG,GAAG;QACvD,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC,CAAA;IACzD,CAAC;CACF;AAKD,MAAM,iBAAkB,SAAQ,aAAa;IAI3C,YAAY,OAAyB;QACnC,KAAK,CAAC,OAAO,CAAC,CAAA;QAJT,WAAM,GAAuB,IAAI,CAAA;QACjC,cAAS,GAAG,IAAI,CAAA;IAIvB,CAAC;CACF;AAKD,MAAM,gBAAiB,SAAQ,aAAa;IAO1C,YAAY,OAAyB;QACnC,KAAK,CAAC,OAAO,CAAC,CAAA;QAPT,YAAO,GAAG,IAAI,CAAA;QAEd,gBAAW,GAAG,CAAC,GAAG,CAAA;QAClB,gBAAW,GAAG,CAAC,EAAE,CAAA;QACjB,0BAAqB,GAAG,GAAG,CAAA;QAIhC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,oBAAoB,CAAC,KAAiB;QAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;YAAE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAA;IACvE,CAAC;IAED,qBAAqB,CAAC,KAAmB;QAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;YACnC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,CAAA;IACvF,CAAC;IAED,qBAAqB,CAAC,KAAiB;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;YAAE,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAA;IACrF,CAAC;CACF;AAKD,MAAa,gBAAgB;IAM3B;QAJgB,eAAU,GAAW,KAAK,CAAA;QAElC,WAAM,GAAsB,WAAW,CAAA;QAG7C,IAAI,CAAC,WAAW,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,CACzC;QAAC,IAAI,CAAC,WAAmB,CAAC,WAAW,CAAC,IAAI,GAAG,sBAAsB,CAAA;QACpE,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAA;QAGxC,WAAW,CAAC,GAAG,EAAE;YACf,gBAAgB,CAAC,WAAW,IAAI,GAAG,CAAA;QACrC,CAAC,EAAE,GAAG,CAAC,CAAA;IACT,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED,MAAM;QACJ,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;QACjD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;QAGvB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAED,OAAO;QACL,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAA;QAClD,IAAI,CAAC,MAAM,GAAG,WAAW,CAAA;QACzB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAED,KAAK;QACH,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;QAChD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAA;QACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAED,kBAAkB;QAChB,OAAO,IAAI,yBAAyB,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED,UAAU;QACR,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,CAAA;IAC/B,CAAC;IAED,kBAAkB;QAChB,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAA;IACvC,CAAC;IAED,YAAY;QAGV,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,CAAA;IACjC,CAAC;IAED,WAAW,CAAC,YAAqB;QAC/B,OAAO,IAAI,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC,CAAA;IAC9C,CAAC;IAED,eAAe;QACb,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAA;IACpC,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAA;IACnC,CAAC;IAED,eAAe,CACb,SAAsB,EACtB,eAAmD,EACnD,aAA6C;QAE7C,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAA;QAKxD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAErC,UAAU,CAAC,GAAG,EAAE;gBACd,MAAM,UAAU,GAAgB;oBAC9B,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,MAAM,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC;oBAC3B,QAAQ,EAAE,CAAC;oBACX,gBAAgB,EAAE,CAAC;oBACnB,cAAc,EAAE,CAAC,OAAe,EAAgB,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;oBACxF,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;oBACzB,aAAa,EAAE,GAAG,EAAE,GAAE,CAAC;iBACxB,CAAA;gBACD,IAAI,eAAe;oBAAE,eAAe,CAAC,UAAU,CAAC,CAAA;gBAChD,OAAO,CAAC,UAAU,CAAC,CAAA;YACrB,CAAC,EAAE,EAAE,CAAC,CAAA;QACR,CAAC,CAAC,CAAA;IACJ,CAAC;;AAjGH,4CAkGC;AA/Fe,4BAAW,GAAG,CAAC,AAAJ,CAAI;AA6H/B,IAAI,YAAY,GAA4B,IAAI,CAAA;AAChD,IAAI,UAAU,GAAqC,IAAI,CAAA;AACvD,IAAI,QAAQ,GAAwB,IAAI,CAAA;AACxC,IAAI,UAAU,GAAgC,IAAI,CAAA;AAClD,IAAI,UAAU,GAA0B,IAAI,CAAA;AAC5C,IAAI,SAAS,GAAyB,IAAI,CAAA;AAC1C,IAAI,aAAa,GAA6B,IAAI,CAAA;AAQ3C,KAAK,UAAU,wBAAwB,CAAC,KAAmB,EAAE,OAAsB;IACxF,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,YAAY,GAAG,IAAI,gBAAgB,EAAE,CAAA;IACvC,CAAC;IACD,MAAM,YAAY,CAAC,MAAM,EAAE,CAAA;IAG3B,IAAI,UAAU,EAAE,CAAC;QACf,IAAI,CAAC;YACH,UAAU,CAAC,IAAI,EAAE,CAAA;QACnB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;QAEb,CAAC;QACD,UAAU,CAAC,UAAU,EAAE,CAAA;IACzB,CAAC;IAGD,UAAU,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAA;IAW9C,MAAM,iBAAiB,GAAG,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA;IAC1F,UAAU,CAAC,MAAM,GAAG,iBAAiB,CAAA;IACrC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAA;IACtB,OAAO,CAAC,GAAG,CAAC,uBAAuB,KAAK,CAAC,KAAK,uBAAuB,CAAC,CAAA;IAEtE,IAAI,WAAW,GAAkB,UAAU,CAAA;IAG3C,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC/B,IAAI,CAAC,QAAQ;YAAE,QAAQ,GAAG,YAAY,CAAC,UAAU,EAAE,CAAA;QACnD,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;QACpE,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAC7B,WAAW,GAAG,QAAQ,CAAA;QACtB,OAAO,CAAC,GAAG,CAAC,uBAAuB,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;IACpD,CAAC;SAAM,IAAI,QAAQ,EAAE,CAAC;QAEpB,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;IACpF,CAAC;IAGD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,CAAC,UAAU;YAAE,UAAU,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAA;QAC/D,UAAU,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAA;QACrC,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;QACvF,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;YACrB,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;QACzE,CAAC;QACD,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QAC/B,WAAW,GAAG,UAAU,CAAA;QACxB,OAAO,CAAC,GAAG,CAAC,8BAA8B,OAAO,CAAC,MAAM,CAAC,IAAI,UAAU,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAA;IACpG,CAAC;SAAM,IAAI,UAAU,EAAE,CAAC;QAEtB,UAAU,CAAC,IAAI,GAAG,SAAS,CAAA;QAC3B,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;QAChG,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;IAClF,CAAC;IAGD,IAAI,OAAO,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QAC9B,IAAI,CAAC,UAAU;YAAE,UAAU,GAAG,YAAY,CAAC,YAAY,EAAE,CAAA;QACzD,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;QACpE,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QAC/B,WAAW,GAAG,UAAU,CAAA;QACxB,OAAO,CAAC,GAAG,CAAC,sBAAsB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAA;IAClD,CAAC;SAAM,IAAI,UAAU,EAAE,CAAC;QACtB,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;IACtF,CAAC;IAGD,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,IAAI,CAAC,SAAS;YAAE,SAAS,GAAG,YAAY,CAAC,WAAW,EAAE,CAAA;QACtD,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;QACrF,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QAC9B,WAAW,GAAG,SAAS,CAAA;QACvB,OAAO,CAAC,GAAG,CAAC,wBAAwB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAA;IAOjE,CAAC;SAAM,IAAI,SAAS,EAAE,CAAC;QACrB,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;IAChG,CAAC;IAGD,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;QACxD,IAAI,CAAC,aAAa;YAAE,aAAa,GAAG,YAAY,CAAC,eAAe,EAAE,CAAA;QAOlE,OAAO,CAAC,GAAG,CAAC,oDAAoD,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAA;QAEpG,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;QAClC,WAAW,GAAG,aAAa,CAAA;QAC3B,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA;IACtC,CAAC;IAGD,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;IAC7C,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAA;IAG3D,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACnB,OAAO,CAAC,GAAG,CAAC,8BAA8B,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA;AAiB5D,CAAC;AAKD,SAAgB,qBAAqB,CAAC,SAAiB,EAAE,KAAU;IACjE,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;QACtD,OAAO,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAA;QACvE,OAAM;IACR,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,0CAA0C,SAAS,eAAe,KAAK,EAAE,CAAC,CAAA;IAEtF,QAAQ,SAAS,EAAE,CAAC;QAClB,KAAK,MAAM;YACT,IAAI,QAAQ;gBAAE,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,KAAe,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;YACrF,MAAK;QACP,KAAK,kBAAkB;YACrB,IAAI,UAAU;gBAAE,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,KAAe,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;YAC9F,MAAK;QACP,KAAK,UAAU;YACb,IAAI,UAAU;gBAAE,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,KAAe,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;YACtF,MAAK;QACP,KAAK,aAAa;YAChB,IAAI,UAAU;gBAAE,UAAU,CAAC,IAAI,GAAG,KAAyB,CAAA;YAC3D,MAAK;QACP,KAAK,KAAK;YACR,IAAI,UAAU;gBAAE,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,KAAe,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;YACxF,MAAK;QACP,KAAK,iBAAiB;YACpB,IAAI,SAAS;gBAAE,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,KAAe,EAAE,YAAY,CAAC,WAAW,CAAC,CAAA;YAC5F,MAAK;QAEP;YACE,OAAO,CAAC,IAAI,CAAC,mCAAmC,SAAS,EAAE,CAAC,CAAA;IAChE,CAAC;AACH,CAAC"}