{"version": 3, "file": "multiplayer-diagnostic.js", "sourceRoot": "", "sources": ["../../lib/multiplayer-diagnostic.ts"], "names": [], "mappings": ";;;AAWA,MAAa,yBAAyB;IAAtC;QACU,gBAAW,GAA4B,EAAE,CAAA;QAChC,oBAAe,GAAG,GAAG,CAAA;IAuMxC,CAAC;IAlMC,MAAM,CAAC,KAAa,EAAE,UAAe,EAAE,EAAE,WAA8C,QAAQ;QAC7F,MAAM,UAAU,GAA0B;YACxC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,KAAK;YACL,OAAO;YACP,QAAQ;SACT,CAAA;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QAGpC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACnD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QACpE,CAAC;QAGD,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC5B,OAAO,CAAC,KAAK,CAAC,qCAAqC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAA;QACtE,CAAC;aAAM,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,iCAAiC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAA;QACjE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,2BAA2B,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE,EAAE,OAAO,CAAC,CAAA;QACrF,CAAC;IACH,CAAC;IAKD,wBAAwB,CAAC,KAUxB;QAEC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE;gBAClC,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ;aACzB,EAAE,UAAU,CAAC,CAAA;QAChB,CAAC;QAGD,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,wBAAwB,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;QACtD,CAAC;QAGD,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC;YAC7E,IAAI,CAAC,MAAM,CAAC,0CAA0C,EAAE;gBACtD,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,cAAc,EAAE,KAAK,CAAC,oBAAoB,CAAC,MAAM;gBACjD,SAAS,EAAE,KAAK,CAAC,SAAS;aAC3B,EAAE,MAAM,CAAC,CAAA;QACZ,CAAC;QAGD,IAAI,KAAK,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAC7E,IAAI,CAAC,MAAM,CAAC,0CAA0C,EAAE;gBACtD,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,kBAAkB,EAAE,CAAC,CAAC,KAAK,CAAC,0BAA0B;aACvD,EAAE,UAAU,CAAC,CAAA;QAChB,CAAC;QAGD,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC;YACnG,IAAI,CAAC,MAAM,CAAC,gCAAgC,EAAE;gBAC5C,aAAa,EAAE,KAAK,CAAC,aAAa;gBAClC,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,EAAE,MAAM,CAAC,CAAA;QACZ,CAAC;QAGD,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACxD,MAAM,YAAY,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAA;YACnE,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;gBAC5C,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAA;gBAExG,IAAI,WAAW,GAAG,IAAI,EAAE,CAAC;oBACvB,IAAI,CAAC,MAAM,CAAC,qCAAqC,EAAE;wBACjD,eAAe,EAAE,WAAW;wBAC5B,WAAW,EAAE,aAAa,CAAC,MAAM;qBAClC,EAAE,MAAM,CAAC,CAAA;gBACZ,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAKD,YAAY;QACV,MAAM,WAAW,GAAa,EAAE,CAAA;QAChC,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAA;QAC9E,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAA;QAEtE,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC;YAChE,WAAW,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAA;YAC3E,WAAW,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAA;YACxD,WAAW,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAA;QAC1D,CAAC;QAED,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC;YACxE,WAAW,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAA;YACpE,WAAW,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAA;YAC1D,WAAW,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAA;QACvE,CAAC;QAED,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC;YAC/D,WAAW,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAA;YACrE,WAAW,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAA;YACpE,WAAW,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAA;QAChF,CAAC;QAED,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;YACzD,WAAW,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAA;YAC/D,WAAW,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAA;YACpE,WAAW,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAA;QAC3D,CAAC;QAED,OAAO,WAAW,CAAA;IACpB,CAAC;IAKD,cAAc;QAOZ,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM,CAAA;QACpF,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,CAAA;QAE5E,IAAI,OAAO,GAAG,oCAAoC,CAAA;QAClD,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,GAAG,GAAG,aAAa,6BAA6B,CAAA;QACzD,CAAC;aAAM,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO,GAAG,GAAG,SAAS,kCAAkC,CAAA;QAC1D,CAAC;QAED,OAAO;YACL,OAAO;YACP,cAAc,EAAE,aAAa;YAC7B,UAAU,EAAE,SAAS;YACrB,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE;YAChC,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;SACjD,CAAA;IACH,CAAC;IAKD,KAAK;QACH,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;IACvB,CAAC;IAKD,UAAU;QACR,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;QAEpC,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAA;QACtD,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAA;QACvC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,cAAc,CAAC,CAAA;QACtD,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAA;QAEvD,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;YAC3B,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;gBAC/C,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,UAAU,EAAE,CAAC,CAAA;YAC9C,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;YAC7B,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBAC5C,OAAO,CAAC,GAAG,CAAC,MAAM,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,OAAO,CAAC,CAAA;YACjG,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,CAAC,QAAQ,EAAE,CAAA;IACpB,CAAC;CACF;AAzMD,8DAyMC;AAGY,QAAA,qBAAqB,GAAG,IAAI,yBAAyB,EAAE,CAAA;AAGpE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;IAC5E,WAAW,CAAC,GAAG,EAAE;QACf,MAAM,MAAM,GAAG,6BAAqB,CAAC,cAAc,EAAE,CAAA;QACrD,IAAI,MAAM,CAAC,cAAc,GAAG,CAAC,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YACvD,6BAAqB,CAAC,UAAU,EAAE,CAAA;QACpC,CAAC;IACH,CAAC,EAAE,KAAK,CAAC,CAAA;AACX,CAAC"}