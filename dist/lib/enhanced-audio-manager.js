"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedAudioManager = void 0;
const audio_manager_1 = require("./audio-manager");
class EnhancedAudioManager extends audio_manager_1.AudioManager {
    constructor(config) {
        super(config);
        this.crossfadeConfig = {
            duration: 3,
            enabled: true
        };
        this.normalizationConfig = {
            enabled: true,
            targetLevel: -14,
            maxGain: 10
        };
        this.audioContext = null;
        this.gainNode = null;
        this.currentSource = null;
        this.nextAudio = null;
        this.crossfading = false;
    }
    async initialize() {
        await super.initialize();
        if (typeof window !== 'undefined' && window.AudioContext) {
            this.audioContext = new AudioContext();
            this.gainNode = this.audioContext.createGain();
            this.gainNode.connect(this.audioContext.destination);
            if (this.audio) {
                this.currentSource = this.audioContext.createMediaElementSource(this.audio);
                this.currentSource.connect(this.gainNode);
            }
        }
    }
    setCrossfadeEnabled(enabled) {
        this.crossfadeConfig.enabled = enabled;
    }
    setCrossfadeDuration(seconds) {
        this.crossfadeConfig.duration = Math.max(0, Math.min(10, seconds));
    }
    setNormalizationEnabled(enabled) {
        this.normalizationConfig.enabled = enabled;
    }
    async skip(direction) {
        if (!this.crossfadeConfig.enabled || direction === 'back') {
            return super.skip(direction);
        }
        await this.crossfadeToNext();
    }
    async crossfadeToNext() {
        if (this.crossfading || !this.audio || !this.audioContext || !this.gainNode) {
            return;
        }
        this.crossfading = true;
        try {
            const nextTrackUrl = await this.getNextTrackUrl();
            if (!nextTrackUrl) {
                this.audio.pause();
                this.crossfading = false;
                return;
            }
            this.nextAudio = new Audio(nextTrackUrl);
            this.nextAudio.crossOrigin = 'anonymous';
            const nextGain = await this.calculateNormalizationGain(nextTrackUrl);
            const nextSource = this.audioContext.createMediaElementSource(this.nextAudio);
            const nextGainNode = this.audioContext.createGain();
            nextGainNode.gain.value = 0;
            if (this.normalizationConfig.enabled) {
                nextGainNode.gain.value = 0;
            }
            nextSource.connect(nextGainNode);
            nextGainNode.connect(this.audioContext.destination);
            await this.nextAudio.play();
            const startTime = this.audioContext.currentTime;
            const fadeDuration = this.crossfadeConfig.duration;
            this.gainNode.gain.setValueAtTime(this.gainNode.gain.value, startTime);
            this.gainNode.gain.linearRampToValueAtTime(0, startTime + fadeDuration);
            const targetGain = this.normalizationConfig.enabled ? nextGain : 1;
            nextGainNode.gain.setValueAtTime(0, startTime);
            nextGainNode.gain.linearRampToValueAtTime(targetGain, startTime + fadeDuration);
            setTimeout(() => {
                if (this.audio) {
                    this.audio.pause();
                    this.audio.src = '';
                }
                this.audio = this.nextAudio;
                this.currentSource = nextSource;
                this.gainNode = nextGainNode;
                this.nextAudio = null;
                this.sendMPDCommand('next');
                this.crossfading = false;
            }, fadeDuration * 1000);
        }
        catch (error) {
            console.error('Crossfade failed:', error);
            this.crossfading = false;
            return super.skip('forward');
        }
    }
    async getNextTrackUrl() {
        try {
            return null;
        }
        catch (error) {
            console.error('Failed to get next track URL:', error);
            return null;
        }
    }
    async calculateNormalizationGain(url) {
        if (!this.normalizationConfig.enabled) {
            return 1;
        }
        try {
            return 0.8;
        }
        catch (error) {
            console.error('Failed to calculate normalization gain:', error);
            return 1;
        }
    }
    async sendMPDCommand(command) {
        try {
            await fetch('/api/mpd/control', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: command })
            });
        }
        catch (error) {
            console.error('Failed to send MPD command:', error);
        }
    }
    async applyNormalization() {
        if (!this.normalizationConfig.enabled || !this.gainNode || !this.audio?.src) {
            return;
        }
        const gain = await this.calculateNormalizationGain(this.audio.src);
        this.gainNode.gain.setValueAtTime(gain, this.audioContext.currentTime);
    }
    setVolume(volume) {
        super.setVolume(volume);
        if (this.gainNode && this.audioContext) {
            const gainValue = volume / 100;
            this.gainNode.gain.setValueAtTime(gainValue, this.audioContext.currentTime);
        }
    }
    cleanup() {
        if (this.audioContext) {
            this.audioContext.close();
        }
        super.cleanup();
    }
}
exports.EnhancedAudioManager = EnhancedAudioManager;
//# sourceMappingURL=enhanced-audio-manager.js.map