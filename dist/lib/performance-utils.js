"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManager = exports.MemoryManager = void 0;
exports.useDebounce = useDebounce;
exports.useThrottle = useThrottle;
exports.calculateVirtualItems = calculateVirtualItems;
exports.fuzzySearch = fuzzySearch;
exports.memoize = memoize;
const react_1 = require("react");
class MemoryManager {
    constructor() {
        this.memoryThresholds = {
            warning: 50 * 1024 * 1024,
            critical: 100 * 1024 * 1024
        };
    }
    static getInstance() {
        if (!MemoryManager.instance) {
            MemoryManager.instance = new MemoryManager();
        }
        return MemoryManager.instance;
    }
    checkMemoryPressure() {
        if ('memory' in performance) {
            const memory = performance.memory;
            const used = memory.usedJSHeapSize;
            if (used > this.memoryThresholds.critical)
                return 'critical';
            if (used > this.memoryThresholds.warning)
                return 'warning';
        }
        return 'normal';
    }
    getMemoryStats() {
        if ('memory' in performance) {
            const memory = performance.memory;
            return {
                used: memory.usedJSHeapSize,
                total: memory.totalJSHeapSize,
                limit: memory.jsHeapSizeLimit,
                usedMB: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                pressure: this.checkMemoryPressure()
            };
        }
        return null;
    }
    adaptToMemoryPressure(pressure) {
        switch (pressure) {
            case 'critical':
                return { virtualListOverscan: 1, maxCacheSize: 50, pageSize: 50 };
            case 'warning':
                return { virtualListOverscan: 2, maxCacheSize: 200, pageSize: 100 };
            default:
                return { virtualListOverscan: 3, maxCacheSize: 500, pageSize: 200 };
        }
    }
}
exports.MemoryManager = MemoryManager;
function useDebounce(value, delay) {
    const [debouncedValue, setDebouncedValue] = (0, react_1.useState)(value);
    (0, react_1.useEffect)(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);
        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);
    return debouncedValue;
}
function useThrottle(callback, delay) {
    const lastRun = (0, react_1.useRef)(Date.now());
    return (0, react_1.useCallback)(((...args) => {
        if (Date.now() - lastRun.current >= delay) {
            callback(...args);
            lastRun.current = Date.now();
        }
    }), [callback, delay]);
}
function calculateVirtualItems(scrollTop, itemCount, options) {
    const { itemHeight, containerHeight, overscan = 3 } = options;
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(itemCount - 1, Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan);
    return {
        startIndex,
        endIndex,
        offsetY: startIndex * itemHeight,
        totalHeight: itemCount * itemHeight,
        visibleItems: endIndex - startIndex + 1
    };
}
function fuzzySearch(items, searchTerm, searchFields) {
    if (!searchTerm)
        return items;
    const searchLower = searchTerm.toLowerCase();
    const searchWords = searchLower.split(' ').filter(word => word.length > 0);
    return items.filter(item => {
        const searchText = searchFields
            .map(field => String(item[field] || ''))
            .join(' ')
            .toLowerCase();
        return searchWords.every(word => searchText.includes(word));
    });
}
function memoize(fn) {
    const cache = new Map();
    return ((...args) => {
        const key = JSON.stringify(args);
        if (cache.has(key)) {
            return cache.get(key);
        }
        const result = fn(...args);
        cache.set(key, result);
        if (cache.size > 1000) {
            const firstKey = cache.keys().next().value;
            cache.delete(firstKey);
        }
        return result;
    });
}
class CacheManager {
    constructor() {
        this.cache = new Map();
    }
    set(key, data, ttl = 300000) {
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl
        });
        this.cleanup();
    }
    get(key) {
        const entry = this.cache.get(key);
        if (!entry)
            return null;
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    has(key) {
        return this.get(key) !== null;
    }
    delete(key) {
        this.cache.delete(key);
    }
    clear() {
        this.cache.clear();
    }
    cleanup() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
                this.cache.delete(key);
            }
        }
    }
    size() {
        this.cleanup();
        return this.cache.size;
    }
}
exports.CacheManager = CacheManager;
//# sourceMappingURL=performance-utils.js.map