"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMPDConnectionString = exports.validateMPDConfig = exports.getEnvironmentConfig = exports.MPD_SETTINGS = exports.MPD_CONFIG = void 0;
exports.MPD_CONFIG = {
    host: process.env.MPD_HOST || 'localhost',
    port: parseInt(process.env.MPD_PORT || '6600'),
    password: process.env.MPD_PASSWORD,
};
exports.MPD_SETTINGS = {
    maxReconnectAttempts: 5,
    reconnectDelay: 1000,
    defaultVolume: 50,
    maxVolume: 100,
    maxQueueSize: 1000,
    searchLimit: 100,
    autoUpdateDatabase: true,
    updateInterval: 300000,
};
const getEnvironmentConfig = () => {
    const env = process.env.NODE_ENV || 'development';
    switch (env) {
        case 'production':
            return {
                ...exports.MPD_CONFIG,
                host: process.env.MPD_HOST || 'mpd-server',
                port: parseInt(process.env.MPD_PORT || '6600'),
            };
        case 'development':
            return {
                ...exports.MPD_CONFIG,
                host: process.env.MPD_HOST || 'localhost',
                port: parseInt(process.env.MPD_PORT || '6600'),
            };
        case 'test':
            return {
                ...exports.MPD_CONFIG,
                host: 'localhost',
                port: 6601,
            };
        default:
            return exports.MPD_CONFIG;
    }
};
exports.getEnvironmentConfig = getEnvironmentConfig;
const validateMPDConfig = (config) => {
    if (config.path) {
        return typeof config.path === 'string' && config.path.length > 0;
    }
    else {
        return (typeof config.host === 'string' &&
            config.host.length > 0 &&
            typeof config.port === 'number' &&
            config.port > 0 &&
            config.port <= 65535);
    }
};
exports.validateMPDConfig = validateMPDConfig;
const getMPDConnectionString = (config) => {
    if (config.path) {
        return `unix:${config.path}`;
    }
    else {
        const auth = config.password ? '***@' : '';
        return `tcp://${auth}${config.host}:${config.port}`;
    }
};
exports.getMPDConnectionString = getMPDConnectionString;
exports.default = exports.MPD_CONFIG;
//# sourceMappingURL=mpd-config.js.map