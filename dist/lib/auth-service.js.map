{"version": 3, "file": "auth-service.js", "sourceRoot": "", "sources": ["../../lib/auth-service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4OA,sCAmBC;AA/PD,+BAAyC;AACzC,mEAA0C;AAI1C,SAAS,YAAY;IACnB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAElC,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAA;IAC/F,CAAC;IAED,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAA;IACrC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,KAAK,CAAC,qFAAqF,CAAC,CAAA;IACxG,CAAC;IAGD,IAAI,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;QACvB,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAA;IAChF,CAAC;IAED,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;AACzC,CAAC;AAgBD,MAAa,WAAW;IAItB,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,OAAe,EAAE,QAAgB;QACzD,IAAI,CAAC;YAEH,IAAI,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACtC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,WAAW,EAAE,EAAE;aACxC,CAAC,CAAA;YAGF,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBAClC,KAAK,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;iBAC7B,CAAC,CAAA;YACJ,CAAC;YAGD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACjC,KAAK,EAAE,EAAE,WAAW,EAAE,OAAO,EAAE;iBAChC,CAAC,CAAA;YACJ,CAAC;YAGD,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACpC,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBAClC,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,gBAAgB,EAAE;iBAC3D,CAAC,CAAA;YACJ,CAAC;YAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAA;YACb,CAAC;YAED,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,uBAAuB,GAAC,CAAA;YAC3D,MAAM,eAAe,GAAG,MAAM,SAAS,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;YAE/E,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAA;YACb,CAAC;YAED,MAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACvB,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;gBACtB,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE;aAChC,CAAC,CAAA;YAEF,MAAM,QAAQ,GAAa;gBACzB,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,SAAS;gBAC1C,IAAI,EAAE,IAAI,CAAC,IAAgB;gBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,SAAS;gBACtC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAA;YAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;YAEhD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAA;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;YAC9C,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,KAAa;QACxC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;YAC1C,IAAI,CAAC,IAAI;gBAAE,OAAO,IAAI,CAAA;YAEtB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACzD,IAAI,CAAC,aAAa;gBAAE,OAAO,IAAI,CAAA;YAE/B,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAA;YAE/D,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO;oBACL,IAAI,EAAE,aAAa;oBACnB,QAAQ,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;iBAClD,CAAA;YACH,CAAC;YAED,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAA;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAA;YAClD,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,KAAa;QACpC,IAAI,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,IAAA,gBAAS,EAAC,KAAK,EAAE,YAAY,EAAE,CAAC,CAAA;YAG1D,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACjB,OAAO,OAAO,CAAC,IAAgB,CAAA;YACjC,CAAC;YAGD,IAAI,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBAChD,OAAO;oBACL,EAAE,EAAE,OAAO,CAAC,EAAY;oBACxB,KAAK,EAAE,OAAO,CAAC,KAAe;oBAC9B,QAAQ,EAAE,OAAO,CAAC,QAAkB,IAAI,OAAO,CAAC,KAAe;oBAC/D,WAAW,EAAE,OAAO,CAAC,WAAqB;oBAC1C,IAAI,EAAE,OAAO,CAAC,IAAgB;oBAC9B,SAAS,EAAE,OAAO,CAAC,SAAmB;oBACtC,WAAW,EAAE,OAAO,CAAC,WAAqB,IAAI,IAAI;oBAClD,SAAS,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAmB,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;iBAC/D,CAAA;YACH,CAAC;YAED,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;YACvE,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;YAC1D,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,MAAc;QACzC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;aACtB,CAAC,CAAA;YAEF,IAAI,CAAC,IAAI;gBAAE,OAAO,IAAI,CAAA;YAEtB,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,SAAS;gBAC1C,IAAI,EAAE,IAAI,CAAC,IAAgB;gBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,SAAS;gBACtC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,IAAc;QACvC,OAAO,MAAM,IAAI,cAAO,CAAC,EAAE,IAAI,EAAE,CAAC;aAC/B,kBAAkB,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;aACpC,WAAW,EAAE;aACb,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;aACpC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAA;IACzB,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,OAAiB,EAAE,OAAiB;QACpE,OAAO,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW,CAAA;IACrF,CAAC;;AA9JH,kCA+JC;AA9JyB,wBAAY,GAAG,KAAK,CAAA;AACpB,6BAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;AA+JhE,MAAa,YAAY;IAIvB,MAAM,CAAC,QAAQ;QACb,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO,IAAI,CAAA;QAC9C,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IAC7C,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,KAAa;QAC3B,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAM;QACzC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;IAC7C,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAM;QACzC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACvC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IACxC,CAAC;IAED,MAAM,CAAC,aAAa;QAClB,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO,IAAI,CAAA;QAC9C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACpD,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QAC/C,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,IAAc;QACjC,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAM;QACzC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;IAC3D,CAAC;;AAjCH,oCAkCC;AAjCyB,sBAAS,GAAG,YAAY,CAAA;AACxB,qBAAQ,GAAG,cAAc,CAAA;AAmC5C,KAAK,UAAU,aAAa,CAAC,OAAgB;IAClD,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;QACvD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yCAAyC,EAAE,CAAA;QAC7E,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACrC,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;QAEjD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAA;QAC9D,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;IAChC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;QAC/C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAA;IAC7D,CAAC;AACH,CAAC"}