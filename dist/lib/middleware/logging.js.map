{"version": 3, "file": "logging.js", "sourceRoot": "", "sources": ["../../../lib/middleware/logging.ts"], "names": [], "mappings": ";;;AA4GA,4CAmBC;AAED,oDAYC;AAED,0DA0DC;AA6HD,0EA8CC;AAGD,0CAyCC;AAED,8CAGC;AAED,wDAkDC;AAzaD,MAAM,WAAW,GAAe,EAAE,CAAA;AAClC,MAAM,cAAc,GAAoB,EAAE,CAAA;AAC1C,MAAM,kBAAkB,GAAwB,EAAE,CAAA;AAGlD,WAAW,CAAC,GAAG,EAAE;IACf,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;IAGjD,OAAO,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;QACnE,WAAW,CAAC,KAAK,EAAE,CAAA;IACrB,CAAC;IAED,OAAO,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;QACzE,cAAc,CAAC,KAAK,EAAE,CAAA;IACxB,CAAC;IAED,OAAO,kBAAkB,CAAC,MAAM,GAAG,CAAC,IAAI,kBAAkB,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;QACjF,kBAAkB,CAAC,KAAK,EAAE,CAAA;IAC5B,CAAC;AACH,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;AAElB,SAAS,aAAa;IACpB,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1D,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;SACzC,IAAI,CAAC,EAAE,CAAC,CAAA;AACb,CAAC;AAED,SAAS,WAAW,CAAC,GAAgB;IACnC,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;IACpD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IAE3C,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;IACvC,CAAC;IAED,OAAO,MAAM,IAAI,GAAG,CAAC,EAAE,IAAI,SAAS,CAAA;AACtC,CAAC;AAED,SAAS,YAAY,CAAC,GAAgB;IACpC,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS,CAAA;AACnD,CAAC;AAED,SAAS,cAAc,CAAC,GAAgB;IACtC,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;IACvD,OAAO,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACxD,CAAC;AAED,SAAS,eAAe,CAAC,QAAsB;IAC7C,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;IAC5D,OAAO,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACxD,CAAC;AAED,SAAS,eAAe,CAAC,GAAgB;IACvC,OAAO;QACL,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,SAAS;QACjD,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,SAAS;KACxD,CAAA;AACH,CAAC;AAED,SAAgB,gBAAgB,CAAC,KAAuC;IACtE,cAAc,CAAC,IAAI,CAAC;QAClB,GAAG,KAAK;QACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;KACtB,CAAC,CAAA;IAGF,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;QAC/B,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;QACxB,EAAE,EAAE,KAAK,CAAC,EAAE;QACZ,OAAO,EAAE,KAAK,CAAC,OAAO;KACvB,CAAC,CAAA;IAGF,IAAI,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;QAElC,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAA;IACnD,CAAC;AACH,CAAC;AAED,SAAgB,oBAAoB,CAAC,MAAyB;IAC5D,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAG/B,IAAI,MAAM,CAAC,YAAY,GAAG,IAAI,EAAE,CAAC;QAC/B,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC7B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,UAAU,EAAE,MAAM,CAAC,UAAU;SAC9B,CAAC,CAAA;IACJ,CAAC;AACH,CAAC;AAED,SAAgB,uBAAuB,CAAC,UAMpC,EAAE;IACJ,MAAM,EACJ,WAAW,GAAG,IAAI,EAClB,WAAW,GAAG,IAAI,EAClB,cAAc,GAAG,IAAI,EACrB,gBAAgB,GAAG,CAAC,eAAe,EAAE,QAAQ,EAAE,WAAW,CAAC,EAC3D,eAAe,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,EACzD,GAAG,OAAO,CAAA;IAEX,OAAO,KAAK,UAAU,iBAAiB,CAAC,GAAgB;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,MAAM,KAAK,GAAG,aAAa,EAAE,CAAA;QAC7B,MAAM,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,CAAA;QAC3B,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;QACnC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,eAAe,CAAC,GAAG,CAAC,CAAA;QAGlD,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,qBAAqB,CAAC,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;QACzD,CAAC;QAGD,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QACxC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,CAAA;QAGlC,MAAM,WAAW,GAAG;YAClB,EAAE,EAAE,KAAK;YACT,SAAS,EAAE,SAAS;YACpB,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ;YAC/B,KAAK,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAClE,SAAS;YACT,EAAE;YACF,MAAM;YACN,SAAS;YACT,WAAW,EAAE,cAAc,CAAC,GAAG,CAAC;SACjC,CAAA;QAGD,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;gBACpC,IAAI,KAAK,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;oBAC/B,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,YAAY,CAAA;gBACzC,CAAC;YACH,CAAC;QACH,CAAC;QAGD,OAAO,IAAI,CAAA;IACb,CAAC,CAAA;AACH,CAAC;AAED,KAAK,UAAU,qBAAqB,CAClC,GAAgB,EAChB,EAAU,EACV,SAAiB,EACjB,MAAe;IAEf,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IAC5B,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAA;IACzB,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAA;IAGzC,MAAM,oBAAoB,GAAG;QAC3B,0BAA0B;QAC1B,yBAAyB;QACzB,wBAAwB;QACxB,yBAAyB;QACzB,wBAAwB;QACxB,yBAAyB;QACzB,sBAAsB;QACtB,sBAAsB;QACtB,wBAAwB;KACzB,CAAA;IAED,MAAM,OAAO,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,CAAA;IAClC,KAAK,MAAM,OAAO,IAAI,oBAAoB,EAAE,CAAC;QAC3C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1B,gBAAgB,CAAC;gBACf,IAAI,EAAE,uBAAuB;gBAC7B,QAAQ,EAAE,MAAM;gBAChB,EAAE;gBACF,SAAS;gBACT,MAAM;gBACN,OAAO,EAAE;oBACP,GAAG,EAAE,GAAG,CAAC,GAAG;oBACZ,OAAO,EAAE,OAAO,CAAC,MAAM;oBACvB,MAAM,EAAE,GAAG,CAAC,MAAM;iBACnB;aACF,CAAC,CAAA;YACF,MAAK;QACP,CAAC;IACH,CAAC;IAGD,MAAM,WAAW,GAAG;QAClB,8BAA8B;QAC9B,eAAe;QACf,aAAa;QACb,aAAa;QACb,8BAA8B;QAC9B,8BAA8B;QAC9B,gBAAgB;KACjB,CAAA;IAED,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;QAClC,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1B,gBAAgB,CAAC;gBACf,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,MAAM;gBAChB,EAAE;gBACF,SAAS;gBACT,MAAM;gBACN,OAAO,EAAE;oBACP,GAAG,EAAE,GAAG,CAAC,GAAG;oBACZ,OAAO,EAAE,OAAO,CAAC,MAAM;oBACvB,MAAM,EAAE,GAAG,CAAC,MAAM;iBACnB;aACF,CAAC,CAAA;YACF,MAAK;QACP,CAAC;IACH,CAAC;IAGD,MAAM,oBAAoB,GAAG;QAC3B,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,OAAO;QACP,kBAAkB;QAClB,MAAM;QACN,UAAU;QACV,SAAS;KACV,CAAA;IAED,KAAK,MAAM,OAAO,IAAI,oBAAoB,EAAE,CAAC;QAC3C,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5B,gBAAgB,CAAC;gBACf,IAAI,EAAE,oBAAoB;gBAC1B,QAAQ,EAAE,QAAQ;gBAClB,EAAE;gBACF,SAAS;gBACT,MAAM;gBACN,OAAO,EAAE;oBACP,MAAM,EAAE,uBAAuB;oBAC/B,SAAS;oBACT,GAAG,EAAE,GAAG,CAAC,GAAG;iBACb;aACF,CAAC,CAAA;YACF,MAAK;QACP,CAAC;IACH,CAAC;IAGD,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAC5C,GAAG,CAAC,EAAE,KAAK,EAAE;QACb,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CACzC,CAAC,MAAM,CAAA;IAER,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;QACtB,gBAAgB,CAAC;YACf,IAAI,EAAE,qBAAqB;YAC3B,QAAQ,EAAE,QAAQ;YAClB,EAAE;YACF,SAAS;YACT,MAAM;YACN,OAAO,EAAE;gBACP,YAAY;gBACZ,UAAU,EAAE,UAAU;aACvB;SACF,CAAC,CAAA;IACJ,CAAC;AACH,CAAC;AAED,SAAgB,+BAA+B;IAC7C,OAAO,SAAS,WAAW,CAAC,QAAsB,EAAE,GAAgB;QAClE,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC1B,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAA;QACjF,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAA;QACpC,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,aAAa,EAAE,CAAA;QAEhE,MAAM,QAAQ,GAAa;YACzB,EAAE,EAAE,KAAK;YACT,SAAS,EAAE,SAAS;YACpB,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ;YAC/B,KAAK,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAClE,SAAS,EAAE,YAAY,CAAC,GAAG,CAAC;YAC5B,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;YACpB,GAAG,eAAe,CAAC,GAAG,CAAC;YACvB,QAAQ;YACR,UAAU,EAAE,QAAQ,CAAC,MAAM;YAC3B,YAAY,EAAE,eAAe,CAAC,QAAQ,CAAC;YACvC,WAAW,EAAE,cAAc,CAAC,GAAG,CAAC;SACjC,CAAA;QAGD,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;YAC3B,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAA;QACtC,CAAC;QAED,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAG1B,oBAAoB,CAAC;YACnB,QAAQ,EAAE,QAAQ,CAAC,IAAI;YACvB,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,YAAY,EAAE,QAAQ;YACtB,SAAS,EAAE,OAAO;YAClB,UAAU,EAAE,QAAQ,CAAC,MAAM;YAC3B,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC,CAAA;QAGF,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,CAAA;QAC3C,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,GAAG,QAAQ,IAAI,CAAC,CAAA;QAExD,OAAO,QAAQ,CAAA;IACjB,CAAC,CAAA;AACH,CAAC;AAGD,SAAgB,eAAe,CAAC,aAAqB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IAOtE,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAA;IACtC,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,CAAA;IAEpE,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAA;IACvC,MAAM,mBAAmB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,aAAa,IAAI,CAAC,CAAA;IACvG,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,MAAM,CAAA;IACzE,MAAM,SAAS,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAG5E,MAAM,cAAc,GAA2B,EAAE,CAAA;IACjD,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACvB,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAA;QACvC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;IACtD,CAAC,CAAC,CAAA;IAEF,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC;SAChD,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;SACjD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;SACjC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAGf,MAAM,WAAW,GAA2B,EAAE,CAAA;IAC9C,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACvB,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAA;QACtC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;IAClD,CAAC,CAAC,CAAA;IAEF,OAAO;QACL,aAAa;QACb,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;QACpD,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG;QAC5C,YAAY;QACZ,WAAW;KACZ,CAAA;AACH,CAAC;AAED,SAAgB,iBAAiB,CAAC,aAAqB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IACxE,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAA;IACtC,OAAO,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,CAAA;AACjE,CAAC;AAED,SAAgB,sBAAsB,CAAC,aAAqB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IAI7E,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAA;IACtC,MAAM,aAAa,GAAG,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,CAAA;IAGpF,MAAM,mBAAmB,GAAqD,EAAE,CAAA;IAEhF,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC7B,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAA;QACjD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;QACnD,CAAC;QACD,mBAAmB,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,YAAY,CAAA;QACrD,mBAAmB,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAA;IACrC,CAAC,CAAC,CAAA;IAEF,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC;SACzD,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3B,QAAQ;QACR,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC1D,KAAK,EAAE,KAAK,CAAC,KAAK;KACnB,CAAC,CAAC;SACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,mBAAmB,GAAG,CAAC,CAAC,mBAAmB,CAAC;SAC7D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAGf,MAAM,aAAa,GAAqD,EAAE,CAAA;IAC1E,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;QAC/E,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;QAC9C,CAAC;QACD,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,YAAY,CAAA;QAChD,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAA;IAChC,CAAC,CAAC,CAAA;IAEF,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;SACnD,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5B,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC;QAC9B,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;KAC3D,CAAC,CAAC;SACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAA;IAE5C,OAAO;QACL,gBAAgB;QAChB,gBAAgB;KACjB,CAAA;AACH,CAAC;AAGY,QAAA,aAAa,GAAG,uBAAuB,EAAE,CAAA;AACzC,QAAA,cAAc,GAAG,+BAA+B,EAAE,CAAA"}