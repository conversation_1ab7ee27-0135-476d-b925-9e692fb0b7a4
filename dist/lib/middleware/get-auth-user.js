"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAuthUser = getAuthUser;
exports.hasRole = hasRole;
exports.isAdmin = isAdmin;
function getAuthUser(req) {
    const userId = req.headers.get('x-user-id');
    const userRole = req.headers.get('x-user-role');
    const userEmail = req.headers.get('x-user-email');
    const username = req.headers.get('x-user-username');
    if (!userId || !userRole) {
        return null;
    }
    return {
        id: userId,
        role: userRole,
        email: userEmail || undefined,
        username: username || undefined
    };
}
function hasRole(user, requiredRoles) {
    if (!user)
        return false;
    return requiredRoles.includes(user.role);
}
function isAdmin(user) {
    return hasRole(user, ['superuser', 'dj']);
}
//# sourceMappingURL=get-auth-user.js.map