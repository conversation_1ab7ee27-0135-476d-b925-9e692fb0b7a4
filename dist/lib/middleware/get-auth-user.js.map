{"version": 3, "file": "get-auth-user.js", "sourceRoot": "", "sources": ["../../../lib/middleware/get-auth-user.ts"], "names": [], "mappings": ";;AAaA,kCAgBC;AAKD,0BAGC;AAKD,0BAEC;AA/BD,SAAgB,WAAW,CAAC,GAAgB;IAC1C,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;IAC/C,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;IACjD,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;IAEnD,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,OAAO;QACL,EAAE,EAAE,MAAM;QACV,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,SAAS,IAAI,SAAS;QAC7B,QAAQ,EAAE,QAAQ,IAAI,SAAS;KAChC,CAAA;AACH,CAAC;AAKD,SAAgB,OAAO,CAAC,IAAqB,EAAE,aAAuB;IACpE,IAAI,CAAC,IAAI;QAAE,OAAO,KAAK,CAAA;IACvB,OAAO,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AAC1C,CAAC;AAKD,SAAgB,OAAO,CAAC,IAAqB;IAC3C,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAA;AAC3C,CAAC"}