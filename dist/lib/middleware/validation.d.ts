import { NextRequest, NextResponse } from 'next/server';
export interface ValidationRule {
    field: string;
    type: 'string' | 'number' | 'boolean' | 'email' | 'url' | 'uuid' | 'array' | 'object';
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    allowedValues?: any[];
    sanitize?: boolean;
    customValidator?: (value: any) => boolean | string;
}
export interface ValidationSchema {
    body?: ValidationRule[];
    query?: ValidationRule[];
    params?: ValidationRule[];
}
export declare class ValidationError extends Error {
    field: string;
    code: string;
    constructor(field: string, message: string, code?: string);
}
export declare const VALIDATION_PATTERNS: {
    email: RegExp;
    uuid: RegExp;
    alphanumeric: RegExp;
    slug: RegExp;
    hexColor: RegExp;
    base64: RegExp;
    mongoId: RegExp;
    filename: RegExp;
};
export declare const sanitizers: {
    html: (input: string) => string;
    plainText: (input: string) => string;
    alphanumeric: (input: string) => string;
    slug: (input: string) => string;
    filename: (input: string) => string;
    numeric: (input: string) => string;
};
export declare function validateValue(value: any, rule: ValidationRule): any;
export declare function validateObject(data: any, rules: ValidationRule[]): any;
export declare function createValidationMiddleware(schema: ValidationSchema): (req: NextRequest) => Promise<NextResponse | null>;
export declare const commonSchemas: {
    userRegistration: {
        body: ({
            field: string;
            type: "email";
            required: boolean;
            maxLength: number;
            minLength?: undefined;
            sanitize?: undefined;
        } | {
            field: string;
            type: "string";
            required: boolean;
            minLength: number;
            maxLength: number;
            sanitize?: undefined;
        } | {
            field: string;
            type: "string";
            required: boolean;
            minLength: number;
            maxLength: number;
            sanitize: boolean;
        } | {
            field: string;
            type: "string";
            required: boolean;
            maxLength: number;
            sanitize: boolean;
            minLength?: undefined;
        })[];
    };
    userLogin: {
        body: ({
            field: string;
            type: "email";
            required: boolean;
        } | {
            field: string;
            type: "string";
            required: boolean;
        })[];
    };
    quizSettings: {
        body: ({
            field: string;
            type: "number";
            required: boolean;
            min: number;
            max: number;
            maxLength?: undefined;
        } | {
            field: string;
            type: "string";
            required: boolean;
            maxLength: number;
            min?: undefined;
            max?: undefined;
        } | {
            field: string;
            type: "boolean";
            required: boolean;
            min?: undefined;
            max?: undefined;
            maxLength?: undefined;
        })[];
    };
    playlistCreation: {
        body: ({
            field: string;
            type: "string";
            required: boolean;
            minLength: number;
            maxLength: number;
            sanitize: boolean;
        } | {
            field: string;
            type: "string";
            required: boolean;
            maxLength: number;
            sanitize: boolean;
            minLength?: undefined;
        } | {
            field: string;
            type: "boolean";
            required: boolean;
            minLength?: undefined;
            maxLength?: undefined;
            sanitize?: undefined;
        })[];
    };
};
export declare const sanitizeHtmlMiddleware: (req: NextRequest) => Promise<NextResponse | null>;
