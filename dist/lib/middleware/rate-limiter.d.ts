import { NextRequest, NextResponse } from 'next/server';
interface RateLimitConfig {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
    keyGenerator?: (req: NextRequest) => string;
}
export declare function createRateLimiter(config: RateLimitConfig): (req: NextRequest) => Promise<NextResponse | null>;
export declare const apiRateLimiter: (req: NextRequest) => Promise<NextResponse | null>;
export declare const authRateLimiter: (req: NextRequest) => Promise<NextResponse | null>;
export declare const quizRateLimiter: (req: NextRequest) => Promise<NextResponse | null>;
export declare const uploadRateLimiter: (req: NextRequest) => Promise<NextResponse | null>;
export declare const passwordResetRateLimit: (req: NextRequest) => Promise<NextResponse | null>;
export declare function createAdaptiveRateLimiter(anonymousConfig: RateLimitConfig, authenticatedConfig: RateLimitConfig): (req: NextRequest) => Promise<NextResponse | null>;
export declare const adaptiveApiLimiter: (req: NextRequest) => Promise<NextResponse | null>;
export {};
