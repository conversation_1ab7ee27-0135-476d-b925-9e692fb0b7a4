import { NextRequest, NextResponse } from 'next/server';
export interface AuthUser {
    id: string;
    email: string;
    username: string;
    displayName?: string;
    role: 'user' | 'dj' | 'superuser';
    isGuest?: boolean;
}
interface AuthConfig {
    requireAuth?: boolean;
    allowedRoles?: ('user' | 'dj' | 'superuser')[];
    allowGuests?: boolean;
}
export declare function getAuthUser(): Promise<AuthUser | null>;
export declare function createAuthMiddleware(config?: AuthConfig): (req: NextRequest) => Promise<NextResponse | null>;
export declare const requireAuth: (req: NextRequest) => Promise<NextResponse | null>;
export declare const requireDJ: (req: NextRequest) => Promise<NextResponse | null>;
export declare const requireSuperuser: (req: NextRequest) => Promise<NextResponse | null>;
export declare const optionalAuth: (req: NextRequest) => Promise<NextResponse | null>;
export declare const guestAllowed: (req: NextRequest) => Promise<NextResponse | null>;
export declare function getUserFromRequest(req: NextRequest): Partial<AuthUser> | null;
export {};
