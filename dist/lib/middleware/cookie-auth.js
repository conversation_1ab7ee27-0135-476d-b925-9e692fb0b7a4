"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateRequest = authenticateRequest;
exports.hasRole = hasRole;
const auth_cookies_1 = require("@/lib/auth-cookies");
const auth_service_1 = require("@/lib/auth-service");
async function authenticateRequest(req) {
    const authCookie = req.cookies.get('auth_token');
    const authHeader = req.headers.get('authorization')?.replace('Bearer ', '');
    if (!authCookie && !authHeader) {
        return null;
    }
    if (authCookie) {
        try {
            const decoded = await auth_cookies_1.CookieAuth.getAuthFromCookie();
            if (!decoded) {
                return null;
            }
            return {
                id: decoded.id,
                email: decoded.email,
                username: decoded.username,
                role: decoded.role
            };
        }
        catch (error) {
            console.error('<PERSON>ie auth error:', error);
        }
    }
    if (authHeader) {
        try {
            const decoded = await auth_service_1.AuthService.verifyToken(authHeader);
            if (!decoded) {
                return null;
            }
            return {
                id: decoded.id,
                email: decoded.email,
                username: decoded.username,
                role: decoded.role
            };
        }
        catch (error) {
            console.error('Bearer token auth error:', error);
        }
    }
    return null;
}
function hasRole(user, requiredRoles) {
    return requiredRoles.includes(user.role);
}
//# sourceMappingURL=cookie-auth.js.map