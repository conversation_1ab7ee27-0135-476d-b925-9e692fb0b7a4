{"version": 3, "file": "cors.js", "sourceRoot": "", "sources": ["../../../lib/middleware/cors.ts"], "names": [], "mappings": ";;;AAsJA,oDA4CC;AAED,0EAOC;AAED,wCAsBC;AAED,gDA8FC;AAGD,wDAeC;AAhVD,wCAAuD;AAmCvD,SAAS,iBAAiB;IAExB,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAChG,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAGtG,OAAO,CAAC,MAAc,EAAE,EAAE;QAExB,MAAM,eAAe,GAAG;YACtB,4BAA4B;YAC5B,+BAA+B;YAC/B,qFAAqF;YACrF,oFAAoF;SACrF,CAAC;QAGF,IAAI,kBAAkB,EAAE,CAAC;YACvB,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAEnC,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;gBACpD,eAAe,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,iBAAiB,cAAc,mBAAmB,CAAC,CAAC,CAAA;YACtF,CAAC,CAAC,CAAA;QACJ,CAAC;QAGD,IAAI,aAAa,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpD,OAAO,IAAI,CAAA;QACb,CAAC;QAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,MAAM,iBAAiB,GAAG,CAAC,wBAAwB,EAAE,4BAA4B,CAAC,CAAA;YAClF,IAAI,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/D,CAAC,CAAA;AACH,CAAC;AAGD,MAAM,mBAAmB,GAAe;IACtC,MAAM,EAAE,iBAAiB,EAAE;IAC3B,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;IAC7D,cAAc,EAAE;QACd,QAAQ;QACR,gBAAgB;QAChB,eAAe;QACf,gBAAgB;QAChB,aAAa;QACb,cAAc;QACd,MAAM;QACN,eAAe;QACf,kBAAkB;QAClB,cAAc;QACd,cAAc;KACf;IACD,cAAc,EAAE;QACd,mBAAmB;QACnB,uBAAuB;QACvB,mBAAmB;QACnB,eAAe;QACf,iBAAiB;KAClB;IACD,WAAW,EAAE,IAAI;IACjB,MAAM,EAAE,KAAK;IACb,oBAAoB,EAAE,GAAG;CAC1B,CAAA;AAGD,MAAM,wBAAwB,GAA0B;IACtD,qBAAqB,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;QAC1D,CAAC,CAAC,yTAAyT;QAC3T,CAAC,CAAC,KAAK;IACT,yBAAyB,EAAE,gBAAgB;IAC3C,uBAAuB,EAAE,aAAa;IACtC,yBAAyB,EAAE,WAAW;IACtC,kBAAkB,EAAE,IAAI;IACxB,YAAY,EAAE,MAAM;IACpB,aAAa,EAAE,IAAI;IACnB,IAAI,EAAE;QACJ,MAAM,EAAE,QAAQ;QAChB,iBAAiB,EAAE,IAAI;QACvB,OAAO,EAAE,IAAI;KACd;IACD,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,IAAI;IACb,kBAAkB,EAAE,IAAI;IACxB,4BAA4B,EAAE,MAAM;IACpC,cAAc,EAAE,iCAAiC;IACjD,SAAS,EAAE,IAAI;CAChB,CAAA;AAED,SAAS,eAAe,CAAC,MAAc,EAAE,cAAiE;IACxG,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE,CAAC;QACzC,OAAO,cAAc,CAAC,MAAM,CAAC,CAAA;IAC/B,CAAC;IAED,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;QACvC,OAAO,MAAM,KAAK,cAAc,IAAI,cAAc,KAAK,GAAG,CAAA;IAC5D,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;QAClC,OAAO,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;IACxE,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAgB,oBAAoB,CAAC,SAAqB,EAAE;IAC1D,MAAM,UAAU,GAAG,EAAE,GAAG,mBAAmB,EAAE,GAAG,MAAM,EAAE,CAAA;IAExD,OAAO,KAAK,UAAU,cAAc,CAAC,GAAgB;QACnD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACxC,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;QAGzB,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,IAAI,qBAAY,CAAC,IAAI,EAAE;gBACtC,MAAM,EAAE,UAAU,CAAC,oBAAoB,IAAI,GAAG;aAC/C,CAAC,CAAA;YAGF,IAAI,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBAChC,IAAI,eAAe,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC/C,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAA;gBAC7D,CAAC;YACH,CAAC;iBAAM,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC3D,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAA;YAC1D,CAAC;YAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;gBAC3B,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAA;YAClE,CAAC;YAED,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACvB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YACrF,CAAC;YAED,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;gBAC9B,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YAC5F,CAAC;YAED,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBACtB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAA;YAC9E,CAAC;YAED,OAAO,QAAQ,CAAA;QACjB,CAAC;QAGD,OAAO,IAAI,CAAA;IACb,CAAC,CAAA;AACH,CAAC;AAED,SAAgB,+BAA+B,CAAC,SAAgC,EAAE;IAChF,MAAM,cAAc,GAAG,EAAE,GAAG,wBAAwB,EAAE,GAAG,MAAM,EAAE,CAAA;IAEjE,OAAO,KAAK,UAAU,yBAAyB,CAAC,GAAgB;QAE9D,OAAO,IAAI,CAAA;IACb,CAAC,CAAA;AACH,CAAC;AAED,SAAgB,cAAc,CAAC,QAAsB,EAAE,GAAgB,EAAE,SAAqB,EAAE;IAC9F,MAAM,UAAU,GAAG,EAAE,GAAG,mBAAmB,EAAE,GAAG,MAAM,EAAE,CAAA;IACxD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAGxC,IAAI,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;QAChC,IAAI,eAAe,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/C,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAA;QAC7D,CAAC;IACH,CAAC;SAAM,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;QAC3D,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAA;IAC1D,CAAC;IAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAA;IAClE,CAAC;IAED,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;QAC9B,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7F,CAAC;IAED,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,SAAgB,kBAAkB,CAAC,QAAsB,EAAE,SAAgC,EAAE;IAC3F,MAAM,cAAc,GAAG,EAAE,GAAG,wBAAwB,EAAE,GAAG,MAAM,EAAE,CAAA;IAGjE,IAAI,cAAc,CAAC,qBAAqB,EAAE,CAAC;QACzC,IAAI,OAAO,cAAc,CAAC,qBAAqB,KAAK,QAAQ,EAAE,CAAC;YAC7D,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,cAAc,CAAC,qBAAqB,CAAC,CAAA;QACvF,CAAC;IACH,CAAC;IAGD,IAAI,cAAc,CAAC,yBAAyB,EAAE,CAAC;QAC7C,IAAI,OAAO,cAAc,CAAC,yBAAyB,KAAK,QAAQ,EAAE,CAAC;YACjE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,cAAc,CAAC,yBAAyB,CAAC,CAAA;QAChG,CAAC;IACH,CAAC;IAGD,IAAI,cAAc,CAAC,uBAAuB,EAAE,CAAC;QAC3C,IAAI,OAAO,cAAc,CAAC,uBAAuB,KAAK,QAAQ,EAAE,CAAC;YAC/D,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,cAAc,CAAC,uBAAuB,CAAC,CAAA;QAC5F,CAAC;IACH,CAAC;IAGD,IAAI,cAAc,CAAC,yBAAyB,EAAE,CAAC;QAC7C,IAAI,OAAO,cAAc,CAAC,yBAAyB,KAAK,QAAQ,EAAE,CAAC;YACjE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,cAAc,CAAC,yBAAyB,CAAC,CAAA;QAChG,CAAC;IACH,CAAC;IAGD,IAAI,cAAc,CAAC,kBAAkB,EAAE,CAAC;QACtC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;IACvD,CAAC;IAGD,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;QAChC,IAAI,OAAO,cAAc,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;YACpD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,cAAc,CAAC,YAAY,CAAC,CAAA;QACtE,CAAC;IACH,CAAC;IAGD,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;QACjC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAA;IACzC,CAAC;IAGD,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;QACxB,IAAI,OAAO,cAAc,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC5C,MAAM,EAAE,MAAM,GAAG,QAAQ,EAAE,iBAAiB,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,cAAc,CAAC,IAAI,CAAA;YAC5F,IAAI,SAAS,GAAG,WAAW,MAAM,EAAE,CAAA;YACnC,IAAI,iBAAiB;gBAAE,SAAS,IAAI,qBAAqB,CAAA;YACzD,IAAI,OAAO;gBAAE,SAAS,IAAI,WAAW,CAAA;YACrC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,SAAS,CAAC,CAAA;QAC9D,CAAC;IACH,CAAC;IAGD,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC;QAC5B,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAA;IACtD,CAAC;IAGD,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;QAC3B,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAA;IAC3D,CAAC;IAGD,IAAI,cAAc,CAAC,kBAAkB,EAAE,CAAC;QACtC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAA;IACpD,CAAC;IAGD,IAAI,cAAc,CAAC,4BAA4B,EAAE,CAAC;QAChD,IAAI,OAAO,cAAc,CAAC,4BAA4B,KAAK,QAAQ,EAAE,CAAC;YACpE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,cAAc,CAAC,4BAA4B,CAAC,CAAA;QACxG,CAAC;IACH,CAAC;IAGD,IAAI,cAAc,CAAC,cAAc,EAAE,CAAC;QAClC,IAAI,OAAO,cAAc,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;YACtD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,cAAc,CAAC,cAAc,CAAC,CAAA;QACxE,CAAC;IACH,CAAC;IAGD,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC;QAC7B,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAA;IAC3D,CAAC;IAED,OAAO,QAAQ,CAAA;AACjB,CAAC;AAGD,SAAgB,sBAAsB,CAAC,aAAyB,EAAE,EAAE,iBAAwC,EAAE;IAC5G,MAAM,cAAc,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAA;IACvD,MAAM,kBAAkB,GAAG,+BAA+B,CAAC,cAAc,CAAC,CAAA;IAE1E,OAAO,KAAK,UAAU,gBAAgB,CAAC,GAAgB;QAErD,MAAM,YAAY,GAAG,MAAM,cAAc,CAAC,GAAG,CAAC,CAAA;QAC9C,IAAI,YAAY,EAAE,CAAC;YAEjB,OAAO,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAA;QACzD,CAAC;QAGD,OAAO,IAAI,CAAA;IACb,CAAC,CAAA;AACH,CAAC;AAGY,QAAA,eAAe,GAAG,sBAAsB,CACnD;IACE,MAAM,EAAE,CAAC,MAAc,EAAE,EAAE;QAEzB,MAAM,eAAe,GAAG;YACtB,0BAA0B;YAC1B,6BAA6B;YAC7B,sCAAsC;SACvC,CAAC;QACF,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/D,CAAC;IACD,WAAW,EAAE,IAAI;CAClB,EACD;IACE,qBAAqB,EAAE,KAAK;IAC5B,IAAI,EAAE,KAAK;CACZ,CACF,CAAA;AAEY,QAAA,cAAc,GAAG,sBAAsB,CAClD;IACE,MAAM,EAAE,iBAAiB,EAAE;IAC3B,WAAW,EAAE,IAAI;CAClB,EACD,wBAAwB,CACzB,CAAA;AAGY,QAAA,OAAO,GAAG,oBAAoB,CAAC;IAC1C,MAAM,EAAE,iBAAiB,EAAE;IAC3B,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;IAClD,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,kBAAkB,CAAC;IACrE,WAAW,EAAE,IAAI;CAClB,CAAC,CAAA;AAEW,QAAA,aAAa,GAAG,oBAAoB,CAAC;IAChD,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;IACxB,cAAc,EAAE,CAAC,cAAc,EAAE,WAAW,CAAC;IAC7C,WAAW,EAAE,KAAK;CACnB,CAAC,CAAA"}