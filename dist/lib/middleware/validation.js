"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sanitizeHtmlMiddleware = exports.commonSchemas = exports.sanitizers = exports.VALIDATION_PATTERNS = exports.ValidationError = void 0;
exports.validateValue = validateValue;
exports.validateObject = validateObject;
exports.createValidationMiddleware = createValidationMiddleware;
const server_1 = require("next/server");
const isomorphic_dompurify_1 = __importDefault(require("isomorphic-dompurify"));
class ValidationError extends Error {
    constructor(field, message, code = 'VALIDATION_ERROR') {
        super(message);
        this.field = field;
        this.code = code;
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
exports.VALIDATION_PATTERNS = {
    email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    alphanumeric: /^[a-zA-Z0-9]+$/,
    slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
    hexColor: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
    base64: /^[A-Za-z0-9+/]*={0,2}$/,
    mongoId: /^[0-9a-fA-F]{24}$/,
    filename: /^[a-zA-Z0-9._-]+$/
};
exports.sanitizers = {
    html: (input) => isomorphic_dompurify_1.default.sanitize(input),
    plainText: (input) => {
        return input
            .replace(/[<>]/g, '')
            .replace(/[&]/g, '&amp;')
            .replace(/['"]/g, '')
            .trim();
    },
    alphanumeric: (input) => {
        return input.replace(/[^a-zA-Z0-9]/g, '');
    },
    slug: (input) => {
        return input
            .toLowerCase()
            .replace(/[^a-z0-9-]/g, '')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    },
    filename: (input) => {
        return input
            .replace(/[^a-zA-Z0-9._-]/g, '')
            .replace(/\.{2,}/g, '.')
            .substring(0, 255);
    },
    numeric: (input) => {
        return input.replace(/[^0-9.-]/g, '');
    }
};
function validateValue(value, rule) {
    const { field, type, required, minLength, maxLength, min, max, pattern, allowedValues, sanitize, customValidator } = rule;
    if (required && (value === undefined || value === null || value === '')) {
        throw new ValidationError(field, `${field} is required`, 'REQUIRED');
    }
    if (value === undefined || value === null) {
        return value;
    }
    let validatedValue = value;
    switch (type) {
        case 'string':
            validatedValue = String(value);
            if (sanitize) {
                validatedValue = exports.sanitizers.plainText(validatedValue);
            }
            if (minLength !== undefined && validatedValue.length < minLength) {
                throw new ValidationError(field, `${field} must be at least ${minLength} characters`, 'MIN_LENGTH');
            }
            if (maxLength !== undefined && validatedValue.length > maxLength) {
                throw new ValidationError(field, `${field} must be at most ${maxLength} characters`, 'MAX_LENGTH');
            }
            break;
        case 'number':
            validatedValue = Number(value);
            if (isNaN(validatedValue)) {
                throw new ValidationError(field, `${field} must be a valid number`, 'INVALID_NUMBER');
            }
            if (min !== undefined && validatedValue < min) {
                throw new ValidationError(field, `${field} must be at least ${min}`, 'MIN_VALUE');
            }
            if (max !== undefined && validatedValue > max) {
                throw new ValidationError(field, `${field} must be at most ${max}`, 'MAX_VALUE');
            }
            break;
        case 'boolean':
            if (typeof value === 'boolean') {
                validatedValue = value;
            }
            else if (typeof value === 'string') {
                validatedValue = value.toLowerCase() === 'true';
            }
            else {
                validatedValue = Boolean(value);
            }
            break;
        case 'email':
            validatedValue = String(value).toLowerCase().trim();
            if (!exports.VALIDATION_PATTERNS.email.test(validatedValue)) {
                throw new ValidationError(field, `${field} must be a valid email`, 'INVALID_EMAIL');
            }
            break;
        case 'url':
            try {
                validatedValue = new URL(String(value)).toString();
            }
            catch {
                throw new ValidationError(field, `${field} must be a valid URL`, 'INVALID_URL');
            }
            break;
        case 'uuid':
            validatedValue = String(value);
            if (!exports.VALIDATION_PATTERNS.uuid.test(validatedValue)) {
                throw new ValidationError(field, `${field} must be a valid UUID`, 'INVALID_UUID');
            }
            break;
        case 'array':
            if (!Array.isArray(value)) {
                throw new ValidationError(field, `${field} must be an array`, 'INVALID_ARRAY');
            }
            validatedValue = value;
            if (minLength !== undefined && validatedValue.length < minLength) {
                throw new ValidationError(field, `${field} must have at least ${minLength} items`, 'MIN_ITEMS');
            }
            if (maxLength !== undefined && validatedValue.length > maxLength) {
                throw new ValidationError(field, `${field} must have at most ${maxLength} items`, 'MAX_ITEMS');
            }
            break;
        case 'object':
            if (typeof value !== 'object' || Array.isArray(value)) {
                throw new ValidationError(field, `${field} must be an object`, 'INVALID_OBJECT');
            }
            validatedValue = value;
            break;
    }
    if (pattern && typeof validatedValue === 'string' && !pattern.test(validatedValue)) {
        throw new ValidationError(field, `${field} format is invalid`, 'INVALID_FORMAT');
    }
    if (allowedValues && !allowedValues.includes(validatedValue)) {
        throw new ValidationError(field, `${field} must be one of: ${allowedValues.join(', ')}`, 'INVALID_VALUE');
    }
    if (customValidator) {
        const customResult = customValidator(validatedValue);
        if (customResult !== true) {
            const message = typeof customResult === 'string' ? customResult : `${field} is invalid`;
            throw new ValidationError(field, message, 'CUSTOM_VALIDATION');
        }
    }
    return validatedValue;
}
function validateObject(data, rules) {
    const validated = {};
    const errors = [];
    for (const rule of rules) {
        try {
            const value = data[rule.field];
            validated[rule.field] = validateValue(value, rule);
        }
        catch (error) {
            if (error instanceof ValidationError) {
                errors.push(error);
            }
            else {
                errors.push(new ValidationError(rule.field, `Validation failed for ${rule.field}`, 'VALIDATION_ERROR'));
            }
        }
    }
    if (errors.length > 0) {
        const error = new Error('Validation failed');
        error.validationErrors = errors;
        throw error;
    }
    return validated;
}
function createValidationMiddleware(schema) {
    return async function validationMiddleware(req) {
        const errors = [];
        try {
            let body = null;
            if (req.method !== 'GET' && req.method !== 'HEAD') {
                const contentType = req.headers.get('content-type') || '';
                if (contentType.includes('application/json')) {
                    try {
                        body = await req.json();
                    }
                    catch {
                        return new server_1.NextResponse(JSON.stringify({
                            error: 'Invalid JSON',
                            message: 'Request body must be valid JSON',
                            code: 'INVALID_JSON'
                        }), {
                            status: 400,
                            headers: { 'Content-Type': 'application/json' }
                        });
                    }
                }
                else if (contentType.includes('application/x-www-form-urlencoded')) {
                    const formData = await req.formData();
                    body = Object.fromEntries(formData.entries());
                }
            }
            if (schema.body && body) {
                try {
                    validateObject(body, schema.body);
                }
                catch (error) {
                    if (error.validationErrors) {
                        errors.push(...error.validationErrors);
                    }
                }
            }
            if (schema.query) {
                const query = Object.fromEntries(new URL(req.url).searchParams.entries());
                try {
                    validateObject(query, schema.query);
                }
                catch (error) {
                    if (error.validationErrors) {
                        errors.push(...error.validationErrors);
                    }
                }
            }
            if (schema.params) {
            }
            if (errors.length > 0) {
                return new server_1.NextResponse(JSON.stringify({
                    error: 'Validation Failed',
                    message: 'One or more fields failed validation',
                    code: 'VALIDATION_FAILED',
                    details: errors.map(e => ({
                        field: e.field,
                        message: e.message,
                        code: e.code
                    }))
                }), {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                });
            }
            return null;
        }
        catch (error) {
            console.error('Validation middleware error:', error);
            return new server_1.NextResponse(JSON.stringify({
                error: 'Internal Server Error',
                message: 'Validation processing failed',
                code: 'VALIDATION_PROCESSING_ERROR'
            }), {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            });
        }
    };
}
exports.commonSchemas = {
    userRegistration: {
        body: [
            { field: 'email', type: 'email', required: true, maxLength: 255 },
            { field: 'password', type: 'string', required: true, minLength: 8, maxLength: 128 },
            { field: 'name', type: 'string', required: true, minLength: 1, maxLength: 100, sanitize: true },
            { field: 'displayName', type: 'string', required: false, maxLength: 50, sanitize: true }
        ]
    },
    userLogin: {
        body: [
            { field: 'email', type: 'email', required: true },
            { field: 'password', type: 'string', required: true }
        ]
    },
    quizSettings: {
        body: [
            { field: 'totalQuestions', type: 'number', required: true, min: 1, max: 50 },
            { field: 'difficultyLevel', type: 'number', required: true, min: 1, max: 5 },
            { field: 'timeLimit', type: 'number', required: true, min: 10, max: 300 },
            { field: 'genre', type: 'string', required: false, maxLength: 50 },
            { field: 'enableHints', type: 'boolean', required: false }
        ]
    },
    playlistCreation: {
        body: [
            { field: 'name', type: 'string', required: true, minLength: 1, maxLength: 100, sanitize: true },
            { field: 'description', type: 'string', required: false, maxLength: 500, sanitize: true },
            { field: 'isPublic', type: 'boolean', required: false }
        ]
    }
};
exports.sanitizeHtmlMiddleware = createValidationMiddleware({
    body: [
        { field: 'content', type: 'string', sanitize: true, customValidator: (value) => {
                return isomorphic_dompurify_1.default.sanitize(value) === value || 'Content contains potentially harmful HTML';
            } }
    ]
});
//# sourceMappingURL=validation.js.map