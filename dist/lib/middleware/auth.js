"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.guestAllowed = exports.optionalAuth = exports.requireSuperuser = exports.requireDJ = exports.requireAuth = void 0;
exports.getAuthUser = getAuthUser;
exports.createAuthMiddleware = createAuthMiddleware;
exports.getUserFromRequest = getUserFromRequest;
const server_1 = require("next/server");
const auth_cookies_1 = require("@/lib/auth-cookies");
const auth_service_1 = require("@/lib/auth-service");
if (typeof window === 'undefined' && !process.env.JWT_SECRET) {
    console.error('Warning: JWT_SECRET environment variable is not set');
}
async function getAuthUser() {
    try {
        const tokenPayload = await auth_cookies_1.CookieAuth.getAuthFromCookie();
        if (!tokenPayload)
            return null;
        const user = await auth_service_1.AuthService.refreshUserData(tokenPayload.id);
        if (!user)
            return null;
        return {
            id: user.id,
            email: user.email,
            username: user.username,
            displayName: user.displayName,
            role: user.role,
            isGuest: false
        };
    }
    catch (error) {
        console.error('Failed to get auth user:', error);
        return null;
    }
}
function createAuthMiddleware(config = {}) {
    const { requireAuth = true, allowedRoles = ['user', 'dj', 'superuser'], allowGuests = false } = config;
    return async function authMiddleware(req) {
        const user = await getAuthUser();
        if (requireAuth && !user) {
            return new server_1.NextResponse(JSON.stringify({
                error: 'Unauthorized',
                message: 'Authentication required',
                code: 'AUTH_REQUIRED'
            }), {
                status: 401,
                headers: {
                    'Content-Type': 'application/json',
                    'WWW-Authenticate': 'Bearer'
                }
            });
        }
        if (user?.isGuest && !allowGuests) {
            return new server_1.NextResponse(JSON.stringify({
                error: 'Forbidden',
                message: 'Guest access not allowed for this resource',
                code: 'GUEST_FORBIDDEN'
            }), {
                status: 403,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        if (user && !allowedRoles.includes(user.role)) {
            return new server_1.NextResponse(JSON.stringify({
                error: 'Forbidden',
                message: 'Insufficient permissions',
                code: 'INSUFFICIENT_PERMISSIONS',
                requiredRoles: allowedRoles,
                userRole: user.role
            }), {
                status: 403,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        if (user) {
            const headers = new Headers(req.headers);
            headers.set('x-user-id', user.id);
            headers.set('x-user-role', user.role);
            headers.set('x-user-email', user.email);
            headers.set('x-user-username', user.username);
            return null;
        }
        return null;
    };
}
exports.requireAuth = createAuthMiddleware({
    requireAuth: true,
    allowedRoles: ['user', 'dj', 'superuser'],
    allowGuests: false
});
exports.requireDJ = createAuthMiddleware({
    requireAuth: true,
    allowedRoles: ['dj', 'superuser'],
    allowGuests: false
});
exports.requireSuperuser = createAuthMiddleware({
    requireAuth: true,
    allowedRoles: ['superuser'],
    allowGuests: false
});
exports.optionalAuth = createAuthMiddleware({
    requireAuth: false,
    allowedRoles: ['user', 'dj', 'superuser'],
    allowGuests: true
});
exports.guestAllowed = createAuthMiddleware({
    requireAuth: false,
    allowedRoles: ['user', 'dj', 'superuser'],
    allowGuests: true
});
function getUserFromRequest(req) {
    const userId = req.headers.get('x-user-id');
    const userRole = req.headers.get('x-user-role');
    const userEmail = req.headers.get('x-user-email');
    const username = req.headers.get('x-user-username');
    if (!userId) {
        return null;
    }
    return {
        id: userId,
        role: userRole,
        email: userEmail || undefined,
        username: username || undefined
    };
}
//# sourceMappingURL=auth.js.map