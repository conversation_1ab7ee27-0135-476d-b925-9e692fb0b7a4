"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheMonitor = exports.CacheWarmer = void 0;
exports.withCacheMetrics = withCacheMetrics;
exports.markCacheHit = markCacheHit;
exports.addCacheHeaders = addCacheHeaders;
exports.noCacheHeaders = noCacheHeaders;
exports.getETag = getETag;
exports.checkETag = checkETag;
exports.withETag = withETag;
exports.checkRateLimit = checkRateLimit;
exports.addRateLimitHeaders = addRateLimitHeaders;
const server_1 = require("next/server");
const redis_cache_1 = require("@/lib/cache/redis-cache");
function withCacheMetrics(handler) {
    return async (request, ...args) => {
        const startTime = Date.now();
        const cacheContext = {
            startTime,
            hit: false,
            key: null,
            ttl: null
        };
        request.cacheContext = cacheContext;
        const response = await handler(request, ...args);
        if (response instanceof server_1.NextResponse) {
            const responseTime = Date.now() - startTime;
            response.headers.set('X-Cache-Mode', redis_cache_1.cache.getMode());
            response.headers.set('X-Response-Time', `${responseTime}ms`);
            if (cacheContext.hit) {
                response.headers.set('X-Cache', 'HIT');
                if (cacheContext.key) {
                    response.headers.set('X-Cache-Key', cacheContext.key);
                }
                if (cacheContext.ttl) {
                    response.headers.set('X-Cache-TTL', cacheContext.ttl.toString());
                }
            }
            else {
                response.headers.set('X-Cache', 'MISS');
            }
            if (process.env.NODE_ENV === 'development') {
                try {
                    const stats = await redis_cache_1.cache.getStats();
                    response.headers.set('X-Cache-Hit-Rate', (stats.hitRate * 100).toFixed(2) + '%');
                    response.headers.set('X-Cache-Total-Keys', stats.totalKeys.toString());
                }
                catch (error) {
                }
            }
        }
        return response;
    };
}
function markCacheHit(request, key, ttl) {
    const context = request.cacheContext;
    if (context) {
        context.hit = true;
        context.key = key;
        context.ttl = ttl;
    }
}
function addCacheHeaders(response, options = {}) {
    const { maxAge = 300, staleWhileRevalidate = 600, cacheControl } = options;
    if (cacheControl) {
        response.headers.set('Cache-Control', cacheControl);
    }
    else {
        response.headers.set('Cache-Control', `public, max-age=${maxAge}, stale-while-revalidate=${staleWhileRevalidate}`);
    }
    return response;
}
function noCacheHeaders(response) {
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    return response;
}
function getETag(data) {
    const crypto = require('crypto');
    const hash = crypto.createHash('md5');
    hash.update(JSON.stringify(data));
    return `"${hash.digest('hex')}"`;
}
function checkETag(request, etag) {
    const ifNoneMatch = request.headers.get('if-none-match');
    return ifNoneMatch === etag;
}
function withETag(response, data) {
    const etag = getETag(data);
    response.headers.set('ETag', etag);
    return response;
}
async function checkRateLimit(key, limit, windowMs) {
    const windowSeconds = Math.ceil(windowMs / 1000);
    const count = await redis_cache_1.cache.incr(key, windowSeconds);
    const resetTime = Date.now() + windowMs;
    return {
        allowed: count <= limit,
        remaining: Math.max(0, limit - count),
        resetTime,
        total: count
    };
}
function addRateLimitHeaders(response, rateLimit) {
    response.headers.set('X-RateLimit-Limit', '100');
    response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString());
    response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toString());
    response.headers.set('X-RateLimit-Used', rateLimit.total.toString());
    return response;
}
class CacheWarmer {
    static async warmLibraryCache() {
        try {
            console.log('Warming library cache...');
            const response = await fetch('/api/quiz/tracks?limit=1000', {
                method: 'GET'
            });
            if (response.ok) {
                console.log('Library cache warmed successfully');
            }
        }
        catch (error) {
            console.error('Failed to warm library cache:', error);
        }
    }
    static async warmUserCache(userId) {
        try {
            console.log(`Warming cache for user ${userId}...`);
            await Promise.all([
                fetch(`/api/jukebox/favorites?userId=${userId}`),
                fetch(`/api/jukebox/suggestions?userId=${userId}&type=intelligent`)
            ]);
            console.log(`User cache warmed for ${userId}`);
        }
        catch (error) {
            console.error(`Failed to warm user cache for ${userId}:`, error);
        }
    }
    static async warmPopularCache() {
        try {
            console.log('Warming popular content cache...');
            await Promise.all([
                fetch('/api/jukebox/suggestions?type=popular'),
                fetch('/api/jukebox/favorites-analysis')
            ]);
            console.log('Popular content cache warmed');
        }
        catch (error) {
            console.error('Failed to warm popular cache:', error);
        }
    }
}
exports.CacheWarmer = CacheWarmer;
class CacheMonitor {
    static async getHealthMetrics() {
        try {
            const stats = await redis_cache_1.cache.getStats();
            const isHealthy = redis_cache_1.cache.isAvailable();
            return {
                status: isHealthy ? 'healthy' : 'degraded',
                mode: redis_cache_1.cache.getMode(),
                metrics: {
                    hitRate: stats.hitRate,
                    totalKeys: stats.totalKeys,
                    memoryUsage: stats.memoryUsage,
                    topKeys: stats.topKeys.slice(0, 5)
                }
            };
        }
        catch (error) {
            return {
                status: 'down',
                mode: 'unknown',
                metrics: null
            };
        }
    }
    static async logCacheMetrics() {
        const health = await this.getHealthMetrics();
        console.log('Cache Health:', health);
    }
}
exports.CacheMonitor = CacheMonitor;
//# sourceMappingURL=cache-middleware.js.map