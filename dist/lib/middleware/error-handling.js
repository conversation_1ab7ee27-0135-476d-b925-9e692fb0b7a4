"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorResponses = exports.ExternalServiceError = exports.DatabaseError = exports.InternalServerError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = exports.APIError = void 0;
exports.createErrorHandler = createErrorHandler;
exports.createGlobalErrorMiddleware = createGlobalErrorMiddleware;
exports.asyncHandler = asyncHandler;
exports.withErrorHandling = withErrorHandling;
exports.createErrorResponse = createErrorResponse;
const server_1 = require("next/server");
const logging_1 = require("./logging");
class APIError extends Error {
    constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', details, isOperational = true, isSecurity = false) {
        super(message);
        this.name = 'APIError';
        this.statusCode = statusCode;
        this.code = code;
        this.details = details;
        this.isOperational = isOperational;
        this.isSecurity = isSecurity;
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, APIError);
        }
    }
}
exports.APIError = APIError;
class ValidationError extends APIError {
    constructor(message, details) {
        super(message, 400, 'VALIDATION_ERROR', details, true, false);
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
class AuthenticationError extends APIError {
    constructor(message = 'Authentication required', details) {
        super(message, 401, 'AUTHENTICATION_ERROR', details, true, true);
        this.name = 'AuthenticationError';
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends APIError {
    constructor(message = 'Insufficient permissions', details) {
        super(message, 403, 'AUTHORIZATION_ERROR', details, true, true);
        this.name = 'AuthorizationError';
    }
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends APIError {
    constructor(message = 'Resource not found', details) {
        super(message, 404, 'NOT_FOUND', details, true, false);
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
class ConflictError extends APIError {
    constructor(message = 'Resource conflict', details) {
        super(message, 409, 'CONFLICT', details, true, false);
        this.name = 'ConflictError';
    }
}
exports.ConflictError = ConflictError;
class RateLimitError extends APIError {
    constructor(message = 'Rate limit exceeded', details) {
        super(message, 429, 'RATE_LIMIT_EXCEEDED', details, true, true);
        this.name = 'RateLimitError';
    }
}
exports.RateLimitError = RateLimitError;
class InternalServerError extends APIError {
    constructor(message = 'Internal server error', details) {
        super(message, 500, 'INTERNAL_ERROR', details, false, false);
        this.name = 'InternalServerError';
    }
}
exports.InternalServerError = InternalServerError;
class DatabaseError extends APIError {
    constructor(message = 'Database operation failed', details) {
        super(message, 500, 'DATABASE_ERROR', details, false, false);
        this.name = 'DatabaseError';
    }
}
exports.DatabaseError = DatabaseError;
class ExternalServiceError extends APIError {
    constructor(message = 'External service unavailable', details) {
        super(message, 502, 'EXTERNAL_SERVICE_ERROR', details, true, false);
        this.name = 'ExternalServiceError';
    }
}
exports.ExternalServiceError = ExternalServiceError;
function getClientIP(req) {
    const forwarded = req.headers.get('x-forwarded-for');
    const realIP = req.headers.get('x-real-ip');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    return realIP || req.ip || 'unknown';
}
function sanitizeError(error, isProduction = process.env.NODE_ENV === 'production') {
    const baseError = {
        error: 'Internal Server Error',
        message: 'An unexpected error occurred',
        code: 'INTERNAL_ERROR',
        timestamp: Date.now()
    };
    if (error instanceof APIError) {
        return {
            error: getErrorTitle(error.statusCode),
            message: error.message,
            code: error.code,
            timestamp: Date.now(),
            ...(isProduction ? {} : { details: error.details })
        };
    }
    if (error.name === 'ValidationError' || error.validationErrors) {
        return {
            error: 'Validation Failed',
            message: error.message || 'Request validation failed',
            code: 'VALIDATION_ERROR',
            timestamp: Date.now(),
            details: error.validationErrors || error.details
        };
    }
    if (error.code?.startsWith('P')) {
        return {
            error: 'Database Error',
            message: isProduction ? 'Database operation failed' : error.message,
            code: 'DATABASE_ERROR',
            timestamp: Date.now(),
            ...(isProduction ? {} : { details: { prismaCode: error.code } })
        };
    }
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return {
            error: 'Service Unavailable',
            message: 'External service is currently unavailable',
            code: 'EXTERNAL_SERVICE_ERROR',
            timestamp: Date.now()
        };
    }
    if (error instanceof SyntaxError && error.message.includes('JSON')) {
        return {
            error: 'Bad Request',
            message: 'Invalid JSON in request body',
            code: 'INVALID_JSON',
            timestamp: Date.now()
        };
    }
    if (isProduction) {
        return baseError;
    }
    else {
        return {
            ...baseError,
            message: error.message || baseError.message,
            details: {
                stack: error.stack,
                name: error.name
            }
        };
    }
}
function getErrorTitle(statusCode) {
    switch (statusCode) {
        case 400: return 'Bad Request';
        case 401: return 'Unauthorized';
        case 403: return 'Forbidden';
        case 404: return 'Not Found';
        case 405: return 'Method Not Allowed';
        case 409: return 'Conflict';
        case 422: return 'Unprocessable Entity';
        case 429: return 'Too Many Requests';
        case 500: return 'Internal Server Error';
        case 502: return 'Bad Gateway';
        case 503: return 'Service Unavailable';
        case 504: return 'Gateway Timeout';
        default: return 'Error';
    }
}
function logError(error, req, context = {}) {
    const errorInfo = {
        message: error.message,
        stack: error.stack,
        name: error.name,
        statusCode: error.statusCode,
        code: error.code,
        url: req.url,
        method: req.method,
        userAgent: req.headers.get('user-agent'),
        ip: getClientIP(req),
        timestamp: new Date().toISOString(),
        requestId: req.headers.get('x-request-id'),
        userId: req.headers.get('x-user-id'),
        ...context
    };
    if (error instanceof APIError && error.isSecurity) {
        (0, logging_1.logSecurityEvent)({
            type: error.code === 'AUTHENTICATION_ERROR' ? 'AUTH_FAILURE' : 'SUSPICIOUS_REQUEST',
            severity: error.statusCode === 401 ? 'medium' : 'high',
            ip: getClientIP(req),
            userAgent: req.headers.get('user-agent') || 'unknown',
            userId: req.headers.get('x-user-id'),
            details: {
                error: error.message,
                code: error.code,
                url: req.url,
                method: req.method
            }
        });
    }
    if (error.statusCode >= 500 || !error.isOperational) {
        console.error('[API ERROR - CRITICAL]', errorInfo);
    }
    else if (error.statusCode >= 400) {
        console.warn('[API ERROR - CLIENT]', errorInfo);
    }
    else {
        console.log('[API ERROR - INFO]', errorInfo);
    }
}
function createErrorHandler(options = {}) {
    const { enableStackTrace = process.env.NODE_ENV !== 'production', enableRequestDetails = process.env.NODE_ENV !== 'production', logErrors = true } = options;
    return function errorHandler(error, req) {
        if (logErrors) {
            logError(error, req);
        }
        let statusCode = 500;
        if (error instanceof APIError) {
            statusCode = error.statusCode;
        }
        else if (error.statusCode) {
            statusCode = error.statusCode;
        }
        else if (error.status) {
            statusCode = error.status;
        }
        const errorResponse = sanitizeError(error, process.env.NODE_ENV === 'production');
        if (req.headers.get('x-request-id')) {
            errorResponse.requestId = req.headers.get('x-request-id');
        }
        const headers = {
            'Content-Type': 'application/json'
        };
        if (error instanceof APIError && error.isSecurity) {
            headers['X-Content-Type-Options'] = 'nosniff';
            headers['X-Frame-Options'] = 'DENY';
            headers['Cache-Control'] = 'no-store, no-cache, must-revalidate';
        }
        if (error instanceof RateLimitError && error.details?.retryAfter) {
            headers['Retry-After'] = error.details.retryAfter.toString();
        }
        return new server_1.NextResponse(JSON.stringify(errorResponse), {
            status: statusCode,
            headers
        });
    };
}
function createGlobalErrorMiddleware() {
    const errorHandler = createErrorHandler();
    return function globalErrorMiddleware(error, req) {
        return errorHandler(error, req);
    };
}
function asyncHandler(fn) {
    return async (...args) => {
        try {
            return await fn(...args);
        }
        catch (error) {
            throw error;
        }
    };
}
function withErrorHandling(handler) {
    return async (req, ...args) => {
        try {
            return await handler(req, ...args);
        }
        catch (error) {
            const errorHandler = createErrorHandler();
            return errorHandler(error, req);
        }
    };
}
function createErrorResponse(message, statusCode = 500, code = 'ERROR', details) {
    const errorResponse = {
        error: getErrorTitle(statusCode),
        message,
        code,
        timestamp: Date.now(),
        ...(details && { details })
    };
    return new server_1.NextResponse(JSON.stringify(errorResponse), {
        status: statusCode,
        headers: {
            'Content-Type': 'application/json'
        }
    });
}
exports.errorResponses = {
    badRequest: (message = 'Bad request', details) => createErrorResponse(message, 400, 'BAD_REQUEST', details),
    unauthorized: (message = 'Authentication required') => createErrorResponse(message, 401, 'UNAUTHORIZED'),
    forbidden: (message = 'Access forbidden') => createErrorResponse(message, 403, 'FORBIDDEN'),
    notFound: (message = 'Resource not found') => createErrorResponse(message, 404, 'NOT_FOUND'),
    conflict: (message = 'Resource conflict') => createErrorResponse(message, 409, 'CONFLICT'),
    rateLimited: (message = 'Rate limit exceeded', retryAfter) => {
        const response = createErrorResponse(message, 429, 'RATE_LIMITED');
        if (retryAfter) {
            response.headers.set('Retry-After', retryAfter.toString());
        }
        return response;
    },
    internalError: (message = 'Internal server error') => createErrorResponse(message, 500, 'INTERNAL_ERROR'),
    serviceUnavailable: (message = 'Service temporarily unavailable') => createErrorResponse(message, 503, 'SERVICE_UNAVAILABLE')
};
exports.default = createGlobalErrorMiddleware;
//# sourceMappingURL=error-handling.js.map