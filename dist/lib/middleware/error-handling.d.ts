import { NextRequest, NextResponse } from 'next/server';
export interface ErrorResponse {
    error: string;
    message: string;
    code: string;
    timestamp: number;
    requestId?: string;
    details?: any;
}
export interface APIError extends Error {
    statusCode?: number;
    code?: string;
    details?: any;
    isOperational?: boolean;
    isSecurity?: boolean;
}
export declare class APIError extends Error {
    statusCode: number;
    code: string;
    details?: any;
    isOperational: boolean;
    isSecurity: boolean;
    constructor(message: string, statusCode?: number, code?: string, details?: any, isOperational?: boolean, isSecurity?: boolean);
}
export declare class ValidationError extends APIError {
    constructor(message: string, details?: any);
}
export declare class AuthenticationError extends APIError {
    constructor(message?: string, details?: any);
}
export declare class AuthorizationError extends APIError {
    constructor(message?: string, details?: any);
}
export declare class NotFoundError extends APIError {
    constructor(message?: string, details?: any);
}
export declare class ConflictError extends APIError {
    constructor(message?: string, details?: any);
}
export declare class RateLimitError extends APIError {
    constructor(message?: string, details?: any);
}
export declare class InternalServerError extends APIError {
    constructor(message?: string, details?: any);
}
export declare class DatabaseError extends APIError {
    constructor(message?: string, details?: any);
}
export declare class ExternalServiceError extends APIError {
    constructor(message?: string, details?: any);
}
export declare function createErrorHandler(options?: {
    enableStackTrace?: boolean;
    enableRequestDetails?: boolean;
    logErrors?: boolean;
}): (error: any, req: NextRequest) => NextResponse;
export declare function createGlobalErrorMiddleware(): (error: any, req: NextRequest) => NextResponse;
export declare function asyncHandler<T extends any[], R>(fn: (...args: T) => Promise<R>): (...args: T) => Promise<R>;
export declare function withErrorHandling(handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>): (req: NextRequest, ...args: any[]) => Promise<NextResponse>;
export declare function createErrorResponse(message: string, statusCode?: number, code?: string, details?: any): NextResponse;
export declare const errorResponses: {
    badRequest: (message?: string, details?: any) => NextResponse<unknown>;
    unauthorized: (message?: string) => NextResponse<unknown>;
    forbidden: (message?: string) => NextResponse<unknown>;
    notFound: (message?: string) => NextResponse<unknown>;
    conflict: (message?: string) => NextResponse<unknown>;
    rateLimited: (message?: string, retryAfter?: number) => NextResponse<unknown>;
    internalError: (message?: string) => NextResponse<unknown>;
    serviceUnavailable: (message?: string) => NextResponse<unknown>;
};
export default createGlobalErrorMiddleware;
