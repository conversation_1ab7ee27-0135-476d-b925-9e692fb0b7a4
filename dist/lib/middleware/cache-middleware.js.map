{"version": 3, "file": "cache-middleware.js", "sourceRoot": "", "sources": ["../../../lib/middleware/cache-middleware.ts"], "names": [], "mappings": ";;;AAgBA,4CAmDC;AAED,oCAOC;AAED,0CAiBC;AAED,wCAKC;AAED,0BAKC;AAED,8BAGC;AAED,4BAIC;AAGD,wCAoBC;AAED,kDAUC;AAtJD,wCAAuD;AACvD,yDAA+C;AAU/C,SAAgB,gBAAgB,CAAC,OAAiB;IAChD,OAAO,KAAK,EAAE,OAAoB,EAAE,GAAG,IAAW,EAAE,EAAE;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAG5B,MAAM,YAAY,GAAG;YACnB,SAAS;YACT,GAAG,EAAE,KAAK;YACV,GAAG,EAAE,IAAqB;YAC1B,GAAG,EAAE,IAAqB;SAC3B,CAGA;QAAC,OAAe,CAAC,YAAY,GAAG,YAAY,CAAA;QAG7C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAA;QAGhD,IAAI,QAAQ,YAAY,qBAAY,EAAE,CAAC;YACrC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YAE3C,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,mBAAK,CAAC,OAAO,EAAE,CAAC,CAAA;YACrD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,GAAG,YAAY,IAAI,CAAC,CAAA;YAE5D,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;gBACrB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;gBACtC,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;oBACrB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,GAAG,CAAC,CAAA;gBACvD,CAAC;gBACD,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;oBACrB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAA;gBAClE,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;YACzC,CAAC;YAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;gBAC3C,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,MAAM,mBAAK,CAAC,QAAQ,EAAE,CAAA;oBACpC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA;oBAChF,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAA;gBACxE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;gBAEjB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAA;IACjB,CAAC,CAAA;AACH,CAAC;AAED,SAAgB,YAAY,CAAC,OAAoB,EAAE,GAAW,EAAE,GAAY;IAC1E,MAAM,OAAO,GAAI,OAAe,CAAC,YAAY,CAAA;IAC7C,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CAAC,GAAG,GAAG,IAAI,CAAA;QAClB,OAAO,CAAC,GAAG,GAAG,GAAG,CAAA;QACjB,OAAO,CAAC,GAAG,GAAG,GAAG,CAAA;IACnB,CAAC;AACH,CAAC;AAED,SAAgB,eAAe,CAAC,QAAsB,EAAE,UAIpD,EAAE;IACJ,MAAM,EAAE,MAAM,GAAG,GAAG,EAAE,oBAAoB,GAAG,GAAG,EAAE,YAAY,EAAE,GAAG,OAAO,CAAA;IAE1E,IAAI,YAAY,EAAE,CAAC;QACjB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,CAAC,CAAA;IACrD,CAAC;SAAM,CAAC;QACN,QAAQ,CAAC,OAAO,CAAC,GAAG,CAClB,eAAe,EACf,mBAAmB,MAAM,4BAA4B,oBAAoB,EAAE,CAC5E,CAAA;IACH,CAAC;IAED,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,SAAgB,cAAc,CAAC,QAAsB;IACnD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,qCAAqC,CAAC,CAAA;IAC5E,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;IAC1C,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;IACpC,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,SAAgB,OAAO,CAAC,IAAS;IAC/B,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAA;IAChC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;IACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;IACjC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAA;AAClC,CAAC;AAED,SAAgB,SAAS,CAAC,OAAoB,EAAE,IAAY;IAC1D,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IACxD,OAAO,WAAW,KAAK,IAAI,CAAA;AAC7B,CAAC;AAED,SAAgB,QAAQ,CAAC,QAAsB,EAAE,IAAS;IACxD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC1B,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAClC,OAAO,QAAQ,CAAA;AACjB,CAAC;AAGM,KAAK,UAAU,cAAc,CAClC,GAAW,EACX,KAAa,EACb,QAAgB;IAOhB,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAA;IAChD,MAAM,KAAK,GAAG,MAAM,mBAAK,CAAC,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;IAClD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAA;IAEvC,OAAO;QACL,OAAO,EAAE,KAAK,IAAI,KAAK;QACvB,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC;QACrC,SAAS;QACT,KAAK,EAAE,KAAK;KACb,CAAA;AACH,CAAC;AAED,SAAgB,mBAAmB,CACjC,QAAsB,EACtB,SAAoF;IAEpF,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;IAChD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAA;IAC7E,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAA;IACzE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAA;IAEpE,OAAO,QAAQ,CAAA;AACjB,CAAC;AAGD,MAAa,WAAW;IACtB,MAAM,CAAC,KAAK,CAAC,gBAAgB;QAC3B,IAAI,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;YAGvC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,6BAA6B,EAAE;gBAC1D,MAAM,EAAE,KAAK;aACd,CAAC,CAAA;YAEF,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAClD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,MAAc;QACvC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,0BAA0B,MAAM,KAAK,CAAC,CAAA;YAGlD,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,KAAK,CAAC,iCAAiC,MAAM,EAAE,CAAC;gBAChD,KAAK,CAAC,mCAAmC,MAAM,mBAAmB,CAAC;aACpE,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,yBAAyB,MAAM,EAAE,CAAC,CAAA;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAA;QAClE,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,gBAAgB;QAC3B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAA;YAE/C,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,KAAK,CAAC,uCAAuC,CAAC;gBAC9C,KAAK,CAAC,iCAAiC,CAAC;aACzC,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;CACF;AAjDD,kCAiDC;AAGD,MAAa,YAAY;IACvB,MAAM,CAAC,KAAK,CAAC,gBAAgB;QAK3B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,mBAAK,CAAC,QAAQ,EAAE,CAAA;YACpC,MAAM,SAAS,GAAG,mBAAK,CAAC,WAAW,EAAE,CAAA;YAErC,OAAO;gBACL,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;gBAC1C,IAAI,EAAE,mBAAK,CAAC,OAAO,EAAE;gBACrB,OAAO,EAAE;oBACP,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;iBACnC;aACF,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,IAAI;aACd,CAAA;QACH,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,eAAe;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC5C,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,CAAA;IACtC,CAAC;CACF;AAjCD,oCAiCC"}