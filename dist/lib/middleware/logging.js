"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseLogger = exports.requestLogger = void 0;
exports.logSecurityEvent = logSecurityEvent;
exports.logPerformanceMetric = logPerformanceMetric;
exports.createLoggingMiddleware = createLoggingMiddleware;
exports.createResponseLoggingMiddleware = createResponseLoggingMiddleware;
exports.getRequestStats = getRequestStats;
exports.getSecurityEvents = getSecurityEvents;
exports.getPerformanceInsights = getPerformanceInsights;
const requestLogs = [];
const securityEvents = [];
const performanceMetrics = [];
setInterval(() => {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000);
    while (requestLogs.length > 0 && requestLogs[0].timestamp < cutoff) {
        requestLogs.shift();
    }
    while (securityEvents.length > 0 && securityEvents[0].timestamp < cutoff) {
        securityEvents.shift();
    }
    while (performanceMetrics.length > 0 && performanceMetrics[0].timestamp < cutoff) {
        performanceMetrics.shift();
    }
}, 60 * 60 * 1000);
function generateLogId() {
    return Array.from(crypto.getRandomValues(new Uint8Array(16)))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
}
function getClientIP(req) {
    const forwarded = req.headers.get('x-forwarded-for');
    const realIP = req.headers.get('x-real-ip');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    return realIP || req.ip || 'unknown';
}
function getUserAgent(req) {
    return req.headers.get('user-agent') || 'unknown';
}
function getRequestSize(req) {
    const contentLength = req.headers.get('content-length');
    return contentLength ? parseInt(contentLength, 10) : 0;
}
function getResponseSize(response) {
    const contentLength = response.headers.get('content-length');
    return contentLength ? parseInt(contentLength, 10) : 0;
}
function extractUserInfo(req) {
    return {
        userId: req.headers.get('x-user-id') || undefined,
        sessionId: req.headers.get('x-session-id') || undefined
    };
}
function logSecurityEvent(event) {
    securityEvents.push({
        ...event,
        timestamp: Date.now()
    });
    console.warn('[SECURITY EVENT]', {
        type: event.type,
        severity: event.severity,
        ip: event.ip,
        details: event.details
    });
    if (event.severity === 'critical') {
        console.error('[CRITICAL SECURITY EVENT]', event);
    }
}
function logPerformanceMetric(metric) {
    performanceMetrics.push(metric);
    if (metric.responseTime > 5000) {
        console.warn('[SLOW REQUEST]', {
            endpoint: metric.endpoint,
            method: metric.method,
            responseTime: metric.responseTime,
            statusCode: metric.statusCode
        });
    }
}
function createLoggingMiddleware(options = {}) {
    const { logRequests = true, logSecurity = true, logPerformance = true, sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'], sensitiveParams = ['password', 'token', 'secret', 'key'] } = options;
    return async function loggingMiddleware(req) {
        const startTime = Date.now();
        const logId = generateLogId();
        const ip = getClientIP(req);
        const userAgent = getUserAgent(req);
        const { userId, sessionId } = extractUserInfo(req);
        if (logSecurity) {
            await performSecurityChecks(req, ip, userAgent, userId);
        }
        const headers = new Headers(req.headers);
        headers.set('x-request-id', logId);
        const requestInfo = {
            id: logId,
            timestamp: startTime,
            method: req.method,
            url: req.url,
            path: new URL(req.url).pathname,
            query: Object.fromEntries(new URL(req.url).searchParams.entries()),
            userAgent,
            ip,
            userId,
            sessionId,
            requestSize: getRequestSize(req)
        };
        if (sensitiveParams.length > 0) {
            for (const param of sensitiveParams) {
                if (param in requestInfo.query) {
                    requestInfo.query[param] = '[REDACTED]';
                }
            }
        }
        return null;
    };
}
async function performSecurityChecks(req, ip, userAgent, userId) {
    const url = new URL(req.url);
    const path = url.pathname;
    const query = url.searchParams.toString();
    const sqlInjectionPatterns = [
        /(\bunion\b.*\bselect\b)/i,
        /(\bselect\b.*\bfrom\b)/i,
        /(\bdrop\b.*\btable\b)/i,
        /(\binsert\b.*\binto\b)/i,
        /(\bupdate\b.*\bset\b)/i,
        /(\bdelete\b.*\bfrom\b)/i,
        /(;|'|"|`|\\|\|\||&&)/,
        /(\bor\b.*=.*\bor\b)/i,
        /(\band\b.*=.*\band\b)/i
    ];
    const fullUrl = `${path}?${query}`;
    for (const pattern of sqlInjectionPatterns) {
        if (pattern.test(fullUrl)) {
            logSecurityEvent({
                type: 'SQL_INJECTION_ATTEMPT',
                severity: 'high',
                ip,
                userAgent,
                userId,
                details: {
                    url: req.url,
                    pattern: pattern.source,
                    method: req.method
                }
            });
            break;
        }
    }
    const xssPatterns = [
        /<script[^>]*>.*?<\/script>/gi,
        /javascript:/gi,
        /vbscript:/gi,
        /on\w+\s*=/gi,
        /<iframe[^>]*>.*?<\/iframe>/gi,
        /<object[^>]*>.*?<\/object>/gi,
        /<embed[^>]*>/gi
    ];
    for (const pattern of xssPatterns) {
        if (pattern.test(fullUrl)) {
            logSecurityEvent({
                type: 'XSS_ATTEMPT',
                severity: 'high',
                ip,
                userAgent,
                userId,
                details: {
                    url: req.url,
                    pattern: pattern.source,
                    method: req.method
                }
            });
            break;
        }
    }
    const suspiciousUserAgents = [
        /sqlmap/i,
        /nmap/i,
        /nikto/i,
        /wget/i,
        /curl/i,
        /python-requests/i,
        /bot/i,
        /crawler/i,
        /spider/i
    ];
    for (const pattern of suspiciousUserAgents) {
        if (pattern.test(userAgent)) {
            logSecurityEvent({
                type: 'SUSPICIOUS_REQUEST',
                severity: 'medium',
                ip,
                userAgent,
                userId,
                details: {
                    reason: 'suspicious_user_agent',
                    userAgent,
                    url: req.url
                }
            });
            break;
        }
    }
    const requestCount = requestLogs.filter(log => log.ip === ip &&
        log.timestamp > Date.now() - (60 * 1000)).length;
    if (requestCount > 60) {
        logSecurityEvent({
            type: 'RATE_LIMIT_EXCEEDED',
            severity: 'medium',
            ip,
            userAgent,
            userId,
            details: {
                requestCount,
                timeWindow: '1 minute'
            }
        });
    }
}
function createResponseLoggingMiddleware() {
    return function logResponse(response, req) {
        const endTime = Date.now();
        const startTime = parseInt(req.headers.get('x-start-time') || endTime.toString());
        const duration = endTime - startTime;
        const logId = req.headers.get('x-request-id') || generateLogId();
        const logEntry = {
            id: logId,
            timestamp: startTime,
            method: req.method,
            url: req.url,
            path: new URL(req.url).pathname,
            query: Object.fromEntries(new URL(req.url).searchParams.entries()),
            userAgent: getUserAgent(req),
            ip: getClientIP(req),
            ...extractUserInfo(req),
            duration,
            statusCode: response.status,
            responseSize: getResponseSize(response),
            requestSize: getRequestSize(req)
        };
        if (response.status >= 400) {
            logEntry.error = response.statusText;
        }
        requestLogs.push(logEntry);
        logPerformanceMetric({
            endpoint: logEntry.path,
            method: logEntry.method,
            responseTime: duration,
            timestamp: endTime,
            statusCode: response.status,
            userId: logEntry.userId
        });
        response.headers.set('x-request-id', logId);
        response.headers.set('x-response-time', `${duration}ms`);
        return response;
    };
}
function getRequestStats(timeWindow = 24 * 60 * 60 * 1000) {
    const cutoff = Date.now() - timeWindow;
    const recentLogs = requestLogs.filter(log => log.timestamp > cutoff);
    const totalRequests = recentLogs.length;
    const averageResponseTime = recentLogs.reduce((sum, log) => sum + log.duration, 0) / totalRequests || 0;
    const errorCount = recentLogs.filter(log => log.statusCode >= 400).length;
    const errorRate = totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0;
    const endpointCounts = {};
    recentLogs.forEach(log => {
        const key = `${log.method} ${log.path}`;
        endpointCounts[key] = (endpointCounts[key] || 0) + 1;
    });
    const topEndpoints = Object.entries(endpointCounts)
        .map(([endpoint, count]) => ({ endpoint, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);
    const statusCodes = {};
    recentLogs.forEach(log => {
        const code = log.statusCode.toString();
        statusCodes[code] = (statusCodes[code] || 0) + 1;
    });
    return {
        totalRequests,
        averageResponseTime: Math.round(averageResponseTime),
        errorRate: Math.round(errorRate * 100) / 100,
        topEndpoints,
        statusCodes
    };
}
function getSecurityEvents(timeWindow = 24 * 60 * 60 * 1000) {
    const cutoff = Date.now() - timeWindow;
    return securityEvents.filter(event => event.timestamp > cutoff);
}
function getPerformanceInsights(timeWindow = 24 * 60 * 60 * 1000) {
    const cutoff = Date.now() - timeWindow;
    const recentMetrics = performanceMetrics.filter(metric => metric.timestamp > cutoff);
    const endpointPerformance = {};
    recentMetrics.forEach(metric => {
        const key = `${metric.method} ${metric.endpoint}`;
        if (!endpointPerformance[key]) {
            endpointPerformance[key] = { total: 0, count: 0 };
        }
        endpointPerformance[key].total += metric.responseTime;
        endpointPerformance[key].count += 1;
    });
    const slowestEndpoints = Object.entries(endpointPerformance)
        .map(([endpoint, stats]) => ({
        endpoint,
        averageResponseTime: Math.round(stats.total / stats.count),
        count: stats.count
    }))
        .sort((a, b) => b.averageResponseTime - a.averageResponseTime)
        .slice(0, 10);
    const hourlyMetrics = {};
    recentMetrics.forEach(metric => {
        const hour = Math.floor(metric.timestamp / (60 * 60 * 1000)) * (60 * 60 * 1000);
        if (!hourlyMetrics[hour]) {
            hourlyMetrics[hour] = { total: 0, count: 0 };
        }
        hourlyMetrics[hour].total += metric.responseTime;
        hourlyMetrics[hour].count += 1;
    });
    const performanceTrend = Object.entries(hourlyMetrics)
        .map(([timestamp, stats]) => ({
        timestamp: parseInt(timestamp),
        averageResponseTime: Math.round(stats.total / stats.count)
    }))
        .sort((a, b) => a.timestamp - b.timestamp);
    return {
        slowestEndpoints,
        performanceTrend
    };
}
exports.requestLogger = createLoggingMiddleware();
exports.responseLogger = createResponseLoggingMiddleware();
//# sourceMappingURL=logging.js.map