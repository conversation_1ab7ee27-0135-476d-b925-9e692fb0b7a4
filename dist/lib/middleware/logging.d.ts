import { NextRequest, NextResponse } from 'next/server';
export interface LogEntry {
    id: string;
    timestamp: number;
    method: string;
    url: string;
    path: string;
    query: Record<string, string>;
    userAgent: string;
    ip: string;
    userId?: string;
    sessionId?: string;
    duration: number;
    statusCode: number;
    responseSize: number;
    requestSize: number;
    error?: string;
    rateLimited?: boolean;
    authAttempt?: boolean;
    securityEvent?: string;
}
export interface SecurityEvent {
    type: 'RATE_LIMIT_EXCEEDED' | 'AUTH_FAILURE' | 'INVALID_TOKEN' | 'SUSPICIOUS_REQUEST' | 'SQL_INJECTION_ATTEMPT' | 'XSS_ATTEMPT';
    severity: 'low' | 'medium' | 'high' | 'critical';
    ip: string;
    userAgent: string;
    userId?: string;
    details: Record<string, any>;
    timestamp: number;
}
export interface PerformanceMetric {
    endpoint: string;
    method: string;
    responseTime: number;
    timestamp: number;
    statusCode: number;
    userId?: string;
}
export declare function logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void;
export declare function logPerformanceMetric(metric: PerformanceMetric): void;
export declare function createLoggingMiddleware(options?: {
    logRequests?: boolean;
    logSecurity?: boolean;
    logPerformance?: boolean;
    sensitiveHeaders?: string[];
    sensitiveParams?: string[];
}): (req: NextRequest) => Promise<NextResponse | null>;
export declare function createResponseLoggingMiddleware(): (response: NextResponse, req: NextRequest) => NextResponse;
export declare function getRequestStats(timeWindow?: number): {
    totalRequests: number;
    averageResponseTime: number;
    errorRate: number;
    topEndpoints: Array<{
        endpoint: string;
        count: number;
    }>;
    statusCodes: Record<string, number>;
};
export declare function getSecurityEvents(timeWindow?: number): SecurityEvent[];
export declare function getPerformanceInsights(timeWindow?: number): {
    slowestEndpoints: Array<{
        endpoint: string;
        averageResponseTime: number;
        count: number;
    }>;
    performanceTrend: Array<{
        timestamp: number;
        averageResponseTime: number;
    }>;
};
export declare const requestLogger: (req: NextRequest) => Promise<NextResponse | null>;
export declare const responseLogger: (response: NextResponse, req: NextRequest) => NextResponse;
