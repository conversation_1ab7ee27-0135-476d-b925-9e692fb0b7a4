import { NextRequest, NextResponse } from 'next/server';
export declare class CSRFProtection {
    static generateToken(): string;
    static setCSRFToken(): string;
    static getCSRFToken(): Promise<string | null>;
    static validateCSRFToken(req: NextRequest): Promise<boolean>;
    static getClientToken(): Promise<string>;
}
export declare function withCSRF<T extends (...args: any[]) => Promise<NextResponse>>(handler: T): T;
export declare function withAuthAndCSRF(handler: (req: any) => Promise<NextResponse>, authOptions?: any): (req: NextRequest) => Promise<NextResponse<unknown>>;
