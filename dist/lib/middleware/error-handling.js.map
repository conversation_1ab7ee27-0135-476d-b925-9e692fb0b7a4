{"version": 3, "file": "error-handling.js", "sourceRoot": "", "sources": ["../../../lib/middleware/error-handling.ts"], "names": [], "mappings": ";;;AA4QA,gDA4DC;AAGD,kEAMC;AAGD,oCAWC;AAGD,8CAWC;AAGD,kDAuBC;AAlYD,wCAAuD;AACvD,uCAA4C;AAmB5C,MAAa,QAAS,SAAQ,KAAK;IAOjC,YACE,OAAe,EACf,aAAqB,GAAG,EACxB,OAAe,gBAAgB,EAC/B,OAAa,EACb,gBAAyB,IAAI,EAC7B,aAAsB,KAAK;QAE3B,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,IAAI,GAAG,UAAU,CAAA;QACtB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;QAClC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAG5B,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC5B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;QACzC,CAAC;IACH,CAAC;CACF;AA5BD,4BA4BC;AAGD,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,kBAAkB,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;QAC7D,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAA;IAC/B,CAAC;CACF;AALD,0CAKC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,UAAkB,yBAAyB,EAAE,OAAa;QACpE,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,sBAAsB,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAChE,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAA;IACnC,CAAC;CACF;AALD,kDAKC;AAED,MAAa,kBAAmB,SAAQ,QAAQ;IAC9C,YAAY,UAAkB,0BAA0B,EAAE,OAAa;QACrE,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAC/D,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAA;IAClC,CAAC;CACF;AALD,gDAKC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,oBAAoB,EAAE,OAAa;QAC/D,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;QACtD,IAAI,CAAC,IAAI,GAAG,eAAe,CAAA;IAC7B,CAAC;CACF;AALD,sCAKC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,mBAAmB,EAAE,OAAa;QAC9D,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;QACrD,IAAI,CAAC,IAAI,GAAG,eAAe,CAAA;IAC7B,CAAC;CACF;AALD,sCAKC;AAED,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,UAAkB,qBAAqB,EAAE,OAAa;QAChE,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAC/D,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAA;IAC9B,CAAC;CACF;AALD,wCAKC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,UAAkB,uBAAuB,EAAE,OAAa;QAClE,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,gBAAgB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5D,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAA;IACnC,CAAC;CACF;AALD,kDAKC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,2BAA2B,EAAE,OAAa;QACtE,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,gBAAgB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5D,IAAI,CAAC,IAAI,GAAG,eAAe,CAAA;IAC7B,CAAC;CACF;AALD,sCAKC;AAED,MAAa,oBAAqB,SAAQ,QAAQ;IAChD,YAAY,UAAkB,8BAA8B,EAAE,OAAa;QACzE,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,wBAAwB,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;QACnE,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAA;IACpC,CAAC;CACF;AALD,oDAKC;AAED,SAAS,WAAW,CAAC,GAAgB;IACnC,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;IACpD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IAE3C,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;IACvC,CAAC;IAED,OAAO,MAAM,IAAI,GAAG,CAAC,EAAE,IAAI,SAAS,CAAA;AACtC,CAAC;AAED,SAAS,aAAa,CAAC,KAAU,EAAE,eAAwB,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;IAC9F,MAAM,SAAS,GAAkB;QAC/B,KAAK,EAAE,uBAAuB;QAC9B,OAAO,EAAE,8BAA8B;QACvC,IAAI,EAAE,gBAAgB;QACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;KACtB,CAAA;IAGD,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,OAAO;YACL,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC;YACtC,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;SACpD,CAAA;IACH,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC/D,OAAO;YACL,KAAK,EAAE,mBAAmB;YAC1B,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,2BAA2B;YACrD,IAAI,EAAE,kBAAkB;YACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,OAAO;SACjD,CAAA;IACH,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QAChC,OAAO;YACL,KAAK,EAAE,gBAAgB;YACvB,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO;YACnE,IAAI,EAAE,gBAAgB;YACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;SACjE,CAAA;IACH,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAClE,OAAO;YACL,KAAK,EAAE,qBAAqB;YAC5B,OAAO,EAAE,2CAA2C;YACpD,IAAI,EAAE,wBAAwB;YAC9B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;IACH,CAAC;IAGD,IAAI,KAAK,YAAY,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACnE,OAAO;YACL,KAAK,EAAE,aAAa;YACpB,OAAO,EAAE,8BAA8B;YACvC,IAAI,EAAE,cAAc;YACpB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;IACH,CAAC;IAGD,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,SAAS,CAAA;IAClB,CAAC;SAAM,CAAC;QACN,OAAO;YACL,GAAG,SAAS;YACZ,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO;YAC3C,OAAO,EAAE;gBACP,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,IAAI,EAAE,KAAK,CAAC,IAAI;aACjB;SACF,CAAA;IACH,CAAC;AACH,CAAC;AAED,SAAS,aAAa,CAAC,UAAkB;IACvC,QAAQ,UAAU,EAAE,CAAC;QACnB,KAAK,GAAG,CAAC,CAAC,OAAO,aAAa,CAAA;QAC9B,KAAK,GAAG,CAAC,CAAC,OAAO,cAAc,CAAA;QAC/B,KAAK,GAAG,CAAC,CAAC,OAAO,WAAW,CAAA;QAC5B,KAAK,GAAG,CAAC,CAAC,OAAO,WAAW,CAAA;QAC5B,KAAK,GAAG,CAAC,CAAC,OAAO,oBAAoB,CAAA;QACrC,KAAK,GAAG,CAAC,CAAC,OAAO,UAAU,CAAA;QAC3B,KAAK,GAAG,CAAC,CAAC,OAAO,sBAAsB,CAAA;QACvC,KAAK,GAAG,CAAC,CAAC,OAAO,mBAAmB,CAAA;QACpC,KAAK,GAAG,CAAC,CAAC,OAAO,uBAAuB,CAAA;QACxC,KAAK,GAAG,CAAC,CAAC,OAAO,aAAa,CAAA;QAC9B,KAAK,GAAG,CAAC,CAAC,OAAO,qBAAqB,CAAA;QACtC,KAAK,GAAG,CAAC,CAAC,OAAO,iBAAiB,CAAA;QAClC,OAAO,CAAC,CAAC,OAAO,OAAO,CAAA;IACzB,CAAC;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,KAAU,EAAE,GAAgB,EAAE,UAAe,EAAE;IAC/D,MAAM,SAAS,GAAG;QAChB,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,UAAU,EAAE,KAAK,CAAC,UAAU;QAC5B,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;QACxC,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;QACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;QAC1C,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;QACpC,GAAG,OAAO;KACX,CAAA;IAGD,IAAI,KAAK,YAAY,QAAQ,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;QAClD,IAAA,0BAAgB,EAAC;YACf,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,sBAAsB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,oBAAoB;YACnF,QAAQ,EAAE,KAAK,CAAC,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM;YACtD,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;YACpB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS;YACrD,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;YACpC,OAAO,EAAE;gBACP,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,MAAM,EAAE,GAAG,CAAC,MAAM;aACnB;SACF,CAAC,CAAA;IACJ,CAAC;IAGD,IAAI,KAAK,CAAC,UAAU,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;QACpD,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAA;IACpD,CAAC;SAAM,IAAI,KAAK,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;QACnC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAA;IACjD,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAA;IAC9C,CAAC;AACH,CAAC;AAED,SAAgB,kBAAkB,CAAC,UAI/B,EAAE;IACJ,MAAM,EACJ,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EACxD,oBAAoB,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAC5D,SAAS,GAAG,IAAI,EACjB,GAAG,OAAO,CAAA;IAEX,OAAO,SAAS,YAAY,CAAC,KAAU,EAAE,GAAgB;QAEvD,IAAI,SAAS,EAAE,CAAC;YACd,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QACtB,CAAC;QAGD,IAAI,UAAU,GAAG,GAAG,CAAA;QACpB,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;YAC9B,UAAU,GAAG,KAAK,CAAC,UAAU,CAAA;QAC/B,CAAC;aAAM,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YAC5B,UAAU,GAAG,KAAK,CAAC,UAAU,CAAA;QAC/B,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACxB,UAAU,GAAG,KAAK,CAAC,MAAM,CAAA;QAC3B,CAAC;QAGD,MAAM,aAAa,GAAG,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAA;QAGjF,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;YACpC,aAAa,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAE,CAAA;QAC5D,CAAC;QAGD,MAAM,OAAO,GAA2B;YACtC,cAAc,EAAE,kBAAkB;SACnC,CAAA;QAGD,IAAI,KAAK,YAAY,QAAQ,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YAClD,OAAO,CAAC,wBAAwB,CAAC,GAAG,SAAS,CAAA;YAC7C,OAAO,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAA;YACnC,OAAO,CAAC,eAAe,CAAC,GAAG,qCAAqC,CAAA;QAClE,CAAC;QAGD,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;YACjE,OAAO,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAA;QAC9D,CAAC;QAED,OAAO,IAAI,qBAAY,CACrB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAC7B;YACE,MAAM,EAAE,UAAU;YAClB,OAAO;SACR,CACF,CAAA;IACH,CAAC,CAAA;AACH,CAAC;AAGD,SAAgB,2BAA2B;IACzC,MAAM,YAAY,GAAG,kBAAkB,EAAE,CAAA;IAEzC,OAAO,SAAS,qBAAqB,CAAC,KAAU,EAAE,GAAgB;QAChE,OAAO,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;IACjC,CAAC,CAAA;AACH,CAAC;AAGD,SAAgB,YAAY,CAC1B,EAA8B;IAE9B,OAAO,KAAK,EAAE,GAAG,IAAO,EAAc,EAAE;QACtC,IAAI,CAAC;YACH,OAAO,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAA;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC;AAGD,SAAgB,iBAAiB,CAC/B,OAAoE;IAEpE,OAAO,KAAK,EAAE,GAAgB,EAAE,GAAG,IAAW,EAAyB,EAAE;QACvE,IAAI,CAAC;YACH,OAAO,MAAM,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,kBAAkB,EAAE,CAAA;YACzC,OAAO,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QACjC,CAAC;IACH,CAAC,CAAA;AACH,CAAC;AAGD,SAAgB,mBAAmB,CACjC,OAAe,EACf,aAAqB,GAAG,EACxB,OAAe,OAAO,EACtB,OAAa;IAEb,MAAM,aAAa,GAAkB;QACnC,KAAK,EAAE,aAAa,CAAC,UAAU,CAAC;QAChC,OAAO;QACP,IAAI;QACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;KAC5B,CAAA;IAED,OAAO,IAAI,qBAAY,CACrB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAC7B;QACE,MAAM,EAAE,UAAU;QAClB,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;SACnC;KACF,CACF,CAAA;AACH,CAAC;AAGY,QAAA,cAAc,GAAG;IAC5B,UAAU,EAAE,CAAC,UAAkB,aAAa,EAAE,OAAa,EAAE,EAAE,CAC7D,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,OAAO,CAAC;IAE3D,YAAY,EAAE,CAAC,UAAkB,yBAAyB,EAAE,EAAE,CAC5D,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,cAAc,CAAC;IAEnD,SAAS,EAAE,CAAC,UAAkB,kBAAkB,EAAE,EAAE,CAClD,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,CAAC;IAEhD,QAAQ,EAAE,CAAC,UAAkB,oBAAoB,EAAE,EAAE,CACnD,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,CAAC;IAEhD,QAAQ,EAAE,CAAC,UAAkB,mBAAmB,EAAE,EAAE,CAClD,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC;IAE/C,WAAW,EAAE,CAAC,UAAkB,qBAAqB,EAAE,UAAmB,EAAE,EAAE;QAC5E,MAAM,QAAQ,GAAG,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,cAAc,CAAC,CAAA;QAClE,IAAI,UAAU,EAAE,CAAC;YACf,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAA;QAC5D,CAAC;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,aAAa,EAAE,CAAC,UAAkB,uBAAuB,EAAE,EAAE,CAC3D,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,gBAAgB,CAAC;IAErD,kBAAkB,EAAE,CAAC,UAAkB,iCAAiC,EAAE,EAAE,CAC1E,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,CAAC;CAC3D,CAAA;AAED,kBAAe,2BAA2B,CAAA"}