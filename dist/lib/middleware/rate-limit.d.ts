import { NextRequest, NextResponse } from 'next/server';
interface RateLimitConfig {
    windowMs: number;
    maxRequests: number;
    keyGenerator?: (req: NextRequest) => string;
}
export declare function rateLimit(config: RateLimitConfig): (request: NextRequest) => Promise<NextResponse<unknown>>;
export declare const rateLimiters: {
    api: (request: NextRequest) => Promise<NextResponse<unknown>>;
    strict: (request: NextRequest) => Promise<NextResponse<unknown>>;
    auth: (request: NextRequest) => Promise<NextResponse<unknown>>;
    status: (request: NextRequest) => Promise<NextResponse<unknown>>;
};
export {};
