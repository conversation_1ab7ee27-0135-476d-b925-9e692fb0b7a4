import { NextRequest, NextResponse } from 'next/server';
export interface CacheMetrics {
    startTime: number;
    cacheHit: boolean;
    cacheKey?: string;
    cacheTTL?: number;
    responseTime: number;
}
export declare function withCacheMetrics(handler: Function): (request: NextRequest, ...args: any[]) => Promise<any>;
export declare function markCacheHit(request: NextRequest, key: string, ttl?: number): void;
export declare function addCacheHeaders(response: NextResponse, options?: {
    maxAge?: number;
    staleWhileRevalidate?: number;
    cacheControl?: string;
}): NextResponse<unknown>;
export declare function noCacheHeaders(response: NextResponse): NextResponse<unknown>;
export declare function getETag(data: any): string;
export declare function checkETag(request: NextRequest, etag: string): boolean;
export declare function withETag(response: NextResponse, data: any): NextResponse;
export declare function checkRateLimit(key: string, limit: number, windowMs: number): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    total: number;
}>;
export declare function addRateLimitHeaders(response: NextResponse, rateLimit: {
    allowed: boolean;
    remaining: number;
    resetTime: number;
    total: number;
}): NextResponse<unknown>;
export declare class CacheWarmer {
    static warmLibraryCache(): Promise<void>;
    static warmUserCache(userId: string): Promise<void>;
    static warmPopularCache(): Promise<void>;
}
export declare class CacheMonitor {
    static getHealthMetrics(): Promise<{
        status: 'healthy' | 'degraded' | 'down';
        mode: 'redis' | 'memory';
        metrics: any;
    }>;
    static logCacheMetrics(): Promise<void>;
}
