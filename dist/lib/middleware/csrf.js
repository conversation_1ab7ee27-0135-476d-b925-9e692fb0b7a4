"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CSRFProtection = void 0;
exports.withCSRF = withCSRF;
exports.withAuthAndCSRF = withAuthAndCSRF;
const server_1 = require("next/server");
const crypto_1 = __importDefault(require("crypto"));
const headers_1 = require("next/headers");
const CSRF_TOKEN_NAME = 'csrf_token';
const CSRF_HEADER_NAME = 'x-csrf-token';
const CSRF_TOKEN_LENGTH = 32;
class CSRFProtection {
    static generateToken() {
        return crypto_1.default.randomBytes(CSRF_TOKEN_LENGTH).toString('hex');
    }
    static setCSRFToken() {
        const token = this.generateToken();
        const isHttps = process.env.NEXT_PUBLIC_URL?.startsWith('https://') ?? false;
        (0, headers_1.cookies)().set(CSRF_TOKEN_NAME, token, {
            httpOnly: true,
            secure: isHttps,
            sameSite: 'lax',
            path: '/',
            maxAge: 60 * 60 * 24
        });
        return token;
    }
    static async getCSRFToken() {
        const cookieStore = await (0, headers_1.cookies)();
        return cookieStore.get(CSRF_TOKEN_NAME)?.value || null;
    }
    static async validateCSRFToken(req) {
        if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
            return true;
        }
        const cookieToken = await this.getCSRFToken();
        const headerToken = req.headers.get(CSRF_HEADER_NAME);
        if (!cookieToken || !headerToken) {
            return false;
        }
        return crypto_1.default.timingSafeEqual(Buffer.from(cookieToken), Buffer.from(headerToken));
    }
    static async getClientToken() {
        let token = await this.getCSRFToken();
        if (!token) {
            token = this.setCSRFToken();
        }
        return token;
    }
}
exports.CSRFProtection = CSRFProtection;
function withCSRF(handler) {
    return (async (...args) => {
        const req = args[0];
        if (!CSRFProtection.validateCSRFToken(req)) {
            return server_1.NextResponse.json({
                success: false,
                message: 'Invalid or missing CSRF token',
                code: 'CSRF_ERROR'
            }, { status: 403 });
        }
        return handler(...args);
    });
}
function withAuthAndCSRF(handler, authOptions = {}) {
    return withCSRF(async (req) => {
        const { withAuth } = await Promise.resolve().then(() => __importStar(require('@/middleware/auth-middleware-cookies')));
        const authHandler = withAuth(handler, authOptions);
        return authHandler(req);
    });
}
//# sourceMappingURL=csrf.js.map