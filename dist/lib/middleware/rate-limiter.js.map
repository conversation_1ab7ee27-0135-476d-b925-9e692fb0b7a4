{"version": 3, "file": "rate-limiter.js", "sourceRoot": "", "sources": ["../../../lib/middleware/rate-limiter.ts"], "names": [], "mappings": ";;;AAkCA,8CA4DC;AA2ED,8DAoBC;AAxLD,wCAAuD;AAiBvD,MAAM,cAAc,GAAG,IAAI,GAAG,EAA0B,CAAA;AAGxD,WAAW,CAAC,GAAG,EAAE;IACf,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IACtB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;QACpD,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;YAC1B,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QAC5B,CAAC;IACH,CAAC;AACH,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;AAEjB,SAAgB,iBAAiB,CAAC,MAAuB;IACvD,MAAM,EACJ,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EACzB,WAAW,GAAG,GAAG,EACjB,sBAAsB,GAAG,KAAK,EAC9B,kBAAkB,GAAG,KAAK,EAC1B,YAAY,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,EACzC,GAAG,MAAM,CAAA;IAEV,OAAO,KAAK,UAAU,mBAAmB,CAAC,GAAgB;QACxD,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;QAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,WAAW,GAAG,GAAG,GAAG,QAAQ,CAAA;QAGlC,IAAI,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACnC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;YACpC,KAAK,GAAG;gBACN,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,QAAQ;gBACzB,QAAQ,EAAE,EAAE;aACb,CAAA;YACD,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QAChC,CAAC;QAGD,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,GAAG,WAAW,CAAC,CAAA;QAG5E,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,IAAI,WAAW,EAAE,CAAC;YACzC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAA;YAE5D,OAAO,IAAI,qBAAY,CACrB,IAAI,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,mBAAmB;gBAC1B,OAAO,EAAE,8CAA8C;gBACvD,UAAU;aACX,CAAC,EACF;gBACE,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,aAAa,EAAE,UAAU,CAAC,QAAQ,EAAE;oBACpC,mBAAmB,EAAE,WAAW,CAAC,QAAQ,EAAE;oBAC3C,uBAAuB,EAAE,GAAG;oBAC5B,mBAAmB,EAAE,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE;iBAChD;aACF,CACF,CAAA;QACH,CAAC;QAGD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACxB,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAA;QAGnC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,CAAA;QAExD,OAAO,IAAI,CAAA;IACb,CAAC,CAAA;AACH,CAAC;AAGY,QAAA,cAAc,GAAG,iBAAiB,CAAC;IAC9C,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,WAAW,EAAE,GAAG;CACjB,CAAC,CAAA;AAEW,QAAA,eAAe,GAAG,iBAAiB,CAAC;IAC/C,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,WAAW,EAAE,CAAC;CACf,CAAC,CAAA;AAEW,QAAA,eAAe,GAAG,iBAAiB,CAAC;IAC/C,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACvB,WAAW,EAAE,EAAE;CAChB,CAAC,CAAA;AAEW,QAAA,iBAAiB,GAAG,iBAAiB,CAAC;IACjD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,WAAW,EAAE,EAAE;CAChB,CAAC,CAAA;AAEW,QAAA,sBAAsB,GAAG,iBAAiB,CAAC;IACtD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,WAAW,EAAE,CAAC;CACf,CAAC,CAAA;AAGF,SAAS,WAAW,CAAC,GAAQ;IAE3B,IAAI,GAAG,CAAC,OAAO,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;QACzD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;QACpD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;QAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;QAE/C,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;QACvC,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAA;QACf,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAA;QACjB,CAAC;QAED,OAAO,GAAG,CAAC,EAAE,IAAI,SAAS,CAAA;IAC5B,CAAC;IAGD,IAAI,GAAG,CAAC,OAAO,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;QACnD,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QAChD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;QACvC,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;QAE3C,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;QACjF,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;QACnD,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAA;QACzD,CAAC;IACH,CAAC;IAGD,OAAO,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,EAAE,aAAa,IAAI,GAAG,CAAC,MAAM,EAAE,aAAa,IAAI,SAAS,CAAA;AAC1F,CAAC;AAGD,SAAgB,yBAAyB,CACvC,eAAgC,EAChC,mBAAoC;IAEpC,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,eAAe,CAAC,CAAA;IAC3D,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,mBAAmB,CAAC,CAAA;IAEnE,OAAO,KAAK,UAAU,2BAA2B,CAAC,GAAgB;QAEhE,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;QACnD,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAEhD,MAAM,eAAe,GAAG,UAAU,IAAI,aAAa,CAAA;QAEnD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,oBAAoB,CAAC,GAAG,CAAC,CAAA;QAClC,CAAC;aAAM,CAAC;YACN,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAA;QAC9B,CAAC;IACH,CAAC,CAAA;AACH,CAAC;AAEY,QAAA,kBAAkB,GAAG,yBAAyB,CACzD,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAC7C,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,CAC/C,CAAA"}