"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.adaptiveApiLimiter = exports.passwordResetRateLimit = exports.uploadRateLimiter = exports.quizRateLimiter = exports.authRateLimiter = exports.apiRateLimiter = void 0;
exports.createRateLimiter = createRateLimiter;
exports.createAdaptiveRateLimiter = createAdaptiveRateLimiter;
const server_1 = require("next/server");
const rateLimitStore = new Map();
setInterval(() => {
    const now = Date.now();
    for (const [key, entry] of rateLimitStore.entries()) {
        if (entry.resetTime < now) {
            rateLimitStore.delete(key);
        }
    }
}, 5 * 60 * 1000);
function createRateLimiter(config) {
    const { windowMs = 15 * 60 * 1000, maxRequests = 100, skipSuccessfulRequests = false, skipFailedRequests = false, keyGenerator = (req) => getClientIP(req) } = config;
    return async function rateLimitMiddleware(req) {
        const key = keyGenerator(req);
        const now = Date.now();
        const windowStart = now - windowMs;
        let entry = rateLimitStore.get(key);
        if (!entry || entry.resetTime < now) {
            entry = {
                count: 0,
                resetTime: now + windowMs,
                requests: []
            };
            rateLimitStore.set(key, entry);
        }
        entry.requests = entry.requests.filter(timestamp => timestamp > windowStart);
        if (entry.requests.length >= maxRequests) {
            const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
            return new server_1.NextResponse(JSON.stringify({
                error: 'Too Many Requests',
                message: 'Rate limit exceeded. Please try again later.',
                retryAfter
            }), {
                status: 429,
                headers: {
                    'Content-Type': 'application/json',
                    'Retry-After': retryAfter.toString(),
                    'X-RateLimit-Limit': maxRequests.toString(),
                    'X-RateLimit-Remaining': '0',
                    'X-RateLimit-Reset': entry.resetTime.toString()
                }
            });
        }
        entry.requests.push(now);
        entry.count = entry.requests.length;
        const remaining = Math.max(0, maxRequests - entry.count);
        return null;
    };
}
exports.apiRateLimiter = createRateLimiter({
    windowMs: 15 * 60 * 1000,
    maxRequests: 100
});
exports.authRateLimiter = createRateLimiter({
    windowMs: 15 * 60 * 1000,
    maxRequests: 5
});
exports.quizRateLimiter = createRateLimiter({
    windowMs: 1 * 60 * 1000,
    maxRequests: 30
});
exports.uploadRateLimiter = createRateLimiter({
    windowMs: 60 * 60 * 1000,
    maxRequests: 10
});
exports.passwordResetRateLimit = createRateLimiter({
    windowMs: 60 * 60 * 1000,
    maxRequests: 3
});
function getClientIP(req) {
    if (req.headers && typeof req.headers.get === 'function') {
        const forwarded = req.headers.get('x-forwarded-for');
        const realIP = req.headers.get('x-real-ip');
        const clientIP = req.headers.get('x-client-ip');
        if (forwarded) {
            return forwarded.split(',')[0].trim();
        }
        if (realIP) {
            return realIP;
        }
        if (clientIP) {
            return clientIP;
        }
        return req.ip || 'unknown';
    }
    if (req.headers && typeof req.headers === 'object') {
        const forwarded = req.headers['x-forwarded-for'];
        const realIP = req.headers['x-real-ip'];
        const clientIP = req.headers['x-client-ip'];
        if (forwarded) {
            return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0].trim();
        }
        if (realIP) {
            return Array.isArray(realIP) ? realIP[0] : realIP;
        }
        if (clientIP) {
            return Array.isArray(clientIP) ? clientIP[0] : clientIP;
        }
    }
    return req.ip || req.connection?.remoteAddress || req.socket?.remoteAddress || 'unknown';
}
function createAdaptiveRateLimiter(anonymousConfig, authenticatedConfig) {
    const anonymousLimiter = createRateLimiter(anonymousConfig);
    const authenticatedLimiter = createRateLimiter(authenticatedConfig);
    return async function adaptiveRateLimitMiddleware(req) {
        const authHeader = req.headers.get('authorization');
        const sessionCookie = req.cookies.get('session');
        const isAuthenticated = authHeader || sessionCookie;
        if (isAuthenticated) {
            return authenticatedLimiter(req);
        }
        else {
            return anonymousLimiter(req);
        }
    };
}
exports.adaptiveApiLimiter = createAdaptiveRateLimiter({ windowMs: 15 * 60 * 1000, maxRequests: 50 }, { windowMs: 15 * 60 * 1000, maxRequests: 200 });
//# sourceMappingURL=rate-limiter.js.map