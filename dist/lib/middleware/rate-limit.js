"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rateLimiters = void 0;
exports.rateLimit = rateLimit;
const server_1 = require("next/server");
const rateLimitStore = new Map();
let cleanupInterval = null;
function startCleanup() {
    if (cleanupInterval)
        return;
    cleanupInterval = setInterval(() => {
        const now = Date.now();
        const maxEntries = 1000;
        if (rateLimitStore.size > maxEntries) {
            const entriesToRemove = rateLimitStore.size - maxEntries;
            let removed = 0;
            for (const [key, entry] of rateLimitStore.entries()) {
                if (removed >= entriesToRemove)
                    break;
                if (!entry.blocked) {
                    rateLimitStore.delete(key);
                    removed++;
                }
            }
        }
        for (const [key, entry] of rateLimitStore.entries()) {
            entry.requests = entry.requests.filter(timestamp => now - timestamp < 5 * 60 * 1000);
            if (entry.requests.length === 0 && !entry.blocked) {
                rateLimitStore.delete(key);
            }
        }
    }, 60000);
}
startCleanup();
if (typeof process !== 'undefined' && typeof process.on === 'function') {
    process.on('SIGINT', () => {
        if (cleanupInterval)
            clearInterval(cleanupInterval);
    });
    process.on('SIGTERM', () => {
        if (cleanupInterval)
            clearInterval(cleanupInterval);
    });
}
function rateLimit(config) {
    const { windowMs, maxRequests, keyGenerator = (req) => {
        const forwarded = req.headers.get('x-forwarded-for');
        const real = req.headers.get('x-real-ip');
        const ip = forwarded?.split(',')[0] || real || 'anonymous';
        return ip;
    } } = config;
    return async function middleware(request) {
        const key = keyGenerator(request);
        const now = Date.now();
        let entry = rateLimitStore.get(key);
        if (!entry) {
            entry = { requests: [], blocked: false };
            rateLimitStore.set(key, entry);
        }
        entry.requests = entry.requests.filter(timestamp => now - timestamp < windowMs);
        if (entry.blocked) {
            const oldestRequest = Math.min(...entry.requests);
            const blockExpiry = oldestRequest + windowMs;
            if (now < blockExpiry) {
                const retryAfter = Math.ceil((blockExpiry - now) / 1000);
                return server_1.NextResponse.json({
                    success: false,
                    message: `Rate limit exceeded. Please try again in ${retryAfter} seconds.`,
                    rateLimited: true,
                    retryAfter
                }, {
                    status: 429,
                    headers: {
                        'Retry-After': retryAfter.toString(),
                        'X-RateLimit-Limit': maxRequests.toString(),
                        'X-RateLimit-Remaining': '0',
                        'X-RateLimit-Reset': new Date(blockExpiry).toISOString()
                    }
                });
            }
            else {
                entry.blocked = false;
                entry.requests = [];
            }
        }
        entry.requests.push(now);
        if (entry.requests.length > maxRequests) {
            entry.blocked = true;
            const retryAfter = Math.ceil(windowMs / 1000);
            return server_1.NextResponse.json({
                success: false,
                message: `Rate limit exceeded. Maximum ${maxRequests} requests per ${windowMs / 1000} seconds.`,
                rateLimited: true,
                retryAfter
            }, {
                status: 429,
                headers: {
                    'Retry-After': retryAfter.toString(),
                    'X-RateLimit-Limit': maxRequests.toString(),
                    'X-RateLimit-Remaining': '0',
                    'X-RateLimit-Reset': new Date(now + windowMs).toISOString()
                }
            });
        }
        const remaining = Math.max(0, maxRequests - entry.requests.length);
        return server_1.NextResponse.next({
            headers: {
                'X-RateLimit-Limit': maxRequests.toString(),
                'X-RateLimit-Remaining': remaining.toString(),
                'X-RateLimit-Reset': new Date(now + windowMs).toISOString()
            }
        });
    };
}
exports.rateLimiters = {
    api: rateLimit({
        windowMs: 60 * 1000,
        maxRequests: 60
    }),
    strict: rateLimit({
        windowMs: 60 * 1000,
        maxRequests: 10
    }),
    auth: rateLimit({
        windowMs: 15 * 60 * 1000,
        maxRequests: 5
    }),
    status: rateLimit({
        windowMs: 60 * 1000,
        maxRequests: 120
    })
};
//# sourceMappingURL=rate-limit.js.map