import { NextRequest, NextResponse } from 'next/server';
export interface CORSConfig {
    origin?: string | string[] | ((origin: string) => boolean);
    methods?: string[];
    allowedHeaders?: string[];
    exposedHeaders?: string[];
    credentials?: boolean;
    maxAge?: number;
    preflightContinue?: boolean;
    optionsSuccessStatus?: number;
}
export interface SecurityHeadersConfig {
    contentSecurityPolicy?: string | boolean;
    crossOriginEmbedderPolicy?: string | boolean;
    crossOriginOpenerPolicy?: string | boolean;
    crossOriginResourcePolicy?: string | boolean;
    dnsPrefetchControl?: boolean;
    frameOptions?: string | boolean;
    hidePoweredBy?: boolean;
    hsts?: {
        maxAge?: number;
        includeSubDomains?: boolean;
        preload?: boolean;
    } | boolean;
    ieNoOpen?: boolean;
    noSniff?: boolean;
    originAgentCluster?: boolean;
    permittedCrossDomainPolicies?: string | boolean;
    referrerPolicy?: string | boolean;
    xssFilter?: boolean;
}
export declare function createCORSMiddleware(config?: CORSConfig): (req: NextRequest) => Promise<NextResponse | null>;
export declare function createSecurityHeadersMiddleware(config?: SecurityHeadersConfig): (req: NextRequest) => Promise<NextResponse | null>;
export declare function addCORSHeaders(response: NextResponse, req: NextRequest, config?: CORSConfig): NextResponse;
export declare function addSecurityHeaders(response: NextResponse, config?: SecurityHeadersConfig): NextResponse;
export declare function createSecureMiddleware(corsConfig?: CORSConfig, securityConfig?: SecurityHeadersConfig): (req: NextRequest) => Promise<NextResponse | null>;
export declare const developmentCORS: (req: NextRequest) => Promise<NextResponse | null>;
export declare const productionCORS: (req: NextRequest) => Promise<NextResponse | null>;
export declare const apiCORS: (req: NextRequest) => Promise<NextResponse | null>;
export declare const publicApiCORS: (req: NextRequest) => Promise<NextResponse | null>;
