{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../lib/middleware/auth.ts"], "names": [], "mappings": ";;;AA6BA,kCAqBC;AAED,oDA+EC;AAmCD,gDAgBC;AAjLD,wCAAuD;AACvD,qDAAkE;AAClE,qDAAgD;AAkBhD,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;IAC7D,OAAO,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAA;AACtE,CAAC;AAEM,KAAK,UAAU,WAAW;IAC/B,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,yBAAU,CAAC,iBAAiB,EAAE,CAAA;QACzD,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAA;QAG9B,MAAM,IAAI,GAAG,MAAM,0BAAW,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC,CAAA;QAC/D,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAA;QAEtB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,KAAK;SACf,CAAA;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;QAChD,OAAO,IAAI,CAAA;IACb,CAAC;AACH,CAAC;AAED,SAAgB,oBAAoB,CAAC,SAAqB,EAAE;IAC1D,MAAM,EACJ,WAAW,GAAG,IAAI,EAClB,YAAY,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,EAC1C,WAAW,GAAG,KAAK,EACpB,GAAG,MAAM,CAAA;IAEV,OAAO,KAAK,UAAU,cAAc,CAAC,GAAgB;QAEnD,MAAM,IAAI,GAAG,MAAM,WAAW,EAAE,CAAA;QAGhC,IAAI,WAAW,IAAI,CAAC,IAAI,EAAE,CAAC;YACzB,OAAO,IAAI,qBAAY,CACrB,IAAI,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE,eAAe;aACtB,CAAC,EACF;gBACE,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,kBAAkB,EAAE,QAAQ;iBAC7B;aACF,CACF,CAAA;QACH,CAAC;QAGD,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;YAClC,OAAO,IAAI,qBAAY,CACrB,IAAI,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE,4CAA4C;gBACrD,IAAI,EAAE,iBAAiB;aACxB,CAAC,EACF;gBACE,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CACF,CAAA;QACH,CAAC;QAGD,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,qBAAY,CACrB,IAAI,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE,0BAA0B;gBAChC,aAAa,EAAE,YAAY;gBAC3B,QAAQ,EAAE,IAAI,CAAC,IAAI;aACpB,CAAC,EACF;gBACE,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CACF,CAAA;QACH,CAAC;QAGD,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;YACxC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;YACjC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;YACrC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YACvC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;YAG7C,OAAO,IAAI,CAAA;QACb,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC,CAAA;AACH,CAAC;AAIY,QAAA,WAAW,GAAG,oBAAoB,CAAC;IAC9C,WAAW,EAAE,IAAI;IACjB,YAAY,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC;IACzC,WAAW,EAAE,KAAK;CACnB,CAAC,CAAA;AAEW,QAAA,SAAS,GAAG,oBAAoB,CAAC;IAC5C,WAAW,EAAE,IAAI;IACjB,YAAY,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;IACjC,WAAW,EAAE,KAAK;CACnB,CAAC,CAAA;AAEW,QAAA,gBAAgB,GAAG,oBAAoB,CAAC;IACnD,WAAW,EAAE,IAAI;IACjB,YAAY,EAAE,CAAC,WAAW,CAAC;IAC3B,WAAW,EAAE,KAAK;CACnB,CAAC,CAAA;AAEW,QAAA,YAAY,GAAG,oBAAoB,CAAC;IAC/C,WAAW,EAAE,KAAK;IAClB,YAAY,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC;IACzC,WAAW,EAAE,IAAI;CAClB,CAAC,CAAA;AAEW,QAAA,YAAY,GAAG,oBAAoB,CAAC;IAC/C,WAAW,EAAE,KAAK;IAClB,YAAY,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC;IACzC,WAAW,EAAE,IAAI;CAClB,CAAC,CAAA;AAGF,SAAgB,kBAAkB,CAAC,GAAgB;IACjD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;IAC/C,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;IACjD,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;IAEnD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,IAAI,CAAA;IACb,CAAC;IAED,OAAO;QACL,EAAE,EAAE,MAAM;QACV,IAAI,EAAE,QAAuC;QAC7C,KAAK,EAAE,SAAS,IAAI,SAAS;QAC7B,QAAQ,EAAE,QAAQ,IAAI,SAAS;KAChC,CAAA;AACH,CAAC"}