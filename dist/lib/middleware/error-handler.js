"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorCodes = exports.ErrorHandler = exports.AppError = void 0;
exports.createSocketErrorHandler = createSocketErrorHandler;
exports.createAPIErrorHandler = createAPIErrorHandler;
class AppError extends Error {
    constructor(code, message, statusCode = 500, details) {
        super(message);
        this.code = code;
        this.statusCode = statusCode;
        this.details = details;
        this.name = 'AppError';
    }
}
exports.AppError = AppError;
class ErrorHandler {
    static createErrorResponse(error, requestId) {
        const timestamp = new Date().toISOString();
        if (error instanceof AppError) {
            return {
                error: error.name,
                message: error.message,
                code: error.code,
                details: process.env.NODE_ENV === 'development' ? error.details : undefined,
                timestamp,
                requestId
            };
        }
        if (error instanceof Error) {
            return {
                error: 'InternalServerError',
                message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
                code: 'INTERNAL_ERROR',
                details: process.env.NODE_ENV === 'development' ? { stack: error.stack } : undefined,
                timestamp,
                requestId
            };
        }
        return {
            error: 'UnknownError',
            message: 'An unknown error occurred',
            code: 'UNKNOWN_ERROR',
            timestamp,
            requestId
        };
    }
    static logError(context, error, metadata) {
        const errorInfo = {
            context,
            timestamp: new Date().toISOString(),
            ...metadata
        };
        if (error instanceof Error) {
            console.error(`[${context}] Error:`, {
                ...errorInfo,
                error: {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                }
            });
        }
        else {
            console.error(`[${context}] Unknown error:`, {
                ...errorInfo,
                error
            });
        }
    }
}
exports.ErrorHandler = ErrorHandler;
exports.ErrorCodes = {
    AUTH_REQUIRED: 'AUTH_REQUIRED',
    INVALID_TOKEN: 'INVALID_TOKEN',
    TOKEN_EXPIRED: 'TOKEN_EXPIRED',
    INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    INVALID_INPUT: 'INVALID_INPUT',
    MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
    NOT_FOUND: 'NOT_FOUND',
    ALREADY_EXISTS: 'ALREADY_EXISTS',
    RESOURCE_LOCKED: 'RESOURCE_LOCKED',
    GAME_NOT_FOUND: 'GAME_NOT_FOUND',
    GAME_FULL: 'GAME_FULL',
    GAME_ALREADY_STARTED: 'GAME_ALREADY_STARTED',
    INVALID_GAME_STATE: 'INVALID_GAME_STATE',
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
    INTERNAL_ERROR: 'INTERNAL_ERROR',
    SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
    DATABASE_ERROR: 'DATABASE_ERROR',
    EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR'
};
function createSocketErrorHandler(socketId) {
    return (handlerName, handler) => {
        return async (...args) => {
            const startTime = Date.now();
            try {
                await handler(...args);
            }
            catch (error) {
                const duration = Date.now() - startTime;
                ErrorHandler.logError(`SocketHandler:${handlerName}`, error, {
                    socketId,
                    duration,
                    args: process.env.NODE_ENV === 'development' ? args : undefined
                });
                throw error;
            }
        };
    };
}
function createAPIErrorHandler(routeName) {
    return async (handler) => {
        const startTime = Date.now();
        try {
            return await handler();
        }
        catch (error) {
            const duration = Date.now() - startTime;
            ErrorHandler.logError(`APIRoute:${routeName}`, error, { duration });
            const errorResponse = ErrorHandler.createErrorResponse(error);
            const statusCode = error instanceof AppError ? error.statusCode : 500;
            return new Response(JSON.stringify(errorResponse), {
                status: statusCode,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
    };
}
//# sourceMappingURL=error-handler.js.map