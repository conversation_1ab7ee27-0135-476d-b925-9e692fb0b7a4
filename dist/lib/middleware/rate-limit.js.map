{"version": 3, "file": "rate-limit.js", "sourceRoot": "", "sources": ["../../../lib/middleware/rate-limit.ts"], "names": [], "mappings": ";;;AA6DA,8BA8FC;AA3JD,wCAAuD;AAavD,MAAM,cAAc,GAAG,IAAI,GAAG,EAA0B,CAAA;AACxD,IAAI,eAAe,GAA0B,IAAI,CAAA;AAGjD,SAAS,YAAY;IACnB,IAAI,eAAe;QAAE,OAAM;IAE3B,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,UAAU,GAAG,IAAI,CAAA;QAGvB,IAAI,cAAc,CAAC,IAAI,GAAG,UAAU,EAAE,CAAC;YACrC,MAAM,eAAe,GAAG,cAAc,CAAC,IAAI,GAAG,UAAU,CAAA;YACxD,IAAI,OAAO,GAAG,CAAC,CAAA;YAEf,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpD,IAAI,OAAO,IAAI,eAAe;oBAAE,MAAK;gBACrC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;oBACnB,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;oBAC1B,OAAO,EAAE,CAAA;gBACX,CAAC;YACH,CAAC;QACH,CAAC;QAGD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAEpD,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;YACpF,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBAClD,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAC5B,CAAC;QACH,CAAC;IACH,CAAC,EAAE,KAAK,CAAC,CAAA;AACX,CAAC;AAED,YAAY,EAAE,CAAA;AAGd,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC;IACvE,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACxB,IAAI,eAAe;YAAE,aAAa,CAAC,eAAe,CAAC,CAAA;IACrD,CAAC,CAAC,CAAA;IACF,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;QACzB,IAAI,eAAe;YAAE,aAAa,CAAC,eAAe,CAAC,CAAA;IACrD,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,SAAgB,SAAS,CAAC,MAAuB;IAC/C,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,YAAY,GAAG,CAAC,GAAG,EAAE,EAAE;QAErB,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;QACpD,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;QACzC,MAAM,EAAE,GAAG,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,WAAW,CAAA;QAC1D,OAAO,EAAE,CAAA;IACX,CAAC,EACF,GAAG,MAAM,CAAA;IAEV,OAAO,KAAK,UAAU,UAAU,CAAC,OAAoB;QACnD,MAAM,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,CAAA;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEtB,IAAI,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACnC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,KAAK,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAA;YACxC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QAChC,CAAC;QAGD,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,QAAQ,CAAC,CAAA;QAG/E,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAA;YACjD,MAAM,WAAW,GAAG,aAAa,GAAG,QAAQ,CAAA;YAE5C,IAAI,GAAG,GAAG,WAAW,EAAE,CAAC;gBACtB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAA;gBACxD,OAAO,qBAAY,CAAC,IAAI,CACtB;oBACE,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,4CAA4C,UAAU,WAAW;oBAC1E,WAAW,EAAE,IAAI;oBACjB,UAAU;iBACX,EACD;oBACE,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE;wBACP,aAAa,EAAE,UAAU,CAAC,QAAQ,EAAE;wBACpC,mBAAmB,EAAE,WAAW,CAAC,QAAQ,EAAE;wBAC3C,uBAAuB,EAAE,GAAG;wBAC5B,mBAAmB,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE;qBACzD;iBACF,CACF,CAAA;YACH,CAAC;iBAAM,CAAC;gBAEN,KAAK,CAAC,OAAO,GAAG,KAAK,CAAA;gBACrB,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAA;YACrB,CAAC;QACH,CAAC;QAGD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAGxB,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;YACxC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAA;YACpB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAA;YAE7C,OAAO,qBAAY,CAAC,IAAI,CACtB;gBACE,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gCAAgC,WAAW,iBAAiB,QAAQ,GAAG,IAAI,WAAW;gBAC/F,WAAW,EAAE,IAAI;gBACjB,UAAU;aACX,EACD;gBACE,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE;oBACP,aAAa,EAAE,UAAU,CAAC,QAAQ,EAAE;oBACpC,mBAAmB,EAAE,WAAW,CAAC,QAAQ,EAAE;oBAC3C,uBAAuB,EAAE,GAAG;oBAC5B,mBAAmB,EAAE,IAAI,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;iBAC5D;aACF,CACF,CAAA;QACH,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAClE,OAAO,qBAAY,CAAC,IAAI,CAAC;YACvB,OAAO,EAAE;gBACP,mBAAmB,EAAE,WAAW,CAAC,QAAQ,EAAE;gBAC3C,uBAAuB,EAAE,SAAS,CAAC,QAAQ,EAAE;gBAC7C,mBAAmB,EAAE,IAAI,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;aAC5D;SACF,CAAC,CAAA;IACJ,CAAC,CAAA;AACH,CAAC;AAGY,QAAA,YAAY,GAAG;IAE1B,GAAG,EAAE,SAAS,CAAC;QACb,QAAQ,EAAE,EAAE,GAAG,IAAI;QACnB,WAAW,EAAE,EAAE;KAChB,CAAC;IAGF,MAAM,EAAE,SAAS,CAAC;QAChB,QAAQ,EAAE,EAAE,GAAG,IAAI;QACnB,WAAW,EAAE,EAAE;KAChB,CAAC;IAGF,IAAI,EAAE,SAAS,CAAC;QACd,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxB,WAAW,EAAE,CAAC;KACf,CAAC;IAGF,MAAM,EAAE,SAAS,CAAC;QAChB,QAAQ,EAAE,EAAE,GAAG,IAAI;QACnB,WAAW,EAAE,GAAG;KACjB,CAAC;CACH,CAAA"}