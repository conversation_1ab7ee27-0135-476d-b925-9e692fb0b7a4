{"version": 3, "file": "error-handler.js", "sourceRoot": "", "sources": ["../../../lib/middleware/error-handler.ts"], "names": [], "mappings": ";;;AA+HA,4DAiBC;AAGD,sDAoBC;AAnKD,MAAa,QAAS,SAAQ,KAAK;IACjC,YACS,IAAY,EACnB,OAAe,EACR,aAAqB,GAAG,EACxB,OAAa;QAEpB,KAAK,CAAC,OAAO,CAAC,CAAA;QALP,SAAI,GAAJ,IAAI,CAAQ;QAEZ,eAAU,GAAV,UAAU,CAAc;QACxB,YAAO,GAAP,OAAO,CAAM;QAGpB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAA;IACxB,CAAC;CACF;AAVD,4BAUC;AAWD,MAAa,YAAY;IACvB,MAAM,CAAC,mBAAmB,CACxB,KAAc,EACd,SAAkB;QAElB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;QAE1C,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;YAC9B,OAAO;gBACL,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;gBAC3E,SAAS;gBACT,SAAS;aACV,CAAA;QACH,CAAC;QAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,OAAO;gBACL,KAAK,EAAE,qBAAqB;gBAC5B,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B;gBAChG,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS;gBACpF,SAAS;gBACT,SAAS;aACV,CAAA;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,cAAc;YACrB,OAAO,EAAE,2BAA2B;YACpC,IAAI,EAAE,eAAe;YACrB,SAAS;YACT,SAAS;SACV,CAAA;IACH,CAAC;IAED,MAAM,CAAC,QAAQ,CACb,OAAe,EACf,KAAc,EACd,QAA8B;QAE9B,MAAM,SAAS,GAAG;YAChB,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,GAAG,QAAQ;SACZ,CAAA;QAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,UAAU,EAAE;gBACnC,GAAG,SAAS;gBACZ,KAAK,EAAE;oBACL,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;iBACnB;aACF,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,kBAAkB,EAAE;gBAC3C,GAAG,SAAS;gBACZ,KAAK;aACN,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;CACF;AAjED,oCAiEC;AAGY,QAAA,UAAU,GAAG;IAExB,aAAa,EAAE,eAAe;IAC9B,aAAa,EAAE,eAAe;IAC9B,aAAa,EAAE,eAAe;IAC9B,wBAAwB,EAAE,0BAA0B;IAGpD,gBAAgB,EAAE,kBAAkB;IACpC,aAAa,EAAE,eAAe;IAC9B,sBAAsB,EAAE,wBAAwB;IAGhD,SAAS,EAAE,WAAW;IACtB,cAAc,EAAE,gBAAgB;IAChC,eAAe,EAAE,iBAAiB;IAGlC,cAAc,EAAE,gBAAgB;IAChC,SAAS,EAAE,WAAW;IACtB,oBAAoB,EAAE,sBAAsB;IAC5C,kBAAkB,EAAE,oBAAoB;IAGxC,mBAAmB,EAAE,qBAAqB;IAG1C,cAAc,EAAE,gBAAgB;IAChC,mBAAmB,EAAE,qBAAqB;IAC1C,cAAc,EAAE,gBAAgB;IAChC,sBAAsB,EAAE,wBAAwB;CACxC,CAAA;AAGV,SAAgB,wBAAwB,CAAC,QAAgB;IACvD,OAAO,CAAC,WAAmB,EAAE,OAA0C,EAAE,EAAE;QACzE,OAAO,KAAK,EAAE,GAAG,IAAW,EAAE,EAAE;YAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC5B,IAAI,CAAC;gBACH,MAAM,OAAO,CAAC,GAAG,IAAI,CAAC,CAAA;YACxB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;gBACvC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,WAAW,EAAE,EAAE,KAAK,EAAE;oBAC3D,QAAQ;oBACR,QAAQ;oBACR,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;iBAChE,CAAC,CAAA;gBACF,MAAM,KAAK,CAAA;YACb,CAAC;QACH,CAAC,CAAA;IACH,CAAC,CAAA;AACH,CAAC;AAGD,SAAgB,qBAAqB,CAAC,SAAiB;IACrD,OAAO,KAAK,EAAE,OAAgC,EAAqB,EAAE;QACnE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,IAAI,CAAC;YACH,OAAO,MAAM,OAAO,EAAE,CAAA;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YACvC,YAAY,CAAC,QAAQ,CAAC,YAAY,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAA;YAEnE,MAAM,aAAa,GAAG,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;YAC7D,MAAM,UAAU,GAAG,KAAK,YAAY,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAA;YAErE,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE;gBACjD,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAA;AACH,CAAC"}