"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.publicApiCORS = exports.apiCORS = exports.productionCORS = exports.developmentCORS = void 0;
exports.createCORSMiddleware = createCORSMiddleware;
exports.createSecurityHeadersMiddleware = createSecurityHeadersMiddleware;
exports.addCORSHeaders = addCORSHeaders;
exports.addSecurityHeaders = addSecurityHeaders;
exports.createSecureMiddleware = createSecureMiddleware;
const server_1 = require("next/server");
function getAllowedOrigins() {
    const customOrigins = process.env.ALLOWED_ORIGINS?.split(',').map(o => o.trim()).filter(Boolean);
    const additionalNetworks = process.env.ALLOWED_NETWORKS?.split(',').map(n => n.trim()).filter(Boolean);
    return (origin) => {
        const allowedPatterns = [
            /^https?:\/\/localhost:\d+$/,
            /^https?:\/\/127\.0\.0\.1:\d+$/,
            /^https?:\/\/192\.168\.50\.(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]):\d+$/,
            /^https?:\/\/192\.168\.0\.(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]):\d+$/
        ];
        if (additionalNetworks) {
            additionalNetworks.forEach(network => {
                const escapedNetwork = network.replace(/\./g, '\\.');
                allowedPatterns.push(new RegExp(`^https?:\\/\\/${escapedNetwork}\\.\\d{1,3}:\\d+$`));
            });
        }
        if (customOrigins && customOrigins.includes(origin)) {
            return true;
        }
        if (process.env.NODE_ENV === 'production') {
            const productionDomains = ['https://yourdomain.com', 'https://www.yourdomain.com'];
            if (productionDomains.includes(origin)) {
                return true;
            }
        }
        return allowedPatterns.some(pattern => pattern.test(origin));
    };
}
const DEFAULT_CORS_CONFIG = {
    origin: getAllowedOrigins(),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
        'Accept',
        'Accept-Version',
        'Authorization',
        'Content-Length',
        'Content-MD5',
        'Content-Type',
        'Date',
        'X-Api-Version',
        'X-Requested-With',
        'X-Session-Id',
        'X-User-Agent'
    ],
    exposedHeaders: [
        'X-RateLimit-Limit',
        'X-RateLimit-Remaining',
        'X-RateLimit-Reset',
        'X-Total-Count',
        'X-Response-Time'
    ],
    credentials: true,
    maxAge: 86400,
    optionsSuccessStatus: 204
};
const DEFAULT_SECURITY_HEADERS = {
    contentSecurityPolicy: process.env.NODE_ENV === 'production'
        ? "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; media-src 'self' blob: data:; connect-src 'self' ws: wss:; frame-ancestors 'none';"
        : false,
    crossOriginEmbedderPolicy: 'credentialless',
    crossOriginOpenerPolicy: 'same-origin',
    crossOriginResourcePolicy: 'same-site',
    dnsPrefetchControl: true,
    frameOptions: 'DENY',
    hidePoweredBy: true,
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    },
    ieNoOpen: true,
    noSniff: true,
    originAgentCluster: true,
    permittedCrossDomainPolicies: 'none',
    referrerPolicy: 'strict-origin-when-cross-origin',
    xssFilter: true
};
function isOriginAllowed(origin, allowedOrigins) {
    if (typeof allowedOrigins === 'function') {
        return allowedOrigins(origin);
    }
    if (typeof allowedOrigins === 'string') {
        return origin === allowedOrigins || allowedOrigins === '*';
    }
    if (Array.isArray(allowedOrigins)) {
        return allowedOrigins.includes(origin) || allowedOrigins.includes('*');
    }
    return false;
}
function createCORSMiddleware(config = {}) {
    const corsConfig = { ...DEFAULT_CORS_CONFIG, ...config };
    return async function corsMiddleware(req) {
        const origin = req.headers.get('origin');
        const method = req.method;
        if (method === 'OPTIONS') {
            const response = new server_1.NextResponse(null, {
                status: corsConfig.optionsSuccessStatus || 204
            });
            if (origin && corsConfig.origin) {
                if (isOriginAllowed(origin, corsConfig.origin)) {
                    response.headers.set('Access-Control-Allow-Origin', origin);
                }
            }
            else if (!corsConfig.origin || corsConfig.origin === '*') {
                response.headers.set('Access-Control-Allow-Origin', '*');
            }
            if (corsConfig.credentials) {
                response.headers.set('Access-Control-Allow-Credentials', 'true');
            }
            if (corsConfig.methods) {
                response.headers.set('Access-Control-Allow-Methods', corsConfig.methods.join(', '));
            }
            if (corsConfig.allowedHeaders) {
                response.headers.set('Access-Control-Allow-Headers', corsConfig.allowedHeaders.join(', '));
            }
            if (corsConfig.maxAge) {
                response.headers.set('Access-Control-Max-Age', corsConfig.maxAge.toString());
            }
            return response;
        }
        return null;
    };
}
function createSecurityHeadersMiddleware(config = {}) {
    const securityConfig = { ...DEFAULT_SECURITY_HEADERS, ...config };
    return async function securityHeadersMiddleware(req) {
        return null;
    };
}
function addCORSHeaders(response, req, config = {}) {
    const corsConfig = { ...DEFAULT_CORS_CONFIG, ...config };
    const origin = req.headers.get('origin');
    if (origin && corsConfig.origin) {
        if (isOriginAllowed(origin, corsConfig.origin)) {
            response.headers.set('Access-Control-Allow-Origin', origin);
        }
    }
    else if (!corsConfig.origin || corsConfig.origin === '*') {
        response.headers.set('Access-Control-Allow-Origin', '*');
    }
    if (corsConfig.credentials) {
        response.headers.set('Access-Control-Allow-Credentials', 'true');
    }
    if (corsConfig.exposedHeaders) {
        response.headers.set('Access-Control-Expose-Headers', corsConfig.exposedHeaders.join(', '));
    }
    return response;
}
function addSecurityHeaders(response, config = {}) {
    const securityConfig = { ...DEFAULT_SECURITY_HEADERS, ...config };
    if (securityConfig.contentSecurityPolicy) {
        if (typeof securityConfig.contentSecurityPolicy === 'string') {
            response.headers.set('Content-Security-Policy', securityConfig.contentSecurityPolicy);
        }
    }
    if (securityConfig.crossOriginEmbedderPolicy) {
        if (typeof securityConfig.crossOriginEmbedderPolicy === 'string') {
            response.headers.set('Cross-Origin-Embedder-Policy', securityConfig.crossOriginEmbedderPolicy);
        }
    }
    if (securityConfig.crossOriginOpenerPolicy) {
        if (typeof securityConfig.crossOriginOpenerPolicy === 'string') {
            response.headers.set('Cross-Origin-Opener-Policy', securityConfig.crossOriginOpenerPolicy);
        }
    }
    if (securityConfig.crossOriginResourcePolicy) {
        if (typeof securityConfig.crossOriginResourcePolicy === 'string') {
            response.headers.set('Cross-Origin-Resource-Policy', securityConfig.crossOriginResourcePolicy);
        }
    }
    if (securityConfig.dnsPrefetchControl) {
        response.headers.set('X-DNS-Prefetch-Control', 'off');
    }
    if (securityConfig.frameOptions) {
        if (typeof securityConfig.frameOptions === 'string') {
            response.headers.set('X-Frame-Options', securityConfig.frameOptions);
        }
    }
    if (securityConfig.hidePoweredBy) {
        response.headers.delete('X-Powered-By');
    }
    if (securityConfig.hsts) {
        if (typeof securityConfig.hsts === 'object') {
            const { maxAge = 31536000, includeSubDomains = true, preload = false } = securityConfig.hsts;
            let hstsValue = `max-age=${maxAge}`;
            if (includeSubDomains)
                hstsValue += '; includeSubDomains';
            if (preload)
                hstsValue += '; preload';
            response.headers.set('Strict-Transport-Security', hstsValue);
        }
    }
    if (securityConfig.ieNoOpen) {
        response.headers.set('X-Download-Options', 'noopen');
    }
    if (securityConfig.noSniff) {
        response.headers.set('X-Content-Type-Options', 'nosniff');
    }
    if (securityConfig.originAgentCluster) {
        response.headers.set('Origin-Agent-Cluster', '?1');
    }
    if (securityConfig.permittedCrossDomainPolicies) {
        if (typeof securityConfig.permittedCrossDomainPolicies === 'string') {
            response.headers.set('X-Permitted-Cross-Domain-Policies', securityConfig.permittedCrossDomainPolicies);
        }
    }
    if (securityConfig.referrerPolicy) {
        if (typeof securityConfig.referrerPolicy === 'string') {
            response.headers.set('Referrer-Policy', securityConfig.referrerPolicy);
        }
    }
    if (securityConfig.xssFilter) {
        response.headers.set('X-XSS-Protection', '1; mode=block');
    }
    return response;
}
function createSecureMiddleware(corsConfig = {}, securityConfig = {}) {
    const corsMiddleware = createCORSMiddleware(corsConfig);
    const securityMiddleware = createSecurityHeadersMiddleware(securityConfig);
    return async function secureMiddleware(req) {
        const corsResponse = await corsMiddleware(req);
        if (corsResponse) {
            return addSecurityHeaders(corsResponse, securityConfig);
        }
        return null;
    };
}
exports.developmentCORS = createSecureMiddleware({
    origin: (origin) => {
        const allowedPatterns = [
            /^http:\/\/localhost:\d+$/,
            /^http:\/\/127\.0\.0\.1:\d+$/,
            /^http:\/\/192\.168\.50\.\d{1,3}:\d+$/
        ];
        return allowedPatterns.some(pattern => pattern.test(origin));
    },
    credentials: true
}, {
    contentSecurityPolicy: false,
    hsts: false
});
exports.productionCORS = createSecureMiddleware({
    origin: getAllowedOrigins(),
    credentials: true
}, DEFAULT_SECURITY_HEADERS);
exports.apiCORS = createCORSMiddleware({
    origin: getAllowedOrigins(),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true
});
exports.publicApiCORS = createCORSMiddleware({
    origin: '*',
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'X-Api-Key'],
    credentials: false
});
//# sourceMappingURL=cors.js.map