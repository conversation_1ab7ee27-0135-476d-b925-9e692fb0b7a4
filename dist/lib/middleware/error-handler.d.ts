export declare class AppError extends <PERSON>rror {
    code: string;
    statusCode: number;
    details?: any | undefined;
    constructor(code: string, message: string, statusCode?: number, details?: any | undefined);
}
export interface ErrorResponse {
    error: string;
    message: string;
    code: string;
    details?: any;
    timestamp: string;
    requestId?: string;
}
export declare class ErrorHandler {
    static createErrorResponse(error: unknown, requestId?: string): ErrorResponse;
    static logError(context: string, error: unknown, metadata?: Record<string, any>): void;
}
export declare const ErrorCodes: {
    readonly AUTH_REQUIRED: "AUTH_REQUIRED";
    readonly INVALID_TOKEN: "INVALID_TOKEN";
    readonly TOKEN_EXPIRED: "TOKEN_EXPIRED";
    readonly INSUFFICIENT_PERMISSIONS: "INSUFFICIENT_PERMISSIONS";
    readonly VALIDATION_ERROR: "VALIDATION_ERROR";
    readonly INVALID_INPUT: "INVALID_INPUT";
    readonly MISSING_REQUIRED_FIELD: "MISSING_REQUIRED_FIELD";
    readonly NOT_FOUND: "NOT_FOUND";
    readonly ALREADY_EXISTS: "ALREADY_EXISTS";
    readonly RESOURCE_LOCKED: "RESOURCE_LOCKED";
    readonly GAME_NOT_FOUND: "GAME_NOT_FOUND";
    readonly GAME_FULL: "GAME_FULL";
    readonly GAME_ALREADY_STARTED: "GAME_ALREADY_STARTED";
    readonly INVALID_GAME_STATE: "INVALID_GAME_STATE";
    readonly RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED";
    readonly INTERNAL_ERROR: "INTERNAL_ERROR";
    readonly SERVICE_UNAVAILABLE: "SERVICE_UNAVAILABLE";
    readonly DATABASE_ERROR: "DATABASE_ERROR";
    readonly EXTERNAL_SERVICE_ERROR: "EXTERNAL_SERVICE_ERROR";
};
export declare function createSocketErrorHandler(socketId: string): (handlerName: string, handler: (...args: any[]) => Promise<void>) => (...args: any[]) => Promise<void>;
export declare function createAPIErrorHandler(routeName: string): (handler: () => Promise<Response>) => Promise<Response>;
