import { AudioManager } from './audio-manager';
import { AudioOutputSelector } from './audio-output-selector';
import { AudioMixer } from './audio-mixer';
export interface AudioSystems {
    audioManager?: AudioManager;
    audioOutputSelector?: AudioOutputSelector;
    audioMixer?: AudioMixer;
}
export interface VolumeState {
    volume: number;
    muted: boolean;
    lastVolume: number;
}
export declare class UnifiedAudioController {
    private systems;
    private volumeState;
    constructor();
    registerSystems(systems: AudioSystems): void;
    setVolume(volume: number): Promise<void>;
    setMuted(muted: boolean): Promise<void>;
    getVolumeState(): VolumeState;
    getVolume(): number;
    isMuted(): boolean;
    getSystemStatus(): Record<string, any>;
    testAllSystems(): Promise<Record<string, boolean>>;
    initialize(defaultVolume?: number): Promise<void>;
    cleanup(): void;
}
export declare function getUnifiedAudioController(): UnifiedAudioController;
export declare function resetUnifiedAudioController(): void;
