{"version": 3, "file": "team-manager.js", "sourceRoot": "", "sources": ["../../lib/team-manager.ts"], "names": [], "mappings": ";;;AAOA,MAAa,WAAW;IAYtB;QAXQ,UAAK,GAAsB,IAAI,GAAG,EAAE,CAAA;QACpC,eAAU,GAAG;YACnB,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;YAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;YAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;SAC3C,CAAA;QACO,eAAU,GAAG;YACnB,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;YAC5C,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;SAC/C,CAAA;IAEc,CAAC;IAKhB,UAAU,CAAC,IAAY,EAAE,SAAiB,EAAE,WAAmB;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;QACpC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAE/C,MAAM,OAAO,GAAW;YACtB,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,EAAE;YACV,KAAK,EAAE,CAAC;YACR,MAAM,EAAE,KAAK;YACb,WAAW,EAAE,KAAK;YAClB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;YACpB,MAAM,EAAE,MAAM;YACd,aAAa,EAAE,IAAI;SACpB,CAAA;QAED,MAAM,IAAI,GAAS;YACjB,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,cAAc;YACrB,KAAK,EAAE,cAAc;YACrB,OAAO,EAAE,CAAC,OAAO,CAAC;YAClB,KAAK,EAAE,CAAC;YACR,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,KAAK;YAClB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC5B,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,eAAe,CAAC,MAAc,EAAE,MAAc;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAA;QAGvB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAA;QACtB,MAAM,CAAC,aAAa,GAAG,KAAK,CAAA;QAE5B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACzB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC5B,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,oBAAoB,CAAC,QAAgB;QACnC,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAClD,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAA;YAClE,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE,CAAC;gBACvB,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;gBAC/C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAA;gBAGnC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC9B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;oBACzB,OAAO,IAAI,CAAA;gBACb,CAAC;gBAGD,IAAI,aAAa,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3D,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAA;oBACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;gBACrC,CAAC;gBAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;gBAC5B,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,WAAW;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAA;IACxC,CAAC;IAKD,OAAO,CAAC,MAAc;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAC/B,CAAC;IAKD,aAAa,CAAC,QAAgB;QAC5B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;gBAC9C,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAA;IAClB,CAAC;IAKD,UAAU,CAAC,MAAc,EAAE,OAAwD;QACjF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAA;QAEvB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;QAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC5B,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,gBAAgB,CAAC,MAAc,EAAE,QAAgB;QAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAA;QAEvB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAA;QACxD,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAA;QAGzB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,CAAA;QAGlD,MAAM,CAAC,aAAa,GAAG,IAAI,CAAA;QAC3B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;QAEzB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC5B,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,gBAAgB,CAAC,UAAsB;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;QAC9C,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAA;QAEvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAA;QACnC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,SAAS,CAAA;QAE1C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACvC,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,mBAAmB,CAAC,WAAyB,EAAE,aAAqB,EAAE,MAAc,EAAE,WAA4B;QAChH,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;YAC9C,IAAI,CAAC,IAAI;gBAAE,SAAQ;YAEnB,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,KAAK,aAAa,CAAA;YACrD,IAAI,CAAC,SAAS;gBAAE,SAAQ;YAExB,IAAI,UAAU,GAAG,CAAC,CAAA;YAElB,QAAQ,WAAW,EAAE,CAAC;gBACpB,KAAK,KAAK;oBAER,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;oBACzC,MAAK;gBACP,KAAK,SAAS;oBAEZ,UAAU,GAAG,MAAM,CAAA;oBACnB,MAAK;gBACP,KAAK,MAAM;oBAET,UAAU,GAAG,MAAM,CAAA;oBACnB,MAAK;gBACP,KAAK,SAAS;oBAEZ,IAAI,UAAU,CAAC,WAAW,KAAK,IAAI,CAAC,SAAS,EAAE,CAAC;wBAC9C,UAAU,GAAG,MAAM,CAAA;oBACrB,CAAC;oBACD,MAAK;YACT,CAAC;YAED,IAAI,CAAC,KAAK,IAAI,UAAU,CAAA;YACxB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACzC,CAAC;IACH,CAAC;IAKD,gBAAgB;QACd,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAClD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;YACxB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;YAC3B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAA;YAC/B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC9B,CAAC;IACH,CAAC;IAKD,gBAAgB,CAAC,OAAiB,EAAE,cAAsB,CAAC;QAEzD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;QAElB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,CAAA;QACxD,MAAM,eAAe,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAA;QAGpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,QAAQ,MAAM,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAA;YACtD,MAAM,OAAO,GAAG,eAAe,CAAC,CAAC,GAAG,WAAW,CAAC,CAAA;YAEhD,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;gBAGhE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACvF,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAA;oBACnD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;gBACvC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,EAAE,CAAA;IAC3B,CAAC;IAKD,WAAW,CAAC,MAAc;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAA;QAGvB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC5B,MAAM,CAAC,MAAM,GAAG,SAAS,CAAA;YACzB,MAAM,CAAC,aAAa,GAAG,KAAK,CAAA;QAC9B,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACzB,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,kBAAkB;QAChB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;IAC7D,CAAC;IAKD,gBAAgB;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QAChC,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IAClE,CAAC;IAKO,iBAAiB;QACvB,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;QAC7E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;IACpF,CAAC;IAKO,iBAAiB;QACvB,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;QAC7E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;IACpF,CAAC;IAKO,cAAc;QACpB,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA;IACxE,CAAC;IAKD,YAAY,CAAC,MAAc;QAMzB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAA;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC1C,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,CAAC,CAAA;QAE7D,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YACjC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5E,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,QAAQ;SACT,CAAA;IACH,CAAC;CACF;AAtUD,kCAsUC;AAEY,QAAA,WAAW,GAAG,IAAI,WAAW,EAAE,CAAA"}