{"version": 3, "file": "multiplayer-socket.js", "sourceRoot": "", "sources": ["../../lib/multiplayer-socket.ts"], "names": [], "mappings": ";;AAiBA,oDAMC;AA4DD,4DAKC;AAKD,kEAIC;AAKD,oEAEC;AAKD,4DAOC;AAKD,kCAEC;AAKD,oDAOC;AAKD,gDAQC;AApJD,uDAA8C;AAQ9C,MAAM,aAAa,GAA6B;IAC9C,MAAM,EAAE,IAAI;IACZ,aAAa,EAAE,KAAK;CACrB,CAAC;AAMF,SAAgB,oBAAoB;IAClC,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QAC1D,gBAAgB,EAAE,CAAC;IACrB,CAAC;IAED,OAAO,aAAa,CAAC,MAAO,CAAC;AAC/B,CAAC;AAKD,SAAS,gBAAgB;IACvB,IAAI,aAAa,CAAC,aAAa,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;QACxD,OAAO;IACT,CAAC;IAGD,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW;QAC9C,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ;QAC1B,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,WAAW,CAAC;IACvD,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,MAAM,CAAC;IACjE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,UAAU,UAAU,IAAI,UAAU,EAAE,CAAC;IAE7F,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAC;IAE7D,aAAa,CAAC,MAAM,GAAG,IAAA,qBAAE,EAAC,SAAS,EAAE;QACnC,WAAW,EAAE,IAAI;QACjB,YAAY,EAAE,IAAI;QAClB,oBAAoB,EAAE,CAAC;QACvB,iBAAiB,EAAE,IAAI;QACvB,oBAAoB,EAAE,IAAI;QAC1B,OAAO,EAAE,KAAK;QACd,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;KACrC,CAAC,CAAC;IAGH,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;QACtC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;QAC/C,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,KAAK,EAAE,EAAE;QACjD,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IAEH,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,aAAa,EAAE,EAAE;QACrD,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;IAClF,CAAC,CAAC,CAAC;IAEH,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,aAAa,EAAE,EAAE;QAC7D,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,aAAa,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;IAEH,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC/C,OAAO,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC;AACrC,CAAC;AAKD,SAAgB,wBAAwB;IACtC,MAAM,MAAM,GAAG,oBAAoB,EAAE,CAAC;IACtC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACtB,MAAM,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;AACH,CAAC;AAKD,SAAgB,2BAA2B;IACzC,IAAI,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAC3D,aAAa,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IACpC,CAAC;AACH,CAAC;AAKD,SAAgB,4BAA4B;IAC1C,OAAO,aAAa,CAAC,MAAM,EAAE,SAAS,IAAI,KAAK,CAAC;AAClD,CAAC;AAKD,SAAgB,wBAAwB;IACtC,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;QACzB,aAAa,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAC1C,aAAa,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;QAClC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;QAC5B,aAAa,CAAC,aAAa,GAAG,KAAK,CAAC;IACtC,CAAC;AACH,CAAC;AAKD,SAAgB,WAAW;IACzB,OAAO,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;AAClC,CAAC;AAKD,SAAgB,oBAAoB,CAAC,KAAa,EAAE,GAAG,IAAW;IAChE,MAAM,MAAM,GAAG,oBAAoB,EAAE,CAAC;IACtC,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;QACrB,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;IAC9B,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,IAAI,CAAC,oCAAoC,KAAK,0BAA0B,CAAC,CAAC;IACpF,CAAC;AACH,CAAC;AAKD,SAAgB,kBAAkB,CAAC,KAAa,EAAE,OAAiC;IACjF,MAAM,MAAM,GAAG,oBAAoB,EAAE,CAAC;IACtC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAG1B,OAAO,GAAG,EAAE;QACV,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC7B,CAAC,CAAC;AACJ,CAAC"}