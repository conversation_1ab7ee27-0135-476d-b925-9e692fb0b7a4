{"version": 3, "file": "enhanced-profile-manager.js", "sourceRoot": "", "sources": ["../../lib/enhanced-profile-manager.ts"], "names": [], "mappings": ";;;AAgBA,MAAa,sBAAsB;IAGjC,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,QAAQ,GAAG,IAAI,sBAAsB,EAAE,CAAA;QACzC,CAAC;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAKD,iBAAiB;QACf,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO,IAAI,CAAA;QAG9C,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;YAC3D,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;gBACvC,IAAI,OAAO,IAAI,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBAC5D,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAA;gBACvC,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;QACxD,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YAC7C,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBACjC,IAAI,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;oBACpB,OAAO,IAAI,CAAC,gBAAgB,CAAC;wBAC3B,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,IAAI,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,IAAI,MAAM;wBACjD,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI;wBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;qBACxB,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAA;QAC7D,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,WAAW,CAAC,OAA4B;QACtC,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAM;QAEzC,IAAI,CAAC;YAEH,MAAM,oBAAoB,GAAG;gBAC3B,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI;gBAChD,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,SAAS;gBACrD,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;aAClC,CAAA;YAGD,MAAM,iBAAiB,GAAG;gBACxB,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,IAAI;gBAC1C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI;gBAChD,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,EAAE,aAAa;gBAClD,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,MAAM;gBAC5B,KAAK,EAAE,CAAC;gBACR,EAAE,EAAE,CAAC;gBACL,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE;gBACzC,KAAK,EAAE;oBACL,UAAU,EAAE,CAAC;oBACb,OAAO,EAAE,CAAC;oBACV,YAAY,EAAE,CAAC;oBACf,UAAU,EAAE,CAAC;oBACb,WAAW,EAAE,CAAC;oBACd,YAAY,EAAE,CAAC;oBACf,IAAI,EAAE,CAAC;oBACP,YAAY,EAAE,cAAc;iBAC7B;gBACD,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE,EAAE;gBACb,QAAQ,EAAE;oBACR,kBAAkB,EAAE,IAAI;oBACxB,YAAY,EAAE,IAAI;oBAClB,QAAQ,EAAE,IAAI;oBACd,SAAS,EAAE,IAAI;oBACf,aAAa,EAAE,IAAI;oBACnB,UAAU,EAAE,KAAK;iBAClB;aACF,CAAA;YAED,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;QACpF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,OAA4B;QAC/C,IAAI,CAAC;YAEH,MAAM,qBAAqB,GAAG,MAAM,KAAK,CAAC,kBAAkB,CAAC,CAAA;YAC7D,IAAI,YAAY,GAAG,IAAI,CAAA;YAEvB,IAAI,qBAAqB,CAAC,EAAE,EAAE,CAAC;gBAC7B,MAAM,IAAI,GAAG,MAAM,qBAAqB,CAAC,IAAI,EAAE,CAAA;gBAC/C,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBAC/B,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CACxC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE;wBACnB,CAAC,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ;wBAC/B,CAAC,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAC1B,CAAA;gBACH,CAAC;YACH,CAAC;YAED,IAAI,YAAY,EAAE,CAAC;gBAEjB,OAAO,CAAC,GAAG,CAAC,4CAA4C,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAA;gBAChF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,kBAAkB,EAAE;oBAC/C,MAAM,EAAE,OAAO;oBACf,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;wBACnB,MAAM,EAAE,YAAY,CAAC,EAAE;wBACvB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,MAAM;qBAC7B,CAAC;iBACH,CAAC,CAAA;gBAEF,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;oBAChB,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;oBAChF,OAAO,IAAI,CAAA;gBACb,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;oBAC7E,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;iBAAM,CAAC;gBAEN,OAAO,CAAC,GAAG,CAAC,uCAAuC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;gBACtF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,kBAAkB,EAAE;oBAC/C,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;wBACnB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI;wBAChD,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,IAAI,IAAI,QAAQ,OAAO,CAAC,EAAE,EAAE;wBAClE,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,EAAE,aAAa;wBAClD,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,MAAM;wBAC5B,WAAW,EAAE,QAAQ;qBACtB,CAAC;iBACH,CAAC,CAAA;gBAEF,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;oBAChB,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;oBAClC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;wBAE9B,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;wBACzB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;oBAC3B,CAAC;oBACD,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;oBAC/E,OAAO,IAAI,CAAA;gBACb,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;oBAC3E,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAA;YAC1D,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAKD,kBAAkB;QAChB,MAAM,YAAY,GAAwB;YACxC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACvE,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,OAAO;YACpB,MAAM,EAAE,IAAI;YACZ,eAAe,EAAE,SAAS;YAC1B,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,MAAM;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAA;QAED,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAA;QAC9B,OAAO,YAAY,CAAA;IACrB,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,OAAiB;QACpD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,kBAAkB,EAAE;gBAC/C,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,MAAM;oBACN,IAAI,EAAE,OAAO;iBACd,CAAC;aACH,CAAC,CAAA;YAEF,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;gBAEhB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;gBAC/C,IAAI,cAAc,IAAI,cAAc,CAAC,EAAE,KAAK,MAAM,EAAE,CAAC;oBACnD,cAAc,CAAC,IAAI,GAAG,OAAO,CAAA;oBAC7B,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAA;gBAClC,CAAC;gBACD,OAAO,IAAI,CAAA;YACb,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;YACnD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,kBAAkB,CAAC,CAAA;YAChD,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;YAElC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO,IAAI,CAAC,KAAK,CAAA;YACnB,CAAC;YACD,OAAO,EAAE,CAAA;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAA;YAC5C,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAKO,gBAAgB,CAAC,OAAY;QACnC,OAAO;YACL,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ,IAAI,MAAM;YAChD,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ;YACpE,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,IAAI;YAC9B,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,SAAS;YACrD,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;YACjC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,MAAM;YAC5B,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,IAAI;YAC1C,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;YACvE,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;SAC3E,CAAA;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,MAAe;QAClC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,kBAAkB,CAAC,CAAA;YAChD,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;YAElC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;gBAC/C,IAAI,CAAC,cAAc,IAAI,CAAC,MAAM;oBAAE,OAAO,IAAI,CAAA;gBAE3C,MAAM,YAAY,GAAG,MAAM,IAAI,cAAc,EAAE,EAAE,CAAA;gBACjD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CACxC,CAAC,CAAC,EAAE,KAAK,YAAY;oBACrB,CAAC,CAAC,QAAQ,KAAK,cAAc,EAAE,QAAQ;oBACvC,CAAC,CAAC,KAAK,KAAK,cAAc,EAAE,KAAK,CAClC,CAAA;gBAED,IAAI,MAAM,IAAI,cAAc,EAAE,CAAC;oBAE7B,cAAc,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;oBACjC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAA;oBAEhC,OAAO,CAAC,GAAG,CAAC,iDAAiD,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;oBAC3E,OAAO,MAAM,CAAC,IAAI,CAAA;gBACpB,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;YACnD,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB;QACrB,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAEtC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEb,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACrC,CAAC;QAGD,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;QAElC,OAAO,OAAO,CAAA;IAChB,CAAC;CACF;AAjUD,wDAiUC"}