import type { MPDStatus, MPDTrack } from '@/lib/types';
export declare class MPDStatusCache {
    private static instance;
    private cache;
    private readonly CACHE_TTL_MS;
    private constructor();
    static getInstance(): MPDStatusCache;
    get(key?: string): {
        status: MPDStatus;
        currentSong: MPDTrack | null;
    } | null;
    set(status: MPDStatus, currentSong: MPDTrack | null, key?: string): void;
    clear(): void;
    cleanup(): void;
}
