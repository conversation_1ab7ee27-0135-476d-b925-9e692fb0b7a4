"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CookieAuth = void 0;
exports.getClientUserInfo = getClientUserInfo;
const getCookies = async () => {
    if (typeof window === 'undefined') {
        const { cookies } = await Promise.resolve().then(() => __importStar(require('next/headers')));
        return cookies;
    }
    return null;
};
const jose_1 = require("jose");
const getJWTSecret = () => {
    if (typeof window !== 'undefined') {
        return new TextEncoder().encode('client-placeholder');
    }
    const secret = process.env.JWT_SECRET;
    if (!secret) {
        throw new Error('JWT_SECRET environment variable is required on server');
    }
    return new TextEncoder().encode(secret);
};
const shouldUseSecureCookies = () => {
    if (process.env.NODE_ENV !== 'production')
        return false;
    if (!process.env.NEXT_PUBLIC_APP_URL)
        return false;
    return process.env.NEXT_PUBLIC_APP_URL.startsWith('https://');
};
const COOKIE_OPTIONS = {
    httpOnly: true,
    secure: shouldUseSecureCookies(),
    sameSite: 'lax',
    path: '/',
    maxAge: 60 * 60 * 24 * 7
};
class CookieAuth {
    static async setAuthCookie(payload) {
        const cookieStore = await getCookies();
        if (!cookieStore) {
            throw new Error('Cookies are only available on the server');
        }
        const token = await new jose_1.SignJWT(payload)
            .setProtectedHeader({ alg: 'HS256' })
            .setIssuedAt()
            .setExpirationTime('7d')
            .sign(getJWTSecret());
        cookieStore().set(this.TOKEN_NAME, token, COOKIE_OPTIONS);
        const userInfo = {
            id: payload.id,
            username: payload.username,
            role: payload.role
        };
        cookieStore().set(this.USER_INFO_NAME, JSON.stringify(userInfo), {
            ...COOKIE_OPTIONS,
            httpOnly: false
        });
    }
    static async getAuthFromCookie() {
        const cookiesFunc = await getCookies();
        if (!cookiesFunc) {
            return null;
        }
        const cookieStore = await cookiesFunc();
        const token = cookieStore.get(this.TOKEN_NAME)?.value;
        if (!token) {
            return null;
        }
        try {
            const { payload } = await (0, jose_1.jwtVerify)(token, getJWTSecret());
            return payload;
        }
        catch (error) {
            console.error('Invalid token:', error);
            return null;
        }
    }
    static async clearAuthCookies() {
        const cookieStore = await getCookies();
        if (!cookieStore) {
            throw new Error('Cookies are only available on the server');
        }
        cookieStore().delete(this.TOKEN_NAME);
        cookieStore().delete(this.USER_INFO_NAME);
    }
    static async refreshToken() {
        const payload = await this.getAuthFromCookie();
        if (!payload) {
            return false;
        }
        await this.setAuthCookie(payload);
        return true;
    }
}
exports.CookieAuth = CookieAuth;
CookieAuth.TOKEN_NAME = 'auth_token';
CookieAuth.USER_INFO_NAME = 'user_info';
function getClientUserInfo() {
    if (typeof window === 'undefined') {
        return null;
    }
    try {
        const userInfoCookie = document.cookie
            .split('; ')
            .find(row => row.startsWith('user_info='))
            ?.split('=')[1];
        if (!userInfoCookie) {
            return null;
        }
        return JSON.parse(decodeURIComponent(userInfoCookie));
    }
    catch (error) {
        console.error('Failed to parse user info:', error);
        return null;
    }
}
//# sourceMappingURL=auth-cookies.js.map