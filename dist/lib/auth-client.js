"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthClient = void 0;
const auth_cookies_client_1 = require("./auth-cookies-client");
const api_client_1 = require("./api-client");
class AuthClient {
    static isAuthenticated() {
        return !!(0, auth_cookies_client_1.getClientUserInfo)();
    }
    static getCurrentUser() {
        return (0, auth_cookies_client_1.getClientUserInfo)();
    }
    static async validateSession() {
        try {
            const result = await api_client_1.api.post('/api/auth/validate');
            return result;
        }
        catch (error) {
            console.error('Session validation failed:', error);
            return { success: false };
        }
    }
    static async login(email, password) {
        try {
            const result = await api_client_1.api.post('/api/auth/login', { email, password });
            return result;
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '<PERSON><PERSON> failed'
            };
        }
    }
    static async register(data) {
        try {
            const result = await api_client_1.api.post('/api/auth/register', data);
            return result;
        }
        catch (error) {
            return {
                success: false,
                message: error.message || 'Registration failed'
            };
        }
    }
    static async logout() {
        try {
            await api_client_1.api.post('/api/auth/logout');
        }
        catch (error) {
            console.error('Logout error:', error);
        }
    }
    static async getProfile() {
        try {
            const result = await api_client_1.api.get('/api/auth/profile');
            return result;
        }
        catch (error) {
            console.error('Profile fetch failed:', error);
            return { success: false };
        }
    }
    static async updateProfile(updates) {
        try {
            const result = await api_client_1.api.put('/api/auth/profile', updates);
            return result;
        }
        catch (error) {
            console.error('Profile update failed:', error);
            return { success: false };
        }
    }
    static hasRole(role) {
        const user = this.getCurrentUser();
        if (!user)
            return false;
        if (role === 'user')
            return true;
        if (role === 'dj')
            return user.role === 'dj' || user.role === 'superuser';
        if (role === 'superuser')
            return user.role === 'superuser';
        return false;
    }
    static isAdmin() {
        return this.hasRole('superuser');
    }
    static isDJOrAdmin() {
        return this.hasRole('dj');
    }
}
exports.AuthClient = AuthClient;
//# sourceMappingURL=auth-client.js.map