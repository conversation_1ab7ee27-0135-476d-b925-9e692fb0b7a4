"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameStore = void 0;
class GameStore {
    constructor() {
        this.games = new Map();
    }
    generateGamePin() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let pin;
        do {
            pin = '';
            for (let i = 0; i < 4; i++) {
                pin += chars.charAt(Math.floor(Math.random() * chars.length));
            }
        } while (this.games.has(pin));
        return pin;
    }
    setGame(gameId, game) {
        this.games.set(gameId, game);
    }
    getGame(gameId) {
        return this.games.get(gameId);
    }
    getAvailableGames() {
        return Array.from(this.games.values());
    }
    deleteGame(gameId) {
        return this.games.delete(gameId);
    }
    getGamesByStatus(status) {
        return Array.from(this.games.values()).filter(game => game.status === status);
    }
    cleanupOldGames(maxAgeMs = 24 * 60 * 60 * 1000) {
        const now = Date.now();
        const gamesToDelete = [];
        for (const [gameId, game] of this.games.entries()) {
            if (now - game.createdAt > maxAgeMs) {
                gamesToDelete.push(gameId);
            }
        }
        gamesToDelete.forEach(gameId => this.deleteGame(gameId));
        if (gamesToDelete.length > 0) {
            console.log(`🧹 Cleaned up ${gamesToDelete.length} old games`);
        }
    }
    getStats() {
        const games = Array.from(this.games.values());
        return {
            totalGames: games.length,
            waitingGames: games.filter(g => g.status === 'waiting').length,
            activeGames: games.filter(g => g.status === 'playing').length,
            completedGames: games.filter(g => g.status === 'finished').length,
            totalPlayers: games.reduce((sum, g) => sum + g.players.length, 0)
        };
    }
}
exports.GameStore = GameStore;
//# sourceMappingURL=game-store.js.map