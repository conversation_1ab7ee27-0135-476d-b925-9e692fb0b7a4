{"version": 3, "file": "multiplayer-question-fallback.js", "sourceRoot": "", "sources": ["../../lib/multiplayer-question-fallback.ts"], "names": [], "mappings": ";;;AA2BA,MAAa,2BAA2B;IAAxC;QACU,sBAAiB,GAAuB;YAC9C;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,2CAA2C;gBACrD,OAAO,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,CAAC;gBAClE,aAAa,EAAE,iBAAiB;gBAChC,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE;gBACV,UAAU,EAAE,CAAC;aACd;YACD;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,oDAAoD;gBAC9D,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;gBAC7C,aAAa,EAAE,OAAO;gBACtB,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE;gBACV,UAAU,EAAE,CAAC;aACd;YACD;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,2CAA2C;gBACrD,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;gBAC1B,aAAa,EAAE,MAAM;gBACrB,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,CAAC;gBACT,UAAU,EAAE,CAAC;aACd;YACD;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,sDAAsD;gBAChE,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;gBAC7C,aAAa,EAAE,QAAQ;gBACvB,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,CAAC;gBACT,UAAU,EAAE,CAAC;aACd;YACD;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,iEAAiE;gBAC3E,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC;gBAC7C,aAAa,EAAE,MAAM;gBACrB,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE;gBACV,UAAU,EAAE,CAAC;aACd;SACF,CAAA;IAiMH,CAAC;IA5LC,oBAAoB,CAAC,QAAgB,CAAC;QAEpC,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAA;QAC5E,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAA;IAC1E,CAAC;IAKD,yBAAyB;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAC7E,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;QAExD,OAAO;YACL,GAAG,YAAY;YACf,EAAE,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACxE,SAAS,EAAE,EAAE;SACd,CAAA;IACH,CAAC;IAKD,uBAAuB,CACrB,oBAA2B,EAC3B,aAAsB,EACtB,SAAiB,EACjB,QAAuB;QAMvB,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO;gBACL,aAAa,EAAE,KAAK;gBACpB,iBAAiB,EAAE,EAAE;gBACrB,MAAM,EAAE,yBAAyB;aAClC,CAAA;QACH,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;gBACL,aAAa,EAAE,KAAK;gBACpB,iBAAiB,EAAE,EAAE;gBACrB,MAAM,EAAE,mCAAmC;aAC5C,CAAA;QACH,CAAC;QAED,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YACvD,OAAO;gBACL,aAAa,EAAE,KAAK;gBACpB,iBAAiB,EAAE,EAAE;gBACrB,MAAM,EAAE,0BAA0B;aACnC,CAAA;QACH,CAAC;QAED,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAA;YAEpF,OAAO;gBACL,aAAa,EAAE,IAAI;gBACnB,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;gBAC/C,MAAM,EAAE,iCAAiC;aAC1C,CAAA;QACH,CAAC;QAED,OAAO;YACL,aAAa,EAAE,KAAK;YACpB,iBAAiB,EAAE,EAAE;YACrB,MAAM,EAAE,qBAAqB;SAC9B,CAAA;IACH,CAAC;IAKD,6BAA6B,CAAC,gBAAkC;QAC9D,OAAO;YACL,EAAE,EAAE,gBAAgB,CAAC,EAAE;YACvB,IAAI,EAAE,gBAAgB,CAAC,IAAI;YAC3B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;YACnC,OAAO,EAAE,gBAAgB,CAAC,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,gBAAgB,CAAC,aAAa;YAC7C,KAAK,EAAE,gBAAgB,CAAC,KAAK,IAAI,IAAI;YACrC,SAAS,EAAE,gBAAgB,CAAC,KAAK,EAAE,IAAI,IAAI,IAAI;YAC/C,UAAU,EAAE,gBAAgB,CAAC,KAAK,EAAE,KAAK,IAAI,IAAI;YACjD,MAAM,EAAE,gBAAgB,CAAC,KAAK,EAAE,MAAM,IAAI,IAAI;YAC9C,KAAK,EAAE,gBAAgB,CAAC,KAAK,EAAE,KAAK,IAAI,IAAI;YAC5C,IAAI,EAAE,gBAAgB,CAAC,KAAK,EAAE,IAAI,IAAI,IAAI;YAC1C,KAAK,EAAE,UAAU;YACjB,UAAU,EAAE,gBAAgB,CAAC,UAAU;YACvC,SAAS,EAAE,gBAAgB,CAAC,SAAS;YACrC,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,QAAQ,EAAE,CAAC;YACX,aAAa,EAAE,IAAI;SACpB,CAAA;IACH,CAAC;IAKD,qBAAqB,CACnB,uBAAmD,EACnD,6BAAsD,EACtD,YAAqC,EACrC,eAA2C;QAE3C,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAA;QAGpE,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAA;QACtD,MAAM,aAAa,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAA;QAGvF,uBAAuB,CAAC,aAAa,CAAC,CAAA;QAEtC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,6BAA6B,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAA;YAC/C,YAAY,CAAC,SAAS,CAAC,CAAA;YACvB,eAAe,CAAC,WAAW,CAAC,CAAA;YAE5B,OAAO,CAAC,GAAG,CAAC,mEAAmE,CAAC,CAAA;QAClF,CAAC;IACH,CAAC;IAKD,iBAAiB,CAAC,QAA0B;QAC1C,OAAO;YACL,GAAG,QAAQ;YACX,UAAU,EAAE;gBACV,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;gBACvB,MAAM,EAAE,gCAAgC;gBACxC,MAAM,EAAE,oBAAoB;aAC7B;SACF,CAAA;IACH,CAAC;IAKD,gBAAgB,CAAC,QAAa;QAC5B,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAA;QAC3B,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAA;QACpD,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,iBAAiB,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,KAAK,CAAA;QACxG,IAAI,QAAQ,CAAC,IAAI,KAAK,iBAAiB,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YAAE,OAAO,KAAK,CAAA;QAC3G,IAAI,CAAC,QAAQ,CAAC,aAAa;YAAE,OAAO,KAAK,CAAA;QAEzC,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,2BAA2B,CACzB,oBAA4B,EAC5B,cAAsB,EACtB,oBAA2B;QAM3B,IAAI,oBAAoB,IAAI,cAAc,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAE9E,OAAO;gBACL,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,yBAAyB;aACnC,CAAA;QACH,CAAC;QAED,IAAI,oBAAoB,GAAG,cAAc,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAE/E,OAAO;gBACL,MAAM,EAAE,QAAQ;gBAChB,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,oBAAoB,CAAC,CAAC;gBAC3F,OAAO,EAAE,8DAA8D;aACxE,CAAA;QACH,CAAC;QAED,OAAO;YACL,MAAM,EAAE,UAAU;YAClB,OAAO,EAAE,0BAA0B;SACpC,CAAA;IACH,CAAC;CACF;AArPD,kEAqPC;AAGY,QAAA,2BAA2B,GAAG,IAAI,2BAA2B,EAAE,CAAA"}