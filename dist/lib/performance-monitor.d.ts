declare class PerformanceMonitor {
    private static instance;
    private metrics;
    private renderMetrics;
    private isEnabled;
    private maxMetrics;
    private maxRenderMetrics;
    private constructor();
    static getInstance(): PerformanceMonitor;
    private initializeObservers;
    recordMetric(name: string, value: number, unit?: string): void;
    recordRender(componentName: string, renderTime: number, props?: Record<string, any>): void;
    measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T>;
    measure<T>(name: string, fn: () => T): T;
    getMetricsSummary(metricName?: string): Record<string, any>;
    getRenderSummary(componentName?: string): Record<string, any>;
    private calculateStats;
    logReport(): void;
    clear(): void;
    setEnabled(enabled: boolean): void;
}
export declare const performanceMonitor: PerformanceMonitor;
export declare const recordMetric: (name: string, value: number, unit?: string) => void;
export declare const recordRender: (componentName: string, renderTime: number, props?: Record<string, any>) => void;
export declare const measureAsync: <T>(name: string, fn: () => Promise<T>) => Promise<T>;
export declare const measure: <T>(name: string, fn: () => T) => T;
export declare function useRenderMetrics(componentName: string, props?: Record<string, any>): void;
export {};
