"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketClient = void 0;
exports.getSocketClient = getSocketClient;
const socket_io_client_1 = require("socket.io-client");
const network_1 = require("./utils/network");
const network_fallback_1 = require("./utils/network-fallback");
class SocketClient {
    constructor() {
        this.socket = null;
        this.connectionAttempts = 0;
        this.maxReconnectAttempts = 3;
        this.isConnecting = false;
        (0, network_fallback_1.initNetworkMonitoring)();
        this.initializeConnection();
    }
    async initializeConnection() {
        if (this.isConnecting)
            return;
        this.isConnecting = true;
        try {
            const socketUrl = await this.getSocketUrl();
            console.log('🔌 Connecting to socket server:', socketUrl);
            const isMobile = (0, network_1.isMobileNetwork)();
            const transports = isMobile ? ['polling'] : ['polling', 'websocket'];
            console.log('🔌 Network detection:', {
                isMobileNetwork: isMobile,
                transports,
                hostname: window.location.hostname
            });
            this.socket = (0, socket_io_client_1.io)(socketUrl, {
                transports,
                upgrade: !isMobile,
                timeout: isMobile ? 30000 : 15000,
                autoConnect: true,
                reconnection: true,
                reconnectionAttempts: this.maxReconnectAttempts,
                reconnectionDelay: isMobile ? 5000 : 2000,
                forceNew: true,
                auth: {
                    timestamp: Date.now()
                }
            });
            this.setupEventListeners();
            this.isConnecting = false;
        }
        catch (error) {
            console.error('Failed to initialize socket connection:', error);
            this.isConnecting = false;
        }
    }
    async getSocketUrl() {
        const networkStatus = (0, network_fallback_1.getNetworkStatus)();
        if (networkStatus.fallbackMode) {
            console.log('🔌 Network in fallback mode, using adaptive URL');
            return (0, network_fallback_1.getAdaptiveSocketUrl)();
        }
        try {
            const response = await fetch('/api/socket-url');
            const data = await response.json();
            if (data.success && data.url) {
                console.log('🔌 Socket URL from API:', data.url);
                return data.url;
            }
            return (0, network_fallback_1.getAdaptiveSocketUrl)();
        }
        catch (error) {
            console.warn('Failed to get socket URL from API, using adaptive fallback');
            return (0, network_fallback_1.getAdaptiveSocketUrl)();
        }
    }
    setupEventListeners() {
        if (!this.socket)
            return;
        this.socket.on('connect', () => {
            console.log('🔌 Connected to server:', this.socket?.id);
            this.connectionAttempts = 0;
        });
        this.socket.on('disconnect', (reason) => {
            console.log('🔌 Disconnected from server:', reason);
        });
        this.socket.on('connect_error', (error) => {
            console.error('🔌 Connection error:', error);
            this.connectionAttempts++;
            if (this.connectionAttempts >= this.maxReconnectAttempts) {
                console.error('Max reconnection attempts reached');
            }
        });
    }
    createGame(hostId, hostName, gameMode) {
        if (!this.socket?.connected) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('create-game', { hostId, hostName, gameMode });
    }
    joinGame(gameId, playerId, playerName, playerAvatar) {
        if (!this.socket?.connected) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('join-game', { gameId, playerId, playerName, playerAvatar });
    }
    startGame(gameId) {
        if (!this.socket?.connected) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('start-game', { gameId });
    }
    submitAnswer(gameId, playerId, answerIndex, timeTaken) {
        if (!this.socket?.connected) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('submit-answer', { gameId, playerId, answerIndex, timeTaken });
    }
    nextQuestion(gameId) {
        if (!this.socket?.connected) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('next-question', { gameId });
    }
    leaveGame(gameId, playerId) {
        if (!this.socket?.connected) {
            throw new Error('Socket not connected');
        }
        this.socket.emit('leave-game', { gameId, playerId });
    }
    on(event, callback) {
        if (!this.socket)
            return;
        this.socket.on(event, callback);
    }
    off(event, callback) {
        if (!this.socket)
            return;
        this.socket.off(event, callback);
    }
    isConnected() {
        return this.socket?.connected || false;
    }
    getConnectionId() {
        return this.socket?.id;
    }
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
    }
}
exports.SocketClient = SocketClient;
let socketClientInstance = null;
function getSocketClient() {
    if (!socketClientInstance) {
        socketClientInstance = new SocketClient();
    }
    return socketClientInstance;
}
//# sourceMappingURL=socket-client.js.map