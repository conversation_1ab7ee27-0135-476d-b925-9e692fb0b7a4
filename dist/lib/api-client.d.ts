export declare class APIError extends Error {
    status: number;
    data?: any | undefined;
    constructor(status: number, message: string, data?: any | undefined);
}
declare class APIClient {
    private static instance;
    private csrfToken;
    private csrfTokenPromise;
    private constructor();
    static getInstance(): APIClient;
    private getCSRFToken;
    private fetchCSRFToken;
    invalidateCSRFToken(): void;
    request<T = any>(url: string, options?: RequestInit): Promise<T>;
    get<T = any>(url: string, options?: RequestInit): Promise<T>;
    post<T = any>(url: string, body?: any, options?: RequestInit): Promise<T>;
    put<T = any>(url: string, body?: any, options?: RequestInit): Promise<T>;
    delete<T = any>(url: string, options?: RequestInit): Promise<T>;
    patch<T = any>(url: string, body?: any, options?: RequestInit): Promise<T>;
}
export declare const apiClient: APIClient;
export declare const api: {
    get: <T = any>(url: string, options?: RequestInit) => Promise<T>;
    post: <T = any>(url: string, body?: any, options?: RequestInit) => Promise<T>;
    put: <T = any>(url: string, body?: any, options?: RequestInit) => Promise<T>;
    delete: <T = any>(url: string, options?: RequestInit) => Promise<T>;
    patch: <T = any>(url: string, body?: any, options?: RequestInit) => Promise<T>;
    request: <T = any>(url: string, options?: RequestInit) => Promise<T>;
};
export {};
