import { Socket } from 'socket.io';
import { VotingType, VotingOption, VotingSession, VotingResult } from './types';
export interface VotingSocketEvents {
    'create-voting': (data: {
        type: VotingType;
        title: string;
        description: string;
        options: VotingOption[];
        timeLimit?: number;
    }) => void;
    'submit-vote': (data: {
        sessionId: string;
        optionIndex: number;
    }) => void;
    'skip-voting': (data: {
        sessionId: string;
    }) => void;
    'get-voting-session': (data: {
        sessionId: string;
    }) => void;
}
export interface VotingServerEvents {
    'voting-session-created': (data: {
        session: VotingSession;
    }) => void;
    'voting-session-updated': (data: {
        session: VotingSession;
    }) => void;
    'vote-submitted': (data: {
        sessionId: string;
        playerId: string;
        optionIndex: number;
        totalVotes: number;
    }) => void;
    'voting-completed': (data: {
        sessionId: string;
        result: VotingResult;
    }) => void;
    'voting-time-update': (data: {
        sessionId: string;
        timeRemaining: number;
    }) => void;
    'voting-stats': (data: {
        sessionId: string;
        totalVotes: number;
        votePercentages: number[];
        participationRate: number;
    }) => void;
}
export declare class VotingSocketHandler {
    private gameVotingSessions;
    private sessionTimers;
    constructor();
    handleConnection(socket: Socket, playerId: string, playerName: string, gameId: string): void;
    handleDisconnection(playerId: string, gameId: string): void;
    startAutomaticVoting(gameId: string, type: VotingType, title: string, description: string, options: VotingOption[], totalPlayers: number, timeLimit: number | undefined, socket: Socket): string;
    getCurrentVotingSession(gameId: string): VotingSession | null;
    hasPlayerVoted(gameId: string, playerId: string): boolean;
    getPlayerVote(gameId: string, playerId: string): number | null;
    private startTimeUpdateTimer;
    private clearTimer;
    private broadcastVotingStats;
    cleanupGame(gameId: string): void;
    createCategoryVoting(gameId: string, totalPlayers: number, socket: Socket): string;
    createDecadeVoting(gameId: string, totalPlayers: number, socket: Socket): string;
    createGameModeVoting(gameId: string, totalPlayers: number, socket: Socket): string;
}
export declare const votingSocketHandler: VotingSocketHandler;
