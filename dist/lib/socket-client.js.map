{"version": 3, "file": "socket-client.js", "sourceRoot": "", "sources": ["../../lib/socket-client.ts"], "names": [], "mappings": ";;;AAsNA,0CAKC;AAvND,uDAA6C;AAE7C,6CAAiD;AACjD,+DAAwG;AA4BxG,MAAa,YAAY;IAMvB;QALQ,WAAM,GAA8C,IAAI,CAAA;QACxD,uBAAkB,GAAG,CAAC,CAAA;QACtB,yBAAoB,GAAG,CAAC,CAAA;QACxB,iBAAY,GAAG,KAAK,CAAA;QAI1B,IAAA,wCAAqB,GAAE,CAAA;QACvB,IAAI,CAAC,oBAAoB,EAAE,CAAA;IAC7B,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,IAAI,IAAI,CAAC,YAAY;YAAE,OAAM;QAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QAExB,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;YAC3C,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAA;YAGzD,MAAM,QAAQ,GAAG,IAAA,yBAAe,GAAE,CAAA;YAClC,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;YAEpE,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE;gBACnC,eAAe,EAAE,QAAQ;gBACzB,UAAU;gBACV,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ;aACnC,CAAC,CAAA;YAEF,IAAI,CAAC,MAAM,GAAG,IAAA,qBAAE,EAAC,SAAS,EAAE;gBAE1B,UAAU;gBACV,OAAO,EAAE,CAAC,QAAQ;gBAClB,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK;gBACjC,WAAW,EAAE,IAAI;gBACjB,YAAY,EAAE,IAAI;gBAClB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;gBAEzC,QAAQ,EAAE,IAAI;gBAEd,IAAI,EAAE;oBACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB;aACF,CAAC,CAAA;YAEF,IAAI,CAAC,mBAAmB,EAAE,CAAA;YAC1B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAA;YAC/D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;QAC3B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY;QAExB,MAAM,aAAa,GAAG,IAAA,mCAAgB,GAAE,CAAA;QAExC,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAA;YAC9D,OAAO,IAAA,uCAAoB,GAAE,CAAA;QAC/B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,iBAAiB,CAAC,CAAA;YAC/C,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;YAElC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;gBAChD,OAAO,IAAI,CAAC,GAAG,CAAA;YACjB,CAAC;YAGD,OAAO,IAAA,uCAAoB,GAAE,CAAA;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAA;YAC1E,OAAO,IAAA,uCAAoB,GAAE,CAAA;QAC/B,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAM;QAExB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;YACvD,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAA;QAC7B,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;YACtC,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAA;QACrD,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,KAAK,EAAE,EAAE;YACxC,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAA;YAC5C,IAAI,CAAC,kBAAkB,EAAE,CAAA;YAEzB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBACzD,OAAO,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAA;YACpD,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAGD,UAAU,CAAC,MAAc,EAAE,QAAgB,EAAE,QAAgB;QAC3D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;QACzC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;IACjE,CAAC;IAED,QAAQ,CAAC,MAAc,EAAE,QAAgB,EAAE,UAAkB,EAAE,YAAqB;QAClF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;QACzC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC,CAAA;IAC/E,CAAC;IAED,SAAS,CAAC,MAAc;QACtB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;QACzC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;IAC5C,CAAC;IAED,YAAY,CAAC,MAAc,EAAE,QAAgB,EAAE,WAAmB,EAAE,SAAiB;QACnF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;QACzC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC,CAAA;IACjF,CAAC;IAED,YAAY,CAAC,MAAc;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;QACzC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;IAC/C,CAAC;IAED,SAAS,CAAC,MAAc,EAAE,QAAgB;QACxC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;QACzC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAA;IACtD,CAAC;IAGD,EAAE,CAA+B,KAAQ,EAAE,QAA8C;QACvF,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAM;QACxB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IACjC,CAAC;IAED,GAAG,CAA+B,KAAQ,EAAE,QAA8C;QACxF,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAM;QACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAClC,CAAC;IAGD,WAAW;QACT,OAAO,IAAI,CAAC,MAAM,EAAE,SAAS,IAAI,KAAK,CAAA;IACxC,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE,CAAA;IACxB,CAAC;IAGD,UAAU;QACR,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAA;YACxB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACpB,CAAC;IACH,CAAC;CACF;AA9KD,oCA8KC;AAGD,IAAI,oBAAoB,GAAwB,IAAI,CAAA;AAEpD,SAAgB,eAAe;IAC7B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC1B,oBAAoB,GAAG,IAAI,YAAY,EAAE,CAAA;IAC3C,CAAC;IACD,OAAO,oBAAoB,CAAA;AAC7B,CAAC"}