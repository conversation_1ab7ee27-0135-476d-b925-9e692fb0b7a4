"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MPDClient = void 0;
const sonner_1 = require("sonner");
class MPDClient {
    constructor(config) {
        this.isConnected = false;
        this.lastErrorToastTime = 0;
        this.errorToastCooldown = 30000;
        this.config = {
            host: config.host,
            port: config.port,
            password: config.password || '',
            httpProxyPort: config.httpProxyPort || 8001,
            timeout: config.timeout || 60000
        };
        if (typeof window !== 'undefined' && typeof document !== 'undefined') {
            window.addEventListener('beforeunload', () => {
                this.disconnect().catch(() => { });
            });
        }
        const proxyHost = (typeof window !== 'undefined' && typeof document !== 'undefined') ? window.location.hostname : config.host;
        this.baseUrl = `http://${proxyHost}:${this.config.httpProxyPort}`;
    }
    async connect() {
        console.log('[MPD] Attempting connection to:', this.baseUrl);
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
            try {
                console.log('[MPD] Sending test status command...');
                await this.getStatus();
                clearTimeout(timeoutId);
                this.isConnected = true;
                console.log('[MPD] Connection successful');
            }
            catch (statusError) {
                clearTimeout(timeoutId);
                console.error('[MPD] Status command failed:', {
                    error: statusError,
                    message: statusError instanceof Error ? statusError.message : String(statusError),
                    type: statusError?.constructor?.name
                });
                throw statusError;
            }
        }
        catch (error) {
            this.isConnected = false;
            console.error('[MPD] Connection failed:', {
                error,
                message: error instanceof Error ? error.message : String(error),
                type: error?.constructor?.name,
                baseUrl: this.baseUrl
            });
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    throw new Error(`MPD connection timeout after ${this.config.timeout}ms`);
                }
                if (error.message.includes('ECONNREFUSED')) {
                    throw new Error(`MPD proxy server not available at ${this.baseUrl}. Please ensure the MPD proxy server is running on port ${this.config.httpProxyPort}`);
                }
                if (error.message.includes('MPD command timeout')) {
                    throw new Error(`MPD server is not responding. Please check:\n1. MPD server is running on ${this.config.host}:${this.config.port}\n2. MPD proxy server is running on port ${this.config.httpProxyPort}\n3. No firewall is blocking the connection`);
                }
            }
            throw new Error(`Could not connect to MPD server: ${error}`);
        }
    }
    getConnectionStatus() {
        return this.isConnected;
    }
    async disconnect() {
        this.isConnected = false;
    }
    async play(songPos) {
        const command = songPos !== undefined ? `play ${songPos}` : 'play';
        await this.sendCommand(command);
    }
    async pause() {
        await this.sendCommand('pause 1');
    }
    async resume() {
        await this.sendCommand('pause 0');
    }
    async stop() {
        await this.sendCommand('stop');
    }
    async next() {
        await this.sendCommand('next');
    }
    async addTrackGetId(file) {
        const response = await this.sendCommand(`addid "${file}"`);
        const match = response.match(/Id:\s*(\d+)/);
        return match ? parseInt(match[1]) : -1;
    }
    async previous() {
        await this.sendCommand('previous');
    }
    async seek(time) {
        await this.sendCommand(`seekcur ${time}`);
    }
    async seekId(songId, time) {
        await this.sendCommand(`seekid ${songId} ${time}`);
    }
    async setVolume(volume) {
        const vol = Math.max(0, Math.min(100, Math.round(volume)));
        try {
            await this.sendCommand(`setvol ${vol}`);
        }
        catch (error) {
            console.error(`[MPD] ❌ Failed to set volume to ${vol}%:`, error);
            throw error;
        }
    }
    async clearPlaylist() {
        try {
            await this.sendCommand('clear');
        }
        catch (error) {
            console.warn('[MPD] Clear playlist error:', error);
            await this.sendCommand('stop').catch(() => { });
            await new Promise(resolve => setTimeout(resolve, 100));
            await this.sendCommand('clear');
        }
    }
    async addTrack(file) {
        await this.sendCommand(`add "${file}"`);
    }
    async addAndPlay(file) {
        await this.clearPlaylist();
        await this.addTrack(file);
        await this.play(0);
    }
    async commandList(commands) {
        const commandListStr = ['command_list_begin', ...commands, 'command_list_end'].join('\n');
        return await this.sendCommand(commandListStr);
    }
    async removeTrack(pos) {
        await this.sendCommand(`delete ${pos}`);
    }
    async moveTrack(from, to) {
        await this.sendCommand(`move ${from} ${to}`);
    }
    async loadPlaylist(name) {
        await this.sendCommand(`load "${name}"`);
    }
    async getPlaylistTracks(name) {
        const response = await this.sendCommand(`listplaylistinfo "${name}"`);
        return this.parseTracks(response);
    }
    async addPlaylistToQueue(name, clearFirst = false) {
        if (clearFirst) {
            await this.clearPlaylist();
        }
        await this.loadPlaylist(name);
    }
    async savePlaylist(name) {
        await this.sendCommand(`save "${name}"`);
    }
    async getCurrentPlaylist() {
        const response = await this.sendCommand('playlistinfo');
        return this.parseTracks(response);
    }
    async getStatus() {
        const response = await this.sendCommand('status');
        return this.parseStatus(response);
    }
    async getCurrentSong() {
        const response = await this.sendCommand('currentsong');
        const tracks = this.parseTracks(response);
        return tracks.length > 0 ? tracks[0] : null;
    }
    async getStats() {
        const response = await this.sendCommand('stats');
        return this.parseKeyValue(response);
    }
    async search(type, query) {
        const response = await this.sendCommand(`search ${type} "${query}"`);
        return this.parseTracks(response);
    }
    async find(type, query) {
        const response = await this.sendCommand(`find ${type} "${query}"`);
        return this.parseTracks(response);
    }
    async listAllTracks() {
        let bestTracks = await this.listAllTracksWithRetries();
        if (bestTracks.length > 200) {
            return bestTracks;
        }
        console.log(`[MPD] Direct approach yielded only ${bestTracks.length} tracks. Trying directory-based approach...`);
        try {
            const directoryTracks = await this.listAllTracksByDirectory();
            if (directoryTracks.length > bestTracks.length) {
                console.log(`[MPD] Directory-based approach found ${directoryTracks.length} tracks (better than ${bestTracks.length})`);
                return directoryTracks;
            }
            else {
                console.log(`[MPD] Directory-based approach found ${directoryTracks.length} tracks (worse than ${bestTracks.length})`);
                return bestTracks;
            }
        }
        catch (error) {
            console.error(`[MPD] Directory-based approach failed:`, error);
            return bestTracks;
        }
    }
    async listAllTracksWithRetries() {
        let retryCount = 0;
        const maxRetries = 3;
        let bestTracks = [];
        while (retryCount < maxRetries) {
            try {
                const response = await this.sendCommand('listallinfo');
                const tracks = this.parseTracks(response);
                if (process.env.DEBUG_MPD || process.env.NODE_ENV === 'development') {
                    console.log(`[MPD] Attempt ${retryCount + 1}: listallinfo response length: ${response.length} chars`);
                    console.log(`[MPD] Attempt ${retryCount + 1}: Parsed ${tracks.length} tracks`);
                }
                if (tracks.length > 200) {
                    return tracks;
                }
                if (tracks.length > bestTracks.length) {
                    bestTracks = tracks;
                }
                if (tracks.length < 100) {
                    console.log(`[MPD] WARNING: Unexpectedly low track count on attempt ${retryCount + 1}! Got ${tracks.length} tracks`);
                    if (tracks.length > 0) {
                        console.log(`[MPD] First few tracks:`);
                        tracks.slice(0, 3).forEach((track, i) => {
                            console.log(`[MPD]   ${i + 1}. ${track.file}`);
                        });
                    }
                }
                retryCount++;
                if (retryCount < maxRetries) {
                    console.log(`[MPD] Retrying in ${retryCount * 1000}ms...`);
                    await new Promise(resolve => setTimeout(resolve, retryCount * 1000));
                }
            }
            catch (error) {
                console.error(`[MPD] Attempt ${retryCount + 1} failed:`, error);
                retryCount++;
                if (retryCount < maxRetries) {
                    console.log(`[MPD] Retrying in ${retryCount * 2000}ms...`);
                    await new Promise(resolve => setTimeout(resolve, retryCount * 2000));
                }
            }
        }
        if (bestTracks.length > 0) {
            console.log(`[MPD] All retries completed. Best result: ${bestTracks.length} tracks`);
        }
        else {
            console.error(`[MPD] All ${maxRetries} attempts failed. No tracks found.`);
        }
        return bestTracks;
    }
    async listAllTracksByDirectory() {
        console.log(`[MPD] Using file-by-file track discovery...`);
        const listAllResponse = await this.sendCommand('listall');
        const lines = listAllResponse.split('\n');
        const allFiles = [];
        for (const line of lines) {
            if (line.startsWith('file: ')) {
                allFiles.push(line.substring(6));
            }
        }
        console.log(`[MPD] Found ${allFiles.length} files to process`);
        const allTracks = [];
        const batchSize = 20;
        for (let i = 0; i < allFiles.length; i += batchSize) {
            const batch = allFiles.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            const totalBatches = Math.ceil(allFiles.length / batchSize);
            console.log(`[MPD] Processing batch ${batchNum}/${totalBatches} (${batch.length} files)...`);
            const batchPromises = batch.map(async (file) => {
                try {
                    const response = await this.sendCommand(`find file "${file}"`);
                    const tracks = this.parseTracks(response);
                    return tracks.length > 0 ? tracks[0] : null;
                }
                catch (error) {
                    console.warn(`[MPD] Failed to get metadata for file ${file}:`, error);
                    return null;
                }
            });
            const batchResults = await Promise.all(batchPromises);
            const validTracks = batchResults.filter(track => track !== null);
            allTracks.push(...validTracks);
            const progress = Math.round((i + batch.length) / allFiles.length * 100);
            console.log(`[MPD] Progress: ${progress}% (${allTracks.length} tracks found)`);
            if (i + batchSize < allFiles.length) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
        console.log(`[MPD] File-by-file discovery complete: ${allTracks.length} total tracks`);
        return allTracks;
    }
    async listArtists() {
        const response = await this.sendCommand('list artist');
        return response.split('\n')
            .filter(line => line.startsWith('Artist: '))
            .map(line => line.substring(8))
            .filter(artist => artist.length > 0);
    }
    async listAlbums() {
        const response = await this.sendCommand('list album');
        return response.split('\n')
            .filter(line => line.startsWith('Album: '))
            .map(line => line.substring(7))
            .filter(album => album.length > 0);
    }
    async listGenres() {
        const response = await this.sendCommand('list genre');
        return response.split('\n')
            .filter(line => line.startsWith('Genre: '))
            .map(line => line.substring(7))
            .filter(genre => genre.length > 0);
    }
    async listPlaylists() {
        const response = await this.sendCommand('listplaylists');
        const playlists = [];
        const lines = response.split('\n');
        let currentPlaylist = {};
        for (const line of lines) {
            if (line.startsWith('playlist: ')) {
                if (currentPlaylist.name) {
                    playlists.push(currentPlaylist);
                }
                currentPlaylist = { name: line.substring(10) };
            }
            else if (line.startsWith('Last-Modified: ')) {
                currentPlaylist.last_modified = line.substring(15);
            }
        }
        if (currentPlaylist.name) {
            playlists.push(currentPlaylist);
        }
        return playlists;
    }
    async setRepeat(enabled) {
        await this.sendCommand(`repeat ${enabled ? '1' : '0'}`);
    }
    async setRandom(enabled) {
        await this.sendCommand(`random ${enabled ? '1' : '0'}`);
    }
    async setSingle(enabled) {
        await this.sendCommand(`single ${enabled ? '1' : '0'}`);
    }
    async setConsume(enabled) {
        await this.sendCommand(`consume ${enabled ? '1' : '0'}`);
    }
    async sendRawCommand(command) {
        return await this.sendCommand(command);
    }
    async sendCommand(command) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
            const requestBody = {
                command,
                password: this.config.password
            };
            const response = await fetch(`${this.baseUrl}/command`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/plain'
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            }).catch(err => {
                if (err instanceof TypeError && err.message.includes('fetch')) {
                    throw new Error(`Cannot connect to MPD proxy at ${this.baseUrl}. Is the proxy server running?`);
                }
                throw err;
            });
            clearTimeout(timeoutId);
            if (!response.ok) {
                const errorText = await response.text().catch(() => 'No error details available');
                console.error(`[MPD] Error response: ${errorText}`);
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            const result = await response.text();
            if (typeof result !== 'string') {
                console.warn('[MPD] Non-string response received:', result);
                return '';
            }
            return result;
        }
        catch (error) {
            if (error instanceof Error && error.name === 'AbortError') {
                const timeoutError = new Error(`MPD command timeout: ${command}`);
                if (command === 'stop') {
                    console.warn('[MPD] Stop command timed out - MPD may be unresponsive');
                    return 'OK';
                }
                throw timeoutError;
            }
            console.error(`[MPD] Command failed: ${command}`, error);
            if (typeof window !== 'undefined' && typeof document !== 'undefined') {
                const now = Date.now();
                const shouldShowToast = now - this.lastErrorToastTime > this.errorToastCooldown;
                if (shouldShowToast) {
                    this.lastErrorToastTime = now;
                    if (error instanceof TypeError && error.message.includes('fetch')) {
                        sonner_1.toast.error('🔌 Music server offline', {
                            description: 'Please check your connection',
                            duration: 5000,
                            style: {
                                background: '#dc2626',
                                color: 'white',
                                borderRadius: '8px',
                            }
                        });
                    }
                    else if (command === 'status') {
                        sonner_1.toast.error('🎵 Cannot reach music player', {
                            description: 'Connection will be retried automatically',
                            duration: 5000,
                            style: {
                                background: '#dc2626',
                                color: 'white',
                                borderRadius: '8px',
                            }
                        });
                    }
                }
            }
            if (error instanceof TypeError && error.message.includes('fetch')) {
                throw new Error(`Cannot connect to MPD proxy server. Please ensure the proxy is running on port ${this.config.httpProxyPort}`);
            }
            throw new Error(`MPD command failed: ${command} - ${error instanceof Error ? error.message : error}`);
        }
    }
    parseStatus(response) {
        if (!response || typeof response !== 'string') {
            console.warn('[MPD] Invalid status response:', response);
            return {
                state: 'stop',
                volume: 0,
                repeat: false,
                random: false,
                single: false,
                consume: false,
                playlist: 0,
                playlistlength: 0
            };
        }
        const data = this.parseKeyValue(response);
        let elapsed;
        let duration;
        if (data.time) {
            const timeParts = data.time.split(':');
            if (timeParts.length >= 2) {
                elapsed = parseFloat(timeParts[0]);
                duration = parseFloat(timeParts[1]);
            }
        }
        return {
            state: data.state || 'stop',
            song: data.song ? parseInt(data.song) : undefined,
            songid: data.songid ? parseInt(data.songid) : undefined,
            time: data.time,
            elapsed,
            duration,
            volume: parseInt(data.volume) || 0,
            repeat: data.repeat === '1',
            random: data.random === '1',
            single: data.single === '1',
            consume: data.consume === '1',
            playlist: parseInt(data.playlist) || 0,
            playlistlength: parseInt(data.playlistlength) || 0,
            bitrate: data.bitrate ? parseInt(data.bitrate) : undefined,
            audio: data.audio,
            updating_db: data.updating_db ? parseInt(data.updating_db) : undefined,
            error: data.error,
            xfade: data.xfade ? parseInt(data.xfade) : undefined
        };
    }
    parseTracks(response) {
        if (!response || typeof response !== 'string') {
            console.warn('[MPD] Invalid tracks response:', response);
            return [];
        }
        const lines = response.split('\n').filter(line => line.length > 0);
        const tracks = [];
        let currentTrack = {};
        for (const line of lines) {
            if (line.startsWith('file: ')) {
                if (currentTrack.file) {
                    tracks.push(currentTrack);
                }
                currentTrack = { file: line.substring(6) };
            }
            else {
                const colonIndex = line.indexOf(': ');
                if (colonIndex > 0) {
                    const key = line.substring(0, colonIndex).toLowerCase();
                    const value = line.substring(colonIndex + 2);
                    if (key === 'time' || key === 'pos' || key === 'id') {
                        currentTrack[key] = parseInt(value);
                    }
                    else {
                        currentTrack[key] = value;
                    }
                }
            }
        }
        if (currentTrack.file) {
            tracks.push(currentTrack);
        }
        return tracks;
    }
    parseKeyValue(response) {
        if (!response || typeof response !== 'string') {
            console.warn('[MPD] Invalid key-value response:', response);
            return {};
        }
        const result = {};
        const lines = response.split('\n');
        for (const line of lines) {
            const colonIndex = line.indexOf(': ');
            if (colonIndex > 0) {
                const key = line.substring(0, colonIndex).toLowerCase();
                const value = line.substring(colonIndex + 2);
                result[key] = value;
            }
        }
        return result;
    }
}
exports.MPDClient = MPDClient;
//# sourceMappingURL=mpd-client.js.map