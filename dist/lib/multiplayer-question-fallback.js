"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.multiplayerQuestionFallback = exports.MultiplayerQuestionFallback = void 0;
class MultiplayerQuestionFallback {
    constructor() {
        this.fallbackQuestions = [
            {
                id: 'fallback-1',
                type: 'multiple-choice',
                question: 'Which artist is known as the King of Pop?',
                options: ['<PERSON>', '<PERSON>', '<PERSON>', 'Madonna'],
                correctAnswer: '<PERSON>',
                timeLimit: 30,
                points: 10,
                difficulty: 2
            },
            {
                id: 'fallback-2',
                type: 'multiple-choice',
                question: 'What decade is known as the golden age of Hip-Hop?',
                options: ['1970s', '1980s', '1990s', '2000s'],
                correctAnswer: '1990s',
                timeLimit: 30,
                points: 10,
                difficulty: 3
            },
            {
                id: 'fallback-3',
                type: 'true-false',
                question: 'The Beatles were from Liverpool, England.',
                options: ['True', 'False'],
                correctAnswer: 'True',
                timeLimit: 20,
                points: 5,
                difficulty: 1
            },
            {
                id: 'fallback-4',
                type: 'multiple-choice',
                question: 'Which instrument is <PERSON><PERSON> famous for playing?',
                options: ['Piano', 'Guitar', 'Drums', 'Bass'],
                correctAnswer: 'Guitar',
                timeLimit: 25,
                points: 8,
                difficulty: 2
            },
            {
                id: 'fallback-5',
                type: 'multiple-choice',
                question: 'What genre originated in New Orleans in the early 20th century?',
                options: ['Blues', 'Jazz', 'Rock', 'Country'],
                correctAnswer: 'Jazz',
                timeLimit: 30,
                points: 12,
                difficulty: 3
            }
        ];
    }
    getFallbackQuestions(count = 5) {
        const shuffled = [...this.fallbackQuestions].sort(() => Math.random() - 0.5);
        return shuffled.slice(0, Math.min(count, this.fallbackQuestions.length));
    }
    generateEmergencyQuestion() {
        const randomIndex = Math.floor(Math.random() * this.fallbackQuestions.length);
        const baseQuestion = this.fallbackQuestions[randomIndex];
        return {
            ...baseQuestion,
            id: `emergency-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            timeLimit: 30
        };
    }
    checkAndProvideFallback(multiplayerQuestions, isMultiplayer, quizState, gameCode) {
        if (!isMultiplayer) {
            return {
                needsFallback: false,
                fallbackQuestions: [],
                reason: 'Not in multiplayer mode'
            };
        }
        if (!gameCode) {
            return {
                needsFallback: false,
                fallbackQuestions: [],
                reason: 'No game code - not joined to game'
            };
        }
        if (quizState !== 'playing' && quizState !== 'waiting') {
            return {
                needsFallback: false,
                fallbackQuestions: [],
                reason: 'Quiz not in active state'
            };
        }
        if (multiplayerQuestions.length === 0) {
            console.warn('[QuestionFallback] No questions loaded, providing fallback questions');
            return {
                needsFallback: true,
                fallbackQuestions: this.getFallbackQuestions(5),
                reason: 'No questions loaded from server'
            };
        }
        return {
            needsFallback: false,
            fallbackQuestions: [],
            reason: 'Questions available'
        };
    }
    createMockMultiplayerQuestion(fallbackQuestion) {
        return {
            id: fallbackQuestion.id,
            type: fallbackQuestion.type,
            question: fallbackQuestion.question,
            options: fallbackQuestion.options || [],
            correctAnswer: fallbackQuestion.correctAnswer,
            track: fallbackQuestion.track || null,
            audioFile: fallbackQuestion.track?.file || null,
            trackTitle: fallbackQuestion.track?.title || null,
            artist: fallbackQuestion.track?.artist || null,
            album: fallbackQuestion.track?.album || null,
            year: fallbackQuestion.track?.year || null,
            genre: 'Fallback',
            difficulty: fallbackQuestion.difficulty,
            timeLimit: fallbackQuestion.timeLimit,
            points: fallbackQuestion.points,
            duration: 0,
            chartPosition: null
        };
    }
    emergencyQuizRecovery(setMultiplayerQuestions, setMultiplayerCurrentQuestion, setQuizState, setQuizSubState) {
        console.warn('[QuestionFallback] Executing emergency quiz recovery');
        const fallbackQuestions = this.getFallbackQuestions(5);
        const mockQuestions = fallbackQuestions.map(q => this.createMockMultiplayerQuestion(q));
        setMultiplayerQuestions(mockQuestions);
        if (mockQuestions.length > 0) {
            setMultiplayerCurrentQuestion(mockQuestions[0]);
            setQuizState('playing');
            setQuizSubState('answering');
            console.log('[QuestionFallback] Emergency quiz started with fallback questions');
        }
    }
    addDiagnosticInfo(question) {
        return {
            ...question,
            diagnostic: {
                isFallback: true,
                generatedAt: Date.now(),
                reason: 'Server question loading failed',
                source: 'emergency-fallback'
            }
        };
    }
    validateQuestion(question) {
        if (!question)
            return false;
        if (!question.id || !question.question)
            return false;
        if (!question.type || !['multiple-choice', 'true-false', 'slider'].includes(question.type))
            return false;
        if (question.type === 'multiple-choice' && (!question.options || question.options.length < 2))
            return false;
        if (!question.correctAnswer)
            return false;
        return true;
    }
    getQuizContinuationStrategy(currentQuestionIndex, totalQuestions, multiplayerQuestions) {
        if (currentQuestionIndex >= totalQuestions && multiplayerQuestions.length > 0) {
            return {
                action: 'complete',
                message: 'Quiz completed normally'
            };
        }
        if (currentQuestionIndex < totalQuestions && multiplayerQuestions.length === 0) {
            return {
                action: 'extend',
                newQuestions: this.getFallbackQuestions(Math.min(3, totalQuestions - currentQuestionIndex)),
                message: 'Extending quiz with fallback questions due to loading issues'
            };
        }
        return {
            action: 'continue',
            message: 'Quiz continuing normally'
        };
    }
}
exports.MultiplayerQuestionFallback = MultiplayerQuestionFallback;
exports.multiplayerQuestionFallback = new MultiplayerQuestionFallback();
//# sourceMappingURL=multiplayer-question-fallback.js.map