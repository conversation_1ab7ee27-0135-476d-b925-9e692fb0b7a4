"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MPDEnhancedClient = void 0;
const mpd_client_1 = require("./mpd-client");
class MPDEnhancedClient extends mpd_client_1.MPDClient {
    constructor(config) {
        super(config);
    }
    async setCrossfade(seconds) {
        const clampedSeconds = Math.max(0, Math.min(30, seconds));
        await this.sendCommand(`crossfade ${clampedSeconds}`);
    }
    async getCrossfade() {
        const status = await this.getStatus();
        return parseInt(status.xfade || '0', 10);
    }
    async setReplayGainMode(mode) {
        await this.sendCommand(`replay_gain_mode ${mode}`);
    }
    async getReplayGainMode() {
        const response = await this.sendCommand('replay_gain_status');
        const lines = response.split('\n');
        for (const line of lines) {
            if (line.startsWith('replay_gain_mode: ')) {
                return line.substring(18);
            }
        }
        return 'off';
    }
    async setReplayGainPreamp(db) {
        const clampedDb = Math.max(-15, Math.min(15, db));
        await this.sendCommand(`replay_gain_missing_preamp ${clampedDb}`);
    }
    async configureAudioEnhancements(config) {
        if (config.crossfade !== undefined) {
            await this.setCrossfade(config.crossfade);
        }
        if (config.replayGain) {
            await this.setReplayGainMode(config.replayGain.mode);
            if (config.replayGain.missingPreamp !== undefined) {
                await this.setReplayGainPreamp(config.replayGain.missingPreamp);
            }
        }
    }
    async getAudioEnhancements() {
        const [crossfade, replayGainMode, status] = await Promise.all([
            this.getCrossfade(),
            this.getReplayGainMode(),
            this.sendCommand('replay_gain_status')
        ]);
        return {
            crossfade,
            replayGain: {
                mode: replayGainMode,
                status
            }
        };
    }
    async applyRecommendedSettings() {
        await this.configureAudioEnhancements({
            crossfade: 3,
            replayGain: {
                mode: 'auto',
                preventClipping: true,
                missingPreamp: -3
            }
        });
    }
}
exports.MPDEnhancedClient = MPDEnhancedClient;
//# sourceMappingURL=mpd-enhanced-client.js.map