"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.teamGameIntegration = exports.TeamGameIntegration = void 0;
const team_scoring_1 = require("./team-scoring");
const team_game_modes_1 = require("./team-game-modes");
class TeamGameIntegration {
    constructor() {
        this.activeGames = new Map();
        this.questionTimers = new Map();
        this.collaborationTimers = new Map();
    }
    initializeTeamGame(gameId, teams, settings, totalQuestions) {
        const gameState = team_game_modes_1.teamGameModeManager.initializeTeamGame(gameId, teams, settings);
        const session = {
            gameId,
            teams: [...teams],
            settings,
            gameState,
            currentQuestion: 0,
            totalQuestions,
            questionStartTime: Date.now(),
            teamAnswers: new Map(),
            teamChatMessages: [],
            scoreHistory: [],
            achievements: new Map()
        };
        this.activeGames.set(gameId, session);
        return session;
    }
    startQuestion(gameId, questionNumber, questionData, timeLimit) {
        const session = this.activeGames.get(gameId);
        if (!session)
            return { success: false, activeTeams: [] };
        session.teamAnswers.clear();
        session.currentQuestion = questionNumber;
        session.questionStartTime = Date.now();
        team_game_modes_1.teamGameModeManager.resetForNewQuestion(gameId);
        if (session.settings.teamGameMode === 'collaborative') {
            session.collaborationStartTime = Date.now();
            this.startCollaborationTimer(gameId, session.settings.collaborationTime);
        }
        const turnInfo = team_game_modes_1.teamGameModeManager.getCurrentTurnInfo(gameId, questionData.category);
        const activeTeams = turnInfo.map(info => info.teamId);
        this.startQuestionTimer(gameId, timeLimit);
        return {
            success: true,
            collaborationTime: session.settings.teamGameMode === 'collaborative'
                ? session.settings.collaborationTime
                : undefined,
            activeTeams,
            currentTurn: turnInfo.find(info => info.canAnswer)?.activePlayerId
        };
    }
    submitTeamAnswer(gameId, teamAnswer) {
        const session = this.activeGames.get(gameId);
        if (!session)
            return { success: false, reason: 'Game not found', allTeamsAnswered: false };
        const player = session.teams
            .find(t => t.id === teamAnswer.teamId)
            ?.players.find(p => p.id === teamAnswer.submittedBy);
        if (!player) {
            return { success: false, reason: 'Player not found', allTeamsAnswered: false };
        }
        const result = team_game_modes_1.teamGameModeManager.handleTeamAnswer(gameId, teamAnswer, session.teams);
        if (result.accepted) {
            session.teamAnswers.set(teamAnswer.teamId, teamAnswer);
            const teamIndex = session.teams.findIndex(t => t.id === teamAnswer.teamId);
            if (teamIndex !== -1) {
                session.teams[teamIndex] = {
                    ...session.teams[teamIndex],
                    hasAnswered: true,
                    lastAnswer: teamAnswer.answer,
                    lastAnswerTime: teamAnswer.timestamp
                };
            }
        }
        const allAnswered = this.checkAllTeamsAnswered(session);
        return {
            success: result.accepted,
            reason: result.reason,
            nextTurn: result.nextTurn,
            allTeamsAnswered: allAnswered
        };
    }
    processQuestionResults(gameId, correctAnswer, questionDifficulty, timeLimit) {
        const session = this.activeGames.get(gameId);
        if (!session)
            return { scoreResults: [], updatedTeams: [], achievements: [] };
        const teamAnswers = Array.from(session.teamAnswers.values());
        const scoreResults = team_scoring_1.teamScoringSystem.calculateTeamScores(session.teams, teamAnswers, correctAnswer, questionDifficulty, timeLimit, session.settings.teamScoringMode, session.settings.teamGameMode);
        const updatedTeams = team_scoring_1.teamScoringSystem.applyScoreResults(session.teams, scoreResults);
        session.teams = updatedTeams;
        session.scoreHistory.push(scoreResults);
        const achievements = this.checkTeamAchievements(session, scoreResults);
        this.clearQuestionTimers(gameId);
        return {
            scoreResults,
            updatedTeams,
            achievements
        };
    }
    handleTeamChat(gameId, message) {
        const session = this.activeGames.get(gameId);
        if (!session)
            return false;
        const team = session.teams.find(t => t.id === message.teamId);
        const player = team?.players.find(p => p.id === message.playerId);
        if (!team || !player)
            return false;
        session.teamChatMessages.push(message);
        if (session.teamChatMessages.length > 100) {
            session.teamChatMessages = session.teamChatMessages.slice(-100);
        }
        return true;
    }
    getTeamGameSession(gameId) {
        return this.activeGames.get(gameId);
    }
    getTeamLeaderboard(gameId) {
        const session = this.activeGames.get(gameId);
        if (!session)
            return [];
        return team_scoring_1.teamScoringSystem.getTeamLeaderboard(session.teams);
    }
    getTeamChatMessages(gameId, teamId) {
        const session = this.activeGames.get(gameId);
        if (!session)
            return [];
        if (teamId) {
            return session.teamChatMessages.filter(msg => msg.teamId === teamId);
        }
        return session.teamChatMessages;
    }
    isCollaborationTimeActive(gameId) {
        const session = this.activeGames.get(gameId);
        if (!session || !session.collaborationStartTime) {
            return { active: false, timeRemaining: 0 };
        }
        const elapsed = (Date.now() - session.collaborationStartTime) / 1000;
        const timeRemaining = Math.max(0, session.settings.collaborationTime - elapsed);
        return {
            active: timeRemaining > 0,
            timeRemaining: Math.ceil(timeRemaining)
        };
    }
    finalizeTeamGame(gameId) {
        const session = this.activeGames.get(gameId);
        if (!session) {
            return {
                finalTeams: [],
                finalScores: [],
                gameStats: {},
                achievements: new Map()
            };
        }
        const finalScores = session.scoreHistory.flat();
        const gameStats = this.calculateGameStats(session);
        this.cleanupGame(gameId);
        return {
            finalTeams: session.teams,
            finalScores,
            gameStats,
            achievements: session.achievements
        };
    }
    cleanupGame(gameId) {
        this.clearQuestionTimers(gameId);
        this.activeGames.delete(gameId);
        team_game_modes_1.teamGameModeManager.cleanupGame(gameId);
    }
    startQuestionTimer(gameId, timeLimit) {
        const timer = setTimeout(() => {
            this.handleQuestionTimeout(gameId);
        }, timeLimit * 1000);
        this.questionTimers.set(gameId, timer);
    }
    startCollaborationTimer(gameId, collaborationTime) {
        const timer = setTimeout(() => {
            this.handleCollaborationTimeout(gameId);
        }, collaborationTime * 1000);
        this.collaborationTimers.set(gameId, timer);
    }
    clearQuestionTimers(gameId) {
        const questionTimer = this.questionTimers.get(gameId);
        if (questionTimer) {
            clearTimeout(questionTimer);
            this.questionTimers.delete(gameId);
        }
        const collaborationTimer = this.collaborationTimers.get(gameId);
        if (collaborationTimer) {
            clearTimeout(collaborationTimer);
            this.collaborationTimers.delete(gameId);
        }
    }
    handleQuestionTimeout(gameId) {
        const session = this.activeGames.get(gameId);
        if (session) {
        }
    }
    handleCollaborationTimeout(gameId) {
        const session = this.activeGames.get(gameId);
        if (session) {
            delete session.collaborationStartTime;
        }
    }
    checkAllTeamsAnswered(session) {
        const activeTeams = session.teams.filter(team => team.players.length > 0);
        return activeTeams.every(team => team.hasAnswered);
    }
    checkTeamAchievements(session, scoreResults) {
        const achievements = [];
        const perfectTeams = scoreResults.filter(result => result.breakdown.accuracyBonus > 0 &&
            session.teams.find(t => t.id === result.teamId)?.players.length ===
                session.teams.find(t => t.id === result.teamId)?.players.length);
        if (perfectTeams.length > 0) {
            achievements.push('perfect-team');
        }
        const speedAchievements = scoreResults.filter(result => result.breakdown.speedBonus > 30);
        if (speedAchievements.length > 0) {
            achievements.push('speed-demons');
        }
        perfectTeams.forEach(result => {
            if (!session.achievements.has(result.teamId)) {
                session.achievements.set(result.teamId, []);
            }
            session.achievements.get(result.teamId).push('perfect-team');
        });
        return achievements;
    }
    calculateGameStats(session) {
        const totalQuestions = session.currentQuestion + 1;
        const totalTime = Date.now() - session.questionStartTime;
        const avgResponseTime = session.teamAnswers.size > 0
            ? Array.from(session.teamAnswers.values())
                .reduce((sum, answer) => sum + (answer.timestamp - session.questionStartTime), 0) / session.teamAnswers.size
            : 0;
        return {
            totalQuestions,
            totalTime,
            avgResponseTime,
            totalTeams: session.teams.length,
            totalPlayers: session.teams.reduce((sum, team) => sum + team.players.length, 0),
            chatMessages: session.teamChatMessages.length
        };
    }
}
exports.TeamGameIntegration = TeamGameIntegration;
exports.teamGameIntegration = new TeamGameIntegration();
//# sourceMappingURL=team-game-integration.js.map