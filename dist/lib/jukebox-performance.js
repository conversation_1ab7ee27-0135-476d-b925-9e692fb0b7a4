"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.memoizedFilterAndSort = exports.memoizedDeduplicateSongs = exports.jukeboxCategoriesCache = exports.jukeboxLibraryCache = void 0;
exports.loadLibraryWithCache = loadLibraryWithCache;
exports.clearJukeboxCache = clearJukeboxCache;
exports.getJukeboxCacheStats = getJukeboxCacheStats;
const performance_utils_1 = require("@/lib/performance-utils");
const enhanced_duplicate_detection_1 = require("@/lib/enhanced-duplicate-detection");
exports.jukeboxLibraryCache = new performance_utils_1.CacheManager();
exports.jukeboxCategoriesCache = new performance_utils_1.CacheManager();
exports.memoizedDeduplicateSongs = (0, performance_utils_1.memoize)((songs) => {
    const songMap = new Map();
    songs.forEach(song => {
        const key = `${song.title.toLowerCase().trim()}-${song.artist.toLowerCase().trim()}`;
        if (songMap.has(key)) {
            const existing = songMap.get(key);
            songMap.set(key, {
                ...existing,
                votes: (existing.votes || 0) + (song.votes || 0),
                duration: existing.duration || song.duration,
                album: existing.album || song.album,
                year: existing.year || song.year,
                genre: existing.genre || song.genre
            });
        }
        else {
            songMap.set(key, { ...song });
        }
    });
    return Array.from(songMap.values());
});
exports.memoizedFilterAndSort = (0, performance_utils_1.memoize)((library, searchTerm, sortBy, selectedCategory) => {
    let filtered = library.filter(song => {
        const searchLower = searchTerm.toLowerCase().trim();
        if (!searchLower) {
            const matchesCategory = selectedCategory === "all" ||
                (song.categories && song.categories.includes(selectedCategory));
            return matchesCategory;
        }
        const matchesSearch = (song.title || '').toLowerCase().includes(searchLower) ||
            (song.artist || '').toLowerCase().includes(searchLower) ||
            (song.album || '').toLowerCase().includes(searchLower) ||
            (song.genre || '').toLowerCase().includes(searchLower) ||
            (song.year && song.year.toString().includes(searchLower));
        const matchesCategory = selectedCategory === "all" ||
            (song.categories && song.categories.includes(selectedCategory));
        return matchesSearch && matchesCategory;
    });
    filtered.sort((a, b) => {
        switch (sortBy) {
            case "votes":
                return (b.votes || 0) - (a.votes || 0);
            case "title":
                return (a.title || '').localeCompare(b.title || '');
            case "artist":
                return (a.artist || '').localeCompare(b.artist || '');
            case "year":
                return (b.year || 0) - (a.year || 0);
            default:
                return 0;
        }
    });
    return filtered;
});
async function loadLibraryWithCache(mpdClient, setLibrary, setAvailableCategories, setIsLoading, searchTerm, page, append, selectedCategory, selectedGenre, hideChartSongs, hideMyItunes) {
    const currentPage = page || 1;
    let cacheKey = `music-library-page-${currentPage}`;
    if (searchTerm)
        cacheKey += `-search-${searchTerm}`;
    if (selectedCategory && selectedCategory !== 'all')
        cacheKey += `-category-${selectedCategory}`;
    if (selectedGenre && selectedGenre !== 'all')
        cacheKey += `-genre-${selectedGenre}`;
    if (hideChartSongs)
        cacheKey += `-hideCharts`;
    if (hideMyItunes)
        cacheKey += `-hideMyItunes`;
    const categoriesCacheKey = 'music-categories';
    const hasFilters = searchTerm || (selectedCategory && selectedCategory !== 'all') || (selectedGenre && selectedGenre !== 'all') || hideChartSongs || hideMyItunes;
    const cacheTime = hasFilters ? 60000 : 300000;
    const cachedLibrary = exports.jukeboxLibraryCache.get(cacheKey);
    const cachedCategories = exports.jukeboxCategoriesCache.get(categoriesCacheKey);
    if (cachedLibrary && currentPage === 1 && !hasFilters && cachedCategories) {
        setLibrary(cachedLibrary);
        setAvailableCategories(cachedCategories);
        console.log('[Jukebox] Using cached library data');
        return { hasMore: true, totalPages: 1 };
    }
    setIsLoading?.(true);
    try {
        console.log(`[Jukebox] Fetching tracks from database... page ${currentPage}`, { searchTerm, selectedCategory, selectedGenre, hideChartSongs, hideMyItunes, append });
        const url = new URL('/api/quiz/tracks', window.location.origin);
        url.searchParams.set('page', currentPage.toString());
        if (hasFilters) {
            url.searchParams.set('limit', '50');
        }
        else {
            url.searchParams.set('limit', '100');
        }
        if (searchTerm) {
            url.searchParams.set('search', searchTerm);
        }
        if (selectedCategory && selectedCategory !== 'all') {
            url.searchParams.set('category', selectedCategory);
        }
        if (selectedGenre && selectedGenre !== 'all') {
            url.searchParams.set('genre', selectedGenre);
        }
        if (hideChartSongs) {
            url.searchParams.set('hideChartSongs', 'true');
        }
        if (hideMyItunes) {
            url.searchParams.set('hideMyItunes', 'true');
        }
        const response = await fetch(url.toString());
        console.log('[Jukebox] API response status:', response.status);
        console.log('[Jukebox] API response headers:', Object.fromEntries(response.headers.entries()));
        if (response.ok) {
            const responseText = await response.text();
            console.log('[Jukebox] Raw response text:', responseText.substring(0, 200) + '...');
            let responseData;
            try {
                responseData = JSON.parse(responseText);
            }
            catch (parseError) {
                console.error('[Jukebox] JSON parse error:', parseError);
                console.error('[Jukebox] Response text that failed to parse:', responseText);
                throw new Error('Failed to parse API response as JSON');
            }
            console.log('[Jukebox] API response success:', responseData.success);
            const tracks = responseData.tracks || [];
            const pagination = responseData.pagination || {};
            console.log('[Jukebox] Received tracks:', tracks.length, 'Page:', pagination.page, 'Total pages:', pagination.pages, 'Has next:', pagination.hasNext);
            if (tracks.length === 0) {
                console.warn('[Jukebox] No tracks found in database');
            }
            const songLibrary = tracks
                .filter((track) => track.title && track.artist && track.mpdFilePath)
                .map((track, i) => ({
                id: track.id || i,
                title: track.title || 'Unknown Title',
                artist: track.artist || 'Unknown Artist',
                album: track.album || 'Unknown Album',
                duration: track.duration || 0,
                genre: track.genre || 'Unknown',
                year: track.year,
                filePath: track.mpdFilePath,
                votes: 0,
                suggested: false,
                addedBy: 'System',
                addedAt: Date.now(),
                categories: track.quizCategories ? JSON.parse(track.quizCategories) : [],
                albumArtUrl: track.albumArtUrl
            }));
            let categoriesArray = [];
            if (currentPage === 1 || !searchTerm) {
                const allCategories = new Set();
                songLibrary.forEach(song => {
                    if (song.categories) {
                        song.categories.forEach((cat) => allCategories.add(cat));
                    }
                });
                categoriesArray = Array.from(allCategories).sort();
            }
            exports.jukeboxLibraryCache.set(cacheKey, songLibrary, cacheTime);
            if (!searchTerm && currentPage === 1) {
                exports.jukeboxCategoriesCache.set(categoriesCacheKey, categoriesArray, 300000);
            }
            if (append && currentPage > 1) {
                setLibrary((prevLibrary) => {
                    const combined = [...prevLibrary, ...songLibrary];
                    return (0, enhanced_duplicate_detection_1.enhancedDeduplicateSongs)(combined);
                });
            }
            else {
                setLibrary(songLibrary);
            }
            if (categoriesArray.length > 0) {
                setAvailableCategories(categoriesArray);
            }
            console.log(`[Jukebox] Loaded ${songLibrary.length} tracks with categories from database`);
            return {
                hasMore: pagination.hasNext || false,
                totalPages: pagination.pages || 1
            };
        }
        else {
            console.error('[Jukebox] API request failed with status:', response.status);
            const errorText = await response.text();
            console.error('[Jukebox] Error response:', errorText);
            if (mpdClient && currentPage === 1) {
                console.log('[Jukebox] Falling back to MPD...');
                const tracks = await mpdClient.listAllTracks();
                const songLibrary = tracks.map((track, i) => ({
                    id: i,
                    title: track.title || 'Unknown Title',
                    artist: track.artist || 'Unknown Artist',
                    album: track.album,
                    duration: track.time || 0,
                    genre: track.genre,
                    year: track.date ? parseInt(track.date) : undefined,
                    filePath: track.file,
                    votes: 0,
                    suggested: false,
                    addedBy: 'System',
                    addedAt: Date.now(),
                    categories: []
                }));
                exports.jukeboxLibraryCache.set(cacheKey, songLibrary, 60000);
                setLibrary(songLibrary);
                console.log(`[Jukebox] Loaded ${songLibrary.length} tracks from MPD (fallback)`);
                return { hasMore: false, totalPages: 1 };
            }
            else {
                console.error('[Jukebox] No MPD client available for fallback');
                return { hasMore: false, totalPages: 1 };
            }
        }
    }
    catch (error) {
        console.error('[Jukebox] Failed to load library:', error);
        return { hasMore: false, totalPages: 1 };
    }
    finally {
        setIsLoading?.(false);
    }
}
function clearJukeboxCache() {
    exports.jukeboxLibraryCache.clear();
    exports.jukeboxCategoriesCache.clear();
    console.log('[Jukebox] Cache cleared');
}
function getJukeboxCacheStats() {
    return {
        library: exports.jukeboxLibraryCache.size(),
        categories: exports.jukeboxCategoriesCache.size()
    };
}
//# sourceMappingURL=jukebox-performance.js.map