{"version": 3, "file": "album-art-scraper.js", "sourceRoot": "", "sources": ["../../lib/album-art-scraper.ts"], "names": [], "mappings": ";;;;;;AAgTA,wDA8BC;AA9UD,yDAAyD;AAEzD,kDAA0B;AAC1B,2DAA6B;AAC7B,gDAAwB;AASxB,MAAa,uBAAuB;IAIlC,YAAY,QAAQ,GAAG,mBAAmB;QACxC,IAAI,CAAC,UAAU,GAAG,IAAI,sCAAmB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QACnC,MAAM,kBAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;IAEO,WAAW,CAAC,MAAc,EAAE,KAAa;QAC/C,OAAO,GAAG,MAAM,IAAI,KAAK,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;IACvE,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,KAAa;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAC;QAG/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;QAAC,MAAM,CAAC;QAET,CAAC;QAED,MAAM,OAAO,GAAqB,EAAE,CAAC;QACrC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAEhD,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAG3D,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAGtD,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAGvD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvB,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAC9D,CAAC;YAGD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACpB,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBACpD,OAAO,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAGH,MAAM,kBAAE,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YAEvD,OAAO,OAAO,CAAC;QACjB,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,IAAS,EACT,MAAc,EACd,KAAa,EACb,OAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,wCAAwC,kBAAkB,CAC1E,GAAG,MAAM,IAAI,KAAK,EAAE,CACrB,wBAAwB,CAAC;YAE1B,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC;YAGzD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,IAAW,EAAE,EAAE,CACvE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACzB,MAAM,IAAI,GAAG,GAAG,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;gBACvD,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACjC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CACnB,CAAC;YAGF,KAAK,MAAM,UAAU,IAAI,QAAQ,EAAE,CAAC;gBAClC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC;gBAE1D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAC/B,gBAAgB,EAChB,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CACtB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;gBAEpB,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;oBAElD,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;oBAE/D,OAAO,CAAC,IAAI,CAAC;wBACX,GAAG,EAAE,UAAU;wBACf,MAAM,EAAE,aAAa;wBACrB,OAAO,EAAE,MAAM;wBACf,UAAU,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;qBACxC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,IAAS,EACT,MAAc,EACd,KAAa,EACb,OAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,6BAA6B,kBAAkB,CAC/D,MAAM,CACP,IAAI,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YAEjC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC;YAEzD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAC/B,+BAA+B,EAC/B,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CACtB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;YAEpB,IAAI,QAAQ,EAAE,CAAC;gBAEb,MAAM,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACrC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;oBAC9C,OAAO,CAAC,IAAI,CAAC;wBACX,GAAG;wBACH,MAAM,EAAE,QAAQ;wBAChB,OAAO,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;wBAC/C,UAAU,EAAE;4BACV,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;4BACnC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;yBACrC;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,IAAS,EACT,MAAc,EACd,KAAa,EACb,OAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,qCAAqC,kBAAkB,CACvE,GAAG,MAAM,IAAI,KAAK,EAAE,CACrB,WAAW,CAAC;YAEb,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC;YAGzD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC;YAC3D,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,WAAW,CAAC,KAAK,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBAE3C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAC9B,oBAAoB,EACpB,CAAC,IAAW,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACtD,CAAC;gBAEF,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;oBACzB,OAAO,CAAC,IAAI,CAAC;wBACX,GAAG;wBACH,MAAM,EAAE,SAAS;wBACjB,OAAO,EAAE,MAAM;qBAChB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,IAAS,EACT,MAAc,EACd,KAAa,EACb,OAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,mCAAmC,kBAAkB,CACrE,GAAG,MAAM,IAAI,KAAK,cAAc,CACjC,qBAAqB,CAAC;YAEvB,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC;YAGzD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAC9B,sCAAsC,EACtC,CAAC,IAAW,EAAE,EAAE,CACd,IAAI;iBACD,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;iBACnB,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;iBAC9C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACjB,CAAC;YAEF,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;gBACzB,OAAO,CAAC,IAAI,CAAC;oBACX,GAAG;oBACH,MAAM,EAAE,QAAQ;oBAChB,OAAO,EAAE,QAAQ;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,QAAgB,EAChB,UAAkB,EAClB,OAKC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAEhD,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;gBAChC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC1C,CAAC;YAGD,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAGrC,IAAI,aAAa,GAAG,IAAA,eAAK,EAAC,MAAM,CAAC,CAAC;YAElC,IAAI,OAAO,EAAE,KAAK,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;gBACtC,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE;oBAClE,GAAG,EAAE,OAAO;oBACZ,QAAQ,EAAE,QAAQ;iBACnB,CAAC,CAAC;YACL,CAAC;YAED,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;gBACpB,aAAa,GAAG,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE;oBACrD,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;iBAC/B,CAAC,CAAC;YACL,CAAC;YAED,MAAM,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACzC,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,SAAiB;QAM1C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;YAEnD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,MAAM,EAAE,QAAQ,CAAC,MAAM;aACxB,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;CACF;AAhSD,0DAgSC;AAGM,KAAK,UAAU,sBAAsB,CAC1C,MAAc,EACd,KAAa,EACb,YAAqB;IAErB,MAAM,OAAO,GAAG,IAAI,uBAAuB,EAAE,CAAC;IAE9C,IAAI,CAAC;QACH,MAAM,OAAO,CAAC,UAAU,EAAE,CAAC;QAC3B,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAE5D,IAAI,YAAY,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAEvC,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,OAAO,CAAC,uBAAuB,CACnC,UAAU,CAAC,GAAG,EACd,YAAY,EACZ;gBACE,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,EAAE;aACZ,CACF,CAAC;QACJ,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;YAAS,CAAC;QACT,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;AACH,CAAC"}