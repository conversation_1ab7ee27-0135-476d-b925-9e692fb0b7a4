"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenManager = exports.AuthService = void 0;
exports.validateToken = validateToken;
const jose_1 = require("jose");
const prisma_1 = __importDefault(require("@/lib/database/prisma"));
function getJWTSecret() {
    if (typeof window !== 'undefined') {
        throw new Error('JWT operations must only run on server-side. This is a security violation.');
    }
    const secret = process.env.JWT_SECRET;
    if (!secret) {
        throw new Error('JWT_SECRET environment variable is required. Please set it in your .env.local file.');
    }
    if (secret.length < 32) {
        throw new Error('JWT_SECRET must be at least 32 characters long for security');
    }
    return new TextEncoder().encode(secret);
}
class AuthService {
    static async authenticate(loginId, password) {
        try {
            let user = await prisma_1.default.user.findUnique({
                where: { email: loginId.toLowerCase() }
            });
            if (!user) {
                user = await prisma_1.default.user.findUnique({
                    where: { username: loginId }
                });
            }
            if (!user) {
                user = await prisma_1.default.user.findFirst({
                    where: { displayName: loginId }
                });
            }
            if (!user && !loginId.includes('@')) {
                user = await prisma_1.default.user.findUnique({
                    where: { email: `${loginId.toLowerCase()}@local.network` }
                });
            }
            if (!user || !user.password) {
                return null;
            }
            const { AuthUtils } = await Promise.resolve().then(() => __importStar(require('@/lib/database/prisma')));
            const isValidPassword = await AuthUtils.verifyPassword(password, user.password);
            if (!isValidPassword) {
                return null;
            }
            await prisma_1.default.user.update({
                where: { id: user.id },
                data: { lastLogin: new Date() }
            });
            const authUser = {
                id: user.id,
                email: user.email,
                username: user.username,
                displayName: user.displayName || undefined,
                role: user.role,
                avatarUrl: user.avatarUrl || undefined,
                preferences: user.preferences,
                lastLogin: user.lastLogin,
                createdAt: user.createdAt
            };
            const token = await this.generateToken(authUser);
            return { user: authUser, token };
        }
        catch (error) {
            console.error('Authentication failed:', error);
            return null;
        }
    }
    static async validateSession(token) {
        try {
            const user = await this.verifyToken(token);
            if (!user)
                return null;
            const freshUserData = await this.refreshUserData(user.id);
            if (!freshUserData)
                return null;
            const hasChanged = this.hasUserDataChanged(user, freshUserData);
            if (hasChanged) {
                return {
                    user: freshUserData,
                    newToken: await this.generateToken(freshUserData)
                };
            }
            return { user: freshUserData };
        }
        catch (error) {
            console.error('Session validation failed:', error);
            return null;
        }
    }
    static async verifyToken(token) {
        try {
            const { payload } = await (0, jose_1.jwtVerify)(token, getJWTSecret());
            if (payload.user) {
                return payload.user;
            }
            if (payload.id && payload.email && payload.role) {
                return {
                    id: payload.id,
                    email: payload.email,
                    username: payload.username || payload.email,
                    displayName: payload.displayName,
                    role: payload.role,
                    avatarUrl: payload.avatarUrl,
                    preferences: payload.preferences || '{}',
                    createdAt: new Date(payload.createdAt || Date.now())
                };
            }
            console.error('Invalid token payload structure:', Object.keys(payload));
            return null;
        }
        catch (error) {
            console.error('Token verification failed:', error.message);
            return null;
        }
    }
    static async refreshUserData(userId) {
        try {
            const user = await prisma_1.default.user.findUnique({
                where: { id: userId }
            });
            if (!user)
                return null;
            return {
                id: user.id,
                email: user.email,
                username: user.username,
                displayName: user.displayName || undefined,
                role: user.role,
                avatarUrl: user.avatarUrl || undefined,
                preferences: user.preferences,
                lastLogin: user.lastLogin,
                createdAt: user.createdAt
            };
        }
        catch (error) {
            return null;
        }
    }
    static async generateToken(user) {
        return await new jose_1.SignJWT({ user })
            .setProtectedHeader({ alg: 'HS256' })
            .setIssuedAt()
            .setExpirationTime(this.TOKEN_EXPIRY)
            .sign(getJWTSecret());
    }
    static hasUserDataChanged(oldUser, newUser) {
        return oldUser.role !== newUser.role || oldUser.displayName !== newUser.displayName;
    }
}
exports.AuthService = AuthService;
AuthService.TOKEN_EXPIRY = '24h';
AuthService.REFRESH_THRESHOLD = 2 * 60 * 60 * 1000;
class TokenManager {
    static getToken() {
        if (typeof window === 'undefined')
            return null;
        return localStorage.getItem(this.TOKEN_KEY);
    }
    static setToken(token) {
        if (typeof window === 'undefined')
            return;
        localStorage.setItem(this.TOKEN_KEY, token);
    }
    static removeToken() {
        if (typeof window === 'undefined')
            return;
        localStorage.removeItem(this.TOKEN_KEY);
        localStorage.removeItem(this.USER_KEY);
    }
    static getStoredUser() {
        if (typeof window === 'undefined')
            return null;
        try {
            const userData = localStorage.getItem(this.USER_KEY);
            return userData ? JSON.parse(userData) : null;
        }
        catch {
            return null;
        }
    }
    static setStoredUser(user) {
        if (typeof window === 'undefined')
            return;
        localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
}
exports.TokenManager = TokenManager;
TokenManager.TOKEN_KEY = 'auth_token';
TokenManager.USER_KEY = 'current_user';
async function validateToken(request) {
    try {
        const authHeader = request.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return { success: false, error: 'Missing or invalid authorization header' };
        }
        const token = authHeader.substring(7);
        const user = await AuthService.verifyToken(token);
        if (!user) {
            return { success: false, error: 'Invalid or expired token' };
        }
        return { success: true, user };
    }
    catch (error) {
        console.error('Token validation error:', error);
        return { success: false, error: 'Token validation failed' };
    }
}
//# sourceMappingURL=auth-service.js.map