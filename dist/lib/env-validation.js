"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateEnvironment = validateEnvironment;
exports.getEnvironment = getEnvironment;
class EnvironmentError extends Error {
    constructor(message) {
        super(message);
        this.name = 'EnvironmentError';
    }
}
function validateEnvironment() {
    const missing = [];
    const warnings = [];
    const required = [
        'DATABASE_URL',
        'JWT_SECRET',
        'NEXTAUTH_SECRET'
    ];
    for (const key of required) {
        if (!process.env[key]) {
            missing.push(key);
        }
    }
    if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
        warnings.push('JWT_SECRET should be at least 32 characters for security');
    }
    if (process.env.NODE_ENV === 'production' && process.env.DATABASE_URL?.includes('file:')) {
        warnings.push('Using SQLite database in production - consider PostgreSQL for better performance');
    }
    if (!process.env.NEXT_PUBLIC_MPD_HOST) {
        warnings.push('NEXT_PUBLIC_MPD_HOST not set - MPD integration may not work');
    }
    if (missing.length > 0) {
        const errorMessage = [
            '❌ Missing required environment variables:',
            ...missing.map(key => `  - ${key}`),
            '',
            '💡 Please check your .env.local file and ensure all required variables are set.',
            '📝 See env.example for reference.'
        ].join('\n');
        throw new EnvironmentError(errorMessage);
    }
    if (warnings.length > 0) {
        console.warn('⚠️  Environment warnings:');
        warnings.forEach(warning => console.warn(`  - ${warning}`));
    }
    console.log('✅ Environment validation passed');
}
function getEnvironment() {
    validateEnvironment();
    return {
        DATABASE_URL: process.env.DATABASE_URL,
        JWT_SECRET: process.env.JWT_SECRET,
        NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
        NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'http://localhost:3000',
        MPD_HOST: process.env.NEXT_PUBLIC_MPD_HOST || 'localhost',
        MPD_PORT: parseInt(process.env.NEXT_PUBLIC_MPD_PORT || '6600'),
        MPD_HTTP_PORT: parseInt(process.env.NEXT_PUBLIC_MPD_HTTP_PORT || '8001'),
        SOCKET_HOST: process.env.NEXT_PUBLIC_SOCKET_HOST || 'localhost',
        WEBSOCKET_PORT: parseInt(process.env.NEXT_PUBLIC_WEBSOCKET_PORT || '3001'),
        SPOTIFY_CLIENT_ID: process.env.SPOTIFY_CLIENT_ID,
        SPOTIFY_CLIENT_SECRET: process.env.SPOTIFY_CLIENT_SECRET,
        LASTFM_API_KEY: process.env.LASTFM_API_KEY,
        NODE_ENV: process.env.NODE_ENV || 'development',
        IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
        IS_PRODUCTION: process.env.NODE_ENV === 'production',
        SIMPLIFIED_ONBOARDING: process.env.NEXT_PUBLIC_SIMPLIFIED_ONBOARDING === 'true',
        MULTIPLAYER_ENABLED: process.env.MULTIPLAYER_ENABLED === 'true',
    };
}
if (process.env.NODE_ENV === 'development') {
    try {
        validateEnvironment();
    }
    catch (error) {
        console.error(error instanceof EnvironmentError ? error.message : error);
        process.exit(1);
    }
}
//# sourceMappingURL=env-validation.js.map