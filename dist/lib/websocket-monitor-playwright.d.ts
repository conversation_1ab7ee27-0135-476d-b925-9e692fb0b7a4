import { Page } from '@playwright/test';
export interface WebSocketMessage {
    type: 'sent' | 'received';
    event: string;
    data: any;
    timestamp: number;
    playerId?: string;
    roomId?: string;
}
export interface GameEvent {
    event: string;
    timestamp: number;
    data: any;
}
export interface PlayerMetrics {
    playerId: string;
    joinTime: number;
    score: number;
    correctAnswers: number;
    responseTime: number[];
    connectionQuality: 'good' | 'fair' | 'poor';
}
export declare class MultiplayerGameMonitor {
    private automation;
    private socketClients;
    private gameEvents;
    private playerMetrics;
    constructor();
    initialize(): Promise<void>;
    close(): Promise<void>;
    monitorGameSession(config: {
        gameUrl: string;
        socketUrl: string;
        playerCount: number;
        duration: number;
        captureScreenshots?: boolean;
    }): Promise<{
        events: GameEvent[];
        metrics: PlayerMetrics[];
        screenshots: string[];
        summary: any;
    }>;
    private setupWebSocketInterception;
    private updatePlayerMetrics;
    private generateGameSummary;
    simulateScenarios(scenarios: Array<{
        name: string;
        setup: (pages: Page[]) => Promise<void>;
        verify: (pages: Page[]) => Promise<boolean>;
    }>): Promise<any[]>;
    stressTest(config: {
        gameUrl: string;
        socketUrl: string;
        maxPlayers: number;
        rampUpTime: number;
        testDuration: number;
    }): Promise<{
        maxConcurrentPlayers: number;
        averageLatency: number;
        errorRate: number;
        performanceData: any[];
    }>;
}
export declare function monitorMultiplayerGame(): Promise<{
    events: GameEvent[];
    metrics: PlayerMetrics[];
    screenshots: string[];
    summary: any;
}>;
export declare function runGameStressTest(): Promise<{
    maxConcurrentPlayers: number;
    averageLatency: number;
    errorRate: number;
    performanceData: any[];
}>;
