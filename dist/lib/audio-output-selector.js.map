{"version": 3, "file": "audio-output-selector.js", "sourceRoot": "", "sources": ["../../lib/audio-output-selector.ts"], "names": [], "mappings": ";;;AAeA,MAAa,mBAAmB;IAM9B,YAAY,SAAoB;QAHxB,gBAAW,GAA4B,IAAI,CAAA;QAC3C,kBAAa,GAAG,KAAK,CAAA;QAG3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,MAAM,GAAG;YACZ,IAAI,EAAE,MAAM;YACZ,SAAS,EAAE,UAAU,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,WAAW,QAAQ;YAC5E,MAAM,EAAE,EAAE;SACX,CAAA;IACH,CAAC;IAKD,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAA;YAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAA;YAC3D,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAEhE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;QACrD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,IAAgB;QAClC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;QACpD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAA;QAEvB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM;gBACT,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;gBAC/B,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAA;gBAClC,MAAK;YACP,KAAK,QAAQ;gBACX,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;gBAChC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAA;gBACjC,MAAK;YACP,KAAK,MAAM;gBACT,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;gBAC/B,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAA;gBACjC,MAAK;QACT,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YAGH,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAA;YACrD,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAA;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;QACtD,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAA;YACtD,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAA;YAGrD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;gBAClC,IAAI,CAAC,WAAW,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;gBACnD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,WAAW,CAAA;gBAC1C,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAA;gBAGlD,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,EAAE;oBAClD,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;gBACrC,CAAC,CAAC,CAAA;gBAEF,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE;oBAChD,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;gBAC/B,CAAC,CAAC,CAAA;gBAEF,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;oBAC/C,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAA;gBACrC,CAAC,CAAC,CAAA;gBAGF,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;YACzB,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAA;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;QACpD,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAA;YAGtD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;gBACxB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAA;gBACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;YACzB,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;QACrD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB;QACrB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;YACvF,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;gBAC7B,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;YACzC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAA;YACxD,CAAC;QACH,CAAC;IACH,CAAC;IAKD,gBAAgB;QACd,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;YACxB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;QACzC,CAAC;IACH,CAAC;IAKD,eAAe,CAAC,MAAc;QAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAA;QACvD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAA;YAClD,OAAO,CAAC,GAAG,CAAC,8CAA8C,MAAM,kBAAkB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAA;QAC/G,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAA;QACxE,CAAC;IACH,CAAC;IAKD,cAAc,CAAC,KAAc;QAC3B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAA;YAC9B,OAAO,CAAC,GAAG,CAAC,gCAAgC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAA;QAC5E,CAAC;IACH,CAAC;IAKD,eAAe;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;IAC3B,CAAC;IAKD,aAAa;QACX,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAA;IAC1D,CAAC;IAKD,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;IAC3B,CAAC;IAKD,eAAe;QACb,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,WAAW,KAAK,IAAI;YACpC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;YAC5D,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK;SACvE,CAAA;IACH,CAAC;CACF;AArOD,kDAqOC"}