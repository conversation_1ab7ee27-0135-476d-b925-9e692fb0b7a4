import { Team, Player, TeamScoringMode, TeamAnswer } from './types';
export declare class TeamManager {
    private teams;
    private teamColors;
    private teamEmojis;
    constructor();
    createTeam(name: string, captainId: string, captainName: string): Team;
    addPlayerToTeam(teamId: string, player: Player): boolean;
    removePlayerFromTeam(playerId: string): Team | null;
    getAllTeams(): Team[];
    getTeam(teamId: string): Team | undefined;
    getPlayerTeam(playerId: string): Team | undefined;
    updateTeam(teamId: string, updates: Partial<Pick<Team, 'name' | 'color' | 'emoji'>>): boolean;
    promoteToCaption(teamId: string, playerId: string): boolean;
    submitTeamAnswer(teamAnswer: TeamAnswer): boolean;
    calculateTeamScores(teamAnswers: TeamAnswer[], correctAnswer: number, points: number, scoringMode: TeamScoringMode): void;
    resetTeamAnswers(): void;
    autoBalanceTeams(players: Player[], maxTeamSize?: number): Team[];
    disbandTeam(teamId: string): boolean;
    getTeamLeaderboard(): Team[];
    allTeamsAnswered(): boolean;
    private getAvailableColor;
    private getAvailableEmoji;
    private generateTeamId;
    getTeamStats(teamId: string): {
        totalPlayers: number;
        averageScore: number;
        answeredQuestions: number;
        teamRank: number;
    } | null;
}
export declare const teamManager: TeamManager;
