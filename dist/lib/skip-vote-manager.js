"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkipVoteManager = void 0;
const mpd_client_1 = require("./mpd-client");
const env_1 = require("./env");
class SkipVoteManager {
    constructor(io) {
        this.votes = new Map();
        this.currentTrackId = null;
        this.currentTrackInfo = { title: '', artist: '' };
        this.connectedUsers = new Set();
        this.skipThreshold = 0.5;
        this.io = io;
    }
    setConnectedUser(userId, connected) {
        if (connected) {
            this.connectedUsers.add(userId);
        }
        else {
            this.connectedUsers.delete(userId);
            if (this.currentTrackId) {
                this.removeVote(this.currentTrackId, userId);
            }
        }
    }
    getConnectedUsersCount() {
        return this.connectedUsers.size;
    }
    setCurrentTrack(trackId, title, artist) {
        if (this.currentTrackId !== trackId) {
            console.log(`Skip vote: Track changed from ${this.currentTrackId} to ${trackId}`);
            this.votes.clear();
            this.currentTrackId = trackId;
            this.currentTrackInfo = { title, artist };
            this.io.emit('skip-vote-reset', {
                trackId,
                trackTitle: title,
                trackArtist: artist
            });
        }
    }
    async addVote(userId, userName) {
        if (!this.currentTrackId) {
            throw new Error('No track currently playing');
        }
        const trackVotes = this.votes.get(this.currentTrackId) || [];
        if (trackVotes.some(vote => vote.userId === userId)) {
            return this.getVoteStatus();
        }
        trackVotes.push({
            userId,
            userName,
            timestamp: Date.now()
        });
        this.votes.set(this.currentTrackId, trackVotes);
        const status = this.getVoteStatus();
        this.io.emit('skip-vote-update', status);
        if (status.percentage >= this.skipThreshold * 100 && !status.isSkipping) {
            await this.executeSkip();
        }
        return status;
    }
    removeVote(trackId, userId) {
        const trackVotes = this.votes.get(trackId);
        if (!trackVotes)
            return false;
        const initialLength = trackVotes.length;
        const filtered = trackVotes.filter(vote => vote.userId !== userId);
        if (filtered.length < initialLength) {
            this.votes.set(trackId, filtered);
            if (trackId === this.currentTrackId) {
                this.io.emit('skip-vote-update', this.getVoteStatus());
            }
            return true;
        }
        return false;
    }
    getVoteStatus() {
        const votes = this.currentTrackId ? (this.votes.get(this.currentTrackId) || []) : [];
        const totalVotes = votes.length;
        const connectedUsers = this.connectedUsers.size;
        const votesNeeded = Math.ceil(connectedUsers * this.skipThreshold);
        const percentage = connectedUsers > 0 ? (totalVotes / connectedUsers) * 100 : 0;
        return {
            trackId: this.currentTrackId || '',
            trackTitle: this.currentTrackInfo.title,
            trackArtist: this.currentTrackInfo.artist,
            votes,
            totalVotes,
            connectedUsers,
            votesNeeded,
            percentage,
            isSkipping: false
        };
    }
    async executeSkip() {
        if (!this.currentTrackId)
            return;
        const status = this.getVoteStatus();
        status.isSkipping = true;
        this.io.emit('skip-vote-update', status);
        try {
            const config = (0, env_1.getAudioConfig)();
            const mpdClient = new mpd_client_1.MPDClient({
                host: config.mpdHost,
                port: config.mpdPort,
                password: config.mpdPassword,
                httpProxyPort: config.mpdHttpPort
            });
            await mpdClient.connect();
            await mpdClient.nextTrack();
            await mpdClient.disconnect();
            console.log(`🎵 Track skipped by vote: ${this.currentTrackInfo.title} - ${this.currentTrackInfo.artist}`);
            this.io.emit('track-skipped', {
                trackId: this.currentTrackId,
                trackTitle: this.currentTrackInfo.title,
                trackArtist: this.currentTrackInfo.artist,
                reason: 'vote',
                voteCount: status.totalVotes,
                percentage: status.percentage
            });
            this.votes.clear();
            this.currentTrackId = null;
        }
        catch (error) {
            console.error('Failed to skip track:', error);
            const status = this.getVoteStatus();
            status.isSkipping = false;
            this.io.emit('skip-vote-update', status);
            this.io.emit('skip-vote-error', {
                message: 'Failed to skip track'
            });
        }
    }
    clearVotes() {
        this.votes.clear();
        if (this.currentTrackId) {
            this.io.emit('skip-vote-update', this.getVoteStatus());
        }
    }
    setSkipThreshold(threshold) {
        this.skipThreshold = Math.max(0.1, Math.min(1.0, threshold));
    }
}
exports.SkipVoteManager = SkipVoteManager;
//# sourceMappingURL=skip-vote-manager.js.map