"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.performanceMonitor = void 0;
exports.performanceMiddleware = performanceMiddleware;
exports.Measure = Measure;
const events_1 = require("events");
class PerformanceMonitor extends events_1.EventEmitter {
    constructor() {
        super(...arguments);
        this.metrics = new Map();
        this.maxMetricsPerKey = 1000;
        this.metricsRetentionMs = 5 * 60 * 1000;
    }
    record(metric) {
        const key = this.getMetricKey(metric);
        if (!this.metrics.has(key)) {
            this.metrics.set(key, []);
        }
        const metrics = this.metrics.get(key);
        metrics.push(metric);
        this.cleanupMetrics(key);
        this.emit('metric', metric);
        this.checkPerformance(key, metric);
    }
    startTimer(name, tags) {
        const start = Date.now();
        return () => {
            const duration = Date.now() - start;
            this.record({
                name,
                duration,
                timestamp: new Date(),
                tags
            });
        };
    }
    async measure(name, fn, tags) {
        const timer = this.startTimer(name, tags);
        try {
            const result = await fn();
            timer();
            return result;
        }
        catch (error) {
            timer();
            this.record({
                name: `${name}.error`,
                duration: 0,
                timestamp: new Date(),
                tags: { ...tags, error: error.message }
            });
            throw error;
        }
    }
    getStats(name, tags) {
        const key = this.getMetricKey({ name, tags });
        const metrics = this.metrics.get(key);
        if (!metrics || metrics.length === 0) {
            return null;
        }
        const durations = metrics.map(m => m.duration).sort((a, b) => a - b);
        const total = durations.reduce((sum, d) => sum + d, 0);
        return {
            count: durations.length,
            total,
            average: total / durations.length,
            min: durations[0],
            max: durations[durations.length - 1],
            p50: this.percentile(durations, 0.5),
            p90: this.percentile(durations, 0.9),
            p95: this.percentile(durations, 0.95),
            p99: this.percentile(durations, 0.99)
        };
    }
    getMetrics(startTime, endTime) {
        const result = new Map();
        for (const [key, metrics] of this.metrics.entries()) {
            const filtered = metrics.filter(m => {
                if (startTime && m.timestamp < startTime)
                    return false;
                if (endTime && m.timestamp > endTime)
                    return false;
                return true;
            });
            if (filtered.length > 0) {
                result.set(key, filtered);
            }
        }
        return result;
    }
    async getHealthStatus() {
        const checks = {
            database: await this.checkDatabase(),
            redis: await this.checkRedis(),
            mpd: await this.checkMPD(),
            socketServer: await this.checkSocketServer()
        };
        const apiStats = this.getStats('api.request');
        const dbStats = this.getStats('db.query');
        const status = Object.values(checks).every(v => v) ? 'healthy' :
            Object.values(checks).filter(v => v).length >= 2 ? 'degraded' :
                'unhealthy';
        return {
            status,
            timestamp: new Date(),
            checks,
            metrics: {
                apiResponseTime: apiStats?.p95 || 0,
                dbQueryTime: dbStats?.p95 || 0,
                activeConnections: await this.getActiveConnections(),
                memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024,
                cpuUsage: process.cpuUsage().user / 1000000
            }
        };
    }
    clear() {
        this.metrics.clear();
    }
    getMetricKey(metric) {
        const tagStr = metric.tags ?
            Object.entries(metric.tags)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([k, v]) => `${k}:${v}`)
                .join(',') : '';
        return tagStr ? `${metric.name}[${tagStr}]` : metric.name;
    }
    cleanupMetrics(key) {
        const metrics = this.metrics.get(key);
        if (!metrics)
            return;
        const now = Date.now();
        const cutoff = now - this.metricsRetentionMs;
        const filtered = metrics.filter(m => m.timestamp.getTime() > cutoff);
        if (filtered.length > this.maxMetricsPerKey) {
            this.metrics.set(key, filtered.slice(-this.maxMetricsPerKey));
        }
        else {
            this.metrics.set(key, filtered);
        }
    }
    checkPerformance(key, metric) {
        const stats = this.getStats(metric.name, metric.tags);
        if (!stats || stats.count < 10)
            return;
        if (metric.duration > stats.average * 2 && metric.duration > 1000) {
            this.emit('performance:degraded', {
                metric: metric.name,
                duration: metric.duration,
                average: stats.average,
                tags: metric.tags
            });
        }
        if (stats.p95 > 5000) {
            this.emit('performance:slow', {
                metric: metric.name,
                p95: stats.p95,
                tags: metric.tags
            });
        }
    }
    percentile(sortedValues, p) {
        const index = Math.ceil(sortedValues.length * p) - 1;
        return sortedValues[Math.max(0, index)];
    }
    async checkDatabase() {
        try {
            const timer = this.startTimer('health.database');
            const result = await Promise.resolve(true);
            timer();
            return result;
        }
        catch {
            return false;
        }
    }
    async checkRedis() {
        try {
            const timer = this.startTimer('health.redis');
            const result = await Promise.resolve(true);
            timer();
            return result;
        }
        catch {
            return false;
        }
    }
    async checkMPD() {
        try {
            const timer = this.startTimer('health.mpd');
            const result = await Promise.resolve(true);
            timer();
            return result;
        }
        catch {
            return false;
        }
    }
    async checkSocketServer() {
        try {
            const timer = this.startTimer('health.socket');
            const result = await Promise.resolve(true);
            timer();
            return result;
        }
        catch {
            return false;
        }
    }
    async getActiveConnections() {
        return 0;
    }
}
exports.performanceMonitor = new PerformanceMonitor();
function performanceMiddleware(req, res, next) {
    const timer = exports.performanceMonitor.startTimer('api.request', {
        method: req.method,
        path: req.path || req.url
    });
    const originalEnd = res.end;
    res.end = function (...args) {
        timer();
        originalEnd.apply(res, args);
    };
    next();
}
function Measure(name) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        const metricName = name || `${target.constructor.name}.${propertyKey}`;
        descriptor.value = async function (...args) {
            return exports.performanceMonitor.measure(metricName, () => originalMethod.apply(this, args));
        };
        return descriptor;
    };
}
//# sourceMappingURL=performance-monitor.js.map