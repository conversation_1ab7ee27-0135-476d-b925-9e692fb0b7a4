import { EventEmitter } from 'events';
export interface PerformanceMetric {
    name: string;
    duration: number;
    timestamp: Date;
    tags?: Record<string, string>;
    metadata?: Record<string, any>;
}
export interface PerformanceStats {
    count: number;
    total: number;
    average: number;
    min: number;
    max: number;
    p50: number;
    p90: number;
    p95: number;
    p99: number;
}
export interface HealthStatus {
    status: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: Date;
    checks: {
        database: boolean;
        redis: boolean;
        mpd: boolean;
        socketServer: boolean;
    };
    metrics: {
        apiResponseTime: number;
        dbQueryTime: number;
        activeConnections: number;
        memoryUsage: number;
        cpuUsage: number;
    };
}
declare class PerformanceMonitor extends EventEmitter {
    private metrics;
    private readonly maxMetricsPerKey;
    private readonly metricsRetentionMs;
    record(metric: PerformanceMetric): void;
    startTimer(name: string, tags?: Record<string, string>): () => void;
    measure<T>(name: string, fn: () => Promise<T>, tags?: Record<string, string>): Promise<T>;
    getStats(name: string, tags?: Record<string, string>): PerformanceStats | null;
    getMetrics(startTime?: Date, endTime?: Date): Map<string, PerformanceMetric[]>;
    getHealthStatus(): Promise<HealthStatus>;
    clear(): void;
    private getMetricKey;
    private cleanupMetrics;
    private checkPerformance;
    private percentile;
    private checkDatabase;
    private checkRedis;
    private checkMPD;
    private checkSocketServer;
    private getActiveConnections;
}
export declare const performanceMonitor: PerformanceMonitor;
export declare function performanceMiddleware(req: any, res: any, next: any): void;
export declare function Measure(name?: string): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export type { PerformanceMonitor };
