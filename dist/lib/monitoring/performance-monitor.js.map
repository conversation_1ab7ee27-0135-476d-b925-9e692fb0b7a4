{"version": 3, "file": "performance-monitor.js", "sourceRoot": "", "sources": ["../../../lib/monitoring/performance-monitor.ts"], "names": [], "mappings": ";;;AAoVA,sDAcC;AAGD,0BAcC;AA5WD,mCAAqC;AAwCrC,MAAM,kBAAmB,SAAQ,qBAAY;IAA7C;;QACU,YAAO,GAAqC,IAAI,GAAG,EAAE,CAAA;QAC5C,qBAAgB,GAAG,IAAI,CAAA;QACvB,uBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAA;IA4RrD,CAAC;IAvRC,MAAM,CAAC,MAAyB;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAErC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;QAC3B,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,CAAA;QACtC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAGpB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;QAGxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;QAG3B,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IACpC,CAAC;IAKD,UAAU,CAAC,IAAY,EAAE,IAA6B;QACpD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAExB,OAAO,GAAG,EAAE;YACV,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;YACnC,IAAI,CAAC,MAAM,CAAC;gBACV,IAAI;gBACJ,QAAQ;gBACR,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,IAAI;aACL,CAAC,CAAA;QACJ,CAAC,CAAA;IACH,CAAC;IAKD,KAAK,CAAC,OAAO,CACX,IAAY,EACZ,EAAoB,EACpB,IAA6B;QAE7B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QACzC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,EAAE,EAAE,CAAA;YACzB,KAAK,EAAE,CAAA;YACP,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,EAAE,CAAA;YACP,IAAI,CAAC,MAAM,CAAC;gBACV,IAAI,EAAE,GAAG,IAAI,QAAQ;gBACrB,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,IAAI,EAAE,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE;aACxC,CAAC,CAAA;YACF,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,QAAQ,CAAC,IAAY,EAAE,IAA6B;QAClD,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,IAAI,EAAuB,CAAC,CAAA;QAClE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAErC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;QACpE,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QAEtD,OAAO;YACL,KAAK,EAAE,SAAS,CAAC,MAAM;YACvB,KAAK;YACL,OAAO,EAAE,KAAK,GAAG,SAAS,CAAC,MAAM;YACjC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC;YACjB,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YACpC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC;YACpC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC;YACpC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC;YACrC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC;SACtC,CAAA;IACH,CAAC;IAKD,UAAU,CACR,SAAgB,EAChB,OAAc;QAEd,MAAM,MAAM,GAAG,IAAI,GAAG,EAA+B,CAAA;QAErD,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBAClC,IAAI,SAAS,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS;oBAAE,OAAO,KAAK,CAAA;gBACtD,IAAI,OAAO,IAAI,CAAC,CAAC,SAAS,GAAG,OAAO;oBAAE,OAAO,KAAK,CAAA;gBAClD,OAAO,IAAI,CAAA;YACb,CAAC,CAAC,CAAA;YAEF,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;YAC3B,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,MAAM,MAAM,GAAG;YACb,QAAQ,EAAE,MAAM,IAAI,CAAC,aAAa,EAAE;YACpC,KAAK,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE;YAC9B,GAAG,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE;YAC1B,YAAY,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;SAC7C,CAAA;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAEzC,MAAM,MAAM,GACV,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBAC/D,WAAW,CAAA;QAEb,OAAO;YACL,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM;YACN,OAAO,EAAE;gBACP,eAAe,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;gBACnC,WAAW,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;gBAC9B,iBAAiB,EAAE,MAAM,IAAI,CAAC,oBAAoB,EAAE;gBACpD,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI;gBACzD,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,IAAI,GAAG,OAAO;aAC5C;SACF,CAAA;IACH,CAAC;IAKD,KAAK;QACH,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;IACtB,CAAC;IAKO,YAAY,CAAC,MAAyB;QAC5C,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;iBACxB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;iBACtC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;iBAC5B,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QAEnB,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAA;IAC3D,CAAC;IAKO,cAAc,CAAC,GAAW;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACrC,IAAI,CAAC,OAAO;YAAE,OAAM;QAEpB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAA;QAG5C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAClC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,MAAM,CAC/B,CAAA;QAGD,IAAI,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAA;QAC/D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;QACjC,CAAC;IACH,CAAC;IAKO,gBAAgB,CAAC,GAAW,EAAE,MAAyB;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;QACrD,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,GAAG,EAAE;YAAE,OAAM;QAGtC,IAAI,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,IAAI,MAAM,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC;YAClE,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAChC,MAAM,EAAE,MAAM,CAAC,IAAI;gBACnB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,IAAI,EAAE,MAAM,CAAC,IAAI;aAClB,CAAC,CAAA;QACJ,CAAC;QAGD,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC5B,MAAM,EAAE,MAAM,CAAC,IAAI;gBACnB,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,IAAI,EAAE,MAAM,CAAC,IAAI;aAClB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAKO,UAAU,CAAC,YAAsB,EAAE,CAAS;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;QACpD,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;IACzC,CAAC;IAKO,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAA;YAEhD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC1C,KAAK,EAAE,CAAA;YACP,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAA;YAE7C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC1C,KAAK,EAAE,CAAA;YACP,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ;QACpB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;YAE3C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC1C,KAAK,EAAE,CAAA;YACP,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAA;YAE9C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC1C,KAAK,EAAE,CAAA;YACP,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAEhC,OAAO,CAAC,CAAA;IACV,CAAC;CACF;AAGY,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAA;AAG1D,SAAgB,qBAAqB,CAAC,GAAQ,EAAE,GAAQ,EAAE,IAAS;IACjE,MAAM,KAAK,GAAG,0BAAkB,CAAC,UAAU,CAAC,aAAa,EAAE;QACzD,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG;KAC1B,CAAC,CAAA;IAGF,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAA;IAC3B,GAAG,CAAC,GAAG,GAAG,UAAS,GAAG,IAAW;QAC/B,KAAK,EAAE,CAAA;QACP,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;IAC9B,CAAC,CAAA;IAED,IAAI,EAAE,CAAA;AACR,CAAC;AAGD,SAAgB,OAAO,CAAC,IAAa;IACnC,OAAO,UAAU,MAAW,EAAE,WAAmB,EAAE,UAA8B;QAC/E,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAA;QACvC,MAAM,UAAU,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,IAAI,WAAW,EAAE,CAAA;QAEtE,UAAU,CAAC,KAAK,GAAG,KAAK,WAAW,GAAG,IAAW;YAC/C,OAAO,0BAAkB,CAAC,OAAO,CAC/B,UAAU,EACV,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CACvC,CAAA;QACH,CAAC,CAAA;QAED,OAAO,UAAU,CAAA;IACnB,CAAC,CAAA;AACH,CAAC"}