"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAudioConfig = getAudioConfig;
exports.getMpdConnectionConfig = getMpdConnectionConfig;
exports.validateAudioConfig = validateAudioConfig;
exports.isDevelopmentMode = isDevelopmentMode;
exports.getAppUrl = getAppUrl;
const network_fallback_1 = require("./utils/network-fallback");
function getAudioConfig() {
    const isClient = typeof window !== 'undefined';
    if (!isClient) {
        return {
            mpdHost: 'localhost',
            mpdPort: 6600,
            mpdPassword: undefined,
            mpdHttpPort: 8001,
            statusUpdateInterval: 1000,
            enableLogging: false,
            autoReconnect: true,
            maxReconnectAttempts: 3,
            quizTimeLimit: 30,
            fadeEffects: true,
            defaultVolume: 70
        };
    }
    const networkStatus = (0, network_fallback_1.getNetworkStatus)();
    const hostEnv = getEnvVar('NEXT_PUBLIC_MPD_HOST', '');
    let resolvedHost;
    if (hostEnv && hostEnv.trim() !== '') {
        resolvedHost = hostEnv;
    }
    else if (networkStatus.fallbackMode || !networkStatus.canReachMpd) {
        resolvedHost = 'localhost';
    }
    else {
        resolvedHost = (typeof window !== 'undefined' ? window.location.hostname : 'localhost');
    }
    return {
        mpdHost: resolvedHost,
        mpdPort: parseInt(getEnvVar('NEXT_PUBLIC_MPD_PORT', '6600')),
        mpdPassword: getEnvVar('NEXT_PUBLIC_MPD_PASSWORD', undefined),
        mpdHttpPort: parseInt(getEnvVar('NEXT_PUBLIC_MPD_HTTP_PORT', '8001')),
        statusUpdateInterval: parseInt(getEnvVar('NEXT_PUBLIC_AUDIO_STATUS_INTERVAL', '1000')),
        enableLogging: getEnvVar('NEXT_PUBLIC_AUDIO_DEBUG_LOGGING', 'false') === 'true',
        autoReconnect: getEnvVar('NEXT_PUBLIC_AUDIO_AUTO_RECONNECT', 'true') !== 'false',
        maxReconnectAttempts: parseInt(getEnvVar('NEXT_PUBLIC_AUDIO_MAX_RECONNECT_ATTEMPTS', '3')),
        quizTimeLimit: parseInt(getEnvVar('NEXT_PUBLIC_QUIZ_TIME_LIMIT', '30')),
        fadeEffects: getEnvVar('NEXT_PUBLIC_AUDIO_FADE_EFFECTS', 'true') === 'true',
        defaultVolume: parseInt(getEnvVar('NEXT_PUBLIC_DEFAULT_VOLUME', '70'))
    };
}
function getMpdConnectionConfig() {
    const audioConfig = getAudioConfig();
    return {
        host: audioConfig.mpdHost,
        port: audioConfig.mpdPort,
        password: audioConfig.mpdPassword,
        httpProxyPort: audioConfig.mpdHttpPort,
    };
}
function getEnvVar(key, defaultValue) {
    if (typeof window === 'undefined') {
        return process.env[key] || defaultValue;
    }
    return process.env[key] || defaultValue;
}
function validateAudioConfig(config) {
    const errors = [];
    if (!config.mpdHost) {
        errors.push('MPD host is required');
    }
    if (config.mpdPort < 1 || config.mpdPort > 65535) {
        errors.push('MPD port must be between 1 and 65535');
    }
    if (config.mpdHttpPort < 1 || config.mpdHttpPort > 65535) {
        errors.push('MPD HTTP proxy port must be between 1 and 65535');
    }
    if (config.mpdPort === config.mpdHttpPort) {
        errors.push('MPD port and HTTP proxy port cannot be the same');
    }
    if (config.statusUpdateInterval < 100) {
        errors.push('Status update interval must be at least 100ms');
    }
    if (config.maxReconnectAttempts < 0) {
        errors.push('Max reconnect attempts must be non-negative');
    }
    if (config.quizTimeLimit < 5 || config.quizTimeLimit > 300) {
        errors.push('Quiz time limit must be between 5 and 300 seconds');
    }
    if (config.defaultVolume < 0 || config.defaultVolume > 100) {
        errors.push('Default volume must be between 0 and 100');
    }
    return errors;
}
function isDevelopmentMode() {
    if (typeof window === 'undefined') {
        return false;
    }
    return getEnvVar('NODE_ENV', 'production') === 'development';
}
function getAppUrl() {
    return getEnvVar('NEXT_PUBLIC_APP_URL', 'http://localhost:3000');
}
//# sourceMappingURL=env.js.map