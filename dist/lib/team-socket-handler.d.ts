import { Socket } from 'socket.io';
import { Team, TeamChatMessage, TeamAnswer, TeamGameSettings } from './types';
export interface TeamSocketEvents {
    'create-team': (data: {
        teamName: string;
        color: string;
        emoji: string;
    }) => void;
    'join-team': (data: {
        teamId: string;
    }) => void;
    'leave-team': () => void;
    'update-team': (data: {
        teamId: string;
        name?: string;
        color?: string;
        emoji?: string;
    }) => void;
    'promote-to-captain': (data: {
        teamId: string;
        playerId: string;
    }) => void;
    'team-chat': (data: {
        teamId: string;
        message: string;
    }) => void;
    'team-answer': (data: {
        teamId: string;
        answer: number;
        collaborative?: boolean;
    }) => void;
    'team-ready': (data: {
        teamId: string;
    }) => void;
    'disband-team': (data: {
        teamId: string;
    }) => void;
    'auto-balance-teams': () => void;
    'toggle-team-mode': (data: {
        enabled: boolean;
        settings?: TeamGameSettings;
    }) => void;
}
export interface TeamServerEvents {
    'team-created': (data: {
        team: Team;
    }) => void;
    'team-updated': (data: {
        team: Team;
    }) => void;
    'team-disbanded': (data: {
        teamId: string;
    }) => void;
    'player-joined-team': (data: {
        team: Team;
        playerId: string;
    }) => void;
    'player-left-team': (data: {
        teamId: string;
        playerId: string;
    }) => void;
    'team-chat-message': (data: {
        message: TeamChatMessage;
    }) => void;
    'team-answer-submitted': (data: {
        teamAnswer: TeamAnswer;
    }) => void;
    'teams-balanced': (data: {
        teams: Team[];
    }) => void;
    'team-mode-toggled': (data: {
        enabled: boolean;
        settings?: TeamGameSettings;
    }) => void;
    'team-leaderboard': (data: {
        teams: Team[];
    }) => void;
}
export declare class TeamSocketHandler {
    private gameTeamSettings;
    private gameTeamChats;
    constructor();
    handleConnection(socket: Socket, playerId: string, playerName: string, gameId: string): void;
    handleDisconnection(playerId: string, gameId: string): void;
    getTeamChatMessages(gameId: string): TeamChatMessage[];
    getTeamSettings(gameId: string): TeamGameSettings | undefined;
    calculateTeamScores(gameId: string, correctAnswer: number, points: number): void;
    resetTeamsForNewQuestion(gameId: string): void;
    private broadcastTeamUpdate;
    cleanupGame(gameId: string): void;
}
export declare const teamSocketHandler: TeamSocketHandler;
