import type { GameState, Player, Question } from './types';
interface ServerEvents {
    'game-state': GameState;
    'player-joined': {
        player: Player;
        players: Player[];
    };
    'player-left': {
        playerId: string;
        players: Player[];
    };
    'game-countdown': {
        countdownTime: number;
        totalQuestions: number;
        serverTime: number;
    };
    'game-started': {
        question: Question;
        questionIndex: number;
        timeLimit?: number;
        serverTime?: number;
    };
    'new-question': {
        question: Question;
        questionIndex: number;
        timeLimit?: number;
        serverTime?: number;
    };
    'answer-submitted': {
        acknowledged: boolean;
        questionIndex: number;
    };
    'question-results': {
        questionIndex: number;
        correctAnswerIndex: number;
        leaderboard: Player[];
    };
    'game-over': {
        leaderboard: Player[];
    };
    'error': {
        message: string;
    };
    'game-created': {
        gameId: string;
        hostId: string;
    };
}
export type SocketEventCallback<T = any> = (data: T) => void;
export declare class SocketClient {
    private socket;
    private connectionAttempts;
    private maxReconnectAttempts;
    private isConnecting;
    constructor();
    private initializeConnection;
    private getSocketUrl;
    private setupEventListeners;
    createGame(hostId: string, hostName: string, gameMode: string): void;
    joinGame(gameId: string, playerId: string, playerName: string, playerAvatar?: string): void;
    startGame(gameId: string): void;
    submitAnswer(gameId: string, playerId: string, answerIndex: number, timeTaken: number): void;
    nextQuestion(gameId: string): void;
    leaveGame(gameId: string, playerId: string): void;
    on<K extends keyof ServerEvents>(event: K, callback: SocketEventCallback<ServerEvents[K]>): void;
    off<K extends keyof ServerEvents>(event: K, callback: SocketEventCallback<ServerEvents[K]>): void;
    isConnected(): boolean;
    getConnectionId(): string | undefined;
    disconnect(): void;
}
export declare function getSocketClient(): SocketClient;
export {};
