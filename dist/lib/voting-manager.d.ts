import { VotingSession, VotingOption, VotingResult, VotingType } from './types';
export declare class VotingManager {
    private votingSessions;
    private sessionVotes;
    private sessionTimers;
    createVotingSession(sessionId: string, type: VotingType, title: string, description: string, options: VotingOption[], timeLimit: number | undefined, totalPlayers: number, hostId?: string): VotingSession;
    submitVote(sessionId: string, playerId: string, playerName: string, optionIndex: number): boolean;
    getVotingSession(sessionId: string): VotingSession | null;
    getPlayerVote(sessionId: string, playerId: string): number | null;
    completeVoting(sessionId: string): VotingResult | null;
    skipVoting(sessionId: string, hostId: string): VotingResult | null;
    private calculateResult;
    getVotingStats(sessionId: string): {
        totalVotes: number;
        votePercentages: number[];
        topOption: {
            index: number;
            votes: number;
            percentage: number;
        };
    } | null;
    getActiveSessions(): VotingSession[];
    cleanupExpiredSessions(): void;
    removeSession(sessionId: string): void;
    getTimeRemaining(sessionId: string): number;
    hasPlayerVoted(sessionId: string, playerId: string): boolean;
    getParticipationRate(sessionId: string): number;
    static createCategoryVotingOptions(): VotingOption[];
    static createDecadeVotingOptions(): VotingOption[];
    static createGameModeVotingOptions(): VotingOption[];
}
export declare const votingManager: VotingManager;
