"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedAlbumArtScraper = void 0;
exports.enhancedAlbumArtSearch = enhancedAlbumArtSearch;
const playwright_utils_1 = require("./playwright-utils");
const sharp_1 = __importDefault(require("sharp"));
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
class EnhancedAlbumArtScraper {
    constructor(cacheDir = './cache/album-art') {
        this.automation = new playwright_utils_1.MusicQuizAutomation({ headless: true });
        this.cacheDir = cacheDir;
    }
    async initialize() {
        await this.automation.initialize();
        await promises_1.default.mkdir(this.cacheDir, { recursive: true });
    }
    async close() {
        await this.automation.close();
    }
    getCacheKey(artist, album) {
        return `${artist}-${album}`.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }
    async searchAlbumArt(artist, album) {
        const cacheKey = this.getCacheKey(artist, album);
        const cachePath = path_1.default.join(this.cacheDir, `${cacheKey}.json`);
        try {
            const cached = await promises_1.default.readFile(cachePath, 'utf-8');
            return JSON.parse(cached);
        }
        catch {
        }
        const results = [];
        const page = await this.automation.createPage();
        try {
            await this.scrapeMusicBrainz(page, artist, album, results);
            await this.scrapeLastFm(page, artist, album, results);
            await this.scrapeDiscogs(page, artist, album, results);
            if (results.length < 3) {
                await this.scrapeGoogleImages(page, artist, album, results);
            }
            results.sort((a, b) => {
                const qualityOrder = { high: 3, medium: 2, low: 1 };
                return qualityOrder[b.quality] - qualityOrder[a.quality];
            });
            await promises_1.default.writeFile(cachePath, JSON.stringify(results));
            return results;
        }
        finally {
            await page.close();
        }
    }
    async scrapeMusicBrainz(page, artist, album, results) {
        try {
            const searchUrl = `https://musicbrainz.org/search?query=${encodeURIComponent(`${artist} ${album}`)}&type=release&limit=25`;
            await page.goto(searchUrl, { waitUntil: 'networkidle' });
            const releases = await page.$$eval('table.tbl tbody tr', (rows) => rows.slice(0, 5).map(row => {
                const link = row.querySelector('a[href*="/release/"]');
                return link ? link.href : null;
            }).filter(Boolean));
            for (const releaseUrl of releases) {
                await page.goto(releaseUrl, { waitUntil: 'networkidle' });
                const coverArt = await page.$eval('.cover-art img', (img) => img.src).catch(() => null);
                if (coverArt && !coverArt.includes('placeholder')) {
                    const highResUrl = coverArt.replace(/\-\d+\.jpg$/, '-500.jpg');
                    results.push({
                        url: highResUrl,
                        source: 'musicbrainz',
                        quality: 'high',
                        dimensions: { width: 500, height: 500 }
                    });
                }
            }
        }
        catch (error) {
            console.error('MusicBrainz scraping error:', error);
        }
    }
    async scrapeLastFm(page, artist, album, results) {
        try {
            const searchUrl = `https://www.last.fm/music/${encodeURIComponent(artist)}/${encodeURIComponent(album)}`;
            await page.goto(searchUrl, { waitUntil: 'networkidle' });
            const albumArt = await page.$eval('.album-overview-cover-art img', (img) => img.src).catch(() => null);
            if (albumArt) {
                const sizes = ['300x300', '600x600'];
                for (const size of sizes) {
                    const url = albumArt.replace(/\d+x\d+/, size);
                    results.push({
                        url,
                        source: 'lastfm',
                        quality: size === '600x600' ? 'high' : 'medium',
                        dimensions: {
                            width: parseInt(size.split('x')[0]),
                            height: parseInt(size.split('x')[1])
                        }
                    });
                }
            }
        }
        catch (error) {
            console.error('Last.fm scraping error:', error);
        }
    }
    async scrapeDiscogs(page, artist, album, results) {
        try {
            const searchUrl = `https://www.discogs.com/search/?q=${encodeURIComponent(`${artist} ${album}`)}&type=all`;
            await page.goto(searchUrl, { waitUntil: 'networkidle' });
            const firstResult = await page.$('.search_result_title a');
            if (firstResult) {
                await firstResult.click();
                await page.waitForLoadState('networkidle');
                const images = await page.$$eval('.image_gallery img', (imgs) => imgs.map(img => img.src).slice(0, 3));
                for (const url of images) {
                    results.push({
                        url,
                        source: 'discogs',
                        quality: 'high'
                    });
                }
            }
        }
        catch (error) {
            console.error('Discogs scraping error:', error);
        }
    }
    async scrapeGoogleImages(page, artist, album, results) {
        try {
            const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(`${artist} ${album} album cover`)}&tbm=isch&tbs=isz:l`;
            await page.goto(searchUrl, { waitUntil: 'networkidle' });
            const images = await page.$$eval('img[alt*="album"], img[alt*="cover"]', (imgs) => imgs
                .map(img => img.src)
                .filter(src => src && !src.startsWith('data:'))
                .slice(0, 5));
            for (const url of images) {
                results.push({
                    url,
                    source: 'google',
                    quality: 'medium'
                });
            }
        }
        catch (error) {
            console.error('Google Images scraping error:', error);
        }
    }
    async downloadAndProcessImage(imageUrl, outputPath, options) {
        const page = await this.automation.createPage();
        try {
            const response = await page.goto(imageUrl);
            if (!response || !response.ok()) {
                throw new Error('Failed to load image');
            }
            const buffer = await response.body();
            let sharpInstance = (0, sharp_1.default)(buffer);
            if (options?.width || options?.height) {
                sharpInstance = sharpInstance.resize(options.width, options.height, {
                    fit: 'cover',
                    position: 'center'
                });
            }
            if (options?.format) {
                sharpInstance = sharpInstance.toFormat(options.format, {
                    quality: options.quality || 90
                });
            }
            await sharpInstance.toFile(outputPath);
        }
        finally {
            await page.close();
        }
    }
    async validateImageQuality(imagePath) {
        try {
            const metadata = await (0, sharp_1.default)(imagePath).metadata();
            return {
                isValid: true,
                width: metadata.width,
                height: metadata.height,
                format: metadata.format
            };
        }
        catch {
            return { isValid: false };
        }
    }
}
exports.EnhancedAlbumArtScraper = EnhancedAlbumArtScraper;
async function enhancedAlbumArtSearch(artist, album, downloadPath) {
    const scraper = new EnhancedAlbumArtScraper();
    try {
        await scraper.initialize();
        const results = await scraper.searchAlbumArt(artist, album);
        if (downloadPath && results.length > 0) {
            const bestResult = results[0];
            await scraper.downloadAndProcessImage(bestResult.url, downloadPath, {
                width: 600,
                height: 600,
                format: 'jpeg',
                quality: 95
            });
        }
        return results;
    }
    finally {
        await scraper.close();
    }
}
//# sourceMappingURL=album-art-scraper.js.map