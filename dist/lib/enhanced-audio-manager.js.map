{"version": 3, "file": "enhanced-audio-manager.js", "sourceRoot": "", "sources": ["../../lib/enhanced-audio-manager.ts"], "names": [], "mappings": ";;;AAUA,mDAA8C;AAc9C,MAAa,oBAAqB,SAAQ,4BAAY;IAkBpD,YAAY,MAA2B;QACrC,KAAK,CAAC,MAAM,CAAC,CAAA;QAlBP,oBAAe,GAAoB;YACzC,QAAQ,EAAE,CAAC;YACX,OAAO,EAAE,IAAI;SACd,CAAA;QAEO,wBAAmB,GAAwB;YACjD,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,CAAC,EAAE;YAChB,OAAO,EAAE,EAAE;SACZ,CAAA;QAEO,iBAAY,GAAwB,IAAI,CAAA;QACxC,aAAQ,GAAoB,IAAI,CAAA;QAChC,kBAAa,GAAuC,IAAI,CAAA;QACxD,cAAS,GAA4B,IAAI,CAAA;QACzC,gBAAW,GAAY,KAAK,CAAA;IAIpC,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,KAAK,CAAC,UAAU,EAAE,CAAA;QAGxB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YACzD,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAA;YACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAA;YAC9C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;YAGpD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAC3E,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC3C,CAAC;QACH,CAAC;IACH,CAAC;IAKD,mBAAmB,CAAC,OAAgB;QAClC,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,OAAO,CAAA;IACxC,CAAC;IAKD,oBAAoB,CAAC,OAAe;QAClC,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAA;IACpE,CAAC;IAKD,uBAAuB,CAAC,OAAgB;QACtC,IAAI,CAAC,mBAAmB,CAAC,OAAO,GAAG,OAAO,CAAA;IAC5C,CAAC;IAKD,KAAK,CAAC,IAAI,CAAC,SAA6B;QACtC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;YAE1D,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAC9B,CAAC;QAGD,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;IAC9B,CAAC;IAKO,KAAK,CAAC,eAAe;QAC3B,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5E,OAAM;QACR,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QAEvB,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;YACjD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAElB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;gBAClB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;gBACxB,OAAM;YACR,CAAC;YAGD,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,CAAC,YAAY,CAAC,CAAA;YACxC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,WAAW,CAAA;YAGxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAA;YAGpE,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC7E,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAA;YACnD,YAAY,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YAE3B,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;gBACrC,YAAY,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YAC7B,CAAC;YAED,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;YAChC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;YAGnD,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;YAG3B,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAA;YAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAA;YAGlD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACtE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,CAAA;YAGvE,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;YAClE,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,SAAS,CAAC,CAAA;YAC9C,YAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,SAAS,GAAG,YAAY,CAAC,CAAA;YAG/E,UAAU,CAAC,GAAG,EAAE;gBAEd,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;oBAClB,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAA;gBACrB,CAAC;gBAGD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;gBAC3B,IAAI,CAAC,aAAa,GAAG,UAAU,CAAA;gBAC/B,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAA;gBAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;gBAGrB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBAE3B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;YAC1B,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,CAAA;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;YACzC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;YAExB,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAC9B,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YAGH,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;YACrD,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAMO,KAAK,CAAC,0BAA0B,CAAC,GAAW;QAClD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;YACtC,OAAO,CAAC,CAAA;QACV,CAAC;QAED,IAAI,CAAC;YAQH,OAAO,GAAG,CAAA;QAEZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAA;YAC/D,OAAO,CAAC,CAAA;QACV,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,cAAc,CAAC,OAAe;QAC1C,IAAI,CAAC;YACH,MAAM,KAAK,CAAC,kBAAkB,EAAE;gBAC9B,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;aAC1C,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;QACrD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;YAC5E,OAAM;QACR,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAClE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,YAAa,CAAC,WAAW,CAAC,CAAA;IACzE,CAAC;IAKD,SAAS,CAAC,MAAc;QACtB,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAGvB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAEvC,MAAM,SAAS,GAAG,MAAM,GAAG,GAAG,CAAA;YAC9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAC7E,CAAC;IACH,CAAC;IAKD,OAAO;QACL,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;QAC3B,CAAC;QACD,KAAK,CAAC,OAAO,EAAE,CAAA;IACjB,CAAC;CACF;AArPD,oDAqPC"}