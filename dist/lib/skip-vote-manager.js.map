{"version": 3, "file": "skip-vote-manager.js", "sourceRoot": "", "sources": ["../../lib/skip-vote-manager.ts"], "names": [], "mappings": ";;;AACA,6CAAwC;AACxC,+BAAsC;AAGtC,MAAa,eAAe;IAQ1B,YAAY,EAAgB;QAPpB,UAAK,GAA4B,IAAI,GAAG,EAAE,CAAA;QAC1C,mBAAc,GAAkB,IAAI,CAAA;QACpC,qBAAgB,GAAsC,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAA;QAC/E,mBAAc,GAAgB,IAAI,GAAG,EAAE,CAAA;QAEvC,kBAAa,GAAW,GAAG,CAAA;QAGjC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;IACd,CAAC;IAED,gBAAgB,CAAC,MAAc,EAAE,SAAkB;QACjD,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACjC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;YAElC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAA;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;IAED,sBAAsB;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAA;IACjC,CAAC;IAED,eAAe,CAAC,OAAe,EAAE,KAAa,EAAE,MAAc;QAE5D,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,iCAAiC,IAAI,CAAC,cAAc,OAAO,OAAO,EAAE,CAAC,CAAA;YACjF,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;YAClB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAA;YAC7B,IAAI,CAAC,gBAAgB,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAA;YAGzC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC9B,OAAO;gBACP,UAAU,EAAE,KAAK;gBACjB,WAAW,EAAE,MAAM;aACpB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,QAAgB;QAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC/C,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAA;QAG5D,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC;YACpD,OAAO,IAAI,CAAC,aAAa,EAAE,CAAA;QAC7B,CAAC;QAGD,UAAU,CAAC,IAAI,CAAC;YACd,MAAM;YACN,QAAQ;YACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAA;QAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAA;QAE/C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAGnC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAA;QAGxC,IAAI,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACxE,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QAC1B,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED,UAAU,CAAC,OAAe,EAAE,MAAc;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAC1C,IAAI,CAAC,UAAU;YAAE,OAAO,KAAK,CAAA;QAE7B,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAA;QACvC,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;QAElE,IAAI,QAAQ,CAAC,MAAM,GAAG,aAAa,EAAE,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;YAGjC,IAAI,OAAO,KAAK,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;YACxD,CAAC;YAED,OAAO,IAAI,CAAA;QACb,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED,aAAa;QACX,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QACpF,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAA;QAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAA;QAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,CAAA;QAClE,MAAM,UAAU,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAE/E,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,cAAc,IAAI,EAAE;YAClC,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK;YACvC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM;YACzC,KAAK;YACL,UAAU;YACV,cAAc;YACd,WAAW;YACX,UAAU;YACV,UAAU,EAAE,KAAK;SAClB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,IAAI,CAAC,IAAI,CAAC,cAAc;YAAE,OAAM;QAGhC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QACnC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAA;QAExC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAA,oBAAc,GAAE,CAAA;YAC/B,MAAM,SAAS,GAAG,IAAI,sBAAS,CAAC;gBAC9B,IAAI,EAAE,MAAM,CAAC,OAAO;gBACpB,IAAI,EAAE,MAAM,CAAC,OAAO;gBACpB,QAAQ,EAAE,MAAM,CAAC,WAAW;gBAC5B,aAAa,EAAE,MAAM,CAAC,WAAW;aAClC,CAAC,CAAA;YAEF,MAAM,SAAS,CAAC,OAAO,EAAE,CAAA;YACzB,MAAM,SAAS,CAAC,SAAS,EAAE,CAAA;YAC3B,MAAM,SAAS,CAAC,UAAU,EAAE,CAAA;YAE5B,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,gBAAgB,CAAC,KAAK,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAA;YAGzG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE;gBAC5B,OAAO,EAAE,IAAI,CAAC,cAAc;gBAC5B,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK;gBACvC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM;gBACzC,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,MAAM,CAAC,UAAU;gBAC5B,UAAU,EAAE,MAAM,CAAC,UAAU;aAC9B,CAAC,CAAA;YAGF,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;YAClB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;QAE5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAA;YAG7C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;YACnC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAA;YACzB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAA;YAExC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC9B,OAAO,EAAE,sBAAsB;aAChC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;QAClB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;QACxD,CAAC;IACH,CAAC;IAED,gBAAgB,CAAC,SAAiB;QAChC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAA;IAC9D,CAAC;CACF;AArLD,0CAqLC"}