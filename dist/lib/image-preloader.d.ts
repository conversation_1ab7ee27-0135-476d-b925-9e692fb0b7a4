declare class ImagePreloader {
    private static instance;
    private preloadQueue;
    private loadedImages;
    private isProcessing;
    private maxConcurrent;
    private activeLoads;
    private constructor();
    static getInstance(): ImagePreloader;
    preload(url: string | null | undefined, priority?: 'high' | 'medium' | 'low'): void;
    preloadBatch(urls: (string | null | undefined)[], priority?: 'high' | 'medium' | 'low'): void;
    preloadQueueAlbumArt(queueItems: Array<{
        albumArtUrl?: string | null;
        id?: string | number | null;
        fingerprint?: string | null;
    }>): void;
    clear(): void;
    private processQueue;
    private getNextItem;
    private loadImage;
    isLoaded(url: string): boolean;
    getStats(): {
        queueSize: number;
        loadedCount: number;
        activeLoads: number;
    };
}
export declare const imagePreloader: ImagePreloader;
export declare const preloadImage: (url: string | null | undefined, priority?: "high" | "medium" | "low") => void;
export declare const preloadImages: (urls: (string | null | undefined)[], priority?: "high" | "medium" | "low") => void;
export declare const preloadQueueAlbumArt: (queueItems: Array<{
    albumArtUrl?: string | null;
    id?: string | number | null;
    fingerprint?: string | null;
}>) => void;
export {};
