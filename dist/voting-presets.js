"use strict";
/**
 * Voting Presets
 * Predefined voting options for different game scenarios
 */
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCustomVoting = exports.getRandomPreset = exports.getPresetsByTag = exports.getPresetById = exports.getPresetsByType = exports.ALL_PRESETS = exports.CUSTOM_PRESETS = exports.THEME_PRESETS = exports.GAME_MODE_PRESETS = exports.DECADE_PRESETS = exports.CATEGORY_PRESETS = void 0;
/**
 * Music Category Voting Presets
 */
exports.CATEGORY_PRESETS = [
    {
        id: 'genres-basic',
        type: 'category',
        title: 'Choose Music Genre',
        description: 'Vote for the music genre for the next round',
        defaultTimeLimit: 30,
        tags: ['genre', 'category', 'music'],
        options: [
            { label: 'Rock & Metal', value: 'rock', description: 'Hard rock, metal, punk, alternative', emoji: '🎸' },
            { label: 'Pop & Dance', value: 'pop', description: 'Pop hits, dance, electronic, disco', emoji: '🎵' },
            { label: 'Hip Hop & R&B', value: 'hiphop', description: 'Rap, hip hop, R&B, soul', emoji: '🎤' },
            { label: 'Classic & Jazz', value: 'classic', description: 'Classical, jazz, blues, folk', emoji: '🎼' }
        ]
    },
    {
        id: 'genres-extended',
        type: 'category',
        title: 'Extended Genre Selection',
        description: 'More specific genre categories',
        defaultTimeLimit: 45,
        tags: ['genre', 'extended', 'specific'],
        options: [
            { label: 'Electronic & EDM', value: 'electronic', description: 'House, techno, dubstep, trance', emoji: '🎧' },
            { label: 'Country & Folk', value: 'country', description: 'Country, folk, bluegrass, americana', emoji: '🤠' },
            { label: 'Alternative & Indie', value: 'alternative', description: 'Alt rock, indie, grunge', emoji: '🎨' },
            { label: 'World Music', value: 'world', description: 'International, world fusion', emoji: '🌍' }
        ]
    },
    {
        id: 'mood-categories',
        type: 'category',
        title: 'Music by Mood',
        description: 'Choose the vibe for the next round',
        defaultTimeLimit: 30,
        tags: ['mood', 'vibe', 'energy'],
        options: [
            { label: 'High Energy', value: 'high-energy', description: 'Upbeat, energetic, pump-up songs', emoji: '⚡' },
            { label: 'Chill & Relaxed', value: 'chill', description: 'Laid-back, ambient, lo-fi', emoji: '😌' },
            { label: 'Party Hits', value: 'party', description: 'Dance floor favorites, crowd pleasers', emoji: '🎉' },
            { label: 'Emotional', value: 'emotional', description: 'Ballads, deep cuts, meaningful lyrics', emoji: '💝' }
        ]
    }
];
/**
 * Time Period / Decade Voting Presets
 */
exports.DECADE_PRESETS = [
    {
        id: 'decades-classic',
        type: 'decade',
        title: 'Choose Time Period',
        description: 'Vote for the era of music for the next round',
        defaultTimeLimit: 30,
        tags: ['decade', 'era', 'time'],
        options: [
            { label: '80s Hits', value: '80s', description: 'Music from the 1980s', emoji: '📻' },
            { label: '90s Classics', value: '90s', description: 'Music from the 1990s', emoji: '💿' },
            { label: '2000s Pop', value: '2000s', description: 'Music from the 2000s', emoji: '💽' },
            { label: '2010s & Beyond', value: '2010s', description: 'Modern hits from 2010s onwards', emoji: '📱' }
        ]
    },
    {
        id: 'decades-extended',
        type: 'decade',
        title: 'Broader Time Periods',
        description: 'Choose from wider time ranges',
        defaultTimeLimit: 30,
        tags: ['era', 'broad', 'range'],
        options: [
            { label: 'Oldies (50s-70s)', value: 'oldies', description: 'Classic hits from 1950s-1970s', emoji: '📼' },
            { label: 'Golden Era (80s-90s)', value: 'golden', description: 'The golden age of music', emoji: '🏆' },
            { label: 'Millennium (2000s)', value: 'millennium', description: 'Y2K era music', emoji: '🌟' },
            { label: 'Modern (2010s+)', value: 'modern', description: 'Contemporary hits', emoji: '🚀' }
        ]
    },
    {
        id: 'specific-years',
        type: 'decade',
        title: 'Specific Year Focus',
        description: 'Pick a specific year to focus on',
        defaultTimeLimit: 45,
        tags: ['year', 'specific', 'focused'],
        options: [
            { label: '1984', value: '1984', description: 'The year of Purple Rain, Born in the USA', emoji: '👑' },
            { label: '1991', value: '1991', description: 'Grunge explosion, Nevermind, Ten', emoji: '🎸' },
            { label: '1999', value: '1999', description: 'End of millennium hits', emoji: '🎊' },
            { label: '2007', value: '2007', description: 'Peak digital music era', emoji: '💎' }
        ]
    }
];
/**
 * Game Mode Voting Presets
 */
exports.GAME_MODE_PRESETS = [
    {
        id: 'game-modes-basic',
        type: 'game-mode',
        title: 'Choose Game Mode',
        description: 'Vote for the game mode for the next round',
        defaultTimeLimit: 30,
        tags: ['mode', 'gameplay', 'mechanics'],
        options: [
            { label: 'Classic Quiz', value: 'classic', description: 'Traditional music quiz format', emoji: '🎯' },
            { label: 'Speed Round', value: 'speed', description: 'Fast-paced, shorter time limits', emoji: '⚡' },
            { label: 'Challenge Mode', value: 'challenge', description: 'Harder questions, longer time', emoji: '🏆' },
            { label: 'Guess the Year', value: 'year', description: 'Identify release years', emoji: '📅' }
        ]
    },
    {
        id: 'game-modes-advanced',
        type: 'game-mode',
        title: 'Advanced Game Modes',
        description: 'More challenging game variants',
        defaultTimeLimit: 45,
        tags: ['advanced', 'challenging', 'complex'],
        options: [
            { label: 'Audio Manipulation', value: 'audio-manipulation', description: 'Reversed, slowed, or distorted audio', emoji: '🔄' },
            { label: 'Blind Listening', value: 'blind', description: 'No visual clues, audio only', emoji: '👁️‍🗨️' },
            { label: 'Snippet Challenge', value: 'snippet', description: 'Very short audio clips', emoji: '⏱️' },
            { label: 'Lyrics Mode', value: 'lyrics', description: 'Guess song from lyrics only', emoji: '📝' }
        ]
    },
    {
        id: 'team-modes',
        type: 'game-mode',
        title: 'Team Game Modes',
        description: 'Collaborative gameplay options',
        defaultTimeLimit: 30,
        tags: ['team', 'collaborative', 'multiplayer'],
        options: [
            { label: 'Team Collaborative', value: 'team-collaborative', description: 'Teams discuss and decide together', emoji: '🤝' },
            { label: 'Team Relay', value: 'team-relay', description: 'Team members take turns', emoji: '🏃‍♂️' },
            { label: 'Team Battle', value: 'team-battle', description: 'Head-to-head team competition', emoji: '⚔️' },
            { label: 'Mixed Teams', value: 'mixed-teams', description: 'Randomly shuffled teams each round', emoji: '🔀' }
        ]
    }
];
/**
 * Theme Voting Presets
 */
exports.THEME_PRESETS = [
    {
        id: 'seasonal-themes',
        type: 'theme',
        title: 'Seasonal Themes',
        description: 'Music themes based on seasons and holidays',
        defaultTimeLimit: 30,
        tags: ['season', 'holiday', 'theme'],
        options: [
            { label: 'Summer Vibes', value: 'summer', description: 'Beach, vacation, sunny day songs', emoji: '☀️' },
            { label: 'Holiday Classics', value: 'holiday', description: 'Christmas, winter holiday music', emoji: '🎄' },
            { label: 'Halloween Spooky', value: 'halloween', description: 'Scary, dark, thriller themes', emoji: '🎃' },
            { label: 'Love Songs', value: 'love', description: 'Romantic, Valentine\'s themed', emoji: '💕' }
        ]
    },
    {
        id: 'movie-tv-themes',
        type: 'theme',
        title: 'Movie & TV Themes',
        description: 'Music from films and television',
        defaultTimeLimit: 45,
        tags: ['movie', 'tv', 'soundtrack'],
        options: [
            { label: 'Movie Soundtracks', value: 'movie-soundtracks', description: 'Songs featured in major films', emoji: '🎬' },
            { label: 'TV Show Themes', value: 'tv-themes', description: 'Opening themes and TV music', emoji: '📺' },
            { label: 'Disney & Animation', value: 'disney', description: 'Animated movie soundtracks', emoji: '🏰' },
            { label: 'Action Movie Hits', value: 'action', description: 'High-energy movie music', emoji: '💥' }
        ]
    },
    {
        id: 'cultural-themes',
        type: 'theme',
        title: 'Cultural Themes',
        description: 'Music representing different cultures and regions',
        defaultTimeLimit: 45,
        tags: ['culture', 'region', 'international'],
        options: [
            { label: 'British Invasion', value: 'british', description: 'UK rock and pop classics', emoji: '🇬🇧' },
            { label: 'American Classics', value: 'american', description: 'USA rock, country, blues', emoji: '🇺🇸' },
            { label: 'Latin Rhythms', value: 'latin', description: 'Salsa, reggaeton, Latin pop', emoji: '🎺' },
            { label: 'World Music', value: 'world', description: 'International and fusion styles', emoji: '🌎' }
        ]
    }
];
/**
 * Custom Voting Presets
 */
exports.CUSTOM_PRESETS = [
    {
        id: 'next-round-options',
        type: 'next-round',
        title: 'Next Round Options',
        description: 'Vote for what happens in the next round',
        defaultTimeLimit: 30,
        tags: ['next', 'round', 'options'],
        options: [
            { label: 'Double Points', value: 'double-points', description: 'Next round worth double points', emoji: '2️⃣' },
            { label: 'Bonus Round', value: 'bonus', description: 'Special bonus question round', emoji: '🎁' },
            { label: 'Lightning Round', value: 'lightning', description: '5 rapid-fire questions', emoji: '⚡' },
            { label: 'Mystery Round', value: 'mystery', description: 'Surprise challenge format', emoji: '❓' }
        ]
    },
    {
        id: 'difficulty-levels',
        type: 'custom',
        title: 'Difficulty Level',
        description: 'Choose the challenge level for upcoming questions',
        defaultTimeLimit: 20,
        tags: ['difficulty', 'challenge', 'level'],
        options: [
            { label: 'Easy Mode', value: 'easy', description: 'Popular, well-known songs', emoji: '😊' },
            { label: 'Medium Challenge', value: 'medium', description: 'Mix of popular and deeper cuts', emoji: '🤔' },
            { label: 'Hard Mode', value: 'hard', description: 'Obscure tracks and B-sides', emoji: '😤' },
            { label: 'Expert Level', value: 'expert', description: 'For true music aficionados', emoji: '🧠' }
        ]
    }
];
/**
 * All presets combined for easy access
 */
exports.ALL_PRESETS = __spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray([], exports.CATEGORY_PRESETS, true), exports.DECADE_PRESETS, true), exports.GAME_MODE_PRESETS, true), exports.THEME_PRESETS, true), exports.CUSTOM_PRESETS, true);
/**
 * Helper functions to get presets
 */
var getPresetsByType = function (type) {
    return exports.ALL_PRESETS.filter(function (preset) { return preset.type === type; });
};
exports.getPresetsByType = getPresetsByType;
var getPresetById = function (id) {
    return exports.ALL_PRESETS.find(function (preset) { return preset.id === id; });
};
exports.getPresetById = getPresetById;
var getPresetsByTag = function (tag) {
    return exports.ALL_PRESETS.filter(function (preset) { return preset.tags.includes(tag); });
};
exports.getPresetsByTag = getPresetsByTag;
var getRandomPreset = function (type) {
    var presets = type ? (0, exports.getPresetsByType)(type) : exports.ALL_PRESETS;
    return presets[Math.floor(Math.random() * presets.length)];
};
exports.getRandomPreset = getRandomPreset;
/**
 * Create custom voting options
 */
var createCustomVoting = function (type, title, description, options, timeLimit) {
    if (timeLimit === void 0) { timeLimit = 30; }
    return {
        id: "custom-".concat(Date.now()),
        type: type,
        title: title,
        description: description,
        options: options,
        defaultTimeLimit: timeLimit,
        tags: ['custom']
    };
};
exports.createCustomVoting = createCustomVoting;
