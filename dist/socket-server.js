"use strict";
/**
 * Socket.IO Server for Multiplayer Music Quiz
 *
 * Handles real-time multiplayer game management, player connections,
 * and game state synchronization with MPD integration.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MultiplayerSocketServer = void 0;
exports.initializeSocketServer = initializeSocketServer;
const socket_io_1 = require("socket.io");
const game_store_1 = require("./game-store");
const game_manager_1 = require("./game-manager");
const team_socket_handler_1 = require("./team-socket-handler");
class MultiplayerSocketServer {
    constructor(httpServer) {
        this.gameManagers = new Map();
        this.connectionCount = 0;
        this.ipConnectionCounts = new Map();
        this.io = new socket_io_1.Server(httpServer, {
            cors: {
                origin: (origin, callback) => {
                    var _a;
                    // Allow all origins in development
                    if (process.env.NODE_ENV !== 'production') {
                        callback(null, true);
                        return;
                    }
                    // In production, check allowed origins
                    const allowedOrigins = ((_a = process.env.ALLOWED_ORIGINS) === null || _a === void 0 ? void 0 : _a.split(',')) || [];
                    if (!origin || allowedOrigins.includes(origin)) {
                        callback(null, true);
                    }
                    else {
                        callback(new Error('Not allowed by CORS'));
                    }
                },
                methods: ['GET', 'POST'],
                credentials: true
            },
            transports: ['websocket', 'polling'],
            pingTimeout: 15000,
            pingInterval: 10000
        });
        this.gameStore = new game_store_1.GameStore();
        this.setupEventHandlers();
        console.log('🎮 Multiplayer Socket Server initialized');
    }
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`🔌 Client connected: ${socket.id}`);
            // Wrap all socket handlers with error boundary
            const wrapHandler = (handlerName, handler) => {
                return async (...args) => {
                    try {
                        await handler(...args);
                    }
                    catch (error) {
                        console.error(`Socket handler error in ${handlerName}:`, {
                            handler: handlerName,
                            socketId: socket.id,
                            playerId: socket.data.playerId,
                            gameId: socket.data.gameId,
                            error: error instanceof Error ? error.message : 'Unknown error',
                            stack: error instanceof Error ? error.stack : undefined
                        });
                        socket.emit('error', {
                            message: `Failed to process ${handlerName}`,
                            code: 'INTERNAL_ERROR',
                            details: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : 'Unknown error') : undefined
                        });
                    }
                };
            };
            // Handle game creation
            socket.on('create-game', wrapHandler('create-game', async (data, callback) => {
                try {
                    const gameId = this.gameStore.generateGamePin();
                    const settings = data.settings || {};
                    const gameManager = new game_manager_1.GameManager({
                        gameId,
                        hostId: data.hostId,
                        gameMode: data.gameMode,
                        maxPlayers: settings.maxPlayers || 6,
                        timePerQuestion: settings.timePerQuestion || 30,
                        totalQuestions: settings.totalQuestions || 10,
                        ultimoteConfig: settings.ultimoteConfig
                    });
                    const host = {
                        id: data.hostId,
                        name: data.hostName,
                        avatar: '/placeholder-user.jpg',
                        score: 0,
                        isHost: true,
                        hasAnswered: false,
                        joinedAt: Date.now()
                    };
                    const gameState = await gameManager.createGame(host);
                    // Store additional settings that GameManager doesn't handle
                    const enhancedGameState = {
                        ...gameState,
                        settings: {
                            ...gameState.settings,
                            ...settings,
                            ultimoteConfig: settings.ultimoteConfig
                        }
                    };
                    this.gameStore.setGame(gameId, enhancedGameState);
                    this.gameManagers.set(gameId, gameManager);
                    // Join host to game room
                    socket.join(gameId);
                    socket.data.gameId = gameId;
                    socket.data.playerId = data.hostId;
                    socket.data.playerName = data.hostName;
                    // Send response with callback if provided
                    if (callback) {
                        callback({
                            success: true,
                            gameId,
                            gamePin: gameId,
                            playerId: data.hostId
                        });
                    }
                    socket.emit('game-created', { gameId, hostId: data.hostId });
                    socket.emit('game-state', { ...enhancedGameState, pin: gameId });
                    // Initialize team handlers for the host
                    team_socket_handler_1.teamSocketHandler.handleConnection(socket, data.hostId, data.hostName, gameId);
                    console.log(`🎮 Game created: ${gameId} by ${data.hostName}`);
                }
                catch (error) {
                    console.error('Error creating game:', {
                        error: error instanceof Error ? error.message : 'Unknown error',
                        data,
                        socketId: socket.id
                    });
                    socket.emit('error', {
                        message: 'Failed to create game',
                        code: 'GAME_CREATION_FAILED',
                        details: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : undefined) : undefined
                    });
                }
            }));
            // Handle game creation with team settings (underscore version)
            socket.on('create_game', async (data, callback) => {
                try {
                    const gameId = this.gameStore.generateGamePin();
                    const settings = data.settings || {};
                    // Create team settings if team mode is enabled
                    let teamSettings;
                    if (settings.teamMode) {
                        teamSettings = {
                            teamMode: true,
                            teamGameMode: 'collaborative', // Default mode
                            teamScoringMode: 'average', // Default scoring
                            maxTeamSize: settings.maxTeamSize || 4,
                            minTeamSize: 2,
                            allowTeamChat: true
                        };
                    }
                    const gameManager = new game_manager_1.GameManager({
                        gameId,
                        hostId: data.playerId,
                        gameMode: settings.gameMode,
                        maxPlayers: settings.maxPlayers || 6,
                        timePerQuestion: settings.timePerQuestion || 30,
                        teamMode: settings.teamMode || false,
                        teamSettings,
                        totalQuestions: settings.totalQuestions || 10
                    });
                    const host = {
                        id: data.playerId,
                        name: data.playerName,
                        avatar: '/placeholder-user.jpg',
                        score: 0,
                        isHost: true,
                        hasAnswered: false,
                        joinedAt: Date.now()
                    };
                    const gameState = await gameManager.createGame(host);
                    // Store additional settings including ultimoteConfig
                    const enhancedGameState = {
                        ...gameState,
                        settings: {
                            ...gameState.settings,
                            ...settings,
                            ultimoteConfig: settings.ultimoteConfig
                        }
                    };
                    this.gameStore.setGame(gameId, enhancedGameState);
                    this.gameManagers.set(gameId, gameManager);
                    // Join host to game room
                    socket.join(gameId);
                    socket.data.gameId = gameId;
                    socket.data.playerId = data.playerId;
                    socket.data.playerName = data.playerName;
                    // Initialize team handlers for the host
                    team_socket_handler_1.teamSocketHandler.handleConnection(socket, data.playerId, data.playerName, gameId);
                    // Send response with callback
                    if (callback) {
                        callback({
                            success: true,
                            data: {
                                gameId,
                                gamePin: gameId,
                                teamMode: settings.teamMode
                            }
                        });
                    }
                    socket.emit('game-state', { ...enhancedGameState, pin: gameId });
                    console.log(`🎮 Game created with settings: ${gameId} by ${data.playerName}`, { teamMode: settings.teamMode });
                }
                catch (error) {
                    console.error('Error creating game with settings:', error);
                    if (callback) {
                        callback({
                            success: false,
                            message: 'Failed to create game'
                        });
                    }
                    socket.emit('error', { message: 'Failed to create game' });
                }
            });
            // Handle joining a game
            socket.on('join-game', async (data, callback) => {
                try {
                    // Support both gameId and gamePin
                    const gameId = data.gameId || data.gamePin;
                    if (!gameId) {
                        const error = { message: 'Game ID or PIN required' };
                        if (callback) {
                            callback({ success: false, error: error.message });
                        }
                        else {
                            socket.emit('error', error);
                        }
                        return;
                    }
                    const game = this.gameStore.getGame(gameId);
                    if (!game) {
                        const error = { message: 'Game not found' };
                        if (callback) {
                            callback({ success: false, error: error.message });
                        }
                        else {
                            socket.emit('error', error);
                        }
                        return;
                    }
                    if (game.status !== 'waiting') {
                        const error = { message: 'Game already in progress' };
                        if (callback) {
                            callback({ success: false, error: error.message });
                        }
                        else {
                            socket.emit('error', error);
                        }
                        return;
                    }
                    if (game.players.length >= 6) {
                        const error = { message: 'Game is full' };
                        if (callback) {
                            callback({ success: false, error: error.message });
                        }
                        else {
                            socket.emit('error', error);
                        }
                        return;
                    }
                    const gameManager = this.gameManagers.get(gameId);
                    if (!gameManager) {
                        const error = { message: 'Game manager not found' };
                        if (callback) {
                            callback({ success: false, error: error.message });
                        }
                        else {
                            socket.emit('error', error);
                        }
                        return;
                    }
                    // Generate player ID if not provided
                    const playerId = data.playerId || `player-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                    const player = {
                        id: playerId,
                        name: data.playerName,
                        avatar: data.playerAvatar || '/placeholder-user.jpg',
                        score: 0,
                        isHost: false,
                        hasAnswered: false,
                        joinedAt: Date.now()
                    };
                    const updatedGame = gameManager.addPlayer(player);
                    this.gameStore.setGame(gameId, updatedGame);
                    // Join player to game room
                    socket.join(gameId);
                    socket.data.gameId = gameId;
                    socket.data.playerId = playerId;
                    socket.data.playerName = data.playerName;
                    // Send callback response if provided
                    if (callback) {
                        callback({
                            success: true,
                            gameId,
                            playerId,
                            players: updatedGame.players
                        });
                    }
                    // Notify all players
                    this.io.to(gameId).emit('player-joined', {
                        player,
                        players: updatedGame.players
                    });
                    this.io.to(gameId).emit('game-state', { ...updatedGame, pin: gameId });
                    // Initialize team handlers for the joined player
                    team_socket_handler_1.teamSocketHandler.handleConnection(socket, data.playerId, data.playerName, data.gameId);
                    console.log(`👤 Player ${data.playerName} joined game ${data.gameId}`);
                }
                catch (error) {
                    console.error('Error joining game:', error);
                    socket.emit('error', { message: 'Failed to join game' });
                }
            });
            // Handle game start
            socket.on('start-game', async (data, callback) => {
                try {
                    const game = this.gameStore.getGame(data.gameId);
                    if (!game) {
                        socket.emit('error', { message: 'Game not found' });
                        if (callback)
                            callback({ success: false, error: 'Game not found' });
                        return;
                    }
                    const player = game.players.find(p => p.id === socket.data.playerId);
                    if (!(player === null || player === void 0 ? void 0 : player.isHost)) {
                        socket.emit('error', { message: 'Only host can start the game' });
                        if (callback)
                            callback({ success: false, error: 'Only host can start the game' });
                        return;
                    }
                    const gameManager = this.gameManagers.get(data.gameId);
                    if (!gameManager) {
                        socket.emit('error', { message: 'Game manager not found' });
                        if (callback)
                            callback({ success: false, error: 'Game manager not found' });
                        return;
                    }
                    // Send immediate callback response
                    if (callback)
                        callback({ success: true });
                    // Start countdown
                    const countdownTime = 3;
                    this.io.to(data.gameId).emit('game-countdown', {
                        countdownTime,
                        totalQuestions: game.totalQuestions,
                        serverTime: Date.now()
                    });
                    // Start game after countdown
                    setTimeout(async () => {
                        try {
                            let startedGame = gameManager.startGame();
                            // Get the enhanced game state from store to preserve settings
                            const storedGame = this.gameStore.getGame(data.gameId);
                            const enhancedStartedGame = {
                                ...startedGame,
                                settings: (storedGame === null || storedGame === void 0 ? void 0 : storedGame.settings) || {},
                                gameMode: (storedGame === null || storedGame === void 0 ? void 0 : storedGame.gameMode) || startedGame.gameMode
                            };
                            this.gameStore.setGame(data.gameId, enhancedStartedGame);
                            if (startedGame.currentQuestion) {
                                this.io.to(data.gameId).emit('game-started', {
                                    question: startedGame.currentQuestion,
                                    questionIndex: startedGame.currentQuestionIndex,
                                    timeLimit: startedGame.timePerQuestion,
                                    serverTime: Date.now()
                                });
                                this.io.to(data.gameId).emit('game-state', { ...enhancedStartedGame, pin: data.gameId });
                            }
                        }
                        catch (error) {
                            console.error('Error starting game:', error);
                            socket.emit('error', { message: 'Failed to start game' });
                        }
                    }, countdownTime * 1000);
                    console.log(`🚀 Game ${data.gameId} started by ${socket.data.playerName}`);
                }
                catch (error) {
                    console.error('Error starting game:', error);
                    socket.emit('error', { message: 'Failed to start game' });
                    if (callback)
                        callback({ success: false, error: 'Failed to start game' });
                }
            });
            // Handle answer submission
            socket.on('submit-answer', async (data) => {
                try {
                    const game = this.gameStore.getGame(data.gameId);
                    if (!game) {
                        socket.emit('error', { message: 'Game not found' });
                        return;
                    }
                    const gameManager = this.gameManagers.get(data.gameId);
                    if (!gameManager) {
                        socket.emit('error', { message: 'Game manager not found' });
                        return;
                    }
                    const updatedGame = gameManager.submitAnswer(data.playerId, data.answerIndex, data.timeTaken);
                    this.gameStore.setGame(data.gameId, updatedGame);
                    // Acknowledge answer submission
                    socket.emit('answer-submitted', {
                        acknowledged: true,
                        questionIndex: updatedGame.currentQuestionIndex
                    });
                    // Update game state for all players
                    this.io.to(data.gameId).emit('game-state', { ...updatedGame, pin: data.gameId });
                    // Check if all players have answered
                    const allAnswered = updatedGame.players.every(p => p.hasAnswered);
                    if (allAnswered && updatedGame.currentQuestion) {
                        // Process results after a short delay
                        setTimeout(() => {
                            var _a, _b, _c, _d;
                            const gameWithResults = gameManager.processQuestionResults();
                            this.gameStore.setGame(data.gameId, gameWithResults);
                            // Get detailed results
                            let results = null;
                            this.io.to(data.gameId).emit('question-results', {
                                questionIndex: gameWithResults.currentQuestionIndex,
                                correctAnswerIndex: ((_a = gameWithResults.currentQuestion) === null || _a === void 0 ? void 0 : _a.correctAnswer) || 0,
                                correctAnswerText: (results === null || results === void 0 ? void 0 : results.correctAnswerText) ||
                                    ((_b = gameWithResults.currentQuestion) === null || _b === void 0 ? void 0 : _b.options[((_c = gameWithResults.currentQuestion) === null || _c === void 0 ? void 0 : _c.correctAnswer) || 0]),
                                leaderboard: gameWithResults.players.sort((a, b) => b.score - a.score),
                                resultsStartTime: Date.now(),
                                playerAnswers: results === null || results === void 0 ? void 0 : results.playerAnswers
                            });
                            // Auto-advance to next question after 5 seconds
                            if (((_d = gameWithResults.settings) === null || _d === void 0 ? void 0 : _d.autoNextQuestion) !== false) {
                                setTimeout(() => {
                                    this.handleNextAction({ gameId: data.gameId }).catch(error => {
                                        console.error('Error auto-advancing to next question:', error);
                                    });
                                }, 5000);
                            }
                        }, 1000);
                    }
                    console.log(`✅ Answer submitted by ${data.playerId} in game ${data.gameId}`);
                }
                catch (error) {
                    console.error('Error submitting answer:', error);
                    socket.emit('error', { message: 'Failed to submit answer' });
                }
            });
            // Handle next question/action
            socket.on('next-action', async (data) => {
                await this.handleNextAction(data);
            });
            // Handle player leaving
            socket.on('leave-game', (data) => {
                this.handlePlayerLeave(socket, data.gameId, data.playerId);
            });
            // Team event handlers
            socket.on('toggle-team-mode', (data) => {
                try {
                    const { gameId, enabled, settings } = data;
                    const game = this.gameStore.getGame(gameId);
                    if (!game) {
                        socket.emit('error', { message: 'Game not found' });
                        return;
                    }
                    // Only host can toggle team mode
                    if (socket.data.playerId !== game.hostId) {
                        socket.emit('error', { message: 'Only host can toggle team mode' });
                        return;
                    }
                    // Update game with team mode settings using GameManager
                    const gameManager = this.gameManagers.get(gameId);
                    let updatedGame;
                    if (gameManager) {
                        updatedGame = gameManager.toggleTeamMode(enabled, settings);
                        this.gameStore.setGame(gameId, updatedGame);
                    }
                    else {
                        // Fallback if GameManager not found
                        updatedGame = { ...game, teamMode: enabled, teamSettings: settings };
                        this.gameStore.setGame(gameId, updatedGame);
                    }
                    // Notify all players
                    this.io.to(gameId).emit('team-mode-toggled', { enabled, settings });
                    this.io.to(gameId).emit('game-state', { ...updatedGame, pin: gameId });
                    console.log(`🏆 Team mode ${enabled ? 'enabled' : 'disabled'} for game ${gameId}`);
                }
                catch (error) {
                    console.error('Error toggling team mode:', error);
                    socket.emit('error', { message: 'Failed to toggle team mode' });
                }
            });
            // Handle getting available games
            socket.on('get-available-games', () => {
                try {
                    const availableGames = this.gameStore.getGamesByStatus('waiting');
                    const gameList = availableGames.map(game => {
                        var _a;
                        return ({
                            gameId: game.gameId,
                            pin: game.gameId, // The gameId is the PIN
                            hostName: ((_a = game.players.find(p => p.isHost)) === null || _a === void 0 ? void 0 : _a.name) || 'Unknown',
                            gameMode: game.gameMode,
                            playerCount: game.players.length,
                            maxPlayers: game.maxPlayers || 6,
                            teamMode: game.teamMode || false
                        });
                    });
                    socket.emit('available-games', { games: gameList });
                    console.log(`📋 Sent ${gameList.length} available games to ${socket.id}`);
                }
                catch (error) {
                    console.error('Error getting available games:', error);
                    socket.emit('error', { message: 'Failed to get available games' });
                }
            });
            // Handle game state requests
            socket.on('request-game-state', (data) => {
                try {
                    const { gameId, playerId } = data;
                    if (!gameId) {
                        socket.emit('error', { message: 'Game ID required' });
                        return;
                    }
                    const game = this.gameStore.getGame(gameId);
                    if (!game) {
                        // Clear client's stored game info if game doesn't exist
                        socket.emit('error', { message: 'Game not found' });
                        console.log(`🔍 Game state requested for non-existent game: ${gameId}`);
                        return;
                    }
                    // Verify player is in the game
                    const player = game.players.find(p => p.id === playerId);
                    if (!player) {
                        socket.emit('error', { message: 'Player not in game' });
                        return;
                    }
                    // Join the socket to the game room if not already joined
                    if (!socket.rooms.has(gameId)) {
                        socket.join(gameId);
                        socket.data.gameId = gameId;
                        socket.data.playerId = playerId;
                        socket.data.playerName = player.name;
                    }
                    // Send the current game state
                    socket.emit('game-state', { ...game, pin: gameId });
                    console.log(`📊 Sent game state for ${gameId} to player ${playerId}`);
                }
                catch (error) {
                    console.error('Error handling game state request:', error);
                    socket.emit('error', { message: 'Failed to get game state' });
                }
            });
            // Team handlers will be attached when player joins/creates a game
            // Handle disconnection
            socket.on('disconnect', async () => {
                // Update connection counts
                this.connectionCount--;
                const clientIP = socket.handshake.address || 'unknown';
                const ipCount = this.ipConnectionCounts.get(clientIP) || 0;
                if (ipCount > 1) {
                    this.ipConnectionCounts.set(clientIP, ipCount - 1);
                }
                else {
                    this.ipConnectionCounts.delete(clientIP);
                }
                console.log(`🔌 Client disconnected: ${socket.id} (${this.connectionCount} remaining connections)`);
                if (socket.data.gameId && socket.data.playerId) {
                    try {
                        await this.handlePlayerLeave(socket, socket.data.gameId, socket.data.playerId);
                        // Handle team disconnection
                        await team_socket_handler_1.teamSocketHandler.handleDisconnection(socket.data.playerId, socket.data.gameId);
                    }
                    catch (error) {
                        console.error('Error during disconnect cleanup:', {
                            socketId: socket.id,
                            playerId: socket.data.playerId,
                            gameId: socket.data.gameId,
                            error: error instanceof Error ? error.message : 'Unknown error'
                        });
                    }
                }
            });
        });
    }
    async handlePlayerLeave(socket, gameId, playerId) {
        try {
            const game = this.gameStore.getGame(gameId);
            if (!game)
                return;
            const gameManager = this.gameManagers.get(gameId);
            if (!gameManager)
                return;
            const updatedGame = gameManager.removePlayer(playerId);
            if (updatedGame.players.length === 0) {
                // No players left, delete game
                this.gameStore.deleteGame(gameId);
                this.gameManagers.delete(gameId);
                console.log(`🗑️ Game ${gameId} deleted - no players remaining`);
            }
            else {
                this.gameStore.setGame(gameId, updatedGame);
                // Notify remaining players
                this.io.to(gameId).emit('player-left', {
                    playerId,
                    players: updatedGame.players
                });
                this.io.to(gameId).emit('game-state', { ...updatedGame, pin: gameId });
            }
            socket.leave(gameId);
            console.log(`👋 Player ${playerId} left game ${gameId}`);
        }
        catch (error) {
            console.error('Error handling player leave:', {
                gameId,
                playerId,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined
            });
            // Try to notify other players even if there was an error
            try {
                this.io.to(gameId).emit('player-left', {
                    playerId,
                    players: []
                });
            }
            catch (notifyError) {
                console.error('Failed to notify players about disconnect:', notifyError);
            }
        }
    }
    async handleNextAction(data) {
        try {
            const game = this.gameStore.getGame(data.gameId);
            if (!game)
                return;
            const gameManager = this.gameManagers.get(data.gameId);
            if (!gameManager)
                return;
            if (game.currentQuestionIndex < game.totalQuestions - 1) {
                // Next question
                const nextGame = gameManager.nextQuestion();
                this.gameStore.setGame(data.gameId, nextGame);
                if (nextGame.currentQuestion) {
                    this.io.to(data.gameId).emit('new-question', {
                        question: nextGame.currentQuestion,
                        questionIndex: nextGame.currentQuestionIndex,
                        timeLimit: nextGame.timePerQuestion,
                        serverTime: Date.now()
                    });
                    this.io.to(data.gameId).emit('game-state', { ...nextGame, pin: data.gameId });
                }
            }
            else {
                // Game over
                const finishedGame = gameManager.endGame();
                this.gameStore.setGame(data.gameId, finishedGame);
                // Get final results including team leaderboard if applicable
                const finalResults = {
                    leaderboard: finishedGame.players.sort((a, b) => b.score - a.score)
                };
                if (finishedGame.teamMode && gameManager) {
                    // Team leaderboard functionality not available in base GameManager
                }
                this.io.to(data.gameId).emit('game-over', finalResults);
                // Clean up after game ends
                setTimeout(() => {
                    // Cleanup if needed
                    this.gameStore.deleteGame(data.gameId);
                    this.gameManagers.delete(data.gameId);
                }, 30000); // 30 seconds to view results
            }
        }
        catch (error) {
            console.error('Error handling next action:', error);
        }
    }
    getStats() {
        return {
            connectedClients: this.io.sockets.sockets.size,
            activeGames: this.gameStore.getAvailableGames().length,
            games: this.gameStore.getAvailableGames().map(game => {
                var _a;
                return ({
                    id: game.gameId,
                    status: game.status,
                    players: game.players.length,
                    host: (_a = game.players.find(p => p.isHost)) === null || _a === void 0 ? void 0 : _a.name
                });
            })
        };
    }
}
exports.MultiplayerSocketServer = MultiplayerSocketServer;
// Initialize server function
function initializeSocketServer(httpServer) {
    return new MultiplayerSocketServer(httpServer);
}
