"use strict";
/**
 * Team Socket Handler
 * Handles team-related socket events and game logic
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.teamSocketHandler = exports.TeamSocketHandler = void 0;
const team_manager_1 = require("./team-manager");
class TeamSocketHandler {
    constructor() {
        this.gameTeamSettings = new Map();
        this.gameTeamChats = new Map();
    }
    /**
     * Handle socket connection and attach team event listeners
     */
    handleConnection(socket, playerId, playerName, gameId) {
        // Create team
        socket.on('create-team', (data) => {
            try {
                const { teamName, color, emoji } = data;
                const team = team_manager_1.teamManager.createTeam(teamName, playerId, playerName);
                if (team) {
                    // Join team-specific room for chat
                    const teamRoomId = `${gameId}_team_${team.id}`;
                    socket.join(teamRoomId);
                }
                // Broadcast team creation to all players in game
                socket.to(gameId).emit('team-created', { team });
                socket.emit('team-created', { team });
                this.broadcastTeamUpdate(socket, gameId);
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to create team' });
            }
        });
        // Join team
        socket.on('join-team', (data) => {
            try {
                const { teamId } = data;
                const player = {
                    id: playerId,
                    name: playerName,
                    avatar: '',
                    score: 0,
                    isHost: false,
                    hasAnswered: false,
                    joinedAt: Date.now()
                };
                const success = team_manager_1.teamManager.addPlayerToTeam(teamId, player);
                if (success) {
                    const team = team_manager_1.teamManager.getTeam(teamId);
                    if (team) {
                        // Join team-specific room for chat
                        const teamRoomId = `${gameId}_team_${teamId}`;
                        socket.join(teamRoomId);
                        socket.to(gameId).emit('player-joined-team', { team, playerId });
                        socket.emit('player-joined-team', { team, playerId });
                        this.broadcastTeamUpdate(socket, gameId);
                    }
                }
                else {
                    socket.emit('error', { message: 'Failed to join team' });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to join team' });
            }
        });
        // Leave team
        socket.on('leave-team', () => {
            try {
                const team = team_manager_1.teamManager.removePlayerFromTeam(playerId);
                if (team) {
                    // Leave team-specific room
                    const teamRoomId = `${gameId}_team_${team.id}`;
                    socket.leave(teamRoomId);
                    socket.to(gameId).emit('player-left-team', { teamId: team.id, playerId });
                    socket.emit('player-left-team', { teamId: team.id, playerId });
                }
                this.broadcastTeamUpdate(socket, gameId);
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to leave team' });
            }
        });
        // Update team
        socket.on('update-team', (data) => {
            try {
                const { teamId, name, color, emoji } = data;
                const team = team_manager_1.teamManager.getTeam(teamId);
                // Check if player is captain
                if (team && team.captainId === playerId) {
                    const updates = {};
                    if (name)
                        updates.name = name;
                    if (color)
                        updates.color = color;
                    if (emoji)
                        updates.emoji = emoji;
                    const success = team_manager_1.teamManager.updateTeam(teamId, updates);
                    if (success) {
                        const updatedTeam = team_manager_1.teamManager.getTeam(teamId);
                        if (updatedTeam) {
                            socket.to(gameId).emit('team-updated', { team: updatedTeam });
                            socket.emit('team-updated', { team: updatedTeam });
                        }
                    }
                }
                else {
                    socket.emit('error', { message: 'Only team captain can update team' });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to update team' });
            }
        });
        // Promote to captain
        socket.on('promote-to-captain', (data) => {
            try {
                const { teamId, playerId: newCaptainId } = data;
                const team = team_manager_1.teamManager.getTeam(teamId);
                // Check if current player is captain
                if (team && team.captainId === playerId) {
                    const success = team_manager_1.teamManager.promoteToCaption(teamId, newCaptainId);
                    if (success) {
                        const updatedTeam = team_manager_1.teamManager.getTeam(teamId);
                        if (updatedTeam) {
                            socket.to(gameId).emit('team-updated', { team: updatedTeam });
                            socket.emit('team-updated', { team: updatedTeam });
                        }
                    }
                }
                else {
                    socket.emit('error', { message: 'Only team captain can promote members' });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to promote captain' });
            }
        });
        // Team chat
        socket.on('team-chat', (data) => {
            try {
                const { teamId, message } = data;
                const team = team_manager_1.teamManager.getPlayerTeam(playerId);
                if (team && team.id === teamId) {
                    const chatMessage = {
                        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                        teamId: teamId,
                        playerId: playerId,
                        playerName: playerName,
                        message: message,
                        timestamp: Date.now(),
                        type: 'normal'
                    };
                    // Store message
                    if (!this.gameTeamChats.has(gameId)) {
                        this.gameTeamChats.set(gameId, []);
                    }
                    this.gameTeamChats.get(gameId).push(chatMessage);
                    // Send to team room (all team members)
                    const teamRoomId = `${gameId}_team_${teamId}`;
                    socket.to(teamRoomId).emit('team-chat-message', { message: chatMessage });
                    socket.emit('team-chat-message', { message: chatMessage });
                }
                else {
                    socket.emit('error', { message: 'You are not in this team' });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to send team message' });
            }
        });
        // Team answer
        socket.on('team-answer', (data) => {
            try {
                const { teamId, answer, collaborative } = data;
                const team = team_manager_1.teamManager.getPlayerTeam(playerId);
                if (team && team.id === teamId) {
                    // Check if team captain or collaborative mode allows any member
                    const canSubmit = team.captainId === playerId || collaborative;
                    if (canSubmit) {
                        const teamAnswer = {
                            teamId: teamId,
                            answer: answer,
                            submittedBy: playerId,
                            collaborative: collaborative || false,
                            discussionTime: 0, // Would be calculated based on collaboration time
                            timestamp: Date.now()
                        };
                        const success = team_manager_1.teamManager.submitTeamAnswer(teamAnswer);
                        if (success) {
                            socket.to(gameId).emit('team-answer-submitted', { teamAnswer });
                            socket.emit('team-answer-submitted', { teamAnswer });
                        }
                    }
                    else {
                        socket.emit('error', { message: 'Only team captain can submit answers in this mode' });
                    }
                }
                else {
                    socket.emit('error', { message: 'You are not in this team' });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to submit team answer' });
            }
        });
        // Auto balance teams
        socket.on('auto-balance-teams', () => {
            try {
                // This would require access to all game players
                // Implementation depends on game state management
                const teams = team_manager_1.teamManager.getAllTeams();
                socket.to(gameId).emit('teams-balanced', { teams });
                socket.emit('teams-balanced', { teams });
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to balance teams' });
            }
        });
        // Toggle team mode
        socket.on('toggle-team-mode', (data) => {
            try {
                const { enabled, settings } = data;
                if (settings) {
                    this.gameTeamSettings.set(gameId, settings);
                }
                socket.to(gameId).emit('team-mode-toggled', { enabled, settings });
                socket.emit('team-mode-toggled', { enabled, settings });
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to toggle team mode' });
            }
        });
        // Disband team
        socket.on('disband-team', (data) => {
            try {
                const { teamId } = data;
                const team = team_manager_1.teamManager.getTeam(teamId);
                // Check if player is captain
                if (team && team.captainId === playerId) {
                    const success = team_manager_1.teamManager.disbandTeam(teamId);
                    if (success) {
                        socket.to(gameId).emit('team-disbanded', { teamId });
                        socket.emit('team-disbanded', { teamId });
                        this.broadcastTeamUpdate(socket, gameId);
                    }
                }
                else {
                    socket.emit('error', { message: 'Only team captain can disband team' });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to disband team' });
            }
        });
    }
    /**
     * Handle player disconnection
     */
    handleDisconnection(playerId, gameId) {
        try {
            const team = team_manager_1.teamManager.removePlayerFromTeam(playerId);
            // Team cleanup is handled in removePlayerFromTeam
        }
        catch (error) {
            console.error('Error handling team disconnection:', error);
        }
    }
    /**
     * Get team chat messages for a game
     */
    getTeamChatMessages(gameId) {
        return this.gameTeamChats.get(gameId) || [];
    }
    /**
     * Get team settings for a game
     */
    getTeamSettings(gameId) {
        return this.gameTeamSettings.get(gameId);
    }
    /**
     * Calculate and broadcast team scores
     */
    calculateTeamScores(gameId, correctAnswer, points) {
        const settings = this.gameTeamSettings.get(gameId);
        if (!settings)
            return;
        // This would need to be implemented with access to team answers
        // For now, just broadcast updated leaderboard
        const teams = team_manager_1.teamManager.getTeamLeaderboard();
        // Broadcast to all players in game
    }
    /**
     * Reset teams for new question
     */
    resetTeamsForNewQuestion(gameId) {
        team_manager_1.teamManager.resetTeamAnswers();
    }
    /**
     * Broadcast team updates to all players in game
     */
    broadcastTeamUpdate(socket, gameId) {
        const teams = team_manager_1.teamManager.getTeamLeaderboard();
        socket.to(gameId).emit('team-leaderboard', { teams });
        socket.emit('team-leaderboard', { teams });
    }
    /**
     * Clean up game data
     */
    cleanupGame(gameId) {
        this.gameTeamSettings.delete(gameId);
        this.gameTeamChats.delete(gameId);
    }
}
exports.TeamSocketHandler = TeamSocketHandler;
exports.teamSocketHandler = new TeamSocketHandler();
