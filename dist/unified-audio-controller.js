"use strict";
/**
 * Unified Audio Controller
 * Manages all audio systems in the Music Quiz application
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedAudioController = void 0;
exports.getUnifiedAudioController = getUnifiedAudioController;
exports.resetUnifiedAudioController = resetUnifiedAudioController;
var UnifiedAudioController = /** @class */ (function () {
    function UnifiedAudioController() {
        this.systems = {};
        this.volumeState = {
            volume: 70,
            muted: false,
            lastVolume: 70
        };
        console.log('[UnifiedAudio] Controller initialized');
    }
    /**
     * Register audio systems
     */
    UnifiedAudioController.prototype.registerSystems = function (systems) {
        this.systems = __assign(__assign({}, this.systems), systems);
        console.log('[UnifiedAudio] Systems registered:', Object.keys(this.systems));
        // Make globally accessible
        if (typeof window !== 'undefined') {
            window.unifiedAudioController = this;
        }
    };
    /**
     * Set volume across all audio systems
     */
    UnifiedAudioController.prototype.setVolume = function (volume) {
        return __awaiter(this, void 0, void 0, function () {
            var clampedVolume, results, error_1, errorMsg, errorMsg, errorMsg, successCount, totalCount;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        clampedVolume = Math.max(0, Math.min(100, volume));
                        console.log("[UnifiedAudio] Setting volume to ".concat(clampedVolume, "%"));
                        // Update internal state
                        this.volumeState.volume = clampedVolume;
                        this.volumeState.muted = clampedVolume === 0;
                        results = [];
                        if (!this.systems.audioManager) return [3 /*break*/, 4];
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, this.systems.audioManager.setVolume(clampedVolume)];
                    case 2:
                        _a.sent();
                        results.push({ system: 'MPD Server', success: true });
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        errorMsg = error_1 instanceof Error ? error_1.message : 'Unknown error';
                        results.push({ system: 'MPD Server', success: false, error: errorMsg });
                        console.error("[UnifiedAudio] \u274C MPD server volume failed:", error_1);
                        return [3 /*break*/, 4];
                    case 4:
                        // 2. Set client stream volume (HTML Audio element)
                        if (this.systems.audioOutputSelector) {
                            try {
                                this.systems.audioOutputSelector.setClientVolume(clampedVolume);
                                results.push({ system: 'Client Stream', success: true });
                                console.log("[UnifiedAudio] \u2705 Client stream volume set to ".concat(clampedVolume, "%"));
                            }
                            catch (error) {
                                errorMsg = error instanceof Error ? error.message : 'Unknown error';
                                results.push({ system: 'Client Stream', success: false, error: errorMsg });
                                console.error("[UnifiedAudio] \u274C Client stream volume failed:", error);
                            }
                        }
                        // 3. Set Web Audio mixer volume
                        if (this.systems.audioMixer) {
                            try {
                                this.systems.audioMixer.updateSettings({ masterVolume: clampedVolume / 100 });
                                results.push({ system: 'Web Audio Mixer', success: true });
                                console.log("[UnifiedAudio] \u2705 Web Audio mixer volume set to ".concat(clampedVolume, "%"));
                            }
                            catch (error) {
                                errorMsg = error instanceof Error ? error.message : 'Unknown error';
                                results.push({ system: 'Web Audio Mixer', success: false, error: errorMsg });
                                console.error("[UnifiedAudio] \u274C Web Audio mixer volume failed:", error);
                            }
                        }
                        successCount = results.filter(function (r) { return r.success; }).length;
                        totalCount = results.length;
                        if (successCount === totalCount) {
                            console.log("[UnifiedAudio] \u2705 Volume set to ".concat(clampedVolume, "% across all ").concat(totalCount, " systems"));
                        }
                        else {
                            console.warn("[UnifiedAudio] \u26A0\uFE0F Volume set on ".concat(successCount, "/").concat(totalCount, " systems"));
                            results.filter(function (r) { return !r.success; }).forEach(function (r) {
                                console.error("[UnifiedAudio] Failed on ".concat(r.system, ": ").concat(r.error));
                            });
                        }
                        return [2 /*return*/, Promise.resolve()];
                }
            });
        });
    };
    /**
     * Mute/unmute all audio systems
     */
    UnifiedAudioController.prototype.setMuted = function (muted) {
        return __awaiter(this, void 0, void 0, function () {
            var restoreVolume;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log("[UnifiedAudio] ".concat(muted ? 'Muting' : 'Unmuting', " all audio systems"));
                        if (!muted) return [3 /*break*/, 2];
                        // Store current volume before muting
                        if (this.volumeState.volume > 0) {
                            this.volumeState.lastVolume = this.volumeState.volume;
                        }
                        return [4 /*yield*/, this.setVolume(0)];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 2:
                        restoreVolume = this.volumeState.lastVolume || 70;
                        return [4 /*yield*/, this.setVolume(restoreVolume)];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        this.volumeState.muted = muted;
                        // Also set muted property on client stream
                        if (this.systems.audioOutputSelector) {
                            this.systems.audioOutputSelector.setClientMuted(muted);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get current volume state
     */
    UnifiedAudioController.prototype.getVolumeState = function () {
        return __assign({}, this.volumeState);
    };
    /**
     * Get current volume
     */
    UnifiedAudioController.prototype.getVolume = function () {
        return this.volumeState.volume;
    };
    /**
     * Check if muted
     */
    UnifiedAudioController.prototype.isMuted = function () {
        return this.volumeState.muted;
    };
    /**
     * Get status of all audio systems
     */
    UnifiedAudioController.prototype.getSystemStatus = function () {
        var status = {};
        if (this.systems.audioManager) {
            status.mpdServer = {
                connected: this.systems.audioManager.isReady(),
                volume: this.systems.audioManager.getUserVolume()
            };
        }
        if (this.systems.audioOutputSelector) {
            status.clientStream = {
                volume: this.systems.audioOutputSelector.getClientVolume(),
                muted: this.systems.audioOutputSelector.isClientMuted()
            };
        }
        if (this.systems.audioMixer) {
            status.webAudioMixer = {
                available: true,
                masterVolume: this.volumeState.volume
            };
        }
        return status;
    };
    /**
     * Test all audio systems
     */
    UnifiedAudioController.prototype.testAllSystems = function () {
        return __awaiter(this, void 0, void 0, function () {
            var results, currentVol;
            return __generator(this, function (_a) {
                results = {};
                // Test MPD server
                if (this.systems.audioManager) {
                    try {
                        results.mpdServer = this.systems.audioManager.isReady();
                    }
                    catch (error) {
                        results.mpdServer = false;
                    }
                }
                // Test client stream
                if (this.systems.audioOutputSelector) {
                    try {
                        currentVol = this.systems.audioOutputSelector.getClientVolume();
                        this.systems.audioOutputSelector.setClientVolume(currentVol);
                        results.clientStream = true;
                    }
                    catch (error) {
                        results.clientStream = false;
                    }
                }
                // Test Web Audio mixer
                if (this.systems.audioMixer) {
                    try {
                        // Try to get current settings as a test
                        this.systems.audioMixer.getSettings();
                        results.webAudioMixer = true;
                    }
                    catch (error) {
                        results.webAudioMixer = false;
                    }
                }
                console.log('[UnifiedAudio] System test results:', results);
                return [2 /*return*/, results];
            });
        });
    };
    /**
     * Initialize with default volume
     */
    UnifiedAudioController.prototype.initialize = function () {
        return __awaiter(this, arguments, void 0, function (defaultVolume) {
            if (defaultVolume === void 0) { defaultVolume = 70; }
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log("[UnifiedAudio] Initializing with default volume: ".concat(defaultVolume, "%"));
                        return [4 /*yield*/, this.setVolume(defaultVolume)];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Cleanup
     */
    UnifiedAudioController.prototype.cleanup = function () {
        console.log('[UnifiedAudio] Cleaning up');
        this.systems = {};
        if (typeof window !== 'undefined') {
            delete window.unifiedAudioController;
        }
    };
    return UnifiedAudioController;
}());
exports.UnifiedAudioController = UnifiedAudioController;
// Create global instance
var globalController = null;
function getUnifiedAudioController() {
    if (!globalController) {
        globalController = new UnifiedAudioController();
    }
    return globalController;
}
function resetUnifiedAudioController() {
    if (globalController) {
        globalController.cleanup();
    }
    globalController = null;
}
