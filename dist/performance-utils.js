"use strict";
// Performance utilities for the music quiz application
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManager = exports.MemoryManager = void 0;
exports.useDebounce = useDebounce;
exports.useThrottle = useThrottle;
exports.calculateVirtualItems = calculateVirtualItems;
exports.fuzzySearch = fuzzySearch;
exports.memoize = memoize;
var react_1 = require("react");
// Memory pressure detection
var MemoryManager = /** @class */ (function () {
    function MemoryManager() {
        this.memoryThresholds = {
            warning: 50 * 1024 * 1024, // 50MB
            critical: 100 * 1024 * 1024 // 100MB
        };
    }
    MemoryManager.getInstance = function () {
        if (!MemoryManager.instance) {
            MemoryManager.instance = new MemoryManager();
        }
        return MemoryManager.instance;
    };
    MemoryManager.prototype.checkMemoryPressure = function () {
        if ('memory' in performance) {
            var memory = performance.memory;
            var used = memory.usedJSHeapSize;
            if (used > this.memoryThresholds.critical)
                return 'critical';
            if (used > this.memoryThresholds.warning)
                return 'warning';
        }
        return 'normal';
    };
    MemoryManager.prototype.getMemoryStats = function () {
        if ('memory' in performance) {
            var memory = performance.memory;
            return {
                used: memory.usedJSHeapSize,
                total: memory.totalJSHeapSize,
                limit: memory.jsHeapSizeLimit,
                usedMB: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                pressure: this.checkMemoryPressure()
            };
        }
        return null;
    };
    MemoryManager.prototype.adaptToMemoryPressure = function (pressure) {
        switch (pressure) {
            case 'critical':
                return { virtualListOverscan: 1, maxCacheSize: 50, pageSize: 50 };
            case 'warning':
                return { virtualListOverscan: 2, maxCacheSize: 200, pageSize: 100 };
            default:
                return { virtualListOverscan: 3, maxCacheSize: 500, pageSize: 200 };
        }
    };
    return MemoryManager;
}());
exports.MemoryManager = MemoryManager;
// Debounce hook for performance optimization
function useDebounce(value, delay) {
    var _a = (0, react_1.useState)(value), debouncedValue = _a[0], setDebouncedValue = _a[1];
    (0, react_1.useEffect)(function () {
        var handler = setTimeout(function () {
            setDebouncedValue(value);
        }, delay);
        return function () {
            clearTimeout(handler);
        };
    }, [value, delay]);
    return debouncedValue;
}
// Throttle hook for scroll events and other high-frequency events
function useThrottle(callback, delay) {
    var lastRun = (0, react_1.useRef)(Date.now());
    return (0, react_1.useCallback)((function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        if (Date.now() - lastRun.current >= delay) {
            callback.apply(void 0, args);
            lastRun.current = Date.now();
        }
    }), [callback, delay]);
}
function calculateVirtualItems(scrollTop, itemCount, options) {
    var itemHeight = options.itemHeight, containerHeight = options.containerHeight, _a = options.overscan, overscan = _a === void 0 ? 3 : _a;
    var startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    var endIndex = Math.min(itemCount - 1, Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan);
    return {
        startIndex: startIndex,
        endIndex: endIndex,
        offsetY: startIndex * itemHeight,
        totalHeight: itemCount * itemHeight,
        visibleItems: endIndex - startIndex + 1
    };
}
// Optimized search function with fuzzy matching
function fuzzySearch(items, searchTerm, searchFields) {
    if (!searchTerm)
        return items;
    var searchLower = searchTerm.toLowerCase();
    var searchWords = searchLower.split(' ').filter(function (word) { return word.length > 0; });
    return items.filter(function (item) {
        var searchText = searchFields
            .map(function (field) { return String(item[field] || ''); })
            .join(' ')
            .toLowerCase();
        return searchWords.every(function (word) { return searchText.includes(word); });
    });
}
// Memoization utility for expensive computations
function memoize(fn) {
    var cache = new Map();
    return (function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        var key = JSON.stringify(args);
        if (cache.has(key)) {
            return cache.get(key);
        }
        var result = fn.apply(void 0, args);
        cache.set(key, result);
        // Prevent memory leaks by limiting cache size
        if (cache.size > 1000) {
            var firstKey = cache.keys().next().value;
            cache.delete(firstKey);
        }
        return result;
    });
}
// Cache utility with TTL support
var CacheManager = /** @class */ (function () {
    function CacheManager() {
        this.cache = new Map();
    }
    CacheManager.prototype.set = function (key, data, ttl) {
        if (ttl === void 0) { ttl = 300000; }
        this.cache.set(key, {
            data: data,
            timestamp: Date.now(),
            ttl: ttl
        });
        // Clean up expired entries
        this.cleanup();
    };
    CacheManager.prototype.get = function (key) {
        var entry = this.cache.get(key);
        if (!entry)
            return null;
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    };
    CacheManager.prototype.has = function (key) {
        return this.get(key) !== null;
    };
    CacheManager.prototype.delete = function (key) {
        this.cache.delete(key);
    };
    CacheManager.prototype.clear = function () {
        this.cache.clear();
    };
    CacheManager.prototype.cleanup = function () {
        var now = Date.now();
        for (var _i = 0, _a = this.cache.entries(); _i < _a.length; _i++) {
            var _b = _a[_i], key = _b[0], entry = _b[1];
            if (now - entry.timestamp > entry.ttl) {
                this.cache.delete(key);
            }
        }
    };
    CacheManager.prototype.size = function () {
        this.cleanup();
        return this.cache.size;
    };
    return CacheManager;
}());
exports.CacheManager = CacheManager;
