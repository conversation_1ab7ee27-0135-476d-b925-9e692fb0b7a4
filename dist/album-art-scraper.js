"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedAlbumArtScraper = void 0;
exports.enhancedAlbumArtSearch = enhancedAlbumArtSearch;
var playwright_utils_1 = require("./playwright-utils");
var sharp_1 = __importDefault(require("sharp"));
var promises_1 = __importDefault(require("fs/promises"));
var path_1 = __importDefault(require("path"));
var EnhancedAlbumArtScraper = /** @class */ (function () {
    function EnhancedAlbumArtScraper(cacheDir) {
        if (cacheDir === void 0) { cacheDir = './cache/album-art'; }
        this.automation = new playwright_utils_1.MusicQuizAutomation({ headless: true });
        this.cacheDir = cacheDir;
    }
    EnhancedAlbumArtScraper.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.automation.initialize()];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, promises_1.default.mkdir(this.cacheDir, { recursive: true })];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    EnhancedAlbumArtScraper.prototype.close = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.automation.close()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    EnhancedAlbumArtScraper.prototype.getCacheKey = function (artist, album) {
        return "".concat(artist, "-").concat(album).toLowerCase().replace(/[^a-z0-9]/g, '-');
    };
    EnhancedAlbumArtScraper.prototype.searchAlbumArt = function (artist, album) {
        return __awaiter(this, void 0, void 0, function () {
            var cacheKey, cachePath, cached, _a, results, page;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        cacheKey = this.getCacheKey(artist, album);
                        cachePath = path_1.default.join(this.cacheDir, "".concat(cacheKey, ".json"));
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, promises_1.default.readFile(cachePath, 'utf-8')];
                    case 2:
                        cached = _b.sent();
                        return [2 /*return*/, JSON.parse(cached)];
                    case 3:
                        _a = _b.sent();
                        return [3 /*break*/, 4];
                    case 4:
                        results = [];
                        return [4 /*yield*/, this.automation.createPage()];
                    case 5:
                        page = _b.sent();
                        _b.label = 6;
                    case 6:
                        _b.trys.push([6, , 13, 15]);
                        // 1. Try MusicBrainz first (most reliable)
                        return [4 /*yield*/, this.scrapeMusicBrainz(page, artist, album, results)];
                    case 7:
                        // 1. Try MusicBrainz first (most reliable)
                        _b.sent();
                        // 2. Try Last.fm
                        return [4 /*yield*/, this.scrapeLastFm(page, artist, album, results)];
                    case 8:
                        // 2. Try Last.fm
                        _b.sent();
                        // 3. Try Discogs
                        return [4 /*yield*/, this.scrapeDiscogs(page, artist, album, results)];
                    case 9:
                        // 3. Try Discogs
                        _b.sent();
                        if (!(results.length < 3)) return [3 /*break*/, 11];
                        return [4 /*yield*/, this.scrapeGoogleImages(page, artist, album, results)];
                    case 10:
                        _b.sent();
                        _b.label = 11;
                    case 11:
                        // Sort by quality
                        results.sort(function (a, b) {
                            var qualityOrder = { high: 3, medium: 2, low: 1 };
                            return qualityOrder[b.quality] - qualityOrder[a.quality];
                        });
                        // Cache results
                        return [4 /*yield*/, promises_1.default.writeFile(cachePath, JSON.stringify(results))];
                    case 12:
                        // Cache results
                        _b.sent();
                        return [2 /*return*/, results];
                    case 13: return [4 /*yield*/, page.close()];
                    case 14:
                        _b.sent();
                        return [7 /*endfinally*/];
                    case 15: return [2 /*return*/];
                }
            });
        });
    };
    EnhancedAlbumArtScraper.prototype.scrapeMusicBrainz = function (page, artist, album, results) {
        return __awaiter(this, void 0, void 0, function () {
            var searchUrl, releases, _i, releases_1, releaseUrl, coverArt, highResUrl, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 8, , 9]);
                        searchUrl = "https://musicbrainz.org/search?query=".concat(encodeURIComponent("".concat(artist, " ").concat(album)), "&type=release&limit=25");
                        return [4 /*yield*/, page.goto(searchUrl, { waitUntil: 'networkidle' })];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, page.$$eval('table.tbl tbody tr', function (rows) {
                                return rows.slice(0, 5).map(function (row) {
                                    var link = row.querySelector('a[href*="/release/"]');
                                    return link ? link.href : null;
                                }).filter(Boolean);
                            })];
                    case 2:
                        releases = _a.sent();
                        _i = 0, releases_1 = releases;
                        _a.label = 3;
                    case 3:
                        if (!(_i < releases_1.length)) return [3 /*break*/, 7];
                        releaseUrl = releases_1[_i];
                        return [4 /*yield*/, page.goto(releaseUrl, { waitUntil: 'networkidle' })];
                    case 4:
                        _a.sent();
                        return [4 /*yield*/, page.$eval('.cover-art img', function (img) { return img.src; }).catch(function () { return null; })];
                    case 5:
                        coverArt = _a.sent();
                        if (coverArt && !coverArt.includes('placeholder')) {
                            highResUrl = coverArt.replace(/\-\d+\.jpg$/, '-500.jpg');
                            results.push({
                                url: highResUrl,
                                source: 'musicbrainz',
                                quality: 'high',
                                dimensions: { width: 500, height: 500 }
                            });
                        }
                        _a.label = 6;
                    case 6:
                        _i++;
                        return [3 /*break*/, 3];
                    case 7: return [3 /*break*/, 9];
                    case 8:
                        error_1 = _a.sent();
                        console.error('MusicBrainz scraping error:', error_1);
                        return [3 /*break*/, 9];
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    EnhancedAlbumArtScraper.prototype.scrapeLastFm = function (page, artist, album, results) {
        return __awaiter(this, void 0, void 0, function () {
            var searchUrl, albumArt, sizes, _i, sizes_1, size, url, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        searchUrl = "https://www.last.fm/music/".concat(encodeURIComponent(artist), "/").concat(encodeURIComponent(album));
                        return [4 /*yield*/, page.goto(searchUrl, { waitUntil: 'networkidle' })];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, page.$eval('.album-overview-cover-art img', function (img) { return img.src; }).catch(function () { return null; })];
                    case 2:
                        albumArt = _a.sent();
                        if (albumArt) {
                            sizes = ['300x300', '600x600'];
                            for (_i = 0, sizes_1 = sizes; _i < sizes_1.length; _i++) {
                                size = sizes_1[_i];
                                url = albumArt.replace(/\d+x\d+/, size);
                                results.push({
                                    url: url,
                                    source: 'lastfm',
                                    quality: size === '600x600' ? 'high' : 'medium',
                                    dimensions: {
                                        width: parseInt(size.split('x')[0]),
                                        height: parseInt(size.split('x')[1])
                                    }
                                });
                            }
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_2 = _a.sent();
                        console.error('Last.fm scraping error:', error_2);
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    EnhancedAlbumArtScraper.prototype.scrapeDiscogs = function (page, artist, album, results) {
        return __awaiter(this, void 0, void 0, function () {
            var searchUrl, firstResult, images, _i, images_1, url, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 7, , 8]);
                        searchUrl = "https://www.discogs.com/search/?q=".concat(encodeURIComponent("".concat(artist, " ").concat(album)), "&type=all");
                        return [4 /*yield*/, page.goto(searchUrl, { waitUntil: 'networkidle' })];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, page.$('.search_result_title a')];
                    case 2:
                        firstResult = _a.sent();
                        if (!firstResult) return [3 /*break*/, 6];
                        return [4 /*yield*/, firstResult.click()];
                    case 3:
                        _a.sent();
                        return [4 /*yield*/, page.waitForLoadState('networkidle')];
                    case 4:
                        _a.sent();
                        return [4 /*yield*/, page.$$eval('.image_gallery img', function (imgs) { return imgs.map(function (img) { return img.src; }).slice(0, 3); })];
                    case 5:
                        images = _a.sent();
                        for (_i = 0, images_1 = images; _i < images_1.length; _i++) {
                            url = images_1[_i];
                            results.push({
                                url: url,
                                source: 'discogs',
                                quality: 'high'
                            });
                        }
                        _a.label = 6;
                    case 6: return [3 /*break*/, 8];
                    case 7:
                        error_3 = _a.sent();
                        console.error('Discogs scraping error:', error_3);
                        return [3 /*break*/, 8];
                    case 8: return [2 /*return*/];
                }
            });
        });
    };
    EnhancedAlbumArtScraper.prototype.scrapeGoogleImages = function (page, artist, album, results) {
        return __awaiter(this, void 0, void 0, function () {
            var searchUrl, images, _i, images_2, url, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        searchUrl = "https://www.google.com/search?q=".concat(encodeURIComponent("".concat(artist, " ").concat(album, " album cover")), "&tbm=isch&tbs=isz:l");
                        return [4 /*yield*/, page.goto(searchUrl, { waitUntil: 'networkidle' })];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, page.$$eval('img[alt*="album"], img[alt*="cover"]', function (imgs) {
                                return imgs
                                    .map(function (img) { return img.src; })
                                    .filter(function (src) { return src && !src.startsWith('data:'); })
                                    .slice(0, 5);
                            })];
                    case 2:
                        images = _a.sent();
                        for (_i = 0, images_2 = images; _i < images_2.length; _i++) {
                            url = images_2[_i];
                            results.push({
                                url: url,
                                source: 'google',
                                quality: 'medium'
                            });
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_4 = _a.sent();
                        console.error('Google Images scraping error:', error_4);
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    EnhancedAlbumArtScraper.prototype.downloadAndProcessImage = function (imageUrl, outputPath, options) {
        return __awaiter(this, void 0, void 0, function () {
            var page, response, buffer, sharpInstance;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.automation.createPage()];
                    case 1:
                        page = _a.sent();
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, , 6, 8]);
                        return [4 /*yield*/, page.goto(imageUrl)];
                    case 3:
                        response = _a.sent();
                        if (!response || !response.ok()) {
                            throw new Error('Failed to load image');
                        }
                        return [4 /*yield*/, response.body()];
                    case 4:
                        buffer = _a.sent();
                        sharpInstance = (0, sharp_1.default)(buffer);
                        if ((options === null || options === void 0 ? void 0 : options.width) || (options === null || options === void 0 ? void 0 : options.height)) {
                            sharpInstance = sharpInstance.resize(options.width, options.height, {
                                fit: 'cover',
                                position: 'center'
                            });
                        }
                        if (options === null || options === void 0 ? void 0 : options.format) {
                            sharpInstance = sharpInstance.toFormat(options.format, {
                                quality: options.quality || 90
                            });
                        }
                        return [4 /*yield*/, sharpInstance.toFile(outputPath)];
                    case 5:
                        _a.sent();
                        return [3 /*break*/, 8];
                    case 6: return [4 /*yield*/, page.close()];
                    case 7:
                        _a.sent();
                        return [7 /*endfinally*/];
                    case 8: return [2 /*return*/];
                }
            });
        });
    };
    EnhancedAlbumArtScraper.prototype.validateImageQuality = function (imagePath) {
        return __awaiter(this, void 0, void 0, function () {
            var metadata, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, (0, sharp_1.default)(imagePath).metadata()];
                    case 1:
                        metadata = _b.sent();
                        return [2 /*return*/, {
                                isValid: true,
                                width: metadata.width,
                                height: metadata.height,
                                format: metadata.format
                            }];
                    case 2:
                        _a = _b.sent();
                        return [2 /*return*/, { isValid: false }];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    return EnhancedAlbumArtScraper;
}());
exports.EnhancedAlbumArtScraper = EnhancedAlbumArtScraper;
// Example usage function
function enhancedAlbumArtSearch(artist, album, downloadPath) {
    return __awaiter(this, void 0, void 0, function () {
        var scraper, results, bestResult;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    scraper = new EnhancedAlbumArtScraper();
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, , 6, 8]);
                    return [4 /*yield*/, scraper.initialize()];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, scraper.searchAlbumArt(artist, album)];
                case 3:
                    results = _a.sent();
                    if (!(downloadPath && results.length > 0)) return [3 /*break*/, 5];
                    bestResult = results[0];
                    return [4 /*yield*/, scraper.downloadAndProcessImage(bestResult.url, downloadPath, {
                            width: 600,
                            height: 600,
                            format: 'jpeg',
                            quality: 95
                        })];
                case 4:
                    _a.sent();
                    _a.label = 5;
                case 5: return [2 /*return*/, results];
                case 6: return [4 /*yield*/, scraper.close()];
                case 7:
                    _a.sent();
                    return [7 /*endfinally*/];
                case 8: return [2 /*return*/];
            }
        });
    });
}
