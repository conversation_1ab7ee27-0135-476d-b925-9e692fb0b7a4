"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientTokenManager = void 0;
/**
 * Client-side token manager for localStorage operations
 * This is safe to use on the client-side as it doesn't involve JWT operations
 */
var ClientTokenManager = /** @class */ (function () {
    function ClientTokenManager() {
    }
    ClientTokenManager.getToken = function () {
        if (typeof window === 'undefined')
            return null;
        return localStorage.getItem(this.TOKEN_KEY);
    };
    ClientTokenManager.setToken = function (token) {
        if (typeof window === 'undefined')
            return;
        localStorage.setItem(this.TOKEN_KEY, token);
    };
    ClientTokenManager.removeToken = function () {
        if (typeof window === 'undefined')
            return;
        localStorage.removeItem(this.TOKEN_KEY);
        localStorage.removeItem(this.USER_KEY);
    };
    ClientTokenManager.getStoredUser = function () {
        if (typeof window === 'undefined')
            return null;
        try {
            var userData = localStorage.getItem(this.USER_KEY);
            return userData ? JSON.parse(userData) : null;
        }
        catch (_a) {
            return null;
        }
    };
    ClientTokenManager.setStoredUser = function (user) {
        if (typeof window === 'undefined')
            return;
        localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    };
    ClientTokenManager.TOKEN_KEY = 'auth_token';
    ClientTokenManager.USER_KEY = 'current_user';
    return ClientTokenManager;
}());
exports.ClientTokenManager = ClientTokenManager;
