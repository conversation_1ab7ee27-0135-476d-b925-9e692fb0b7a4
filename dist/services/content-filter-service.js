/**
 * Content Filter Service
 *
 * Handles filtering of music content based on user preferences
 */
import { DEFAULT_FILTERS, FILTER_PRESETS } from '@/lib/types/filters';
export class ContentFilterService {
    constructor() {
        this.currentFilters = DEFAULT_FILTERS;
        this.filterSettings = {
            enabled: false,
            applyToAllModes: true
        };
        // Special categories that should be treated as quiz categories, not genres
        this.SPECIAL_CATEGORIES = ['all-time-favorites', 'MyItunes'];
    }
    static getInstance() {
        if (!ContentFilterService.instance) {
            ContentFilterService.instance = new ContentFilterService();
        }
        return ContentFilterService.instance;
    }
    /**
     * Load filter settings from localStorage or user preferences
     */
    loadSettings() {
        if (typeof window === 'undefined')
            return this.filterSettings;
        try {
            const stored = localStorage.getItem('gameFilterSettings');
            if (stored) {
                this.filterSettings = JSON.parse(stored);
                if (this.filterSettings.customFilters) {
                    this.currentFilters = this.filterSettings.customFilters;
                }
                else if (this.filterSettings.activePresetId) {
                    const preset = this.getPreset(this.filterSettings.activePresetId);
                    if (preset) {
                        this.currentFilters = preset.filters;
                    }
                }
            }
        }
        catch (error) {
            console.error('Failed to load filter settings:', error);
        }
        return this.filterSettings;
    }
    /**
     * Save filter settings to localStorage
     */
    saveSettings(settings) {
        this.filterSettings = settings;
        if (typeof window !== 'undefined') {
            localStorage.setItem('gameFilterSettings', JSON.stringify(settings));
        }
    }
    /**
     * Apply a filter preset
     */
    applyPreset(presetId) {
        const preset = this.getPreset(presetId);
        if (preset) {
            this.currentFilters = preset.filters;
            this.filterSettings.activePresetId = presetId;
            this.filterSettings.customFilters = undefined;
            this.saveSettings(this.filterSettings);
        }
    }
    /**
     * Apply custom filters
     */
    applyCustomFilters(filters) {
        this.currentFilters = filters;
        this.filterSettings.customFilters = filters;
        this.filterSettings.activePresetId = undefined;
        this.saveSettings(this.filterSettings);
    }
    /**
     * Get a filter preset by ID
     */
    getPreset(id) {
        return FILTER_PRESETS.find(preset => preset.id === id);
    }
    /**
     * Get all available presets
     */
    getPresets() {
        return FILTER_PRESETS;
    }
    /**
     * Check if filters are currently active
     */
    isFilteringEnabled() {
        return this.filterSettings.enabled;
    }
    /**
     * Enable/disable filtering
     */
    setFilteringEnabled(enabled) {
        this.filterSettings.enabled = enabled;
        this.saveSettings(this.filterSettings);
    }
    /**
     * Get current active filters
     */
    getCurrentFilters() {
        return this.currentFilters;
    }
    /**
     * Get filters for a specific game mode
     */
    getFiltersForMode(gameMode) {
        if (!this.filterSettings.applyToAllModes && this.filterSettings.modeSpecificFilters?.[gameMode]) {
            return this.filterSettings.modeSpecificFilters[gameMode];
        }
        return this.currentFilters;
    }
    /**
     * Filter an array of tracks based on current filters
     */
    filterTracks(tracks, gameMode) {
        if (!this.filterSettings.enabled) {
            return tracks;
        }
        const filters = gameMode ? this.getFiltersForMode(gameMode) : this.currentFilters;
        return tracks.filter(track => this.trackMatchesFilters(track, filters));
    }
    /**
     * Check if a single track matches the filters
     */
    trackMatchesFilters(track, filters) {
        // Genre and special category filter
        if (filters.genres.values.length > 0) {
            // Check if any of the filter values are special categories
            const specialCategories = filters.genres.values.filter(g => this.SPECIAL_CATEGORIES.includes(g));
            const regularGenres = filters.genres.values.filter(g => !this.SPECIAL_CATEGORIES.includes(g));
            let matches = false;
            // Check regular genres
            if (regularGenres.length > 0 && track.genre) {
                matches = regularGenres.some(genre => track.genre?.toLowerCase().includes(genre.toLowerCase()));
            }
            // Check special categories
            if (specialCategories.length > 0) {
                // Handle MyItunes specially - check mpdFilePath
                if (specialCategories.includes('MyItunes') && track.mpdFilePath?.startsWith('MyItunes/')) {
                    matches = true;
                }
                // Handle all-time-favorites - check quizCategories JSON field
                if (specialCategories.includes('all-time-favorites') && track.quizCategories) {
                    try {
                        const categories = JSON.parse(track.quizCategories);
                        if (Array.isArray(categories) && categories.includes('all-time-favorites')) {
                            matches = true;
                        }
                    }
                    catch (e) {
                        // Invalid JSON, skip
                    }
                }
            }
            if (filters.genres.mode === 'include' && !matches)
                return false;
            if (filters.genres.mode === 'exclude' && matches)
                return false;
        }
        // Year range filter
        if (filters.yearRange.enabled) {
            if (!track.year)
                return false;
            if (filters.yearRange.min && track.year < filters.yearRange.min)
                return false;
            if (filters.yearRange.max && track.year > filters.yearRange.max)
                return false;
        }
        // Chart filter
        if (!filters.charts.includeChartMusic && track.chartPosition)
            return false;
        if (!filters.charts.includeNonChartMusic && !track.chartPosition)
            return false;
        if (filters.charts.countries?.length && track.chartPosition) {
            if (!filters.charts.countries.includes(track.chartCountry))
                return false;
        }
        // Quality filters
        if (filters.quality.minDifficulty && track.difficultyRating < filters.quality.minDifficulty)
            return false;
        if (filters.quality.maxDifficulty && track.difficultyRating > filters.quality.maxDifficulty)
            return false;
        if (filters.quality.minPopularity && track.popularityScore < filters.quality.minPopularity)
            return false;
        // Check albumArtUrl field instead of non-existent hasAlbumArt
        if (filters.quality.requireAlbumArt && !track.albumArtUrl)
            return false;
        // Metadata filters
        if (filters.metadata.requireYear && !track.year)
            return false;
        if (filters.metadata.requireGenre && !track.genre)
            return false;
        if (filters.metadata.requireAlbum && !track.album)
            return false;
        // Remove isCompilation check as this field doesn't exist in QuizTrack schema
        // Could potentially check album name for "compilation" or "greatest hits" patterns
        if (filters.metadata.excludeCompilations) {
            const albumLower = track.album?.toLowerCase() || '';
            const titleLower = track.title?.toLowerCase() || '';
            if (albumLower.includes('compilation') || albumLower.includes('greatest hits') ||
                albumLower.includes('best of') || titleLower.includes('compilation'))
                return false;
        }
        if (filters.metadata.excludeLiveRecordings && track.title?.toLowerCase().includes('live'))
            return false;
        if (filters.metadata.excludeRemixes && track.title?.toLowerCase().includes('remix'))
            return false;
        // Source filters - check mpdFilePath instead of non-existent source field
        if (!filters.sources.includeMyItunes && track.mpdFilePath?.startsWith('MyItunes/'))
            return false;
        if (!filters.sources.includeSharedLibrary && !track.mpdFilePath?.startsWith('MyItunes/'))
            return false;
        // Folder filters
        if (filters.folders?.values.length > 0) {
            let trackFolders = [];
            // Extract folder from mpdFilePath (primary source)
            if (track.mpdFilePath) {
                const firstPathSegment = track.mpdFilePath.split('/')[0];
                if (firstPathSegment) {
                    trackFolders.push(firstPathSegment);
                }
            }
            // Also check quizCategories for folder information
            if (track.quizCategories) {
                try {
                    const categories = JSON.parse(track.quizCategories);
                    // Add any categories that look like folder names (e.g., DC2016, Billboard2020)
                    categories.forEach(cat => {
                        if (cat && !trackFolders.includes(cat)) {
                            trackFolders.push(cat);
                        }
                    });
                }
                catch {
                    // Ignore JSON parsing errors
                }
            }
            // Check if any of the track's folders match the filter
            const matches = filters.folders.values.some(filterFolder => trackFolders.some(trackFolder => trackFolder.toLowerCase() === filterFolder.toLowerCase()));
            if (filters.folders.mode === 'include' && !matches)
                return false;
            if (filters.folders.mode === 'exclude' && matches)
                return false;
        }
        return true;
    }
    /**
     * Get statistics about how filters affect the library
     */
    async getFilterStatistics(totalTracks) {
        // This would need to be implemented with actual database queries
        // For now, return a placeholder
        return {
            totalTracks,
            filteredTracks: totalTracks,
            percentageRemaining: 100,
            removedByFilter: {}
        };
    }
    /**
     * Export current filter configuration
     */
    exportFilters() {
        return JSON.stringify({
            settings: this.filterSettings,
            filters: this.currentFilters
        }, null, 2);
    }
    /**
     * Import filter configuration
     */
    importFilters(jsonString) {
        try {
            const data = JSON.parse(jsonString);
            if (data.settings) {
                this.filterSettings = data.settings;
            }
            if (data.filters) {
                this.currentFilters = data.filters;
            }
            this.saveSettings(this.filterSettings);
            return true;
        }
        catch (error) {
            console.error('Failed to import filters:', error);
            return false;
        }
    }
}
