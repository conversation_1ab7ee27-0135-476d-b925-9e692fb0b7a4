"use strict";
/**
 * Performance Monitoring Service
 * Tracks application performance metrics and provides insights
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.performanceMonitor = exports.PerformanceMonitor = void 0;
var logger_1 = require("@/lib/logger");
/**
 * Performance Monitor for tracking application metrics
 */
var PerformanceMonitor = /** @class */ (function () {
    function PerformanceMonitor() {
        this.metrics = [];
        this.maxMetrics = 1000;
        this.errorCount = 0;
        this.totalOperations = 0;
        // Set up performance observers if available
        this.setupPerformanceObservers();
        logger_1.logger.info('PerformanceMonitor initialized');
    }
    PerformanceMonitor.getInstance = function () {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor();
        }
        return PerformanceMonitor.instance;
    };
    /**
     * Record a timing metric
     */
    PerformanceMonitor.prototype.recordTiming = function (name, duration, tags) {
        this.addMetric({
            name: name,
            value: duration,
            timestamp: Date.now(),
            tags: tags,
            type: 'timing'
        });
        this.totalOperations++;
        // Log slow operations
        if (duration > 1000) { // > 1 second
            logger_1.logger.warn("Slow operation detected: ".concat(name), { duration: duration, tags: tags });
        }
    };
    /**
     * Record a counter metric
     */
    PerformanceMonitor.prototype.recordCounter = function (name, value, tags) {
        if (value === void 0) { value = 1; }
        this.addMetric({
            name: name,
            value: value,
            timestamp: Date.now(),
            tags: tags,
            type: 'counter'
        });
    };
    /**
     * Record a gauge metric (current value)
     */
    PerformanceMonitor.prototype.recordGauge = function (name, value, tags) {
        this.addMetric({
            name: name,
            value: value,
            timestamp: Date.now(),
            tags: tags,
            type: 'gauge'
        });
    };
    /**
     * Record an error
     */
    PerformanceMonitor.prototype.recordError = function (operation, error, tags) {
        this.errorCount++;
        this.recordCounter('errors', 1, __assign({ operation: operation, error: error.name }, tags));
        logger_1.logger.error("Operation failed: ".concat(operation), error, tags);
    };
    /**
     * Measure and record a function execution
     */
    PerformanceMonitor.prototype.measure = function (name, fn, tags) {
        var start = performance.now();
        try {
            var result = fn();
            var duration = performance.now() - start;
            this.recordTiming(name, duration, tags);
            return result;
        }
        catch (error) {
            var duration = performance.now() - start;
            this.recordTiming(name, duration, __assign(__assign({}, tags), { error: 'true' }));
            this.recordError(name, error, tags);
            throw error;
        }
    };
    /**
     * Measure and record an async function execution
     */
    PerformanceMonitor.prototype.measureAsync = function (name, fn, tags) {
        return __awaiter(this, void 0, void 0, function () {
            var start, result, duration, error_1, duration;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        start = performance.now();
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, fn()];
                    case 2:
                        result = _a.sent();
                        duration = performance.now() - start;
                        this.recordTiming(name, duration, tags);
                        return [2 /*return*/, result];
                    case 3:
                        error_1 = _a.sent();
                        duration = performance.now() - start;
                        this.recordTiming(name, duration, __assign(__assign({}, tags), { error: 'true' }));
                        this.recordError(name, error_1, tags);
                        throw error_1;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get performance report
     */
    PerformanceMonitor.prototype.getReport = function (timeRangeMs) {
        if (timeRangeMs === void 0) { timeRangeMs = 5 * 60 * 1000; }
        var now = Date.now();
        var cutoff = now - timeRangeMs;
        var recentMetrics = this.metrics.filter(function (m) { return m.timestamp >= cutoff; });
        var timingMetrics = recentMetrics.filter(function (m) { return m.type === 'timing'; });
        var averageResponseTime = timingMetrics.length > 0
            ? timingMetrics.reduce(function (sum, m) { return sum + m.value; }, 0) / timingMetrics.length
            : 0;
        var slowestOperations = timingMetrics
            .sort(function (a, b) { return b.value - a.value; })
            .slice(0, 10)
            .map(function (m) { return ({ name: m.name, duration: m.value }); });
        var errorRate = this.totalOperations > 0 ? (this.errorCount / this.totalOperations) * 100 : 0;
        return {
            metrics: recentMetrics,
            summary: {
                totalMetrics: recentMetrics.length,
                averageResponseTime: averageResponseTime,
                slowestOperations: slowestOperations,
                errorRate: errorRate
            },
            timeRange: {
                start: cutoff,
                end: now
            }
        };
    };
    /**
     * Get metrics by name
     */
    PerformanceMonitor.prototype.getMetricsByName = function (name, timeRangeMs) {
        if (timeRangeMs === void 0) { timeRangeMs = 5 * 60 * 1000; }
        var cutoff = Date.now() - timeRangeMs;
        return this.metrics.filter(function (m) { return m.name === name && m.timestamp >= cutoff; });
    };
    /**
     * Get current system performance info
     */
    PerformanceMonitor.prototype.getSystemInfo = function () {
        var info = {
            timestamp: Date.now(),
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
            language: typeof navigator !== 'undefined' ? navigator.language : 'unknown'
        };
        // Add memory info if available
        if (typeof performance !== 'undefined' && 'memory' in performance) {
            var memory = performance.memory;
            info.memory = {
                usedJSHeapSize: memory.usedJSHeapSize,
                totalJSHeapSize: memory.totalJSHeapSize,
                jsHeapSizeLimit: memory.jsHeapSizeLimit
            };
        }
        // Add connection info if available
        if (typeof navigator !== 'undefined' && 'connection' in navigator) {
            var connection = navigator.connection;
            info.connection = {
                effectiveType: connection.effectiveType,
                downlink: connection.downlink,
                rtt: connection.rtt
            };
        }
        return info;
    };
    /**
     * Clear old metrics
     */
    PerformanceMonitor.prototype.cleanup = function () {
        var cutoff = Date.now() - (30 * 60 * 1000); // Keep 30 minutes
        this.metrics = this.metrics.filter(function (m) { return m.timestamp >= cutoff; });
        // Reset counters if they get too high
        if (this.totalOperations > 10000) {
            this.totalOperations = Math.floor(this.totalOperations / 2);
            this.errorCount = Math.floor(this.errorCount / 2);
        }
    };
    /**
     * Export metrics for external analysis
     */
    PerformanceMonitor.prototype.exportMetrics = function () {
        var report = this.getReport();
        return JSON.stringify(report, null, 2);
    };
    PerformanceMonitor.prototype.addMetric = function (metric) {
        this.metrics.push(metric);
        // Implement circular buffer
        if (this.metrics.length > this.maxMetrics) {
            this.metrics.shift();
        }
    };
    PerformanceMonitor.prototype.setupPerformanceObservers = function () {
        var _this = this;
        if (typeof window === 'undefined')
            return;
        // Observe navigation timing
        if ('PerformanceObserver' in window) {
            try {
                var observer = new PerformanceObserver(function (list) {
                    for (var _i = 0, _a = list.getEntries(); _i < _a.length; _i++) {
                        var entry = _a[_i];
                        if (entry.entryType === 'navigation') {
                            var navEntry = entry;
                            _this.recordTiming('page-load', navEntry.loadEventEnd - navEntry.fetchStart);
                            _this.recordTiming('dom-content-loaded', navEntry.domContentLoadedEventEnd - navEntry.fetchStart);
                        }
                        else if (entry.entryType === 'paint') {
                            _this.recordTiming("paint-".concat(entry.name), entry.startTime);
                        }
                    }
                });
                observer.observe({ entryTypes: ['navigation', 'paint'] });
            }
            catch (error) {
                logger_1.logger.warn('Failed to setup PerformanceObserver', { error: error });
            }
        }
        // Monitor memory usage periodically
        setInterval(function () {
            var systemInfo = _this.getSystemInfo();
            if (systemInfo.memory) {
                _this.recordGauge('memory-used', systemInfo.memory.usedJSHeapSize);
                _this.recordGauge('memory-total', systemInfo.memory.totalJSHeapSize);
            }
        }, 30000); // Every 30 seconds
    };
    PerformanceMonitor.instance = null;
    return PerformanceMonitor;
}());
exports.PerformanceMonitor = PerformanceMonitor;
// Export singleton instance
exports.performanceMonitor = PerformanceMonitor.getInstance();
// Automatic cleanup every 5 minutes
if (typeof window !== 'undefined') {
    setInterval(function () {
        exports.performanceMonitor.cleanup();
    }, 5 * 60 * 1000);
}
