"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMPDConnectionString = exports.validateMPDConfig = exports.getEnvironmentConfig = exports.MPD_SETTINGS = exports.MPD_CONFIG = void 0;
// MPD Configuration
exports.MPD_CONFIG = {
    host: process.env.MPD_HOST || 'localhost',
    port: parseInt(process.env.MPD_PORT || '6600'),
    password: process.env.MPD_PASSWORD,
    // Uncomment for Unix socket connection
    // path: process.env.MPD_SOCKET_PATH || '/run/mpd/socket'
};
// MPD Connection Settings
exports.MPD_SETTINGS = {
    // Reconnection settings
    maxReconnectAttempts: 5,
    reconnectDelay: 1000, // milliseconds
    // Default volume settings
    defaultVolume: 50,
    maxVolume: 100,
    // Queue settings
    maxQueueSize: 1000,
    // Search settings
    searchLimit: 100,
    // Update settings
    autoUpdateDatabase: true,
    updateInterval: 300000, // 5 minutes
};
// Environment-specific configurations
var getEnvironmentConfig = function () {
    var env = process.env.NODE_ENV || 'development';
    switch (env) {
        case 'production':
            return __assign(__assign({}, exports.MPD_CONFIG), { host: process.env.MPD_HOST || 'mpd-server', port: parseInt(process.env.MPD_PORT || '6600') });
        case 'development':
            return __assign(__assign({}, exports.MPD_CONFIG), { host: process.env.MPD_HOST || 'localhost', port: parseInt(process.env.MPD_PORT || '6600') });
        case 'test':
            return __assign(__assign({}, exports.MPD_CONFIG), { host: 'localhost', port: 6601 });
        default:
            return exports.MPD_CONFIG;
    }
};
exports.getEnvironmentConfig = getEnvironmentConfig;
// Validation function for MPD configuration
var validateMPDConfig = function (config) {
    if (config.path) {
        // Unix socket connection
        return typeof config.path === 'string' && config.path.length > 0;
    }
    else {
        // TCP connection
        return (typeof config.host === 'string' &&
            config.host.length > 0 &&
            typeof config.port === 'number' &&
            config.port > 0 &&
            config.port <= 65535);
    }
};
exports.validateMPDConfig = validateMPDConfig;
// Helper function to get connection string for logging
var getMPDConnectionString = function (config) {
    if (config.path) {
        return "unix:".concat(config.path);
    }
    else {
        var auth = config.password ? '***@' : '';
        return "tcp://".concat(auth).concat(config.host, ":").concat(config.port);
    }
};
exports.getMPDConnectionString = getMPDConnectionString;
exports.default = exports.MPD_CONFIG;
