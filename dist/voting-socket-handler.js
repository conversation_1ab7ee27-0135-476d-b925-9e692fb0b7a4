/**
 * Voting Socket Handler
 * Handles voting-related socket events and real-time voting sessions
 */
import { votingManager } from './voting-manager';
export class VotingSocketHandler {
    constructor() {
        this.gameVotingSessions = new Map(); // gameId -> sessionId
        this.sessionTimers = new Map();
    }
    /**
     * Handle socket connection and attach voting event listeners
     */
    handleConnection(socket, playerId, playerName, gameId) {
        // Create voting session (host only)
        socket.on('create-voting', (data) => {
            try {
                const { type, title, description, options, timeLimit = 30 } = data;
                // Check if player is host (this would need to be validated)
                // For now, we'll allow any player to create voting sessions
                const sessionId = `voting_${gameId}_${Date.now()}`;
                // Get total players from game (this would come from game state)
                const totalPlayers = 4; // Placeholder - should get from actual game state
                const session = votingManager.createVotingSession(sessionId, type, title, description, options, timeLimit, totalPlayers, playerId);
                // Track session for this game
                this.gameVotingSessions.set(gameId, sessionId);
                // Broadcast to all players in game
                socket.to(gameId).emit('voting-session-created', { session });
                socket.emit('voting-session-created', { session });
                // Start time update timer
                this.startTimeUpdateTimer(sessionId, gameId, socket);
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to create voting session' });
            }
        });
        // Submit vote
        socket.on('submit-vote', (data) => {
            try {
                const { sessionId, optionIndex } = data;
                const success = votingManager.submitVote(sessionId, playerId, playerName, optionIndex);
                if (success) {
                    const session = votingManager.getVotingSession(sessionId);
                    if (session) {
                        // Broadcast vote submission
                        socket.to(gameId).emit('vote-submitted', {
                            sessionId,
                            playerId,
                            optionIndex,
                            totalVotes: session.votes.length
                        });
                        socket.emit('vote-submitted', {
                            sessionId,
                            playerId,
                            optionIndex,
                            totalVotes: session.votes.length
                        });
                        // Broadcast updated session
                        socket.to(gameId).emit('voting-session-updated', { session });
                        socket.emit('voting-session-updated', { session });
                        // Send voting stats
                        this.broadcastVotingStats(sessionId, gameId, socket);
                        // Check if voting is complete
                        if (session.status === 'completed' && session.result) {
                            socket.to(gameId).emit('voting-completed', {
                                sessionId,
                                result: session.result
                            });
                            socket.emit('voting-completed', {
                                sessionId,
                                result: session.result
                            });
                            // Clear timer
                            this.clearTimer(sessionId);
                        }
                    }
                }
                else {
                    socket.emit('error', { message: 'Failed to submit vote' });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to submit vote' });
            }
        });
        // Skip voting (host only)
        socket.on('skip-voting', (data) => {
            try {
                const { sessionId } = data;
                const result = votingManager.skipVoting(sessionId, playerId);
                if (result) {
                    // Broadcast completion
                    socket.to(gameId).emit('voting-completed', { sessionId, result });
                    socket.emit('voting-completed', { sessionId, result });
                    // Clear timer
                    this.clearTimer(sessionId);
                }
                else {
                    socket.emit('error', { message: 'Cannot skip voting' });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to skip voting' });
            }
        });
        // Get current voting session
        socket.on('get-voting-session', (data) => {
            try {
                const { sessionId } = data;
                const session = votingManager.getVotingSession(sessionId);
                if (session) {
                    socket.emit('voting-session-updated', { session });
                }
            }
            catch (error) {
                socket.emit('error', { message: 'Failed to get voting session' });
            }
        });
    }
    /**
     * Handle player disconnection
     */
    handleDisconnection(playerId, gameId) {
        // Voting continues even if players disconnect
        // No special handling needed for voting sessions
    }
    /**
     * Start automatic voting session for game phase
     */
    startAutomaticVoting(gameId, type, title, description, options, totalPlayers, timeLimit = 30, socket) {
        const sessionId = `auto_voting_${gameId}_${Date.now()}`;
        const session = votingManager.createVotingSession(sessionId, type, title, description, options, timeLimit, totalPlayers);
        // Track session for this game
        this.gameVotingSessions.set(gameId, sessionId);
        // Broadcast to all players in game
        socket.to(gameId).emit('voting-session-created', { session });
        socket.emit('voting-session-created', { session });
        // Start time update timer
        this.startTimeUpdateTimer(sessionId, gameId, socket);
        return sessionId;
    }
    /**
     * Get current voting session for game
     */
    getCurrentVotingSession(gameId) {
        const sessionId = this.gameVotingSessions.get(gameId);
        if (!sessionId)
            return null;
        return votingManager.getVotingSession(sessionId);
    }
    /**
     * Check if player has voted in current session
     */
    hasPlayerVoted(gameId, playerId) {
        const sessionId = this.gameVotingSessions.get(gameId);
        if (!sessionId)
            return false;
        return votingManager.hasPlayerVoted(sessionId, playerId);
    }
    /**
     * Get player's vote in current session
     */
    getPlayerVote(gameId, playerId) {
        const sessionId = this.gameVotingSessions.get(gameId);
        if (!sessionId)
            return null;
        return votingManager.getPlayerVote(sessionId, playerId);
    }
    /**
     * Start time update timer for voting session
     */
    startTimeUpdateTimer(sessionId, gameId, socket) {
        const timer = setInterval(() => {
            const timeRemaining = votingManager.getTimeRemaining(sessionId);
            if (timeRemaining <= 0) {
                // Voting completed by timeout
                const result = votingManager.completeVoting(sessionId);
                if (result) {
                    socket.to(gameId).emit('voting-completed', { sessionId, result });
                    socket.emit('voting-completed', { sessionId, result });
                }
                clearInterval(timer);
                this.sessionTimers.delete(sessionId);
            }
            else {
                // Broadcast time update
                socket.to(gameId).emit('voting-time-update', { sessionId, timeRemaining });
                socket.emit('voting-time-update', { sessionId, timeRemaining });
                // Broadcast stats update
                this.broadcastVotingStats(sessionId, gameId, socket);
            }
        }, 1000);
        this.sessionTimers.set(sessionId, timer);
    }
    /**
     * Clear timer for session
     */
    clearTimer(sessionId) {
        const timer = this.sessionTimers.get(sessionId);
        if (timer) {
            clearInterval(timer);
            this.sessionTimers.delete(sessionId);
        }
    }
    /**
     * Broadcast voting statistics
     */
    broadcastVotingStats(sessionId, gameId, socket) {
        const stats = votingManager.getVotingStats(sessionId);
        if (stats) {
            const participationRate = votingManager.getParticipationRate(sessionId);
            socket.to(gameId).emit('voting-stats', {
                sessionId,
                totalVotes: stats.totalVotes,
                votePercentages: stats.votePercentages,
                participationRate
            });
            socket.emit('voting-stats', {
                sessionId,
                totalVotes: stats.totalVotes,
                votePercentages: stats.votePercentages,
                participationRate
            });
        }
    }
    /**
     * Clean up game voting data
     */
    cleanupGame(gameId) {
        const sessionId = this.gameVotingSessions.get(gameId);
        if (sessionId) {
            this.clearTimer(sessionId);
            votingManager.removeSession(sessionId);
            this.gameVotingSessions.delete(gameId);
        }
    }
    /**
     * Create common voting scenarios
     */
    createCategoryVoting(gameId, totalPlayers, socket) {
        return this.startAutomaticVoting(gameId, 'category', 'Choose Next Category', 'Vote for the music category for the next round', [
            { label: 'Rock & Metal', value: 'rock', description: 'Hard rock, metal, punk', emoji: '🎸' },
            { label: 'Pop & Dance', value: 'pop', description: 'Pop hits, dance, electronic', emoji: '🎵' },
            { label: 'Hip Hop & R&B', value: 'hiphop', description: 'Rap, hip hop, R&B', emoji: '🎤' },
            { label: 'Classic & Jazz', value: 'classic', description: 'Classical, jazz, blues', emoji: '🎼' }
        ], totalPlayers, 30, socket);
    }
    createDecadeVoting(gameId, totalPlayers, socket) {
        return this.startAutomaticVoting(gameId, 'decade', 'Choose Time Period', 'Vote for the era of music for the next round', [
            { label: '80s Hits', value: '80s', description: 'Music from the 1980s', emoji: '📻' },
            { label: '90s Classics', value: '90s', description: 'Music from the 1990s', emoji: '💿' },
            { label: '2000s Pop', value: '2000s', description: 'Music from the 2000s', emoji: '💽' },
            { label: '2010s & Beyond', value: '2010s', description: 'Modern hits', emoji: '📱' }
        ], totalPlayers, 30, socket);
    }
    createGameModeVoting(gameId, totalPlayers, socket) {
        return this.startAutomaticVoting(gameId, 'game-mode', 'Choose Game Mode', 'Vote for the game mode for the next round', [
            { label: 'Classic Quiz', value: 'classic', description: 'Traditional music quiz', emoji: '🎯' },
            { label: 'Speed Round', value: 'speed', description: 'Fast-paced questions', emoji: '⚡' },
            { label: 'Challenge Mode', value: 'challenge', description: 'Harder questions', emoji: '🏆' },
            { label: 'Guess the Year', value: 'year', description: 'Identify release years', emoji: '📅' }
        ], totalPlayers, 30, socket);
    }
}
export const votingSocketHandler = new VotingSocketHandler();
