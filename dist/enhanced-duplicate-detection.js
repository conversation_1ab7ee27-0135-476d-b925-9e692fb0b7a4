"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.enhancedDeduplicateSongs = enhancedDeduplicateSongs;
exports.analyzeArtistVariations = analyzeArtistVariations;
exports.normalizeArtistName = normalizeArtistName;
exports.normalizeSongTitle = normalizeSongTitle;
// Helper function to normalize artist names for better duplicate detection
function normalizeArtistName(artist) {
    if (!artist)
        return 'unknown';
    var normalized = artist.toLowerCase().trim();
    // Remove common prefixes and suffixes
    normalized = normalized
        // Remove leading/trailing dashes and spaces
        .replace(/^[-\s]+|[-\s]+$/g, '')
        // Remove "various artists" variations
        .replace(/^(various|varios|different|diverse)\s*(artists?|artistas?)$/i, 'various')
        // Remove "feat.", "featuring", "ft." variations
        .replace(/\s+(feat\.?|featuring|ft\.?|with)\s+.*/i, '')
        // Remove bracketed content like "[Explicit]", "(Remastered)", etc.
        .replace(/\s*[\[\(].*?[\]\)]\s*/g, ' ')
        // Remove extra whitespace
        .replace(/\s+/g, ' ')
        .trim();
    // Handle empty result
    if (!normalized)
        return 'unknown';
    return normalized;
}
// Helper function to normalize song titles for better duplicate detection
function normalizeSongTitle(title) {
    if (!title)
        return 'unknown';
    var normalized = title.toLowerCase().trim();
    // Remove common variations
    normalized = normalized
        // Remove version indicators
        .replace(/\s*[-\(]\s*(remaster|remix|edit|version|mix|radio|clean|explicit|acoustic|live|demo|instrumental|karaoke).*?[\)\-]?\s*$/i, '')
        // Remove bracketed content
        .replace(/\s*[\[\(].*?[\]\)]\s*/g, ' ')
        // Remove "feat." and similar
        .replace(/\s+(feat\.?|featuring|ft\.?|with)\s+.*/i, '')
        // Remove extra whitespace
        .replace(/\s+/g, ' ')
        .trim();
    // Handle empty result
    if (!normalized)
        return 'unknown';
    return normalized;
}
// Enhanced duplicate detection with smart artist/title normalization
function enhancedDeduplicateSongs(songs) {
    var songMap = new Map();
    var debugInfo = [];
    songs.forEach(function (song) {
        // Create a unique key based on normalized title and artist
        var normalizedTitle = normalizeSongTitle(song.title);
        var normalizedArtist = normalizeArtistName(song.artist);
        var key = "".concat(normalizedTitle, "-").concat(normalizedArtist);
        if (songMap.has(key)) {
            // Merge with existing song - combine votes and keep best properties
            var existing = songMap.get(key);
            // Choose the best version based on priority:
            // 1. Most played/voted (weight: 10x)
            // 2. Longest duration (likely better quality)
            // 3. More complete metadata (album, year, genre)
            // 4. Has album art
            var currentScore = (song.votes || 0) * 10 +
                (song.duration || 0) / 60 +
                (song.album && song.album !== 'Unknown Album' ? 5 : 0) +
                (song.year ? 3 : 0) +
                (song.genre && song.genre !== 'Unknown' ? 2 : 0) +
                (song.albumArtUrl ? 2 : 0);
            var existingScore = (existing.votes || 0) * 10 +
                (existing.duration || 0) / 60 +
                (existing.album && existing.album !== 'Unknown Album' ? 5 : 0) +
                (existing.year ? 3 : 0) +
                (existing.genre && existing.genre !== 'Unknown' ? 2 : 0) +
                (existing.albumArtUrl ? 2 : 0);
            // Keep the better version as the primary, but merge data
            var betterSong = currentScore > existingScore ? song : existing;
            var otherSong = currentScore > existingScore ? existing : song;
            songMap.set(key, __assign(__assign({}, betterSong), { 
                // Combine votes from both versions
                votes: (existing.votes || 0) + (song.votes || 0), 
                // Use best available metadata
                duration: betterSong.duration || otherSong.duration, album: (betterSong.album && betterSong.album !== 'Unknown Album') ? betterSong.album : otherSong.album, year: betterSong.year || otherSong.year, genre: (betterSong.genre && betterSong.genre !== 'Unknown') ? betterSong.genre : otherSong.genre, albumArtUrl: betterSong.albumArtUrl || otherSong.albumArtUrl }));
            // Track for debugging
            if (process.env.NODE_ENV === 'development') {
                console.log("[Duplicate] Merged \"".concat(otherSong.title, "\" by \"").concat(otherSong.artist, "\" into \"").concat(betterSong.title, "\" by \"").concat(betterSong.artist, "\""));
            }
        }
        else {
            songMap.set(key, __assign({}, song));
        }
    });
    return Array.from(songMap.values());
}
// Test function to show what would be merged
function analyzeArtistVariations(songs) {
    var analysis = new Map();
    songs.forEach(function (song) {
        var normalizedTitle = normalizeSongTitle(song.title);
        var normalizedArtist = normalizeArtistName(song.artist);
        var key = "".concat(normalizedTitle, "-").concat(normalizedArtist);
        if (!analysis.has(key)) {
            analysis.set(key, {
                artists: new Set(),
                titles: new Set()
            });
        }
        var entry = analysis.get(key);
        entry.artists.add(song.artist);
        entry.titles.add(song.title);
    });
    return Array.from(analysis.entries()).map(function (_a) {
        var key = _a[0], data = _a[1];
        return ({
            normalizedKey: key,
            originalArtists: Array.from(data.artists),
            originalTitles: Array.from(data.titles),
            wouldMerge: data.artists.size > 1 || data.titles.size > 1
        });
    }).filter(function (item) { return item.wouldMerge; });
}
