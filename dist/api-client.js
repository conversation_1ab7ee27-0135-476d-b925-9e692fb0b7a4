"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.api = exports.apiClient = exports.APIError = void 0;
var auth_cookies_client_1 = require("./auth-cookies-client");
var network_fallback_1 = require("./utils/network-fallback");
var APIError = /** @class */ (function (_super) {
    __extends(APIError, _super);
    function APIError(status, message, data) {
        var _this = _super.call(this, message) || this;
        _this.status = status;
        _this.data = data;
        _this.name = 'APIError';
        return _this;
    }
    return APIError;
}(Error));
exports.APIError = APIError;
var APIClient = /** @class */ (function () {
    function APIClient() {
        this.csrfToken = null;
        this.csrfTokenPromise = null;
    }
    APIClient.getInstance = function () {
        if (!APIClient.instance) {
            APIClient.instance = new APIClient();
        }
        return APIClient.instance;
    };
    APIClient.prototype.getCSRFToken = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        // If we're already fetching a token, wait for that request
                        if (this.csrfTokenPromise) {
                            return [2 /*return*/, this.csrfTokenPromise];
                        }
                        // If we have a valid token, return it
                        if (this.csrfToken) {
                            return [2 /*return*/, this.csrfToken];
                        }
                        // Fetch a new token
                        this.csrfTokenPromise = this.fetchCSRFToken();
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, , 3, 4]);
                        _a = this;
                        return [4 /*yield*/, this.csrfTokenPromise];
                    case 2:
                        _a.csrfToken = _b.sent();
                        return [2 /*return*/, this.csrfToken];
                    case 3:
                        this.csrfTokenPromise = null;
                        return [7 /*endfinally*/];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    APIClient.prototype.fetchCSRFToken = function () {
        return __awaiter(this, void 0, void 0, function () {
            var response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, (0, network_fallback_1.fetchWithFallback)('/api/auth/csrf', {
                            credentials: 'include'
                        })];
                    case 1:
                        response = _a.sent();
                        if (!response.ok) {
                            throw new APIError(response.status, 'Failed to get CSRF token');
                        }
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        if (!data.success || !data.csrfToken) {
                            throw new APIError(500, 'Invalid CSRF token response');
                        }
                        return [2 /*return*/, data.csrfToken];
                }
            });
        });
    };
    APIClient.prototype.invalidateCSRFToken = function () {
        this.csrfToken = null;
        this.csrfTokenPromise = null;
    };
    APIClient.prototype.request = function (url_1) {
        return __awaiter(this, arguments, void 0, function (url, options) {
            var userInfo, needsCSRF, headers, csrfToken, error_1, response, data, newToken, retryResponse, data, networkStatus, errorMessage;
            var _a, _b;
            if (options === void 0) { options = {}; }
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        userInfo = (0, auth_cookies_client_1.getClientUserInfo)();
                        needsCSRF = ['POST', 'PUT', 'DELETE', 'PATCH'].includes(((_a = options.method) === null || _a === void 0 ? void 0 : _a.toUpperCase()) || 'GET');
                        headers = __assign({}, options.headers);
                        if (!needsCSRF) return [3 /*break*/, 4];
                        _c.label = 1;
                    case 1:
                        _c.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, this.getCSRFToken()];
                    case 2:
                        csrfToken = _c.sent();
                        headers['x-csrf-token'] = csrfToken;
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _c.sent();
                        console.error('Failed to get CSRF token:', error_1);
                        throw error_1;
                    case 4: return [4 /*yield*/, (0, network_fallback_1.fetchWithFallback)(url, __assign(__assign({}, options), { headers: headers, credentials: 'include' }))
                        // Handle CSRF token expiration
                    ];
                    case 5:
                        response = _c.sent();
                        if (!(response.status === 403)) return [3 /*break*/, 9];
                        return [4 /*yield*/, response.json().catch(function () { return ({}); })];
                    case 6:
                        data = _c.sent();
                        if (!((_b = data.error) === null || _b === void 0 ? void 0 : _b.includes('CSRF'))) return [3 /*break*/, 9];
                        // Invalidate token and retry once
                        this.invalidateCSRFToken();
                        if (!needsCSRF) return [3 /*break*/, 9];
                        return [4 /*yield*/, this.getCSRFToken()];
                    case 7:
                        newToken = _c.sent();
                        headers['x-csrf-token'] = newToken;
                        return [4 /*yield*/, (0, network_fallback_1.fetchWithFallback)(url, __assign(__assign({}, options), { headers: headers, credentials: 'include' }))];
                    case 8:
                        retryResponse = _c.sent();
                        if (!retryResponse.ok) {
                            throw new APIError(retryResponse.status, data.error || 'Request failed', data);
                        }
                        return [2 /*return*/, retryResponse.json()];
                    case 9:
                        if (!!response.ok) return [3 /*break*/, 11];
                        return [4 /*yield*/, response.json().catch(function () { return ({}); })];
                    case 10:
                        data = _c.sent();
                        networkStatus = (0, network_fallback_1.getNetworkStatus)();
                        errorMessage = data.error || data.message || 'Request failed';
                        if (networkStatus.fallbackMode) {
                            errorMessage = "Network issue: ".concat(errorMessage);
                        }
                        throw new APIError(response.status, errorMessage, __assign(__assign({}, data), { networkFallback: networkStatus.fallbackMode }));
                    case 11: return [2 /*return*/, response.json()];
                }
            });
        });
    };
    // Convenience methods
    APIClient.prototype.get = function (url, options) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.request(url, __assign(__assign({}, options), { method: 'GET' }))];
            });
        });
    };
    APIClient.prototype.post = function (url, body, options) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.request(url, __assign(__assign({}, options), { method: 'POST', headers: __assign({ 'Content-Type': 'application/json' }, options === null || options === void 0 ? void 0 : options.headers), body: body ? JSON.stringify(body) : undefined }))];
            });
        });
    };
    APIClient.prototype.put = function (url, body, options) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.request(url, __assign(__assign({}, options), { method: 'PUT', headers: __assign({ 'Content-Type': 'application/json' }, options === null || options === void 0 ? void 0 : options.headers), body: body ? JSON.stringify(body) : undefined }))];
            });
        });
    };
    APIClient.prototype.delete = function (url, options) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.request(url, __assign(__assign({}, options), { method: 'DELETE' }))];
            });
        });
    };
    APIClient.prototype.patch = function (url, body, options) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.request(url, __assign(__assign({}, options), { method: 'PATCH', headers: __assign({ 'Content-Type': 'application/json' }, options === null || options === void 0 ? void 0 : options.headers), body: body ? JSON.stringify(body) : undefined }))];
            });
        });
    };
    return APIClient;
}());
// Export singleton instance
exports.apiClient = APIClient.getInstance();
// Export convenience functions
exports.api = {
    get: function (url, options) {
        return exports.apiClient.get(url, options);
    },
    post: function (url, body, options) {
        return exports.apiClient.post(url, body, options);
    },
    put: function (url, body, options) {
        return exports.apiClient.put(url, body, options);
    },
    delete: function (url, options) {
        return exports.apiClient.delete(url, options);
    },
    patch: function (url, body, options) {
        return exports.apiClient.patch(url, body, options);
    },
    request: function (url, options) {
        return exports.apiClient.request(url, options);
    }
};
