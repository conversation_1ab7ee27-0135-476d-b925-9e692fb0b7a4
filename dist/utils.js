"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RandomUtils = void 0;
exports.cn = cn;
var clsx_1 = require("clsx");
var tailwind_merge_1 = require("tailwind-merge");
function cn() {
    var inputs = [];
    for (var _i = 0; _i < arguments.length; _i++) {
        inputs[_i] = arguments[_i];
    }
    return (0, tailwind_merge_1.twMerge)((0, clsx_1.clsx)(inputs));
}
/**
 * Improved randomness utilities to fix poor shuffling
 */
var RandomUtils = /** @class */ (function () {
    function RandomUtils() {
    }
    /**
     * Generate a cryptographically secure random number between 0 and 1
     * Falls back to Math.random() if crypto is not available
     */
    RandomUtils.secureRandom = function () {
        if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
            var array = new Uint32Array(1);
            window.crypto.getRandomValues(array);
            return array[0] / (0xFFFFFFFF + 1);
        }
        return Math.random();
    };
    /**
     * Fisher-Yates shuffle algorithm for proper randomization
     * Much better than Array.sort(() => Math.random() - 0.5)
     */
    RandomUtils.shuffle = function (array) {
        var _a;
        var shuffled = __spreadArray([], array, true);
        for (var i = shuffled.length - 1; i > 0; i--) {
            var j = Math.floor(this.secureRandom() * (i + 1));
            _a = [shuffled[j], shuffled[i]], shuffled[i] = _a[0], shuffled[j] = _a[1];
        }
        return shuffled;
    };
    /**
     * Get random elements from an array without replacement
     */
    RandomUtils.sample = function (array, count) {
        if (count >= array.length)
            return this.shuffle(array);
        var shuffled = this.shuffle(array);
        return shuffled.slice(0, count);
    };
    /**
     * Generate a random integer between min and max (inclusive)
     */
    RandomUtils.randomInt = function (min, max) {
        return Math.floor(this.secureRandom() * (max - min + 1)) + min;
    };
    /**
     * Generate a unique seed based on current time and random values
     * Useful for ensuring different randomness across quiz sessions
     */
    RandomUtils.generateSeed = function () {
        var timestamp = Date.now();
        var random1 = Math.floor(this.secureRandom() * 1000000);
        var random2 = Math.floor(this.secureRandom() * 1000000);
        return "".concat(timestamp, "-").concat(random1, "-").concat(random2);
    };
    return RandomUtils;
}());
exports.RandomUtils = RandomUtils;
