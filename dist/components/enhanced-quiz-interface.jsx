"use strict";
"use client";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedQuizInterface = EnhancedQuizInterface;
const react_1 = require("react");
const card_1 = require("@/components/ui/card");
const button_1 = require("@/components/ui/button");
const progress_1 = require("@/components/ui/progress");
const badge_1 = require("@/components/ui/badge");
const slider_1 = require("@/components/ui/slider");
const framer_motion_1 = require("framer-motion");
const lucide_react_1 = require("lucide-react");
const sonner_1 = require("sonner");
const user_context_1 = require("@/lib/user-context");
const GameTransition_1 = require("./GameTransition");
const useGameTransitions_1 = require("@/hooks/useGameTransitions");
const categoryIcons = {
    music: lucide_react_1.Music,
    general: lucide_react_1.Brain,
    science: lucide_react_1.Lightbulb,
    history: lucide_react_1.BookOpen,
    geography: lucide_react_1.Globe,
    sports: lucide_react_1.Target,
    entertainment: lucide_react_1.Star
};
const categoryColors = {
    music: 'from-purple-500 to-pink-500',
    general: 'from-blue-500 to-indigo-500',
    science: 'from-green-500 to-teal-500',
    history: 'from-amber-500 to-orange-500',
    geography: 'from-cyan-500 to-blue-500',
    sports: 'from-red-500 to-pink-500',
    entertainment: 'from-yellow-500 to-amber-500'
};
const difficultyLabels = {
    1: { label: 'Beginner', color: 'bg-green-500' },
    2: { label: 'Easy', color: 'bg-blue-500' },
    3: { label: 'Medium', color: 'bg-yellow-500' },
    4: { label: 'Hard', color: 'bg-orange-500' },
    5: { label: 'Expert', color: 'bg-red-500' }
};
function EnhancedQuizInterface({ config, questions, onComplete, onBackToMenu }) {
    const { user, updateXP, unlockAchievement } = (0, user_context_1.useUser)();
    const { isTransitioning, transitionConfig, hideTransition, showGameStart, showRoundStart, showRoundEnd, showGameEnd } = (0, useGameTransitions_1.useGameTransitions)();
    const [currentQuestionIndex, setCurrentQuestionIndex] = (0, react_1.useState)(0);
    const [selectedAnswer, setSelectedAnswer] = (0, react_1.useState)(null);
    const [sliderValue, setSliderValue] = (0, react_1.useState)([50]);
    const [timeLeft, setTimeLeft] = (0, react_1.useState)(config.timePerQuestion);
    const [score, setScore] = (0, react_1.useState)(0);
    const [streak, setStreak] = (0, react_1.useState)(0);
    const [maxStreak, setMaxStreak] = (0, react_1.useState)(0);
    const [answers, setAnswers] = (0, react_1.useState)([]);
    const [showFeedback, setShowFeedback] = (0, react_1.useState)(false);
    const [isAnswered, setIsAnswered] = (0, react_1.useState)(false);
    const [isComplete, setIsComplete] = (0, react_1.useState)(false);
    const [quizStartTime] = (0, react_1.useState)(Date.now());
    const [questionStartTime, setQuestionStartTime] = (0, react_1.useState)(Date.now());
    const [isPlaying, setIsPlaying] = (0, react_1.useState)(false);
    const [audioProgress, setAudioProgress] = (0, react_1.useState)(0);
    const [volume, setVolume] = (0, react_1.useState)(70);
    const audioRef = (0, react_1.useRef)(null);
    const [showHint, setShowHint] = (0, react_1.useState)(false);
    const [usedHints, setUsedHints] = (0, react_1.useState)(new Set());
    const [animatingScore, setAnimatingScore] = (0, react_1.useState)(false);
    const [gameStarted, setGameStarted] = (0, react_1.useState)(false);
    const currentQuestion = questions[currentQuestionIndex];
    const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
    (0, react_1.useEffect)(() => {
        if (!gameStarted && questions.length > 0) {
            showGameStart(`${config.category} Quiz - ${difficultyLabels[config.difficulty]?.label || 'Medium'}`, false, []);
            setGameStarted(true);
        }
    }, [gameStarted, questions.length, config, showGameStart]);
    (0, react_1.useEffect)(() => {
        if (!showFeedback && !isComplete && timeLeft > 0) {
            const timer = setTimeout(() => {
                setTimeLeft(prev => prev - 1);
            }, 1000);
            return () => clearTimeout(timer);
        }
        else if (timeLeft === 0 && !isAnswered) {
            handleTimeUp();
        }
    }, [timeLeft, showFeedback, isComplete, isAnswered]);
    (0, react_1.useEffect)(() => {
        setTimeLeft(config.timePerQuestion);
        setQuestionStartTime(Date.now());
        setIsAnswered(false);
        setSelectedAnswer(null);
        setSliderValue([50]);
        setShowHint(false);
    }, [currentQuestionIndex, config.timePerQuestion]);
    (0, react_1.useEffect)(() => {
        if (currentQuestion?.audioFile && audioRef.current) {
            audioRef.current.src = currentQuestion.audioFile;
            if (config.enableAudio) {
                audioRef.current.play();
                setIsPlaying(true);
            }
        }
    }, [currentQuestion, config.enableAudio]);
    const handleTimeUp = (0, react_1.useCallback)(() => {
        if (!isAnswered) {
            submitAnswer(null);
        }
    }, [isAnswered]);
    const submitAnswer = (0, react_1.useCallback)((answer) => {
        if (isAnswered)
            return;
        setIsAnswered(true);
        const timeSpent = config.timePerQuestion - timeLeft;
        let userAnswer = answer;
        if (currentQuestion.type === 'slider') {
            userAnswer = sliderValue[0];
        }
        else {
            userAnswer = selectedAnswer;
        }
        const correctAnswer = currentQuestion.correctAnswer;
        const isCorrect = userAnswer === correctAnswer;
        let pointsEarned = 0;
        if (isCorrect) {
            const timeBonus = Math.max(0, timeLeft / config.timePerQuestion);
            pointsEarned = Math.round(currentQuestion.points * (1 + timeBonus * 0.5));
            setStreak(prev => prev + 1);
            setMaxStreak(prev => Math.max(prev, streak + 1));
        }
        else {
            setStreak(0);
        }
        if (usedHints.has(currentQuestion.id)) {
            pointsEarned = Math.round(pointsEarned * 0.8);
        }
        setScore(prev => prev + pointsEarned);
        const answerRecord = {
            questionId: currentQuestion.id,
            question: currentQuestion.question,
            userAnswer: userAnswer || 'No answer',
            correctAnswer,
            isCorrect,
            timeSpent,
            pointsEarned
        };
        setAnswers(prev => [...prev, answerRecord]);
        if (pointsEarned > 0) {
            setAnimatingScore(true);
            setTimeout(() => setAnimatingScore(false), 1000);
        }
        setShowFeedback(true);
        setTimeout(() => {
            setShowFeedback(false);
            if (currentQuestionIndex < questions.length - 1) {
                const nextRound = currentQuestionIndex + 2;
                const totalRounds = questions.length;
                if (nextRound === Math.ceil(totalRounds / 2) && totalRounds > 4) {
                    showRoundStart(nextRound, totalRounds, `${config.category} Quiz`);
                }
                else if (nextRound === totalRounds) {
                    showRoundStart(nextRound, totalRounds, `${config.category} Quiz - Finale`);
                }
                else if (nextRound % 3 === 0 && totalRounds > 6) {
                    showRoundStart(nextRound, totalRounds, `${config.category} Quiz`);
                }
                setCurrentQuestionIndex(prev => prev + 1);
            }
            else {
                completeQuiz();
            }
        }, 3000);
    }, [
        isAnswered,
        timeLeft,
        selectedAnswer,
        sliderValue,
        currentQuestion,
        currentQuestionIndex,
        config,
        streak,
        usedHints,
        showRoundStart
    ]);
    const completeQuiz = (0, react_1.useCallback)(() => {
        setIsComplete(true);
        const totalTimeSpent = Date.now() - quizStartTime;
        const correctAnswers = answers.filter(a => a.isCorrect).length + (isAnswered && selectedAnswer === currentQuestion.correctAnswer ? 1 : 0);
        const accuracy = (correctAnswers / questions.length) * 100;
        const results = {
            score,
            totalQuestions: questions.length,
            correctAnswers,
            timeSpent: totalTimeSpent,
            maxStreak,
            accuracy,
            category: config.category,
            difficulty: config.difficulty,
            answers
        };
        updateXP(score);
        if (accuracy === 100) {
            unlockAchievement('perfect-score');
        }
        if (maxStreak >= 5) {
            unlockAchievement('streak-master');
        }
        showGameEnd([], `${config.category} Quiz - Abgeschlossen!`);
        setTimeout(() => onComplete(results), 4000);
    }, [score, answers, maxStreak, config, questions.length, onComplete, updateXP, unlockAchievement, showGameEnd]);
    const handleAnswerSelect = (answer) => {
        if (isAnswered)
            return;
        setSelectedAnswer(answer);
    };
    const handleSliderChange = (value) => {
        if (isAnswered)
            return;
        setSliderValue(value);
    };
    const toggleAudio = () => {
        if (!audioRef.current)
            return;
        if (isPlaying) {
            audioRef.current.pause();
        }
        else {
            audioRef.current.play();
        }
        setIsPlaying(!isPlaying);
    };
    const showHintForQuestion = () => {
        if (!config.enableHints || !currentQuestion.hints || usedHints.has(currentQuestion.id))
            return;
        setUsedHints(prev => new Set([...prev, currentQuestion.id]));
        setShowHint(true);
        sonner_1.toast.info(currentQuestion.hints[0]);
    };
    const CategoryIcon = categoryIcons[config.category] || lucide_react_1.Brain;
    const categoryGradient = categoryColors[config.category] || 'from-blue-500 to-indigo-500';
    const difficultyInfo = difficultyLabels[config.difficulty] || difficultyLabels[3];
    if (isComplete) {
        return (<framer_motion_1.motion.div initial={{ opacity: 0, scale: 0.9 }} animate={{ opacity: 1, scale: 1 }} className="min-h-screen bg-gradient-to-br from-purple-900/20 to-blue-900/20 flex items-center justify-center p-4">
        <card_1.Card className="w-full max-w-md text-center bg-gradient-to-br from-green-500/10 to-blue-500/10 border-green-500/20">
          <card_1.CardContent className="pt-6">
            <framer_motion_1.motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} transition={{ delay: 0.2, type: "spring" }}>
              <lucide_react_1.Award className="w-16 h-16 text-yellow-400 mx-auto mb-4"/>
              <h2 className="text-2xl font-bold mb-2">Quiz Complete!</h2>
              <p className="text-lg text-green-400 mb-4">Final Score: {score.toLocaleString()}</p>
              <div className="animate-spin rounded-full h-8 w-8 border-4 border-green-500/30 border-t-green-500 mx-auto"></div>
              <p className="text-sm text-gray-400 mt-2">Calculating results...</p>
            </framer_motion_1.motion.div>
          </card_1.CardContent>
        </card_1.Card>
      </framer_motion_1.motion.div>);
    }
    return (<div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 p-4">
      
      {isTransitioning && transitionConfig && (<GameTransition_1.GameTransition {...transitionConfig} onComplete={hideTransition}/>)}
      
      <framer_motion_1.motion.div initial={{ y: -20, opacity: 0 }} animate={{ y: 0, opacity: 1 }} className="max-w-4xl mx-auto mb-6">
        <div className="flex items-center justify-between mb-4">
          <button_1.Button variant="ghost" onClick={onBackToMenu} className="flex items-center gap-2">
            <lucide_react_1.ArrowLeft className="w-4 h-4"/>
            Back to Menu
          </button_1.Button>
          
          <div className="flex items-center gap-4">
            <badge_1.Badge className={`${difficultyInfo.color} text-white`}>
              {difficultyInfo.label}
            </badge_1.Badge>
            <div className="flex items-center gap-2">
              <CategoryIcon className="w-5 h-5"/>
              <span className="capitalize">{config.category}</span>
            </div>
          </div>
        </div>
        
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Question {currentQuestionIndex + 1} of {questions.length}</span>
            <span>Score: {score.toLocaleString()}</span>
          </div>
          <progress_1.Progress value={progress} className="h-2"/>
        </div>
      </framer_motion_1.motion.div>
      
      
      <div className="max-w-4xl mx-auto">
        <framer_motion_1.AnimatePresence mode="wait">
          <framer_motion_1.motion.div key={currentQuestionIndex} initial={{ x: 300, opacity: 0 }} animate={{ x: 0, opacity: 1 }} exit={{ x: -300, opacity: 0 }} transition={{ type: "spring", damping: 20 }}>
            <card_1.Card className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-lg border-white/20">
              <card_1.CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <framer_motion_1.motion.div animate={animatingScore ? { scale: [1, 1.2, 1] } : {}} className={`w-12 h-12 rounded-full bg-gradient-to-r ${categoryGradient} flex items-center justify-center`}>
                      <CategoryIcon className="w-6 h-6 text-white"/>
                    </framer_motion_1.motion.div>
                    <div>
                      <h3 className="text-lg font-semibold">Question {currentQuestionIndex + 1}</h3>
                      <p className="text-sm text-gray-400">Difficulty: {difficultyInfo.label}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    {streak > 0 && (<framer_motion_1.motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="flex items-center gap-1 text-orange-400">
                        <lucide_react_1.Zap className="w-4 h-4"/>
                        <span className="font-bold">{streak}</span>
                      </framer_motion_1.motion.div>)}
                    
                    <framer_motion_1.motion.div animate={{ scale: timeLeft <= 10 ? [1, 1.1, 1] : 1 }} transition={{ repeat: timeLeft <= 10 ? Infinity : 0, duration: 1 }} className={`flex items-center gap-2 ${timeLeft <= 10 ? 'text-red-400' : 'text-blue-400'}`}>
                      <lucide_react_1.Clock className="w-5 h-5"/>
                      <span className="font-mono text-lg">{timeLeft}s</span>
                    </framer_motion_1.motion.div>
                  </div>
                </div>
              </card_1.CardHeader>
              
              <card_1.CardContent className="space-y-6">
                
                <framer_motion_1.motion.div initial={{ y: 20, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ delay: 0.1 }}>
                  <h2 className="text-xl font-semibold mb-4">{currentQuestion.question}</h2>
                </framer_motion_1.motion.div>
                
                
                {currentQuestion.audioFile && (<framer_motion_1.motion.div initial={{ y: 20, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ delay: 0.2 }} className="bg-black/20 rounded-lg p-4">
                    <div className="flex items-center gap-4">
                      <button_1.Button onClick={toggleAudio} size="sm" className="w-12 h-12 rounded-full">
                        {isPlaying ? <lucide_react_1.Pause className="w-5 h-5"/> : <lucide_react_1.Play className="w-5 h-5"/>}
                      </button_1.Button>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <lucide_react_1.Headphones className="w-4 h-4"/>
                          <span className="text-sm">Listen to identify</span>
                        </div>
                        <progress_1.Progress value={audioProgress} className="h-2"/>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <lucide_react_1.Volume2 className="w-4 h-4"/>
                        <slider_1.Slider value={[volume]} onValueChange={(value) => setVolume(value[0])} max={100} step={1} className="w-20"/>
                      </div>
                    </div>
                    
                    <audio ref={audioRef} onTimeUpdate={(e) => {
                const progress = (e.currentTarget.currentTime / e.currentTarget.duration) * 100;
                setAudioProgress(progress || 0);
            }} onEnded={() => setIsPlaying(false)} volume={volume / 100}/>
                  </framer_motion_1.motion.div>)}
                
                
                {currentQuestion.imageUrl && (<framer_motion_1.motion.div initial={{ y: 20, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ delay: 0.3 }} className="flex justify-center">
                    <img src={currentQuestion.imageUrl} alt="Question image" className="max-w-md max-h-64 rounded-lg object-cover"/>
                  </framer_motion_1.motion.div>)}
                
                
                <framer_motion_1.motion.div initial={{ y: 20, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ delay: 0.4 }}>
                  {currentQuestion.type === 'multiple-choice' && currentQuestion.options && (<div className="grid gap-3">
                      {currentQuestion.options.map((option, index) => (<framer_motion_1.motion.button key={index} onClick={() => handleAnswerSelect(option)} disabled={isAnswered} whileHover={!isAnswered ? { scale: 1.02 } : {}} whileTap={!isAnswered ? { scale: 0.98 } : {}} className={`p-4 rounded-lg border-2 text-left transition-all ${selectedAnswer === option
                    ? 'border-blue-500 bg-blue-500/20'
                    : 'border-gray-600 hover:border-gray-500 bg-white/5'} ${isAnswered ? 'opacity-60' : ''}`}>
                          <div className="flex items-center gap-3">
                            <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${selectedAnswer === option ? 'border-blue-500 bg-blue-500' : 'border-gray-400'}`}>
                              {selectedAnswer === option && <lucide_react_1.Check className="w-3 h-3 text-white"/>}
                            </div>
                            <span>{option}</span>
                          </div>
                        </framer_motion_1.motion.button>))}
                    </div>)}
                  
                  {currentQuestion.type === 'true-false' && (<div className="grid grid-cols-2 gap-4">
                      {['True', 'False'].map((option) => (<framer_motion_1.motion.button key={option} onClick={() => handleAnswerSelect(option)} disabled={isAnswered} whileHover={!isAnswered ? { scale: 1.02 } : {}} whileTap={!isAnswered ? { scale: 0.98 } : {}} className={`p-6 rounded-lg border-2 transition-all ${selectedAnswer === option
                    ? 'border-blue-500 bg-blue-500/20'
                    : 'border-gray-600 hover:border-gray-500 bg-white/5'} ${isAnswered ? 'opacity-60' : ''}`}>
                          <div className="flex items-center justify-center gap-2">
                            {option === 'True' ? (<lucide_react_1.Check className="w-6 h-6 text-green-400"/>) : (<lucide_react_1.X className="w-6 h-6 text-red-400"/>)}
                            <span className="text-lg font-semibold">{option}</span>
                          </div>
                        </framer_motion_1.motion.button>))}
                    </div>)}
                  
                  {currentQuestion.type === 'slider' && (<div className="space-y-4">
                      <div className="text-center">
                        <span className="text-2xl font-bold text-blue-400">{sliderValue[0]}</span>
                      </div>
                      <slider_1.Slider value={sliderValue} onValueChange={handleSliderChange} disabled={isAnswered} max={100} step={1} className="w-full"/>
                      <div className="flex justify-between text-sm text-gray-400">
                        <span>0</span>
                        <span>50</span>
                        <span>100</span>
                      </div>
                    </div>)}
                </framer_motion_1.motion.div>
                
                
                <framer_motion_1.motion.div initial={{ y: 20, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ delay: 0.5 }} className="flex justify-between items-center">
                  <div className="flex gap-2">
                    {config.enableHints && currentQuestion.hints && !usedHints.has(currentQuestion.id) && (<button_1.Button onClick={showHintForQuestion} variant="outline" size="sm" disabled={isAnswered}>
                        <lucide_react_1.Lightbulb className="w-4 h-4 mr-2"/>
                        Hint
                      </button_1.Button>)}
                  </div>
                  
                  <button_1.Button onClick={() => submitAnswer(selectedAnswer)} disabled={!selectedAnswer && currentQuestion.type !== 'slider'} className="px-8">
                    Submit Answer
                  </button_1.Button>
                </framer_motion_1.motion.div>
              </card_1.CardContent>
            </card_1.Card>
          </framer_motion_1.motion.div>
        </framer_motion_1.AnimatePresence>
        
        
        <framer_motion_1.AnimatePresence>
          {showFeedback && (<framer_motion_1.motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
              <framer_motion_1.motion.div initial={{ scale: 0.8, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} exit={{ scale: 0.8, opacity: 0 }} className="bg-white dark:bg-slate-800 rounded-lg p-6 max-w-md mx-4">
                <div className="text-center">
                  <framer_motion_1.motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} transition={{ delay: 0.2, type: "spring" }}>
                    {selectedAnswer === currentQuestion.correctAnswer ? (<lucide_react_1.Check className="w-16 h-16 text-green-500 mx-auto mb-4"/>) : (<lucide_react_1.X className="w-16 h-16 text-red-500 mx-auto mb-4"/>)}
                  </framer_motion_1.motion.div>
                  
                  <h3 className="text-xl font-bold mb-2">
                    {selectedAnswer === currentQuestion.correctAnswer ? 'Correct!' : 'Incorrect'}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    The correct answer was: {currentQuestion.correctAnswer}
                  </p>
                  
                  {currentQuestion.explanation && (<p className="text-sm text-gray-500 dark:text-gray-400">
                      {currentQuestion.explanation}
                    </p>)}
                </div>
              </framer_motion_1.motion.div>
            </framer_motion_1.motion.div>)}
        </framer_motion_1.AnimatePresence>
      </div>
    </div>);
}
//# sourceMappingURL=enhanced-quiz-interface.jsx.map