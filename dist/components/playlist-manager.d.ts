export interface PlaylistTrack {
    id: string;
    title: string;
    artist: string;
    album: string;
    year: number;
    duration: number;
    genre: string;
    chartPosition?: number;
    addedAt: Date;
    addedFrom: 'quiz' | 'search' | 'manual';
    quizScore?: number;
    isFavorite: boolean;
}
export interface Playlist {
    id: string;
    name: string;
    description: string;
    coverImage?: string;
    tracks: PlaylistTrack[];
    isPublic: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    totalDuration: number;
    genre?: string;
    mood?: string;
}
interface PlaylistManagerProps {
    onClose: () => void;
    currentTrack?: PlaylistTrack;
    onPlayTrack?: (track: PlaylistTrack) => void;
}
export declare function PlaylistManager({ onClose, currentTrack, onPlayTrack }: PlaylistManagerProps): import("react").JSX.Element;
export {};
