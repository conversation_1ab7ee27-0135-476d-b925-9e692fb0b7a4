export type QuizType = 'music' | 'general' | 'mixed';
export type QuestionType = 'multiple-choice' | 'true-false' | 'slider' | 'audio-identification';
export interface QuizQuestion {
    id: string;
    type: QuestionType;
    category: string;
    difficulty: number;
    question: string;
    options?: string[];
    correctAnswer: string | number;
    explanation?: string;
    timeLimit: number;
    points: number;
    audioFile?: string;
    imageUrl?: string;
    hints?: string[];
}
export interface QuizConfig {
    type: QuizType;
    category: string;
    totalQuestions: number;
    timePerQuestion: number;
    difficulty: number;
    enableHints: boolean;
    enableAudio: boolean;
    enableImages: boolean;
}
export interface QuizResults {
    score: number;
    totalQuestions: number;
    correctAnswers: number;
    timeSpent: number;
    maxStreak: number;
    accuracy: number;
    category: string;
    difficulty: number;
    answers: QuizAnswer[];
}
export interface QuizAnswer {
    questionId: string;
    question: string;
    userAnswer: string | number;
    correctAnswer: string | number;
    isCorrect: boolean;
    timeSpent: number;
    pointsEarned: number;
}
interface EnhancedQuizInterfaceProps {
    config: QuizConfig;
    questions: QuizQuestion[];
    onComplete: (results: QuizResults) => void;
    onBackToMenu: () => void;
}
export declare function EnhancedQuizInterface({ config, questions, onComplete, onBackToMenu }: EnhancedQuizInterfaceProps): import("react").JSX.Element;
export {};
