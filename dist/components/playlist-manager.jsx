"use strict";
"use client";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlaylistManager = PlaylistManager;
const react_1 = require("react");
const card_1 = require("@/components/ui/card");
const button_1 = require("@/components/ui/button");
const input_1 = require("@/components/ui/input");
const label_1 = require("@/components/ui/label");
const textarea_1 = require("@/components/ui/textarea");
const badge_1 = require("@/components/ui/badge");
const dialog_1 = require("@/components/ui/dialog");
const dropdown_menu_1 = require("@/components/ui/dropdown-menu");
const sonner_1 = require("sonner");
const framer_motion_1 = require("framer-motion");
const lucide_react_1 = require("lucide-react");
const SAMPLE_PLAYLISTS = [
    {
        id: '1',
        name: 'Quiz Favorites',
        description: 'Songs I discovered and loved during quizzes',
        tracks: [
            {
                id: '1',
                title: 'Bohemian Rhapsody',
                artist: 'Queen',
                album: 'A Night at the Opera',
                year: 1975,
                duration: 355,
                genre: 'Rock',
                chartPosition: 1,
                addedAt: new Date('2024-06-25'),
                addedFrom: 'quiz',
                quizScore: 95,
                isFavorite: true
            },
            {
                id: '2',
                title: 'Billie Jean',
                artist: 'Michael Jackson',
                album: 'Thriller',
                year: 1983,
                duration: 294,
                genre: 'Pop',
                chartPosition: 1,
                addedAt: new Date('2024-06-24'),
                addedFrom: 'quiz',
                quizScore: 88,
                isFavorite: true
            }
        ],
        isPublic: false,
        createdAt: new Date('2024-06-20'),
        updatedAt: new Date('2024-06-25'),
        createdBy: 'current-user',
        totalDuration: 649,
        genre: 'Mixed',
        mood: 'Energetic'
    },
    {
        id: '2',
        name: 'Rock Classics',
        description: 'The greatest rock songs of all time',
        tracks: [
            {
                id: '3',
                title: 'Stairway to Heaven',
                artist: 'Led Zeppelin',
                album: 'Led Zeppelin IV',
                year: 1971,
                duration: 482,
                genre: 'Rock',
                chartPosition: 37,
                addedAt: new Date('2024-06-23'),
                addedFrom: 'manual',
                isFavorite: true
            }
        ],
        isPublic: true,
        createdAt: new Date('2024-06-15'),
        updatedAt: new Date('2024-06-23'),
        createdBy: 'current-user',
        totalDuration: 482,
        genre: 'Rock',
        mood: 'Classic'
    }
];
function PlaylistManager({ onClose, currentTrack, onPlayTrack }) {
    const [playlists, setPlaylists] = (0, react_1.useState)(SAMPLE_PLAYLISTS);
    const [selectedPlaylist, setSelectedPlaylist] = (0, react_1.useState)(null);
    const [isCreating, setIsCreating] = (0, react_1.useState)(false);
    const [searchQuery, setSearchQuery] = (0, react_1.useState)('');
    const [filterGenre, setFilterGenre] = (0, react_1.useState)('');
    const [sortBy, setSortBy] = (0, react_1.useState)('recent');
    const [playingTrack, setPlayingTrack] = (0, react_1.useState)(null);
    const [isPlaying, setIsPlaying] = (0, react_1.useState)(false);
    const [newPlaylist, setNewPlaylist] = (0, react_1.useState)({
        name: '',
        description: '',
        isPublic: false,
        genre: '',
        mood: ''
    });
    const formatDuration = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };
    const formatTotalDuration = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const mins = Math.floor((seconds % 3600) / 60);
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    };
    const createPlaylist = () => {
        if (!newPlaylist.name.trim()) {
            sonner_1.toast.error('Please enter a playlist name');
            return;
        }
        const playlist = {
            id: Date.now().toString(),
            name: newPlaylist.name,
            description: newPlaylist.description,
            tracks: currentTrack ? [currentTrack] : [],
            isPublic: newPlaylist.isPublic,
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: 'current-user',
            totalDuration: currentTrack?.duration || 0,
            genre: newPlaylist.genre,
            mood: newPlaylist.mood
        };
        setPlaylists(prev => [playlist, ...prev]);
        setNewPlaylist({ name: '', description: '', isPublic: false, genre: '', mood: '' });
        setIsCreating(false);
        sonner_1.toast.success(`Playlist "${playlist.name}" created!`);
    };
    const addToPlaylist = (playlistId, track) => {
        setPlaylists(prev => prev.map(playlist => {
            if (playlist.id === playlistId) {
                const trackExists = playlist.tracks.some(t => t.id === track.id);
                if (trackExists) {
                    sonner_1.toast.error('Track already in playlist');
                    return playlist;
                }
                const updatedPlaylist = {
                    ...playlist,
                    tracks: [...playlist.tracks, { ...track, addedAt: new Date() }],
                    totalDuration: playlist.totalDuration + track.duration,
                    updatedAt: new Date()
                };
                sonner_1.toast.success(`Added "${track.title}" to "${playlist.name}"`);
                return updatedPlaylist;
            }
            return playlist;
        }));
    };
    const removeFromPlaylist = (playlistId, trackId) => {
        setPlaylists(prev => prev.map(playlist => {
            if (playlist.id === playlistId) {
                const track = playlist.tracks.find(t => t.id === trackId);
                if (track) {
                    return {
                        ...playlist,
                        tracks: playlist.tracks.filter(t => t.id !== trackId),
                        totalDuration: playlist.totalDuration - track.duration,
                        updatedAt: new Date()
                    };
                }
            }
            return playlist;
        }));
        sonner_1.toast.success('Track removed from playlist');
    };
    const deletePlaylist = (playlistId) => {
        setPlaylists(prev => prev.filter(p => p.id !== playlistId));
        if (selectedPlaylist?.id === playlistId) {
            setSelectedPlaylist(null);
        }
        sonner_1.toast.success('Playlist deleted');
    };
    const toggleFavorite = (trackId) => {
        setPlaylists(prev => prev.map(playlist => ({
            ...playlist,
            tracks: playlist.tracks.map(track => track.id === trackId
                ? { ...track, isFavorite: !track.isFavorite }
                : track)
        })));
    };
    const playTrack = (track) => {
        setPlayingTrack(track);
        setIsPlaying(true);
        onPlayTrack?.(track);
        sonner_1.toast.success(`Playing "${track.title}"`);
    };
    const filteredPlaylists = playlists
        .filter(playlist => playlist.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        playlist.description.toLowerCase().includes(searchQuery.toLowerCase()))
        .filter(playlist => !filterGenre || playlist.genre === filterGenre)
        .sort((a, b) => {
        switch (sortBy) {
            case 'name':
                return a.name.localeCompare(b.name);
            case 'duration':
                return b.totalDuration - a.totalDuration;
            case 'tracks':
                return b.tracks.length - a.tracks.length;
            case 'recent':
            default:
                return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        }
    });
    const allGenres = [...new Set(playlists.map(p => p.genre).filter(Boolean))];
    if (selectedPlaylist) {
        return (<div className="max-w-6xl mx-auto space-y-6">
        
        <div className="flex items-center justify-between">
          <button_1.Button variant="ghost" onClick={() => setSelectedPlaylist(null)}>
            ← Back to Playlists
          </button_1.Button>
          <div className="flex items-center gap-2">
            <button_1.Button variant="outline" size="sm">
              <lucide_react_1.Share className="w-4 h-4 mr-2"/>
              Share
            </button_1.Button>
            <dropdown_menu_1.DropdownMenu>
              <dropdown_menu_1.DropdownMenuTrigger asChild>
                <button_1.Button variant="outline" size="sm">
                  <lucide_react_1.MoreVertical className="w-4 h-4"/>
                </button_1.Button>
              </dropdown_menu_1.DropdownMenuTrigger>
              <dropdown_menu_1.DropdownMenuContent align="end">
                <dropdown_menu_1.DropdownMenuItem>
                  <lucide_react_1.Edit className="w-4 h-4 mr-2"/>
                  Edit Playlist
                </dropdown_menu_1.DropdownMenuItem>
                <dropdown_menu_1.DropdownMenuItem>
                  <lucide_react_1.Download className="w-4 h-4 mr-2"/>
                  Export
                </dropdown_menu_1.DropdownMenuItem>
                <dropdown_menu_1.DropdownMenuItem className="text-red-600" onClick={() => deletePlaylist(selectedPlaylist.id)}>
                  <lucide_react_1.Trash2 className="w-4 h-4 mr-2"/>
                  Delete Playlist
                </dropdown_menu_1.DropdownMenuItem>
              </dropdown_menu_1.DropdownMenuContent>
            </dropdown_menu_1.DropdownMenu>
          </div>
        </div>

        
        <card_1.Card className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
          <card_1.CardContent className="p-8">
            <div className="flex items-end gap-6">
              <div className="w-48 h-48 bg-white/20 rounded-lg flex items-center justify-center">
                <lucide_react_1.Music className="w-20 h-20 text-white/80"/>
              </div>
              <div className="flex-1">
                <badge_1.Badge className="bg-white/20 text-white border-white/30 mb-2">
                  Playlist
                </badge_1.Badge>
                <h1 className="text-4xl font-bold mb-2">{selectedPlaylist.name}</h1>
                <p className="text-white/80 mb-4">{selectedPlaylist.description}</p>
                <div className="flex items-center gap-4 text-sm text-white/80">
                  <span>Created {selectedPlaylist.createdAt.toLocaleDateString()}</span>
                  <span>•</span>
                  <span>{selectedPlaylist.tracks.length} songs</span>
                  <span>•</span>
                  <span>{formatTotalDuration(selectedPlaylist.totalDuration)}</span>
                </div>
              </div>
            </div>
          </card_1.CardContent>
        </card_1.Card>

        
        <div className="flex items-center gap-4">
          <button_1.Button size="lg" className="bg-green-600 hover:bg-green-700 text-white rounded-full">
            <lucide_react_1.Play className="w-6 h-6"/>
          </button_1.Button>
          <button_1.Button variant="outline" size="lg" className="rounded-full">
            <lucide_react_1.Shuffle className="w-5 h-5"/>
          </button_1.Button>
          <button_1.Button variant="outline" size="lg" className="rounded-full">
            <lucide_react_1.Heart className="w-5 h-5"/>
          </button_1.Button>
          <button_1.Button variant="outline" size="lg" className="rounded-full">
            <lucide_react_1.MoreVertical className="w-5 h-5"/>
          </button_1.Button>
        </div>

        
        <card_1.Card>
          <card_1.CardContent className="p-0">
            <div className="divide-y">
              {selectedPlaylist.tracks.map((track, index) => (<framer_motion_1.motion.div key={track.id} className="flex items-center gap-4 p-4 hover:bg-gray-50 dark:hover:bg-gray-800 group" whileHover={{ backgroundColor: "rgba(0,0,0,0.02)" }}>
                  <div className="w-8 text-center text-gray-500">
                    {playingTrack?.id === track.id && isPlaying ? (<div className="w-4 h-4 mx-auto">
                        <div className="flex items-center gap-1">
                          <div className="w-1 h-3 bg-green-600 animate-pulse"></div>
                          <div className="w-1 h-2 bg-green-600 animate-pulse" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-1 h-4 bg-green-600 animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>) : (<span className="group-hover:hidden">{index + 1}</span>)}
                    <button_1.Button size="sm" variant="ghost" className="w-8 h-8 p-0 hidden group-hover:flex" onClick={() => playTrack(track)}>
                      <lucide_react_1.Play className="w-4 h-4"/>
                    </button_1.Button>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium truncate">{track.title}</h4>
                      {track.addedFrom === 'quiz' && (<badge_1.Badge variant="outline" className="text-xs">
                          Quiz {track.quizScore}%
                        </badge_1.Badge>)}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {track.artist} • {track.album}
                    </p>
                  </div>

                  <div className="hidden md:block text-sm text-gray-600 dark:text-gray-400">
                    {track.year}
                  </div>

                  <div className="hidden lg:block text-sm text-gray-600 dark:text-gray-400">
                    Added {track.addedAt.toLocaleDateString()}
                  </div>

                  <div className="flex items-center gap-2">
                    <button_1.Button size="sm" variant="ghost" onClick={() => toggleFavorite(track.id)} className={track.isFavorite ? 'text-red-500' : 'text-gray-400'}>
                      <lucide_react_1.Heart className={`w-4 h-4 ${track.isFavorite ? 'fill-current' : ''}`}/>
                    </button_1.Button>
                    <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[40px]">
                      {formatDuration(track.duration)}
                    </span>
                    <dropdown_menu_1.DropdownMenu>
                      <dropdown_menu_1.DropdownMenuTrigger asChild>
                        <button_1.Button size="sm" variant="ghost" className="opacity-0 group-hover:opacity-100">
                          <lucide_react_1.MoreVertical className="w-4 h-4"/>
                        </button_1.Button>
                      </dropdown_menu_1.DropdownMenuTrigger>
                      <dropdown_menu_1.DropdownMenuContent align="end">
                        <dropdown_menu_1.DropdownMenuItem onClick={() => playTrack(track)}>
                          <lucide_react_1.Play className="w-4 h-4 mr-2"/>
                          Play
                        </dropdown_menu_1.DropdownMenuItem>
                        <dropdown_menu_1.DropdownMenuItem>
                          <lucide_react_1.Plus className="w-4 h-4 mr-2"/>
                          Add to Playlist
                        </dropdown_menu_1.DropdownMenuItem>
                        <dropdown_menu_1.DropdownMenuItem className="text-red-600" onClick={() => removeFromPlaylist(selectedPlaylist.id, track.id)}>
                          <lucide_react_1.Trash2 className="w-4 h-4 mr-2"/>
                          Remove
                        </dropdown_menu_1.DropdownMenuItem>
                      </dropdown_menu_1.DropdownMenuContent>
                    </dropdown_menu_1.DropdownMenu>
                  </div>
                </framer_motion_1.motion.div>))}
            </div>
          </card_1.CardContent>
        </card_1.Card>
      </div>);
    }
    return (<div className="max-w-6xl mx-auto space-y-6">
      
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Your Playlists</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your music collections and favorites
          </p>
        </div>
        <div className="flex items-center gap-2">
          <dialog_1.Dialog open={isCreating} onOpenChange={setIsCreating}>
            <dialog_1.DialogTrigger asChild>
              <button_1.Button className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                <lucide_react_1.Plus className="w-4 h-4 mr-2"/>
                Create Playlist
              </button_1.Button>
            </dialog_1.DialogTrigger>
            <dialog_1.DialogContent>
              <dialog_1.DialogHeader>
                <dialog_1.DialogTitle>Create New Playlist</dialog_1.DialogTitle>
              </dialog_1.DialogHeader>
              <div className="space-y-4">
                <div>
                  <label_1.Label htmlFor="name">Playlist Name *</label_1.Label>
                  <input_1.Input id="name" value={newPlaylist.name} onChange={(e) => setNewPlaylist(prev => ({ ...prev, name: e.target.value }))} placeholder="My Awesome Playlist"/>
                </div>
                <div>
                  <label_1.Label htmlFor="description">Description</label_1.Label>
                  <textarea_1.Textarea id="description" value={newPlaylist.description} onChange={(e) => setNewPlaylist(prev => ({ ...prev, description: e.target.value }))} placeholder="Describe your playlist..." rows={3}/>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label_1.Label htmlFor="genre">Genre</label_1.Label>
                    <input_1.Input id="genre" value={newPlaylist.genre} onChange={(e) => setNewPlaylist(prev => ({ ...prev, genre: e.target.value }))} placeholder="Rock, Pop, etc."/>
                  </div>
                  <div>
                    <label_1.Label htmlFor="mood">Mood</label_1.Label>
                    <input_1.Input id="mood" value={newPlaylist.mood} onChange={(e) => setNewPlaylist(prev => ({ ...prev, mood: e.target.value }))} placeholder="Energetic, Chill, etc."/>
                  </div>
                </div>
                {currentTrack && (<div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      &quot;{currentTrack.title}&quot; by {currentTrack.artist} will be added to this playlist
                    </p>
                  </div>)}
                <div className="flex justify-end gap-2">
                  <button_1.Button variant="outline" onClick={() => setIsCreating(false)}>
                    Cancel
                  </button_1.Button>
                  <button_1.Button onClick={createPlaylist}>
                    Create Playlist
                  </button_1.Button>
                </div>
              </div>
            </dialog_1.DialogContent>
          </dialog_1.Dialog>
          <button_1.Button variant="outline" onClick={onClose}>
            <lucide_react_1.X className="w-4 h-4 mr-2"/>
            Close
          </button_1.Button>
        </div>
      </div>

      
      <card_1.Card>
        <card_1.CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <lucide_react_1.Search className="absolute left-3 top-3 w-4 h-4 text-gray-400"/>
                <input_1.Input placeholder="Search playlists..." value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} className="pl-10"/>
              </div>
            </div>
            <div className="flex gap-2">
              <select value={filterGenre} onChange={(e) => setFilterGenre(e.target.value)} className="px-3 py-2 border rounded-md bg-background">
                <option value="">All Genres</option>
                {allGenres.map(genre => (<option key={genre} value={genre}>{genre}</option>))}
              </select>
              <select value={sortBy} onChange={(e) => setSortBy(e.target.value)} className="px-3 py-2 border rounded-md bg-background">
                <option value="recent">Recently Updated</option>
                <option value="name">Name</option>
                <option value="duration">Duration</option>
                <option value="tracks">Track Count</option>
              </select>
            </div>
          </div>
        </card_1.CardContent>
      </card_1.Card>

      
      {currentTrack && (<card_1.Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950">
          <card_1.CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <lucide_react_1.Music className="w-6 h-6 text-white"/>
                </div>
                <div>
                  <h4 className="font-medium">&quot;{currentTrack.title}&quot; by {currentTrack.artist}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Add this track to one of your playlists
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                {playlists.slice(0, 3).map(playlist => (<button_1.Button key={playlist.id} variant="outline" size="sm" onClick={() => addToPlaylist(playlist.id, currentTrack)}>
                    <lucide_react_1.Plus className="w-4 h-4 mr-1"/>
                    {playlist.name}
                  </button_1.Button>))}
              </div>
            </div>
          </card_1.CardContent>
        </card_1.Card>)}

      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPlaylists.map((playlist) => (<framer_motion_1.motion.div key={playlist.id} whileHover={{ y: -4 }} transition={{ duration: 0.2 }}>
            <card_1.Card className="cursor-pointer hover:shadow-lg transition-all duration-200" onClick={() => setSelectedPlaylist(playlist)}>
              <card_1.CardContent className="p-0">
                <div className="aspect-square bg-gradient-to-br from-purple-500 to-pink-500 rounded-t-lg flex items-center justify-center relative overflow-hidden">
                  <lucide_react_1.Music className="w-16 h-16 text-white/80"/>
                  <div className="absolute top-4 right-4">
                    {playlist.isPublic ? (<badge_1.Badge className="bg-white/20 text-white border-white/30">
                        Public
                      </badge_1.Badge>) : (<badge_1.Badge variant="secondary" className="bg-black/20 text-white">
                        Private
                      </badge_1.Badge>)}
                  </div>
                  <button_1.Button size="sm" className="absolute bottom-4 right-4 bg-green-600 hover:bg-green-700 text-white rounded-full w-12 h-12 p-0 opacity-0 group-hover:opacity-100 transition-opacity" onClick={(e) => {
                e.stopPropagation();
                if (playlist.tracks.length > 0) {
                    playTrack(playlist.tracks[0]);
                }
            }}>
                    <lucide_react_1.Play className="w-5 h-5"/>
                  </button_1.Button>
                </div>
                <div className="p-4">
                  <h3 className="font-semibold mb-1 truncate">{playlist.name}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {playlist.description || 'No description'}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{playlist.tracks.length} tracks</span>
                    <span>{formatTotalDuration(playlist.totalDuration)}</span>
                  </div>
                  {playlist.genre && (<badge_1.Badge variant="outline" className="mt-2 text-xs">
                      {playlist.genre}
                    </badge_1.Badge>)}
                </div>
              </card_1.CardContent>
            </card_1.Card>
          </framer_motion_1.motion.div>))}
      </div>

      {filteredPlaylists.length === 0 && (<card_1.Card className="text-center py-12">
          <card_1.CardContent>
            <lucide_react_1.Music className="w-16 h-16 mx-auto text-gray-400 mb-4"/>
            <h3 className="text-xl font-semibold mb-2">No playlists found</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {searchQuery || filterGenre
                ? 'Try adjusting your search or filters'
                : 'Create your first playlist to get started'}
            </p>
            {!searchQuery && !filterGenre && (<button_1.Button onClick={() => setIsCreating(true)}>
                <lucide_react_1.Plus className="w-4 h-4 mr-2"/>
                Create Your First Playlist
              </button_1.Button>)}
          </card_1.CardContent>
        </card_1.Card>)}
    </div>);
}
//# sourceMappingURL=playlist-manager.jsx.map