interface OnboardingFlowProps {
    onComplete: (userProfile: UserProfile) => void;
    onSkip: () => Promise<void>;
}
export interface UserProfile {
    username: string;
    displayName: string;
    email: string;
    bio: string;
    avatar: string;
    favoriteGenres: string[];
    preferences: {
        theme: 'light' | 'dark' | 'system';
        difficulty: number;
        volume: number;
        autoPlay: boolean;
        showHints: boolean;
        soundEffects: boolean;
    };
    location?: string;
    isFirstTime: boolean;
}
export declare function OnboardingFlow({ onComplete, onSkip }: OnboardingFlowProps): import("react").JSX.Element;
export {};
