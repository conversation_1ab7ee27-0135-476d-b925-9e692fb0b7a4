export type TransitionType = 'game-start' | 'round-start' | 'round-end' | 'halfway-point' | 'final-countdown' | 'game-end' | 'leaderboard';
export interface PlayerScore {
    id: string;
    name: string;
    score: number;
    avatar?: string;
    isCurrentPlayer?: boolean;
}
export interface GameTransitionProps {
    type: TransitionType;
    duration?: number;
    currentRound?: number;
    totalRounds?: number;
    gameMode?: string;
    players?: PlayerScore[];
    isMultiplayer?: boolean;
    onComplete?: () => void;
    customMessage?: string;
    showLeaderboard?: boolean;
}
export declare function GameTransition({ type, duration, currentRound, totalRounds, gameMode, players, isMultiplayer, onComplete, customMessage, showLeaderboard }: GameTransitionProps): import("react").JSX.Element;
