"use strict";
"use client";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameTransition = GameTransition;
const react_1 = require("react");
const framer_motion_1 = require("framer-motion");
const badge_1 = require("@/components/ui/badge");
const progress_1 = require("@/components/ui/progress");
const lucide_react_1 = require("lucide-react");
const GERMAN_MESSAGES = {
    'game-start': [
        'Bereit machen für das Musik-Quiz!',
        'Das Spiel beginnt gleich...',
        'Macht euch bereit!',
        '<PERSON> geht\'s mit der Musik!',
        'Ohren gespitzt!'
    ],
    'round-start': [
        'Bereit machen für Runde {round}!',
        'Nächste Runde startet...',
        'Runde {round} von {total}',
        'Auf zur nächsten Herausforderung!',
        'Konzentration bitte!'
    ],
    'round-end': [
        'Runde {round} abgeschlossen!',
        'Gut gemacht!',
        '<PERSON><PERSON> zur nächsten Runde...',
        'Das war Runde {round}!',
        'Zwischenergebnis wird berechnet...'
    ],
    'halfway-point': [
        'Halbzeit erreicht!',
        'Die Hälfte ist geschafft!',
        'Zwischenstand wird angezeigt...',
        'Weiter so!',
        'Noch {remaining} Runden zu spielen!'
    ],
    'final-countdown': [
        'Letzte Runde!',
        'Das Finale beginnt!',
        'Alles oder nichts!',
        'Die Entscheidung fällt jetzt!',
        'Gebt alles!'
    ],
    'game-end': [
        'Spiel beendet!',
        'Das war\'s!',
        'Endergebnis wird berechnet...',
        'Danke fürs Mitspielen!',
        'Auswertung läuft...'
    ],
    'leaderboard': [
        'Aktuelle Rangliste',
        'Zwischenstand',
        'Wer liegt vorne?',
        'Die besten Spieler',
        'Rangliste wird aktualisiert...'
    ]
};
const getRandomMessage = (type, round, total) => {
    const messages = GERMAN_MESSAGES[type];
    const message = messages[Math.floor(Math.random() * messages.length)];
    return message
        .replace('{round}', round?.toString() || '')
        .replace('{total}', total?.toString() || '')
        .replace('{remaining}', total && round ? (total - round).toString() : '');
};
const getTransitionIcon = (type, gameMode) => {
    switch (type) {
        case 'game-start':
            return gameMode?.includes('audio') ? <lucide_react_1.Headphones className="w-16 h-16"/> : <lucide_react_1.Music className="w-16 h-16"/>;
        case 'round-start':
            return <lucide_react_1.Zap className="w-16 h-16"/>;
        case 'round-end':
            return <lucide_react_1.Target className="w-16 h-16"/>;
        case 'halfway-point':
            return <lucide_react_1.Clock className="w-16 h-16"/>;
        case 'final-countdown':
            return <lucide_react_1.Crown className="w-16 h-16"/>;
        case 'game-end':
            return <lucide_react_1.Award className="w-16 h-16"/>;
        case 'leaderboard':
            return <lucide_react_1.TrendingUp className="w-16 h-16"/>;
        default:
            return <lucide_react_1.Star className="w-16 h-16"/>;
    }
};
const getBackgroundGradient = (type) => {
    switch (type) {
        case 'game-start':
            return 'from-blue-600 via-purple-600 to-indigo-600';
        case 'round-start':
            return 'from-green-500 via-emerald-500 to-teal-500';
        case 'round-end':
            return 'from-orange-500 via-amber-500 to-yellow-500';
        case 'halfway-point':
            return 'from-purple-500 via-pink-500 to-rose-500';
        case 'final-countdown':
            return 'from-red-600 via-orange-600 to-yellow-600';
        case 'game-end':
            return 'from-indigo-600 via-purple-600 to-pink-600';
        case 'leaderboard':
            return 'from-cyan-500 via-blue-500 to-indigo-500';
        default:
            return 'from-gray-600 via-gray-700 to-gray-800';
    }
};
function GameTransition({ type, duration = 3000, currentRound, totalRounds, gameMode, players = [], isMultiplayer = false, onComplete, customMessage, showLeaderboard = false }) {
    const [progress, setProgress] = (0, react_1.useState)(0);
    const [currentMessage, setCurrentMessage] = (0, react_1.useState)(customMessage || getRandomMessage(type, currentRound, totalRounds));
    (0, react_1.useEffect)(() => {
        const interval = setInterval(() => {
            setProgress(prev => {
                const newProgress = prev + (100 / (duration / 100));
                if (newProgress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => onComplete?.(), 200);
                    return 100;
                }
                return newProgress;
            });
        }, 100);
        if (duration > 2000 && !customMessage) {
            setTimeout(() => {
                setCurrentMessage(getRandomMessage(type, currentRound, totalRounds));
            }, duration / 2);
        }
        return () => clearInterval(interval);
    }, [duration, type, currentRound, totalRounds, onComplete, customMessage]);
    const sortedPlayers = [...players].sort((a, b) => b.score - a.score);
    const backgroundGradient = getBackgroundGradient(type);
    const icon = getTransitionIcon(type, gameMode);
    return (<framer_motion_1.AnimatePresence>
      <framer_motion_1.motion.div className={`fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br ${backgroundGradient}`} initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} transition={{ duration: 0.5 }}>
        
        <div className="absolute inset-0 opacity-10">
          <div className="h-full w-full bg-[radial-gradient(circle_at_1px_1px,white_1px,transparent_0)] bg-[length:50px_50px]"/>
        </div>

        <framer_motion_1.motion.div className="relative z-10 max-w-2xl mx-auto px-6 text-center" initial={{ scale: 0.8, y: 50 }} animate={{ scale: 1, y: 0 }} transition={{ duration: 0.6, type: "spring", stiffness: 100 }}>
          
          <framer_motion_1.motion.div className="text-white mb-8 flex justify-center" animate={{
            scale: [1, 1.1, 1],
            rotate: type === 'game-start' ? [0, 10, -10, 0] : [0]
        }} transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
        }}>
            {icon}
          </framer_motion_1.motion.div>

          
          <framer_motion_1.motion.h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2, duration: 0.6 }} key={currentMessage}>
            {currentMessage}
          </framer_motion_1.motion.h1>

          
          {currentRound && totalRounds && (<framer_motion_1.motion.div className="mb-8" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.4, duration: 0.6 }}>
              <badge_1.Badge className="bg-white/20 text-white border-white/30 text-lg px-4 py-2">
                Runde {currentRound} von {totalRounds}
              </badge_1.Badge>
            </framer_motion_1.motion.div>)}

          
          {gameMode && (<framer_motion_1.motion.div className="mb-8" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.3, duration: 0.6 }}>
              <badge_1.Badge className="bg-white/10 text-white border-white/20 text-sm px-3 py-1">
                {gameMode}
              </badge_1.Badge>
            </framer_motion_1.motion.div>)}

          
          {showLeaderboard && players.length > 0 && (<framer_motion_1.motion.div className="mb-8 bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5, duration: 0.6 }}>
              <h3 className="text-white text-xl font-semibold mb-4 flex items-center justify-center gap-2">
                <lucide_react_1.Crown className="w-5 h-5"/>
                Aktuelle Rangliste
              </h3>
              <div className="space-y-2">
                {sortedPlayers.slice(0, 3).map((player, index) => (<framer_motion_1.motion.div key={player.id} className={`flex items-center justify-between p-3 rounded-lg ${player.isCurrentPlayer
                    ? 'bg-yellow-500/20 border border-yellow-400/50'
                    : 'bg-white/5'}`} initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} transition={{ delay: 0.6 + index * 0.1, duration: 0.4 }}>
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${index === 0 ? 'bg-yellow-500 text-yellow-900' :
                    index === 1 ? 'bg-gray-300 text-gray-700' :
                        'bg-orange-400 text-orange-900'}`}>
                        {index + 1}
                      </div>
                      <span className="text-white font-medium">
                        {player.avatar} {player.name}
                      </span>
                    </div>
                    <span className="text-white font-bold">
                      {player.score}
                    </span>
                  </framer_motion_1.motion.div>))}
              </div>
            </framer_motion_1.motion.div>)}

          
          {isMultiplayer && players.length > 0 && !showLeaderboard && (<framer_motion_1.motion.div className="mb-8 flex items-center justify-center gap-2 text-white/80" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.6, duration: 0.6 }}>
              <lucide_react_1.Users className="w-5 h-5"/>
              <span>{players.length} Spieler</span>
            </framer_motion_1.motion.div>)}

          
          <framer_motion_1.motion.div className="w-full max-w-md mx-auto" initial={{ opacity: 0, scaleX: 0 }} animate={{ opacity: 1, scaleX: 1 }} transition={{ delay: 0.7, duration: 0.6 }}>
            <progress_1.Progress value={progress} className="h-2 bg-white/20"/>
            <framer_motion_1.motion.p className="text-white/60 text-sm mt-2" animate={{ opacity: [0.5, 1, 0.5] }} transition={{ duration: 1.5, repeat: Infinity }}>
              {progress < 100 ? 'Bereitet vor...' : 'Fertig!'}
            </framer_motion_1.motion.p>
          </framer_motion_1.motion.div>
        </framer_motion_1.motion.div>

        
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(20)].map((_, i) => (<framer_motion_1.motion.div key={i} className="absolute w-2 h-2 bg-white/20 rounded-full" style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
            }} animate={{
                y: [0, -100, 0],
                opacity: [0, 1, 0],
                scale: [0.5, 1, 0.5],
            }} transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: "easeInOut"
            }}/>))}
        </div>
      </framer_motion_1.motion.div>
    </framer_motion_1.AnimatePresence>);
}
//# sourceMappingURL=GameTransition.jsx.map