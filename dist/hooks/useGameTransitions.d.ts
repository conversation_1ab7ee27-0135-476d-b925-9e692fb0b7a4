import type { TransitionType, PlayerScore } from '@/components/GameTransition';
export interface GameTransitionOptions {
    type: TransitionType;
    duration?: number;
    currentRound?: number;
    totalRounds?: number;
    gameMode?: string;
    players?: PlayerScore[];
    isMultiplayer?: boolean;
    customMessage?: string;
    showLeaderboard?: boolean;
}
export declare function useGameTransitions(): {
    isTransitioning: boolean;
    transitionConfig: GameTransitionOptions | null;
    showTransition: (options: GameTransitionOptions) => void;
    hideTransition: () => void;
    showGameStart: (gameMode: string, isMultiplayer?: boolean, players?: PlayerScore[]) => void;
    showRoundStart: (round: number, total: number, gameMode?: string) => void;
    showRoundEnd: (round: number, total: number, players?: PlayerScore[]) => void;
    showHalfwayPoint: (round: number, total: number, players?: PlayerScore[]) => void;
    showFinalCountdown: (gameMode?: string, players?: PlayerScore[]) => void;
    showGameEnd: (players?: PlayerScore[], gameMode?: string) => void;
    showLeaderboard: (players: PlayerScore[], duration?: number) => void;
};
