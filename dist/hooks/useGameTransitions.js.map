{"version": 3, "file": "useGameTransitions.js", "sourceRoot": "", "sources": ["../../hooks/useGameTransitions.ts"], "names": [], "mappings": ";;AAeA,gDAoGC;AAnHD,iCAA6C;AAe7C,SAAgB,kBAAkB;IAChC,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAA;IAC7D,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,IAAA,gBAAQ,EAA+B,IAAI,CAAC,CAAA;IAE5F,MAAM,cAAc,GAAG,IAAA,mBAAW,EAAC,CAAC,OAA8B,EAAE,EAAE;QACpE,mBAAmB,CAAC,OAAO,CAAC,CAAA;QAC5B,kBAAkB,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC,EAAE,EAAE,CAAC,CAAA;IAEN,MAAM,cAAc,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QACtC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QACzB,mBAAmB,CAAC,IAAI,CAAC,CAAA;IAC3B,CAAC,EAAE,EAAE,CAAC,CAAA;IAGN,MAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,CAAC,QAAgB,EAAE,aAAa,GAAG,KAAK,EAAE,UAAyB,EAAE,EAAE,EAAE;QACzG,cAAc,CAAC;YACb,IAAI,EAAE,YAAY;YAClB,QAAQ,EAAE,IAAI;YACd,QAAQ;YACR,aAAa;YACb,OAAO;SACR,CAAC,CAAA;IACJ,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAA;IAEpB,MAAM,cAAc,GAAG,IAAA,mBAAW,EAAC,CAAC,KAAa,EAAE,KAAa,EAAE,QAAiB,EAAE,EAAE;QACrF,cAAc,CAAC;YACb,IAAI,EAAE,aAAa;YACnB,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,KAAK;YACnB,WAAW,EAAE,KAAK;YAClB,QAAQ;SACT,CAAC,CAAA;IACJ,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAA;IAEpB,MAAM,YAAY,GAAG,IAAA,mBAAW,EAAC,CAAC,KAAa,EAAE,KAAa,EAAE,UAAyB,EAAE,EAAE,EAAE;QAC7F,cAAc,CAAC;YACb,IAAI,EAAE,WAAW;YACjB,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,KAAK;YACnB,WAAW,EAAE,KAAK;YAClB,OAAO;YACP,eAAe,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;SACpC,CAAC,CAAA;IACJ,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAA;IAEpB,MAAM,gBAAgB,GAAG,IAAA,mBAAW,EAAC,CAAC,KAAa,EAAE,KAAa,EAAE,UAAyB,EAAE,EAAE,EAAE;QACjG,cAAc,CAAC;YACb,IAAI,EAAE,eAAe;YACrB,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,KAAK;YACnB,WAAW,EAAE,KAAK;YAClB,OAAO;YACP,eAAe,EAAE,IAAI;SACtB,CAAC,CAAA;IACJ,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAA;IAEpB,MAAM,kBAAkB,GAAG,IAAA,mBAAW,EAAC,CAAC,QAAiB,EAAE,UAAyB,EAAE,EAAE,EAAE;QACxF,cAAc,CAAC;YACb,IAAI,EAAE,iBAAiB;YACvB,QAAQ,EAAE,IAAI;YACd,QAAQ;YACR,OAAO;YACP,eAAe,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;SACpC,CAAC,CAAA;IACJ,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAA;IAEpB,MAAM,WAAW,GAAG,IAAA,mBAAW,EAAC,CAAC,UAAyB,EAAE,EAAE,QAAiB,EAAE,EAAE;QACjF,cAAc,CAAC;YACb,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE,IAAI;YACd,QAAQ;YACR,OAAO;YACP,eAAe,EAAE,IAAI;SACtB,CAAC,CAAA;IACJ,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAA;IAEpB,MAAM,eAAe,GAAG,IAAA,mBAAW,EAAC,CAAC,OAAsB,EAAE,QAAQ,GAAG,IAAI,EAAE,EAAE;QAC9E,cAAc,CAAC;YACb,IAAI,EAAE,aAAa;YACnB,QAAQ;YACR,OAAO;YACP,eAAe,EAAE,IAAI;SACtB,CAAC,CAAA;IACJ,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAA;IAEpB,OAAO;QACL,eAAe;QACf,gBAAgB;QAChB,cAAc;QACd,cAAc;QAEd,aAAa;QACb,cAAc;QACd,YAAY;QACZ,gBAAgB;QAChB,kBAAkB;QAClB,WAAW;QACX,eAAe;KAChB,CAAA;AACH,CAAC"}