"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useGameTransitions = useGameTransitions;
const react_1 = require("react");
function useGameTransitions() {
    const [isTransitioning, setIsTransitioning] = (0, react_1.useState)(false);
    const [transitionConfig, setTransitionConfig] = (0, react_1.useState)(null);
    const showTransition = (0, react_1.useCallback)((options) => {
        setTransitionConfig(options);
        setIsTransitioning(true);
    }, []);
    const hideTransition = (0, react_1.useCallback)(() => {
        setIsTransitioning(false);
        setTransitionConfig(null);
    }, []);
    const showGameStart = (0, react_1.useCallback)((gameMode, isMultiplayer = false, players = []) => {
        showTransition({
            type: 'game-start',
            duration: 3000,
            gameMode,
            isMultiplayer,
            players
        });
    }, [showTransition]);
    const showRoundStart = (0, react_1.useCallback)((round, total, gameMode) => {
        showTransition({
            type: 'round-start',
            duration: 2500,
            currentRound: round,
            totalRounds: total,
            gameMode
        });
    }, [showTransition]);
    const showRoundEnd = (0, react_1.useCallback)((round, total, players = []) => {
        showTransition({
            type: 'round-end',
            duration: 2000,
            currentRound: round,
            totalRounds: total,
            players,
            showLeaderboard: players.length > 1
        });
    }, [showTransition]);
    const showHalfwayPoint = (0, react_1.useCallback)((round, total, players = []) => {
        showTransition({
            type: 'halfway-point',
            duration: 3000,
            currentRound: round,
            totalRounds: total,
            players,
            showLeaderboard: true
        });
    }, [showTransition]);
    const showFinalCountdown = (0, react_1.useCallback)((gameMode, players = []) => {
        showTransition({
            type: 'final-countdown',
            duration: 2500,
            gameMode,
            players,
            showLeaderboard: players.length > 1
        });
    }, [showTransition]);
    const showGameEnd = (0, react_1.useCallback)((players = [], gameMode) => {
        showTransition({
            type: 'game-end',
            duration: 3500,
            gameMode,
            players,
            showLeaderboard: true
        });
    }, [showTransition]);
    const showLeaderboard = (0, react_1.useCallback)((players, duration = 4000) => {
        showTransition({
            type: 'leaderboard',
            duration,
            players,
            showLeaderboard: true
        });
    }, [showTransition]);
    return {
        isTransitioning,
        transitionConfig,
        showTransition,
        hideTransition,
        showGameStart,
        showRoundStart,
        showRoundEnd,
        showHalfwayPoint,
        showFinalCountdown,
        showGameEnd,
        showLeaderboard
    };
}
//# sourceMappingURL=useGameTransitions.js.map