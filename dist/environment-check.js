"use strict";
/**
 * Environment validation utilities to check for JavaScript runtime issues
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateEnvironment = validateEnvironment;
exports.logEnvironmentCheck = logEnvironmentCheck;
exports.setupErrorTracking = setupErrorTracking;
exports.checkReactCompatibility = checkReactCompatibility;
exports.runAllChecks = runAllChecks;
function validateEnvironment() {
    var _a;
    var errors = [];
    var warnings = [];
    var timestamp = new Date().toISOString();
    // Check Reflect API availability
    var reflectSupport = true;
    if (typeof Reflect === 'undefined') {
        errors.push('Reflect API not available');
        reflectSupport = false;
    }
    else {
        // Check specific Reflect methods
        var requiredMethods = ['apply', 'construct', 'defineProperty', 'get', 'has', 'set'];
        for (var _i = 0, requiredMethods_1 = requiredMethods; _i < requiredMethods_1.length; _i++) {
            var method = requiredMethods_1[_i];
            if (typeof Reflect[method] !== 'function') {
                errors.push("Reflect.".concat(method, " not available"));
                reflectSupport = false;
            }
        }
    }
    // Check for polyfill conflicts
    if (typeof window !== 'undefined') {
        // Check if multiple polyfills might be loaded
        var globalObj = window;
        if (globalObj._babelPolyfill) {
            warnings.push('Babel polyfill detected - may cause conflicts');
        }
        if (globalObj.core) {
            warnings.push('Core-js polyfill detected - may cause conflicts');
        }
    }
    // Gather environment info
    var info = {
        userAgent: typeof window !== 'undefined' ? (_a = window.navigator) === null || _a === void 0 ? void 0 : _a.userAgent : undefined,
        nodeVersion: typeof process !== 'undefined' ? process.version : undefined,
        reflectSupport: reflectSupport,
        timestamp: timestamp
    };
    var isValid = errors.length === 0;
    return {
        isValid: isValid,
        errors: errors,
        warnings: warnings,
        info: info
    };
}
function logEnvironmentCheck() {
    var result = validateEnvironment();
    if (result.isValid) {
        console.log('✅ Environment validation passed', result.info);
        if (result.warnings.length > 0) {
            console.warn('⚠️ Environment warnings:', result.warnings);
        }
    }
    else {
        console.error('❌ Environment validation failed:', {
            errors: result.errors,
            warnings: result.warnings,
            info: result.info
        });
    }
    return result;
}
function setupErrorTracking() {
    if (typeof window === 'undefined')
        return;
    // Track JavaScript runtime errors
    window.addEventListener('error', function (event) {
        var _a, _b, _c;
        var errorMessage = ((_a = event.error) === null || _a === void 0 ? void 0 : _a.message) || event.message || 'Unknown error';
        if (errorMessage.includes('ReflectApply') || errorMessage.includes('Reflect')) {
            console.error('🔍 Reflect API Error Detected:', {
                message: errorMessage,
                stack: (_b = event.error) === null || _b === void 0 ? void 0 : _b.stack,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                timestamp: new Date().toISOString(),
                userAgent: (_c = window.navigator) === null || _c === void 0 ? void 0 : _c.userAgent
            });
            // Re-validate environment when Reflect errors occur
            var validation = validateEnvironment();
            console.error('🔍 Environment state during error:', validation);
        }
    });
    // Track unhandled promise rejections
    window.addEventListener('unhandledrejection', function (event) {
        var reason = event.reason;
        var reasonMessage = (reason === null || reason === void 0 ? void 0 : reason.message) || (reason === null || reason === void 0 ? void 0 : reason.toString()) || 'Unknown rejection';
        if (reasonMessage.includes('ReflectApply') || reasonMessage.includes('Reflect')) {
            console.error('🔍 Reflect API Promise Rejection:', {
                reason: reasonMessage,
                stack: reason === null || reason === void 0 ? void 0 : reason.stack,
                timestamp: new Date().toISOString()
            });
        }
    });
    console.log('🔍 Error tracking initialized for Reflect API issues');
}
function checkReactCompatibility() {
    if (typeof window === 'undefined')
        return true;
    try {
        // Check if React is properly loaded
        var React_1 = window.React;
        if (!React_1) {
            console.warn('⚠️ React not found on window object');
            return false;
        }
        // Check React version
        var version = React_1.version;
        if (version) {
            console.log("\uD83D\uDCE6 React version: ".concat(version));
            // React 19+ has different behavior
            if (version.startsWith('19.')) {
                console.log('ℹ️ React 19 detected - using latest features');
            }
        }
        return true;
    }
    catch (error) {
        console.error('❌ React compatibility check failed:', error);
        return false;
    }
}
// Development helper to run all checks
function runAllChecks() {
    console.group('🔍 Environment Diagnostics');
    var envResult = logEnvironmentCheck();
    var reactOk = checkReactCompatibility();
    console.log('📊 Summary:', {
        environmentValid: envResult.isValid,
        reactCompatible: reactOk,
        totalErrors: envResult.errors.length,
        totalWarnings: envResult.warnings.length
    });
    console.groupEnd();
}
