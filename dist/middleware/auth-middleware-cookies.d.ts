import { NextRequest, NextResponse } from 'next/server';
import type { UserRole } from '@/lib/user-context';
export interface AuthenticatedRequest extends NextRequest {
    user?: {
        id: string;
        email: string;
        username: string;
        role: UserRole;
    };
}
interface AuthOptions {
    requiredRole?: UserRole | UserRole[];
}
export declare function withAuth(handler: (req: AuthenticatedRequest) => Promise<NextResponse>, options?: AuthOptions): (req: NextRequest) => Promise<NextResponse<unknown>>;
export {};
