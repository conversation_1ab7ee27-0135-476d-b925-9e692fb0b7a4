"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.withAuth = withAuth;
const server_1 = require("next/server");
const auth_cookies_1 = require("@/lib/auth-cookies");
function withAuth(handler, options = {}) {
    return async (req) => {
        try {
            const user = await auth_cookies_1.CookieAuth.getAuthFromCookie();
            if (!user) {
                return server_1.NextResponse.json({ success: false, message: 'Authentication required' }, { status: 401 });
            }
            if (options.requiredRole) {
                const requiredRoles = Array.isArray(options.requiredRole)
                    ? options.requiredRole
                    : [options.requiredRole];
                if (!requiredRoles.includes(user.role)) {
                    return server_1.NextResponse.json({ success: false, message: 'Insufficient permissions' }, { status: 403 });
                }
            }
            const authenticatedReq = req;
            authenticatedReq.user = {
                id: user.id,
                email: user.email,
                username: user.username,
                role: user.role
            };
            await auth_cookies_1.CookieAuth.refreshToken();
            return handler(authenticatedReq);
        }
        catch (error) {
            console.error('Auth middleware error:', error);
            return server_1.NextResponse.json({ success: false, message: 'Authentication failed' }, { status: 401 });
        }
    };
}
//# sourceMappingURL=auth-middleware-cookies.js.map