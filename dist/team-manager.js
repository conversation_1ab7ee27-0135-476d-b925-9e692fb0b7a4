"use strict";
/**
 * Team Management System
 * Handles team creation, player assignment, scoring, and game logic
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.teamManager = exports.TeamManager = void 0;
class TeamManager {
    constructor() {
        this.teams = new Map();
        this.teamColors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
            '#FECA57', '#FF9FF3', '#54A0FF', '#5F27CD',
            '#00D2D3', '#FF9F43', '#C44569', '#40407A'
        ];
        this.teamEmojis = [
            '🔥', '⚡', '🌟', '🎯', '🚀', '💎', '🏆', '⭐',
            '🎵', '🎸', '🥇', '🎪', '🌈', '💫', '🎭', '🎨'
        ];
    }
    /**
     * Create a new team
     */
    createTeam(name, captainId, captainName) {
        const teamId = this.generateTeamId();
        const availableColor = this.getAvailableColor();
        const availableEmoji = this.getAvailableEmoji();
        const captain = {
            id: captainId,
            name: captainName,
            avatar: '',
            score: 0,
            isHost: false,
            hasAnswered: false,
            joinedAt: Date.now(),
            teamId: teamId,
            isTeamCaptain: true
        };
        const team = {
            id: teamId,
            name: name,
            color: availableColor,
            emoji: availableEmoji,
            players: [captain],
            score: 0,
            captainId: captainId,
            hasAnswered: false,
            createdAt: Date.now()
        };
        this.teams.set(teamId, team);
        return team;
    }
    /**
     * Add player to team
     */
    addPlayerToTeam(teamId, player) {
        const team = this.teams.get(teamId);
        if (!team)
            return false;
        // Update player with team info
        player.teamId = teamId;
        player.isTeamCaptain = false;
        team.players.push(player);
        this.teams.set(teamId, team);
        return true;
    }
    /**
     * Remove player from team
     */
    removePlayerFromTeam(playerId) {
        for (const [teamId, team] of this.teams.entries()) {
            const playerIndex = team.players.findIndex(p => p.id === playerId);
            if (playerIndex !== -1) {
                const removedPlayer = team.players[playerIndex];
                team.players.splice(playerIndex, 1);
                // If team becomes empty, disband it
                if (team.players.length === 0) {
                    this.teams.delete(teamId);
                    return null;
                }
                // If captain left, promote someone else
                if (removedPlayer.isTeamCaptain && team.players.length > 0) {
                    team.players[0].isTeamCaptain = true;
                    team.captainId = team.players[0].id;
                }
                this.teams.set(teamId, team);
                return team;
            }
        }
        return null;
    }
    /**
     * Get all teams
     */
    getAllTeams() {
        return Array.from(this.teams.values());
    }
    /**
     * Get team by ID
     */
    getTeam(teamId) {
        return this.teams.get(teamId);
    }
    /**
     * Get player's team
     */
    getPlayerTeam(playerId) {
        for (const team of this.teams.values()) {
            if (team.players.some(p => p.id === playerId)) {
                return team;
            }
        }
        return undefined;
    }
    /**
     * Update team properties
     */
    updateTeam(teamId, updates) {
        const team = this.teams.get(teamId);
        if (!team)
            return false;
        Object.assign(team, updates);
        this.teams.set(teamId, team);
        return true;
    }
    /**
     * Promote player to team captain
     */
    promoteToCaption(teamId, playerId) {
        const team = this.teams.get(teamId);
        if (!team)
            return false;
        const player = team.players.find(p => p.id === playerId);
        if (!player)
            return false;
        // Remove captain status from current captain
        team.players.forEach(p => p.isTeamCaptain = false);
        // Make new player captain
        player.isTeamCaptain = true;
        team.captainId = playerId;
        this.teams.set(teamId, team);
        return true;
    }
    /**
     * Submit team answer
     */
    submitTeamAnswer(teamAnswer) {
        const team = this.teams.get(teamAnswer.teamId);
        if (!team)
            return false;
        team.hasAnswered = true;
        team.lastAnswer = teamAnswer.answer;
        team.lastAnswerTime = teamAnswer.timestamp;
        this.teams.set(teamAnswer.teamId, team);
        return true;
    }
    /**
     * Calculate team scores based on scoring mode
     */
    calculateTeamScores(teamAnswers, correctAnswer, points, scoringMode) {
        for (const teamAnswer of teamAnswers) {
            const team = this.teams.get(teamAnswer.teamId);
            if (!team)
                continue;
            const isCorrect = teamAnswer.answer === correctAnswer;
            if (!isCorrect)
                continue;
            let teamPoints = 0;
            switch (scoringMode) {
                case 'sum':
                    // All team members get points
                    teamPoints = points * team.players.length;
                    break;
                case 'average':
                    // Team gets average points (usually just the base points)
                    teamPoints = points;
                    break;
                case 'best':
                    // Team gets full points for correct answer
                    teamPoints = points;
                    break;
                case 'captain':
                    // Only captain's answer counts, but team gets points
                    if (teamAnswer.submittedBy === team.captainId) {
                        teamPoints = points;
                    }
                    break;
            }
            team.score += teamPoints;
            this.teams.set(teamAnswer.teamId, team);
        }
    }
    /**
     * Reset team answers for new question
     */
    resetTeamAnswers() {
        for (const [teamId, team] of this.teams.entries()) {
            team.hasAnswered = false;
            team.lastAnswer = undefined;
            team.lastAnswerTime = undefined;
            this.teams.set(teamId, team);
        }
    }
    /**
     * Auto-balance teams by distributing players evenly
     */
    autoBalanceTeams(players, maxTeamSize = 3) {
        // Clear existing teams
        this.teams.clear();
        const numTeams = Math.ceil(players.length / maxTeamSize);
        const shuffledPlayers = [...players].sort(() => Math.random() - 0.5);
        // Create teams
        for (let i = 0; i < numTeams; i++) {
            const teamName = `Team ${String.fromCharCode(65 + i)}`; // Team A, Team B, etc.
            const captain = shuffledPlayers[i * maxTeamSize];
            if (captain) {
                const team = this.createTeam(teamName, captain.id, captain.name);
                // Add remaining players to this team
                for (let j = 1; j < maxTeamSize && (i * maxTeamSize + j) < shuffledPlayers.length; j++) {
                    const player = shuffledPlayers[i * maxTeamSize + j];
                    this.addPlayerToTeam(team.id, player);
                }
            }
        }
        return this.getAllTeams();
    }
    /**
     * Disband team
     */
    disbandTeam(teamId) {
        const team = this.teams.get(teamId);
        if (!team)
            return false;
        // Clear team info from all players
        team.players.forEach(player => {
            player.teamId = undefined;
            player.isTeamCaptain = false;
        });
        this.teams.delete(teamId);
        return true;
    }
    /**
     * Get team leaderboard sorted by score
     */
    getTeamLeaderboard() {
        return this.getAllTeams().sort((a, b) => b.score - a.score);
    }
    /**
     * Check if all teams have answered
     */
    allTeamsAnswered() {
        const teams = this.getAllTeams();
        return teams.length > 0 && teams.every(team => team.hasAnswered);
    }
    /**
     * Get available team color
     */
    getAvailableColor() {
        const usedColors = new Set(Array.from(this.teams.values()).map(t => t.color));
        return this.teamColors.find(color => !usedColors.has(color)) || this.teamColors[0];
    }
    /**
     * Get available team emoji
     */
    getAvailableEmoji() {
        const usedEmojis = new Set(Array.from(this.teams.values()).map(t => t.emoji));
        return this.teamEmojis.find(emoji => !usedEmojis.has(emoji)) || this.teamEmojis[0];
    }
    /**
     * Generate unique team ID
     */
    generateTeamId() {
        return `team_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Get team statistics
     */
    getTeamStats(teamId) {
        const team = this.teams.get(teamId);
        if (!team)
            return null;
        const allTeams = this.getTeamLeaderboard();
        const teamRank = allTeams.findIndex(t => t.id === teamId) + 1;
        return {
            totalPlayers: team.players.length,
            averageScore: team.players.length > 0 ? team.score / team.players.length : 0,
            answeredQuestions: team.hasAnswered ? 1 : 0, // This would be tracked better in real implementation
            teamRank
        };
    }
}
exports.TeamManager = TeamManager;
exports.teamManager = new TeamManager();
