import prisma, { DatabaseError, QuizUtils, ArrayUtils } from './prisma';
export class MPDLibrarySync {
    constructor(mpdClient) {
        this.mpdClient = mpdClient;
        this.isRunning = false;
    }
    /**
     * Set progress callback for sync operations
     */
    onProgress(callback) {
        this.progressCallback = callback;
    }
    /**
     * Discover new tracks from MPD library and sync to database
     */
    async syncMPDLibrary(options = {}) {
        if (this.isRunning) {
            throw new Error('Sync operation already in progress');
        }
        const startTime = Date.now();
        this.isRunning = true;
        const result = {
            success: false,
            tracksDiscovered: 0,
            tracksProcessed: 0,
            tracksUpdated: 0,
            tracksCreated: 0,
            errors: [],
            duration: 0
        };
        try {
            console.log('[MPD-Sync] Starting library synchronization...');
            this.reportProgress('discovering', 0, 0, 'Discovering tracks from MPD library...');
            // Get all tracks from MPD
            const mpdTracks = await this.mpdClient.listAllTracks();
            result.tracksDiscovered = mpdTracks.length;
            console.log(`[MPD-Sync] Found ${mpdTracks.length} tracks in MPD library`);
            if (mpdTracks.length === 0) {
                throw new Error('No tracks found in MPD library');
            }
            // Process tracks in batches
            const batchSize = options.batchSize || 50;
            let processed = 0;
            for (let i = 0; i < mpdTracks.length; i += batchSize) {
                const batch = mpdTracks.slice(i, i + batchSize);
                const batchEnd = Math.min(i + batchSize, mpdTracks.length);
                this.reportProgress('processing', processed, mpdTracks.length, `Processing tracks ${i + 1}-${batchEnd} of ${mpdTracks.length}...`);
                try {
                    const batchResult = await this.processBatch(batch, options.forceUpdate || false, options.preview || false);
                    result.tracksProcessed += batchResult.processed;
                    result.tracksCreated += batchResult.created;
                    result.tracksUpdated += batchResult.updated;
                    processed += batch.length;
                }
                catch (error) {
                    const errorMsg = `Batch ${i + 1}-${batchEnd} failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
                    console.error('[MPD-Sync]', errorMsg);
                    result.errors.push(errorMsg);
                }
            }
            this.reportProgress('updating', processed, mpdTracks.length, 'Updating statistics...');
            result.success = result.errors.length === 0;
            result.duration = Date.now() - startTime;
            this.reportProgress('complete', processed, mpdTracks.length, `Sync complete: ${result.tracksCreated} created, ${result.tracksUpdated} updated`);
            console.log('[MPD-Sync] Library synchronization complete:', result);
        }
        catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error during sync';
            console.error('[MPD-Sync] Synchronization failed:', error);
            result.errors.push(errorMsg);
            result.duration = Date.now() - startTime;
            this.reportProgress('error', 0, 0, `Sync failed: ${errorMsg}`);
        }
        finally {
            this.isRunning = false;
        }
        return result;
    }
    /**
     * Get enhanced track info combining MPD + database data
     */
    async getEnhancedTrack(mpdFilePath) {
        try {
            // Get basic track info from MPD
            const mpdTracks = await this.mpdClient.search('file', mpdFilePath);
            if (mpdTracks.length === 0) {
                console.warn(`[MPD-Sync] Track not found in MPD: ${mpdFilePath}`);
                return null;
            }
            const mpdTrack = mpdTracks[0];
            // Get enhanced info from database
            const quizTrack = await prisma.quizTrack.findUnique({
                where: { mpdFilePath }
            });
            // Combine data
            const enhancedTrack = {
                ...mpdTrack,
                quizTrackId: quizTrack?.id,
                difficultyRating: quizTrack?.difficultyRating || 3,
                popularityScore: quizTrack?.popularityScore || 50,
                triviaFacts: ArrayUtils.fromJsonString(quizTrack?.triviaFacts || '[]'),
                albumArtUrl: quizTrack?.albumArtUrl || undefined,
                artistImageUrl: quizTrack?.artistImageUrl || undefined,
                chartPosition: quizTrack?.chartPosition || undefined,
                chartCountry: quizTrack?.chartCountry || 'US',
                interestingFacts: ArrayUtils.objectFromJsonString(quizTrack?.interestingFacts || '{}'),
                similarArtists: ArrayUtils.fromJsonString(quizTrack?.similarArtists || '[]'),
                timesPlayed: quizTrack?.timesPlayed || 0,
                correctAnswers: quizTrack?.correctAnswers || 0,
                lastPlayed: quizTrack?.lastPlayed || undefined
            };
            return enhancedTrack;
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get enhanced track:', error);
            return null;
        }
    }
    /**
     * Search for tracks with enhanced quiz data
     */
    async searchEnhancedTracks(query, filters = {}) {
        try {
            const whereClause = {};
            // Apply search query
            if (query.trim()) {
                whereClause.OR = [
                    { title: { contains: query } },
                    { artist: { contains: query } },
                    { album: { contains: query } }
                ];
            }
            // Apply filters
            if (filters.genre) {
                whereClause.genre = filters.genre;
            }
            if (filters.artist) {
                whereClause.artist = filters.artist;
            }
            if (filters.decade) {
                const startYear = parseInt(filters.decade);
                const endYear = startYear + 9;
                whereClause.year = {
                    gte: startYear,
                    lte: endYear
                };
            }
            if (filters.difficulty) {
                whereClause.difficultyRating = filters.difficulty;
            }
            // Popularity filters
            if (filters.minPopularity || filters.maxPopularity) {
                whereClause.popularityScore = {};
                if (filters.minPopularity) {
                    ;
                    whereClause.popularityScore.gte = filters.minPopularity;
                }
                if (filters.maxPopularity) {
                    ;
                    whereClause.popularityScore.lte = filters.maxPopularity;
                }
            }
            const quizTracks = await prisma.quizTrack.findMany({
                where: whereClause,
                take: filters.limit || undefined,
                orderBy: [
                    { popularityScore: 'desc' },
                    { year: 'desc' }
                ]
            });
            // Convert to enhanced tracks
            const enhancedTracks = quizTracks.map((track) => ({
                // Create MPDTrack structure from database data
                file: track.mpdFilePath,
                title: track.title || 'Unknown Title',
                artist: track.artist || 'Unknown Artist',
                album: track.album || 'Unknown Album',
                date: track.year?.toString() || '',
                genre: track.genre || 'Unknown',
                time: track.duration || 0,
                // Enhanced fields
                quizTrackId: track.id,
                difficultyRating: track.difficultyRating,
                popularityScore: track.popularityScore,
                triviaFacts: ArrayUtils.fromJsonString(track.triviaFacts),
                albumArtUrl: track.albumArtUrl || undefined,
                artistImageUrl: track.artistImageUrl || undefined,
                chartPosition: track.chartPosition || undefined,
                chartCountry: track.chartCountry,
                interestingFacts: ArrayUtils.objectFromJsonString(track.interestingFacts),
                similarArtists: ArrayUtils.fromJsonString(track.similarArtists),
                timesPlayed: track.timesPlayed,
                correctAnswers: track.correctAnswers,
                lastPlayed: track.lastPlayed || undefined
            }));
            return enhancedTracks;
        }
        catch (error) {
            console.error('[MPD-Sync] Search failed:', error);
            throw new DatabaseError('Failed to search enhanced tracks', 'searchEnhancedTracks', error instanceof Error ? error : new Error('Unknown error'));
        }
    }
    /**
     * Get random tracks for quiz generation
     */
    async getRandomTracks(count, filters = {}) {
        try {
            const perfStart = Date.now();
            // If we're in the browser, delegate to API to avoid Prisma in client bundle
            if (typeof window !== 'undefined') {
                console.time('[MPD-Sync] getRandomTracks-browser');
                const params = new URLSearchParams({ count: String(count) });
                for (const [key, value] of Object.entries(filters)) {
                    if (value !== undefined) {
                        params.append(key, String(value));
                    }
                }
                const res = await fetch(`/api/quiz/random-tracks?${params.toString()}`);
                if (!res.ok) {
                    throw new Error(`API error ${res.status}`);
                }
                const data = await res.json();
                console.timeEnd('[MPD-Sync] getRandomTracks-browser');
                return data;
            }
            const tracks = await QuizUtils.getRandomTracks(count, filters);
            const duration = Date.now() - perfStart;
            console.log(`[MPD-Sync] getRandomTracks db fetch ${tracks.length} tracks in ${duration} ms`);
            // Convert to enhanced tracks
            return tracks.map((track) => ({
                file: track.mpd_file_path || track.mpdFilePath,
                title: track.title || 'Unknown Title',
                artist: track.artist || 'Unknown Artist',
                album: track.album || 'Unknown Album',
                date: track.year?.toString() || '',
                genre: track.genre || 'Unknown',
                time: track.duration || 0,
                quizTrackId: track.id,
                difficultyRating: track.difficultyRating,
                popularityScore: track.popularityScore,
                triviaFacts: ArrayUtils.fromJsonString(track.triviaFacts),
                albumArtUrl: track.albumArtUrl || undefined,
                artistImageUrl: track.artistImageUrl || undefined,
                chartPosition: track.chartPosition || undefined,
                chartCountry: track.chartCountry,
                interestingFacts: ArrayUtils.objectFromJsonString(track.interestingFacts),
                similarArtists: ArrayUtils.fromJsonString(track.similarArtists),
                timesPlayed: track.timesPlayed,
                correctAnswers: track.correctAnswers,
                lastPlayed: track.lastPlayed || undefined,
                lufsVolume: track.lufsVolume || undefined,
                calculatedGain: track.calculatedGain || 0
            }));
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get random tracks:', error);
            throw error;
        }
    }
    /**
     * Update track statistics after quiz play
     */
    async updateTrackStats(trackId, wasCorrect) {
        try {
            await QuizUtils.updateTrackStats(trackId, wasCorrect);
        }
        catch (error) {
            console.error('[MPD-Sync] Track stats update failed:', error);
            throw error;
        }
    }
    /**
     * Get library statistics
     */
    async getLibraryStats() {
        try {
            return await QuizUtils.getLibraryStats();
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get library stats:', error);
            throw error;
        }
    }
    async processBatch(tracks, forceUpdate = false, preview = false) {
        try {
            let created = 0;
            let updated = 0;
            // Process tracks individually for better error handling and upsert support
            for (const track of tracks) {
                try {
                    // Generate file stats (simplified for MPD tracks)
                    const fileFingerprint = `mpd-${Buffer.from(track.file).toString('base64').slice(0, 32)}`;
                    const fileSize = track.time ? track.time * 44100 * 2 * 2 : 0; // Rough estimate
                    const fileMtime = new Date();
                    const trackData = {
                        mpdFilePath: track.file,
                        fileFingerprint,
                        fileSize,
                        fileMtime,
                        title: track.title || this.extractTitleFromPath(track.file),
                        artist: track.artist || 'Unknown Artist',
                        album: track.album || 'Unknown Album',
                        year: track.date ? parseInt(track.date) || null : null,
                        genre: track.genre || 'Unknown',
                        duration: track.time || 0,
                        // JSON array fields (using ArrayUtils for SQLite compatibility)
                        quizCategories: ArrayUtils.toJsonString([]),
                        thematicTags: ArrayUtils.toJsonString([]),
                        specialLists: ArrayUtils.toJsonString([]),
                        similarArtists: ArrayUtils.toJsonString([]),
                        triviaFacts: ArrayUtils.toJsonString([]),
                        // JSON object fields
                        chartData: ArrayUtils.objectToJsonString({}),
                        culturalContext: ArrayUtils.objectToJsonString({}),
                        interestingFacts: ArrayUtils.objectToJsonString({}),
                        syncedAt: new Date()
                    };
                    if (preview) {
                        // Preview mode – determine if track would be created or updated then output diff
                        const existing = await prisma.quizTrack.findUnique({ where: { mpdFilePath: track.file } });
                        if (!existing) {
                            console.log(`(preview) CREATE -> ${track.file}`);
                            console.table([{ field: 'new', ...trackData }]);
                            created++;
                        }
                        else {
                            // build diff
                            const diff = [];
                            for (const [k, v] of Object.entries(trackData)) {
                                // @ts-ignore
                                const cur = existing[k];
                                if (JSON.stringify(cur) !== JSON.stringify(v)) {
                                    diff.push({ field: k, current: cur, new: v });
                                }
                            }
                            if (diff.length > 0) {
                                console.log(`(preview) UPDATE -> ${track.file}`);
                                console.table(diff);
                                updated++;
                            }
                        }
                        continue; // skip DB write
                    }
                    if (forceUpdate) {
                        // Use upsert for force update
                        const result = await prisma.quizTrack.upsert({
                            where: { mpdFilePath: track.file },
                            update: {
                                ...trackData,
                                updatedAt: new Date()
                            },
                            create: {
                                ...trackData,
                                difficultyRating: 3,
                                popularityScore: 50
                            }
                        });
                        updated++;
                    }
                    else {
                        // Try to create, skip if exists
                        try {
                            if (!preview) {
                                await prisma.quizTrack.create({
                                    data: {
                                        ...trackData,
                                        difficultyRating: 3,
                                        popularityScore: 50
                                    }
                                });
                            }
                            created++;
                        }
                        catch (error) {
                            if (error && error.code === 'P2002') {
                                // Track already exists, skip
                                continue;
                            }
                            throw error;
                        }
                    }
                }
                catch (error) {
                    console.error(`[MPD-Sync] Failed to process track ${track.file}:`, error);
                    // Continue with other tracks
                }
            }
            return {
                processed: tracks.length,
                created,
                updated
            };
        }
        catch (error) {
            console.error('[MPD-Sync] Batch processing failed:', error);
            throw new DatabaseError('Batch processing failed', 'processBatch', error instanceof Error ? error : new Error('Unknown error'));
        }
    }
    extractTitleFromPath(filePath) {
        const fileName = filePath.split('/').pop() || filePath;
        return fileName.replace(/\.[^/.]+$/, '').replace(/[-_]/g, ' ');
    }
    reportProgress(phase, current, total, message) {
        if (this.progressCallback) {
            this.progressCallback({ phase, current, total, message });
        }
    }
    /**
     * Check if sync is currently running
     */
    isSyncRunning() {
        return this.isRunning;
    }
    /**
     * Get available genres from the database
     */
    async getAvailableGenres() {
        try {
            return await QuizUtils.getAvailableGenres();
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get genres:', error);
            return [];
        }
    }
    /**
     * Get available decades from the database
     */
    async getAvailableDecades() {
        try {
            return await QuizUtils.getAvailableDecades();
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get decades:', error);
            return [];
        }
    }
    /**
     * Get available artists from the database
     */
    async getAvailableArtists(limit = 100) {
        try {
            const artists = await prisma.quizTrack.findMany({
                where: { artist: { not: null } },
                select: { artist: true },
                distinct: ['artist'],
                take: limit
            });
            return artists
                .map((a) => a.artist)
                .filter(Boolean)
                .sort();
        }
        catch (error) {
            console.error('[MPD-Sync] Failed to get artists:', error);
            return [];
        }
    }
    /**
     * Clean up orphaned tracks (in database but not in MPD)
     */
    async cleanupOrphanedTracks() {
        try {
            console.log('[MPD-Sync] Starting cleanup of orphaned tracks...');
            // Get all MPD file paths
            const mpdTracks = await this.mpdClient.listAllTracks();
            const mpdFilePaths = new Set(mpdTracks.map((track) => track.file));
            // Find tracks in database that are no longer in MPD
            const dbTracks = await prisma.quizTrack.findMany({
                select: { id: true, mpdFilePath: true }
            });
            const orphanedIds = dbTracks
                .filter((track) => !mpdFilePaths.has(track.mpdFilePath))
                .map((track) => track.id);
            if (orphanedIds.length > 0) {
                console.log(`[MPD-Sync] Found ${orphanedIds.length} orphaned tracks, removing...`);
                // Delete orphaned tracks
                const deleted = await prisma.quizTrack.deleteMany({
                    where: { id: { in: orphanedIds } }
                });
                console.log(`[MPD-Sync] Removed ${deleted.count} orphaned tracks`);
                return deleted.count;
            }
            console.log('[MPD-Sync] No orphaned tracks found');
            return 0;
        }
        catch (error) {
            console.error('[MPD-Sync] Cleanup failed:', error);
            throw new DatabaseError('Cleanup failed', 'cleanupOrphanedTracks', error);
        }
    }
}
