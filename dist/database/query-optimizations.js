// Optimized select fields for different use cases
export const optimizedSelects = {
    // Minimal track info for queue display
    queueTrack: {
        id: true,
        fileFingerprint: true,
        mpdFilePath: true,
        title: true,
        artist: true,
        album: true,
        albumArtUrl: true,
        localAlbumArtThumbnail: true,
        localAlbumArtCover: true,
        localAlbumArtOriginal: true,
        duration: true,
        calculatedGain: true, // For volume normalization
    },
    // Suggestion with votes
    suggestionWithVotes: {
        id: true,
        title: true,
        artist: true,
        album: true,
        duration: true,
        genre: true,
        year: true,
        filePath: true,
        albumArtUrl: true,
        suggestedById: true,
        suggestedBy: true,
        status: true,
        createdAt: true,
        _count: {
            select: { votes: true }
        }
    },
    // User minimal info
    userMinimal: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        role: true,
    },
    // Track for MPD status
    mpdTrack: {
        id: true,
        mpdFilePath: true,
        title: true,
        artist: true,
        albumArtUrl: true,
        genre: true,
        year: true,
    },
    // Quiz track minimal
    quizTrackMinimal: {
        id: true,
        mpdFilePath: true,
        title: true,
        artist: true,
        album: true,
        year: true,
        genre: true,
        duration: true,
        albumArtUrl: true,
    },
    // Quiz track for questions
    quizTrackQuestion: {
        id: true,
        mpdFilePath: true,
        title: true,
        artist: true,
        album: true,
        year: true,
        genre: true,
        duration: true,
        albumArtUrl: true,
        triviaFacts: true,
        interestingFacts: true,
        chartData: true,
    },
    // Game session minimal
    gameSessionMinimal: {
        id: true,
        userId: true,
        gameMode: true,
        totalQuestions: true,
        correctAnswers: true,
        totalScore: true,
        startedAt: true,
        completedAt: true,
    },
};
// Helper to build efficient queries
export function buildOptimizedQuery(baseQuery, selectFields) {
    if (selectFields) {
        return {
            ...baseQuery,
            select: selectFields
        };
    }
    return baseQuery;
}
