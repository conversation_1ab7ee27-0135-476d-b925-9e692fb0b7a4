# Socket Server Technical Specifications

## Overview

The Music Quiz application uses a robust WebSocket architecture built on Socket.IO for real-time multiplayer functionality. The system supports horizontal scaling, automatic failover, and comprehensive monitoring.

## Server Architecture

### Entry Points

1. **Production Server** (`scripts/socket-server.js`)
   - Main multiplayer server implementation
   - Full multiplayer functionality
   - Uses TypeScript compilation on startup

### Core Components

#### Socket Server (`lib/socket-server.ts`)

Core game management server:
- Room-based architecture  
- Player session management
- Game state synchronization
- Event routing
- Question timer management
- Automatic question advancement

### Supporting Services

**Note**: These services enhance the main socket server functionality but are optional. The core multiplayer functionality is implemented in `lib/socket-server.ts`.

#### Performance Monitoring (`lib/socket/performance-monitor.ts`)

Real-time metrics tracking:
- Connection latency
- Error rates
- Resource usage (CPU/Memory)
- Game completion rates
- Player behavior analytics

#### Anti-Cheat System (`lib/multiplayer/anti-cheat-validator.ts`)

Multi-layer cheat detection:
- Timing validation (min response time)
- Pattern detection (accuracy patterns)
- Statistical analysis
- Risk scoring system

#### Reconnection Manager (`lib/multiplayer/reconnection-manager.ts`)

Graceful disconnection handling:
- 15-minute reconnection window
- State recovery
- Exponential backoff
- Session persistence

#### Host Migration (`lib/multiplayer/host-migration-manager.ts`)

Automatic host selection strategies:
- Longest connected player
- Highest score
- Most active player
- Round-robin fallback

#### Message Batching (`lib/socket/message-batcher.ts`)

Network optimization:
- Automatic message aggregation
- Compression support
- Adaptive batch sizing
- 50%+ overhead reduction

#### State Management (`lib/socket/game-state-cache.ts`)

High-performance caching:
- LRU eviction policy
- TTL-based expiration
- Compression for large states
- Memory monitoring

## Socket Events Reference

### Connection Events

| Event | Direction | Payload | Description |
|-------|-----------|---------|-------------|
| `connection` | Server ← Client | `{ auth: token }` | Initial connection |
| `disconnect` | Server ← Client | `{ reason: string }` | Client disconnect |
| `reconnect` | Server ← Client | `{ gameId, playerId, token }` | Reconnection attempt |

### Game Management

| Event | Direction | Payload | Description |
|-------|-----------|---------|-------------|
| `create-game` | Client → Server | `{ hostName, settings }` | Create new game |
| `game-created` | Server → Client | `{ gameId, gameCode }` | Game creation success |
| `join-game` | Client → Server | `{ gameCode, playerName }` | Join existing game |
| `player-joined` | Server → All | `{ player, totalPlayers }` | New player notification |
| `start-game` | Client → Server | `{ gameId }` | Host starts game |
| `game-started` | Server → All | `{ totalQuestions, timePerQuestion }` | Game begin notification |

### Game Play

| Event | Direction | Payload | Description |
|-------|-----------|---------|-------------|
| `question` | Server → All | `{ question, options, timeLimit }` | New question |
| `submit-answer` | Client → Server | `{ answerId, responseTime }` | Player answer |
| `answer-result` | Server → Client | `{ correct, score, explanation }` | Answer feedback |
| `scores-update` | Server → All | `{ leaderboard }` | Score updates |
| `game-ended` | Server → All | `{ finalScores, statistics }` | Game complete |

### Host Migration

| Event | Direction | Payload | Description |
|-------|-----------|---------|-------------|
| `host-disconnected` | Server → All | `{ timeout: 5000 }` | Host disconnect notice |
| `host-migration` | Server → All | `{ newHost, reason }` | New host selected |
| `host-changed` | Server → New Host | `{ permissions }` | Host role assigned |

### Error Handling

| Event | Direction | Payload | Description |
|-------|-----------|---------|-------------|
| `error` | Server → Client | `{ code, message, details }` | Error notification |
| `validation-error` | Server → Client | `{ field, reason }` | Input validation failure |
| `rate-limit` | Server → Client | `{ retryAfter }` | Rate limit exceeded |

## Security Measures

### Authentication
- Token-based session management
- Secure WebSocket upgrade
- Player identity verification

### Rate Limiting
- Per-minute request limits
- Game creation throttling
- Connection attempt limits

### Input Validation
- All inputs sanitized
- Length limits enforced
- Special character filtering

### Anti-Cheat
- Response time validation
- Pattern analysis
- Statistical anomaly detection
- Risk-based flagging

## Performance Specifications

### Capacity
- **Concurrent games**: 100+
- **Players per game**: 50
- **Total concurrent players**: 1000+
- **Messages per second**: 10,000+

### Latency
- **Average response**: <100ms
- **99th percentile**: <500ms
- **Reconnection time**: <2s
- **State sync**: <200ms

### Resource Usage
- **Memory per game**: ~2MB
- **CPU per 100 players**: ~5%
- **Network per player**: ~1KB/s

## Monitoring and Alerts

### Performance Alerts
- High latency (>1000ms)
- High error rate (>5%)
- Resource exhaustion (CPU >80%, Memory >90%)
- Low completion rate (<80%)

### Security Alerts
- Suspicious timing patterns
- High accuracy anomalies
- Rapid reconnection attempts
- Invalid message patterns

### Operational Metrics
- Active games count
- Total players online
- Average game duration
- Question generation rate
- Cache hit rates

## Deployment Configuration

### Environment Variables
```bash
# Socket Server
SOCKET_PORT=3001
NODE_ENV=production

# Redis (optional for scaling)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Performance
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ANTI_CHEAT=true
ENABLE_MESSAGE_BATCHING=true

# Limits
MAX_PLAYERS_PER_GAME=50
MAX_GAMES_PER_HOUR=10
MAX_REQUESTS_PER_MINUTE=100
```

### PM2 Configuration
```javascript
module.exports = {
  apps: [{
    name: 'music-quiz-socket',
    script: './scripts/socket-server.js',
    instances: 1,  // Socket.IO requires single instance without Redis adapter
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      SOCKET_PORT: 3001
    },
    error_file: './logs/socket-error.log',
    out_file: './logs/socket-out.log',
    merge_logs: true,
    time: true
  }]
}
```

## Scaling Architecture

### Horizontal Scaling
- Redis-based session sharing
- Sticky sessions for Socket.IO
- Load balancer configuration
- Shared game state

### Vertical Scaling
- Connection pooling
- Worker thread utilization
- Memory optimization
- CPU affinity

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Check CORS configuration
   - Verify firewall rules
   - Test WebSocket upgrade

2. **Performance Degradation**
   - Monitor cache hit rates
   - Check message batch sizes
   - Review game cleanup

3. **State Synchronization**
   - Verify Redis connectivity
   - Check state diff sizes
   - Monitor network latency

### Debug Commands
```bash
# Check server status
pm2 status music-quiz-socket

# View real-time logs
pm2 logs music-quiz-socket --lines 100

# Monitor performance
pm2 monit

# Test socket connection
wscat -c ws://localhost:3001
```