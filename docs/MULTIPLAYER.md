# Multiplayer Guide

This guide covers the real-time multiplayer functionality in the Music Quiz application.

## Overview

The multiplayer system uses WebSockets with Socket.IO for real-time game synchronization. The architecture supports multiple concurrent games, automatic reconnection, host migration, and anti-cheat protection.

## Architecture

### Server Components

1. **Socket Server** (`lib/socket-server.ts`)
   - Core game logic and player management
   - Room-based architecture for isolated games
   - Real-time event handling and state synchronization
   - Question timer management
   - Automatic question advancement

2. **Supporting Systems**
   - **Anti-Cheat** (`lib/multiplayer/anti-cheat-validator.ts`): Timing validation and pattern detection
   - **Reconnection** (`lib/multiplayer/reconnection-manager.ts`): 15-minute grace period for disconnections
   - **Host Migration** (`lib/multiplayer/host-migration-manager.ts`): Automatic host selection on disconnect
   - **Performance Monitor** (`lib/socket/performance-monitor.ts`): Real-time metrics and alerting
   - **Game State Cache** (`lib/socket/game-state-cache.ts`): LRU caching for performance
   - **Message Batcher** (`lib/socket/message-batcher.ts`): Optimized socket communication

### Client Components

1. **Multiplayer Hook** (`hooks/use-multiplayer.ts`)
   - Primary interface for multiplayer functionality
   - Handles connection, game state, and player actions
   - Automatic reconnection with state recovery

2. **Game Socket Hook** (`hooks/use-game-socket.ts`)
   - Enhanced socket connection management
   - Real-time event handling and state updates

3. **UI Components**
   - **MultiplayerLobby** (`components/multiplayer-lobby.tsx`): Game creation and joining
   - **QuizInterface**: Synchronized question display and answer submission
   - **WebSocketMonitor** (`components/websocket-monitor.tsx`): Debug monitoring

## Socket Events

### Client → Server

- `create-game`: Create a new game room
- `join-game`: Join existing game with code
- `start-game`: Host starts the game
- `submit-answer`: Player submits answer
- `leave-game`: Player leaves the game
- `reconnect`: Attempt to reconnect to game

### Server → Client

- `game-created`: Game successfully created
- `player-joined`: New player joined
- `game-started`: Game has begun
- `question`: New question data
- `answer-result`: Answer feedback
- `game-ended`: Game completed
- `host-changed`: New host selected
- `error`: Error messages

## Game Flow

1. **Host creates game** → Server generates game code
2. **Players join** → Server validates and adds to game
3. **Host starts game** → Server begins question sequence
4. **Questions distributed** → All players receive simultaneously
5. **Answers collected** → Server validates timing and correctness
6. **Scores updated** → Real-time leaderboard updates
7. **Game ends** → Final results and statistics

## Security Features

- **Anti-cheat validation**: Timing checks and pattern detection
- **Rate limiting**: Request and game creation limits
- **Input validation**: Sanitization of all player inputs
- **Session security**: Token-based authentication
- **Connection limits**: Max players per game enforced

## Performance Optimizations

- **Message batching**: Reduces socket overhead by 50%+
- **State caching**: LRU cache for frequently accessed data
- **Database indexes**: Optimized queries for multiplayer operations
- **Connection pooling**: Efficient resource management
- **State diffing**: Only send changed data to clients
