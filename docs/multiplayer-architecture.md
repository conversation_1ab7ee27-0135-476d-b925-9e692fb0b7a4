# Multiplayer Integration Architecture

## Overview
The multiplayer quiz system consists of several interconnected components that handle real-time game management, player synchronization, and quiz flow.

## Core Components

### 1. Socket Server (`/lib/socket-server.ts`)
- **Purpose**: Main WebSocket server handling real-time communication
- **Entry Point**: `/scripts/socket-server.js` (bootstraps TypeScript)
- **Port**: 3001 (configurable via SOCKET_PORT env var)
- **Class**: `MultiplayerSocketServer`
- **Features**:
  - Real-time player connections via Socket.IO
  - Game room management
  - Event broadcasting to all players in a game
  - Connection/disconnection handling
  - CORS configuration for cross-origin requests
  - Question timer management
  - Automatic question advancement on timeout
- **Note**: No `multiplayer-server.ts` file - all functionality in `socket-server.ts`

### 2. Game Manager (`/lib/game-manager.ts`)
- **Purpose**: Core game logic and state management
- **Responsibilities**:
  - Game creation and configuration
  - Question generation based on game mode
  - Player management (add/remove)
  - Game state transitions (waiting → playing → question-results → finished)
  - Score calculation in `processQuestionResults()`
  - Answer tracking without immediate feedback
- **Game Modes**:
  - `ultimote`: Advanced quiz with customizable categories (default)
  - `general`: General knowledge questions only
  - `classic`: Traditional music identification
  - Other music modes: guess-the-year, chart-position, etc.
- **Scoring System**:
  - Max 1000 points per question
  - Points decrease based on answer time
  - Scores updated only after question ends

### 3. Game Store (`/lib/game-store.ts`)
- **Purpose**: In-memory storage for active games
- **Features**:
  - Game PIN generation (4-character codes)
  - Game state persistence during sessions
  - Game lookup by ID
  - Automatic cleanup of finished games

### 4. Multiplayer Quiz Generator (`/lib/multiplayer-quiz-generator.ts`)
- **Purpose**: Generate questions for multiplayer games
- **Methods**:
  - `generateUltimoteQuestions()`: Mixed category questions
  - `generateGeneralKnowledgeQuestions()`: General trivia questions
  - `generateDatabaseQuestions()`: Music questions from database
- **Integration**: Fetches from Prisma database and general knowledge data

### 5. Client-Side Hooks

#### `useMultiplayer` (`/hooks/use-multiplayer.ts`)
- **Purpose**: Main hook for multiplayer functionality
- **Features**:
  - Game state management
  - Socket event handling
  - Timer countdown logic
  - Answer submission
  - Game creation/joining
- **State Management**:
  - `gameState`: Current game information
  - `currentQuestion`: Active question data
  - `timeRemaining`: Countdown timer
  - `leaderboard`: Player rankings
  - `questionEndData`: Correct answer info

#### `useMultiplayerSocket` (`/hooks/use-multiplayer-socket.ts`)
- **Purpose**: WebSocket connection management
- **Singleton Pattern**: Ensures single connection per client
- **Features**:
  - Auto-reconnection
  - Event listener registration
  - Connection state tracking

### 6. UI Components

#### `MultiplayerPage` (`/components/multiplayer-page.tsx`)
- **Purpose**: Main container for multiplayer experience
- **States**:
  - `menu`: Game selection screen
  - `lobby`: Waiting room before game starts
  - `playing`: Active quiz
  - `results`: Final leaderboard

#### `MultiplayerQuiz` (`/components/multiplayer-quiz.tsx`)
- **Purpose**: Active quiz gameplay UI
- **Features**:
  - Question display
  - Answer selection
  - Timer display
  - Score tracking
  - Audio playback (for music questions)
  - Transition animations

#### `MultiplayerLobby` (`/components/multiplayer-lobby.tsx`)
- **Purpose**: Pre-game waiting room
- **Features**:
  - Player list
  - Game PIN display
  - Start game button (host only)
  - Game settings display

#### `MultiplayerMenu` (`/components/multiplayer-menu.tsx`)
- **Purpose**: Initial game selection
- **Options**:
  - Create new game
  - Join existing game
  - Game mode selection
  - Settings configuration

#### `MultiplayerResults` (`/components/multiplayer-results.tsx`)
- **Purpose**: End-game leaderboard
- **Features**:
  - Final rankings
  - Score display
  - Play again option
  - Return to menu

### 7. Socket Event Flow

#### Client → Server Events:
- `create-game`: Initialize new game with settings
- `join-game`: Player joins existing game
- `start-game`: Host begins the quiz
- `submit-answer`: Player selects an answer (no immediate feedback)
- `next-action`: Progress to next question
- `leave-game`: Player exits
- `request-game-state`: Reconnection sync

#### Server → Client Events:
- `game-created`: Confirms game creation
- `game-state`: Full game state update
- `player-joined/left`: Player list changes
- `game-countdown`: Pre-game countdown
- `game-started`: Quiz begins with first question
- `question`: New question data
- `question-results`: Reveals correct answer and scores
- `game-over`: Final results with leaderboard
- `answer-submitted`: Acknowledges answer received
- `error`: Error notifications

### 8. Data Models

#### GameState
```typescript
{
  gameId: string
  status: 'waiting' | 'playing' | 'finished'
  gameMode: string
  players: Player[]
  questions: Question[]
  currentQuestionIndex: number
  currentQuestion: Question | null
  totalQuestions: number
  timePerQuestion: number
  settings: GameSettings
}
```

#### Player
```typescript
{
  id: string
  name: string
  avatar: string
  score: number
  isHost: boolean
  hasAnswered: boolean
  joinedAt: number
}
```

#### Question
```typescript
{
  id: string
  type: 'multiple-choice'
  question: string
  options: string[]
  correctAnswer: number
  points: number
  timeLimit: number
  audioFile?: string  // For music questions
  track?: Track       // Full track data
}
```

### 9. PM2 Process Management
- **music-quiz-socket**: Socket server process
- **music-quiz-nextjs**: Next.js frontend
- **music-quiz-mpd-proxy**: MPD proxy for audio

### 10. Additional Features
- **Team Mode**: Support for team-based gameplay
- **Voting System**: Players can vote on categories
- **Spectator Mode**: Watch games without participating
- **Auto-advancement**: Automatic progression between questions
- **Reconnection Support**: Players can rejoin after disconnection

## Architecture Diagram
```
┌─────────────┐     WebSocket      ┌──────────────┐
│   Client    │ ←---------------→  │Socket Server │
│  (Next.js)  │                    │  (Port 3001) │
└─────────────┘                    └──────────────┘
      ↓                                    ↓
┌─────────────┐                    ┌──────────────┐
│   Hooks     │                    │Game Manager  │
│useMultiplayer│                   │              │
└─────────────┘                    └──────────────┘
                                          ↓
                                   ┌──────────────┐
                                   │ Game Store   │
                                   │  (Memory)    │
                                   └──────────────┘
                                          ↓
                                   ┌──────────────┐
                                   │Quiz Generator│
                                   │              │
                                   └──────────────┘
                                          ↓
                                   ┌──────────────┐
                                   │   Prisma DB  │
                                   │   (Tracks)   │
                                   └──────────────┘
```

## Key Integration Points
1. **Real-time Sync**: All players see questions simultaneously
2. **Score Calculation**: Points based on correctness and speed
3. **Audio Streaming**: MPD integration for music questions
4. **Database Access**: Prisma ORM for track/question data
5. **State Management**: React hooks for client-side state
6. **Error Handling**: Graceful disconnection/reconnection