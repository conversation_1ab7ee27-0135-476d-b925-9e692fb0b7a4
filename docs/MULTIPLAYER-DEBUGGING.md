# Multiplayer Debugging Guide

This guide explains the debugging tools available for the multiplayer quiz system.

## Overview

The multiplayer system now includes several debugging tools that eliminate the need for manual browser testing:

1. **Socket Event Monitor** - Real-time logging of all socket events
2. **Automated Test Suite** - Comprehensive tests using Vitest
3. **Mock Game Runner** - Simulates complete multiplayer games
4. **Interactive CLI Debugger** - Manual testing via command line

## Quick Start

```bash
# Run automated tests
npm run mp:test

# Run a mock game with 3 players
npm run mp:mock

# Start interactive debugger
npm run mp:debug

# Start server with event monitoring
npm run mp:monitor
```

## 1. Socket Event Monitor

The Socket Event Monitor logs all socket communication in real-time.

### Enable Monitoring

```bash
# Start server with monitoring enabled
SOCKET_DEBUG=true npm run socket-server

# Or use the convenience script
npm run mp:monitor
```

### Monitor Features

- Logs all incoming/outgoing events with timestamps
- Shows event data (can be disabled)
- Tracks room joins/leaves
- Logs to both console and file (`socket-debug.log`)
- Filters out noisy events like ping/pong

### Configuration

Edit `lib/socket-server.ts` to customize:

```typescript
attachDebugMonitor(this.io, {
  consoleOutput: true,
  logToFile: true,
  includeData: true,
  excludeEvents: ['ping', 'pong'],
  filterEvents: ['question', 'answer'] // Only log specific events
})
```

## 2. Automated Test Suite

Comprehensive test coverage for multiplayer functionality.

### Run Tests

```bash
# Run all multiplayer tests
npm run mp:test

# Run with watch mode
npm run mp:test -- --watch

# Run specific test
npm run mp:test -- -t "should create a game"
```

### Test Categories

- **Connection Tests** - Server connectivity
- **Game Creation Tests** - Creating games with different modes
- **Game Joining Tests** - Player joining and validation
- **Game Flow Tests** - Complete game simulation
- **Error Handling Tests** - Disconnection, reconnection
- **Performance Tests** - Concurrent operations

### Writing New Tests

```javascript
import { testUtils } from './multiplayer-test-suite.test'

it('should handle custom scenario', async () => {
  const host = await testUtils.createPlayer('Host')
  const response = await testUtils.emitWithResponse(host.socket, 'create-game', {
    hostName: 'Host',
    gameMode: 'classic'
  })
  expect(response.success).toBe(true)
})
```

## 3. Mock Game Runner

Simulates complete multiplayer games with AI players.

### Basic Usage

```bash
# Run a default game (3 players, classic mode, 5 questions)
npm run mp:mock

# Custom configuration
npm run mp:mock -- --players 5 --mode ultimote --questions 10

# Enable debug logging
npm run mp:mock -- --debug

# Interactive mode with scenarios
npm run mp:mock interactive
```

### Command Line Options

- `-s, --server <url>` - Socket.IO server URL (default: http://localhost:3001)
- `-p, --players <count>` - Number of players (default: 3)
- `-m, --mode <mode>` - Game mode: classic, quickFire, ultimote (default: classic)
- `-q, --questions <count>` - Number of questions (default: 5)
- `-d, --debug` - Enable debug logging

### Mock Player Behavior

- Players join with random delays
- Answer questions after 1-4 seconds
- Choose random answers
- Automatic game flow handling

## 4. Interactive CLI Debugger

Manual testing and exploration via command line.

### Start the Debugger

```bash
npm run mp:debug
```

### Commands

#### Connection
- `connect [url]` - Connect to server
- `disconnect` - Disconnect from server

#### Game Management
- `create <name> [mode]` - Create game as host
- `join <pin> <name>` - Join game with PIN
- `start` - Start the game (host only)
- `answer <option>` - Submit answer

#### Information
- `players` - Show current players
- `state` - Show game state
- `events [filter]` - Show event log

#### Advanced
- `emit <event> [data]` - Emit custom event
- `listen <event>` - Add event listener

#### Utility
- `clear` - Clear screen
- `help` - Show help
- `exit` - Exit CLI

### Example Session

```bash
mp> connect
✓ Connected! Socket ID: abc123

mp> create Alice classic
✓ Game created!
Game PIN: A1B2

mp> state
Game State:
  PIN: A1B2
  Status: waiting
  Players: 1
  Question: 0/5
  Mode: classic

mp> start
✓ Game started!

❓ Question 1/5:
What year was "Bohemian Rhapsody" released?
Category: classic
Time: 30s

Options:
  1. 1973
  2. 1975
  3. 1977
  4. 1979

mp> answer 2
✓ Correct! (+100 points)
```

## Debugging Workflows

### 1. Testing New Features

```bash
# 1. Enable monitoring
npm run mp:monitor

# 2. Run automated tests
npm run mp:test

# 3. Test with mock games
npm run mp:mock -- --debug
```

### 2. Investigating Issues

```bash
# 1. Start server with monitoring
npm run mp:monitor

# 2. Use interactive debugger to reproduce
npm run mp:debug

# 3. Check socket-debug.log for detailed events
cat socket-debug.log | grep "error"
```

### 3. Performance Testing

```bash
# Run stress test scenario
npm run mp:mock interactive
# Select "Stress Test" (8 players, 20 questions)

# Or run custom high-load test
npm run mp:mock -- --players 8 --questions 50
```

### 4. Testing Specific Game Modes

```bash
# Test UlTimote mode
npm run mp:mock -- --mode ultimote --questions 15

# Test Quick Fire mode
npm run mp:mock -- --mode quickFire --questions 10 --players 4
```

## Troubleshooting

### Server Not Starting
```bash
# Check if port is in use
lsof -i :3001

# Kill existing process
kill -9 <PID>
```

### Tests Failing
```bash
# Ensure server is running
npm run socket-server

# Check server logs
npm run mp:monitor
```

### Socket Connection Issues
```bash
# Test basic connectivity
npm run mp:debug
mp> connect
mp> emit ping
```

## Best Practices

1. **Always run tests after changes**
   ```bash
   npm run mp:test
   ```

2. **Use monitoring during development**
   ```bash
   npm run mp:monitor
   ```

3. **Test edge cases with CLI**
   ```bash
   npm run mp:debug
   # Test disconnection, invalid inputs, etc.
   ```

4. **Simulate real scenarios**
   ```bash
   npm run mp:mock -- --players 6 --questions 15
   ```

5. **Check logs for anomalies**
   ```bash
   tail -f socket-debug.log | grep -E "(error|warn|disconnect)"
   ```

## Advanced Usage

### Custom Event Testing

```javascript
// In mp:debug CLI
mp> emit custom-event {"type":"test","data":123}
mp> listen custom-response
```

### Automated Scenario Testing

Create custom test scenarios in `tests/multiplayer/scenarios/`:

```javascript
export async function testScenario() {
  const result = await testUtils.simulateCompleteGame(4, 10)
  console.log('Game completed:', result)
}
```

### Integration with CI/CD

```yaml
# .github/workflows/test.yml
- name: Start socket server
  run: npm run socket-server &
  
- name: Wait for server
  run: sleep 5
  
- name: Run multiplayer tests
  run: npm run mp:test
```

## Summary

With these tools, you can:

- Debug socket communication without opening browsers
- Run automated tests for all multiplayer scenarios
- Simulate complete games with mock players
- Manually test specific features via CLI
- Monitor all socket events in real-time

No more manual browser testing needed!