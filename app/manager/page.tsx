"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Sheet, SheetContent, SheetDescription, SheetHeader, Sheet<PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet"
import { BluetoothSettings } from "@/components/bluetooth-settings"
import {
  Server,
  Users,
  Music,
  Play,
  Pause,
  RefreshCw,
  Trash2,
  Shield,
  Database,
  Wifi,
  Activity,
  Settings,
  BarChart3,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ArrowLeft,
  Loader2,
  Square,
  RotateCcw,
  X,
  Menu,
  Home,
  List
} from "lucide-react"
import Link from "next/link"
import { DashboardSkeleton } from "@/components/skeletons/dashboard-skeleton"
import { clearJukeboxCache, getJukeboxCacheStats } from "@/lib/jukebox-performance"
import { AudioSettings } from "@/components/audio-settings"
import { AudioOutputSwitcher } from "@/components/audio-output-switcher"
import { SpeakerOutputSwitcher } from "@/components/speaker-output-switcher"
import { ContentFilterSettings } from "@/components/content-filter-settings"
import { AutoQueueSettings } from "@/components/auto-queue-settings"
import { toast } from "sonner"

interface ServerStatus {
  name: string
  status: 'online' | 'offline' | 'error'
  port: number
  uptime?: string
  lastCheck: string
  details?: string
}

interface User {
  id: string
  displayName: string
  email?: string
  role: string
  lastActive: string
  gamesPlayed: number
}

interface GameSession {
  id: string
  gameMode: string
  status: 'active' | 'finished'
  playerCount: number
  startTime: string
  host: string
}

interface LibraryStats {
  totalTracks: number
  totalArtists: number
  totalAlbums: number
  genres: { [key: string]: number }
  decades: { [key: string]: number }
  recentlyAdded: number
}

export default function ManagerDashboard() {
  const [serverStatuses, setServerStatuses] = useState<ServerStatus[]>([
    { name: 'Socket Server', status: 'offline', port: 3001, lastCheck: 'Never' },
    { name: 'MPD Server', status: 'offline', port: 6600, lastCheck: 'Never' },
    { name: 'MPD Proxy', status: 'offline', port: 8001, lastCheck: 'Never' }
  ])
  
  const [users, setUsers] = useState<User[]>([])
  const [games, setGames] = useState<GameSession[]>([])
  const [libraryStats, setLibraryStats] = useState<LibraryStats>({
    totalTracks: 0,
    totalArtists: 0,
    totalAlbums: 0,
    genres: {},
    decades: {},
    recentlyAdded: 0
  })
  
  const [loading, setLoading] = useState(true)
  const [userFilter, setUserFilter] = useState('')
  const [selectedRole, setSelectedRole] = useState<string>('')
  const [updatingUserId, setUpdatingUserId] = useState<string | null>(null)
  const [managingServer, setManagingServer] = useState<string | null>(null)
  const [pendingRoles, setPendingRoles] = useState<{[userId: string]: string}>({})
  const [roleChangeStatus, setRoleChangeStatus] = useState<{[userId: string]: 'success' | 'error'}>({})
  const [cacheStats, setCacheStats] = useState({ library: 0, categories: 0 })
  const [jukeboxSettings, setJukeboxSettings] = useState({
    hideChartSongs: false,
    hideMyItunes: false
  })
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  useEffect(() => {
    loadDashboardData()
    updateCacheStats()
    loadSettings()
    // Check server status every 60 seconds instead of 30
    const interval = setInterval(checkServerStatuses, 60000)
    return () => clearInterval(interval)
  }, [])

  const loadSettings = () => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('jukebox-settings')
      if (stored) {
        try {
          setJukeboxSettings(JSON.parse(stored))
        } catch (e) {
          console.warn('Failed to parse stored settings')
        }
      }
    }
  }

  const saveSettings = (newSettings: typeof jukeboxSettings) => {
    setJukeboxSettings(newSettings)
    if (typeof window !== 'undefined') {
      localStorage.setItem('jukebox-settings', JSON.stringify(newSettings))
    }
  }

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      await Promise.all([
        checkServerStatuses(),
        loadUsers(),
        loadGames(),
        loadLibraryStats()
      ])
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const manageServer = async (action: 'start' | 'stop' | 'restart', serverName: string) => {
    setManagingServer(`${action}-${serverName}`)

    try {
      const serviceMap: { [key: string]: string } = {
        'Socket Server': 'socket-server',
        'MPD Server': 'mpd',
        'MPD Proxy': 'mpd-proxy'
      }

      const service = serviceMap[serverName]
      if (!service) {
        throw new Error(`Unknown server: ${serverName}`)
      }

      console.log(`${action}ing ${serverName}...`)

      const response = await fetch('/api/manager/servers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action, service })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to manage server')
      }

      const result = await response.json()
      console.log(`${action} ${serverName} result:`, result)

      setTimeout(() => {
        checkServerStatuses()
        setManagingServer(null)
      }, 3000)

      return result
    } catch (error) {
      console.error(`Failed to ${action} ${serverName}:`, error)
      setManagingServer(null)
      toast.error(`Failed to ${action} ${serverName}`, {
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        duration: 5000,
      })
      throw error
    }
  }

  const checkServerStatuses = async () => {
    const newStatuses = [...serverStatuses]
    
    // Check Socket Server with a simple health check
    try {
      // Use a HEAD request to check if the server is responding
      const socketResponse = await fetch('http://************:3001/socket.io/', { 
        method: 'HEAD',
        mode: 'no-cors',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      }).catch(() => null)
      
      // In no-cors mode, we can't read the response status, but if the request
      // doesn't throw, we assume the server is at least responding
      newStatuses[0] = { 
        ...newStatuses[0], 
        status: 'online', 
        lastCheck: new Date().toLocaleTimeString() 
      }
    } catch (error) {
      // Socket server is not responding
      newStatuses[0] = { 
        ...newStatuses[0], 
        status: 'offline', 
        lastCheck: new Date().toLocaleTimeString() 
      }
    }

    // Check MPD via API
    try {
      const { TokenManager } = await import('@/lib/auth-service')
      const token = TokenManager.getToken()
      
      const headers: HeadersInit = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const mpdResponse = await fetch('/api/mpd/status', { headers })
      if (mpdResponse.ok) {
        newStatuses[1] = { 
          ...newStatuses[1], 
          status: 'online', 
          lastCheck: new Date().toLocaleTimeString() 
        }
      } else {
        newStatuses[1] = { 
          ...newStatuses[1], 
          status: 'error', 
          lastCheck: new Date().toLocaleTimeString() 
        }
      }
    } catch {
      newStatuses[1] = { 
        ...newStatuses[1], 
        status: 'offline', 
        lastCheck: new Date().toLocaleTimeString() 
      }
    }

    // Check MPD Proxy
    try {
      let proxyResponse = await fetch('http://************:8001/health')
      let isProxyOnline = false
      let mpdConnected = false

      if (proxyResponse.ok) {
        const healthData = await proxyResponse.json()
        isProxyOnline = true
        mpdConnected = healthData.mpd?.connected || false
      } else {
        proxyResponse = await fetch('http://************:8001/status')
        if (proxyResponse.ok) {
          const statusData = await proxyResponse.json()
          isProxyOnline = true
          mpdConnected = statusData.connected || false
        }
      }
      
      if (isProxyOnline) {
        newStatuses[2] = {
          ...newStatuses[2],
          status: mpdConnected ? 'online' : 'error',
          lastCheck: new Date().toLocaleTimeString(),
          details: mpdConnected ? 'Proxy online, MPD connected' : 'Proxy online, MPD disconnected'
        }
      } else {
        newStatuses[2] = {
          ...newStatuses[2],
          status: 'error',
          lastCheck: new Date().toLocaleTimeString(),
          details: 'Proxy not responding'
        }
      }
    } catch (error: unknown) {
      console.log('MPD Proxy check failed:', error instanceof Error ? error.message : 'Unknown error')
      newStatuses[2] = { 
        ...newStatuses[2], 
        status: 'offline', 
        lastCheck: new Date().toLocaleTimeString() 
      }
    }

    setServerStatuses(newStatuses)
  }

  const loadUsers = async () => {
    try {
      // Get auth token
      const { TokenManager } = await import('@/lib/auth-service')
      const token = TokenManager.getToken()
      
      console.log('Loading users with token:', token ? 'Present' : 'Missing')
      
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      } else {
        console.warn('No auth token found in localStorage')
      }
      
      const response = await fetch('/api/admin/roles-debug', { 
        headers,
        credentials: 'include' // Include cookies
      })
      
      console.log('Response status:', response.status)
      console.log('Response headers:', response.headers)
      
      if (response.ok) {
        const data = await response.json()
        console.log('Loaded users:', data)
        // Map database users to display format
        const mappedUsers = (data.users || []).map((user: any) => ({
          id: user.id,
          displayName: user.displayName || user.username || user.email || 'Unknown User',
          email: user.email,
          role: user.role || 'user',
          lastActive: user.lastActive ? new Date(user.lastActive).toLocaleString() : 'Never',
          gamesPlayed: user.gamesPlayed || 0
        }))
        setUsers(mappedUsers)
        toast.success(`Loaded ${mappedUsers.length} users successfully`)
      } else {
        let errorMessage = `Failed to load users: ${response.status} ${response.statusText}`
        let errorDescription = 'The server encountered an error while loading users'
        
        try {
          const errorData = await response.json()
          if (errorData.message) {
            errorDescription = errorData.message
          }
        } catch (e) {
          // If response is not JSON, try text
          try {
            const errorText = await response.text()
            if (errorText) {
              errorDescription = errorText
            }
          } catch (textError) {
            console.error('Could not parse error response')
          }
        }
        
        console.error('Failed to load users:', response.status, response.statusText, errorDescription)
        toast.error(errorMessage, {
          description: errorDescription,
          duration: 5000,
        })
      }
    } catch (error) {
      console.error('Failed to load users:', error)
      toast.error(`Failed to load users`, {
        description: error instanceof Error ? error.message : 'Network error occurred',
        duration: 5000,
      })
    }
  }

  const loadGames = async () => {
    // Mock data for now - you can implement real game session tracking
    setGames([
      {
        id: 'game-1',
        gameMode: 'classic',
        status: 'active',
        playerCount: 3,
        startTime: new Date(Date.now() - 300000).toISOString(),
        host: 'Player1'
      },
      {
        id: 'game-2',
        gameMode: 'decade-challenge',
        status: 'finished',
        playerCount: 2,
        startTime: new Date(Date.now() - 900000).toISOString(),
        host: 'Player2'
      }
    ])
  }

  const loadLibraryStats = async () => {
    try {
      console.log('Loading library stats...')

      // Try the new dedicated stats API first
      try {
        const statsResponse = await fetch('/api/library/stats')
        if (statsResponse.ok) {
          const statsData = await statsResponse.json()
          if (statsData.success) {
            console.log(`Loaded stats via API: ${statsData.stats.totalTracks} tracks`)
            setLibraryStats(statsData.stats)
            return
          }
        }
      } catch (statsError) {
        console.log('Stats API failed, falling back to tracks API:', statsError)
      }

      // Fallback: Fetch tracks for stats
      const countResponse = await fetch('/api/quiz/tracks?limit=1')
      if (!countResponse.ok) {
        throw new Error('Failed to get track count')
      }

      const countData = await countResponse.json()
      const totalTracks = countData.pagination?.total || 0

      setLibraryStats({
        totalTracks: totalTracks,
        totalArtists: 0,
        totalAlbums: 0,
        genres: {},
        decades: {},
        recentlyAdded: 0
      })
    } catch (error) {
      console.error('Failed to load library stats:', error)
      setLibraryStats({
        totalTracks: 0,
        totalArtists: 0,
        totalAlbums: 0,
        genres: {},
        decades: {},
        recentlyAdded: 0
      })
    }
  }

  const handleRoleChange = async (userId: string, newRole: string) => {
    setUpdatingUserId(userId)
    setRoleChangeStatus(prev => {
      const newStatus = {...prev}
      delete newStatus[userId]
      return newStatus
    })
    
    try {
      console.log(`Updating user ${userId} role to ${newRole}`)
      
      // Get auth token
      const { TokenManager } = await import('@/lib/auth-service')
      const token = TokenManager.getToken()
      
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch('/api/admin/roles-debug', {
        method: 'PATCH',
        headers,
        credentials: 'include',
        body: JSON.stringify({ userId, role: newRole })
      })
      
      const data = await response.json()
      
      if (response.ok && data.success) {
        console.log('Role updated successfully:', data.message)
        
        setPendingRoles(prev => {
          const newPending = {...prev}
          delete newPending[userId]
          return newPending
        })
        
        setRoleChangeStatus(prev => ({...prev, [userId]: 'success'}))
        
        toast.success('Role updated successfully', {
          description: `User role changed to ${newRole}`,
          duration: 3000,
        })
        
        loadUsers()
        
        setTimeout(() => {
          setRoleChangeStatus(prev => {
            const newStatus = {...prev}
            delete newStatus[userId]
            return newStatus
          })
        }, 3000)
        
        try {
          if (typeof window !== 'undefined' && (window as any).refreshJukeboxUserRole) {
            await (window as any).refreshJukeboxUserRole()
          }
        } catch (refreshError) {
          console.log('Jukebox role refresh not available:', refreshError)
        }
      } else {
        console.error('Failed to update role:', data.message)
        setRoleChangeStatus(prev => ({...prev, [userId]: 'error'}))
        
        toast.error('Failed to update role', {
          description: data.message || 'Server error occurred',
          duration: 5000,
        })
        
        setTimeout(() => {
          setRoleChangeStatus(prev => {
            const newStatus = {...prev}
            delete newStatus[userId]
            return newStatus
          })
        }, 5000)
      }
    } catch (error: unknown) {
      console.error('Failed to update role:', error instanceof Error ? error.message : 'Unknown error')
      setRoleChangeStatus(prev => ({...prev, [userId]: 'error'}))
      
      toast.error('Failed to update role', {
        description: error instanceof Error ? error.message : 'Network error occurred',
        duration: 5000,
      })
      
      setTimeout(() => {
        setRoleChangeStatus(prev => {
          const newStatus = {...prev}
          delete newStatus[userId]
          return newStatus
        })
      }, 5000)
    } finally {
      setUpdatingUserId(null)
    }
  }

  const handleDeleteUser = async (userId: string) => {
    try {
      // Get auth token
      const { TokenManager } = await import('@/lib/auth-service')
      const token = TokenManager.getToken()
      
      const headers: HeadersInit = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch(`/api/admin/roles-debug?userId=${userId}`, {
        method: 'DELETE',
        headers,
        credentials: 'include'
      })
      
      if (response.ok) {
        toast.success('User deleted successfully')
        loadUsers() // Refresh users
      } else {
        const errorText = await response.text()
        toast.error('Failed to delete user', {
          description: errorText || 'Server error occurred',
          duration: 5000,
        })
      }
    } catch (error) {
      toast.error('Failed to delete user', {
        description: error instanceof Error ? error.message : 'Network error occurred',
        duration: 5000,
      })
    }
  }

  const handleDeleteGame = async (gameId: string) => {
    try {
      setGames(games.filter(g => g.id !== gameId))
      toast.success('Game session deleted')
    } catch (error) {
      toast.error('Failed to delete game', {
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        duration: 5000,
      })
    }
  }

  const updateCacheStats = () => {
    const stats = getJukeboxCacheStats()
    setCacheStats(stats)
  }

  const handleClearCache = () => {
    clearJukeboxCache()
    updateCacheStats()
  }

  const handleSettingsChange = (key: keyof typeof jukeboxSettings, value: boolean) => {
    const newSettings = { ...jukeboxSettings, [key]: value }
    saveSettings(newSettings)
  }

  const handleResetSettings = () => {
    const defaultSettings = {
      hideChartSongs: false,
      hideMyItunes: false
    }
    saveSettings(defaultSettings)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'offline': return <XCircle className="h-4 w-4 text-red-500" />
      case 'error': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default: return <XCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'offline': return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'error': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const filteredUsers = users.filter(user => 
    user.displayName.toLowerCase().includes(userFilter.toLowerCase()) ||
    user.email?.toLowerCase().includes(userFilter.toLowerCase())
  )

  if (loading) {
    return <DashboardSkeleton />
  }

  // Mobile navigation items
  const mobileNavItems = [
    { value: 'overview', label: 'Overview', icon: Home },
    { value: 'servers', label: 'Servers', icon: Server },
    { value: 'users', label: 'Users', icon: Users },
    { value: 'games', label: 'Games', icon: Activity },
    { value: 'library', label: 'Library', icon: Music },
    { value: 'settings', label: 'Settings', icon: Settings }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68] text-gray-900 dark:text-white">
      <div className="container mx-auto px-4 py-4 sm:py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2 sm:gap-4">
            <Link href="/">
              <Button variant="outline" size="sm" className="border-gray-600 dark:border-gray-400">
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden sm:inline ml-2">Back to Home</span>
              </Button>
            </Link>
            <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
              Manager
            </h1>
          </div>
          <Button onClick={loadDashboardData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4" />
            <span className="hidden sm:inline ml-2">Refresh</span>
          </Button>
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          {/* Desktop Tab List */}
          <TabsList className="hidden lg:grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="servers">Servers</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="games">Games</TabsTrigger>
            <TabsTrigger value="library">Library</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Mobile Tab Navigation */}
          <div className="lg:hidden">
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" className="w-full">
                  <Menu className="h-4 w-4 mr-2" />
                  Dashboard Menu
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[250px] sm:w-[300px]">
                <SheetHeader>
                  <SheetTitle>Navigation</SheetTitle>
                  <SheetDescription>Choose a section to manage</SheetDescription>
                </SheetHeader>
                <div className="mt-6">
                  <TabsList className="flex flex-col h-auto">
                    {mobileNavItems.map((item) => (
                      <TabsTrigger
                        key={item.value}
                        value={item.value}
                        className="w-full justify-start"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <item.icon className="h-4 w-4 mr-2" />
                        {item.label}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>
              </SheetContent>
            </Sheet>
          </div>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            {/* Quick Access Section */}
            <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Quick Access</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  <Link href="/playlist-manager">
                    <Card className="cursor-pointer hover:bg-white/10 dark:hover:bg-white/10 transition-colors">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <Music className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500 flex-shrink-0" />
                          <div className="min-w-0">
                            <h3 className="font-medium text-sm sm:text-base">Playlist Manager</h3>
                            <p className="text-xs text-gray-600 dark:text-gray-400 hidden sm:block">
                              Create and manage playlists
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                  
                  <Link href="/jukebox">
                    <Card className="cursor-pointer hover:bg-white/10 dark:hover:bg-white/10 transition-colors">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <Play className="h-6 w-6 sm:h-8 sm:w-8 text-green-500 flex-shrink-0" />
                          <div className="min-w-0">
                            <h3 className="font-medium text-sm sm:text-base">Jukebox</h3>
                            <p className="text-xs text-gray-600 dark:text-gray-400 hidden sm:block">
                              Manage music queue
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                  
                  <Link href="/admin/roles">
                    <Card className="cursor-pointer hover:bg-white/10 dark:hover:bg-white/10 transition-colors">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <Shield className="h-6 w-6 sm:h-8 sm:w-8 text-purple-500 flex-shrink-0" />
                          <div className="min-w-0">
                            <h3 className="font-medium text-sm sm:text-base">User Roles</h3>
                            <p className="text-xs text-gray-600 dark:text-gray-400 hidden sm:block">
                              Manage permissions
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
              <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 sm:p-6">
                  <CardTitle className="text-xs sm:text-sm font-medium">Total Users</CardTitle>
                  <Users className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent className="p-3 sm:p-6 pt-0 sm:pt-0">
                  <div className="text-lg sm:text-2xl font-bold">{users.length}</div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 hidden sm:block">
                    Active users
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 sm:p-6">
                  <CardTitle className="text-xs sm:text-sm font-medium">Active Games</CardTitle>
                  <Play className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent className="p-3 sm:p-6 pt-0 sm:pt-0">
                  <div className="text-lg sm:text-2xl font-bold">
                    {games.filter(g => g.status === 'active').length}
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 hidden sm:block">
                    Running now
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 sm:p-6">
                  <CardTitle className="text-xs sm:text-sm font-medium">Music Library</CardTitle>
                  <Music className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent className="p-3 sm:p-6 pt-0 sm:pt-0">
                  <div className="text-lg sm:text-2xl font-bold">{libraryStats.totalTracks}</div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 hidden sm:block">
                    Total tracks
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 sm:p-6">
                  <CardTitle className="text-xs sm:text-sm font-medium">Server Status</CardTitle>
                  <Server className="h-4 w-4 text-orange-500" />
                </CardHeader>
                <CardContent className="p-3 sm:p-6 pt-0 sm:pt-0">
                  <div className="text-lg sm:text-2xl font-bold">
                    {serverStatuses.filter(s => s.status === 'online').length}/{serverStatuses.length}
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 hidden sm:block">
                    Services online
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Server Status Overview */}
            <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Server Status Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {serverStatuses.map((server) => (
                    <div key={server.name} className="flex items-center justify-between p-3 rounded-lg bg-white/5 dark:bg-white/5">
                      <div className="flex items-center gap-2 sm:gap-3 min-w-0">
                        {getStatusIcon(server.status)}
                        <div className="min-w-0">
                          <p className="font-medium text-sm sm:text-base truncate">{server.name}</p>
                          <p className="text-xs text-gray-600 dark:text-gray-400">Port {server.port}</p>
                        </div>
                      </div>
                      <Badge className={`${getStatusColor(server.status)} text-xs`}>
                        {server.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Servers Tab */}
          <TabsContent value="servers" className="space-y-4">
            <div className="grid gap-4">
              {serverStatuses.map((server) => (
                <Card key={server.name} className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
                  <CardHeader>
                    <div className="flex flex-col gap-4">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(server.status)}
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-lg sm:text-xl">{server.name}</CardTitle>
                          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                            Port {server.port} • Last: {server.lastCheck}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={checkServerStatuses}
                          className="flex-1 sm:flex-none"
                        >
                          <RefreshCw className="h-4 w-4 mr-1 sm:mr-2" />
                          Check
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          disabled={server.status === 'online' || managingServer === `start-${server.name}`}
                          onClick={() => manageServer('start', server.name)}
                          className="flex-1 sm:flex-none"
                        >
                          {managingServer === `start-${server.name}` ? (
                            <Loader2 className="h-4 w-4 mr-1 sm:mr-2 animate-spin" />
                          ) : (
                            <Play className="h-4 w-4 mr-1 sm:mr-2" />
                          )}
                          Start
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          disabled={server.status === 'offline' || managingServer === `stop-${server.name}`}
                          onClick={() => manageServer('stop', server.name)}
                          className="flex-1 sm:flex-none"
                        >
                          {managingServer === `stop-${server.name}` ? (
                            <Loader2 className="h-4 w-4 mr-1 sm:mr-2 animate-spin" />
                          ) : (
                            <Square className="h-4 w-4 mr-1 sm:mr-2" />
                          )}
                          Stop
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          disabled={managingServer === `restart-${server.name}`}
                          onClick={() => manageServer('restart', server.name)}
                          className="flex-1 sm:flex-none"
                        >
                          {managingServer === `restart-${server.name}` ? (
                            <Loader2 className="h-4 w-4 mr-1 sm:mr-2 animate-spin" />
                          ) : (
                            <RotateCcw className="h-4 w-4 mr-1 sm:mr-2" />
                          )}
                          Restart
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-4">
                      <Badge className={getStatusColor(server.status)}>
                        {server.status}
                      </Badge>
                      {server.uptime && (
                        <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          Uptime: {server.uptime}
                        </span>
                      )}
                      {server.details && (
                        <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          {server.details}
                        </span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-4">
            <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <CardTitle className="text-lg sm:text-xl">User Management</CardTitle>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Search users..."
                      value={userFilter}
                      onChange={(e) => setUserFilter(e.target.value)}
                      className="w-full sm:w-64"
                    />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredUsers.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Users className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>No users found</p>
                      <div className="flex gap-2 justify-center mt-2">
                        <Button onClick={loadUsers} variant="outline" size="sm">
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Reload Users
                        </Button>
                        <Button 
                          onClick={async () => {
                            try {
                              const { TokenManager } = await import('@/lib/auth-service')
                              const token = TokenManager.getToken()
                              const response = await fetch('/api/test-auth', {
                                headers: token ? { 'Authorization': `Bearer ${token}` } : {},
                                credentials: 'include'
                              })
                              const data = await response.json()
                              console.log('Auth test result:', data)
                              if (data.success) {
                                toast.success('Authentication working', {
                                  description: `Logged in as: ${data.user.email} (${data.user.role})`
                                })
                              } else {
                                toast.error('Authentication failed', {
                                  description: data.message
                                })
                              }
                            } catch (error) {
                              console.error('Auth test failed:', error)
                              toast.error('Auth test failed', {
                                description: error instanceof Error ? error.message : 'Unknown error'
                              })
                            }
                          }}
                          variant="outline" 
                          size="sm"
                        >
                          Test Auth
                        </Button>
                      </div>
                    </div>
                  ) : (
                    filteredUsers.map((user) => (
                      <div key={user.id} className="flex flex-col gap-3 p-4 rounded-lg bg-white/5 dark:bg-white/5">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold flex-shrink-0">
                            {user.displayName.charAt(0).toUpperCase()}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{user.displayName}</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                              {user.email || 'No email'} • {user.gamesPlayed} games
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-500">
                              Last: {user.lastActive}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-wrap items-center gap-2">
                          <div className="relative flex-1 sm:flex-none">
                            <Select 
                              value={pendingRoles[user.id] || user.role} 
                              onValueChange={(value) => setPendingRoles(prev => ({...prev, [user.id]: value}))}
                              disabled={updatingUserId === user.id}
                            >
                              <SelectTrigger className="w-full sm:w-32">
                                <SelectValue />
                                {updatingUserId === user.id && (
                                  <Loader2 className="h-3 w-3 animate-spin ml-2" />
                                )}
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="user">User</SelectItem>
                                <SelectItem value="dj">DJ</SelectItem>
                                <SelectItem value="superuser">Superuser</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          
                          {/* Save Button - Shows only when role is changed */}
                          {pendingRoles[user.id] && pendingRoles[user.id] !== user.role && (
                            <div className="flex items-center gap-1">
                              <Button 
                                size="sm" 
                                onClick={() => handleRoleChange(user.id, pendingRoles[user.id])}
                                disabled={updatingUserId === user.id}
                                className="bg-green-600 hover:bg-green-700 text-white"
                              >
                                {updatingUserId === user.id ? (
                                  <Loader2 className="h-3 w-3 animate-spin" />
                                ) : (
                                  <CheckCircle className="h-3 w-3" />
                                )}
                                Save
                              </Button>
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => setPendingRoles(prev => {
                                  const newPending = {...prev}
                                  delete newPending[user.id]
                                  return newPending
                                })}
                                disabled={updatingUserId === user.id}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                          
                          {/* Status Badge */}
                          {roleChangeStatus[user.id] && (
                            <Badge className={roleChangeStatus[user.id] === 'success' ? 
                              "bg-green-100 text-green-800 border-green-200" : 
                              "bg-red-100 text-red-800 border-red-200"}>
                              {roleChangeStatus[user.id] === 'success' ? (
                                <>
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Updated!
                                </>
                              ) : (
                                <>
                                  <X className="h-3 w-3 mr-1" />
                                  Failed
                                </>
                              )}
                            </Badge>
                          )}
                          
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete User</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete {user.displayName}? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDeleteUser(user.id)}>
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Games Tab */}
          <TabsContent value="games" className="space-y-4">
            <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Active Game Sessions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {games.map((game) => (
                    <div key={game.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-4 rounded-lg bg-white/5 dark:bg-white/5">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge className={game.status === 'active' ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'}>
                            {game.status}
                          </Badge>
                          <span className="font-medium text-sm sm:text-base">{game.gameMode}</span>
                        </div>
                        <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          Host: {game.host} • {game.playerCount} players
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500">
                          Started: {new Date(game.startTime).toLocaleString()}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        {game.status === 'active' && (
                          <Button variant="outline" size="sm">
                            <Pause className="h-4 w-4 mr-1 sm:mr-2" />
                            End
                          </Button>
                        )}
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Game Session</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete this game session? This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDeleteGame(game.id)}>
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                  ))}
                  {games.length === 0 && (
                    <p className="text-center text-gray-600 dark:text-gray-400 py-8">
                      No game sessions found
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Library Tab */}
          <TabsContent value="library" className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                    <Music className="h-5 w-5" />
                    Library Overview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Total Tracks:</span>
                    <span className="font-bold">{libraryStats.totalTracks.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Artists:</span>
                    <span className="font-bold">{libraryStats.totalArtists.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Albums:</span>
                    <span className="font-bold">{libraryStats.totalAlbums.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Recently Added:</span>
                    <span className="font-bold">{libraryStats.recentlyAdded}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                    <BarChart3 className="h-5 w-5" />
                    Top Genres
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(libraryStats.genres)
                      .sort(([,a], [,b]) => b - a)
                      .slice(0, 5)
                      .map(([genre, count]) => (
                        <div key={genre} className="flex justify-between items-center text-sm">
                          <span className="truncate mr-2">{genre}</span>
                          <Badge variant="outline" className="text-xs">{count}</Badge>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                    <Clock className="h-5 w-5" />
                    By Decade
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(libraryStats.decades).length === 0 ? (
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        No decade data available
                      </div>
                    ) : (
                      Object.entries(libraryStats.decades)
                        .sort(([,a], [,b]) => b - a)
                        .slice(0, 5)
                        .map(([decade, count]) => (
                          <div key={decade} className="flex justify-between items-center text-sm">
                            <span>{decade}</span>
                            <Badge variant="outline" className="text-xs">{count}</Badge>
                          </div>
                        ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-4">
            <Card className="bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-gray-200/30 dark:border-gray-700/30">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                  <Settings className="h-5 w-5" />
                  Jukebox Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <Card className="bg-white/5 dark:bg-white/5 border border-gray-200/20 dark:border-gray-700/20">
                      <CardHeader>
                        <CardTitle className="text-base sm:text-lg">Content Filtering</CardTitle>
                        <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          Control what content is visible to users
                        </p>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 mr-4">
                            <Label htmlFor="hideChartSongs" className="text-sm font-medium">
                              Hide Chart Songs
                            </Label>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              Hide songs from music charts
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            id="hideChartSongs"
                            checked={jukeboxSettings.hideChartSongs}
                            onChange={(e) => handleSettingsChange('hideChartSongs', e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex-1 mr-4">
                            <Label htmlFor="hideMyItunes" className="text-sm font-medium">
                              Hide My iTunes
                            </Label>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              Hide iTunes personal library
                            </p>
                          </div>
                          <input
                            type="checkbox"
                            id="hideMyItunes"
                            checked={jukeboxSettings.hideMyItunes}
                            onChange={(e) => handleSettingsChange('hideMyItunes', e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card className="bg-white/5 dark:bg-white/5 border border-gray-200/20 dark:border-gray-700/20">
                      <CardHeader>
                        <CardTitle className="text-base sm:text-lg">Cache Management</CardTitle>
                        <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          Manage cached data and performance
                        </p>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-sm font-medium">
                              Library Cache Status
                            </Label>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              Current cache usage
                            </p>
                          </div>
                          <Badge variant="outline" className="text-green-600">
                            Active
                          </Badge>
                        </div>
                        
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="flex-1"
                            onClick={updateCacheStats}
                          >
                            <RefreshCw className="h-4 w-4 mr-1 sm:mr-2" />
                            Refresh
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="flex-1"
                            onClick={handleClearCache}
                          >
                            <Trash2 className="h-4 w-4 mr-1 sm:mr-2" />
                            Clear
                          </Button>
                        </div>
                        
                        <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                          <p>• Library: {cacheStats.library} entries</p>
                          <p>• Categories: {cacheStats.categories} entries</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {/* Audio Output Switcher */}
                    <AudioOutputSwitcher />
                    
                    {/* Audio Settings */}
                    <AudioSettings />
                  </div>
                  
                  {/* Speaker Output Switcher - Full width */}
                  <div className="col-span-1 lg:col-span-2">
                    <SpeakerOutputSwitcher />
                  </div>
                  
                  {/* Bluetooth Settings - Full width */}
                  <div className="col-span-1 lg:col-span-2">
                    <BluetoothSettings />
                  </div>
                  
                  {/* Content Filters - Full width on mobile, half width on desktop */}
                  <div className="col-span-1 lg:col-span-2">
                    <ContentFilterSettings />
                  </div>
                  
                  {/* Auto Queue Settings - Full width */}
                  <div className="col-span-1 lg:col-span-2">
                    <AutoQueueSettings />
                  </div>
                  
                  <div className="flex justify-end gap-2 pt-4 border-t border-gray-200/20 dark:border-gray-700/20 col-span-1 lg:col-span-2">
                    <Button variant="outline" size="sm" onClick={handleResetSettings}>
                      Reset to Defaults
                    </Button>
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white" size="sm">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Auto-Saved
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}