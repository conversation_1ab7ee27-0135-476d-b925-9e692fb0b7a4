"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Palette, Download, Upload, RotateCcw } from "lucide-react"
import { useTheme } from "next-themes"
import { PageHeader } from "@/components/ui/page-header"

const colorSchemes = [
  { name: "Dark", value: "dark", color: "hsl(222.2, 84%, 4.9%)" },
  { name: "Light", value: "light", color: "hsl(0, 0%, 100%)" },
  { name: "Blue", value: "blue", color: "hsl(221.2, 83.2%, 53.3%)" },
  { name: "Green", value: "green", color: "hsl(142.1, 76.2%, 36.3%)" },
  { name: "Orange", value: "orange", color: "hsl(24.6, 95%, 53.1%)" },
  { name: "Red", value: "red", color: "hsl(0, 72.2%, 50.6%)" },
  { name: "Rose", value: "rose", color: "hsl(346.8, 77.2%, 49.8%)" },
  { name: "Slate", value: "slate", color: "hsl(215, 27.9%, 16.9%)" },
  { name: "Stone", value: "stone", color: "hsl(28, 4%, 16%)" },
  { name: "Violet", value: "violet", color: "hsl(262.1, 83.3%, 57.8%)" },
  { name: "Zinc", value: "zinc", color: "hsl(240, 3.7%, 15.9%)" },
  { name: "ulTimote", value: "ulTimote", color: "hsl(58, 100%, 68%)" },
]

const sampleComponents = [
  { name: "Primary Button", component: <Button className="w-full">Primary Button</Button> },
  { name: "Secondary Button", component: <Button variant="secondary" className="w-full">Secondary Button</Button> },
  { name: "Outline Button", component: <Button variant="outline" className="w-full">Outline Button</Button> },
  { name: "Destructive Button", component: <Button variant="destructive" className="w-full">Destructive Button</Button> },
  { name: "Input Field", component: <Input placeholder="Type something..." /> },
  { name: "Badge", component: <Badge>Sample Badge</Badge> },
]

export default function ThemeExplorerPage() {
  const { theme, setTheme } = useTheme()
  const [selectedScheme, setSelectedScheme] = useState(theme || "dark")

  const handleThemeChange = (newTheme: string) => {
    setSelectedScheme(newTheme)
    setTheme(newTheme)
  }

  const exportTheme = () => {
    const themeConfig = {
      name: selectedScheme,
      timestamp: new Date().toISOString(),
      scheme: selectedScheme
    }
    
    const blob = new Blob([JSON.stringify(themeConfig, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `theme-${selectedScheme}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  const resetTheme = () => {
    setTheme("dark")
    setSelectedScheme("dark")
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-6 sm:py-8 px-4 max-w-6xl">
        <PageHeader 
          title="Theme Explorer" 
          backLabel="Home"
          titleClassName="text-foreground"
        />
        <div className="mb-6">
          <p className="text-muted-foreground flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Customize your Music Quiz experience with different color schemes
          </p>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={resetTheme}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button variant="outline" size="sm" onClick={exportTheme}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Theme Selection */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Color Schemes</CardTitle>
                <CardDescription>
                  Choose from our pre-built color schemes
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {colorSchemes.map((scheme) => (
                  <motion.div
                    key={scheme.value}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      variant={selectedScheme === scheme.value ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => handleThemeChange(scheme.value)}
                    >
                      <div
                        className="w-4 h-4 rounded-full mr-3 border border-border"
                        style={{ backgroundColor: scheme.color }}
                      />
                      {scheme.name}
                      {selectedScheme === scheme.value && (
                        <Badge variant="secondary" className="ml-auto">
                          Active
                        </Badge>
                      )}
                    </Button>
                  </motion.div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Preview Area */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="components" className="space-y-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="components">Components</TabsTrigger>
                <TabsTrigger value="playground">Playground</TabsTrigger>
              </TabsList>

              <TabsContent value="components" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Component Preview</CardTitle>
                    <CardDescription>
                      See how UI components look with the selected theme
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {sampleComponents.map((comp, index) => (
                        <motion.div
                          key={comp.name}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="space-y-2"
                        >
                          <Label className="text-sm font-medium">{comp.name}</Label>
                          {comp.component}
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Color Tokens</CardTitle>
                    <CardDescription>
                      Current theme color values
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {[
                        { name: "Background", var: "--background", class: "bg-background" },
                        { name: "Foreground", var: "--foreground", class: "bg-foreground" },
                        { name: "Primary", var: "--primary", class: "bg-primary" },
                        { name: "Secondary", var: "--secondary", class: "bg-secondary" },
                        { name: "Muted", var: "--muted", class: "bg-muted" },
                        { name: "Accent", var: "--accent", class: "bg-accent" },
                        { name: "Destructive", var: "--destructive", class: "bg-destructive" },
                        { name: "Border", var: "--border", class: "bg-border" },
                      ].map((color) => (
                        <div key={color.name} className="space-y-2">
                          <div className={`w-full h-12 rounded-md border ${color.class}`} />
                          <div className="space-y-1">
                            <p className="text-xs font-medium">{color.name}</p>
                            <p className="text-xs text-muted-foreground font-mono">{color.var}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="playground" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Interactive Playground</CardTitle>
                    <CardDescription>
                      Test how your theme looks with real UI elements
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Sample Quiz Interface */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Sample Quiz Interface</h3>
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-xl">Which artist performed this song?</CardTitle>
                          <CardDescription>Listen to the audio and choose the correct answer</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {["The Beatles", "Led Zeppelin", "Pink Floyd", "Queen"].map((option, idx) => (
                              <Button key={idx} variant="outline" className="justify-start">
                                {String.fromCharCode(65 + idx)}. {option}
                              </Button>
                            ))}
                          </div>
                          <div className="flex items-center justify-between pt-4 border-t">
                            <Badge variant="secondary">Question 3 of 10</Badge>
                            <div className="flex gap-2">
                              <Button size="sm">Skip</Button>
                              <Button size="sm">Submit</Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Sample Jukebox Interface */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Sample Jukebox Interface</h3>
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-xl">Now Playing</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="flex items-center gap-4">
                            <div className="w-16 h-16 bg-muted rounded-md"></div>
                            <div className="flex-1">
                              <h4 className="font-medium">Bohemian Rhapsody</h4>
                              <p className="text-sm text-muted-foreground">Queen • A Night at the Opera</p>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>2:45</span>
                              <span>5:55</span>
                            </div>
                            <div className="w-full bg-secondary rounded-full h-2">
                              <div className="bg-primary h-2 rounded-full" style={{ width: "45%" }}></div>
                            </div>
                          </div>
                          <div className="flex justify-center gap-2">
                            <Button size="sm" variant="outline">Previous</Button>
                            <Button size="sm">Pause</Button>
                            <Button size="sm" variant="outline">Next</Button>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}