"use client"

import { <PERSON>Header } from "@/components/ui/page-header"
import { UltimoteConfigTester } from "@/components/ultimote-config-tester"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle } from "lucide-react"

export default function TestUltimatePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68] text-gray-900 dark:text-white">
      <div className="container mx-auto px-4 py-6 sm:py-8">
        <PageHeader title="ulTimote Configuration Testing" backLabel="Home" />
        
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Testing Instructions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <p>This tool tests different ulTimote configurations to ensure:</p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Configurations are properly saved to the server</li>
                  <li>Games start with only the configured categories</li>
                  <li>Question counts match expected values</li>
                  <li>Audio tracks are present/absent as expected</li>
                  <li>Category distribution is correct</li>
                </ul>
                <p className="mt-4 text-gray-600 dark:text-gray-400">
                  Make sure the development server is running before starting tests.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Test Component */}
          <UltimoteConfigTester />
          
          {/* Command Line Alternative */}
          <Card>
            <CardHeader>
              <CardTitle>Command Line Testing</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm mb-4">
                You can also run tests from the command line:
              </p>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm">
                <p className="text-green-400"># Run all configuration tests</p>
                <p>node test-ultimote-configurations.js</p>
                <p className="mt-2 text-green-400"># Run automated test suite</p>
                <p>./test-ultimote-automated.js</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}