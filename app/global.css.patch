/* Add these mobile-specific styles to app/globals.css */

/* Prevent iOS bounce effect */
html,
body {
  overscroll-behavior: none;
}

/* Improve touch responsiveness */
.touch-manipulation {
  touch-action: manipulation;
}

/* Ensure buttons are easily tappable on mobile */
button,
[role="button"] {
  min-height: 44px;
  min-width: 44px;
}

/* Prevent text selection on interactive elements */
button,
a,
[role="button"] {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Improve input focus on mobile */
input:focus,
textarea:focus,
select:focus {
  font-size: 16px; /* Prevents zoom on iOS */
}

/* Better scrolling on mobile */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
}

/* Prevent double-tap zoom on buttons */
button {
  touch-action: manipulation;
}

@media (max-width: 640px) {
  /* Ensure modals/cards don't go off-screen */
  .max-w-md {
    max-width: calc(100vw - 2rem);
  }
  
  .max-w-4xl {
    max-width: calc(100vw - 2rem);
  }
  
  /* Better spacing for mobile */
  .space-y-6 > * + * {
    margin-top: 1.5rem;
  }
}

/* Prevent horizontal scroll on mobile */
@media (max-width: 768px) {
  body {
    overflow-x: hidden;
  }
}