"use client"

import { MusicSearch } from "@/components/music-search"
import ErrorBoundary from "@/components/error-boundary"
import { PageHeader } from "@/components/ui/page-header"

export default function MusicSearchPage() {
  return (
    <ErrorBoundary
      fallback={(error) => (
        <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-6">
          <div className="max-w-md w-full bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <h2 className="text-2xl font-bold text-white mb-4">🔍 Search Error</h2>
            <p className="text-white/80 mb-4">
              Something went wrong with the music search. This might be due to:
            </p>
            <ul className="text-left text-white/70 text-sm mb-6 space-y-1">
              <li>• YouTube API connectivity issues</li>
              <li>• Network connection problems</li>
              <li>• Download service errors</li>
            </ul>
            <button 
              onClick={() => window.location.reload()}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Retry Search
            </button>
          </div>
        </div>
      )}
    >
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="container mx-auto px-4 py-6 sm:py-8 max-w-6xl">
          <PageHeader 
            title="Music Search & Download" 
            backLabel="Home"
            titleClassName="text-white"
          />
          <div className="mb-6">
            <p className="text-gray-300">
              Search YouTube for music tracks, download them with automatic tagging, and add them to your library
            </p>
          </div>
          
          <MusicSearch />
        </div>
      </div>
    </ErrorBoundary>
  )
} 