'use client'

import { useEffect } from 'react'
import Link from 'next/link'

export const dynamic = 'force-dynamic'

export default function Error({ 
  error, 
  reset, 
}: { 
  error: Error & { digest?: string }, 
  reset: () => void 
}) {
  useEffect(() => {
    console.error(error)
  }, [error])

  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={() => reset()}>Try again</button>
      <Link href="/">Go back home</Link>
    </div>
  )
}
