'use client'

import { useState } from 'react'
import { OnboardingFlow, UserProfile } from '@/components/onboarding-flow'
import { Button } from '@/components/ui/button'

export const dynamic = 'force-dynamic'

export default function TestOnboardingPage() {
  const [showOnboarding, setShowOnboarding] = useState(true)
  const [completedProfile, setCompletedProfile] = useState<UserProfile | null>(null)

  const handleComplete = (profile: UserProfile) => {
    setCompletedProfile(profile)
    setShowOnboarding(false)
    console.log('Onboarding completed:', profile)
  }

  const handleSkip = async () => {
    setShowOnboarding(false)
    console.log('Onboarding skipped')
  }

  const resetTest = () => {
    setShowOnboarding(true)
    setCompletedProfile(null)
  }

  if (showOnboarding) {
    return (
      <OnboardingFlow 
        onComplete={handleComplete}
        onSkip={handleSkip}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-100 to-blue-100 p-4 flex items-center justify-center">
      <div className="max-w-md mx-auto text-center space-y-6">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h1 className="text-2xl font-bold text-green-600 mb-4">
            ✅ Onboarding Test Complete!
          </h1>
          
          {completedProfile ? (
            <div className="text-left space-y-2 mb-4">
              <p><strong>Username:</strong> {completedProfile.username}</p>
              <p><strong>Display Name:</strong> {completedProfile.displayName}</p>
              <p><strong>Avatar:</strong> {completedProfile.avatar}</p>
              <p><strong>Favorite Genres:</strong> {completedProfile.favoriteGenres.join(', ')}</p>
              <p><strong>Theme:</strong> {completedProfile.preferences.theme}</p>
              <p><strong>Difficulty:</strong> {completedProfile.preferences.difficulty}/5</p>
            </div>
          ) : (
            <p className="text-gray-600 mb-4">Onboarding was skipped</p>
          )}
          
          <Button onClick={resetTest} className="w-full">
            Test Again
          </Button>
        </div>
        
        <div className="text-sm text-gray-600">
          <p>This page tests the onboarding flow on mobile devices.</p>
          <p>Check that scrolling works properly on all steps.</p>
        </div>
      </div>
    </div>
  )
}
