import type { Metada<PERSON>, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { ErrorBoundary } from "@/components/error-boundary"
import { MobileErrorBoundary } from "@/components/mobile-error-boundary"
import { PerformanceWidget } from "@/components/performance-widget"
import { UserProvider } from "@/lib/user-context"
import { ErrorMonitor } from "@/components/error-monitor"
import { GlobalErrorHandler } from "@/components/global-error-handler"
import { Toaster } from "@/components/ui/sonner"
import { NetworkStatus } from "@/components/network-status"

// Force dynamic rendering for the entire app
export const dynamic = 'force-dynamic'
export const dynamicParams = true
export const revalidate = 0

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "UlTimote Music Quiz",
  description: "The ultimate open-source remote music & quiz experience",
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ErrorBoundary>
          <MobileErrorBoundary>
            <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={true}>
              <UserProvider>
                {children}
                <PerformanceWidget />
                <ErrorMonitor />
                <GlobalErrorHandler />
                <NetworkStatus />
                <Toaster />
              </UserProvider>
            </ThemeProvider>
          </MobileErrorBoundary>
        </ErrorBoundary>
      </body>
    </html>
  )
}
