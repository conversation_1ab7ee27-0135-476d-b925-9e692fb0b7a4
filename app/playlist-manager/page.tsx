'use client'

import { UnifiedPlaylistManager } from '@/components/unified-playlist-manager'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Loader2, Shield, Music, ArrowLeft, RefreshCw } from 'lucide-react'
import { useUser } from '@/lib/user-context'
import Link from 'next/link'

export const dynamic = 'force-dynamic'

export default function PlaylistManagerPage() {
  const { user, isAuthenticated, isLoading, refreshUser, isDJOrAdmin, isAdmin, isDJ } = useUser()

  const handleRefreshRole = async () => {
    console.log('🔄 Refreshing user role...')
    await refreshUser()
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading playlist manager...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <Shield className="h-5 w-5" />
              Authentication Required
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Please log in to access the playlist manager.
            </p>
            <Link href="/">
              <Button className="w-full">
                Go to Login
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  const canManagePlaylists = isDJOrAdmin()

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
              </Link>
              <Music className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">Playlist Manager</h1>
                <p className="text-muted-foreground">
                  {canManagePlaylists 
                    ? 'Create, edit, and manage music playlists' 
                    : 'View and load playlists'
                  }
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-right">
                <p className="font-medium">{user.displayName}</p>
                <p className="text-sm text-muted-foreground">@{user.username}</p>
              </div>
              <Badge 
                variant={
                  isAdmin() ? 'default' : 
                  isDJ() ? 'secondary' : 'outline'
                }
                className="gap-1"
              >
                <Shield className="h-3 w-3" />
                {user.role.toUpperCase()}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshRole}
                className="gap-1"
              >
                <RefreshCw className="h-3 w-3" />
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="h-[calc(100vh-140px)]">
        <UnifiedPlaylistManager 
          userRole={user.role === 'superuser' ? 'admin' : user.role as any}
          defaultView="grid"
        />
      </div>
    </div>
  )
} 