@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import text overflow utilities */
@import '../styles/text-utilities.css';

/* Import Shepherd.js theme */
@import '../styles/shepherd-theme.css';

/* Mobile accessibility improvements */
@layer utilities {
  /* Safe areas for notched devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  /* Ensure minimum touch target size */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Improve focus indicators for mobile */
  @media (hover: none) and (pointer: coarse) {
    button:focus-visible,
    a:focus-visible {
      outline: 3px solid currentColor;
      outline-offset: 2px;
    }
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
    
    /* Custom loading screen colors */
    --spotify-green: 142 76% 36%;
    --dark-bg: 0 0% 7%;
    --dark-secondary: 0 0% 10%;
    --dark-tertiary: 0 0% 16%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }

  /* Blue Theme */
  .blue {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 221.2 83.2% 53.3%;
  }

  /* Green Theme */
  .green {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142.1 76.2% 36.3%;
  }

  /* Orange Theme */
  .orange {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 24.6 95% 53.1%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 24.6 95% 53.1%;
  }

  /* Red Theme */
  .red {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 0 72.2% 50.6%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 0 72.2% 50.6%;
  }

  /* Rose Theme */
  .rose {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 346.8 77.2% 49.8%;
  }

  /* Slate Theme */
  .slate {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 40% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
  }

  /* Stone Theme */
  .stone {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 28 4% 16%;
    --secondary-foreground: 210 40% 98%;
    --muted: 28 4% 16%;
    --muted-foreground: 24 5.4% 63.9%;
    --accent: 28 4% 16%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 28 4% 16%;
    --input: 28 4% 16%;
    --ring: 25 5.3% 44.7%;
  }

  /* Violet Theme */
  .violet {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262.1 83.3% 57.8%;
  }

  /* Zinc Theme */
  .zinc {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 210 40% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }

  /* ulTimote Theme - Ultimate branding with yellow/purple/blue gradient */
  .ulTimote {
    --background: 261 80% 8%;
    --foreground: 58 100% 98%;
    --card: 261 80% 8%;
    --card-foreground: 58 100% 98%;
    --popover: 261 80% 8%;
    --popover-foreground: 58 100% 98%;
    --primary: 58 100% 68%;
    --primary-foreground: 261 80% 8%;
    --secondary: 254 83% 27%;
    --secondary-foreground: 58 100% 98%;
    --muted: 254 83% 27%;
    --muted-foreground: 58 50% 85%;
    --accent: 221 83% 53%;
    --accent-foreground: 58 100% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 58 100% 98%;
    --border: 254 83% 27%;
    --input: 254 83% 27%;
    --ring: 58 100% 68%;
  }

  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations for loading screen */
@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Loading screen CSS */
@layer utilities {
  /* Flowing image animations */
  @keyframes flowRight {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100vw);
    }
  }

  @keyframes flowLeft {
    0% {
      transform: translateX(100vw);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  /* Loading dots animation */
  @keyframes loadingDots {
    0%, 20% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.7;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Slow pulse animation */
  @keyframes pulse-slow {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  /* Slow spin animation */
  @keyframes spin-slow {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* Flowing row positioning */
  .flowing-row {
    position: absolute;
    display: flex;
    gap: 2rem;
    align-items: center;
    white-space: nowrap;
    will-change: transform;
  }

  /* Loading dot styles */
  .loading-dot {
    animation: loadingDots 1.4s infinite both;
  }

  .loading-dot:nth-child(1) {
    animation-delay: -0.32s;
  }

  .loading-dot:nth-child(2) {
    animation-delay: -0.16s;
  }

  .loading-dot:nth-child(3) {
    animation-delay: 0s;
  }

  /* Custom animation classes */
  .animate-pulse-slow {
    animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }

  /* Opacity utility classes for loading screen */
  .opacity-60 { opacity: 0.6; }
  .opacity-65 { opacity: 0.65; }
  .opacity-70 { opacity: 0.7; }
  .opacity-75 { opacity: 0.75; }
  .opacity-80 { opacity: 0.8; }
  .opacity-85 { opacity: 0.85; }
}

/* Additional custom utilities */
.album-cover {
  transition: all 0.3s ease;
}

.album-cover:hover {
  transform: scale(1.1);
}

.gradient-overlay {
  background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-secondary) 50%, var(--dark-bg) 100%);
}

.animate-fade-in-out {
  animation: fadeInOut 3s ease-in-out infinite;
}

/* Mobile-first responsive improvements */
@media (max-width: 768px) {
  .flowing-row {
    gap: 1rem !important;
  }
  
  .loading-dot {
    width: 0.5rem !important;
    height: 0.5rem !important;
  }
  
  /* Better touch targets for mobile */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Hide complex animations on mobile to improve performance */
  .animate-spin-slow,
  .animate-pulse-slow {
    animation: none;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .animate-spin-slow,
  .animate-pulse-slow,
  .animate-fade-in-out,
  .loading-dot {
    animation: none;
  }
  
  .album-cover {
    transition: none;
  }
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
  .album-cover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .gradient-overlay {
    background: var(--background);
  }
}