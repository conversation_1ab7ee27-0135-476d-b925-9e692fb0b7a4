"use client"

import { useState, useEffect } from "react"
import dynamic from "next/dynamic"
import { <PERSON>, Tv, ArrowLeft, Shield } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PageHeader } from "@/components/ui/page-header"
import type { GameMode } from "@/lib/types"
import { useUser } from "@/lib/user-context"
import { useMultiplayer } from "@/hooks/use-multiplayer"

// Lazy load heavy components
const MultiplayerLobby = dynamic(() => import("@/components/multiplayer-lobby").then(mod => ({ default: mod.MultiplayerLobby })), {
  ssr: false,
  loading: () => <div className="text-center p-8">Loading lobby...</div>
})

const GameBrowser = dynamic(() => import("@/components/game-browser").then(mod => ({ default: mod.GameBrowser })), {
  ssr: false,
  loading: () => <div className="text-center p-8">Loading game browser...</div>
})

const MultiplayerQuiz = dynamic(() => import("@/components/multiplayer-quiz").then(mod => ({ default: mod.MultiplayerQuiz })), {
  ssr: false,
  loading: () => <div className="text-center p-8">Loading quiz...</div>
})

const MultiplayerGameResults = dynamic(() => import("@/components/multiplayer-game-results").then(mod => ({ default: mod.MultiplayerGameResults })), {
  ssr: false,
  loading: () => <div className="text-center p-8">Loading results...</div>
})


type MultiplayerState = "selection" | "browse" | "lobby" | "playing" | "results"
type MultiplayerRole = "host" | "player"

export default function MultiplayerPage() {
  const [multiplayerState, setMultiplayerState] = useState<MultiplayerState>("selection")
  const [role, setRole] = useState<MultiplayerRole | null>(null)
  const [selectedGameMode, setSelectedGameMode] = useState<GameMode>("classic")
  const [gameResults, setGameResults] = useState<any>(null)
  const { isAdmin } = useUser()
  const multiplayerHook = useMultiplayer()
  
  // Debug logging
  useEffect(() => {
    console.log('[MultiplayerPage] State:', multiplayerState, 'Role:', role)
    console.log('[MultiplayerPage] Game state from hook:', multiplayerHook.gameState)
  }, [multiplayerState, role, multiplayerHook.gameState])
  
  // Auto-transition to playing when game starts
  useEffect(() => {
    if (multiplayerHook.gameState?.status === 'playing' && multiplayerState === 'lobby') {
      console.log('[MultiplayerPage] Game started, transitioning to playing state')
      setMultiplayerState('playing')
    }
  }, [multiplayerHook.gameState?.status, multiplayerState])

  const handleRoleSelection = (selectedRole: MultiplayerRole) => {
    setRole(selectedRole)
    if (selectedRole === "host") {
      setMultiplayerState("lobby")
    } else {
      setMultiplayerState("browse")
    }
  }

  const handleBackToSelection = () => {
    setMultiplayerState("selection")
    setRole(null)
    setSelectedGameMode("classic")
  }

  const handleJoinGameFromBrowser = (gameId: string) => {
    localStorage.setItem('joiningGameCode', gameId)
    setMultiplayerState("lobby")
  }

  const handleCreateGameFromBrowser = () => {
    setRole("host")
    setMultiplayerState("lobby")
  }

  const handleGameStart = (gameMode: GameMode, teamModeEnabled = false, maxTeamSize = 4) => {
    console.log('[MultiplayerPage] Game has started on server, transitioning to quiz')
    
    setSelectedGameMode(gameMode)
    setMultiplayerState("playing")
    
    const gameCode = localStorage.getItem('currentGameCode') || ''
    const gameId = localStorage.getItem('currentGameId') || ''
    
    localStorage.setItem('currentGameMode', gameMode)
    localStorage.setItem('currentMultiplayerRole', role || 'player')
    
    console.log('Starting multiplayer game:', { gameMode, role, gameCode, gameId })
  }

  const handleGameComplete = (results: any) => {
    console.log("Game completed with results:", results)
    setGameResults(results)
    setMultiplayerState("results")
  }

  const handleNewGame = () => {
    setMultiplayerState("lobby")
    setGameResults(null)
  }

  const handleBackToMainMenu = () => {
    setMultiplayerState("selection")
    setRole(null)
    setSelectedGameMode("classic")
    setGameResults(null)
  }

  const handleBackToLobby = () => {
    setMultiplayerState("lobby")
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68] text-gray-900 dark:text-white">
      <div className="container mx-auto px-4 py-6 sm:py-8">
        <PageHeader title="Multiplayer Quiz" backLabel="Home" />

        {/* Content */}
        {multiplayerState === "selection" && (
          <div className="max-w-4xl mx-auto">
            <div className="text-center space-y-4 mb-8 md:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-semibold text-gray-800 dark:text-gray-200">
                Choose Your Role
              </h2>
              <p className="text-base sm:text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto px-4">
                Join an existing game or host a new one (admin only)
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6 md:gap-8 max-w-2xl mx-auto">
              <div className="bg-white/10 dark:bg-white/5 backdrop-blur-lg rounded-2xl p-6 sm:p-8 shadow-lg border border-blue-200/30 dark:border-blue-900/30 text-center">
                <Tv className="h-12 w-12 sm:h-16 sm:w-16 text-blue-500 dark:text-blue-400 mx-auto mb-4 sm:mb-6" />
                <h3 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 text-gray-900 dark:text-white">
                  Host a Game
                </h3>
                <p className="text-sm sm:text-base text-gray-700 dark:text-gray-300 mb-4 sm:mb-6 leading-relaxed">
                  Create a new multiplayer quiz room. Only admins can start games.
                </p>
                <Button 
                  onClick={() => handleRoleSelection("host")}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold"
                  size="lg"
                >
                  <Tv className="h-5 w-5 mr-2" />
                  Host Game
                  {isAdmin && (
                    <Shield className="h-4 w-4 ml-2" />
                  )}
                </Button>
              </div>

              <div className="bg-white/10 dark:bg-white/5 backdrop-blur-lg rounded-2xl p-6 sm:p-8 shadow-lg border border-green-200/30 dark:border-green-900/30 text-center">
                <Users className="h-12 w-12 sm:h-16 sm:w-16 text-green-500 dark:text-green-400 mx-auto mb-4 sm:mb-6" />
                <h3 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 text-gray-900 dark:text-white">
                  Join a Game
                </h3>
                <p className="text-sm sm:text-base text-gray-700 dark:text-gray-300 mb-4 sm:mb-6 leading-relaxed">
                  Enter a game code to join an existing multiplayer quiz
                </p>
                <Button 
                  onClick={() => handleRoleSelection("player")}
                  variant="outline"
                  className="w-full border-2 border-green-600 text-green-700 dark:text-green-400 font-bold hover:bg-green-50 dark:hover:bg-green-900/20"
                  size="lg"
                >
                  <Users className="h-5 w-5 mr-2" />
                  Join Game
                </Button>
              </div>
            </div>
          </div>
        )}

        {multiplayerState === "browse" && (
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <Button 
                variant="outline" 
                onClick={handleBackToSelection}
                size="sm"
                className="bg-transparent border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Role Selection
              </Button>
            </div>
            <GameBrowser
              onJoinGame={handleJoinGameFromBrowser}
              onCreateGame={handleCreateGameFromBrowser}
            />
          </div>
        )}

        {multiplayerState === "lobby" && role && (
          <MultiplayerLobby
            role={role}
            onStartGame={handleGameStart}
            onBackToMenu={handleBackToSelection}
          />
        )}

        {multiplayerState === "playing" && (
          <MultiplayerQuiz
            onGameComplete={handleGameComplete}
            onBackToMenu={handleBackToMainMenu}
          />
        )}

        {multiplayerState === "results" && gameResults && (
          <MultiplayerGameResults
            leaderboard={gameResults.leaderboard || []}
            playerPosition={gameResults.playerPosition || 0}
            playerScore={gameResults.playerScore || 0}
            gameStats={gameResults.gameStats}
            onPlayAgain={handleNewGame}
            onBackToMenu={handleBackToMainMenu}
          />
        )}
      </div>
    </div>
  )
}