"use client"

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Music, Users, Sparkles, Play, User, Heart, Shield, Settings, Radio, HelpCircle } from "lucide-react"
import { ThemeSwitcher } from "@/components/theme-switcher"
import { type CustomQuizSettings } from '@/components/custom-quiz-config';
import LoadingScreen from '@/components/LoadingScreen';
import { useUser } from '@/lib/user-context';
import { GameMode } from '@/lib/types';
import { motion, AnimatePresence } from 'framer-motion';
import { useAudioNavigation } from '@/hooks/useAudioNavigation';
import { useGlobalAudio } from '@/lib/services/global-audio-service';
import { LandingTour, resetLandingTour } from '@/components/tours/landing-tour';

// Dynamically import heavy components for better code splitting
const IntroSequence = dynamic(
  () => import('@/components/intro-sequence').then(mod => mod.IntroSequence),
  { ssr: false }
);

const GameModeSelector = dynamic(
  () => import('@/components/game-mode-selector').then(mod => ({ default: mod.GameModeSelector })),
  { ssr: false }
);

const QuizInterface = dynamic(
  () => import('@/components/quiz-interface').then(mod => ({ default: mod.QuizInterface })),
  { ssr: false }
);

const ResultsScreen = dynamic(
  () => import('@/components/results-screen').then(mod => ({ default: mod.ResultsScreen })),
  { ssr: false }
);

const ProfileScreen = dynamic(
  () => import('@/components/profile-screen').then(mod => ({ default: mod.ProfileScreen })),
  { ssr: false }
);

const OnboardingFlow = dynamic(
  () => import('@/components/onboarding-flow').then(mod => ({ default: mod.OnboardingFlow })),
  { ssr: false }
);

const PlaylistManager = dynamic(
  () => import('@/components/playlist-manager').then(mod => ({ default: mod.PlaylistManager })),
  { ssr: false }
);

const CustomQuizConfig = dynamic(
  () => import('@/components/custom-quiz-config').then(mod => ({ default: mod.CustomQuizConfig })),
  { ssr: false }
);

const LoginForm = dynamic(
  () => import('@/components/login-form-mobile').then(mod => ({ default: mod.LoginForm })),
  { ssr: false }
);

type AppState = 'landing' | 'intro' | 'menu' | 'custom-config' | 'quiz' | 'results' | 'profile' | 'playlists' | 'onboarding' | 'login';

// Animation variants for smooth transitions
const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.95
  },
  in: {
    opacity: 1,
    y: 0,
    scale: 1
  },
  out: {
    opacity: 0,
    y: -20,
    scale: 0.95
  }
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.4
};

export default function Home() {
  const { user, isAuthenticated, isFirstTime, isLoading, completeOnboarding, skipOnboarding, resetOnboarding, logout, isAdmin, register, login } = useUser();
  const [appState, setAppState] = useState<AppState>('landing');
  const [preventLoginRedirect, setPreventLoginRedirect] = useState(false);
  const [gameMode, setGameMode] = useState<GameMode>('classic');
  const [customSettings, setCustomSettings] = useState<CustomQuizSettings | null>(null);
  const [lastResults, setLastResults] = useState<any>(null);
  const [isStartingQuiz, setIsStartingQuiz] = useState(false);
  const [showLandingTour, setShowLandingTour] = useState(false);
  const { transitionAudioMode } = useAudioNavigation();
  const globalAudio = useGlobalAudio();

  // Handle onboarding flow
  useEffect(() => {
    if (!isLoading) {
      if (isFirstTime) {
        setAppState('onboarding');
      }
    }
  }, [isLoading, isFirstTime]);
  
  // Handle tour display - separate from onboarding
  useEffect(() => {
    // Only show tour if:
    // 1. Not loading
    // 2. Not first time user (onboarding complete)
    // 3. On landing page
    // 4. Haven't seen tour before
    if (!isLoading && !isFirstTime && appState === 'landing') {
      const hasSeenTour = localStorage.getItem('tour-completed-landing-tour')
      const hasCompletedOnboarding = localStorage.getItem('music-quiz-onboarding-complete')
      
      if (!hasSeenTour && hasCompletedOnboarding) {
        // Delay tour start to ensure DOM is ready
        setTimeout(() => setShowLandingTour(true), 2000)
      }
    }
  }, [isLoading, isFirstTime, appState]);

  // Handle authentication state changes
  useEffect(() => {
    // Only redirect from login to landing if user is authenticated and we're on login page
    if (isAuthenticated && user && appState === 'login' && !preventLoginRedirect) {
      console.log('[Page] User authenticated while on login page, redirecting to landing');
      setAppState('landing');
    }
  }, [isAuthenticated, user, appState, preventLoginRedirect]);

  const handleIntroComplete = () => {
    setAppState('menu');
  };

  const handleEnterApp = async () => {
    // If not logged in, create a guest account
    if (!isAuthenticated) {
      // Generate unique ID for guest to prevent conflicts
      const uniqueId = `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      const guestNumber = Math.floor(Math.random() * 10000);
      const guestUser = {
        username: `Guest${guestNumber}`,
        displayName: `Guest ${guestNumber}`,
        role: 'guest' as const
        // Email will be auto-generated with unique ID in register function
      };
      
      // Register as guest user with default password
      const result = await register(guestUser, 'guest123');
      if (result.success && result.email) {
        // Auto-login as the guest user with the actual generated email
        await login(result.email, 'guest123');
      }
    }
    setAppState('menu');
  };

  const handleGameStart = async (mode: GameMode) => {
    setIsStartingQuiz(true);
    
    // Transition audio from jukebox/menu to quiz mode
    await transitionAudioMode(globalAudio.mode, 'quiz', {
      fadeOutDuration: 1.5,
      pauseInsteadOfStop: true // Keep music playing at low volume during quiz
    });
    
    if (mode === 'custom') {
      // Show custom quiz configuration screen
      setGameMode(mode);
      setAppState('custom-config');
      setIsStartingQuiz(false);
    } else {
      // Start quiz directly for predefined modes
      setGameMode(mode);
      setCustomSettings(null);
      setAppState('quiz');
      // Reset loading state after a short delay to allow quiz to initialize
      setTimeout(() => setIsStartingQuiz(false), 2000);
    }
  };

  const handleCustomQuizStart = (settings: CustomQuizSettings) => {
    setCustomSettings(settings);
    setAppState('quiz');
  };

  const handleBackToMenuFromCustom = () => {
    setAppState('menu');
    setCustomSettings(null);
  };

  const handleGameComplete = async (results: any) => {
    setLastResults(results);
    setAppState('results');
    
    // Transition audio back to idle mode when quiz completes
    await transitionAudioMode('quiz', 'idle', {
      fadeInDuration: 1.0,
      pauseInsteadOfStop: false
    });
  };

  const handleBackToMenu = async () => {
    // Transition audio back to jukebox/menu mode
    await transitionAudioMode(globalAudio.mode, 'jukebox', {
      fadeInDuration: 1.2,
      pauseInsteadOfStop: false
    });
    
    setAppState('menu');
    setCustomSettings(null);
  };

  const handleOnboardingComplete = async (profile: any) => {
    await completeOnboarding(profile);
    setAppState('landing');
    // Show tour after a delay to let the landing page render
    setTimeout(() => {
      setShowLandingTour(true);
    }, 2500);
  };

  const handleSkipOnboarding = async () => {
    await skipOnboarding();
    setAppState('landing');
  };

  const handleLoginSuccess = () => {
    console.log('[Page] handleLoginSuccess called, setting appState to landing');
    // Clear any auth success flag
    localStorage.removeItem('music-quiz-auth-success');
    // Prevent automatic redirect for a moment to avoid race conditions
    setPreventLoginRedirect(true);
    // Set state immediately - the user context should already be updated
    setAppState('landing');
    // Re-enable redirects after a short delay
    setTimeout(() => {
      setPreventLoginRedirect(false);
    }, 1000);
  };

  const handleShowLogin = () => {
    console.log('[Page] handleShowLogin called, isAuthenticated:', isAuthenticated);
    // Don't show login if already authenticated
    if (isAuthenticated) {
      console.log('[Page] User already authenticated, not showing login');
      return;
    }
    setAppState('login');
  };

  const handleLogout = async () => {
    await logout();
    setAppState('landing');
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <LoadingScreen
          messages={[
            "Initializing Music Quiz...",
            "Loading user profile...",
            "Syncing your data...",
            "Preparing the experience...",
            "Getting everything ready...",
            "Almost there...",
            "Finalizing setup...",
            "Welcome back!",
            "Loading complete..."
          ]}
          subtitles={[
            "Setting up your musical journey",
            "Fetching your preferences",
            "Synchronizing playlists",
            "Customizing interface",
            "Loading achievements",
            "Just a moment more",
            "Polishing details",
            "Ready to rock!",
            "Let's get started!"
          ]}
          primaryColor="#3b82f6"
          backgroundColor="#0f172a"
          textColor="#ffffff"
          messageInterval={1800}
          showProgress={true}
          showStats={false}
          showFloatingParticles={true}
        />
      );
    }

    switch (appState) {
      case 'onboarding':
        return (
          <motion.div
            key="onboarding"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <OnboardingFlow 
              onComplete={handleOnboardingComplete}
              onSkip={handleSkipOnboarding}
            />
          </motion.div>
        );
      case 'login':
        console.log('[Page] Rendering login form, appState:', appState, 'isAuthenticated:', isAuthenticated);
        // Don't show login if already authenticated
        if (isAuthenticated && user && !preventLoginRedirect) {
          console.log('[Page] User already authenticated, showing landing instead');
          setAppState('landing');
          return null;
        }
        return (
          <motion.div
            key="login"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
            className="min-h-screen flex items-center justify-center p-4"
          >
            <LoginForm 
              onSuccess={handleLoginSuccess}
              onCancel={handleBackToMenu}
              onSwitchToRegister={() => {
                console.log('[Page] Switching to register - not implemented yet');
                // TODO: Add register state and form
              }}
            />
          </motion.div>
        );
      case 'intro':
        return (
          <motion.div
            key="intro"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <IntroSequence onComplete={handleIntroComplete} />
          </motion.div>
        );
      case 'custom-config':
        return (
          <motion.div
            key="custom-config"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <CustomQuizConfig
              onStartQuiz={handleCustomQuizStart}
              onBack={handleBackToMenuFromCustom}
            />
          </motion.div>
        );
      case 'quiz':
        if (isStartingQuiz) {
          return (
            <LoadingScreen
              messages={[
                "Starting your quiz adventure...",
                "Loading audio tracks...",
                "Preparing questions...",
                "Setting up the challenge...",
                "Initializing game engine...",
                "Ready to test your knowledge!",
                "Let the music begin...",
                "Game starting now!"
              ]}
              subtitles={[
                "Get ready to rock!",
                "High-quality audio loading",
                "Difficulty level set",
                "Timer synchronized",
                "All systems ready",
                "Challenge mode activated",
                "Beat ready to drop",
                "Let's play!"
              ]}
              primaryColor="#f59e0b"
              backgroundColor="#0f172a"
              textColor="#ffffff"
              messageInterval={1500}
              showProgress={true}
              showStats={true}
              showFloatingParticles={true}
            />
          );
        }
        return (
          <motion.div
            key="quiz"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <QuizInterface
              gameMode={gameMode}
              customSettings={customSettings}
              onGameComplete={handleGameComplete}
              onBackToMenu={handleBackToMenu}
              isMultiplayer={false}
            />
          </motion.div>
        );
      case 'results':
        return (
          <motion.div
            key="results"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <ResultsScreen 
              results={lastResults} 
              onBackToMenu={handleBackToMenu} 
              onPlayAgain={() => handleGameStart(gameMode)} 
            />
          </motion.div>
        );
      case 'profile':
        return (
          <motion.div
            key="profile"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <ProfileScreen onBackToMenu={handleBackToMenu} />
          </motion.div>
        );
      case 'playlists':
        return (
          <motion.div
            key="playlists"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <PlaylistManager onClose={handleBackToMenu} />
          </motion.div>
        );
      case 'menu':
      default:
        return (
          <motion.div
            key="menu"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
            className="w-full"
          >
            {/* User Header */}
            {isAuthenticated && user && (
              <div className="mb-8 text-center">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-2xl">
                    {user.avatar === 'rockstar' ? '🎸' : 
                     user.avatar === 'popstar' ? '⭐' : 
                     user.avatar === 'dj' ? '🎧' : '🎵'}
                  </div>
                  <div className="text-left">
                    <h2 className="text-2xl font-bold">Welcome back, {user.displayName}!</h2>
                    <p className="text-base text-gray-600 dark:text-gray-400">
                      Level {user.level} • {user.xp} XP • {user.stats?.totalGames || 0} games played
                    </p>
                  </div>
                </div>
                <div className="flex justify-center gap-2 mb-6">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setAppState('profile')}
                    data-tour="profile-button"
                  >
                    <User className="w-4 h-4 mr-2" />
                    Profile
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setAppState('playlists')}
                  >
                    <Heart className="w-4 h-4 mr-2" />
                    Playlists ({user.playlists.length})
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleLogout}
                    className="text-red-600 border-red-300 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    Logout
                  </Button>
                </div>
              </div>
            )}

            <GameModeSelector onSelectMode={handleGameStart} />

            {/* Small Profile Link */}
            {isAuthenticated && user && (
              <div className="mb-8 flex gap-4 justify-center">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleEnterApp}
                  className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                >
                  <User className="w-4 h-4 mr-2" />
                  Continue as {user.displayName} ({user.role})
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLogout}
                  className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                >
                  Logout
                </Button>
              </div>
            )}

            {/* Login/Register for non-authenticated users */}
            {!isAuthenticated && (
              <div className="mb-8 flex gap-4 justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleShowLogin}
                  className="border-blue-500 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                >
                  <User className="w-4 h-4 mr-2" />
                  Login to Account
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleEnterApp}
                  className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  Continue as Guest
                </Button>
              </div>
            )}

            {/* Debug Section - Only show in development */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <h3 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Debug Tools</h3>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetOnboarding}
                    className="text-xs"
                  >
                    Reset Onboarding
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      resetLandingTour()
                      window.location.reload()
                    }}
                    className="text-xs"
                  >
                    Reset Tour
                  </Button>
                </div>
              </div>
            )}
          </motion.div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68] text-gray-900 dark:text-white flex flex-col relative">
      {/* Theme Switcher and Help - Fixed position with safe area */}
      {appState !== 'onboarding' && (
        <div className="fixed top-4 right-4 sm:top-4 sm:right-4 z-40 safe-top safe-right flex gap-2">
          {appState === 'landing' && (
            <Button
              size="icon"
              variant="outline"
              onClick={() => {
                resetLandingTour()
                setShowLandingTour(true)
                setTimeout(() => setShowLandingTour(false), 100)
              }}
              className="rounded-full"
              title="Start tour"
            >
              <HelpCircle className="h-5 w-5" />
            </Button>
          )}
          <ThemeSwitcher />
        </div>
      )}
      
      {/* Onboarding Modal - Full Screen Overlay */}
      {appState === 'onboarding' && (
        <div className="fixed inset-0 z-50">
          <OnboardingFlow
            onComplete={handleOnboardingComplete}
            onSkip={handleSkipOnboarding}
          />
        </div>
      )}

      {/* Main Content - Hidden when onboarding is active */}
      {appState !== 'onboarding' && (
        <>
          <AnimatePresence mode="wait">
            {appState === 'landing' ? (
              <motion.div
                key="landing"
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
                className="flex-1 flex flex-col items-center justify-center text-center px-4 py-16"
              >
                {/* Clean Landing Page Layout */}
                {/* Hero Section */}
                <div className="mb-12">
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent mb-4">
                    ulTimote
                  </h1>
                  <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 max-w-2xl mx-auto mb-8 leading-relaxed px-4">
                    The ultimate open-source remote music & quiz experience.<br className="hidden sm:inline" />
                    <span className="sm:hidden">Control the vibe.</span>
                    <span className="hidden sm:inline">Control the vibe. Play together. Anywhere.</span>
                    <span className="sm:hidden"><br />Play together. Anywhere.</span>
                  </p>

                  {/* Action Buttons - Clean Layout */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-xl mx-auto mb-8">
                    <Link href="/jukebox">
                      <Button size="lg" className="w-full h-24 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold shadow-lg hover:scale-105 transition-all duration-200">
                        <div className="flex flex-col items-center gap-2">
                          <Radio className="h-8 w-8" />
                          <span className="text-lg">Jukebox</span>
                        </div>
                      </Button>
                    </Link>
                    <Link href="/multiplayer">
                      <Button size="lg" className="w-full h-24 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-semibold shadow-lg hover:scale-105 transition-all duration-200">
                        <div className="flex flex-col items-center gap-2">
                          <Users className="h-8 w-8" />
                          <span className="text-lg">Multiplayer</span>
                        </div>
                      </Button>
                    </Link>
                  </div>
                  
                  {/* Additional Links */}
                  <div className="flex flex-wrap justify-center gap-4 md:gap-6 mb-8">
                    <a href="http://************:3003" target="_blank" rel="noopener noreferrer">
                      <Button size="default" variant="ghost" className="min-w-[120px] px-4 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                        <Play className="h-5 w-5 mr-2" /> Quiz
                      </Button>
                    </a>
                    <Link href="/library">
                      <Button size="default" variant="ghost" className="min-w-[120px] px-4 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                        <Music className="h-5 w-5 mr-2" /> Music Library
                      </Button>
                    </Link>
                    {isAdmin && (
                      <Link href="/library-manager">
                        <Button size="default" variant="ghost" className="min-w-[120px] px-4 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                          <Settings className="h-5 w-5 mr-2" /> Manager
                        </Button>
                      </Link>
                    )}
                  </div>

                  {/* Authentication Buttons */}
                  {!isAuthenticated ? (
                    <div className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center mb-8">
                      <Button
                        size="lg"
                        onClick={handleShowLogin}
                        className="min-w-[160px] px-6 py-3 bg-gradient-to-r from-indigo-500 to-blue-600 text-white font-semibold shadow-md hover:scale-105 transition-transform"
                      >
                        <User className="h-5 w-5 mr-2" />
                        Login
                      </Button>
                      <Button
                        size="lg"
                        variant="outline"
                        onClick={handleEnterApp}
                        className="min-w-[160px] px-6 py-3 border-2 border-gray-400 dark:border-gray-500 text-gray-700 dark:text-gray-300 font-semibold hover:bg-gray-100 dark:hover:bg-gray-800 shadow-md hover:scale-105 transition-transform"
                      >
                        <Sparkles className="h-5 w-5 mr-2" />
                        Guest Mode
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center mb-8">
                      <Button
                        size="lg"
                        onClick={() => setAppState('profile')}
                        className="min-w-[160px] px-6 py-3 bg-gradient-to-r from-teal-500 to-cyan-600 text-white font-semibold shadow-md hover:scale-105 transition-transform"
                        data-tour="profile-button"
                      >
                        <User className="h-5 w-5 mr-2" />
                        {user?.displayName || 'Profile'}
                      </Button>
                      <Button
                        size="lg"
                        variant="outline"
                        onClick={handleLogout}
                        className="min-w-[160px] px-6 py-3 border-2 border-red-400 text-red-600 dark:text-red-400 font-semibold hover:bg-red-50 dark:hover:bg-red-900/20 shadow-md hover:scale-105 transition-transform"
                      >
                        Logout
                      </Button>
                    </div>
                  )}
                </div>

                {/* Simple Feature Cards */}
                <div className="max-w-3xl mx-auto px-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white/10 dark:bg-white/5 backdrop-blur-lg rounded-xl p-6 shadow-lg border border-blue-200/30 dark:border-blue-900/30">
                      <div className="flex items-start gap-4">
                        <Radio className="h-8 w-8 text-blue-500 dark:text-blue-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Remote Jukebox</h3>
                          <p className="text-base text-gray-700 dark:text-gray-300 leading-relaxed">
                            Control music playback remotely. Queue songs, vote on tracks, and manage playlists from any device.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white/10 dark:bg-white/5 backdrop-blur-lg rounded-xl p-6 shadow-lg border border-purple-200/30 dark:border-purple-900/30">
                      <div className="flex items-start gap-4">
                        <Users className="h-8 w-8 text-purple-500 dark:text-purple-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Multiplayer Quiz</h3>
                          <p className="text-base text-gray-700 dark:text-gray-300 leading-relaxed">
                            Join quiz games hosted by admins. Test your music knowledge and compete with friends.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Landing Tour Component */}
                <LandingTour 
                  autoStart={showLandingTour} 
                  onComplete={() => setShowLandingTour(false)} 
                />
              </motion.div>
            ) : appState === 'intro' ? (
              <motion.div
                key="intro-layout"
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
                className="flex min-h-screen flex-col items-center justify-center"
              >
                {renderContent()}
              </motion.div>
            ) : (
              <motion.div
                key="main-layout"
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
                className="flex min-h-screen flex-col items-center justify-between p-4 md:p-8 lg:p-12"
              >
                {renderContent()}
              </motion.div>
            )}
          </AnimatePresence>
        </>
      )}
    </div>
  )
}
