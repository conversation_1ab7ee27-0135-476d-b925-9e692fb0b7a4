'use client'

import React, { useEffect, useState } from 'react'
import dynamicImport from 'next/dynamic'
import { useUser } from '@/lib/user-context'
import { useRouter } from 'next/navigation'
import { Loader2, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import LoadingScreen from '@/components/LoadingScreen'

export const dynamic = 'force-dynamic'

// Dynamically import the component to avoid SSR issues
const MobileLibraryBrowser = dynamicImport(
  () => import('@/components/mobile-library-browser/MobileLibraryBrowser').then(mod => mod.MobileLibraryBrowser),
  { 
    ssr: false,
    loading: () => (
      <LoadingScreen
        messages={[
          "Loading your music library...",
          "Organizing your favorite tracks...",
          "Preparing your personal collection...",
          "Gathering album artwork...",
          "Syncing with the music server..."
        ]}
        subtitles={[
          "Your music awaits",
          "Almost ready to browse",
          "Building your library view",
          "Just a moment more",
          "Connecting to MPD server"
        ]}
        stats={[
          "🎵 Loading library metadata...",
          "📁 Scanning music folders...",
          "🎧 Preparing playback controls...",
          "💿 Indexing your collection...",
          "🔊 Initializing audio system..."
        ]}
        primaryColor="#8b5cf6"
        showProgress={true}
        showStats={true}
        showFloatingParticles={true}
      />
    )
  }
)

export default function LibraryPage() {
  const { user, isLoading, isAuthenticated } = useUser()
  const [mounted, setMounted] = useState(false)
  const router = useRouter()

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    // Redirect if not authenticated after loading is complete
    if (!isLoading && !isAuthenticated && mounted) {
      router.push('/login')
    }
  }, [isLoading, isAuthenticated, mounted, router])

  // Show loading while auth is being checked
  if (!mounted || isLoading) {
    return (
      <LoadingScreen
        messages={[
          "Checking your credentials...",
          "Verifying access permissions...",
          "Authenticating your session...",
          "Loading user preferences..."
        ]}
        subtitles={[
          "Please wait a moment",
          "Security check in progress",
          "Almost there",
          "Finalizing authentication"
        ]}
        primaryColor="#3b82f6"
        showProgress={false}
        showStats={false}
        showFloatingParticles={false}
      />
    )
  }

  // Don't render content if not authenticated
  if (!isAuthenticated) {
    return (
      <LoadingScreen
        messages={[
          "Redirecting to login...",
          "Authentication required...",
          "Please sign in to continue..."
        ]}
        subtitles={[
          "You'll be redirected shortly",
          "Access restricted",
          "Taking you to the login page"
        ]}
        primaryColor="#ef4444"
        showProgress={false}
        showStats={false}
        showFloatingParticles={false}
      />
    )
  }

  return (
    <div className="min-h-screen">
      <div className="fixed top-4 left-4 z-50">
        <Link href="/">
          <Button variant="ghost" size="icon" className="bg-white/10 backdrop-blur-md hover:bg-white/20">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
      </div>
      <MobileLibraryBrowser 
        userRole={user?.role || 'user'}
        isVisible={true}
        currentUser={user}
      />
    </div>
  )
}