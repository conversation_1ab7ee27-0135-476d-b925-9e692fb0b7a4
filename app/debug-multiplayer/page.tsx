'use client'

import { useState, useEffect, useRef } from 'react'
import { io, Socket } from 'socket.io-client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export const dynamic = 'force-dynamic'

export default function DebugMultiplayerPage() {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [connected, setConnected] = useState(false)
  const [logs, setLogs] = useState<Array<{time: string, message: string, type: string}>>([])
  const [gamePin, setGamePin] = useState('')
  const [gameId, setGameId] = useState('')
  const [playerName, setPlayerName] = useState('Debug Player')
  const [currentQuestion, setCurrentQuestion] = useState<any>(null)
  const [localStorage, setLocalStorageState] = useState<any>({})
  
  const playerId = useRef(`debug-${Math.random().toString(36).substr(2, 9)}`)
  
  const log = (message: string, type = 'info') => {
    const time = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, { time, message, type }])
    console.log(`[Debug] ${message}`)
  }
  
  const connectSocket = async () => {
    if (socket?.connected) {
      log('Already connected', 'warning')
      return
    }
    
    log('Connecting to Socket.IO server...')
    
    const newSocket = io('http://192.168.0.23:3001', {
      transports: ['polling', 'websocket'],
      reconnectionAttempts: 3,
      timeout: 20000
    })
    
    newSocket.on('connect', () => {
      log(`✅ Connected! Socket ID: ${newSocket.id}`, 'success')
      setConnected(true)
    })
    
    newSocket.on('disconnect', (reason) => {
      log(`❌ Disconnected: ${reason}`, 'error')
      setConnected(false)
    })
    
    newSocket.on('connect_error', (error) => {
      log(`❌ Connection error: ${error.message}`, 'error')
    })
    
    // Game events
    newSocket.on('player_joined', (data) => {
      log(`👤 Player joined: ${data.name}`, 'info')
    })
    
    newSocket.on('game_started', (data) => {
      log(`🎮 Game started! Total questions: ${data.totalQuestions}`, 'success')
    })
    
    newSocket.on('question_started', (data) => {
      log('❓ Question started!', 'success')
      log(`Question data: ${JSON.stringify(data)}`, 'info')
      setCurrentQuestion(data)
    })
    
    newSocket.on('question_ended', (data) => {
      log(`✅ Question ended. Correct answer: ${data.correctAnswerIndex}`, 'info')
    })
    
    newSocket.on('game_ended', (data) => {
      log('🏁 Game ended!', 'info')
    })
    
    newSocket.on('error', (data) => {
      log(`❌ Error: ${data.message}`, 'error')
    })
    
    // Log all events
    const originalEmit = newSocket.emit.bind(newSocket)
    newSocket.emit = function(...args: any[]) {
      log(`📤 Emitting: ${args[0]}`, 'info')
      return originalEmit(...args)
    }
    
    setSocket(newSocket)
  }
  
  const disconnectSocket = () => {
    if (socket) {
      socket.disconnect()
      setSocket(null)
      setConnected(false)
    }
  }
  
  const createGame = () => {
    if (!socket?.connected) {
      log('❌ Not connected to server', 'error')
      return
    }
    
    log('Creating game...')
    socket.emit('create-game', {
      playerId: playerId.current,
      playerName: playerName,
      settings: {
        gameMode: 'classic',
        maxPlayers: 6,
        questionCount: 10
      }
    }, (response: any) => {
      if (response?.success) {
        setGamePin(response.data.gamePin)
        setGameId(response.data.gameId)
        log(`✅ Game created! PIN: ${response.data.gamePin}`, 'success')
        log(`Game ID: ${response.data.gameId}`, 'info')
      } else {
        log(`❌ Failed to create game: ${response?.error || 'Unknown error'}`, 'error')
      }
    })
  }
  
  const joinGame = () => {
    if (!socket?.connected) {
      log('❌ Not connected to server', 'error')
      return
    }
    
    if (!gamePin) {
      log('❌ Please enter a game PIN', 'error')
      return
    }
    
    log(`Joining game ${gamePin}...`)
    socket.emit('join-game', {
      gamePin: gamePin,
      playerId: playerId.current,
      playerName: playerName
    }, (response: any) => {
      if (response?.success) {
        setGameId(response.data.gameId)
        log('✅ Joined game successfully!', 'success')
      } else {
        log(`❌ Failed to join game: ${response?.error || 'Unknown error'}`, 'error')
      }
    })
  }
  
  const startGame = () => {
    if (!socket?.connected || !gameId) {
      log('❌ Cannot start game', 'error')
      return
    }
    
    log('Starting game...')
    socket.emit('start-game', { gameId }, (response: any) => {
      if (response?.success) {
        log('✅ Game start command sent', 'success')
      } else {
        log(`❌ Failed to start game: ${response?.error || 'Unknown error'}`, 'error')
      }
    })
  }
  
  const submitAnswer = (answerIndex: number) => {
    if (!socket?.connected || !gameId) {
      log('❌ Cannot submit answer', 'error')
      return
    }
    
    log(`Submitting answer: ${answerIndex}`)
    socket.emit('submit-answer', {
      gameId: gameId,
      playerId: playerId.current,
      answerIndex: answerIndex,
      timeTaken: 5000
    })
  }
  
  const checkLocalStorage = () => {
    const state = {
      currentGameCode: window.localStorage.getItem('currentGameCode'),
      currentGameId: window.localStorage.getItem('currentGameId'),
      currentPlayerId: window.localStorage.getItem('currentPlayerId'),
      currentPlayerName: window.localStorage.getItem('currentPlayerName'),
      currentGameMode: window.localStorage.getItem('currentGameMode'),
      currentMultiplayerRole: window.localStorage.getItem('currentMultiplayerRole')
    }
    setLocalStorageState(state)
    log('localStorage checked', 'info')
  }
  
  const clearLocalStorage = () => {
    window.localStorage.clear()
    log('✅ localStorage cleared', 'success')
    checkLocalStorage()
  }
  
  const clearLogs = () => {
    setLogs([])
  }
  
  useEffect(() => {
    connectSocket()
    return () => {
      disconnectSocket()
    }
  }, [])
  
  const getLogColor = (type: string) => {
    switch(type) {
      case 'error': return 'text-red-400'
      case 'success': return 'text-green-400'
      case 'warning': return 'text-yellow-400'
      case 'info': return 'text-blue-400'
      default: return 'text-gray-300'
    }
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#18122B] via-[#393053] to-[#443C68] p-4">
      <div className="max-w-4xl mx-auto space-y-4">
        <h1 className="text-3xl font-bold text-white mb-6">🔍 Multiplayer Debug Tool</h1>
        
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <CardTitle className="text-white">📡 Socket Connection</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Badge className={connected ? 'bg-green-600' : 'bg-red-600'}>
              {connected ? 'Connected' : 'Disconnected'}
            </Badge>
            <div className="flex gap-2">
              <Button onClick={connectSocket}>Connect</Button>
              <Button onClick={disconnectSocket} variant="outline">Disconnect</Button>
              <Button onClick={clearLogs} variant="outline">Clear Log</Button>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <CardTitle className="text-white">🎮 Game Management</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={playerName}
                onChange={(e) => setPlayerName(e.target.value)}
                placeholder="Player Name"
                className="bg-white/10 border-white/20 text-white"
              />
              <Button onClick={createGame}>Create Game</Button>
            </div>
            <div className="flex gap-2">
              <Input
                value={gamePin}
                onChange={(e) => setGamePin(e.target.value)}
                placeholder="Game PIN"
                className="bg-white/10 border-white/20 text-white"
              />
              <Button onClick={joinGame}>Join Game</Button>
            </div>
            <div>
              <Button onClick={startGame} disabled={!gameId}>Start Game</Button>
            </div>
            {(gamePin || gameId) && (
              <div className="bg-black/20 p-3 rounded text-white text-sm">
                <div>Game PIN: <strong>{gamePin}</strong></div>
                <div>Game ID: <code>{gameId}</code></div>
                <div>Player ID: <code>{playerId.current}</code></div>
              </div>
            )}
          </CardContent>
        </Card>
        
        {currentQuestion && (
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader>
              <CardTitle className="text-white">📋 Current Question</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-black/20 p-4 rounded text-white">
                <h3 className="font-bold mb-2">Question {currentQuestion.questionIndex + 1}</h3>
                <p className="mb-2">{currentQuestion.question?.question}</p>
                {currentQuestion.question?.track && (
                  <p className="text-sm text-gray-300 mb-2">
                    🎵 Track: {currentQuestion.question.track.title} by {currentQuestion.question.track.artist}
                  </p>
                )}
                {currentQuestion.question?.options?.map((option: string, index: number) => (
                  <div key={index} className="my-1">
                    {String.fromCharCode(65 + index)}: {option}
                  </div>
                ))}
                <div className="flex gap-2 mt-4">
                  {[0, 1, 2, 3].map(i => (
                    <Button key={i} onClick={() => submitAnswer(i)} size="sm">
                      Answer {String.fromCharCode(65 + i)}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <CardTitle className="text-white">🔧 Browser State</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Button onClick={checkLocalStorage} variant="outline">Check localStorage</Button>
              <Button onClick={clearLocalStorage} variant="outline">Clear localStorage</Button>
            </div>
            {Object.keys(localStorage).length > 0 && (
              <pre className="bg-black/20 p-3 rounded text-white text-xs overflow-x-auto">
                {JSON.stringify(localStorage, null, 2)}
              </pre>
            )}
          </CardContent>
        </Card>
        
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <CardTitle className="text-white">📝 Event Log</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-black/20 p-3 rounded max-h-96 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className={`text-sm font-mono ${getLogColor(log.type)}`}>
                  [{log.time}] {log.message}
                </div>
              ))}
              {logs.length === 0 && (
                <div className="text-gray-400">No events yet...</div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}