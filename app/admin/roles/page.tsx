'use client'

import { useState, useEffect } from 'react'
import { useUser } from '@/lib/user-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { Shield, Users, Crown, User, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export const dynamic = 'force-dynamic'

interface UserWithRole {
  id: string
  displayName: string
  email: string
  role: 'user' | 'dj' | 'superuser'
  createdAt: string
  lastActive: string | null
}

const roleIcons = {
  user: <User className="h-4 w-4" />,
  dj: <Users className="h-4 w-4" />,
  superuser: <Crown className="h-4 w-4" />
}

const roleColors = {
  user: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
  dj: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  superuser: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
}

export default function AdminRolesPage() {
  const { user: currentUser } = useUser()
  const router = useRouter()
  const [users, setUsers] = useState<UserWithRole[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)

  useEffect(() => {
    // Check if user is superuser
    if (currentUser && currentUser.role !== 'superuser') {
      toast.error('Access denied. Superuser role required.')
      router.push('/')
      return
    }

    fetchUsers()
  }, [currentUser, router])

  const fetchUsers = async () => {
    try {
      // Get auth token for the API request
      const { TokenManager } = await import('@/lib/auth-service')
      const token = TokenManager.getToken()
      
      const headers: HeadersInit = { 'Content-Type': 'application/json' }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch('/api/admin/roles', {
        headers,
        credentials: 'include'
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      setUsers(data.users || [])
    } catch (error) {
      console.error('Failed to load users:', error)
      toast.error('Failed to load users', {
        description: error instanceof Error ? error.message : 'Please check your authentication and try again',
        duration: 5000
      })
    } finally {
      setLoading(false)
    }
  }

  const updateUserRole = async (userId: string, newRole: string) => {
    setUpdating(userId)
    try {
      // Get auth token for the API request
      const { TokenManager } = await import('@/lib/auth-service')
      const token = TokenManager.getToken()
      
      const headers: HeadersInit = { 'Content-Type': 'application/json' }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await fetch(`/api/admin/roles`, {
        method: 'PATCH',
        headers,
        credentials: 'include',
        body: JSON.stringify({ userId, role: newRole })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      toast.success('Role updated successfully')
      fetchUsers() // Refresh the list
    } catch (error) {
      console.error('Failed to update role:', error)
      toast.error('Failed to update role', {
        description: error instanceof Error ? error.message : 'Please try again',
        duration: 5000
      })
    } finally {
      setUpdating(null)
    }
  }

  if (!currentUser || currentUser.role !== 'superuser') {
    return null
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-6">
        <Link href="/">
          <Button variant="ghost" size="sm" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </Link>
        
        <div className="flex items-center gap-3">
          <Shield className="h-8 w-8 text-purple-500" />
          <div>
            <h1 className="text-3xl font-bold">Role Management</h1>
            <p className="text-muted-foreground">Manage user permissions and access levels</p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
          <CardDescription>
            Total users: {users.length}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map(user => (
              <div
                key={user.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <div>
                      <h3 className="font-semibold">{user.displayName}</h3>
                      <p className="text-sm text-muted-foreground">{user.email}</p>
                      <p className="text-xs text-muted-foreground">
                        Joined: {new Date(user.createdAt).toLocaleDateString()}
                        {user.lastActive && (
                          <> • Last active: {new Date(user.lastActive).toLocaleDateString()}</>
                        )}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <Badge className={roleColors[user.role]}>
                    {roleIcons[user.role]}
                    <span className="ml-1">{user.role}</span>
                  </Badge>

                  <Select
                    value={user.role}
                    onValueChange={(value) => updateUserRole(user.id, value)}
                    disabled={updating === user.id || user.id === currentUser.id}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">User</SelectItem>
                      <SelectItem value="dj">DJ</SelectItem>
                      <SelectItem value="superuser">Superuser</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="mt-8 p-4 bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-lg">
        <h3 className="font-semibold text-amber-800 dark:text-amber-200 mb-2">Role Permissions</h3>
        <div className="space-y-2 text-sm text-amber-700 dark:text-amber-300">
          <div className="flex items-start gap-2">
            <User className="h-4 w-4 mt-0.5" />
            <div>
              <strong>User:</strong> Can play quizzes, view library, and use basic features
            </div>
          </div>
          <div className="flex items-start gap-2">
            <Users className="h-4 w-4 mt-0.5" />
            <div>
              <strong>DJ:</strong> All user permissions + manage playlists, control music playback, and access jukebox
            </div>
          </div>
          <div className="flex items-start gap-2">
            <Crown className="h-4 w-4 mt-0.5" />
            <div>
              <strong>Superuser:</strong> All permissions + user management, system settings, and admin tools
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}