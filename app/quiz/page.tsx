"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import dynamic from "next/dynamic"
import { PageHeader } from "@/components/ui/page-header"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { GameModeSelector } from "@/components/game-mode-selector"
import { ResultsScreen } from "@/components/results-screen"
import { CustomQuizConfig, type CustomQuizSettings } from "@/components/custom-quiz-config"
import { QuizErrorBoundary } from "@/components/quiz-error-boundary"
import type { GameMode } from "@/lib/types"

const QuizInterface = dynamic(() => import('@/components/quiz-interface').then(mod => mod.QuizInterface), {
  ssr: false,
  loading: () => {
    const LoadingScreen = dynamic(() => import('@/components/LoadingScreen'), { ssr: false });
    return (
      <LoadingScreen
        messages={[
          "Loading the ultimate music quiz...",
          "Preparing your musical challenge...",
          "Tuning the difficulty...",
          "Selecting the perfect tracks...",
          "Getting the beat ready...",
          "Mixing up some questions...",
          "Calibrating your music knowledge...",
          "Loading audio samples...",
          "Preparing the quiz engine...",
          "Ready to test your skills..."
        ]}
        subtitles={[
          "Get ready to rock!",
          "This will be epic...",
          "Time to show what you know!",
          "Let the music begin...",
          "Challenge accepted!",
          "Feel the rhythm...",
          "Music mastery awaits...",
          "Beat the high score!",
          "Almost ready to play...",
          "Let's make some music!"
        ]}
        primaryColor="#f59e0b"
        backgroundColor="#0f172a"
        textColor="#ffffff"
        messageInterval={2200}
        progressUpdateInterval={100}
        logo={
          <div className="w-20 h-20 mx-auto rounded-full flex items-center justify-center animate-pulse-slow bg-gradient-to-br from-amber-500 to-orange-500">
            <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
            </svg>
          </div>
        }
      />
    );
  }
});

type QuizState = "mode-selection" | "genre-selection" | "custom-config" | "playing" | "results"

function QuizContent() {
  const searchParams = useSearchParams()
  const [quizState, setQuizState] = useState<QuizState>("mode-selection")
  const [selectedMode, setSelectedMode] = useState<GameMode | null>(null)
  const [selectedGenre, setSelectedGenre] = useState<string | undefined>(undefined)
  const [gameResults, setGameResults] = useState<any>(null)
  const [genreInput, setGenreInput] = useState("")
  const [customSettings, setCustomSettings] = useState<CustomQuizSettings | null>(null)
  const [isMultiplayer, setIsMultiplayer] = useState(false)
  const [multiplayerRole, setMultiplayerRole] = useState<'host' | 'player' | null>(null)

  // Check for multiplayer parameters on mount
  useEffect(() => {
    const multiplayer = searchParams.get('multiplayer') === 'true'
    const mode = searchParams.get('mode') as GameMode
    const role = searchParams.get('role') as 'host' | 'player'
    const gameCode = searchParams.get('gameCode')
    const gameId = searchParams.get('gameId')

    if (multiplayer && mode) {
      setIsMultiplayer(true)
      setMultiplayerRole(role)
      setSelectedMode(mode)
      
      // Store multiplayer info for quiz interface
      if (gameCode) localStorage.setItem('currentGameCode', gameCode)
      if (gameId) localStorage.setItem('currentGameId', gameId)
      
      // Start game directly for multiplayer
      setQuizState("playing")
    }
  }, [searchParams])

  const handleStartGame = (mode: GameMode) => {
    if (mode === "genre-specialist") {
      setSelectedMode(mode)
      setQuizState("genre-selection")
    } else if (mode === "custom") {
      setSelectedMode(mode)
      setQuizState("custom-config")
    } else {
      setSelectedMode(mode)
      setQuizState("playing")
    }
  }

  const handleGenreConfirm = () => {
    if (genreInput.trim()) {
      setSelectedGenre(genreInput.trim())
      setQuizState("playing")
    }
  }

  const handleGenreCancel = () => {
    setQuizState("mode-selection")
    setSelectedMode(null)
    setGenreInput("")
  }

  const handleCustomQuizStart = (settings: CustomQuizSettings) => {
    setCustomSettings(settings)
    setQuizState("playing")
  }

  const handleCustomConfigBack = () => {
    setQuizState("mode-selection")
    setSelectedMode(null)
    setCustomSettings(null)
  }

  const handleGameComplete = (results: any) => {
    setGameResults(results)
    setQuizState("results")
  }

  const handleBackToModeSelection = () => {
    // If multiplayer, go back to multiplayer page
    if (isMultiplayer) {
      window.location.href = '/multiplayer'
      return
    }
    
    setQuizState("mode-selection")
    setSelectedMode(null)
    setSelectedGenre(undefined)
    setGameResults(null)
    setGenreInput("")
    setCustomSettings(null)
  }

  const handlePlayAgain = () => {
    setQuizState("mode-selection")
    setSelectedMode(null)
    setSelectedGenre(undefined)
    setGameResults(null)
    setGenreInput("")
    setCustomSettings(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68] text-gray-900 dark:text-white">
      <div className="container mx-auto px-4 py-6 sm:py-8">
        <PageHeader title="Music Quiz" backLabel="Home" />

        {/* Quiz Content */}
        {quizState === "mode-selection" && (
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <h2 className="text-2xl md:text-3xl font-semibold text-gray-800 dark:text-gray-200">
                Choose Your Quiz Mode
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Test your music knowledge across different challenges and genres
              </p>
            </div>
            <GameModeSelector onSelectMode={handleStartGame} />
          </div>
        )}

        {/* Genre Selection Dialog */}
        <Dialog open={quizState === "genre-selection"} onOpenChange={() => {}}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Choose Your Genre</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="genre-input">Enter a music genre (e.g., Rock, Pop, Jazz, Hip-Hop)</Label>
                <Input
                  id="genre-input"
                  value={genreInput}
                  onChange={(e) => setGenreInput(e.target.value)}
                  placeholder="e.g. Rock"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && genreInput.trim()) {
                      handleGenreConfirm()
                    }
                  }}
                  autoFocus
                />
              </div>
              <div className="flex gap-2">
                <Button 
                  onClick={handleGenreConfirm} 
                  disabled={!genreInput.trim()}
                  className="flex-1"
                >
                  Start Quiz
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleGenreCancel}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {quizState === "custom-config" && (
          <CustomQuizConfig
            onStartQuiz={handleCustomQuizStart}
            onBack={handleCustomConfigBack}
          />
        )}

        {quizState === "playing" && selectedMode && (
          <QuizErrorBoundary>
            <QuizInterface
              gameMode={selectedMode}
              genreFilter={selectedGenre}
              customSettings={customSettings}
              onGameComplete={handleGameComplete}
              onBackToMenu={handleBackToModeSelection}
              isMultiplayer={isMultiplayer}
              multiplayerRole={multiplayerRole}
              showBackButton={false}
            />
          </QuizErrorBoundary>
        )}

        {quizState === "results" && gameResults && (
          <ResultsScreen
            results={gameResults}
            onPlayAgain={handlePlayAgain}
            onBackToMenu={handleBackToModeSelection}
          />
        )}
      </div>
    </div>
  )
}

export default function QuizPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68] text-gray-900 dark:text-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-amber-500 to-orange-500 animate-pulse" />
          <p className="text-lg text-gray-600 dark:text-gray-400">Loading quiz...</p>
        </div>
      </div>
    }>
      <QuizContent />
    </Suspense>
  )
} 