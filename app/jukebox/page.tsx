'use client'

import { Suspense } from 'react'
import Jukebox from '@/components/jukebox'
import ErrorBoundary from '@/components/error-boundary'
import { JukeboxSkeleton } from '@/components/skeletons/jukebox-skeleton'
import { PageHeader } from '@/components/ui/page-header'

export const dynamic = 'force-dynamic'

export default function JukeboxPage() {
    return (
        <ErrorBoundary
            fallback={(error) => (
                <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
                    <div className="max-w-md w-full bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
                        <h2 className="text-2xl font-bold text-white mb-4">🎵 Jukebox Error</h2>
                        <p className="text-white/80 mb-4">
                            Something went wrong with the jukebox. This might be due to:
                        </p>
                        <ul className="text-left text-white/70 text-sm mb-6 space-y-1">
                            <li>• MPD server connection issues</li>
                            <li>• Audio system problems</li>
                            <li>• Network connectivity</li>
                        </ul>
                        <button 
                            onClick={() => window.location.reload()}
                            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                            Retry Jukebox
                        </button>
                    </div>
                </div>
            )}
        >
            <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
                <Suspense fallback={<JukeboxSkeleton />}>
                    <Jukebox />
                </Suspense>
            </div>
        </ErrorBoundary>
    )
}