"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { PageHeader } from "@/components/ui/page-header"
import io from "socket.io-client"

export default function TestWebSocketPage() {
  const [status, setStatus] = useState<string>("Not connected")
  const [messages, setMessages] = useState<string[]>([])
  const [socket, setSocket] = useState<any>(null)

  const connect = () => {
    setMessages(prev => [...prev, "Attempting to connect..."])
    
    const newSocket = io("http://localhost:3001", {
      transports: ["websocket", "polling"],
      reconnection: true
    })
    
    newSocket.on("connect", () => {
      setStatus("Connected")
      setMessages(prev => [...prev, `Connected with ID: ${newSocket.id}`])
    })
    
    newSocket.on("connect_error", (error: any) => {
      setStatus("Connection Error")
      setMessages(prev => [...prev, `Error: ${error.message}`])
    })
    
    newSocket.on("disconnect", () => {
      setStatus("Disconnected")
      setMessages(prev => [...prev, "Disconnected from server"])
    })
    
    setSocket(newSocket)
  }
  
  const testCreateGame = () => {
    if (!socket || !socket.connected) {
      setMessages(prev => [...prev, "Not connected!"])
      return
    }
    
    const config = {
      questionsPerRound: 2,
      categories: {
        classic: { enabled: false, rounds: 0 },
        quickFire: { enabled: false, rounds: 0 },
        audioTricks: { enabled: false, rounds: 0 },
        albumArt: { enabled: false, rounds: 0 },
        audioFingerprint: { enabled: false, rounds: 0 },
        chartPosition: { enabled: false, rounds: 0 },
        decadeChallenge: { enabled: false, rounds: 0 },
        genreSpecialist: { enabled: false, rounds: 0 },
        generalKnowledge: { enabled: true, rounds: 2, categories: ["allgemein"] }
      }
    }
    
    setMessages(prev => [...prev, "Sending create-game with GK-only config..."])
    
    socket.emit("create-game", {
      hostName: "WebSocketTest",
      gameMode: "ultimote",
      settings: {
        totalQuestions: 4,
        timePerQuestion: 30,
        ultimoteConfig: config
      }
    }, (response: any) => {
      setMessages(prev => [...prev, `Response: ${JSON.stringify(response)}`])
    })
    
    socket.on("game-state", (state: any) => {
      setMessages(prev => [...prev, `Game state received: ${state.pin}`])
      if (state.settings?.ultimoteConfig) {
        setMessages(prev => [...prev, "✅ ultimoteConfig is in game state"])
      } else {
        setMessages(prev => [...prev, "❌ ultimoteConfig missing from game state"])
      }
    })
  }
  
  const disconnect = () => {
    if (socket) {
      socket.disconnect()
      setSocket(null)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-100 to-indigo-200 dark:from-[#18122B] dark:via-[#393053] dark:to-[#443C68]">
      <div className="container mx-auto px-4 py-6">
        <PageHeader title="WebSocket Test" backLabel="Home" />
        
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle>WebSocket Connection Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <span>Status:</span>
              <span className={`font-bold ${
                status === "Connected" ? "text-green-600" : 
                status === "Connection Error" ? "text-red-600" : 
                "text-gray-600"
              }`}>
                {status}
              </span>
            </div>
            
            <div className="flex gap-2">
              <Button onClick={connect} disabled={socket?.connected}>
                Connect
              </Button>
              <Button onClick={testCreateGame} disabled={!socket?.connected}>
                Test Create Game
              </Button>
              <Button onClick={disconnect} variant="outline" disabled={!socket}>
                Disconnect
              </Button>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">Messages:</h3>
              <div className="h-64 overflow-y-auto bg-gray-100 dark:bg-gray-800 rounded p-3 font-mono text-sm">
                {messages.map((msg, i) => (
                  <div key={i}>{msg}</div>
                ))}
              </div>
            </div>
            
            <div className="text-sm text-gray-600">
              <p>This page tests direct WebSocket connection to the multiplayer server.</p>
              <p>If connection fails, check:</p>
              <ul className="list-disc ml-5 mt-2">
                <li>Is the dev server running? (npm run dev)</li>
                <li>Check browser console for errors</li>
                <li>Try different ports: 3000, 3001, 3002</li>
                <li>Check for CORS or firewall issues</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}