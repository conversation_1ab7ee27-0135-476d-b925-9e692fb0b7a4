import { NextResponse } from 'next/server'
import { performanceMonitor } from '@/lib/monitoring/performance-monitor'
import prisma from '@/lib/database/prisma'
import { getMpdConnectionConfig } from '@/lib/env'
import { MPDClient } from '@/lib/mpd-client'

export const dynamic = 'force-dynamic'

// GET /api/monitoring/health - Get system health status
export async function GET() {
  const checks: Record<string, boolean> = {}
  const details: Record<string, any> = {}
  const startTime = Date.now()

  // Check database
  try {
    const dbStart = Date.now()
    const count = await prisma.user.count()
    const dbTime = Date.now() - dbStart
    
    checks.database = true
    details.database = {
      status: 'healthy',
      responseTime: dbTime,
      userCount: count
    }
    
    performanceMonitor.record({
      name: 'health.database',
      duration: dbTime,
      timestamp: new Date()
    })
  } catch (error) {
    checks.database = false
    details.database = {
      status: 'unhealthy',
      error: error.message
    }
  }

  // Check MPD connection
  try {
    const mpdStart = Date.now()
    const config = getMpdConnectionConfig()
    const client = new MPDClient(config)
    
    await client.connect()
    const status = await client.getStatus()
    await client.disconnect()
    
    const mpdTime = Date.now() - mpdStart
    
    checks.mpd = true
    details.mpd = {
      status: 'healthy',
      responseTime: mpdTime,
      mpdStatus: status
    }
    
    performanceMonitor.record({
      name: 'health.mpd',
      duration: mpdTime,
      timestamp: new Date()
    })
  } catch (error) {
    checks.mpd = false
    details.mpd = {
      status: 'unhealthy',
      error: error.message
    }
  }

  // Check Socket.IO server
  try {
    const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001'
    const socketStart = Date.now()
    
    const response = await fetch(`${socketUrl}/health`, {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    }).catch(() => null)
    
    const socketTime = Date.now() - socketStart
    
    checks.socketServer = response?.ok || false
    details.socketServer = {
      status: response?.ok ? 'healthy' : 'unhealthy',
      responseTime: socketTime,
      url: socketUrl
    }
    
    performanceMonitor.record({
      name: 'health.socket',
      duration: socketTime,
      timestamp: new Date()
    })
  } catch (error) {
    checks.socketServer = false
    details.socketServer = {
      status: 'unhealthy',
      error: error.message
    }
  }

  // Memory usage
  const memoryUsage = process.memoryUsage()
  details.memory = {
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
    external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB',
    rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB'
  }

  // Overall status
  const healthyCount = Object.values(checks).filter(v => v).length
  const totalChecks = Object.keys(checks).length
  const overallStatus = 
    healthyCount === totalChecks ? 'healthy' :
    healthyCount >= totalChecks / 2 ? 'degraded' :
    'unhealthy'

  const totalTime = Date.now() - startTime

  return NextResponse.json({
    status: overallStatus,
    timestamp: new Date(),
    responseTime: totalTime,
    checks,
    details,
    metrics: {
      healthy: healthyCount,
      total: totalChecks,
      percentage: Math.round((healthyCount / totalChecks) * 100)
    }
  }, {
    status: overallStatus === 'healthy' ? 200 : 503,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    }
  })
}