import { NextRequest, NextResponse } from 'next/server'
import { performanceMonitor } from '@/lib/monitoring/performance-monitor'
import { withAuth, AuthenticatedRequest } from '@/middleware/auth-middleware'

// GET /api/monitoring/metrics - Get performance metrics
async function handleGet(req: NextRequest) {
  try {
    // Get query parameters
    const searchParams = req.nextUrl.searchParams
    const metric = searchParams.get('metric')
    const startTime = searchParams.get('startTime')
    const endTime = searchParams.get('endTime')

    if (metric) {
      // Get specific metric stats
      const stats = performanceMonitor.getStats(metric)
      if (!stats) {
        return NextResponse.json({
          success: false,
          message: `No metrics found for: ${metric}`
        }, { status: 404 })
      }

      return NextResponse.json({
        success: true,
        metric,
        stats
      })
    }

    // Get all metrics for time range
    const metrics = performanceMonitor.getMetrics(
      startTime ? new Date(startTime) : undefined,
      endTime ? new Date(endTime) : undefined
    )

    // Convert Map to object for JSON serialization
    const metricsObj: Record<string, any> = {}
    for (const [key, values] of metrics.entries()) {
      metricsObj[key] = {
        count: values.length,
        metrics: values.slice(-100) // Last 100 metrics
      }
    }

    return NextResponse.json({
      success: true,
      metrics: metricsObj,
      timestamp: new Date()
    })
  } catch (error) {
    console.error('Failed to get metrics:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to retrieve metrics'
    }, { status: 500 })
  }
}

// POST /api/monitoring/metrics - Record a custom metric
async function handlePost(req: AuthenticatedRequest) {
  try {
    const body = await req.json()
    const { name, duration, tags, metadata } = body

    if (!name || typeof duration !== 'number') {
      return NextResponse.json({
        success: false,
        message: 'Name and duration are required'
      }, { status: 400 })
    }

    performanceMonitor.record({
      name,
      duration,
      timestamp: new Date(),
      tags,
      metadata
    })

    return NextResponse.json({
      success: true,
      message: 'Metric recorded'
    })
  } catch (error) {
    console.error('Failed to record metric:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to record metric'
    }, { status: 500 })
  }
}

export const GET = withAuth(async (req: AuthenticatedRequest) => {
  return handleGet(req)
}, { requiredRole: 'dj' })

export const POST = withAuth(async (req: AuthenticatedRequest) => {
  return handlePost(req)
})