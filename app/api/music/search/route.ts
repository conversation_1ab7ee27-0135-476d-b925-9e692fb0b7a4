import { NextRequest, NextResponse } from 'next/server'
import { spawn } from 'child_process'
import { promises as fs } from 'fs'
import path from 'path'
import { promisify } from 'util'
import { exec } from 'child_process'
import prisma from '@/lib/database/prisma'

const execAsync = promisify(exec)

interface YouTubeSearchResult {
  id: string
  title: string
  channel: string
  duration: string
  thumbnail: string
  url: string
  views?: string
  uploadDate?: string
}

interface DownloadRequest {
  videoId: string
  artist: string
  title: string
  album?: string
  year?: number
  genre?: string
}

interface DuplicateCheckResult {
  exists: boolean
  tracks: Array<{
    id: string
    artist: string
    title: string
    album?: string
    year?: number
    genre?: string
    mpdFilePath: string
  }>
}

interface MetadataPreview {
  suggestedGenre?: string
  suggestedYear?: number
  similarTracks: Array<{
    artist: string
    title: string
    album?: string
    year?: number
    genre?: string
  }>
}

// Enhanced logging function
function logDebug(level: 'INFO' | 'WARN' | 'ERROR', message: string, data?: any) {
  const timestamp = new Date().toISOString()
  const prefix = `[${timestamp}] [MUSIC-DOWNLOADER] [${level}]`
  
  if (data) {
    console.log(`${prefix} ${message}`, JSON.stringify(data, null, 2))
  } else {
    console.log(`${prefix} ${message}`)
  }
}

// New endpoint for duplicate checking and metadata preview
export async function PUT(request: NextRequest) {
  try {
    const { artist, title } = await request.json()
    
    if (!artist || !title) {
      return NextResponse.json(
        { error: 'Artist and title are required' },
        { status: 400 }
      )
    }

    logDebug('INFO', `Checking for duplicates and metadata preview: ${artist} - ${title}`)

    // Check for exact duplicates
    const exactMatch = await prisma.quizTrack.findMany({
      where: {
        AND: [
          { artist: { equals: artist, mode: 'insensitive' } },
          { title: { equals: title, mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        artist: true,
        title: true,
        album: true,
        year: true,
        genre: true,
        mpdFilePath: true
      }
    })

    // Check for similar tracks by same artist
    const similarByArtist = await prisma.quizTrack.findMany({
      where: {
        artist: { equals: artist, mode: 'insensitive' }
      },
      select: {
        artist: true,
        title: true,
        album: true,
        year: true,
        genre: true
      },
      take: 10,
      orderBy: { timesPlayed: 'desc' }
    })

    // Generate metadata suggestions
    const metadataPreview: MetadataPreview = {
      similarTracks: similarByArtist
    }

    // Suggest genre from most common genre by this artist
    const genreCounts = similarByArtist.reduce((acc, track) => {
      if (track.genre && track.genre !== 'Unknown') {
        acc[track.genre] = (acc[track.genre] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    if (Object.keys(genreCounts).length > 0) {
      metadataPreview.suggestedGenre = Object.entries(genreCounts)
        .sort(([,a], [,b]) => b - a)[0][0]
    }

    // Suggest year from median year of artist's tracks
    const years = similarByArtist
      .map(t => t.year)
      .filter(y => y && y > 1900)
      .sort((a, b) => a - b)
    
    if (years.length > 0) {
      metadataPreview.suggestedYear = years[Math.floor(years.length / 2)]
    }

    const duplicateResult: DuplicateCheckResult = {
      exists: exactMatch.length > 0,
      tracks: exactMatch
    }

    logDebug('INFO', `Duplicate check result: ${duplicateResult.exists ? 'FOUND' : 'NONE'}`, {
      exactMatches: exactMatch.length,
      similarTracks: similarByArtist.length,
      suggestedGenre: metadataPreview.suggestedGenre,
      suggestedYear: metadataPreview.suggestedYear
    })

    return NextResponse.json({
      success: true,
      duplicateCheck: duplicateResult,
      metadataPreview
    })

  } catch (error) {
    logDebug('ERROR', 'Duplicate check failed', { error: error instanceof Error ? error.message : String(error) })
    return NextResponse.json(
      { error: 'Failed to check duplicates' },
      { status: 500 }
    )
  }
}

// YouTube search endpoint (enhanced logging)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50) // Cap at 50 results

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      )
    }

    if (query.trim().length < 2) {
      return NextResponse.json(
        { error: 'Search query must be at least 2 characters' },
        { status: 400 }
      )
    }

    logDebug('INFO', `Starting YouTube search`, { query: query.trim(), limit })

    // Check if yt-dlp is available with enhanced debugging
    try {
      const { stdout: ytDlpPath } = await execAsync('which yt-dlp', { timeout: 5000 })
      logDebug('INFO', `yt-dlp found at: ${ytDlpPath.trim()}`)
      
      // Check yt-dlp version
      const { stdout: ytDlpVersion } = await execAsync('yt-dlp --version', { timeout: 5000 })
      logDebug('INFO', `yt-dlp version: ${ytDlpVersion.trim()}`)
      
    } catch (error) {
      logDebug('ERROR', 'yt-dlp not found in PATH', { error: error instanceof Error ? error.message : String(error) })
      return NextResponse.json(
        { error: 'YouTube search service is not available (yt-dlp not found)' },
        { status: 503 }
      )
    }

    // Use yt-dlp to search YouTube with enhanced logging
    const searchResults = await searchYouTube(query.trim(), limit)

    logDebug('INFO', `Search completed successfully`, { 
      query: query.trim(), 
      resultsCount: searchResults.length 
    })

    return NextResponse.json({
      success: true,
      query: query.trim(),
      results: searchResults,
      count: searchResults.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    logDebug('ERROR', 'YouTube search failed', { error: error instanceof Error ? error.message : String(error) })

    // Provide more specific error messages
    let errorMessage = 'Failed to search YouTube'
    let statusCode = 500

    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        errorMessage = 'Search request timed out. Please try again.'
        statusCode = 408
      } else if (error.message.includes('not found')) {
        errorMessage = 'YouTube search service is not available'
        statusCode = 503
      } else {
        errorMessage = `Search failed: ${error.message}`
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: statusCode }
    )
  }
}

// Download and process endpoint (enhanced with duplicate checking)
export async function POST(request: NextRequest) {
  try {
    const downloadRequest: DownloadRequest = await request.json()
    
    if (!downloadRequest.videoId || !downloadRequest.artist || !downloadRequest.title) {
      return NextResponse.json(
        { error: 'videoId, artist, and title are required' },
        { status: 400 }
      )
    }

    logDebug('INFO', `Starting download process`, {
      videoId: downloadRequest.videoId,
      artist: downloadRequest.artist,
      title: downloadRequest.title,
      album: downloadRequest.album,
      year: downloadRequest.year,
      genre: downloadRequest.genre
    })

    // Check for duplicates before downloading
    const duplicateCheck = await prisma.quizTrack.findMany({
      where: {
        AND: [
          { artist: { equals: downloadRequest.artist, mode: 'insensitive' } },
          { title: { equals: downloadRequest.title, mode: 'insensitive' } }
        ]
      }
    })

    if (duplicateCheck.length > 0) {
      logDebug('WARN', `Duplicate track found, aborting download`, {
        existingTracks: duplicateCheck.map(t => ({
          id: t.id,
          artist: t.artist,
          title: t.title,
          mpdFilePath: t.mpdFilePath
        }))
      })
      
      return NextResponse.json({
        success: false,
        error: 'This track already exists in the database',
        duplicates: duplicateCheck.map(t => ({
          id: t.id,
          artist: t.artist,
          title: t.title,
          album: t.album,
          mpdFilePath: t.mpdFilePath
        }))
      }, { status: 409 })
    }

    // Start the download and processing pipeline
    const result = await downloadAndProcessTrack(downloadRequest)

    logDebug('INFO', `Download completed successfully`, {
      trackId: result.id,
      filePath: result.filePath
    })

    return NextResponse.json({
      success: true,
      message: 'Track downloaded and processed successfully',
      track: result
    })

  } catch (error) {
    logDebug('ERROR', 'Download failed', { error: error instanceof Error ? error.message : String(error) })
    return NextResponse.json(
      { error: 'Failed to download track: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    )
  }
}

async function searchYouTube(query: string, limit: number): Promise<YouTubeSearchResult[]> {
  try {
    // Enhanced command with better debugging
    const command = `yt-dlp --quiet --no-warnings --dump-json --flat-playlist --max-downloads=${limit} "ytsearch${limit}:${query}"`
    
    logDebug('INFO', 'Executing yt-dlp search command', { 
      command,
      query,
      limit 
    })

    const { stdout, stderr } = await execAsync(command, {
      timeout: 30000, // 30 second timeout
      maxBuffer: 1024 * 1024 * 10, // 10MB buffer
      env: {
        ...process.env,
        // Add any yt-dlp specific environment variables
        PYTHONIOENCODING: 'utf-8'
      }
    })

    if (stderr && stderr.trim()) {
      logDebug('WARN', 'yt-dlp stderr output', { stderr: stderr.trim() })
    }

    const results: YouTubeSearchResult[] = []
    const lines = stdout.trim().split('\n').filter(line => line.trim())

    logDebug('INFO', `Processing yt-dlp output`, { lineCount: lines.length })

    for (const line of lines) {
      try {
        const data = JSON.parse(line)
        if (data.id && data.title) {
          // Get the best thumbnail
          let thumbnail = `https://img.youtube.com/vi/${data.id}/mqdefault.jpg`
          if (data.thumbnails && data.thumbnails.length > 0) {
            // Find the best quality thumbnail
            const bestThumbnail = data.thumbnails.find(t => t.height >= 300) || data.thumbnails[data.thumbnails.length - 1]
            thumbnail = bestThumbnail.url
          }

          results.push({
            id: data.id,
            title: data.title,
            channel: data.uploader || data.channel || 'Unknown',
            duration: formatDuration(data.duration),
            thumbnail: thumbnail,
            url: `https://www.youtube.com/watch?v=${data.id}`,
            views: data.view_count ? formatViews(data.view_count) : undefined,
            uploadDate: data.upload_date ? formatDate(data.upload_date) : undefined
          })
        }
      } catch (parseError) {
        logDebug('WARN', 'Failed to parse search result line', { 
          line: line.substring(0, 100) + '...',
          error: parseError instanceof Error ? parseError.message : String(parseError)
        })
      }
    }

    logDebug('INFO', `Successfully parsed search results`, { 
      totalResults: results.length,
      query 
    })
    
    return results

  } catch (error) {
    logDebug('ERROR', 'yt-dlp search failed', { 
      error: error instanceof Error ? error.message : String(error),
      query,
      limit
    })

    // Fallback: try with spawn method
    logDebug('INFO', 'Attempting fallback search method')
    return searchYouTubeWithSpawn(query, limit)
  }
}

async function searchYouTubeWithSpawn(query: string, limit: number): Promise<YouTubeSearchResult[]> {
  return new Promise((resolve, reject) => {
    const args = [
      '--quiet',
      '--no-warnings', 
      '--dump-json',
      '--flat-playlist',
      '--max-downloads', String(limit),
      `ytsearch${limit}:${query}`
    ]

    logDebug('INFO', 'Starting yt-dlp fallback search with spawn', { args })

    const process = spawn('yt-dlp', args)
    let outputData = ''
    let errorOutput = ''

    process.stdout.on('data', (data) => {
      outputData += data.toString()
    })

    process.stderr.on('data', (data) => {
      errorOutput += data.toString()
      logDebug('WARN', 'yt-dlp stderr (spawn)', { stderr: data.toString() })
    })

    process.on('close', (code) => {
      logDebug('INFO', `yt-dlp process closed`, { exitCode: code })
      
      if (code !== 0) {
        logDebug('ERROR', 'yt-dlp search failed with spawn', { exitCode: code, stderr: errorOutput })
        reject(new Error(`yt-dlp search failed with code ${code}: ${errorOutput}`))
        return
      }

      try {
        const results: YouTubeSearchResult[] = []
        const lines = outputData.trim().split('\n').filter(line => line.trim())

        for (const line of lines) {
          try {
            const data = JSON.parse(line)
            if (data.id && data.title) {
              results.push({
                id: data.id,
                title: data.title,
                channel: data.uploader || 'Unknown',
                duration: formatDuration(data.duration),
                thumbnail: `https://img.youtube.com/vi/${data.id}/mqdefault.jpg`,
                url: `https://www.youtube.com/watch?v=${data.id}`,
                views: data.view_count ? formatViews(data.view_count) : undefined,
                uploadDate: data.upload_date ? formatDate(data.upload_date) : undefined
              })
            }
          } catch (parseError) {
            logDebug('WARN', 'Failed to parse fallback search result', { line, parseError })
          }
        }

        logDebug('INFO', `Fallback search completed`, { resultCount: results.length })
        resolve(results)
      } catch (error) {
        logDebug('ERROR', 'Failed to process fallback search results', { error })
        reject(error)
      }
    })

    process.on('error', (error) => {
      logDebug('ERROR', 'Failed to execute yt-dlp fallback', { error: error.message })
      reject(new Error('Failed to execute yt-dlp: ' + error.message))
    })
  })
}

async function downloadAndProcessTrack(request: DownloadRequest): Promise<any> {
  const musicDir = process.env.MUSIC_ROOT || '/tmp/music-downloads'
  const tempDir = path.join(musicDir, 'temp')
  
  // Ensure directories exist
  await fs.mkdir(tempDir, { recursive: true })

  const sanitizedFilename = sanitizeFilename(`${request.artist} - ${request.title}`)
  const outputPath = path.join(tempDir, `${sanitizedFilename}.%(ext)s`)
  const finalPath = path.join(musicDir, `${sanitizedFilename}.mp3`)

  logDebug('INFO', 'Starting download and processing', {
    musicDir,
    tempDir,
    sanitizedFilename,
    outputPath,
    finalPath
  })

  try {
    // Step 1: Download audio with yt-dlp (enhanced logging)
    logDebug('INFO', `Downloading audio`, { videoId: request.videoId, outputPath })
    await downloadAudio(request.videoId, outputPath)

    // Step 2: Find the downloaded file
    const downloadedFiles = await fs.readdir(tempDir)
    const audioFile = downloadedFiles.find(file => file.startsWith(sanitizedFilename))
    
    if (!audioFile) {
      throw new Error('Downloaded audio file not found')
    }

    const downloadedPath = path.join(tempDir, audioFile)
    logDebug('INFO', `Found downloaded file`, { audioFile, downloadedPath })

    // Step 3: Convert to MP3 and add metadata
    logDebug('INFO', `Converting to MP3 and adding metadata`)
    await addMetadataAndConvert(downloadedPath, finalPath, request)

    // Step 4: Add to database
    logDebug('INFO', `Adding to database`)
    const dbTrack = await addToDatabase(finalPath, request)

    // Step 5: Sync with MPD
    logDebug('INFO', `Syncing with MPD`)
    await syncWithMPD()

    // Step 6: Clean up temp files
    await fs.unlink(downloadedPath).catch((err) => {
      logDebug('WARN', 'Failed to clean up temp file', { downloadedPath, error: err.message })
    })

    logDebug('INFO', `Successfully processed track`, {
      trackId: dbTrack.id,
      artist: request.artist,
      title: request.title
    })

    return {
      id: dbTrack.id,
      filePath: finalPath,
      artist: request.artist,
      title: request.title,
      album: request.album,
      year: request.year,
      genre: request.genre
    }

  } catch (error) {
    logDebug('ERROR', 'Download and processing failed', { 
      error: error instanceof Error ? error.message : String(error),
      sanitizedFilename
    })

    // Clean up on error
    try {
      const downloadedFiles = await fs.readdir(tempDir)
      for (const file of downloadedFiles) {
        if (file.startsWith(sanitizedFilename)) {
          await fs.unlink(path.join(tempDir, file)).catch(() => {})
        }
      }
    } catch {}

    throw error
  }
}

async function downloadAudio(videoId: string, outputPath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const downloadCommand = [
      'yt-dlp',
      '--extract-audio',
      '--audio-format', 'mp3',
      '--audio-quality', '192K',
      '--output', outputPath,
      '--no-playlist',
      '--no-warnings',
      '--quiet',
      // Enhanced anti-bot detection measures
      '--user-agent', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
      '--referer', 'https://www.youtube.com/',
      '--add-header', 'Accept-Language:en-US,en;q=0.9',
      '--add-header', 'Accept:text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      '--sleep-interval', '3',
      '--max-sleep-interval', '8',
      '--sleep-requests', '2',
      // Rate limiting
      '--limit-rate', '300K',
      // Retry options
      '--retries', '3',
      '--fragment-retries', '3',
      '--retry-sleep', '5',
      // Additional options
      '--no-check-certificate',
      '--prefer-insecure',
      // Format selection - prefer lower quality to avoid bot detection
      '-f', 'bestaudio[ext=m4a]/bestaudio[ext=webm]/bestaudio',
      `https://www.youtube.com/watch?v=${videoId}`
    ]

    logDebug('INFO', 'Starting yt-dlp download', { 
      command: downloadCommand.join(' '),
      videoId,
      outputPath
    })

    const process = spawn(downloadCommand[0], downloadCommand.slice(1))
    let outputData = ''
    let errorOutput = ''

    process.stdout.on('data', (data) => {
      const output = data.toString()
      outputData += output
      if (output.trim()) {
        logDebug('INFO', 'yt-dlp stdout', { output: output.trim() })
      }
    })

    process.stderr.on('data', (data) => {
      const output = data.toString()
      errorOutput += output
      if (output.trim()) {
        logDebug('INFO', 'yt-dlp stderr', { output: output.trim() })
      }
    })

    process.on('close', (code) => {
      logDebug('INFO', 'yt-dlp download completed', { 
        exitCode: code,
        outputLength: outputData.length,
        errorLength: errorOutput.length
      })
      
      if (code !== 0) {
        // Check for common error patterns
        const errorStr = errorOutput.toLowerCase()
        let errorMessage = `Download failed with code ${code}`
        
        if (errorStr.includes('sign in to confirm')) {
          errorMessage = 'YouTube bot detection triggered. Please try again in a few minutes.'
        } else if (errorStr.includes('video unavailable')) {
          errorMessage = 'Video is unavailable or private.'
        } else if (errorStr.includes('copyright')) {
          errorMessage = 'Video blocked due to copyright restrictions.'
        } else if (errorStr.includes('age-restricted')) {
          errorMessage = 'Video is age-restricted and cannot be downloaded.'
        } else if (errorOutput.trim()) {
          errorMessage = `${errorMessage}: ${errorOutput.trim()}`
        }
        
        logDebug('ERROR', 'yt-dlp download failed', { 
          exitCode: code, 
          errorOutput: errorOutput.trim(),
          parsedError: errorMessage
        })
        reject(new Error(errorMessage))
      } else {
        resolve()
      }
    })

    process.on('error', (error) => {
      logDebug('ERROR', 'Failed to execute yt-dlp download', { error: error.message })
      reject(new Error('Failed to execute yt-dlp: ' + error.message))
    })
  })
}

async function addMetadataAndConvert(inputPath: string, outputPath: string, metadata: DownloadRequest): Promise<void> {
  // Use ffmpeg to add metadata and ensure MP3 format
  const ffmpegArgs = [
    '-i', inputPath,
    '-acodec', 'libmp3lame',
    '-ab', '192k',
    '-metadata', `artist=${metadata.artist}`,
    '-metadata', `title=${metadata.title}`,
    '-y' // Overwrite output file
  ]

  if (metadata.album) {
    ffmpegArgs.push('-metadata', `album=${metadata.album}`)
  }
  if (metadata.year) {
    ffmpegArgs.push('-metadata', `date=${metadata.year}`)
  }
  if (metadata.genre) {
    ffmpegArgs.push('-metadata', `genre=${metadata.genre}`)
  }

  ffmpegArgs.push(outputPath)

  logDebug('INFO', 'Starting ffmpeg conversion', { 
    command: `ffmpeg ${ffmpegArgs.map(arg => `"${arg}"`).join(' ')}`,
    inputPath,
    outputPath
  })

  try {
    const { stdout, stderr } = await execAsync(`ffmpeg ${ffmpegArgs.map(arg => `"${arg}"`).join(' ')}`)
    logDebug('INFO', 'FFmpeg conversion completed', { stdout: stdout.trim(), stderr: stderr.trim() })
  } catch (error) {
    logDebug('ERROR', 'FFmpeg conversion failed', { 
      error: error instanceof Error ? error.message : String(error)
    })
    throw new Error('Failed to convert and tag audio: ' + (error instanceof Error ? error.message : 'Unknown error'))
  }
}

async function addToDatabase(filePath: string, metadata: DownloadRequest): Promise<any> {
  // Add the track to your database
  // This should integrate with your existing database schema
  try {
    logDebug('INFO', 'Adding track to database', { filePath, metadata })

    const response = await fetch('/api/quiz/tracks', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: metadata.title,
        artist: metadata.artist,
        album: metadata.album || 'Downloaded',
        year: metadata.year,
        genre: metadata.genre || 'Unknown',
        filePath: filePath,
        source: 'youtube',
        dateAdded: new Date().toISOString()
      })
    })

    if (!response.ok) {
      throw new Error('Failed to add track to database')
    }

    const result = await response.json()
    logDebug('INFO', 'Track added to database successfully', { trackId: result.id })
    return result
  } catch (error) {
    logDebug('ERROR', 'Database insertion failed', { 
      error: error instanceof Error ? error.message : String(error),
      filePath,
      metadata
    })
    throw error
  }
}

async function syncWithMPD(): Promise<void> {
  try {
    logDebug('INFO', 'Triggering MPD database update')
    
    // Trigger MPD database update
    await fetch('/api/mpd/control', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'update' })
    })
    
    logDebug('INFO', 'MPD sync completed successfully')
  } catch (error) {
    logDebug('WARN', 'MPD sync failed', { 
      error: error instanceof Error ? error.message : String(error)
    })
    // Don't throw - this is not critical
  }
}

// Utility functions
function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[<>:"/\\|?*]/g, '_')
    .replace(/\s+/g, ' ')
    .trim()
    .substring(0, 200) // Limit length
}

function formatDuration(seconds: number): string {
  if (!seconds) return 'Unknown'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

function formatViews(views: number): string {
  if (views >= 1000000) {
    return `${(views / 1000000).toFixed(1)}M views`
  } else if (views >= 1000) {
    return `${(views / 1000).toFixed(1)}K views`
  } else {
    return `${views} views`
  }
}

function formatDate(dateString: string): string {
  if (dateString.length === 8) {
    const year = dateString.substring(0, 4)
    const month = dateString.substring(4, 6)
    const day = dateString.substring(6, 8)
    return `${year}-${month}-${day}`
  }
  return dateString
} 