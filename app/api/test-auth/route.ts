import { NextRequest, NextResponse } from 'next/server'
import { <PERSON><PERSON>A<PERSON> } from '@/lib/auth-cookies'
import { AuthService, TokenManager } from '@/lib/auth-service'

export async function GET(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization')
    const cookieHeader = req.headers.get('cookie')
    const authCookie = req.cookies.get('auth_token')?.value
    
    console.log('[test-auth] Debug info:', {
      hasAuthHeader: !!authHeader,
      hasCookieHeader: !!cookieHeader,
      hasAuthCookie: !!authCookie,
      cookieNames: req.cookies.getAll().map(c => c.name)
    })
    
    // Try multiple auth methods
    let user = null
    let authMethod = 'none'
    
    // Method 1: Check auth cookie
    if (authCookie) {
      try {
        const payload = await AuthService.verifyToken(authCookie)
        if (payload) {
          user = payload
          authMethod = 'cookie'
        }
      } catch (e) {
        console.log('[test-auth] Cookie verification failed:', e)
      }
    }
    
    // Method 2: Check Authorization header
    if (!user && authHeader) {
      const token = authHeader.replace('Bearer ', '')
      try {
        const payload = await AuthService.verifyToken(token)
        if (payload) {
          user = payload
          authMethod = 'header'
        }
      } catch (e) {
        console.log('[test-auth] Header token verification failed:', e)
      }
    }
    
    // Method 3: Try CookieAuth
    if (!user) {
      try {
        const cookieUser = await CookieAuth.getAuthFromCookie()
        if (cookieUser) {
          user = cookieUser
          authMethod = 'cookieAuth'
        }
      } catch (e) {
        console.log('[test-auth] CookieAuth failed:', e)
      }
    }
    
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'Not authenticated',
        debug: {
          hasAuthHeader: !!authHeader,
          hasAuthCookie: !!authCookie,
          cookieNames: req.cookies.getAll().map(c => c.name),
          authMethod
        }
      }, { status: 401 })
    }
    
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      },
      authMethod,
      debug: {
        hasAuthHeader: !!authHeader,
        hasAuthCookie: !!authCookie,
        cookieNames: req.cookies.getAll().map(c => c.name)
      }
    })
  } catch (error) {
    console.error('[test-auth] Error:', error)
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}