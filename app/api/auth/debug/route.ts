import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/middleware/auth-middleware'
import { AuthService } from '@/lib/auth-service'

// Debug endpoint to check authentication status
export const GET = async (req: NextRequest) => {
  try {
    // Get token from header or cookie
    const authHeader = req.headers.get('authorization')
    const cookieToken = req.cookies.get('auth_token')?.value
    const token = authHeader?.replace('Bearer ', '') || cookieToken

    const debugInfo = {
      hasAuthHeader: !!authHeader,
      hasCookieToken: !!cookieToken,
      hasToken: !!token,
      tokenLength: token?.length || 0,
      cookies: req.cookies.getAll().map(c => ({ name: c.name, hasValue: !!c.value })),
      headers: {
        authorization: authHeader ? 'present' : 'missing',
        cookie: req.headers.get('cookie') ? 'present' : 'missing'
      }
    }

    if (token) {
      try {
        const decoded = await AuthService.verifyToken(token)
        return NextResponse.json({
          authenticated: true,
          user: decoded,
          debugInfo
        })
      } catch (error) {
        return NextResponse.json({
          authenticated: false,
          error: 'Invalid token',
          message: error.message,
          debugInfo
        })
      }
    }

    return NextResponse.json({
      authenticated: false,
      error: 'No token found',
      debugInfo
    })
  } catch (error) {
    return NextResponse.json({
      authenticated: false,
      error: 'Debug check failed',
      message: error.message
    })
  }
}