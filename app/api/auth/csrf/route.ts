import { NextResponse } from 'next/server'
import { CSRFProtection } from '@/lib/middleware/csrf'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const token = await CSRFProtection.getClientToken()
    
    const response = NextResponse.json({
      success: true,
      csrfToken: token
    })
    
    // Add CORS headers for VPN/cross-origin access
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set('Access-Control-Allow-Credentials', 'true')
    
    return response
  } catch (error) {
    console.error('CSRF token generation error:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to generate CSRF token'
    }, { status: 500 })
  }
}