import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { AuthUtils } from '@/lib/database/prisma'
import { AuthService } from '@/lib/auth-service'
import { CookieAuth } from '@/lib/auth-cookies'
import { authRateLimiter } from '@/lib/middleware/rate-limiter'
import { CSRFProtection } from '@/lib/middleware/csrf'

export const dynamic = 'force-dynamic'

// Password validation regex - at least 8 chars, 1 uppercase, 1 lowercase, 1 number
const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/

export async function POST(req: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await authRateLimiter(req)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate CSRF token
    if (!CSRFProtection.validateCSRFToken(req)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid or missing CSRF token',
        code: 'CSRF_ERROR'
      }, { status: 403 })
    }
    const { email, password, displayName } = await req.json()

    if (!email || !password || !displayName) {
      return NextResponse.json({
        success: false,
        message: 'Email, password, and display name are required'
      }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid email format'
      }, { status: 400 })
    }

    // Validate password strength
    if (!PASSWORD_REGEX.test(password)) {
      return NextResponse.json({
        success: false,
        message: 'Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number'
      }, { status: 400 })
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    })

    if (existingUser) {
      return NextResponse.json({
        success: false,
        message: 'An account with this email already exists'
      }, { status: 409 })
    }

    // Extract username from email
    const username = email.split('@')[0].toLowerCase()

    // Hash the password
    const hashedPassword = await AuthUtils.hashPassword(password)

    // Create the user
    const newUser = await prisma.user.create({
      data: {
        email: email.toLowerCase(),
        username,
        displayName,
        password: hashedPassword,
        role: 'user',
        preferences: JSON.stringify({
          theme: 'system',
          difficulty: 3,
          volume: 70,
          autoPlay: true,
          showHints: true,
          soundEffects: true
        })
      }
    })

    // Generate token for auto-login
    const authUser = {
      id: newUser.id,
      email: newUser.email,
      username: newUser.username,
      displayName: newUser.displayName || undefined,
      role: newUser.role as 'user' | 'admin' | 'guest' | 'moderator',
      avatarUrl: newUser.avatarUrl || undefined,
      preferences: newUser.preferences,
      createdAt: newUser.createdAt
    }

    // Set httpOnly cookie for secure token storage
    await CookieAuth.setAuthCookie({
      id: authUser.id,
      email: authUser.email,
      username: authUser.username,
      role: authUser.role as 'user' | 'dj' | 'superuser'
    })

    return NextResponse.json({
      success: true,
      user: authUser,
      message: 'Registration successful'
    })
  } catch (error) {
    console.error('Registration API error:', error)
    return NextResponse.json({
      success: false,
      message: 'Registration failed. Please try again.'
    }, { status: 500 })
  }
}