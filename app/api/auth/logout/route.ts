import { NextResponse } from 'next/server'
import { CookieAuth } from '@/lib/auth-cookies'

export const dynamic = 'force-dynamic'

export async function POST() {
  try {
    // Clear authentication cookies
    await CookieAuth.clearAuthCookies()

    return NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    })
  } catch (error) {
    console.error('Logout error:', error)
    return NextResponse.json({
      success: false,
      message: 'Logout failed'
    }, { status: 500 })
  }
}