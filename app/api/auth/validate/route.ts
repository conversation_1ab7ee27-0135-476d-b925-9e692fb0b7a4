import { NextRequest, NextResponse } from 'next/server'
import { <PERSON><PERSON>A<PERSON> } from '@/lib/auth-cookies'
import { AuthService } from '@/lib/auth-service'

export const dynamic = 'force-dynamic'

export async function OPTIONS(req: NextRequest) {
  const response = new NextResponse(null, { status: 200 })
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type')
  response.headers.set('Access-Control-Allow-Credentials', 'true')
  return response
}

export async function POST(req: NextRequest) {
  try {
    // Get auth from cookie
    const tokenPayload = await CookieAuth.getAuthFromCookie()
    
    if (!tokenPayload) {
      return NextResponse.json({
        success: false,
        message: 'No valid session found'
      }, { status: 401 })
    }

    // Get fresh user data from database
    const user = await AuthService.refreshUserData(tokenPayload.id)
    
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'User not found'
      }, { status: 404 })
    }

    // Check if user data has changed and update cookie if needed
    if (user.role !== tokenPayload.role || user.username !== tokenPayload.username) {
      await CookieAuth.setAuthCookie({
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role
      })
    }

    const response = NextResponse.json({
      success: true,
      user,
      message: 'Session validated successfully'
    })
    
    // Add CORS headers for VPN/cross-origin access
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set('Access-Control-Allow-Credentials', 'true')
    
    return response
  } catch (error) {
    console.error('Session validation API error:', error)
    return NextResponse.json({
      success: false,
      message: 'Session validation failed'
    }, { status: 500 })
  }
} 