import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth-service'
import { CookieA<PERSON> } from '@/lib/auth-cookies'
import { authRateLimiter } from '@/lib/middleware/rate-limiter'
import { CSRFProtection } from '@/lib/middleware/csrf'

export const dynamic = 'force-dynamic'

export async function OPTIONS(req: NextRequest) {
  const response = new NextResponse(null, { status: 200 })
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, x-csrf-token')
  response.headers.set('Access-Control-Allow-Credentials', 'true')
  return response
}

export async function POST(req: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await authRateLimiter(req)
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    // Validate CSRF token
    if (!CSRFProtection.validateCSRFToken(req)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid or missing CSRF token',
        code: 'CSRF_ERROR'
      }, { status: 403 })
    }
    const body = await req.json()
    // Support both 'email' and 'username' fields for backwards compatibility
    const loginId = body.email || body.username || body.loginId
    const password = body.password

    if (!loginId || !password) {
      return NextResponse.json({
        success: false,
        message: 'Username and password are required'
      }, { status: 400 })
    }

    const result = await AuthService.authenticate(loginId, password)
    
    if (!result) {
      return NextResponse.json({
        success: false,
        message: 'Invalid username or password'
      }, { status: 401 })
    }

    // Set httpOnly cookie for secure token storage
    await CookieAuth.setAuthCookie({
      id: result.user.id,
      email: result.user.email,
      username: result.user.username,
      role: result.user.role
    })

    // Return user data without token (token is in httpOnly cookie)
    const response = NextResponse.json({
      success: true,
      user: result.user,
      message: 'Login successful'
    })
    
    // Add CORS headers for VPN/cross-origin access
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set('Access-Control-Allow-Credentials', 'true')
    
    return response
  } catch (error) {
    console.error('Login API error:', error)
    return NextResponse.json({
      success: false,
      message: 'Login failed. Please try again.'
    }, { status: 500 })
  }
} 