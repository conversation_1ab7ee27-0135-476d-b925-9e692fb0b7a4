import { NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { validateToken } from '@/lib/auth-service'
import { <PERSON>ieA<PERSON> } from '@/lib/auth-cookies'
import { cookies } from 'next/headers'

export async function GET(request: Request) {
  try {
    // Try to get auth from cookie first (the primary auth method)
    const authUser = await CookieAuth.getAuthFromCookie()
    
    if (!authUser) {
      // Fallback to Authorization header if no cookie
      const authHeader = request.headers.get('authorization')
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7)
        const { AuthService } = await import('@/lib/auth-service')
        const headerAuthUser = await AuthService.verifyToken(token)
        
        if (!headerAuthUser) {
          return NextResponse.json({
            success: false,
            error: 'Unauthorized'
          }, { status: 401 })
        }
        // Use the auth from header
        Object.assign(authUser || {}, headerAuthUser)
      } else {
        return NextResponse.json({
          success: false,
          error: 'Unauthorized'
        }, { status: 401 })
      }
    }

    const user = await prisma.user.findUnique({
      where: { id: authUser.id },
      select: {
        id: true,
        username: true,
        displayName: true,
        email: true,
        role: true,
        avatarUrl: true,
        createdAt: true,
        lastActive: true,
        preferences: true
      }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 })
    }

    // Parse preferences JSON and extract favoriteGenres and location
    let preferences = {}
    try {
      preferences = JSON.parse(user.preferences || '{}')
    } catch (e) {
      preferences = {}
    }

    // Return user with favoriteGenres and location from preferences
    const userWithExtras = {
      ...user,
      favoriteGenres: preferences.favoriteGenres || [],
      location: preferences.location || '',
      settings: preferences.settings || {},
      avatar: user.avatarUrl || 'rockstar' // Default avatar
    }

    return NextResponse.json({
      success: true,
      user: userWithExtras
    })
  } catch (error) {
    console.error('Profile fetch error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function PUT(request: Request) {
  try {
    // Try to get auth from cookie first (the primary auth method)
    let authUser = await CookieAuth.getAuthFromCookie()
    
    if (!authUser) {
      // Fallback to Authorization header if no cookie
      const authHeader = request.headers.get('authorization')
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7)
        const { AuthService } = await import('@/lib/auth-service')
        const headerAuthUser = await AuthService.verifyToken(token)
        
        if (!headerAuthUser) {
          return NextResponse.json({
            success: false,
            error: 'Unauthorized'
          }, { status: 401 })
        }
        // Use the auth from header
        authUser = headerAuthUser
      } else {
        return NextResponse.json({
          success: false,
          error: 'Unauthorized'
        }, { status: 401 })
      }
    }

    const body = await request.json()
    const { username, displayName, avatarUrl, favoriteGenres, location } = body

    // Validate required fields
    if (!username || !displayName) {
      return NextResponse.json({
        success: false,
        error: 'Username and display name are required'
      }, { status: 400 })
    }

    // Check if username is already taken by another user
    if (username !== authUser.username) {
      const existingUser = await prisma.user.findFirst({
        where: {
          username: { equals: username, mode: 'insensitive' },
          id: { not: authUser.id }
        }
      })

      if (existingUser) {
        return NextResponse.json({
          success: false,
          error: 'Username already taken'
        }, { status: 400 })
      }
    }

    // Get current preferences
    const currentUser = await prisma.user.findUnique({
      where: { id: authUser.id },
      select: { preferences: true }
    })

    let currentPreferences = {}
    try {
      currentPreferences = JSON.parse(currentUser?.preferences || '{}')
    } catch (e) {
      currentPreferences = {}
    }

    // Update preferences with new values
    const updatedPreferences = {
      ...currentPreferences,
      favoriteGenres: favoriteGenres || currentPreferences.favoriteGenres || [],
      location: location || currentPreferences.location || ''
    }

    const updatedUser = await prisma.user.update({
      where: { id: authUser.id },
      data: {
        username,
        displayName,
        avatarUrl: avatarUrl || undefined,
        preferences: JSON.stringify(updatedPreferences),
        lastActive: new Date()
      },
      select: {
        id: true,
        username: true,
        displayName: true,
        email: true,
        role: true,
        avatarUrl: true,
        createdAt: true,
        lastActive: true,
        preferences: true
      }
    })

    // Parse preferences for response
    let preferences = {}
    try {
      preferences = JSON.parse(updatedUser.preferences || '{}')
    } catch (e) {
      preferences = {}
    }

    // Return user with favoriteGenres and location from preferences
    const userWithExtras = {
      ...updatedUser,
      favoriteGenres: preferences.favoriteGenres || [],
      location: preferences.location || '',
      settings: preferences.settings || {},
      avatar: updatedUser.avatarUrl || 'rockstar'
    }

    return NextResponse.json({
      success: true,
      user: userWithExtras,
      message: 'Profile updated successfully'
    })
  } catch (error) {
    console.error('Profile update error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}