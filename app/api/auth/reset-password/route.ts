import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/database/prisma';
import { AuthUtils } from '@/lib/database/prisma';
import { passwordResetRateLimit } from '@/lib/middleware/rate-limiter';
import { UserSessionService } from '@/lib/services/session-service';

// Password validation regex - at least 8 chars, 1 uppercase, 1 lowercase, 1 number
const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await passwordResetRateLimit(request);
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    const body = await request.json();
    const { email, newPassword, adminToken } = body;

    // Validate input
    if (!email || !newPassword) {
      return NextResponse.json({
        success: false,
        message: 'Email and new password are required'
      }, { status: 400 });
    }

    // Validate password strength
    if (!PASSWORD_REGEX.test(newPassword)) {
      return NextResponse.json({
        success: false,
        message: 'Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number'
      }, { status: 400 });
    }

    // Check for admin token - REQUIRED for security
    // Only allow password reset if valid admin token is provided
    if (!process.env.ADMIN_RESET_TOKEN) {
      console.error('[Auth] ADMIN_RESET_TOKEN not configured');
      return NextResponse.json({
        success: false,
        message: 'Password reset is not configured'
      }, { status: 503 });
    }

    if (adminToken !== process.env.ADMIN_RESET_TOKEN) {
      console.warn(`[Auth] Invalid password reset attempt for ${email}`);
      return NextResponse.json({
        success: false,
        message: 'Invalid admin token'
      }, { status: 403 });
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });

    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'User not found'
      }, { status: 404 });
    }

    // Hash the new password
    const hashedPassword = await AuthUtils.hashPassword(newPassword);

    // Update user password
    await prisma.user.update({
      where: { id: user.id },
      data: { 
        password: hashedPassword,
        updatedAt: new Date()
      }
    });

    // Invalidate all user sessions to force re-login
    await UserSessionService.endActiveSessions(user.id);
    
    // Log the password reset for security auditing
    console.log(`[Auth] Password reset for user: ${email} (ID: ${user.id})`);
    
    // In production, you should also:
    // 1. Send an email notification to the user
    // 2. Log this event in an audit table
    // 3. Clear any password reset tokens if using email-based reset

    return NextResponse.json({
      success: true,
      message: 'Password reset successfully',
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.displayName,
        role: user.role
      }
    });

  } catch (error) {
    console.error('[Auth] Password reset error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to reset password'
    }, { status: 500 });
  }
}

// GET endpoint to check if password reset is available
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Password reset endpoint is available',
    usage: {
      method: 'POST',
      body: {
        email: '<EMAIL>',
        newPassword: 'newPassword123',
        adminToken: 'music-quiz-admin-2024'
      }
    }
  });
}