import { NextRequest, NextResponse } from 'next/server'
import { MPDEnhancedClient } from '@/lib/mpd-enhanced-client'
import { getMpdConnectionConfig } from '@/lib/env'

export async function POST(req: NextRequest) {
  try {
    const { value } = await req.json()
    const crossfadeValue = value !== undefined ? value : 3
    
    const config = getMpdConnectionConfig()
    const client = new MPDEnhancedClient(config)
    
    try {
      await client.connect()
      
      console.log('[Crossfade Test] Setting crossfade to:', crossfadeValue)
      
      // Try setting crossfade
      await client.sendCommand(`crossfade ${crossfadeValue}`)
      
      // Get status to verify
      const statusResponse = await client.sendCommand('status')
      console.log('[Crossfade Test] Status after setting:', statusResponse)
      
      // Also try getting crossfade value directly
      const crossfade = await client.getCrossfade()
      console.log('[Crossfade Test] Crossfade value from getCrossfade:', crossfade)
      
      await client.disconnect()
      
      return NextResponse.json({
        success: true,
        requestedValue: crossfadeValue,
        actualValue: crossfade,
        statusResponse
      })
    } catch (mpdError) {
      console.error('[Crossfade Test] MPD error:', mpdError)
      await client.disconnect()
      throw mpdError
    }
  } catch (error) {
    console.error('[Crossfade Test] Failed:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to test crossfade',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}