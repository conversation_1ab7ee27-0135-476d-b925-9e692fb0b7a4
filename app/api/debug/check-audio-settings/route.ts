import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/database/prisma'

export async function GET(req: NextRequest) {
  try {
    // Get all users with preferences
    const users = await prisma.user.findMany({
      select: {
        id: true,
        displayName: true,
        email: true,
        role: true,
        preferences: true
      }
    })
    
    const usersWithAudioSettings = users.map(user => {
      const prefs = JSON.parse(user.preferences || '{}')
      return {
        id: user.id,
        displayName: user.displayName,
        email: user.email,
        role: user.role,
        audioEnhancements: prefs.audioEnhancements || null
      }
    }).filter(user => user.audioEnhancements !== null)
    
    return NextResponse.json({
      success: true,
      totalUsersWithPreferences: users.length,
      usersWithAudioSettings: usersWithAudioSettings
    })
  } catch (error) {
    console.error('[Debug] Failed to check audio settings:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to check audio settings',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}