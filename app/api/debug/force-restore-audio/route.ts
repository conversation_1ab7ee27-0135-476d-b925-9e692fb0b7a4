import { NextRequest, NextResponse } from 'next/server'
import { MPDEnhancedClient } from '@/lib/mpd-enhanced-client'
import { getMpdConnectionConfig } from '@/lib/env'
import { MPDSettingsPersistence } from '@/lib/services/mpd-settings-persistence'

export async function POST(req: NextRequest) {
  try {
    const { userId } = await req.json()
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID required' },
        { status: 400 }
      )
    }
    
    const config = getMpdConnectionConfig()
    const client = new MPDEnhancedClient(config)
    const persistence = MPDSettingsPersistence.getInstance()
    
    try {
      await client.connect()
      
      // Get saved settings from database
      const savedSettings = await persistence.loadSettingsFromDatabase(userId)
      
      if (!savedSettings) {
        return NextResponse.json({
          success: false,
          message: 'No saved settings found for user'
        })
      }
      
      console.log('[Force Restore] Found saved settings:', savedSettings)
      
      // Force apply the settings to MPD
      await client.configureAudioEnhancements({
        crossfade: savedSettings.crossfade,
        replayGain: savedSettings.replayGain
      })
      
      // Verify they were applied
      const currentSettings = await client.getAudioEnhancements()
      
      console.log('[Force Restore] Settings after restoration:', currentSettings)
      
      await client.disconnect()
      
      return NextResponse.json({
        success: true,
        message: 'Settings forcefully restored',
        savedSettings,
        currentSettings,
        applied: {
          crossfadeMatch: savedSettings.crossfade === currentSettings.crossfade,
          replayGainMatch: savedSettings.replayGain.mode === currentSettings.replayGain.mode
        }
      })
    } catch (mpdError) {
      console.error('[Force Restore] MPD error:', mpdError)
      await client.disconnect()
      throw mpdError
    }
  } catch (error) {
    console.error('[Force Restore] Failed:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to restore settings',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}