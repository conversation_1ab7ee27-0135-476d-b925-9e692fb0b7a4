import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { FilterCache } from '@/lib/cache/cache-helpers'

export async function GET(request: NextRequest) {
  try {
    // Try to get from cache first
    const cachedFilters = await FilterCache.getFilters()
    if (cachedFilters) {
      return NextResponse.json(cachedFilters)
    }

    // Get unique genres
    const genresResult = await prisma.quizTrack.findMany({
      select: { genre: true },
      distinct: ['genre'],
      where: { 
        genre: { 
          not: null,
          notIn: ['Unknown', '']
        } 
      },
      orderBy: { genre: 'asc' }
    })
    const genres = genresResult
      .map(r => r.genre)
      .filter(Boolean) as string[]

    // Get unique years
    const yearsResult = await prisma.quizTrack.findMany({
      select: { year: true },
      distinct: ['year'],
      where: { 
        year: { 
          not: null,
          gt: 1900
        } 
      },
      orderBy: { year: 'desc' }
    })
    const years = yearsResult
      .map(r => r.year?.toString())
      .filter(Boolean) as string[]

    // Get unique artists (limit to top 500 by track count for performance)
    const artistsResult = await prisma.quizTrack.groupBy({
      by: ['artist'],
      where: {
        artist: {
          not: null,
          notIn: ['Unknown Artist', 'Unknown', '']
        }
      },
      _count: {
        artist: true
      },
      orderBy: {
        _count: {
          artist: 'desc'
        }
      },
      take: 500
    })
    const artists = artistsResult
      .map(r => r.artist)
      .filter(Boolean) as string[]
    
    // Sort artists alphabetically after getting top 500
    artists.sort((a, b) => a.localeCompare(b))

    // Get unique quiz categories
    const categoriesSet = new Set<string>()
    const categoryResults = await prisma.quizTrack.findMany({
      select: { quizCategories: true },
      where: {
        quizCategories: {
          not: null,
          not: '[]'
        }
      }
    })
    
    categoryResults.forEach(result => {
      if (result.quizCategories) {
        try {
          const cats = JSON.parse(result.quizCategories)
          if (Array.isArray(cats)) {
            cats.forEach(cat => {
              if (cat && typeof cat === 'string') {
                categoriesSet.add(cat)
              }
            })
          }
        } catch (e) {
          // Skip invalid JSON
        }
      }
    })
    
    const categories = Array.from(categoriesSet).sort()

    const filters = {
      genres,
      years,
      artists,
      categories
    }

    // Cache the results
    await FilterCache.setFilters(filters, 3600) // Cache for 1 hour

    return NextResponse.json(filters)
  } catch (error) {
    console.error("Failed to fetch library filters:", error)
    return NextResponse.json({
      genres: [],
      years: [],
      artists: [],
      categories: []
    }, { status: 500 })
  }
}