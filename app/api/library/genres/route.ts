import { NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    // Get unique genres from database
    const genresResult = await prisma.quizTrack.groupBy({
      by: ['genre'],
      _count: { genre: true },
      where: { 
        genre: { 
          not: null,
          not: ''
        }
      },
      orderBy: { _count: { genre: 'desc' } }
    })

    const genres = genresResult
      .map(g => g.genre)
      .filter(Boolean)
      .filter(genre => genre.trim() !== '')

    // Add special categories to the genres list
    const specialCategories = ['all-time-favorites', 'MyItunes']
    const allGenresAndCategories = [...new Set([...specialCategories, ...genres])]

    console.log(`[Genres API] Found ${genres.length} unique genres and ${specialCategories.length} special categories`)

    return NextResponse.json({
      success: true,
      genres: allGenresAndCategories,
      count: allGenresAndCategories.length,
      genresWithCounts: [
        // Add special categories with placeholder counts
        { genre: 'all-time-favorites', count: 0 },
        { genre: 'MyItunes', count: 0 },
        // Add regular genres with actual counts
        ...genresResult.map(g => ({
          genre: g.genre,
          count: g._count.genre
        }))
      ]
    })

  } catch (error) {
    console.error('[Genres API] Failed to fetch genres:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch genres',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}