import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { getMpdConnectionConfig } from '@/lib/env'
import { MPDClient } from '@/lib/mpd-client'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    // Get pagination parameters from query string
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const skip = (page - 1) * limit

    console.log(`[Favorites API] Fetching favorite songs... Page: ${page}, Limit: ${limit}`)
    
    // Get total count for pagination info
    const totalCount = await prisma.userFavorite.count()
    
    // First try to get favorites from database with pagination
    const dbFavorites = await prisma.userFavorite.findMany({
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' }
    })

    let favorites = dbFavorites.map(fav => ({
      id: fav.id,
      title: fav.title,
      artist: fav.artist,
      album: fav.album,
      year: fav.year,
      genre: fav.genre,
      duration: fav.duration,
      filePath: fav.filePath,
      albumArtUrl: fav.albumArtUrl
    }))

    // If we don't have many favorites in DB, try MPD favorites playlist
    if (favorites.length < 10) {
      try {
        const config = getMpdConnectionConfig()
        if (config && config.host) {
          const mpdClient = new MPDClient(config)
          await mpdClient.connect()
          
          try {
            // Try to get favorites playlist
            const mpdFavorites = await mpdClient.getPlaylistSongs('favorites')
            console.log(`[Favorites API] Found ${mpdFavorites.length} songs in MPD favorites playlist`)
            
            // Convert MPD songs to our format
            const mpdFormattedFavorites = mpdFavorites.map(song => ({
              id: song.id || Math.random().toString(),
              title: song.title || 'Unknown Title',
              artist: song.artist || 'Unknown Artist',
              album: song.album || 'Unknown Album',
              year: song.year || (song.date ? parseInt(song.date.toString()) : null),
              genre: song.genre || null,
              duration: song.duration || song.time || 0,
              filePath: song.file,
              albumArtUrl: song.albumArtUrl || null
            }))
            
            // Combine DB favorites with MPD favorites (remove duplicates)
            const existingPaths = new Set(favorites.map(f => f.filePath).filter(Boolean))
            const newMpdFavorites = mpdFormattedFavorites.filter(song => 
              song.filePath && !existingPaths.has(song.filePath)
            )
            
            favorites = [...favorites, ...newMpdFavorites]
            
          } catch (mpdError) {
            console.log('[Favorites API] No MPD favorites playlist found or error accessing it')
          }
          
          await mpdClient.disconnect()
        }
      } catch (mpdError) {
        console.log('[Favorites API] MPD connection failed, using DB favorites only')
      }
    }

    console.log(`[Favorites API] Returning ${favorites.length} favorite songs (Page ${page} of ${Math.ceil(totalCount / limit)})`)

    return NextResponse.json({
      success: true,
      favorites,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasMore: skip + favorites.length < totalCount
      }
    })

  } catch (error) {
    console.error('[Favorites API] Failed to fetch favorites:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch favorite songs',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}