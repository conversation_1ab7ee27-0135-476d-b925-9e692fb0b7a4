import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET() {
  try {
    // Get all unique folders from quiz categories and file paths
    const tracks = await prisma.quizTrack.findMany({
      select: {
        quizCategories: true,
        mpdFilePath: true
      }
    })

    const folderSet = new Set<string>()

    tracks.forEach(track => {
      // Extract folders from quiz categories
      if (track.quizCategories) {
        try {
          const categories = JSON.parse(track.quizCategories) as string[]
          categories.forEach(category => {
            // Add meaningful categories that represent collections or folders
            if (category && category.trim()) {
              const cleanCategory = category.trim()
              // Include various folder-like patterns commonly found in music collections
              if (cleanCategory.match(/^(DC\d{4}|Billboard\d{4}|UK\d{4}|Top\d{4}|Milestones|All Time|MyItunes|Kontor|Chart|Hit|Collection|Compilation|Best Of|Greatest|Ultimate|Essential|Classic|Decade|Year \d{4}|\d{4}s|[A-Za-z][A-Za-z0-9\s\._-]{2,})$/)) {
                folderSet.add(cleanCategory)
              }
            }
          })
        } catch {
          // Ignore parsing errors
        }
      }

      // Extract folders from file path (both top-level and meaningful sub-folders)
      if (track.mpdFilePath) {
        const pathParts = track.mpdFilePath.split('/')
        
        // Add top-level folder
        if (pathParts.length > 0 && pathParts[0]) {
          folderSet.add(pathParts[0])
        }
        
        // Also add meaningful second-level folders that represent collections
        if (pathParts.length > 1 && pathParts[1]) {
          const secondLevel = pathParts[1]
          // Include common music collection patterns
          if (secondLevel.match(/^(All Time|Top \d+|Best Of|Greatest|Chart|Hit|Collection|Compilation|Ultimate|Essential|Classic|Favorites|\d{4}s?|Vol\.|Volume|CD\d+|Disc\d+)/i)) {
            folderSet.add(secondLevel)
          }
        }
      }
    })

    // Convert to sorted array, filtering out only truly generic/unwanted folder names
    const folders = Array.from(folderSet)
      .filter(folder => {
        const folderLower = folder.toLowerCase()
        return (
          folder.length > 1 && 
          // Allow dots - they're common in music folder names (e.g., Vol.100)
          // Only filter out clearly unwanted patterns
          !folderLower.includes('unknown') &&
          !folderLower.startsWith('.') && // Hidden folders
          folder !== '.' &&
          folder !== '..' &&
          // Filter out obvious file extensions that got mistaken for folders
          !folder.match(/\.(mp3|flac|wav|m4a|aac|ogg)$/i) &&
          // Filter out obviously broken/invalid folder names
          !folder.match(/^[^a-zA-Z0-9].*[^a-zA-Z0-9]$/) &&
          folder.trim() === folder // No leading/trailing whitespace
        )
      })
      .sort((a, b) => {
        // Sort with special handling for numbered series (e.g., Vol.1, Vol.2, Vol.10)
        const aMatch = a.match(/(.*)(Vol|Volume|Part|CD)[\.\s]*(\d+)/i)
        const bMatch = b.match(/(.*)(Vol|Volume|Part|CD)[\.\s]*(\d+)/i)
        
        if (aMatch && bMatch && aMatch[1] === bMatch[1]) {
          // Same series, sort by number
          return parseInt(aMatch[3]) - parseInt(bMatch[3])
        }
        
        // Regular alphabetical sort
        return a.localeCompare(b, undefined, { numeric: true, caseFirst: 'lower' })
      })

    return NextResponse.json({
      success: true,
      folders
    })
  } catch (error) {
    console.error('Failed to fetch folders:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch folders',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}