import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const category = searchParams.get('category')
    const limit = parseInt(searchParams.get('limit') || '10')
    const shuffle = searchParams.get('shuffle') === 'true'

    if (!category) {
      return NextResponse.json({ 
        success: false, 
        error: 'Category parameter is required' 
      }, { status: 400 })
    }

    // Build the query
    const whereClause: any = {}
    
    if (category !== 'all') {
      if (category === 'all-time-favorites') {
        // For all-time-favorites, check the quizCategories JSON field
        whereClause.OR = [
          {
            quizCategories: {
              contains: 'all-time-favorites'
            }
          }
        ]
      } else {
        // For other categories, check the quizCategories JSON field
        whereClause.quizCategories = {
          contains: `"${category}"`
        }
      }
    }

    // Get total count
    const totalCount = await prisma.quizTrack.count({
      where: whereClause
    })

    if (totalCount === 0) {
      return NextResponse.json({ 
        success: false, 
        error: `No songs found in category: ${category}`,
        songs: [] 
      })
    }

    // Fetch songs with random ordering if shuffle is enabled
    let songs
    if (shuffle) {
      // For shuffled results, we need to get all matching songs and then randomly select
      // This ensures we get exactly the number requested
      const allSongs = await prisma.quizTrack.findMany({
        where: whereClause,
        select: {
          id: true,
          fileFingerprint: true,
          mpdFilePath: true,
          title: true,
          artist: true,
          album: true,
          duration: true,
          genre: true,
          year: true,
          quizCategories: true
        }
      })
      
      // Fisher-Yates shuffle algorithm for better randomization
      const shuffled = [...allSongs]
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
      }
      
      // Take only the requested number of songs
      songs = shuffled.slice(0, Math.min(limit, shuffled.length))
    } else {
      // Get songs without shuffle
      songs = await prisma.quizTrack.findMany({
        where: whereClause,
        take: Math.min(limit, totalCount),
        orderBy: { title: 'asc' },
        select: {
          id: true,
          fileFingerprint: true,
          mpdFilePath: true,
          title: true,
          artist: true,
          album: true,
          duration: true,
          genre: true,
          year: true,
          quizCategories: true
        }
      })
    }

    // Map to the expected format
    const formattedSongs = songs.map(track => ({
      id: track.fileFingerprint || track.id,
      fingerprint: track.fileFingerprint,
      title: track.title,
      artist: track.artist,
      album: track.album || '',
      duration: track.duration,
      filePath: track.mpdFilePath,
      file: track.mpdFilePath,
      genre: track.genre || '',
      year: track.year || 0,
      votes: 0,
      suggested: false,
      addedBy: 'Library',
      addedAt: Date.now(),
      categories: track.quizCategories || []
    }))

    return NextResponse.json({ 
      success: true, 
      songs: formattedSongs,
      total: totalCount,
      returned: formattedSongs.length,
      category: category
    })

  } catch (error) {
    console.error('Error fetching category songs:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to fetch songs from category' 
    }, { status: 500 })
  }
}