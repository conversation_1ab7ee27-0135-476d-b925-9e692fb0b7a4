import { NextRequest, NextResponse } from 'next/server'
import { MPDClient } from '@/lib/mpd-client'
import { getAudioConfig } from '@/lib/env'
import { mpdControlSchema, validateRequest } from '@/lib/validations/api-schemas'
import { RateLimitService } from '@/lib/services/rate-limit-service'
import { getAuthUser, hasRole } from '@/lib/middleware/get-auth-user'

export const dynamic = 'force-dynamic'

// POST control playback
export const POST = async (req: NextRequest) => {
  // Get authenticated user from middleware headers
  const user = getAuthUser(req)
  
  // This shouldn't happen if middleware is working correctly
  if (!user) {
    console.error('[Control API] No user found in headers - middleware might have failed')
    return NextResponse.json(
      { success: false, message: 'Authentication required' },
      { status: 401 }
    )
  }
  // Rate limit: 60 actions per minute per user (increased for smoother volume control)
  const rateLimiter = RateLimitService.getInstance()
  const limited = await rateLimiter.checkLimit(
    `mpd-control:${user.id}`,
    60,
    60
  )
  
  if (!limited.allowed) {
    return NextResponse.json(
      { success: false, message: 'Rate limit exceeded' },
      { status: 429, headers: { 'Retry-After': Math.ceil((limited.reset - Date.now()) / 1000).toString() } }
    )
  }

  try {
    const body = await req.json()
    
    // Validate input
    const validation = await validateRequest(mpdControlSchema, body)
    if (!validation.success) {
      return NextResponse.json({ 
        success: false, 
        message: 'Invalid request',
        errors: validation.errors.flatten()
      }, { status: 400 })
    }

    const { action, volume, seek } = validation.data
    
    console.log('[MPD Control] Received command:', { action, volume, seek, user: user.email })

    // Role-based permissions: only admin/superuser/dj can stop playback
    if (action === 'stop' && !['admin', 'superuser', 'dj'].includes(user.role)) {
      return NextResponse.json(
        { success: false, message: 'Insufficient permissions to stop playback' },
        { status: 403 }
      )
    }

    const config = getAudioConfig()
    const mpdClient = new MPDClient({
      host: config.mpdHost,
      port: config.mpdPort,
      password: config.mpdPassword,
      httpProxyPort: config.mpdHttpPort
    })

    await mpdClient.connect()

    let result
    switch (action) {
      case 'play':
        console.log('[MPD Control] Executing play command...')
        result = await mpdClient.play()
        console.log('[MPD Control] Play command completed')
        break
      case 'pause':
        console.log('[MPD Control] Executing pause command...')
        result = await mpdClient.pause()
        console.log('[MPD Control] Pause command completed')
        break
      case 'stop':
        result = await mpdClient.stop()
        break
      case 'next':
        result = await mpdClient.next()
        break
      case 'previous':
        result = await mpdClient.previous()
        break
      case 'volume':
        if (volume !== undefined) {
          // Clamp volume between 0 and 100
          const safeVolume = Math.max(0, Math.min(100, volume))
          result = await mpdClient.setVolume(safeVolume)
        } else {
          throw new Error('Volume value required for volume action')
        }
        break
      case 'seek':
        if (seek !== undefined && seek >= 0) {
          result = await mpdClient.seek(seek)
        } else {
          throw new Error('Valid seek position required for seek action')
        }
        break
      default:
        throw new Error('Invalid action')
    }

    await mpdClient.disconnect()

    return NextResponse.json({ 
      success: true, 
      result,
      user: user.email // Include user for audit logging
    })
  } catch (error) {
    console.error("Failed to control MPD:", error)
    return NextResponse.json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to control playback' 
    }, { status: 500 })
  }
}