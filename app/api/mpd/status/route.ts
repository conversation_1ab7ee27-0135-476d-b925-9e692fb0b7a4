import { NextRequest, NextResponse } from 'next/server'
import { MPDClient } from '@/lib/mpd-client'
import { getAudioConfig } from '@/lib/env'
import prisma from '@/lib/database/prisma'
import { MPDStatusCache } from '@/lib/mpd-status-cache'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    // Check cache first
    const cache = MPDStatusCache.getInstance()
    const cached = cache.get()
    
    if (cached && cached.currentSong) {
      // Ensure cached response has enhanced data
      const hasEnhancedData = cached.currentSong.albumArtUrl !== undefined || 
                             cached.currentSong.fingerprint !== undefined ||
                             (cached.currentSong as any).dbId !== undefined
      
      if (hasEnhancedData) {
        // Return cached response without rate limit check
        return NextResponse.json({
          success: true,
          status: cached.status,
          currentSong: cached.currentSong,
          cached: true
        })
      }
      // If no enhanced data, skip cache and fetch fresh data
    }
    
    // Skip rate limiting for status endpoint - it has built-in caching
    // Rate limiting was causing issues with jukebox polling every 2 seconds
    
    const config = getAudioConfig()
    if (!config) {
      return NextResponse.json({
        success: false,
        error: 'MPD configuration not found'
      }, { status: 500 })
    }

    const mpdClient = new MPDClient({
      host: config.mpdHost,
      port: config.mpdPort,
      password: config.mpdPassword,
      httpProxyPort: config.mpdHttpPort,
      timeout: 2000 // Reduce timeout to 2 seconds
    })

    await mpdClient.connect()
    const status = await mpdClient.getStatus()
    const currentSong = await mpdClient.getCurrentSong()
    await mpdClient.disconnect()

    // Enhance current song with database data for album art BEFORE caching
    let enhancedCurrentSong = currentSong
    if (currentSong && currentSong.file) {
      try {
        console.log('[MPD Status] Looking up track in database:', currentSong.file)
        const dbTrack = await prisma.quizTrack.findFirst({
          where: {
            mpdFilePath: {
              equals: currentSong.file,
              mode: 'insensitive'
            }
          },
          select: {
            id: true,
            fileFingerprint: true,
            albumArtUrl: true
          }
        })
        console.log('[MPD Status] Database lookup result:', dbTrack ? 'Found' : 'Not found')
        
        if (dbTrack) {
          enhancedCurrentSong = {
            ...currentSong,
            dbId: dbTrack.id, // Add database ID for album art lookup
            fingerprint: dbTrack.fileFingerprint,
            fileFingerprint: dbTrack.fileFingerprint,
            albumArtUrl: dbTrack.albumArtUrl
          }
        }
      } catch (error) {
        console.error('[MPD Status] Database lookup error:', error)
        // Continue with unenhanced data if database lookup fails
        // Note: This is a background operation, no user notification needed
      }
    }
    
    // Cache the enhanced response
    cache.set(status, enhancedCurrentSong)

    return NextResponse.json({
      success: true,
      status: status,
      currentSong: enhancedCurrentSong
    })

  } catch (error) {
    console.error('[MPD Status] Error:', error)
    // Return user-friendly error response
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return NextResponse.json({
      success: false,
      error: 'Failed to get MPD status',
      details: errorMessage,
      userMessage: 'Unable to connect to music server. Please check if MPD is running.'
    }, { status: 500 })
  }
}