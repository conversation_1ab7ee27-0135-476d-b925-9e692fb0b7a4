import { NextRequest, NextResponse } from 'next/server'
import { MPDClient } from '@/lib/mpd-client'
import { getAudioConfig } from '@/lib/env'
import prisma from '@/lib/database/prisma'
import { cache, cachedResponse } from '@/lib/cache'
import { optimizedSelects } from '@/lib/database/query-optimizations'
import { getAuthUser } from '@/lib/middleware/get-auth-user'

export const dynamic = 'force-dynamic'

// Generate fallback album art URL
function generateFallbackAlbumArt(track: any): string {
  // Create a placeholder image URL based on artist and album
  if (track.artist && track.album) {
    const artist = track.artist.substring(0, 8).replace(/[^a-zA-Z0-9]/g, '')
    const album = track.album.substring(0, 8).replace(/[^a-zA-Z0-9]/g, '')
    const text = `${artist}+${album}`
    return `https://via.placeholder.com/300x300/6366f1/ffffff?text=${encodeURIComponent(text)}`
  } else if (track.artist) {
    const artist = track.artist.substring(0, 12).replace(/[^a-zA-Z0-9]/g, '')
    return `https://via.placeholder.com/300x300/8b5cf6/ffffff?text=${encodeURIComponent(artist)}`
  }

  return `https://via.placeholder.com/300x300/64748b/ffffff?text=Music`
}

// Rate limiting: Track user additions with timestamps
// Structure: { userId: [timestamp1, timestamp2, ...] }
const userAdditions = new Map<string, number[]>()

// Rate limit configuration
const RATE_LIMIT_WINDOW_MS = 60 * 1000 // 1 minute in milliseconds
const RATE_LIMIT_MAX_ADDITIONS = 3 // Max 3 songs per minute

// Function to check and enforce rate limits
function checkRateLimit(userId: string, userRole: string): { allowed: boolean; message?: string } {
  // Admins and superusers are exempt from rate limiting
  if (userRole === 'admin' || userRole === 'superuser' || userRole === 'dj') {
    return { allowed: true }
  }

  const now = Date.now()
  const userHistory = userAdditions.get(userId) || []
  
  // Remove timestamps older than the rate limit window
  const recentAdditions = userHistory.filter(timestamp => 
    now - timestamp < RATE_LIMIT_WINDOW_MS
  )
  
  // Check if user has exceeded the limit
  if (recentAdditions.length >= RATE_LIMIT_MAX_ADDITIONS) {
    const oldestAddition = Math.min(...recentAdditions)
    const waitTimeMs = RATE_LIMIT_WINDOW_MS - (now - oldestAddition)
    const waitTimeSeconds = Math.ceil(waitTimeMs / 1000)
    
    return { 
      allowed: false, 
      message: `Rate limit exceeded. You can add ${RATE_LIMIT_MAX_ADDITIONS} songs per minute. Please wait ${waitTimeSeconds} seconds.` 
    }
  }
  
  // Update user's addition history
  recentAdditions.push(now)
  userAdditions.set(userId, recentAdditions)
  
  return { allowed: true }
}

// GET current queue
export async function GET() {
  try {
    // Cache queue for 2 seconds to reduce MPD load
    const enhancedQueue = await cachedResponse(
      'mpd:queue',
      async () => {
        const config = getAudioConfig()
        const mpdClient = new MPDClient({
          host: config.mpdHost,
          port: config.mpdPort,
          password: config.mpdPassword,
          httpProxyPort: config.mpdHttpPort
        })

        await mpdClient.connect()
        const queue = await mpdClient.getCurrentPlaylist()
        await mpdClient.disconnect()

    // Enhance queue items with database IDs and album art when possible
    // First try to match by file path
    const filePaths = queue.map(track => track.file)
    const dbTracksByPath = await prisma.quizTrack.findMany({
      where: { mpdFilePath: { in: filePaths } },
      select: optimizedSelects.queueTrack
    })
    
    // Create a lookup map for O(1) access
    const trackMapByPath = new Map(dbTracksByPath.map(track => [track.mpdFilePath, track]))
    
    // For tracks not found by path, try to match by title/artist
    const unmatchedTracks = queue.filter(track => !trackMapByPath.has(track.file))
    let trackMapByTitleArtist = new Map()
    
    if (unmatchedTracks.length > 0) {
      const titleArtistPairs = unmatchedTracks.map(track => ({
        title: track.title || '',
        artist: track.artist || ''
      })).filter(pair => pair.title && pair.artist)
      
      if (titleArtistPairs.length > 0) {
        const dbTracksByTitleArtist = await prisma.quizTrack.findMany({
          where: {
            OR: titleArtistPairs.map(pair => ({
              AND: [
                { title: { equals: pair.title, mode: 'insensitive' } },
                { artist: { equals: pair.artist, mode: 'insensitive' } }
              ]
            }))
          },
          select: optimizedSelects.queueTrack
        })
        
        // Create lookup by title+artist
        dbTracksByTitleArtist.forEach(track => {
          const key = `${track.title?.toLowerCase()}|${track.artist?.toLowerCase()}`
          trackMapByTitleArtist.set(key, track)
        })
      }
    }
    
    const enhancedQueue = queue.map(track => {
      const fallbackUrl = generateFallbackAlbumArt(track)
      
      // First try to find by file path
      let dbTrack = trackMapByPath.get(track.file)
      
      // If not found by path, try by title+artist
      if (!dbTrack && track.title && track.artist) {
        const key = `${track.title.toLowerCase()}|${track.artist.toLowerCase()}`
        dbTrack = trackMapByTitleArtist.get(key)
      }
      
      if (dbTrack) {
        return {
          ...track,
          id: dbTrack.id.toString(), // Ensure ID is string for consistency
          fingerprint: dbTrack.fileFingerprint, // Add fingerprint for album art
          fileFingerprint: dbTrack.fileFingerprint, // Also add as fileFingerprint for compatibility
          albumArtUrl: dbTrack.albumArtUrl || fallbackUrl,
          localAlbumArtThumbnail: dbTrack.localAlbumArtThumbnail,
          localAlbumArtCover: dbTrack.localAlbumArtCover,
          localAlbumArtOriginal: dbTrack.localAlbumArtOriginal
        }
      }
      
      // Log unmatched tracks for debugging
      console.warn(`[MPD Queue] No database match for track: ${track.title} - ${track.artist} (${track.file})`)
      
      return {
        ...track,
        albumArtUrl: fallbackUrl
      }
    })

        return enhancedQueue
      },
      2 // 2 second cache TTL
    )

    return NextResponse.json({
      success: true,
      queue: enhancedQueue
    })
  } catch (error) {
    console.error("Failed to fetch MPD queue:", error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch queue'
    }, { status: 500 })
  }
}

// POST add to queue or manage queue
export async function POST(req: NextRequest) {
  // Get authenticated user from middleware headers
  const user = getAuthUser(req)
  
  // This shouldn't happen if middleware is working correctly
  if (!user) {
    console.error('[Queue API] No user found in headers - middleware might have failed')
    return NextResponse.json(
      { success: false, message: 'Authentication required' },
      { status: 401 }
    )
  }
  
  const startTime = Date.now()
  let mpdClient: MPDClient | null = null
  
  try {
    const body = await req.json()
    const { filePath, action, position, from, to } = body
    
    // Use authenticated user info instead of body values
    const userId = user.id
    const userRole = user.role
    const addedBy = user.username

    console.log(`[Queue API] Request body:`, body)
    console.log(`[Queue API] ${action} request for user ${userId || 'guest'}`)

    if (!action) {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing action parameter' 
      }, { status: 400 })
    }

    // For add/insert actions, filePath is required
    if ((action === 'add' || action === 'insert') && !filePath) {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing filePath for add/insert action' 
      }, { status: 400 })
    }

    // Apply rate limiting for add actions (not for admin operations like remove/move)
    if (action === 'add' || action === 'insert') {
      const effectiveUserId = userId || 'guest'
      const effectiveUserRole = userRole || 'guest'
      
      const rateLimitCheck = checkRateLimit(effectiveUserId, effectiveUserRole)
      if (!rateLimitCheck.allowed) {
        return NextResponse.json({ 
          success: false, 
          message: rateLimitCheck.message,
          rateLimited: true
        }, { status: 429 }) // 429 Too Many Requests
      }
    }

    const config = getAudioConfig()
    mpdClient = new MPDClient({
      host: config.mpdHost,
      port: config.mpdPort,
      password: config.mpdPassword,
      httpProxyPort: config.mpdHttpPort
    })

    await mpdClient.connect()

    let result
    switch (action) {
      case 'add':
        // Check for duplicates before adding
        const currentQueue = await mpdClient.getCurrentPlaylist()
        const isDuplicate = currentQueue.some(track => track.file === filePath)
        
        if (isDuplicate) {
          await mpdClient.disconnect()
          return NextResponse.json({ 
            success: false, 
            message: 'Song is already in queue',
            duplicate: true
          }, { status: 400 })
        }
        
        result = await mpdClient.addTrack(filePath)
        break
        
      case 'insert':
        // For insert, we'll add at the end since MPD doesn't support position in add
        result = await mpdClient.addTrack(filePath)
        break
        
      case 'remove':
        if (position !== undefined) {
          result = await mpdClient.removeTrack(position)
        } else {
          throw new Error('Position required for remove action')
        }
        break
        
      case 'move':
        if (from !== undefined && to !== undefined) {
          result = await mpdClient.moveTrack(from, to)
        } else {
          throw new Error('Both from and to positions required for move action')
        }
        break
        
      default:
        throw new Error(`Invalid action: ${action}`)
    }

    await mpdClient.disconnect()
    mpdClient = null

    // Invalidate queue cache on any modification
    cache.delete('mpd:queue')

    // TODO: Store user attribution in database for queue items
    // This would require a JukeboxQueue table to track who added what

    const duration = Date.now() - startTime

    return NextResponse.json({ 
      success: true, 
      result,
      action,
      duration: `${duration}ms`
    })
  } catch (error) {
    // Always try to disconnect if we have a client
    if (mpdClient) {
      try {
        await mpdClient.disconnect()
      } catch (disconnectError) {
        console.warn('[Queue API] Failed to disconnect MPD client:', disconnectError)
      }
    }

    const duration = Date.now() - startTime
    console.error(`[Queue API] Failed to manage MPD queue after ${duration}ms:`, error)
    
    // Provide more specific error messages
    let errorMessage = 'Failed to manage queue'
    let statusCode = 500
    
    if (error instanceof Error) {
      if (error.message.includes('Connection refused') || error.message.includes('Could not connect')) {
        errorMessage = 'Music server is not available. Please try again later.'
        statusCode = 503 // Service Unavailable
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Request timed out. The music server may be busy.'
        statusCode = 504 // Gateway Timeout
      } else if (error.message.includes('Invalid action')) {
        errorMessage = error.message
        statusCode = 400 // Bad Request
      } else if (error.message.includes('required')) {
        errorMessage = error.message
        statusCode = 400 // Bad Request
      } else {
        errorMessage = `Server error: ${error.message}`
      }
    }
    
    return NextResponse.json({ 
      success: false, 
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? error?.toString() : undefined,
      duration: `${duration}ms`
    }, { status: statusCode })
  }
}

// DELETE clear queue
export async function DELETE() {
  try {
    const config = getAudioConfig()
    const mpdClient = new MPDClient({
      host: config.mpdHost,
      port: config.mpdPort,
      password: config.mpdPassword,
      httpProxyPort: config.mpdHttpPort
    })

    await mpdClient.connect()
    await mpdClient.clearPlaylist()
    await mpdClient.disconnect()

    // Invalidate queue cache
    cache.delete('mpd:queue')

    return NextResponse.json({ 
      success: true, 
      message: 'Queue cleared' 
    })
  } catch (error) {
    console.error("Failed to clear MPD queue:", error)
    return NextResponse.json({ 
      success: false, 
      message: 'Failed to clear queue' 
    }, { status: 500 })
  }
}
