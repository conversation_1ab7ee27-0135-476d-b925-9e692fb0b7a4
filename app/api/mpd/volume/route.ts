import { NextRequest, NextResponse } from 'next/server'
import { MPDClient } from '@/lib/mpd-client'
import { getAudioConfig } from '@/lib/env'
import { getAuthUser } from '@/lib/middleware/get-auth-user'

export const dynamic = 'force-dynamic'

// Lightweight volume control endpoint with minimal overhead
export async function POST(req: NextRequest) {
  // Get authenticated user from middleware headers
  const user = getAuthUser(req)
  
  // This shouldn't happen if middleware is working correctly
  if (!user) {
    console.error('[Volume API] No user found in headers - middleware might have failed')
    return NextResponse.json(
      { success: false, message: 'Authentication required' },
      { status: 401 }
    )
  }
  
  try {
    const body = await req.json()
    const { volume } = body
    
    if (typeof volume !== 'number' || volume < 0 || volume > 100) {
      return NextResponse.json({ 
        success: false, 
        message: 'Invalid volume value' 
      }, { status: 400 })
    }
    
    const config = getAudioConfig()
    const mpdClient = new MPDClient({
      host: config.mpdHost,
      port: config.mpdPort,
      password: config.mpdPassword,
      httpProxyPort: config.mpdHttpPort
    })
    
    await mpdClient.connect()
    await mpdClient.setVolume(Math.round(volume))
    await mpdClient.disconnect()
    
    return NextResponse.json({ 
      success: true, 
      volume: Math.round(volume)
    })
  } catch (error) {
    console.error("Failed to set volume:", error)
    return NextResponse.json({ 
      success: false, 
      message: 'Failed to set volume' 
    }, { status: 500 })
  }
}