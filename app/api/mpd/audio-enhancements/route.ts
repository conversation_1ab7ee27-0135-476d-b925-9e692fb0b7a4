import { NextRequest, NextResponse } from 'next/server'
import { MPDEnhancedClient } from '@/lib/mpd-enhanced-client'
import { getMpdConnectionConfig } from '@/lib/env'
import { withAuth, AuthenticatedRequest } from '@/middleware/auth-middleware'
import { MPDSettingsPersistence } from '@/lib/services/mpd-settings-persistence'

// GET current audio enhancement settings (allow all authenticated users to read)
export const GET = withAuth(async (req: AuthenticatedRequest) => {
  try {
    console.log('[Audio Settings] GET request from user:', req.user?.id)
    
    const config = getMpdConnectionConfig()
    console.log('[Audio Settings] MPD config:', { host: config.host, port: config.port, httpProxyPort: config.httpProxyPort })
    
    const client = new MPDEnhancedClient(config)
    const persistence = MPDSettingsPersistence.getInstance()
    const userId = req.user?.id
    
    console.log('[Audio Settings] Connecting to MPD...')
    
    try {
      await client.connect()
      console.log('[Audio Settings] Connected to MPD')
    } catch (mpdError) {
      console.error('[Audio Settings] MPD connection failed:', mpdError)
      
      // Return saved settings if available, otherwise defaults
      const savedSettings = userId ? await persistence.loadSettingsFromDatabase(userId) : null
      const defaultSettings = {
        crossfade: 0,
        replayGain: {
          mode: 'off' as const,
          preventClipping: true,
          missingPreamp: 0
        }
      }
      
      return NextResponse.json({
        success: true,
        settings: savedSettings || defaultSettings,
        mpdOffline: true,
        message: 'MPD is offline, showing saved settings'
      })
    }
    
    // Get current MPD settings
    const mpdSettings = await client.getAudioEnhancements()
    
    // Get saved settings from database for comparison
    const savedSettings = userId ? await persistence.loadSettingsFromDatabase(userId) : null
    
    // If we have saved settings that are different from MPD, apply them
    if (savedSettings && persistence.areSettingsRecent(savedSettings)) {
      const needsUpdate = (
        savedSettings.crossfade !== mpdSettings.crossfade ||
        savedSettings.replayGain.mode !== mpdSettings.replayGain.mode
      )
      
      if (needsUpdate) {
        console.log('Restoring saved MPD settings:', savedSettings)
        await client.configureAudioEnhancements({
          crossfade: savedSettings.crossfade,
          replayGain: savedSettings.replayGain
        })
        
        // Get updated settings after restoration
        const restoredSettings = await client.getAudioEnhancements()
        return NextResponse.json({
          success: true,
          settings: restoredSettings,
          restored: true
        })
      }
    }
    
    return NextResponse.json({
      success: true,
      settings: mpdSettings,
      restored: false
    })
  } catch (error) {
    console.error('Failed to get audio enhancement settings:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get audio settings',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}) // No role requirement for GET - all authenticated users can read settings

// POST update audio enhancement settings
export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json()
    const { crossfade, replayGain, applyRecommended } = body
    
    const config = getMpdConnectionConfig()
    const client = new MPDEnhancedClient(config)
    const persistence = MPDSettingsPersistence.getInstance()
    
    await client.connect()
    
    let settingsToSave
    
    if (applyRecommended) {
      // Apply recommended settings for best experience
      await client.applyRecommendedSettings()
      settingsToSave = {
        crossfade: 3,
        replayGain: {
          mode: 'auto' as const,
          preventClipping: true,
          missingPreamp: -3
        }
      }
    } else {
      // Apply custom settings
      await client.configureAudioEnhancements({
        crossfade,
        replayGain
      })
      settingsToSave = {
        crossfade: crossfade || 0,
        replayGain: replayGain || {
          mode: 'off' as const,
          preventClipping: true,
          missingPreamp: 0
        }
      }
    }
    
    // Save settings to database for persistence
    if (req.user?.id) {
      await persistence.saveSettingsToDatabase(req.user.id, {
        ...settingsToSave,
        lastUpdated: new Date()
      })
    }
    
    // Get the updated settings from MPD
    const settings = await client.getAudioEnhancements()
    
    console.log('Audio settings applied and saved:', settings)
    
    return NextResponse.json({
      success: true,
      message: 'Audio enhancements configured and saved successfully',
      settings,
      persisted: true
    })
  } catch (error) {
    console.error('Failed to configure audio enhancements:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to configure audio enhancements',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}, { requiredRole: 'dj' }) // DJ role required to change audio settings