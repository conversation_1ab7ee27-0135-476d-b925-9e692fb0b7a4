import { NextRequest, NextResponse } from 'next/server'
import { exec } from 'child_process'
import { promisify } from 'util'
import fs from 'fs/promises'
import path from 'path'

const execAsync = promisify(exec)

// Path to the audio switching script (using user MPD)
const AUDIO_SWITCH_SCRIPT = path.join(process.cwd(), 'scripts', 'switch-audio-mpd.sh')

export async function GET() {
  try {
    // Get current audio output status - check user MPD config first
    let currentMode = 'alsa'
    try {
      // Check user MPD config
      const userConfig = await fs.readFile('/home/<USER>/.config/mpd/mpd.conf', 'utf-8')
      if (userConfig.includes('type            "pulse"') && !userConfig.includes('#audio_output')) {
        currentMode = 'bluetooth'
      }
    } catch {
      // Fallback to system MPD check
      try {
        const { stdout } = await execAsync(`${AUDIO_SWITCH_SCRIPT} status`)
        currentMode = stdout.includes('type            "pulse"') ? 'bluetooth' : 'alsa'
      } catch {
        // Default to alsa if both fail
      }
    }
    
    // Check if Bluetooth is connected - try multiple methods
    let bluetoothConnected = false
    let bluetoothDevices: string[] = []
    
    try {
      // Method 1: Check connected devices
      const { stdout: connectedDevices } = await execAsync('bluetoothctl devices Connected')
      if (connectedDevices && connectedDevices.trim()) {
        bluetoothDevices = connectedDevices.trim().split('\n').filter(Boolean)
        bluetoothConnected = bluetoothDevices.length > 0
      }
    } catch (e) {
      console.log('Failed to get connected devices:', e)
    }
    
    // Method 2: If no connected devices found, check controller status
    if (!bluetoothConnected) {
      try {
        const { stdout: controllerStatus } = await execAsync('bluetoothctl show')
        // Check if controller is powered on
        if (controllerStatus.includes('Powered: yes')) {
          // Try to list all devices and check their connection status
          const { stdout: allDevices } = await execAsync('bluetoothctl devices')
          if (allDevices) {
            const deviceLines = allDevices.trim().split('\n').filter(Boolean)
            for (const line of deviceLines) {
              const match = line.match(/Device\s+([0-9A-F:]+)\s+(.+)/)
              if (match) {
                const [, mac, name] = match
                try {
                  const { stdout: deviceInfo } = await execAsync(`bluetoothctl info ${mac}`)
                  if (deviceInfo.includes('Connected: yes')) {
                    bluetoothConnected = true
                    bluetoothDevices.push(`Device ${mac} ${name}`)
                  }
                } catch {
                  // Device not available
                }
              }
            }
          }
        }
      } catch (e) {
        console.log('Failed to check controller status:', e)
      }
    }
    
    return NextResponse.json({
      success: true,
      currentMode,
      bluetoothConnected,
      bluetoothDevices
    })
  } catch (error) {
    console.error('Failed to get audio output status:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to get audio output status',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { mode } = await request.json()
    
    if (!mode || !['alsa', 'bluetooth'].includes(mode)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid mode. Must be "alsa" or "bluetooth"'
      }, { status: 400 })
    }
    
    // Check if script exists
    try {
      await fs.access(AUDIO_SWITCH_SCRIPT)
    } catch {
      return NextResponse.json({
        success: false,
        message: 'Audio switch script not found'
      }, { status: 500 })
    }
    
    // Execute the switch
    const { stdout, stderr } = await execAsync(`${AUDIO_SWITCH_SCRIPT} ${mode}`)
    
    // Check if switch was successful
    const success = stdout.includes('✓ Switched to') && !stderr.includes('Failed')
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: `Switched to ${mode === 'alsa' ? 'headphone jack' : 'Bluetooth'} output`,
        output: stdout
      })
    } else {
      return NextResponse.json({
        success: false,
        message: 'Failed to switch audio output',
        error: stderr || 'Unknown error',
        output: stdout
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Failed to switch audio output:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to switch audio output',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}