import { NextRequest, NextResponse } from 'next/server'
import { MPDEnhancedClient } from '@/lib/mpd-enhanced-client'
import { getMpdConnectionConfig } from '@/lib/env'
import { MPDSettingsPersistence } from '@/lib/services/mpd-settings-persistence'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'

// Debug version without withAuth middleware
export async function GET(req: NextRequest) {
  try {
    console.log('[Audio Settings Debug] GET request')
    
    // Manual auth check
    const authHeader = req.headers.get('authorization')
    const cookieStore = await cookies()
    const cookieToken = cookieStore.get('auth_token')?.value
    const token = authHeader?.replace('Bearer ', '') || cookieToken

    let userId: string | undefined
    if (token) {
      try {
        const secret = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production'
        const decoded = jwt.verify(token, secret) as any
        userId = decoded.id || decoded.user?.id
      } catch (e) {
        console.log('[Audio Settings Debug] Token verification failed:', e)
      }
    }
    
    const config = getMpdConnectionConfig()
    console.log('[Audio Settings Debug] MPD config:', { host: config.host, port: config.port })
    
    const client = new MPDEnhancedClient(config)
    const persistence = MPDSettingsPersistence.getInstance()
    
    try {
      await client.connect()
      console.log('[Audio Settings Debug] Connected to MPD')
      
      // Get current MPD settings
      const mpdSettings = await client.getAudioEnhancements()
      
      // Get saved settings from database if user is authenticated
      const savedSettings = userId ? await persistence.loadSettingsFromDatabase(userId) : null
      
      console.log('[Audio Settings Debug] Current MPD settings:', mpdSettings)
      console.log('[Audio Settings Debug] Saved database settings:', savedSettings)
      
      // If we have saved settings that are different from MPD, apply them
      if (savedSettings && persistence.areSettingsRecent(savedSettings)) {
        const needsUpdate = (
          savedSettings.crossfade !== mpdSettings.crossfade ||
          savedSettings.replayGain.mode !== mpdSettings.replayGain.mode
        )
        
        console.log('[Audio Settings Debug] Settings need update:', needsUpdate)
        console.log('[Audio Settings Debug] Crossfade - saved:', savedSettings.crossfade, 'current:', mpdSettings.crossfade)
        console.log('[Audio Settings Debug] ReplayGain - saved:', savedSettings.replayGain.mode, 'current:', mpdSettings.replayGain.mode)
        
        if (needsUpdate) {
          console.log('[Audio Settings Debug] Restoring saved MPD settings:', savedSettings)
          await client.configureAudioEnhancements({
            crossfade: savedSettings.crossfade,
            replayGain: savedSettings.replayGain
          })
          
          // Get updated settings after restoration
          const restoredSettings = await client.getAudioEnhancements()
          console.log('[Audio Settings Debug] Settings after restoration:', restoredSettings)
          
          await client.disconnect()
          return NextResponse.json({
            success: true,
            settings: restoredSettings,
            restored: true
          })
        }
      }
      
      await client.disconnect()
      return NextResponse.json({
        success: true,
        settings: mpdSettings,
        restored: false
      })
    } catch (mpdError) {
      console.error('[Audio Settings Debug] MPD connection failed:', mpdError)
      await client.disconnect()
      
      // Return saved settings if available, otherwise defaults
      const savedSettings = userId ? await persistence.loadSettingsFromDatabase(userId) : null
      const defaultSettings = {
        crossfade: 0,
        replayGain: {
          mode: 'off' as const,
          preventClipping: true,
          missingPreamp: 0
        }
      }
      
      return NextResponse.json({
        success: true,
        settings: savedSettings || defaultSettings,
        mpdOffline: true,
        message: 'MPD is offline, showing saved settings'
      })
    }
  } catch (error) {
    console.error('[Audio Settings Debug] Failed to get audio enhancement settings:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get audio settings',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST update audio enhancement settings
export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { crossfade, replayGain, applyRecommended } = body
    
    // Manual auth check for write operations
    const authHeader = req.headers.get('authorization')
    const cookieStore = await cookies()
    const cookieToken = cookieStore.get('auth_token')?.value
    const token = authHeader?.replace('Bearer ', '') || cookieToken
    
    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      )
    }
    
    let userId: string | undefined
    let userRole: string | undefined
    try {
      const secret = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production'
      const decoded = jwt.verify(token, secret) as any
      userId = decoded.id || decoded.user?.id
      userRole = decoded.role || decoded.user?.role
    } catch (e) {
      return NextResponse.json(
        { success: false, message: 'Invalid token' },
        { status: 401 }
      )
    }
    
    // Check DJ permission
    if (userRole !== 'dj' && userRole !== 'superuser') {
      return NextResponse.json(
        { success: false, message: 'DJ or admin permission required' },
        { status: 403 }
      )
    }
    
    const config = getMpdConnectionConfig()
    const client = new MPDEnhancedClient(config)
    const persistence = MPDSettingsPersistence.getInstance()
    
    await client.connect()
    
    let settingsToSave
    
    if (applyRecommended) {
      // Apply recommended settings for best experience
      await client.applyRecommendedSettings()
      settingsToSave = {
        crossfade: 3,
        replayGain: {
          mode: 'auto' as const,
          preventClipping: true,
          missingPreamp: -3
        }
      }
    } else {
      // Apply custom settings
      await client.configureAudioEnhancements({
        crossfade,
        replayGain
      })
      settingsToSave = {
        crossfade: crossfade || 0,
        replayGain: replayGain || {
          mode: 'off' as const,
          preventClipping: true,
          missingPreamp: 0
        }
      }
    }
    
    // Save settings to database for persistence
    if (userId) {
      await persistence.saveSettingsToDatabase(userId, {
        ...settingsToSave,
        lastUpdated: new Date()
      })
    }
    
    // Get the updated settings from MPD
    const settings = await client.getAudioEnhancements()
    
    console.log('[Audio Settings Debug] Audio settings applied and saved:', settings)
    await client.disconnect()
    
    return NextResponse.json({
      success: true,
      message: 'Audio enhancements configured and saved successfully',
      settings,
      persisted: true
    })
  } catch (error) {
    console.error('[Audio Settings Debug] Failed to configure audio enhancements:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to configure audio enhancements',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}