import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  // In Next.js 15 `params` is a Promise
  context: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path: pathSegments } = await context.params
    const imagePath = pathSegments.join('/')
    
    // Security: prevent directory traversal
    if (imagePath.includes('..') || imagePath.includes('~')) {
      return new NextResponse('Invalid path', { status: 400 })
    }
    
    // Try both /media/images and /public paths for album art
    const mediaPath = path.join('/media/images', imagePath)
    const publicPath = path.join(process.cwd(), 'public', imagePath)
    // Also try album-art directory for legacy paths
    const albumArtPath = path.join(process.cwd(), 'public', 'album-art', imagePath.replace(/^album\//, ''))
    
    const tryRead = async (filePath: string): Promise<{ buffer: Buffer, contentType: string } | null> => {
      try {
        const buffer = await fs.readFile(filePath)
        const ext = path.extname(filePath).toLowerCase()
        let type: string
        switch (ext) {
          case '.png':
            type = 'image/png'
            break
          case '.webp':
            type = 'image/webp'
            break
          case '.gif':
            type = 'image/gif'
            break
          case '.svg':
            type = 'image/svg+xml'
            break
          case '.jpg':
          case '.jpeg':
          default:
            type = 'image/jpeg'
            break
        }
        return { buffer, contentType: type }
      } catch {
        return null
      }
    }

    // Try different paths in order
    let result = await tryRead(mediaPath)
    if (!result) result = await tryRead(publicPath)
    if (!result) result = await tryRead(albumArtPath)
    
    // Fallback: if .webp missing, try .jpg version for all paths
    if (!result) {
      for (const basePath of [mediaPath, publicPath, albumArtPath]) {
        if (basePath.endsWith('.webp')) {
          const jpgPath = basePath.replace(/\.webp$/, '.jpg')
          result = await tryRead(jpgPath)
          if (result) break
        }
      }
    }

    if (result) {
      const { buffer: imageBuffer, contentType } = result
      return new NextResponse(imageBuffer, {
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=31536000, immutable',
        },
      })
    }

    console.log(`Image not found: ${imagePath}`)
    return new NextResponse('Image not found', { status: 404 })
  } catch (error) {
    console.error('Error serving image:', error)
    return new NextResponse('Internal server error', { status: 500 })
  }
} 