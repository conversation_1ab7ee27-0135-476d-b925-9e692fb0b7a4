import { NextRequest, NextResponse } from 'next/server'
import { bluetoothManager } from '@/lib/bluetooth-manager'
import { ApiError } from '@/lib/errors'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'

// Debug version without withAuth middleware
export async function GET(req: NextRequest) {
  try {
    console.log('[Bluetooth Debug] GET request')
    
    const isAvailable = await bluetoothManager.isAvailable()
    
    if (!isAvailable) {
      return NextResponse.json({
        available: false,
        message: 'Bluetooth is not available on this system'
      })
    }

    const status = await bluetoothManager.getStatus()
    
    return NextResponse.json({
      available: true,
      ...status
    })
  } catch (error) {
    console.error('[Bluetooth Debug] Failed to get Bluetooth status:', error)
    return NextResponse.json(
      { 
        success: false,
        message: 'Failed to get Bluetooth status',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST /api/bluetooth-debug - Execute Bluetooth commands
export async function POST(req: NextRequest) {
  try {
    // Manual auth check for write operations
    const authHeader = req.headers.get('authorization')
    const cookieStore = await cookies()
    const cookieToken = cookieStore.get('auth_token')?.value
    const token = authHeader?.replace('Bearer ', '') || cookieToken
    
    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      )
    }
    
    let userRole: string | undefined
    try {
      const secret = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production'
      const decoded = jwt.verify(token, secret) as any
      userRole = decoded.role || decoded.user?.role
    } catch (e) {
      return NextResponse.json(
        { success: false, message: 'Invalid token' },
        { status: 401 }
      )
    }
    
    // Check DJ permission for write operations
    if (userRole !== 'dj' && userRole !== 'superuser') {
      return NextResponse.json(
        { success: false, message: 'DJ or admin permission required' },
        { status: 403 }
      )
    }
    
    const body = await req.json()
    const { action, deviceMac } = body

    switch (action) {
      case 'scan': {
        const devices = await bluetoothManager.startScan(15)
        return NextResponse.json({ success: true, devices })
      }

      case 'connect': {
        if (!deviceMac) {
          return NextResponse.json(
            { success: false, message: 'Device MAC address required' },
            { status: 400 }
          )
        }
        
        const success = await bluetoothManager.connectDevice(deviceMac)
        return NextResponse.json({ success })
      }

      case 'disconnect': {
        const success = await bluetoothManager.disconnectDevice(deviceMac)
        return NextResponse.json({ success })
      }

      case 'remove': {
        if (!deviceMac) {
          return NextResponse.json(
            { success: false, message: 'Device MAC address required' },
            { status: 400 }
          )
        }
        
        const success = await bluetoothManager.removeDevice(deviceMac)
        return NextResponse.json({ success })
      }

      case 'power': {
        const { enable } = body
        try {
          const success = await bluetoothManager.setPower(enable)
          return NextResponse.json({ success })
        } catch (error) {
          return NextResponse.json({ 
            success: false,
            message: error instanceof Error ? error.message : 'Failed to toggle power'
          })
        }
      }

      case 'autoConnect': {
        const success = await bluetoothManager.autoConnect()
        return NextResponse.json({ success })
      }

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('[Bluetooth Debug] Operation failed:', error)
    return NextResponse.json(
      { 
        success: false,
        message: 'Bluetooth operation failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}