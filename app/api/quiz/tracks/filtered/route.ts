import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { ContentFilterService } from '@/lib/services/content-filter-service'
import type { ContentFilters } from '@/lib/types/filters'
import { Prisma } from '@prisma/client'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      filters, 
      gameMode,
      limit = 20,
      page = 1,
      randomize = false
    } = body
    
    const skip = (page - 1) * limit
    
    // Get filter service instance
    const filterService = ContentFilterService.getInstance()
    
    // Use provided filters or get from service
    const activeFilters: ContentFilters = filters || 
      (gameMode ? filterService.getFiltersForMode(gameMode) : filterService.getCurrentFilters())
    
    // Build Prisma where clause from filters
    const where: Prisma.QuizTrackWhereInput = {}
    
    // Genre filter
    if (activeFilters.genres.values.length > 0) {
      if (activeFilters.genres.mode === 'include') {
        where.genre = {
          in: activeFilters.genres.values
        }
      } else {
        where.genre = {
          notIn: activeFilters.genres.values
        }
      }
    }
    
    // Year range filter
    if (activeFilters.yearRange.enabled) {
      const yearConditions: any = {}
      if (activeFilters.yearRange.min) {
        yearConditions.gte = activeFilters.yearRange.min
      }
      if (activeFilters.yearRange.max) {
        yearConditions.lte = activeFilters.yearRange.max
      }
      where.year = yearConditions
    }
    
    // Chart filter
    if (!activeFilters.charts.includeChartMusic || !activeFilters.charts.includeNonChartMusic) {
      if (!activeFilters.charts.includeChartMusic) {
        where.chartPosition = null
      } else if (!activeFilters.charts.includeNonChartMusic) {
        where.chartPosition = { not: null }
      }
    }
    
    // Chart country filter
    if (activeFilters.charts.countries?.length) {
      where.chartCountry = {
        in: activeFilters.charts.countries
      }
    }
    
    // Quality filters
    if (activeFilters.quality.minDifficulty) {
      where.difficultyRating = {
        ...where.difficultyRating as any,
        gte: activeFilters.quality.minDifficulty
      }
    }
    if (activeFilters.quality.maxDifficulty) {
      where.difficultyRating = {
        ...where.difficultyRating as any,
        lte: activeFilters.quality.maxDifficulty
      }
    }
    if (activeFilters.quality.minPopularity) {
      where.popularityScore = {
        gte: activeFilters.quality.minPopularity
      }
    }
    if (activeFilters.quality.requireAlbumArt) {
      where.hasAlbumArt = true
    }
    
    // Metadata filters
    if (activeFilters.metadata.requireYear) {
      where.year = { ...where.year as any, not: null }
    }
    if (activeFilters.metadata.requireGenre) {
      where.genre = { ...where.genre as any, not: null }
    }
    if (activeFilters.metadata.requireAlbum) {
      where.album = { ...where.album as any, not: null }
    }
    if (activeFilters.metadata.excludeCompilations) {
      where.isCompilation = { not: true }
    }
    if (activeFilters.metadata.excludeLiveRecordings) {
      where.title = {
        not: {
          contains: 'live',
          mode: 'insensitive'
        }
      }
    }
    if (activeFilters.metadata.excludeRemixes) {
      where.title = {
        not: {
          contains: 'remix',
          mode: 'insensitive'
        }
      }
    }
    
    // Source filters
    if (!activeFilters.sources.includeMyItunes && !activeFilters.sources.includeSharedLibrary) {
      // If both are false, no tracks should match
      where.id = -1 // Impossible condition
    } else if (!activeFilters.sources.includeMyItunes) {
      where.mpdFilePath = {
        not: {
          startsWith: 'MyItunes/'
        }
      }
    } else if (!activeFilters.sources.includeSharedLibrary) {
      where.mpdFilePath = {
        startsWith: 'MyItunes/'
      }
    }
    
    // Playlist/Category filters
    if (activeFilters.playlists.values.length > 0) {
      if (activeFilters.playlists.mode === 'include') {
        // Include mode: track must have at least one of the selected categories
        const categoryConditions = activeFilters.playlists.values.map(category => ({
          quizCategories: {
            contains: category
          }
        }))
        
        if (where.OR) {
          where.OR = [...where.OR, ...categoryConditions]
        } else {
          where.OR = categoryConditions
        }
      } else {
        // Exclude mode: track must not have any of the selected categories
        const categoryConditions = activeFilters.playlists.values.map(category => ({
          NOT: {
            quizCategories: {
              contains: category
            }
          }
        }))
        
        if (where.AND) {
          where.AND = [...where.AND, ...categoryConditions]
        } else {
          where.AND = categoryConditions
        }
      }
    }
    
    // Folder filters
    if (activeFilters.folders?.values.length > 0) {
      if (activeFilters.folders.mode === 'include') {
        // Include mode: track must be in one of the selected folders
        const folderConditions = activeFilters.folders.values.map(folder => ({
          importFolder: folder
        }))
        
        if (where.OR) {
          where.OR = [...where.OR, ...folderConditions]
        } else {
          where.OR = folderConditions
        }
      } else {
        // Exclude mode: track must not be in any of the selected folders
        const folderConditions = activeFilters.folders.values.map(folder => ({
          NOT: {
            importFolder: folder
          }
        }))
        
        if (where.AND) {
          where.AND = [...where.AND, ...folderConditions]
        } else {
          where.AND = folderConditions
        }
      }
    }
    
    // Count total matching tracks
    const total = await prisma.quizTrack.count({ where })
    
    // Fetch tracks with optional randomization
    let tracks
    if (randomize && total > 0) {
      // For randomized results, get all IDs first then sample randomly
      const allIds = await prisma.quizTrack.findMany({
        where,
        select: { id: true }
      })
      
      // Shuffle and take limited sample
      const shuffledIds = allIds.sort(() => Math.random() - 0.5).slice(0, limit)
      const selectedIds = shuffledIds.map(item => item.id)
      
      // Fetch full tracks for selected IDs
      tracks = await prisma.quizTrack.findMany({
        where: {
          id: { in: selectedIds }
        }
      })
    } else {
      // Regular paginated query
      tracks = await prisma.quizTrack.findMany({
        where,
        skip,
        take: limit,
        orderBy: [
          { artist: 'asc' },
          { album: 'asc' }, 
          { title: 'asc' }
        ]
      })
    }
    
    // Calculate filter statistics
    const totalUnfiltered = await prisma.quizTrack.count()
    const percentageRemaining = totalUnfiltered > 0 
      ? Math.round((total / totalUnfiltered) * 100) 
      : 0
    
    return NextResponse.json({
      success: true,
      tracks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      },
      filterStats: {
        totalTracks: totalUnfiltered,
        filteredTracks: total,
        percentageRemaining,
        filtersApplied: activeFilters
      }
    })
  } catch (error) {
    console.error("Failed to fetch filtered quiz tracks:", error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch filtered quiz tracks',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// GET endpoint for testing
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const gameMode = searchParams.get('gameMode') || undefined
  
  // Convert to POST request body format
  return POST(new NextRequest(request.url, {
    method: 'POST',
    body: JSON.stringify({
      gameMode,
      limit: parseInt(searchParams.get('limit') || '20'),
      page: parseInt(searchParams.get('page') || '1'),
      randomize: searchParams.get('randomize') === 'true'
    })
  }))
}