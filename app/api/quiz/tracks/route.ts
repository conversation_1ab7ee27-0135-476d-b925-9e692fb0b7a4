import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { LibraryCache } from '@/lib/cache/cache-helpers'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const page = parseInt(searchParams.get('page') || '1')
    const skip = (page - 1) * limit
    const search = searchParams.get('search') || ''
    const genre = searchParams.get('genre') || ''
    const category = searchParams.get('category') || ''
    const year = searchParams.get('year') || ''
    const artist = searchParams.get('artist') || ''
    const hideChartSongs = searchParams.get('hideChartSongs') === 'true'
    const hideMyItunes = searchParams.get('hideMyItunes') === 'true'

    // Create cache key based on all filters
    const filters = { limit, page, search, genre, category, year, artist, hideChartSongs, hideMyItunes }
    const isFilterRequest = search || genre || category || year || artist || hideChartSongs || hideMyItunes || page > 1

    // For unfiltered first page requests, try to get from cache
    if (!isFilterRequest) {
      const cachedLibrary = await LibraryCache.getLibrary()
      if (cachedLibrary && cachedLibrary.length > 0) {
        const tracks = cachedLibrary.slice(skip, skip + limit)
        const total = cachedLibrary.length
        
        return NextResponse.json({
          success: true,
          tracks,
          cached: true,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
            hasNext: page < Math.ceil(total / limit),
            hasPrev: page > 1
          }
        })
      }
    }

    // For filtered requests, try cached filtered results (but skip cache for search queries to ensure fresh results)
    if (isFilterRequest && !search) {
      const cachedFiltered = await LibraryCache.getFilteredLibrary(filters)
      if (cachedFiltered) {
        const total = cachedFiltered.length
        return NextResponse.json({
          success: true,
          tracks: cachedFiltered,
          cached: true,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
            hasNext: page < Math.ceil(total / limit),
            hasPrev: page > 1
          }
        })
      }
    }

    // Build where clause for filtering
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { artist: { contains: search, mode: 'insensitive' } },
        { album: { contains: search, mode: 'insensitive' } },
      ]
    }
    
    if (genre && genre !== 'all') {
      where.genre = genre
    }
    
    if (category && category !== 'all') {
      where.quizCategories = { contains: `"${category}"` }
    }
    
    if (year && year !== 'all') {
      where.year = parseInt(year)
    }
    
    if (artist && artist !== 'all') {
      where.artist = artist
    }
    
    // Filter out chart songs if requested (hide songs WITH chart data)
    if (hideChartSongs) {
      where.chartData = "{}" // Only show tracks with empty chart data
    }
    
    // Filter out MyItunes songs if requested (for performance)
    if (hideMyItunes) {
      where.mpdFilePath = {
        not: {
          startsWith: "MyItunes/"
        }
      }
    }

    // Fetch from database
    const tracks = await prisma.quizTrack.findMany({
      where,
      skip,
      take: limit,
      orderBy: [
        { artist: 'asc' },
        { album: 'asc' }, 
        { title: 'asc' }
      ]
    })

    const total = await prisma.quizTrack.count({ where })

    // Cache the results
    if (!isFilterRequest && page === 1) {
      // Cache full library for unfiltered requests
      const fullLibrary = await prisma.quizTrack.findMany({
        orderBy: [
          { artist: 'asc' },
          { album: 'asc' }, 
          { title: 'asc' }
        ]
      })
      await LibraryCache.setLibrary(fullLibrary, 1800) // Cache for 30 minutes
    } else if (isFilterRequest && !search) {
      // Only cache non-search filtered results (genre, category filters)
      await LibraryCache.setFilteredLibrary(filters, tracks, 600) // Cache for 10 minutes
    }

    return NextResponse.json({
      success: true,
      tracks,
      cached: false,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error("Failed to fetch quiz tracks:", error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch quiz tracks',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const track = await prisma.quizTrack.create({
      data: body
    })

    // Invalidate library cache when new track is added
    await LibraryCache.invalidate()

    return NextResponse.json({
      success: true,
      track
    })
  } catch (error) {
    console.error("Failed to create quiz track:", error)
    return NextResponse.json({
      success: false,
      message: 'Failed to create quiz track',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}