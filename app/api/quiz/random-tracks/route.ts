import { NextRequest, NextResponse } from 'next/server'
import { QuizUtils, ArrayUtils } from '@/lib/database/prisma'
import { cache, cachedResponse } from '@/lib/cache'

export const dynamic = 'force-dynamic'

export async function GET(req: NextRequest) {
  try {
    const apiStart = Date.now()
    const { searchParams } = new URL(req.url)
    const count = parseInt(searchParams.get('count') || '10', 10)

    const filters: any = {}
    const genre = searchParams.get('genre')
    const decade = searchParams.get('decade')
    const difficulty = searchParams.get('difficulty')
    const minPopularity = searchParams.get('minPopularity')
    const minLastPlayedMinutesParam = searchParams.get('minLastPlayedMinutes')
    if (genre) filters.genre = genre
    if (decade) filters.decade = decade
    if (difficulty) filters.difficulty = parseInt(difficulty, 10)
    if (minPopularity) filters.minPopularity = parseInt(minPopularity, 10)
    if (minLastPlayedMinutesParam) filters.minLastPlayedMinutes = parseInt(minLastPlayedMinutesParam,10)

    const excludeIds = searchParams.getAll('excludeIds')
    if (excludeIds && excludeIds.length > 0) {
      filters.excludeIds = excludeIds
    }

    console.log('[API] random-tracks request:', { count, filters })

    // Create cache key based on parameters
    const cacheKey = `quiz:random-tracks:${count}:${JSON.stringify(filters)}`
    
    const allTracks = await cachedResponse(
      cacheKey,
      async () => QuizUtils.getRandomTracks(count * 3, filters), // Get more tracks to filter
      30 // Cache for 30 seconds
    )
    
    // Debug: log the first track to see available fields
    if (allTracks.length > 0) {
      console.log('[API] Sample track fields:', Object.keys(allTracks[0]))
      console.log('[API] Sample track mpdFilePath:', allTracks[0].mpdFilePath)
      console.log('[API] Sample track mpd_file_path:', (allTracks[0] as any).mpd_file_path)
    }

    // Filter out tracks with unknown metadata and missing file paths
    const tracks = allTracks.filter(track =>
      track.artist &&
      track.artist !== 'Unknown Artist' &&
      track.title &&
      track.title !== 'Unknown Title' &&
      ((track as any).mpd_file_path || track.mpdFilePath) // Must have a valid MPD file path
    ).slice(0, count) // Take only the requested count after filtering

    const apiDuration = Date.now() - apiStart
    console.log(`[API] random-tracks found ${tracks.length} usable tracks from ${allTracks.length} total in ${apiDuration} ms`)

    if (tracks.length === 0) {
      console.warn('[API] No tracks with proper metadata found in database - returning empty array')
      // Return empty array instead of error to trigger fallback in quiz-data.ts
      return NextResponse.json([])
    }

    const payload = tracks.map(track => ({
      file: (track as any).mpd_file_path || track.mpdFilePath,
      title: track.title || 'Unknown Title',
      artist: track.artist || 'Unknown Artist',
      album: track.album || 'Unknown Album',
      date: track.year?.toString() || '',
      genre: track.genre || 'Unknown',
      time: track.duration || 0,
      quizTrackId: track.id,
      difficultyRating: track.difficultyRating,
      popularityScore: track.popularityScore,
      triviaFacts: ArrayUtils.fromJsonString((track.triviaFacts as unknown as string) || '[]'),
      albumArtUrl: track.albumArtUrl || undefined,
      artistImageUrl: track.artistImageUrl || undefined,
      chartPosition: track.chartPosition || undefined,
      chartCountry: track.chartCountry,
      interestingFacts: ArrayUtils.objectFromJsonString((track.interestingFacts as unknown as string) || '{}'),
      similarArtists: ArrayUtils.fromJsonString((track.similarArtists as unknown as string) || '[]'),
      timesPlayed: track.timesPlayed,
      correctAnswers: track.correctAnswers,
      lastPlayed: track.lastPlayed || undefined
    }))

    return NextResponse.json(payload)
  } catch (error) {
    console.error('[API] random-tracks error:', error)
    // Return empty array instead of 500 error to trigger fallback in quiz-data.ts
    return NextResponse.json([])
  }
} 