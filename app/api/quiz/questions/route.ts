import { NextRequest, NextResponse } from 'next/server'
import { QuizDataManager } from '@/lib/database/quiz-data'
import { MPDLibrarySync } from '@/lib/database/mpd-sync'
import { MPDClient } from '@/lib/mpd-client'
import { getAudioConfig } from '@/lib/env'
import { GeneralQuizService } from '@/lib/database/general-quiz-data'
import { cache, cachedResponse } from '@/lib/cache'

export const dynamic = 'force-dynamic'

export async function POST(req: NextRequest) {
  try {
    const { gameMode, settings } = await req.json()
    
    if (!gameMode || !settings) {
      return NextResponse.json({ 
        error: 'Missing gameMode or settings' 
      }, { status: 400 })
    }

    // Handle general knowledge questions
    if (gameMode === 'general-knowledge') {
      const generalQuizService = GeneralQuizService.getInstance()
      
      // Extract category from settings or use mixed
      const category = settings.category || 'mixed'
      const count = settings.totalQuestions || 10
      const difficulty = settings.difficultyLevel
      
      let questions
      if (category === 'mixed' && settings.categories && settings.categories.length > 0) {
        // If specific categories are provided, use them
        questions = await generalQuizService.getQuestionsByCategories(settings.categories, count, difficulty)
      } else if (category === 'mixed') {
        // Otherwise get from all categories
        questions = await generalQuizService.getMixedQuestions(count, difficulty)
      } else {
        questions = await generalQuizService.getQuestionsByCategory(category, count, difficulty)
      }
      
      return NextResponse.json({
        success: true,
        questions,
        count: questions.length
      })
    }

    // Handle music quiz questions
    const config = getAudioConfig()
    const mpdClient = new MPDClient({
      host: config.mpdHost,
      port: config.mpdPort,
      password: config.mpdPassword,
      httpProxyPort: config.mpdHttpPort
    })

    const librarySync = new MPDLibrarySync(mpdClient)
    const quizDataManager = new QuizDataManager(librarySync)

    // Generate questions - handle both totalQuestions and questionCount
    const questionCount = settings.totalQuestions || settings.questionCount || 10
    console.log(`[Quiz API] Generating ${questionCount} questions for mode: ${gameMode}`)
    const questions = await quizDataManager.generateQuizQuestions(gameMode, settings)
    console.log(`[Quiz API] Generated ${questions.length} questions (${questions.filter(q => q.metadata?.isAlbumArtQuiz).length} with album art metadata)`)

    return NextResponse.json({
      success: true,
      questions,
      count: questions.length
    })

  } catch (error) {
    console.error('[API] Quiz questions generation failed:', error)
    return NextResponse.json({ 
      error: 'Failed to generate quiz questions',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
