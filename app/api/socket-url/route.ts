/**
 * Socket URL API endpoint
 * Returns the appropriate WebSocket URL for the current environment
 */

import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Get the host from the request headers
    const host = request.headers.get('host') || 'localhost:3000'
    const forwardedHost = request.headers.get('x-forwarded-host')
    const forwardedProto = request.headers.get('x-forwarded-proto') || 'http'
    
    // Extract just the hostname without port
    const hostname = (forwardedHost || host).split(':')[0]
    const socketPort = process.env.SOCKET_PORT || '3001'
    
    let socketUrl: string
    
    // If it's localhost, use the configured socket URL
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || `http://${hostname}:${socketPort}`
    } else {
      // For other hosts (like IP addresses from mobile), construct dynamic URL
      const protocol = forwardedProto || 'http'
      socketUrl = `${protocol}://${hostname}:${socketPort}`
    }

    return NextResponse.json({
      url: socketUrl,
      host: hostname,
      environment: process.env.NODE_ENV,
      success: true
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type'
      }
    })
  } catch (error) {
    console.error('Error getting socket URL:', error)
    // Never return localhost as fallback for non-localhost requests
    return NextResponse.json(
      { 
        url: '/socket.io', // Use relative path as fallback
        error: 'Failed to determine socket URL',
        success: false
      },
      { status: 500 }
    )
  }
} 