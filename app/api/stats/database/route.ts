import { NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { cache, cachedResponse } from '@/lib/cache'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    // Cache database stats for 5 minutes since they don't change often
    const stats = await cachedResponse(
      'database:stats',
      async () => {
        const [
          totalTracks,
          totalUsers,
          totalPlaylists,
          totalFavorites,
          totalGameSessions,
          totalSuggestions
        ] = await Promise.all([
          prisma.quizTrack.count(),
          prisma.user.count(),
          prisma.playlist.count(),
          prisma.userFavorite.count(),
          prisma.gameSession.count(),
          prisma.jukeboxSuggestion.count()
        ])

        return {
          totalTracks,
          totalUsers,
          totalPlaylists,
          totalFavorites,
          totalGameSessions,
          totalSuggestions,
          timestamp: new Date().toISOString()
        }
      },
      300 // 5 minutes cache
    )

    return NextResponse.json({
      success: true,
      stats,
      cached: true
    })
  } catch (error) {
    console.error('Failed to get database stats:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve database statistics'
    }, { status: 500 })
  }
}