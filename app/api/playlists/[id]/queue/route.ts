import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { getMpdConnectionConfig } from '@/lib/env'
import { MPDClient } from '@/lib/mpd-client'

export const dynamic = 'force-dynamic'

interface RouteParams {
  params: {
    id: string
  }
}

// POST /api/playlists/[id]/queue - Load playlist to MPD queue
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const body = await request.json()
    const { userId, shuffle = false, clear = false, startPosition } = body

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'User ID required'
      }, { status: 400 })
    }

    // Get playlist with tracks
    const playlist = await prisma.playlist.findUnique({
      where: { id: params.id },
      include: {
        tracks: {
          orderBy: { position: 'asc' }
        },
        user: {
          select: {
            id: true,
            displayName: true,
            role: true
          }
        }
      }
    })

    if (!playlist) {
      return NextResponse.json({
        success: false,
        error: 'Playlist not found'
      }, { status: 404 })
    }

    // Check access permissions
    const canAccess = playlist.isPublic || 
                     playlist.userId === userId ||
                     playlist.isSystem

    if (!canAccess) {
      return NextResponse.json({
        success: false,
        error: 'Access denied'
      }, { status: 403 })
    }

    if (playlist.tracks.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Playlist is empty'
      }, { status: 400 })
    }

    // Get MPD configuration
    const config = getMpdConnectionConfig()
    if (!config) {
      return NextResponse.json({
        success: false,
        error: 'MPD configuration not available'
      }, { status: 500 })
    }

    try {
      const mpdClient = new MPDClient(config)
      await mpdClient.connect()

      // Clear queue if requested
      if (clear) {
        await mpdClient.clearPlaylist()
        console.log('Cleared MPD queue')
      }

      // Prepare tracks for adding
      let tracks = [...playlist.tracks]
      
      // Shuffle if requested
      if (shuffle) {
        tracks = tracks.sort(() => Math.random() - 0.5)
      }

      // Add tracks to queue
      let addedCount = 0
      const user = await prisma.user.findUnique({ where: { id: userId } })
      const addedBy = user?.displayName || user?.username || 'Unknown User'

      for (const track of tracks) {
        if (track.filePath) {
          try {
            // Add to MPD queue directly
            await mpdClient.addTrack(track.filePath)
            addedCount++
            
            // Log the addition for tracking (optional)
            console.log(`Added to queue: ${track.title} by ${addedBy}`)
          } catch (addError) {
            console.error(`Error adding track ${track.title}:`, addError)
          }
        }
      }

      // Start playback at specific position if requested
      if (startPosition !== undefined && startPosition >= 0) {
        try {
          await mpdClient.playPosition(startPosition)
        } catch (playError) {
          console.error('Failed to start playback at position:', playError)
          // Don't fail the request if playback start fails
        }
      }

      await mpdClient.disconnect()

      // Update playlist statistics
      await prisma.playlist.update({
        where: { id: params.id },
        data: {
          playCount: { increment: 1 },
          lastPlayed: new Date()
        }
      })

      // Update track play counts
      await prisma.playlistTrack.updateMany({
        where: { playlistId: params.id },
        data: {
          playCount: { increment: 1 },
          lastPlayed: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        message: `Added ${addedCount} tracks from "${playlist.name}" to queue`,
        addedCount,
        totalTracks: tracks.length,
        skippedTracks: tracks.length - addedCount,
        shuffle,
        cleared: clear
      })

    } catch (mpdError) {
      console.error('MPD operation failed:', mpdError)
      return NextResponse.json({
        success: false,
        error: 'Failed to add playlist to queue'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Failed to load playlist to queue:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to load playlist to queue'
    }, { status: 500 })
  }
}

// GET /api/playlists/[id]/queue - Get queue preview for playlist
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { searchParams } = new URL(request.url)
    const shuffle = searchParams.get('shuffle') === 'true'
    const limit = parseInt(searchParams.get('limit') || '50')

    // Get playlist with tracks
    const playlist = await prisma.playlist.findUnique({
      where: { id: params.id },
      include: {
        tracks: {
          orderBy: { position: 'asc' },
          take: limit,
          include: {
            track: {
              select: {
                id: true,
                difficultyRating: true,
                popularityScore: true,
                albumArtUrl: true
              }
            }
          }
        }
      }
    })

    if (!playlist) {
      return NextResponse.json({
        success: false,
        error: 'Playlist not found'
      }, { status: 404 })
    }

    // Prepare tracks preview
    let tracks = [...playlist.tracks]
    
    if (shuffle) {
      tracks = tracks.sort(() => Math.random() - 0.5)
    }

    // Add queue preview metadata
    const queuePreview = tracks.map((track, index) => ({
      queuePosition: index,
      title: track.title,
      artist: track.artist,
      album: track.album,
      duration: track.duration,
      albumArtUrl: track.albumArtUrl || track.track?.albumArtUrl,
      filePath: track.filePath,
      quizMetadata: track.track ? {
        difficultyRating: track.track.difficultyRating,
        popularityScore: track.track.popularityScore
      } : null
    }))

    return NextResponse.json({
      success: true,
      playlist: {
        id: playlist.id,
        name: playlist.name,
        trackCount: playlist.trackCount,
        totalDuration: playlist.totalDuration
      },
      queuePreview,
      previewCount: queuePreview.length,
      totalTracks: playlist.trackCount,
      shuffle
    })

  } catch (error) {
    console.error('Failed to get queue preview:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to get queue preview'
    }, { status: 500 })
  }
}