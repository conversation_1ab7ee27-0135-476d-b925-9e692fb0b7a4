import { NextRequest, NextResponse } from 'next/server'
import { exec } from 'child_process'
import { promisify } from 'util'
import { requireSuperuser, getUserFromRequest } from '@/lib/middleware/auth'

const execAsync = promisify(exec)

async function handleSpeakerSwitch(req: NextRequest) {
  // Handle build-time environment
  if (process.env.NODE_ENV === 'production' && !process.env.RUNTIME_ENV) {
    return NextResponse.json(
      { error: 'Audio controls not available during build' },
      { status: 503 }
    )
  }

  try {
    const body = await req.json()
    const { output } = body

    if (!['onboard', 'alsa'].includes(output)) {
      return NextResponse.json(
        { error: 'Invalid output type. Must be "onboard" or "alsa"' },
        { status: 400 }
      )
    }

    let commands: string[]
    let message: string

    if (output === 'alsa') {
      // Disable onboard speaker and enable headphone/ALSA output
      commands = [
        'amixer -c 0 set Speaker mute',
        'amixer -c 0 set Speaker 0%',
        'amixer -c 0 set Headphone unmute',
        'amixer -c 0 set "Headphone+LO" 100%'
      ]
      message = 'Audio output switched to ALSA (headphone jack). Onboard speaker disabled.'
    } else {
      // Enable onboard speaker
      commands = [
        'amixer -c 0 set Speaker unmute',
        'amixer -c 0 set Speaker 100%'
      ]
      message = 'Audio output switched to onboard speaker.'
    }

    // Execute all commands
    for (const command of commands) {
      try {
        await execAsync(command)
      } catch (error) {
        console.error(`Failed to execute: ${command}`, error)
      }
    }

    // Try to save settings permanently (will fail without sudo, but that's ok)
    try {
      await execAsync('alsactl store', { timeout: 2000 })
    } catch (error) {
      // Expected to fail without sudo, ignore
    }

    return NextResponse.json({
      success: true,
      output,
      message
    })
  } catch (error) {
    console.error('Speaker switch error:', error)
    return NextResponse.json(
      { error: 'Failed to switch speaker output' },
      { status: 500 }
    )
  }
}

// Only superusers can change audio settings
export async function POST(req: NextRequest) {
  const authResult = await requireSuperuser(req)
  if (authResult) return authResult
  return handleSpeakerSwitch(req)
}

// Get current speaker status
async function handleGetStatus(req: NextRequest) {
  // Handle build-time environment
  if (process.env.NODE_ENV === 'production' && !process.env.RUNTIME_ENV) {
    return NextResponse.json({
      output: 'unknown',
      error: 'Audio controls not available during build'
    })
  }

  try {
    // Check if Speaker is muted
    const { stdout } = await execAsync('amixer -c 0 get Speaker | grep -E "\\[(on|off)\\]" | head -1')
    const isOnboardEnabled = stdout.includes('[on]')
    
    return NextResponse.json({
      output: isOnboardEnabled ? 'onboard' : 'alsa',
      onboardEnabled: isOnboardEnabled
    })
  } catch (error) {
    return NextResponse.json({
      output: 'unknown',
      error: 'Failed to get speaker status'
    })
  }
}

export async function GET(req: NextRequest) {
  const authResult = await requireSuperuser(req)
  if (authResult) return authResult
  return handleGetStatus(req)
}