import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { withAuth, AuthenticatedRequest } from '@/middleware/auth-middleware'
import { RateLimitService } from '@/lib/services/rate-limit-service'

export const dynamic = 'force-dynamic'

// GET all users with their roles
export const GET = withAuth(async (req: AuthenticatedRequest) => {
  console.log('[admin/roles GET] Starting request')
  console.log('[admin/roles GET] Authenticated user:', JSON.stringify(req.user))
  console.log('[admin/roles GET] Headers:', req.headers)
  
  try {
    // Test database connection first
    console.log('[admin/roles GET] Testing database connection...')
    try {
      await prisma.$connect()
      console.log('[admin/roles GET] Database connection successful')
    } catch (dbError) {
      console.error('[admin/roles GET] Database connection failed:', dbError)
      return NextResponse.json({
        success: false,
        message: 'Database connection failed',
        error: dbError instanceof Error ? dbError.message : 'Unknown database error',
        stack: dbError instanceof Error ? dbError.stack : undefined
      }, { status: 500 })
    }

    // Get users from database
    console.log('[admin/roles GET] Fetching users from database...')
    const users = await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        lastActive: true
      }
    })

    console.log(`[admin/roles GET] Successfully retrieved ${users.length} users from database`)

    return NextResponse.json({
      success: true,
      users,
      dbAvailable: true,
      message: 'Users loaded from database'
    })
  } catch (error) {
    console.error("[admin/roles GET] Failed to fetch users:", error)
    console.error("[admin/roles GET] Error type:", error?.constructor?.name)
    console.error("[admin/roles GET] Error details:", {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      stringified: JSON.stringify(error, null, 2)
    })

    return NextResponse.json({
      success: false,
      message: 'Failed to fetch users',
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error?.constructor?.name || 'Unknown',
      stack: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.stack : 'No stack') : undefined
    }, { status: 500 })
  }
}, { requiredRole: 'superuser' })

// PATCH update user role
export const PATCH = withAuth(async (req: AuthenticatedRequest) => {
  // Rate limit: 5 role updates per minute per admin
  const rateLimiter = RateLimitService.getInstance()
  const limited = await rateLimiter.checkLimit(
    `admin-role-update:${req.user!.id}`,
    5,
    60
  )
  
  if (!limited.allowed) {
    return NextResponse.json(
      { success: false, message: 'Rate limit exceeded' },
      { status: 429 }
    )
  }

  try {
    const { userId, role } = await req.json()

    if (!userId || !role) {
      return NextResponse.json({
        success: false,
        message: 'Missing required fields'
      }, { status: 400 })
    }

    // Valid roles
    const validRoles = ['user', 'dj', 'superuser']
    if (!validRoles.includes(role)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid role'
      }, { status: 400 })
    }

    console.log(`Updating user ${userId} role to ${role}`)

    // Update user role in database using Prisma's safe update method
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { 
        role: role as any, // Cast to UserRole type
        updatedAt: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      user: updatedUser,
      message: 'Role updated successfully'
    })
  } catch (error) {
    console.error("Failed to update user role:", error)
    return NextResponse.json({
      success: false,
      message: 'Failed to update role: ' + (error instanceof Error ? error.message : 'Unknown error')
    }, { status: 500 })
  }
}, { requiredRole: 'superuser' })

// POST create new user
export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    const { displayName, username, email, role, adminUserId } = await req.json()

    if (!displayName || !email || !role) {
      return NextResponse.json({
        success: false,
        message: 'Missing required fields'
      }, { status: 400 })
    }

    console.log(`Creating user ${displayName} with role ${role}`)

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: email },
          { username: username || displayName.toLowerCase().replace(/\s+/g, '_') }
        ]
      }
    })

    if (existingUser) {
      // Update existing user using Prisma's safe update method
      const updatedUser = await prisma.user.update({
        where: { id: existingUser.id },
        data: {
          displayName,
          role: role as any,
          lastActive: new Date(),
          updatedAt: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        user: updatedUser,
        message: 'User updated successfully'
      })
    } else {
      // Create new user using Prisma's safe create method
      const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      const generatedUsername = username || displayName.toLowerCase().replace(/\s+/g, '_')
      
      const newUser = await prisma.user.create({
        data: {
          id: userId,
          email,
          username: generatedUsername,
          displayName,
          role: role as any,
          preferences: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          lastActive: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        user: newUser,
        message: 'User created successfully'
      })
    }
  } catch (error) {
    console.error("Failed to create user:", error)
    return NextResponse.json({
      success: false,
      message: 'Failed to create user: ' + (error instanceof Error ? error.message : 'Unknown error')
    }, { status: 500 })
  }
}, { requiredRole: 'superuser' })
