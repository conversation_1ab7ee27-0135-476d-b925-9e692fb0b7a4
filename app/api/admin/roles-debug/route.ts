import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { AuthService } from '@/lib/auth-service'

export const dynamic = 'force-dynamic'

export async function GET(req: NextRequest) {
  console.log('[roles-debug] Starting request')
  
  try {
    // Check auth manually
    const authHeader = req.headers.get('authorization')
    const cookieToken = req.cookies.get('auth_token')?.value
    const token = authHeader?.replace('Bearer ', '') || cookieToken
    
    console.log('[roles-debug] Auth check:', {
      hasAuthHeader: !!authHeader,
      hasCookieToken: !!cookieToken,
      hasToken: !!token
    })
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: 'No authentication token found'
      }, { status: 401 })
    }
    
    // Verify token
    let user;
    try {
      user = await AuthService.verifyToken(token)
      console.log('[roles-debug] Token verified, user:', user)
    } catch (tokenError) {
      console.error('[roles-debug] Token verification failed:', tokenError)
      return NextResponse.json({
        success: false,
        message: 'Invalid token',
        error: tokenError instanceof Error ? tokenError.message : 'Unknown error'
      }, { status: 401 })
    }
    
    // Check if superuser
    if (user?.role !== 'superuser') {
      return NextResponse.json({
        success: false,
        message: 'Requires superuser role',
        userRole: user?.role
      }, { status: 403 })
    }
    
    console.log('[roles-debug] User is superuser, fetching users...')
    
    // Fetch users
    const users = await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        lastActive: true
      }
    })
    
    console.log(`[roles-debug] Found ${users.length} users`)
    
    return NextResponse.json({
      success: true,
      users,
      authenticatedUser: {
        id: user.id,
        email: user.email,
        role: user.role
      }
    })
    
  } catch (error) {
    console.error('[roles-debug] Error:', error)
    console.error('[roles-debug] Error stack:', error instanceof Error ? error.stack : 'No stack')
    
    return NextResponse.json({
      success: false,
      message: 'Server error',
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}

export async function DELETE(req: NextRequest) {
  console.log('[roles-debug DELETE] Starting request')
  
  try {
    // Check auth manually
    const authHeader = req.headers.get('authorization')
    const cookieToken = req.cookies.get('auth_token')?.value
    const token = authHeader?.replace('Bearer ', '') || cookieToken
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: 'No authentication token found'
      }, { status: 401 })
    }
    
    // Verify token
    let user;
    try {
      user = await AuthService.verifyToken(token)
    } catch (tokenError) {
      return NextResponse.json({
        success: false,
        message: 'Invalid token'
      }, { status: 401 })
    }
    
    // Check if superuser
    if (user?.role !== 'superuser') {
      return NextResponse.json({
        success: false,
        message: 'Requires superuser role'
      }, { status: 403 })
    }
    
    // Get userId from query params
    const { searchParams } = new URL(req.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return NextResponse.json({
        success: false,
        message: 'User ID is required'
      }, { status: 400 })
    }
    
    console.log(`[roles-debug DELETE] Deleting user ${userId}`)
    
    // Don't allow deleting yourself
    if (userId === user.id) {
      return NextResponse.json({
        success: false,
        message: 'Cannot delete your own account'
      }, { status: 400 })
    }
    
    // Delete user
    await prisma.user.delete({
      where: { id: userId }
    })
    
    console.log(`[roles-debug DELETE] User ${userId} deleted successfully`)
    
    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    })
    
  } catch (error) {
    console.error('[roles-debug DELETE] Error:', error)
    
    return NextResponse.json({
      success: false,
      message: 'Failed to delete user',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function PATCH(req: NextRequest) {
  console.log('[roles-debug PATCH] Starting request')
  
  try {
    // Check auth manually
    const authHeader = req.headers.get('authorization')
    const cookieToken = req.cookies.get('auth_token')?.value
    const token = authHeader?.replace('Bearer ', '') || cookieToken
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: 'No authentication token found'
      }, { status: 401 })
    }
    
    // Verify token
    let user;
    try {
      user = await AuthService.verifyToken(token)
    } catch (tokenError) {
      return NextResponse.json({
        success: false,
        message: 'Invalid token'
      }, { status: 401 })
    }
    
    // Check if superuser
    if (user?.role !== 'superuser') {
      return NextResponse.json({
        success: false,
        message: 'Requires superuser role'
      }, { status: 403 })
    }
    
    // Get request body
    const { userId, role } = await req.json()
    
    if (!userId || !role) {
      return NextResponse.json({
        success: false,
        message: 'User ID and role are required'
      }, { status: 400 })
    }
    
    // Validate role
    const validRoles = ['user', 'dj', 'superuser']
    if (!validRoles.includes(role)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid role'
      }, { status: 400 })
    }
    
    console.log(`[roles-debug PATCH] Updating user ${userId} role to ${role}`)
    
    // Update user role
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { 
        role: role as any,
        updatedAt: new Date()
      }
    })
    
    console.log(`[roles-debug PATCH] User ${userId} role updated successfully`)
    
    return NextResponse.json({
      success: true,
      user: updatedUser,
      message: 'Role updated successfully'
    })
    
  } catch (error) {
    console.error('[roles-debug PATCH] Error:', error)
    
    return NextResponse.json({
      success: false,
      message: 'Failed to update role',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}