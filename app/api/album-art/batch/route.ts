import { NextRequest, NextResponse } from 'next/server'
import { AlbumArtProcessor } from '@/lib/services/album-art-processor'

export const dynamic = 'force-dynamic'

// POST - Batch process album art
export async function POST(req: NextRequest) {
  try {
    let body = {}
    try {
      body = await req.json()
    } catch (jsonError) {
      // Handle empty or invalid JSON - use defaults
      console.log('No JSON body provided, using defaults')
    }
    const { limit = 50, dryRun = false } = body

    // Use Server-Sent Events for real-time progress
    const stream = new ReadableStream({
      start(controller) {
        const encoder = new TextEncoder()

        const onProgress = (processed: number, total: number) => {
          const progress = {
            type: 'progress',
            processed,
            total,
            percentage: Math.round((processed / total) * 100)
          }
          
          const chunk = `data: ${JSON.stringify(progress)}\n\n`
          controller.enqueue(encoder.encode(chunk))
        }

        // Start processing
        AlbumArtProcessor.batchProcessAlbumArt(limit, onProgress)
          .then(() => {
            const completion = {
              type: 'complete',
              message: 'Batch processing completed'
            }
            
            const chunk = `data: ${JSON.stringify(completion)}\n\n`
            controller.enqueue(encoder.encode(chunk))
            controller.close()
          })
          .catch((error) => {
            const errorData = {
              type: 'error',
              message: error.message
            }
            
            const chunk = `data: ${JSON.stringify(errorData)}\n\n`
            controller.enqueue(encoder.encode(chunk))
            controller.close()
          })
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    })

  } catch (error) {
    console.error('Batch processing failed:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to start batch processing'
    }, { status: 500 })
  }
}

// GET - Get batch processing status
export async function GET() {
  try {
    // Get count of unprocessed tracks
    const { default: prisma } = await import('@/lib/database/prisma')
    
    const unprocessedCount = await prisma.quizTrack.count({
      where: {
        OR: [
          { albumArtProcessed: false },
          { albumArtProcessed: null }
        ]
      }
    })

    const processedCount = await prisma.quizTrack.count({
      where: { albumArtProcessed: true }
    })

    return NextResponse.json({
      success: true,
      stats: {
        unprocessedCount,
        processedCount,
        totalCount: unprocessedCount + processedCount
      }
    })

  } catch (error) {
    console.error('Failed to get batch status:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to get batch status'
    }, { status: 500 })
  }
} 