/**
 * Database Health Check API
 * GET /api/health/database
 */

import { NextResponse } from 'next/server'
import { dbRecovery } from '@/lib/database/connection-recovery'
import prisma from '@/lib/database/prisma'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const startTime = Date.now()
    
    // Simple direct database connectivity check without retry logic
    let isConnected = false
    let dbResponseTime = 0
    
    try {
      const dbStart = Date.now()
      await prisma.$queryRaw`SELECT 1`
      dbResponseTime = Date.now() - dbStart
      isConnected = true
    } catch (error) {
      console.error('Database connection check failed:', error)
      isConnected = false
    }
    
    const stats = {
      connected: isConnected,
      timestamp: new Date().toISOString()
    }

    const responseTime = Date.now() - startTime

    const response = {
      status: isConnected ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      database: {
        connected: isConnected,
        responseTime: dbResponseTime || responseTime,
        lastCheck: new Date()
      },
      stats,
      version: process.env.npm_package_version || 'unknown'
    }

    const statusCode = isConnected ? 200 : 503

    return NextResponse.json(response, { status: statusCode })

  } catch (error) {
    console.error('Health check failed:', error)
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      database: {
        connected: false,
        responseTime: null,
        consecutiveFailures: -1,
        totalFailures: -1,
        uptime: 0,
        lastCheck: new Date()
      }
    }, { status: 503 })
  }
}