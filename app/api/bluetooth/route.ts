import { NextRequest, NextResponse } from 'next/server'
import { bluetoothManager } from '@/lib/bluetooth-manager'
import { withAuth, AuthenticatedRequest } from '@/middleware/auth-middleware'
import { ApiError } from '@/lib/errors'

// GET /api/bluetooth - Get Bluetooth status and devices
async function handleGet() {
  try {
    const isAvailable = await bluetoothManager.isAvailable()
    
    if (!isAvailable) {
      return NextResponse.json({
        available: false,
        message: 'Bluetooth is not available on this system'
      })
    }

    const status = await bluetoothManager.getStatus()
    
    return NextResponse.json({
      available: true,
      ...status
    })
  } catch (error) {
    throw new ApiError(500, 'Failed to get Bluetooth status')
  }
}

// POST /api/bluetooth - Execute Bluetooth commands
async function handlePost(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, deviceMac } = body

    switch (action) {
      case 'scan': {
        const devices = await bluetoothManager.startScan(15)
        return NextResponse.json({ success: true, devices })
      }

      case 'connect': {
        if (!deviceMac) {
          throw new ApiError(400, 'Device MAC address required')
        }
        
        const success = await bluetoothManager.connectDevice(deviceMac)
        return NextResponse.json({ success })
      }

      case 'disconnect': {
        const success = await bluetoothManager.disconnectDevice(deviceMac)
        return NextResponse.json({ success })
      }

      case 'remove': {
        if (!deviceMac) {
          throw new ApiError(400, 'Device MAC address required')
        }
        
        const success = await bluetoothManager.removeDevice(deviceMac)
        return NextResponse.json({ success })
      }

      case 'power': {
        const { enable } = body
        const success = await bluetoothManager.setPower(enable)
        return NextResponse.json({ success })
      }

      case 'autoConnect': {
        const success = await bluetoothManager.autoConnect()
        return NextResponse.json({ success })
      }

      default:
        throw new ApiError(400, 'Invalid action')
    }
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(500, 'Bluetooth operation failed')
  }
}

export const GET = withAuth(async (req: AuthenticatedRequest) => {
  return handleGet()
})

export const POST = withAuth(async (req: AuthenticatedRequest) => {
  return handlePost(req)
}, { requiredRole: 'dj' })