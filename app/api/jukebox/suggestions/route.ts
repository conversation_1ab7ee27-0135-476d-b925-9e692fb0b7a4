import { NextResponse } from 'next/server';
import prisma from '@/lib/database/prisma'
import { cache, cachedResponse } from '@/lib/cache'
import { optimizedSelects } from '@/lib/database/query-optimizations'

interface Song {
  title: string;
  artist: string;
  album?: string;
  duration?: number;
  genre?: string;
  year?: number;
  filePath: string;
  albumArtUrl?: string;
}

interface UserProfile {
  id: string;
  name: string;
  displayName?: string;
}

// GET all suggestions with vote counts
export async function GET() {
  try {
    // Cache suggestions for 5 seconds
    const suggestionsWithVotes = await cachedResponse(
      'jukebox:suggestions:pending',
      async () => {
        const suggestions = await prisma.jukeboxSuggestion.findMany({
          where: {
            status: 'pending' // Only show pending suggestions for voting
          },
          select: {
            id: true,
            title: true,
            artist: true,
            album: true,
            duration: true,
            genre: true,
            year: true,
            filePath: true,
            albumArtUrl: true,
            suggestedById: true,
            suggestedBy: true,
            createdAt: true,
            _count: {
              select: { votes: true }
            }
          },
          orderBy: [
            { createdAt: 'desc' } // Sort by newest first (simpler query)
          ],
          take: 20 // Reduce to 20 suggestions for faster response
        });

        // Transform to include vote count and track object for frontend compatibility
        return suggestions.map(suggestion => ({
          ...suggestion,
          voteCount: suggestion._count.votes,
          votes: suggestion._count.votes, // Also add this for compatibility
          track: {
            id: suggestion.id,
            title: suggestion.title,
            artist: suggestion.artist,
            album: suggestion.album,
            duration: suggestion.duration,
            genre: suggestion.genre,
            year: suggestion.year,
            filePath: suggestion.filePath,
            albumArtUrl: suggestion.albumArtUrl
          }
        }));
      },
      5 // 5 second cache TTL
    );

    return NextResponse.json({ 
      success: true, 
      suggestions: suggestionsWithVotes 
    });
  } catch (error) {
    console.error("Failed to fetch suggestions:", error);
    return NextResponse.json({ 
      success: false, 
      message: 'Failed to fetch suggestions' 
    }, { status: 500 });
  }
}

// POST a new suggestion
export async function POST(request: Request) {
  try {
    const body = await request.json();
    console.log('[Suggestions API] POST request received:', {
      hasFilePath: !!body.filePath,
      hasSong: !!body.song,
      hasUserProfile: !!body.userProfile,
      bodyKeys: Object.keys(body)
    });
    
    // Get current user from auth
    const authHeader = request.headers.get('authorization');
    let currentUser = null;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const { verifyJWT } = await import('@/lib/auth-service');
        const decoded = await verifyJWT(token);
        
        // Get user from database
        currentUser = await prisma.user.findUnique({
          where: { id: decoded.userId }
        });
      } catch (error) {
        console.error('Failed to verify token:', error);
      }
    }
    
    // If no auth user, try to get from session/cookie
    if (!currentUser) {
      const userEmail = request.headers.get('X-User-Email');
      if (userEmail) {
        currentUser = await prisma.user.findUnique({
          where: { email: userEmail }
        });
      }
    }
    
    // Default user if none found
    if (!currentUser) {
      console.log('[Suggestions API] No authenticated user found, using anonymous');
      currentUser = {
        id: 'anonymous',
        name: 'Anonymous',
        email: '<EMAIL>'
      };
    } else {
      console.log('[Suggestions API] Authenticated user:', currentUser.email);
    }
    
    // Handle different formats
    let song: Song;
    let userProfile: UserProfile;
    
    if (body.song && body.userProfile) {
      // Original format
      song = body.song;
      userProfile = body.userProfile;
    } else if (body.filePath) {
      // Direct song format from mobile library
      song = {
        title: body.title || 'Unknown Title',
        artist: body.artist || 'Unknown Artist',
        album: body.album,
        duration: body.duration,
        genre: body.genre,
        year: body.year,
        filePath: body.filePath,
        albumArtUrl: body.albumArtUrl
      };
      userProfile = {
        id: currentUser.id,
        name: currentUser.name || 'Anonymous',
        displayName: currentUser.name
      };
    } else {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing song data' 
      }, { status: 400 });
    }

    console.log('[Suggestions API] Creating suggestion:', {
      title: song.title,
      artist: song.artist,
      filePath: song.filePath,
      suggestedBy: userProfile.displayName || userProfile.name,
      suggestedById: userProfile.id
    });

    // Create suggestion with initial vote from suggester
    const newSuggestion = await prisma.jukeboxSuggestion.create({
      data: {
        title: song.title,
        artist: song.artist,
        album: song.album,
        duration: song.duration,
        genre: song.genre,
        year: song.year,
        filePath: song.filePath,
        albumArtUrl: song.albumArtUrl,
        suggestedById: userProfile.id,
        suggestedBy: userProfile.displayName || userProfile.name,
        status: 'pending',
        // Create initial vote from the suggester
        votes: {
          create: {
            userId: userProfile.id,
          }
        }
      },
      include: {
        votes: true,
        _count: {
          select: { votes: true }
        }
      }
    });

    console.log(`[Suggestions] Created suggestion "${song.title}" with auto-vote from ${userProfile.displayName || userProfile.name}`);

    // Invalidate cache
    cache.delete('jukebox:suggestions:pending');

    return NextResponse.json({ 
      success: true, 
      suggestion: {
        ...newSuggestion,
        voteCount: newSuggestion._count.votes,
        votes: newSuggestion._count.votes // For compatibility
      }
    });
  } catch (error) {
    console.error("Failed to create suggestion:", error);
    return NextResponse.json({ 
      success: false, 
      message: 'Failed to create suggestion' 
    }, { status: 500 });
  }
}

// PATCH approve/reject suggestion
export async function PATCH(request: Request) {
  try {
    const { suggestionId, action, userId } = await request.json();

    if (!suggestionId || !action || !userId) {
      return NextResponse.json({
        success: false,
        message: 'Missing required fields'
      }, { status: 400 });
    }

    if (!['approve', 'reject'].includes(action)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid action'
      }, { status: 400 });
    }

    // First check if the suggestion exists and is still pending
    const existingSuggestion = await prisma.jukeboxSuggestion.findUnique({
      where: { id: suggestionId }
    });

    if (!existingSuggestion) {
      return NextResponse.json({
        success: false,
        message: 'Suggestion not found'
      }, { status: 404 });
    }

    if (existingSuggestion.status !== 'pending') {
      return NextResponse.json({
        success: false,
        message: `Suggestion has already been ${existingSuggestion.status}`
      }, { status: 409 });
    }

    const suggestion = await prisma.jukeboxSuggestion.update({
      where: { id: suggestionId },
      data: {
        status: action === 'approve' ? 'approved' : 'rejected',
        reviewedBy: userId,
        reviewedAt: new Date()
      }
    });

    // Invalidate cache when status changes
    cache.delete('jukebox:suggestions:pending');

    return NextResponse.json({
      success: true,
      suggestion
    });
  } catch (error) {
    console.error("Failed to update suggestion:", error);
    return NextResponse.json({
      success: false,
      message: 'Failed to update suggestion'
    }, { status: 500 });
  }
}