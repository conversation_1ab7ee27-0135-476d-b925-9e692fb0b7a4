import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const { autoAdded, reason } = body

    // Get the suggestion
    const suggestion = await prisma.songSuggestion.findUnique({
      where: { id },
      include: {
        track: true
      }
    })

    if (!suggestion) {
      return NextResponse.json({ 
        success: false, 
        error: 'Suggestion not found' 
      }, { status: 404 })
    }

    if (suggestion.status !== 'pending') {
      return NextResponse.json({ 
        success: false, 
        error: 'Suggestion already processed' 
      }, { status: 400 })
    }

    // Update suggestion status to approved
    await prisma.songSuggestion.update({
      where: { id },
      data: {
        status: 'approved',
        approvedAt: new Date(),
        approvedBy: autoAdded ? 'Auto-Queue System' : 'Admin'
      }
    })

    // Add to MPD queue
    try {
      const response = await fetch(`${request.nextUrl.origin}/api/mpd/queue`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action: 'add',
          filePath: suggestion.track.filePath,
          title: suggestion.track.title,
          artist: suggestion.track.artist,
          album: suggestion.track.album,
          duration: suggestion.track.duration,
          addedBy: autoAdded 
            ? `Auto-Queue (${suggestion.votes} votes)` 
            : `Admin Approval (${suggestion.votes} votes)`
        })
      })

      if (!response.ok) {
        console.error('Failed to add to MPD queue')
      }
    } catch (error) {
      console.error('Error adding to MPD queue:', error)
    }

    // Log the auto-add event
    if (autoAdded) {
      console.log(`[Auto-Queue] Added suggestion "${suggestion.track.title}" with ${suggestion.votes} votes`)
    }

    return NextResponse.json({ 
      success: true,
      suggestion: {
        ...suggestion,
        status: 'approved'
      },
      message: autoAdded 
        ? `Auto-added "${suggestion.track.title}" to queue (${suggestion.votes} votes)`
        : `Approved "${suggestion.track.title}"`
    })

  } catch (error) {
    console.error('Error approving suggestion:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to approve suggestion' 
    }, { status: 500 })
  }
}