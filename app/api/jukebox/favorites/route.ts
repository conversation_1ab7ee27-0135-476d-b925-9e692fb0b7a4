import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'

export const dynamic = 'force-dynamic'

// GET user favorites
export async function GET(req: NextRequest) {
  try {
    if (!prisma) {
      return NextResponse.json({
        success: false,
        message: 'Database connection not available'
      }, { status: 503 })
    }

    const { searchParams } = new URL(req.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({
        success: false,
        message: 'User ID is required'
      }, { status: 400 })
    }

    console.log(`[Favorites API] Loading favorites for user: ${userId}`)

    const favorites = await prisma.userFavorite.findMany({
      where: { userId },
      orderBy: { addedAt: 'desc' }
    })

    console.log(`[Favorites API] Found ${favorites.length} favorites for user ${userId}`)

    return NextResponse.json({
      success: true,
      favorites
    })
  } catch (error) {
    console.error('Failed to fetch user favorites:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch favorites'
    }, { status: 500 })
  }
}

// POST add to favorites
export async function POST(req: NextRequest) {
  try {
    if (!prisma) {
      return NextResponse.json({
        success: false,
        message: 'Database connection not available'
      }, { status: 503 })
    }

    const { userId, song } = await req.json()
    console.log('[Favorites API] POST request:', { userId, songTitle: song?.title, filePath: song?.filePath })

    if (!userId || !song) {
      return NextResponse.json({
        success: false,
        message: 'User ID and song data are required'
      }, { status: 400 })
    }

    console.log(`[Favorites API] Adding to favorites for user ${userId}:`, song.title)

    // Check if already in favorites
    const existing = await prisma.userFavorite.findUnique({
      where: {
        userId_filePath: {
          userId,
          filePath: song.filePath
        }
      }
    })

    if (existing) {
      return NextResponse.json({
        success: false,
        message: 'Song is already in your favorites',
        alreadyExists: true
      }, { status: 400 })
    }

    // Add to favorites
    try {
      // Try to find matching QuizTrack by file path for songId reference
      let songId = null
      if (song.filePath) {
        const quizTrack = await prisma.quizTrack.findFirst({
          where: { mpdFilePath: song.filePath },
          select: { id: true }
        })
        songId = quizTrack?.id || null
      }

      const favorite = await prisma.userFavorite.create({
        data: {
          userId,
          songId, // Only set if we found a matching QuizTrack, otherwise null
          filePath: song.filePath,
          title: song.title,
          artist: song.artist,
          album: song.album,
          duration: song.duration,
          genre: song.genre,
          year: song.year,
          albumArtUrl: song.albumArtUrl
        }
      })

      return NextResponse.json({
        success: true,
        favorite
      })
    } catch (dbError: any) {
      // Handle unique constraint violation
      if (dbError.code === 'P2002') {
        return NextResponse.json({
          success: false,
          message: 'Song is already in your favorites',
          alreadyExists: true
        }, { status: 400 })
      }
      throw dbError // Re-throw other errors
    }
  } catch (error) {
    console.error('Failed to add to favorites:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to add to favorites'
    }, { status: 500 })
  }
}

// DELETE remove from favorites
export async function DELETE(req: NextRequest) {
  try {
    if (!prisma) {
      return NextResponse.json({
        success: false,
        message: 'Database connection not available'
      }, { status: 503 })
    }

    const { searchParams } = new URL(req.url)
    const userId = searchParams.get('userId')
    const filePath = searchParams.get('filePath')

    if (!userId || !filePath) {
      return NextResponse.json({
        success: false,
        message: 'User ID and file path are required'
      }, { status: 400 })
    }

    console.log(`[Favorites API] Removing from favorites for user ${userId}:`, filePath)

    try {
      await prisma.userFavorite.delete({
        where: {
          userId_filePath: {
            userId,
            filePath
          }
        }
      })

      return NextResponse.json({
        success: true,
        message: 'Removed from favorites'
      })
    } catch (dbError: any) {
      // Handle record not found
      if (dbError.code === 'P2025') {
        return NextResponse.json({
          success: true,
          message: 'Song was not in favorites'
        })
      }
      throw dbError // Re-throw other errors
    }
  } catch (error) {
    console.error('Failed to remove from favorites:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to remove from favorites'
    }, { status: 500 })
  }
} 