import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/database/prisma'
import { withAuth, AuthenticatedRequest } from '@/middleware/auth-middleware'

export const dynamic = 'force-dynamic'

// GET all auto queue configurations
export const GET = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // Check if user has manager/admin role
    if (!req.user || !['dj', 'superuser'].includes(req.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get all configurations
    const configs = await prisma.autoQueueConfig.findMany({
      orderBy: [
        { isDefault: 'desc' },
        { isActive: 'desc' },
        { name: 'asc' }
      ]
    })

    // Parse JSON fields
    const parsedConfigs = configs.map(config => ({
      ...config,
      algorithmWeights: JSON.parse(config.algorithmWeights),
      genreFilter: JSON.parse(config.genreFilter)
    }))

    return NextResponse.json({ configs: parsedConfigs })
  } catch (error) {
    console.error('Failed to fetch auto queue configs:', error)
    return NextResponse.json(
      { error: 'Failed to fetch configurations' },
      { status: 500 }
    )
  }
})

// POST create new configuration
export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // Check if user has manager/admin role
    if (!req.user || !['dj', 'superuser'].includes(req.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await req.json()
    
    // If this config is set as active, deactivate others
    if (body.isActive) {
      await prisma.autoQueueConfig.updateMany({
        where: { isActive: true },
        data: { isActive: false }
      })
    }

    // If this config is set as default, remove default from others
    if (body.isDefault) {
      await prisma.autoQueueConfig.updateMany({
        where: { isDefault: true },
        data: { isDefault: false }
      })
    }

    // Create new configuration
    const config = await prisma.autoQueueConfig.create({
      data: {
        name: body.name,
        description: body.description,
        isActive: body.isActive || false,
        isDefault: body.isDefault || false,
        
        // Trigger Settings
        queueThreshold: body.queueThreshold || 5,
        checkInterval: body.checkInterval || 30,
        minConnectedUsers: body.minConnectedUsers || 1,
        adaptiveMonitoring: body.adaptiveMonitoring || false,
        predictiveQueueing: body.predictiveQueueing || false,
        
        // Algorithm Settings
        algorithm: body.algorithm || 'hybrid',
        algorithmWeights: JSON.stringify(body.algorithmWeights || {}),
        fallbackEnabled: body.fallbackEnabled !== false,
        fallbackAlgorithm: body.fallbackAlgorithm || 'popularity',
        
        // Content Filters
        genreFilter: JSON.stringify(body.genreFilter || []),
        yearRangeMin: body.yearRangeMin,
        yearRangeMax: body.yearRangeMax,
        minDuration: body.minDuration,
        maxDuration: body.maxDuration,
        excludeExplicit: body.excludeExplicit || false,
        
        // Advanced Settings
        diversityLevel: body.diversityLevel || 'medium',
        energyAdaptation: body.energyAdaptation !== false,
        timeOfDayAdaptation: body.timeOfDayAdaptation !== false,
        userPreferenceWeight: body.userPreferenceWeight || 0.7,
        maxSongsPerTrigger: body.maxSongsPerTrigger || 10,
        preventRepeats: body.preventRepeats || 50,
        
        // Buffer Settings
        maintainBuffer: body.maintainBuffer !== false,
        bufferSize: body.bufferSize || 20,
        emergencyQueueSize: body.emergencyQueueSize || 5
      }
    })

    return NextResponse.json({ 
      success: true, 
      config: {
        ...config,
        algorithmWeights: JSON.parse(config.algorithmWeights),
        genreFilter: JSON.parse(config.genreFilter)
      }
    })
  } catch (error) {
    console.error('Failed to create auto queue config:', error)
    return NextResponse.json(
      { error: 'Failed to create configuration' },
      { status: 500 }
    )
  }
})

// PUT update existing configuration
export const PUT = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // Check if user has manager/admin role
    if (!req.user || !['dj', 'superuser'].includes(req.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await req.json()
    
    if (!body.id) {
      return NextResponse.json({ error: 'Configuration ID required' }, { status: 400 })
    }

    // If this config is set as active, deactivate others
    if (body.isActive) {
      await prisma.autoQueueConfig.updateMany({
        where: { 
          isActive: true,
          NOT: { id: body.id }
        },
        data: { isActive: false }
      })
    }

    // If this config is set as default, remove default from others
    if (body.isDefault) {
      await prisma.autoQueueConfig.updateMany({
        where: { 
          isDefault: true,
          NOT: { id: body.id }
        },
        data: { isDefault: false }
      })
    }

    // Update configuration
    const config = await prisma.autoQueueConfig.update({
      where: { id: body.id },
      data: {
        name: body.name,
        description: body.description,
        isActive: body.isActive,
        isDefault: body.isDefault,
        
        // Trigger Settings
        queueThreshold: body.queueThreshold,
        checkInterval: body.checkInterval,
        minConnectedUsers: body.minConnectedUsers,
        adaptiveMonitoring: body.adaptiveMonitoring,
        predictiveQueueing: body.predictiveQueueing,
        
        // Algorithm Settings
        algorithm: body.algorithm,
        algorithmWeights: JSON.stringify(body.algorithmWeights),
        fallbackEnabled: body.fallbackEnabled,
        fallbackAlgorithm: body.fallbackAlgorithm,
        
        // Content Filters
        genreFilter: JSON.stringify(body.genreFilter),
        yearRangeMin: body.yearRangeMin,
        yearRangeMax: body.yearRangeMax,
        minDuration: body.minDuration,
        maxDuration: body.maxDuration,
        excludeExplicit: body.excludeExplicit,
        
        // Advanced Settings
        diversityLevel: body.diversityLevel,
        energyAdaptation: body.energyAdaptation,
        timeOfDayAdaptation: body.timeOfDayAdaptation,
        userPreferenceWeight: body.userPreferenceWeight,
        maxSongsPerTrigger: body.maxSongsPerTrigger,
        preventRepeats: body.preventRepeats,
        
        // Buffer Settings
        maintainBuffer: body.maintainBuffer,
        bufferSize: body.bufferSize,
        emergencyQueueSize: body.emergencyQueueSize
      }
    })

    return NextResponse.json({ 
      success: true, 
      config: {
        ...config,
        algorithmWeights: JSON.parse(config.algorithmWeights),
        genreFilter: JSON.parse(config.genreFilter)
      }
    })
  } catch (error) {
    console.error('Failed to update auto queue config:', error)
    return NextResponse.json(
      { error: 'Failed to update configuration' },
      { status: 500 }
    )
  }
})

// DELETE configuration
export const DELETE = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // Check if user has superuser role
    if (!req.user || req.user.role !== 'superuser') {
      return NextResponse.json({ error: 'Only superusers can delete configurations' }, { status: 403 })
    }

    const { searchParams } = new URL(req.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json({ error: 'Configuration ID required' }, { status: 400 })
    }

    // Check if this is the default config
    const config = await prisma.autoQueueConfig.findUnique({
      where: { id },
      select: { isDefault: true }
    })

    if (config?.isDefault) {
      return NextResponse.json({ error: 'Cannot delete default configuration' }, { status: 400 })
    }

    await prisma.autoQueueConfig.delete({
      where: { id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Failed to delete auto queue config:', error)
    return NextResponse.json(
      { error: 'Failed to delete configuration' },
      { status: 500 }
    )
  }
})